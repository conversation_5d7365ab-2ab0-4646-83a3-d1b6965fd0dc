﻿using Capstone2.Framework.RestApi.Dtos;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Linq;

namespace Capstone2.Framework.RestApi.Controllers
{
    public class PublicBaseController : ControllerBase
    {
       
        public PublicBaseHttpRequestContext publicbaseHttpRequestContext;
        public PublicBaseController(IHttpContextAccessor httpContextAccessor)
        {
            //TODO:Remove the code
            var oid = httpContextAccessor.HttpContext.Items?["oid"];
            //this.orgId = Convert.ToInt32(oid);
            var prid = httpContextAccessor.HttpContext.Items?["prid"];
            var orgCode = httpContextAccessor.HttpContext.Items?["ocode"];
            var fvt = httpContextAccessor.HttpContext.Items?["fvt"];
            var tt = httpContextAccessor.HttpContext.Items?["tt"];

            publicbaseHttpRequestContext = new()
            {
                OrgId = Convert.ToInt32(oid),
                PrId = Convert.ToInt64(prid),               
                OrgCode = Convert.ToString(orgCode),
                fvt = Convert.ToString(fvt),
                tt = Convert.ToString(tt),
                BearerToken = (string)httpContextAccessor.HttpContext.Request.Headers["Authorization"].FirstOrDefault()?.Split(" ").Last(),
                RemoteIpAddress = httpContextAccessor.HttpContext.Connection.RemoteIpAddress,
                Referer = httpContextAccessor.HttpContext.Request.Headers?["Referer"]
            };
        }
    }
}
