﻿using Capstone2.Framework.RestApi.Dtos;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Linq;

namespace Capstone2.Framework.RestApi.Controllers
{
    public class BaseController : ControllerBase
    {
        public BaseHttpRequestContext baseHttpRequestContext;

        public BaseController(IHttpContextAccessor httpContextAccessor)
        {
            //TenantConnection tenantConnection = new TenantConnection
            //{
            //    ClientConnectionString = "Data Source=capstone2-dev-sql-00.database.windows.net;Initial Catalog=CapstoneClient;Persist Security Info=True;User ID=<EMAIL>;Password=****************;Encrypt=True;TrustServerCertificate=True;Connection Timeout=3000;Column Encryption Setting=enabled;Authentication=Active Directory Password;",
            //    OrgId = 1, // your organization ID
            //    OrgCode = "caps" // your organization code
            //};


            //httpContextAccessor.HttpContext.Items["ocode"] = "caps";
            //httpContextAccessor.HttpContext.Items["oid"] = 1;
            //httpContextAccessor.HttpContext.Items["caps"] = tenantConnection;
            //TODO:Remove the code
            var oid = httpContextAccessor.HttpContext.Items?["oid"];
            //this.orgId = Convert.ToInt32(oid);
            var uid = httpContextAccessor.HttpContext.Items?["uid"];
            //userId = Convert.ToInt64(uid);
            var rid = httpContextAccessor.HttpContext.Items?["rid"];
            //roleId = Convert.ToInt64(rid);
            var orgCode = httpContextAccessor.HttpContext.Items?["ocode"];

          
            baseHttpRequestContext = new()
            {
                OrgId = Convert.ToInt32(oid),
                UserId = Convert.ToInt64(uid),
                RoleId = Convert.ToInt32(rid),
                OrgCode = Convert.ToString(orgCode),
                BearerToken = (string)httpContextAccessor.HttpContext.Request.Headers["Authorization"].FirstOrDefault()?.Split(" ").Last(),
                InterServiceToken = (string)httpContextAccessor.HttpContext.Items?["interservicetoken"],
                RemoteIpAddress = httpContextAccessor.HttpContext.Connection.RemoteIpAddress,
                Referer = httpContextAccessor.HttpContext.Request.Headers?["Referer"]
        };
        }

        
    }
}
