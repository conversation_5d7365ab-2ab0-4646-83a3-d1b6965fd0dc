﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
  	<PackageReference Include="Microsoft.ApplicationInsights" Version="2.20.0" />
    <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.20.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc" Version="2.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Formatters.Json" Version="2.2.0" />
    <PackageReference Include="Microsoft.CodeAnalysis.Common" Version="3.8.0" />
    <PackageReference Include="Microsoft.CodeAnalysis.CSharp" Version="3.8.0" />
    <PackageReference Include="Microsoft.Data.SqlClient.AlwaysEncrypted.AzureKeyVaultProvider" Version="1.2.0" />
    <PackageReference Include="Microsoft.IdentityModel.Clients.ActiveDirectory" Version="5.2.8" />
    <PackageReference Include="Azure.Identity" Version="1.13.2" />
    <PackageReference Include="Azure.Security.KeyVault.Certificates" Version="4.3.0" />
    <PackageReference Include="Azure.Security.KeyVault.Secrets" Version="4.2.0" />
    <PackageReference Include="Azure.Messaging.ServiceBus" Version="7.6.0" />
  </ItemGroup>  

  <ItemGroup>
    <ProjectReference Include="..\Capstone2.Shared.Models\Capstone2.Shared.Models.csproj" />
    <ProjectReference Include="..\Capstone2.Framework.Business\Capstone2.Framework.Business.csproj" />
  </ItemGroup>
</Project>