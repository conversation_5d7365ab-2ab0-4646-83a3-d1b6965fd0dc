﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Capstone2.Framework.RestApi.Dtos
{
    public class JwtTokenModel
    {
        public long Id { get; set; }
        public long UserId { get; set; }
        public int OrgId { get; set; }
        public string JwtToken { get; set; }
        public DateTime? ExpiryDate { get; set; }
        // public bool IsDeleted { get; set; }
        public short? StatusId { get; set; }

        public short TokenType { get; set; }
        [Required, DatabaseGenerated(DatabaseGeneratedOption.Computed)]
        public DateTime CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public long? ModifiedBy { get; set; }
    }
}
