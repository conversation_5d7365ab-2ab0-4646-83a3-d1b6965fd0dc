﻿using System.Net;

namespace Capstone2.Framework.RestApi.Dtos
{
    public class BaseHttpRequestContext
    {

        public long UserId { get; set; }
        public int OrgId { get; set; }
        public int RoleId { get; set; }
        public string BearerToken { get; set; }
        public string InterServiceToken { get; set; }
        public IPAddress RemoteIpAddress { get; set; }
        public string OrgCode { get; set; }
        public string Referer { get; set; }
    }

    public class PublicBaseHttpRequestContext
    {

        public long PrId { get; set; }
        public int OrgId { get; set; }
        public string fvt { get; set; }
        public string BearerToken { get; set; }
        public IPAddress RemoteIpAddress { get; set; }
        public string OrgCode { get; set; }
        public string Referer { get; set; }
        public string tt { get; set; }
    }
}
