﻿namespace Capstone2.Framework.RestApi.Dtos
{
    public class AuthContext
    {
        public bool IsAuthenticated { get; set; }
        public string Oid { get; set; }
        public string Rid { get; set; }
        public string Uid { get; set; }
        public string Roles { get; set; } //todo add access permission for user
        public UserDto User { get; set; }
        public AuthCredentials Credentials { get; set; }
        public string InterServiceToken { get; set; }
        public string Ocode { get; set; }
    }

    public class PublicAuthContext
    {
        public bool IsAuthenticated { get; set; }
        public string prid { get; set; }
        public UserDto User { get; set; }        
        public string Ocode { get; set; }
        public string fvt { get; set; }
        public string tt { get; set; }
        public string oid { get; set; }
    }
}
