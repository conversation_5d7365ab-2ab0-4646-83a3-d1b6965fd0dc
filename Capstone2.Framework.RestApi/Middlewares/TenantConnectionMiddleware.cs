﻿using Capstone2.Framework.Business.Common;
using Capstone2.Framework.RestApi.Context;
using Capstone2.Framework.RestApi.Utility;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using static Microsoft.AspNetCore.Hosting.Internal.HostingApplication;

namespace Capstone2.Framework.RestApi.Middlewares
{
    public class TenantConnectionMiddleware
    {
        private readonly RequestDelegate next_;
        private readonly IConfiguration _configuration;
        private IDistributedCacheHelper _redisCache;
        private IJwtSecurityTokenHelper _jwtSecurityTokenHelper;
        private readonly ILogger _logger;

        public TenantConnectionMiddleware(RequestDelegate next, IConfiguration configuration, IJwtSecurityTokenHelper jwtSecurityTokenHelper, ILogger<TenantConnectionMiddleware> logger, IDistributedCacheHelper cache)
        {
            next_ = next;
            _configuration = configuration;
            _redisCache = cache;
            _jwtSecurityTokenHelper = jwtSecurityTokenHelper;
            _logger = logger;


        }

        public async Task Invoke(HttpContext httpContext, ReadOnlyDBMasterContext requestContext)
        {
            var token = "";
            try
            {
                var endpoint = httpContext.Features.Get<IEndpointFeature>()?.Endpoint;
                token = httpContext.Request.Headers["Authorization"].FirstOrDefault()?.Split(" ").Last();
                if (endpoint?.Metadata?.GetMetadata<IAllowAnonymous>() != null && (httpContext.Request.Path.ToString().Contains(TenantConnectionService.HealthCheck, StringComparison.OrdinalIgnoreCase) || httpContext.Request.Path.ToString().Contains("PostFormReportAction", StringComparison.OrdinalIgnoreCase)))
                {
                    await next_.Invoke(httpContext); return;
                }

                // TenantConnectionService.AITrackTrace(httpContext, "Tenant Middleware Invoke"); //AILogger
                _configuration[TenantConnectionService.ConfigSecureKey] = httpContext.Request.Headers["X-Auth-Token"].FirstOrDefault() != null ?
                    await _jwtSecurityTokenHelper.GetJWTSecureKey(isInterServiceKey: true) : await _jwtSecurityTokenHelper.GetJWTSecureKey();
                string subDomainName = string.Empty;
                if (httpContext.Request.QueryString.HasValue && httpContext.Request.QueryString.Value.Contains(TenantConnectionService.OrgTag, StringComparison.OrdinalIgnoreCase))
                    subDomainName = HttpUtility.ParseQueryString(httpContext.Request.QueryString.ToString()).Get(TenantConnectionService.OrgTag);
                else if (httpContext.Items["ocode"] != null)
                    subDomainName = httpContext.Items["ocode"].ToString();
                else
                    //while getting orgcode also token validation happening
                    subDomainName = TenantConnectionService.GetOrgCode(httpContext, TenantConnectionService.GetSecureTokenKeys(httpContext, _configuration));
                subDomainName = TenantConnectionService.GetFilterDomainName(subDomainName);

                // TenantConnectionService.AITrackTrace(httpContext, $"{"Requested domain -"}{subDomainName}"); //AILogger
                // TenantConnectionService.AITrackTrace(httpContext, $"{"GetFromCache key -"}{TenantConnectionService.GetRedisDomainKey(httpContext, subDomainName)}"); //AILogger

                var cachedData = await _redisCache.GetFromCache<TenantConnection>($"{TenantConnectionService.GetRedisDomainKey(httpContext, subDomainName)}");
                if (cachedData == null)
                {
                    TenantConnection tenantDBReference = null;
                    var _currentClientConnection = requestContext.OrganisationDetails.Where(a => a.OrgCode == subDomainName.Trim().ToLower()).FirstOrDefault();
                    if (_currentClientConnection != null)
                        tenantDBReference = new TenantConnection { ClientConnectionString = _currentClientConnection.ConnectionString, OrgId = _currentClientConnection.OrgId, OrgCode = _currentClientConnection.OrgCode };
                    if (tenantDBReference != null)
                    {
                        SetOrgContext(httpContext, tenantDBReference, subDomainName);
                        await _redisCache.SetIntoCache(tenantDBReference, $"{TenantConnectionService.GetRedisDomainKey(httpContext, subDomainName)}");
                    }
                    else
                    {
                        //  TenantConnectionService.AITrackTrace(httpContext, $"{"Request doesn't contains header OR subdomain(" + subDomainName + ") is not a valid company."}");
                        var errorResponse = PrepareErrorResponse(httpContext, StatusCodes.Status401Unauthorized, new List<string> { "Could not find the TenantDBReference" });

                        await httpContext.Response.WriteAsync(errorResponse); return;
                    }
                }
                else
                {
                    SetOrgContext(httpContext, cachedData, subDomainName);
                }
                await next_(httpContext);

            }
            catch (Exception ex)
            {
                // TenantConnectionService.AIException(httpContext, ex);//AILogger
                _logger.LogError($"Message: {ex.Message}, StackTrace: {ex.StackTrace} ,Tenet middleware exception caught.Organisation cannot be determined,  token: {token}, InnerException: {ex.InnerException}");
                var errorResponse = PrepareErrorResponse(httpContext, StatusCodes.Status401Unauthorized, new List<string> { "Tenet middleware exception caught.Organisation cannot be determined." });
                await httpContext.Response.WriteAsync(errorResponse);
                return;
            }
        }

        private void SetOrgContext(HttpContext httpContext, TenantConnection tenantDBReference, string subDomainName)
        {
            httpContext.Items["ocode"] = tenantDBReference.OrgCode;
            httpContext.Items["oid"] = tenantDBReference.OrgId;
            httpContext.Items[subDomainName] = tenantDBReference;
        }

        private static string PrepareErrorResponse(HttpContext context, int statusCode, List<string> errors)
        {
            context.Response.StatusCode = statusCode;
            context.Response.ContentType = "application/json";
            var response = new ApiErrorResponse
            {
                StatusCode = statusCode,
                Errors = errors
            };

            var json = JsonConvert.SerializeObject(response, new JsonSerializerSettings
            {
                Formatting = Formatting.Indented,
                NullValueHandling = NullValueHandling.Ignore
            });
            return json;
        }
    }
}
