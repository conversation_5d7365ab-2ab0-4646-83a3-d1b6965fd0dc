﻿using Capstone2.Framework.Business.Common;
using Capstone2.Framework.RestApi.Context;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Capstone2.Framework.RestApi.Middlewares
{
    public class RoleAuthorizeMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly IConfiguration _config;
        private readonly ILogger _logger;
        private IDistributedCacheHelper _redisCache;

        public RoleAuthorizeMiddleware(RequestDelegate next, IConfiguration config, ILogger<RoleAuthorizeMiddleware> logger, IDistributedCacheHelper cache)
        {
            _next = next;
            _config = config;
            _logger = logger;
            _redisCache = cache;

        }

        public async Task Invoke(HttpContext context, ReadOnlyAuthDBContext readOnlyAuthDBContext, ReadOnlyDBMasterContext readOnlyDBMasterContext)
        {
            // Middleware to check if the user's role is Authorized to access API 
            try
            {
                ApiResponse<string> apiResponse = new ApiResponse<string>();
                //ApiPermissionsAssoc apiPermission = new ApiPermissionsAssoc();
                var requestPath = context.Request.Path.ToString().ToLower();
                var requestType = context.Request.Method.ToLower();

                if (!requestPath.EndsWith("healthcheck"))
                {
                    var oid = Convert.ToInt32(context.Items["oid"]);
                    var rid = Convert.ToInt32(context.Items["rid"]);
                    var uid = Convert.ToInt32(context.Items["uid"]);


                    if (rid != 1)
                    {
                        //from redis cache get the ApiPermissionsAssoc 
                        //using master db context ==  get api to permission assoc (permissionsids)== pass the api url
                        string pattern = @"\d+";
                        string replacement = "%";
                        string outputRequestPath = Regex.Replace(requestPath, pattern, replacement);
                        //TODO: Optimization if a more efficient solution is found.
                        if (outputRequestPath.Contains("snapforms/form/") && (outputRequestPath.Contains("/responses") || outputRequestPath.Contains("/fields")))
                        {
                            string matchedSegment = outputRequestPath.Contains("fields") ? "fields" : "responses";
                            pattern = $@"form\/[a-zA-Z0-9\-]+\/{matchedSegment}";
                            replacement = $"form/%/{matchedSegment}";
                            outputRequestPath = Regex.Replace(outputRequestPath, pattern, replacement);
                        }

                        List<ApiPermissionsAssoc> responseData = await _redisCache.GetFromCache<List<ApiPermissionsAssoc>>("_Tbl_ApiPermissionsAssocs");
                        if (responseData == null || responseData.Count == 0)
                        {
                            responseData = await GetApiPermissionsDAL(readOnlyDBMasterContext);
                            await _redisCache.SetIntoCache(responseData, $"_Tbl_ApiPermissionsAssocs");
                        }

                        ApiPermissionsAssoc apiPermission = responseData.Where(i => outputRequestPath.Equals(i.ApiPath.ToLower()) && i.ApiRequestType == requestType).FirstOrDefault();

                        //List<ApiPermissionsAssoc> apiPermissions = responseData.Where(i => requestPath.Contains(i.ApiPath.ToLower()) && i.ApiRequestType == requestType).ToList();

                        if (apiPermission == null)
                        {
                            var errorResponse = PrepareErrorResponse(context, StatusCodes.Status403Forbidden, new List<string> { "Not a Authorized User/ ApiPermission Assoc is not defined" });
                            await context.Response.WriteAsync(errorResponse);
                            return;
                        }


                        /* foreach(var apiperm in apiPermissions)
                         {
                             string[] apipermsplit = apiperm.ApiPath.ToLower().Split('/');
                             string[] reqpathsplit = requestPath.Split('/');

                             if (apipermsplit.Length == reqpathsplit.Length)
                             {
                                 for (var i = 0;i< reqpathsplit.Length;i++)
                                 {
                                     if(reqpathsplit[i] == "%")
                                     {
                                         reqpathsplit[i] = apipermsplit[i];
                                     }
                                 }

                                 var finalreqpath = string.Join("/", reqpathsplit);

                                 if (apiperm.ApiPath.ToLower().Equals(finalreqpath))
                                 {
                                     apiPermission = apiperm;
                                     continue;
                                 }

                             }


                         }*/



                        //using clinet db context = get data isallowed or not for given orgid, roleid and permissionid 

                        var permissionobject = readOnlyAuthDBContext.RolesPermissionsAssocs.FirstOrDefault(x => x.RolesId == rid && x.StatusId == (short)Status.Active && x.OrgId == oid && x.PermissionsId == apiPermission.PermissionsId);

                        //if permission is not defined throw the error msg that not authorilzed
                        if (permissionobject == null)
                        {
                            var errorResponse = PrepareErrorResponse(context, StatusCodes.Status403Forbidden, new List<string> { "Not a Authorized User/ RolePermission Assoc is not defined" });
                            await context.Response.WriteAsync(errorResponse);
                            return;
                        }

                        //if permission is false(not allowed) for given role throw the error msg that not authorilzed
                        if (permissionobject.IsAllowed == false)
                        {
                            var errorResponse = PrepareErrorResponse(context, StatusCodes.Status403Forbidden, new List<string> { "Not a Authorized User" });
                            await context.Response.WriteAsync(errorResponse);
                            return;
                        }
                    }
                    //if allowed go next 
                }

            }
            catch (Exception ex)
            {
                _logger.LogError($"Message: {ex.Message}, StackTrace: {ex.StackTrace}");
                var errorResponse = PrepareErrorResponse(context, StatusCodes.Status403Forbidden, new List<string> { "Not a authorized user" });
                await context.Response.WriteAsync(errorResponse);
                return;
            }
            await _next.Invoke(context);

        }
        private static string PrepareErrorResponse(HttpContext context, int statusCode, List<string> errors)
        {
            context.Response.StatusCode = statusCode;
            context.Response.ContentType = "application/json";
            var response = new ApiErrorResponse
            {
                StatusCode = statusCode,
                Errors = errors
            };

            var json = JsonConvert.SerializeObject(response, new JsonSerializerSettings
            {
                Formatting = Formatting.Indented,
                NullValueHandling = NullValueHandling.Ignore
            });
            return json;
        }

        public async Task<List<ApiPermissionsAssoc>> GetApiPermissionsDAL(ReadOnlyDBMasterContext readOnlyDBMasterContext)
        {
            return await readOnlyDBMasterContext.ApiPermissionsAssocs.ToListAsync();
        }
    }
}