﻿using Capstone2.Framework.RestApi.Context;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.Framework.RestApi.Utility;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Capstone2.Framework.RestApi.Middlewares
{
    public class AuthenticationMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly IConfiguration _config;
        private readonly ILogger _logger;
        private readonly IAuthUtility _authUtility;
        public const string HealthCheck = "healthcheck";
        public const string GetSmsReply = "GetSmsReply";
        public AuthenticationMiddleware(RequestDelegate next, IConfiguration config, ILogger<AuthenticationMiddleware> logger, IAuthUtility authUtility)
        {
            _next = next;
            _config = config;
            _logger = logger;
            _authUtility = authUtility;
        }

        public async Task Invoke(HttpContext context, ReadOnlyAuthDBContext readOnlyAuthDBContext)
        {
            var token = "";
            try
            {
                var endpoint = context.Features.Get<IEndpointFeature>()?.Endpoint;
                //var routePattern = context.Request.Path.Value;
                //string[] compareAgainst = { "/auth/login", "/auth/anonymous_token", "/Email/SendEmail", "/Sms/SendSms", "/auth/factors/reset_link", "/auth/login/factor" };

                //if (compareAgainst.Any(prefix => routePattern.ToLower() == prefix.ToLower())) {
                //    await _next.Invoke(context);
                //    return;
                //}
             
                if (endpoint?.Metadata?.GetMetadata<IAllowAnonymous>() != null && (context.Request.Path.ToString().Contains(TenantConnectionService.HealthCheck, StringComparison.OrdinalIgnoreCase) || 
                    context.Request.Path.ToString().Contains("PostFormReportAction", StringComparison.OrdinalIgnoreCase)))
                {
                    await _next.Invoke(context); return;
                }
                var interServiceToken = context.Request.Headers["X-Auth-Token"].FirstOrDefault()?.ToString();
                 token = context.Request.Headers["Authorization"].FirstOrDefault()?.Split(" ").Last();
                var AuthServiceUrls = Convert.ToString(_config["AppSettings:ApiUrls:AuthServiceUrl"]);
                ApiResponse<AuthContext> apiResponse = new ApiResponse<AuthContext>();

                if (context.Request.Path.ToString().Contains(GetSmsReply, StringComparison.OrdinalIgnoreCase))
                {
                    token = context.Request.Query["apiKey"];
                }

                if (!string.IsNullOrWhiteSpace(interServiceToken))
                {
                    apiResponse = await _authUtility.ValidateInterServiceToken(interServiceToken);
                }
                else if (token != null)
                {
                    apiResponse = await _authUtility.ValidateToken(token, readOnlyAuthDBContext);
                }
                if (apiResponse == null)
                {
                    var errorResponse = PrepareErrorResponse(context, StatusCodes.Status401Unauthorized, new List<string> { "Api Resonse for validation of taoken is null" });
                    await context.Response.WriteAsync(errorResponse);
                    return;
                }
                if ((apiResponse.Errors != null && apiResponse.Errors.Count > 0) || string.IsNullOrEmpty(Convert.ToString(apiResponse.Result)))
                {
                    var errorResponse = PrepareErrorResponse(context, apiResponse.StatusCode, apiResponse.Errors);
                    _logger.LogError($"AuthMiddleware Token Validation failed,token:{token}, {string.Join(',', apiResponse.Errors)}");
                    await context.Response.WriteAsync(errorResponse);
                    return;
                }

                context.Items["oid"] = apiResponse.Result.Oid;
                context.Items["rid"] = apiResponse.Result.Rid;
                context.Items["uid"] = apiResponse.Result.Uid;
                context.Items["interservicetoken"] = apiResponse.Result.InterServiceToken;
                context.Items["ocode"] = apiResponse.Result.Ocode;

                using (var stream = new StreamReader(context.Request.Body))
                {
                    var requestBody = await stream.ReadToEndAsync();
                    if (requestBody.Contains("encryptedPayload"))
                    {
                        dynamic data = JObject.Parse(requestBody);
                        var payload = data.encryptedPayload.ToString();
                        var decreptedRequest = Convert.FromBase64String(payload);
                        string decodedDescription = Encoding.UTF8.GetString(decreptedRequest);

                        context.Request.HttpContext.Request.Body = new MemoryStream(Encoding.UTF8.GetBytes(decodedDescription));
                        context.Request.HttpContext.Request.Body.Position = 0;
                    }
                    else
                    {
                        context.Request.Body = new MemoryStream(Encoding.UTF8.GetBytes(requestBody));
                    }

                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Message: {ex.Message}, StackTrace: {ex.StackTrace},Invoke Not a valid Token:,token:{token}, InnerException: {ex.InnerException}");
                var errorResponse = PrepareErrorResponse(context, StatusCodes.Status401Unauthorized, new List<string> { "Exception caught not valid token :" + token } );
                await context.Response.WriteAsync(errorResponse);
                return;
            }
            await _next.Invoke(context);
        }
        private static string PrepareErrorResponse(HttpContext context, int statusCode, List<string> errors)
        {
            context.Response.StatusCode = statusCode;
            context.Response.ContentType = "application/json";
            var response = new ApiErrorResponse
            {
                StatusCode = statusCode,
                Errors = errors
            };

            var json = JsonConvert.SerializeObject(response, new JsonSerializerSettings
            {
                Formatting = Formatting.Indented,
                NullValueHandling = NullValueHandling.Ignore
            });
            return json;
        }
    }
}
