﻿using Microsoft.Data.SqlClient;
using Microsoft.Data.SqlClient.AlwaysEncrypted.AzureKeyVaultProvider;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Clients.ActiveDirectory;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Capstone2.Framework.RestApi.Context
{
    public class EncryptionDbContext : DbContext
    {
        static bool isInitialized;
        static ClientCredential _clientCredential;
        private static readonly object lockObj = new object();
        protected EncryptionDbContext(IConfiguration configuration)
        {
            SetEncryptionDbContext(configuration);
        }
        public EncryptionDbContext(DbContextOptions<EncryptionDbContext> options, IConfiguration configuration) : base(options)
        {
            SetEncryptionDbContext(configuration);
        }

        protected EncryptionDbContext(DbContextOptions options, IConfiguration configuration) : base(options)
        {
            SetEncryptionDbContext(configuration);
        }

        public void SetEncryptionDbContext(IConfiguration configuration)
        {
            if (!isInitialized)
            {
                lock (lockObj)
                {
                    if (!isInitialized)
                    {
                        InitializeAzureKeyVaultProvider();
                        isInitialized = true;
                    }
                }
            }

            void InitializeAzureKeyVaultProvider()
            {
                _clientCredential = new ClientCredential(configuration["AzureAD:KVClientId"], configuration["AzureAD:KVClientSecret"]);
                SqlColumnEncryptionAzureKeyVaultProvider azureKeyVaultProvider = new SqlColumnEncryptionAzureKeyVaultProvider(GetToken);
                Dictionary<string, SqlColumnEncryptionKeyStoreProvider> providers = new Dictionary<string, SqlColumnEncryptionKeyStoreProvider>();
                providers.Add(SqlColumnEncryptionAzureKeyVaultProvider.ProviderName, azureKeyVaultProvider);
                SqlConnection.RegisterColumnEncryptionKeyStoreProviders(providers);
            }

            async Task<string> GetToken(string authority, string resource, string scope)
            {
                var authContext = new AuthenticationContext(authority);
                AuthenticationResult result = await authContext.AcquireTokenAsync(resource, _clientCredential);

                if (result == null)
                    throw new InvalidOperationException("Failed to obtain the access token");
                return result.AccessToken;
            }
        }
    }
}