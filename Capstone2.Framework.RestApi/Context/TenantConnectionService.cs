﻿using Microsoft.ApplicationInsights;
using Microsoft.ApplicationInsights.DataContracts;
using Microsoft.ApplicationInsights.Extensibility;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Text;

namespace Capstone2.Framework.RestApi.Context
{
    public static class TenantConnectionService
    {
        public const string OrgTag = "orgcode";
        public const string HealthCheck = "healthcheck";
        public const string GetSmsReply = "GetSmsReply";
        public const string ConfigSecureKey = "JwtSecureKey";
        public static TenantConnection GetTenantConnection(this HttpContext context, IConfiguration configuration)
        {
            object tenantObj;
            var domain = GetDomainName(context, configuration);
            if (domain != null && context.Items.TryGetValue(domain.Trim().ToLower(), out tenantObj))
            {
                return tenantObj as TenantConnection;
            }

            return null;
        }

        public static string GetDomainName(HttpContext httpContext, IConfiguration configuration)
        {
            if (!string.IsNullOrWhiteSpace(GetHeaderSecureToken(httpContext)))
            {
                AppSettingsToken _appSettings = GetSecureTokenKeys(httpContext, configuration);
                if (httpContext.Items["ocode"] != null)
                    return httpContext.Items["ocode"].ToString();
                else
                    return GetOrgCode(httpContext, _appSettings);
            }
            return null;
        }

        public static string GetOrgCode(HttpContext httpContext, AppSettingsToken _appSettings)
        {
            string token;
            string orgCode;
            JwtSecurityTokenHandler tokenHandler;
            byte[] key = null;

            SecurityToken validatedToken = null;
            JwtSecurityToken jwtToken = null;


            try
            {
                orgCode = string.Empty;
                token = GetHeaderSecureToken(httpContext);
                tokenHandler = new JwtSecurityTokenHandler();
                key = Encoding.ASCII.GetBytes(_appSettings.JwtKey);
                //  var IssuerSigningKeyNew = new SymmetricSecurityKey(key);
                var securityToken = tokenHandler.ReadJwtToken(token);

                if (securityToken == null)
                {

                    throw new Exception("Read Token Failed");
                }

                /* ClaimsPrincipal principal = tokenHandler.ValidateToken(token, new TokenValidationParameters
                 {
                     ValidateIssuerSigningKey = true,
                     IssuerSigningKey = IssuerSigningKeyNew,
                     ValidateIssuer = true,
                     ValidateAudience = true,
                     ValidateActor = false,
                     ValidateLifetime = false,
                     ValidIssuer = _appSettings.Issuer,
                     ValidAudience = _appSettings.Audience,
                     // set clockskew to zero so tokens expire exactly at token expiration time (instead of 5 minutes later)
                     ClockSkew = TimeSpan.Zero
                 }, out  validatedToken);

                 jwtToken = (JwtSecurityToken)validatedToken;*/
                orgCode = securityToken.Claims.FirstOrDefault(c => c.Type == "ocode").Value;
                // IssuerSigningKeyNew = null;
                return orgCode;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString());
            }
            finally
            {
                token = null;
                tokenHandler = null;
                key = null;

                validatedToken = null;
                jwtToken = null;
                // call GC

            }


            /*   // Read token without validation
              var tokenWithoutValidation = tokenHandler.ReadToken(token) as JwtSecurityToken;

              // Extract claims from the token
                  return tokenWithoutValidation.Claims.First(c => c.Type == "ocode").Value;*/


        }

        public static string GetHeaderSecureToken(HttpContext httpContext)
        {
            string token = string.Empty;
            if (httpContext.Request.Headers["Authorization"].FirstOrDefault() != null)
                token = httpContext.Request.Headers["Authorization"].FirstOrDefault()?.Split(" ").Last();
            else if (httpContext.Request.Headers["X-Auth-Token"].FirstOrDefault() != null)
                token = httpContext.Request.Headers["X-Auth-Token"].FirstOrDefault().ToString();
            else if (httpContext.Request.Path.Value.Contains(GetSmsReply, StringComparison.OrdinalIgnoreCase))
                token = httpContext.Request.Query["apiKey"].ToString();
            return token;
        }

        public static AppSettingsToken GetSecureTokenKeys(HttpContext httpContext, IConfiguration configuration)
        {
            AppSettingsToken _appSettings = new AppSettingsToken();
            if (httpContext.Items["AppSettingsToken"] == null)
            {
                _appSettings.JwtKey = configuration[ConfigSecureKey];
                _appSettings.Audience = configuration["AppSettings:Audience"];
                _appSettings.Issuer = configuration["AppSettings:Issuer"];
                _appSettings.AesIV = configuration["AppSettings:AesIV"];
                httpContext.Items["AppSettingsToken"] = _appSettings;
            }
            else
                _appSettings = httpContext.Items["AppSettingsToken"] as AppSettingsToken;
            return _appSettings;
        }

        public static string GetHostDomainName(HttpContext httpContext)
        {
            var host = httpContext.Request.Host.Host;
            if (!string.IsNullOrWhiteSpace(host))
            {
                return host.Split('.')[0];
            }
            return "";
        }

        public static string GetFilterDomainName(string orgRequest)
        {
            if (!string.IsNullOrWhiteSpace(orgRequest) && !orgRequest.Contains(":"))
                orgRequest = orgRequest.Split(".")[0];
            else if (!string.IsNullOrWhiteSpace(orgRequest))
                orgRequest = orgRequest.Split(":")[0];
            return orgRequest;
        }

        public static string GetRedisDomainKey(HttpContext httpContext, string subDomain)
        {
            var host = httpContext.Request.Host.Host;
            if (!string.IsNullOrWhiteSpace(host) && !string.IsNullOrWhiteSpace(subDomain))
            {
                return $"{subDomain}_{host}";
            }
            return subDomain;
        }


        /*  public static void AITrackTrace(HttpContext httpContext, string message)
          {
              TelemetryConfiguration configuration = new TelemetryConfiguration();
              configuration.InstrumentationKey = httpContext.Features.Get<RequestTelemetry>()?.Context?.InstrumentationKey;
              var _telemetryClient = new TelemetryClient(configuration);
              _telemetryClient.TrackTrace(message);
          }

          public static void AIException(HttpContext httpContext, Exception exMessage)
          {
              TelemetryConfiguration configuration = new TelemetryConfiguration();
              configuration.InstrumentationKey = httpContext.Features.Get<RequestTelemetry>()?.Context?.InstrumentationKey;
              var _telemetryClient = new TelemetryClient(configuration);
              var aiProperties = new Dictionary<string, string> { ["Domain"] = TenantConnectionService.GetHostDomainName(httpContext), ["Service"] = httpContext.Request.Path, ["Tenant Exception"] = "Tenant Middleware Exception" };
              _telemetryClient.TrackException(exMessage, aiProperties);
          }*/

    }
    public class AppSettingsToken
    {
        public string AesIV { get; set; }
        public string Audience { get; set; }
        public string Issuer { get; set; }
        public string JwtKey { get; set; }
    }
}
