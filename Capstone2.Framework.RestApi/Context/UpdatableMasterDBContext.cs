using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Medicare;
using Capstone2.Shared.Models.Proda;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace Capstone2.Framework.RestApi.Context
{
    public class UpdatableDBMasterContext : EncryptionDbContext
    {
        public UpdatableDBMasterContext(DbContextOptions<UpdatableDBMasterContext> options, IConfiguration configuration) : base(options, configuration)
        {
            //options.use
        }

        public virtual DbSet<ProdaConfigs> ProdaConfigs { get; set; }
        public virtual DbSet<RequestLogs> RequestLogs { get; set; }
        public virtual DbSet<SubscriberDetails> SubscriberDetails { get; set; }
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            //Configure default schema
            modelBuilder.HasDefaultSchema("Authentication");
            modelBuilder.Entity<ProdaConfigs>().ToTable("ProdaConfigs", "Proda");
            modelBuilder.Entity<RequestLogs>().ToTable("RequestLogs", "Proda");
            modelBuilder.Entity<MbsData>().ToTable("MbsData", "MedicalSchedule");
            modelBuilder.Entity<SubscriberDetails>().ToTable("SubscriberDetails", "Medicare");
        }

        public virtual DbSet<MbsData> MbsData { get; set; }


    }
}
