using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Medicare;
using Capstone2.Shared.Models.Proda;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace Capstone2.Framework.RestApi.Context
{
    public class ReadOnlyDBMasterContext : EncryptionDbContext
    {
        public ReadOnlyDBMasterContext(DbContextOptions<ReadOnlyDBMasterContext> options, IConfiguration configuration) : base(options, configuration)
        {
            //options.use
        }
        public virtual DbSet<OrganisationDetails> OrganisationDetails { get; set; }
        public virtual DbSet<ProdaConfigs> ProdaConfigs { get; set; }
        public virtual DbSet<RequestLogs> RequestLogs { get; set; }
        public virtual DbSet<SubscriberDetails> SubscriberDetails { get; set; }
        public virtual DbSet<ReportDetails> ReportDetails { get; set; }
        public virtual DbSet<ReportDetailStoredProcs> ReportDetailStoredProcs { get; set; }
        public virtual DbSet<ApiPermissionsAssoc> ApiPermissionsAssocs { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<OrganisationDetails>().ToTable("OrganisationDetails", "Common");
            modelBuilder.Entity<ProdaConfigs>().ToTable("ProdaConfigs", "Proda");
            modelBuilder.Entity<RequestLogs>().ToTable("RequestLogs", "Proda");
            modelBuilder.Entity<SubscriberDetails>().ToTable("SubscriberDetails", "Medicare");
            modelBuilder.Entity<ReportDetailStoredProcs>().ToTable("ReportDetailStoredProcs", "SSRS");
            modelBuilder.Entity<ReportDetails>().ToTable("ReportDetails", "SSRS");
            modelBuilder.Entity<ApiPermissionsAssoc>().ToTable("ApiPermissionsAssocs", "Common");

        }
    }
}