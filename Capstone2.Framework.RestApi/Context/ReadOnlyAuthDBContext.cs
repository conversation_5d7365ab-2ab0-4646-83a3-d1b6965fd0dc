using Capstone2.Framework.RestApi.Dtos;
using Capstone2.Shared.Models.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace Capstone2.Framework.RestApi.Context
{
    public class ReadOnlyAuthDBContext : EncryptionDbContext
    {
        private TenantConnection _tenantReference;

        public ReadOnlyAuthDBContext(TenantConnection tenantReference, IConfiguration configuration) : base(configuration)
        {
            _tenantReference = tenantReference;
        }
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseSqlServer(_tenantReference.ClientConnectionString);
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            //Configure default schema
            modelBuilder.HasDefaultSchema("Authentication");
            modelBuilder.Entity<RolesPermissionsAssoc>().ToTable("RolesPermissionsAssocs", "Utility");

        }
        public virtual DbSet<JwtTokenModel> UserTokenAssocs { get; set; }
        public virtual DbSet<RolesPermissionsAssoc> RolesPermissionsAssocs { get; set; }

    }
}