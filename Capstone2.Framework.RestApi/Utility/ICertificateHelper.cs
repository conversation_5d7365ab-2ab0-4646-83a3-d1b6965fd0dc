﻿using System.Security.Cryptography.X509Certificates;

namespace Capstone2.Framework.RestApi.Utility
{
    public interface ICertificateHelper
    {
        byte[] GenerateRSACertificateBytes();
        string GetRSAPrivateKey(byte[] rsaCertificate);
        string GetInterServicePrivateKey(byte[] rsaCertificate);
        byte[] CreateSelfSignedECDSACertificate();
        string GetPrivateKeyAsString(byte[] certificateBytes);
    }
}
