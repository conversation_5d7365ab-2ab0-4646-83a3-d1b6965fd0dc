﻿using Capstone2.Framework.Business.Common;
using Microsoft.Extensions.Configuration;
using System;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using System.Threading.Tasks;

namespace Capstone2.Framework.RestApi.Utility
{
    public class JwtSecurityTokenHelper : IJwtSecurityTokenHelper
    {
        private ICertificateHelper _certificateHelper;
        private IKeyVaultHelper _keyVaultHelper;
        private IDistributedCacheHelper _redisCache;
        private readonly IConfiguration _configuration;
        public JwtSecurityTokenHelper(IConfiguration configuration, ICertificateHelper certificateHelper, IKeyVaultHelper keyVaultHelper,IDistributedCacheHelper cache)
        {
            this._configuration = configuration;
            this._certificateHelper = certificateHelper;
            this._keyVaultHelper = keyVaultHelper;
            this._redisCache = cache;
        }

        // To get proper certificae for Token validation 
        public async Task<string> GetJWTSecureKey(bool isInterServiceKey = false)
        {
            string jwtSecureKey = string.Empty;
            string cachedSecureKey = "JwtSecureKey";
            string jwtCertificateName = _configuration["AzureAD:JWTCertificateName"];
            if (isInterServiceKey)
            {
                //jwtCertificateName = _configuration["AzureAD:JWTInterSvcCertificateName"];
                cachedSecureKey = "JwtInterServiceSecureKey";
            }
            var cachedSecureJwtKey = await _redisCache.GetFromCache<string>(cachedSecureKey);
            //dynamic cachedSecureJwtKey = null; // await _redisCache.GetFromCache<string>(cachedSecureKey);
            if (cachedSecureJwtKey == null)
            {
                byte[] rsaCertificateBytes = await _keyVaultHelper.GetSetCertificateKey(jwtCertificateName, _configuration["AzureAD:KVTenantId"], _configuration["AzureAD:KVClientId"], _configuration["AzureAD:KVClientSecret"], string.Format(_configuration["AzureAD:KeyVaultUrl"], _configuration["AzureAD:KeyVaultName"]), null);
                if (rsaCertificateBytes != null)
                {
                    X509Certificate2 certificate = new X509Certificate2(rsaCertificateBytes, string.Empty, X509KeyStorageFlags.Exportable);
                    DateTime certExpiry = Convert.ToDateTime(certificate.GetExpirationDateString());
                    if (certExpiry.Date >= DateTime.UtcNow.Date)
                    {
                        jwtSecureKey = isInterServiceKey ? _certificateHelper.GetInterServicePrivateKey(rsaCertificateBytes) : _certificateHelper.GetRSAPrivateKey(rsaCertificateBytes);
                        await _redisCache.SetIntoCache(jwtSecureKey, cachedSecureKey);
                        return jwtSecureKey;
                    }
                }
                rsaCertificateBytes = _certificateHelper.GenerateRSACertificateBytes();
                await _keyVaultHelper.GetSetCertificateKey(jwtCertificateName, _configuration["AzureAD:KVTenantId"], _configuration["AzureAD:KVClientId"], _configuration["AzureAD:KVClientSecret"], string.Format(_configuration["AzureAD:KeyVaultUrl"], _configuration["AzureAD:KeyVaultName"]), rsaCertificateBytes);
                jwtSecureKey = isInterServiceKey ? _certificateHelper.GetInterServicePrivateKey(rsaCertificateBytes) : _certificateHelper.GetRSAPrivateKey(rsaCertificateBytes);

                await _redisCache.SetIntoCache(jwtSecureKey, cachedSecureKey);
            }
            else
                jwtSecureKey = cachedSecureJwtKey;
            return jwtSecureKey;
        }


        public async Task<string> GetJWTExternalSecureKey(bool isInterServiceKey = false)
        {
            string jwtSecureKey = string.Empty;
            string cachedSecureKey = "JwtExternalSecureKey";
            string jwtCertificateName = _configuration["AzureAD:JWTExternalCertName"];
            if (isInterServiceKey)
            {
                //jwtCertificateName = _configuration["AzureAD:JWTInterSvcCertificateName"];
                cachedSecureKey = "JwtInterServiceSecureKey";
            }
            var cachedSecureJwtKey = await _redisCache.GetFromCache<string>(cachedSecureKey);
            //dynamic cachedSecureJwtKey = null; // await _redisCache.GetFromCache<string>(cachedSecureKey);
            if (String.IsNullOrWhiteSpace(cachedSecureJwtKey))
            {
                byte[] ecdsaCertificateBytes = await _keyVaultHelper.GetSetCertificateKey(jwtCertificateName, _configuration["AzureAD:KVTenantId"], _configuration["AzureAD:KVClientId"], _configuration["AzureAD:KVClientSecret"], string.Format(_configuration["AzureAD:KeyVaultUrl"], _configuration["AzureAD:KeyVaultName"]), null);
                if (ecdsaCertificateBytes != null)
                {
                    X509Certificate2 certificate = new X509Certificate2(
                                                                            ecdsaCertificateBytes,
                                                                            string.Empty, // No password
                                                                            X509KeyStorageFlags.EphemeralKeySet | X509KeyStorageFlags.Exportable
                                                                        );
                    
                    
                    DateTime certExpiry = Convert.ToDateTime(certificate.GetExpirationDateString());
                    if (certExpiry.Date >= DateTime.UtcNow.Date)
                    {
                         jwtSecureKey = _certificateHelper.GetPrivateKeyAsString(ecdsaCertificateBytes);
                        await _redisCache.SetIntoCache(jwtSecureKey, cachedSecureKey);
                        return jwtSecureKey;
                    }
                }

                ecdsaCertificateBytes = _certificateHelper.CreateSelfSignedECDSACertificate();
                await _keyVaultHelper.GetSetCertificateKey(jwtCertificateName, _configuration["AzureAD:KVTenantId"], _configuration["AzureAD:KVClientId"], _configuration["AzureAD:KVClientSecret"], string.Format(_configuration["AzureAD:KeyVaultUrl"], _configuration["AzureAD:KeyVaultName"]), ecdsaCertificateBytes);
                jwtSecureKey =  _certificateHelper.GetPrivateKeyAsString(ecdsaCertificateBytes);

                await _redisCache.SetIntoCache(jwtSecureKey, cachedSecureKey);
            }
            else
                jwtSecureKey = cachedSecureJwtKey;
            return jwtSecureKey;
        }

        public async Task<string> GetJWTExternalRSASecureKey(bool isInterServiceKey = false)
        {
            string jwtSecureKey = string.Empty;
            string cachedSecureKey = "JwtExternalSecureKey";
            string jwtCertificateName = _configuration["AzureAD:JWTExternalCertName"];
            if (isInterServiceKey)
            {
                //jwtCertificateName = _configuration["AzureAD:JWTInterSvcCertificateName"];
                cachedSecureKey = "JwtInterServiceSecureKey";
            }
            var cachedSecureJwtKey = await _redisCache.GetFromCache<string>(cachedSecureKey);
            //dynamic cachedSecureJwtKey = null; // await _redisCache.GetFromCache<string>(cachedSecureKey);
            if (cachedSecureJwtKey == null)
            {
                byte[] rsaCertificateBytes = await _keyVaultHelper.GetSetCertificateKey(jwtCertificateName, _configuration["AzureAD:KVTenantId"], _configuration["AzureAD:KVClientId"], _configuration["AzureAD:KVClientSecret"], string.Format(_configuration["AzureAD:KeyVaultUrl"], _configuration["AzureAD:KeyVaultName"]), null);
                if (rsaCertificateBytes != null)
                {
                    X509Certificate2 certificate = new X509Certificate2(rsaCertificateBytes, string.Empty, X509KeyStorageFlags.EphemeralKeySet | X509KeyStorageFlags.Exportable);
                    DateTime certExpiry = Convert.ToDateTime(certificate.GetExpirationDateString());
                    if (certExpiry.Date >= DateTime.UtcNow.Date)
                    {
                        jwtSecureKey = isInterServiceKey ? _certificateHelper.GetInterServicePrivateKey(rsaCertificateBytes) : _certificateHelper.GetRSAPrivateKey(rsaCertificateBytes);
                        await _redisCache.SetIntoCache(jwtSecureKey, cachedSecureKey);
                        return jwtSecureKey;
                    }
                }
                rsaCertificateBytes = _certificateHelper.GenerateRSACertificateBytes();
                await _keyVaultHelper.GetSetCertificateKey(jwtCertificateName, _configuration["AzureAD:KVTenantId"], _configuration["AzureAD:KVClientId"], _configuration["AzureAD:KVClientSecret"], string.Format(_configuration["AzureAD:KeyVaultUrl"], _configuration["AzureAD:KeyVaultName"]), rsaCertificateBytes);
                jwtSecureKey = isInterServiceKey ? _certificateHelper.GetInterServicePrivateKey(rsaCertificateBytes) : _certificateHelper.GetRSAPrivateKey(rsaCertificateBytes);

                await _redisCache.SetIntoCache(jwtSecureKey, cachedSecureKey);
            }
            else
                jwtSecureKey = cachedSecureJwtKey;
            return jwtSecureKey;
        }


    }
}
