﻿using Capstone2.Framework.RestApi.Context;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.Shared.Models.Responses;
using System.Threading.Tasks;

namespace Capstone2.Framework.RestApi.Utility
{
    public interface IAuthUtility
    {
        Task<ApiResponse<AuthContext>> ValidateToken(string token, ReadOnlyAuthDBContext readOnlyAuthDBContext);
        Task<ApiResponse<AuthContext>> ValidateInterServiceToken(string token);
        Task<ApiResponse<PublicAuthContext>> ValidatePublicToken(string token, ReadOnlyAuthDBContext readOnlyAuthDBContext);

    }
}
