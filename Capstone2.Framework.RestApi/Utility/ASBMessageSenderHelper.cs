﻿using Azure.Messaging.ServiceBus;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Capstone2.Framework.RestApi.Utility
{
    public class ASBMessageSenderHelper : IASBMessageSenderHelper
    {
        public async Task MessageSenderTopic(byte[] jsonString, Dictionary<string, string> userPros, string serviceBusConnectionString, string topicName)
        {
            try
            {
                await using (var client = new ServiceBusClient(serviceBusConnectionString))
                {
                    var sender = client.CreateSender(topicName);
                    var messageOutput = new ServiceBusMessage(jsonString)
                    {
                        MessageId = Convert.ToString(Guid.NewGuid())
                    };

                    foreach (var prop in userPros)
                    {
                        messageOutput.ApplicationProperties.Add(prop.Key, prop.Value);
                    }
                    await sender.SendMessageAsync(messageOutput);
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
    }

}
