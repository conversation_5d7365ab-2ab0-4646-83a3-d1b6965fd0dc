﻿using Capstone2.Framework.Business.Common;
using Capstone2.Framework.RestApi.Context;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using System;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;

namespace Capstone2.Framework.RestApi.Utility
{
    public class AuthUtility : IAuthUtility
    {
        private IConfiguration _configuration;
        private IJwtSecurityTokenHelper _jwtSecurityTokenHelper;
        private IDistributedCacheHelper _redisCache;

        public AuthUtility(IConfiguration configuration, IJwtSecurityTokenHelper jwtSecurityTokenHelper,IDistributedCacheHelper redisCache)
        {
            _configuration = configuration;
            _jwtSecurityTokenHelper = jwtSecurityTokenHelper;
            _redisCache = redisCache;
        }
        private async Task<long> GetTokenForUser(long userId, int orgId, string token, ReadOnlyAuthDBContext readOnlyAuthDBContext)
        {
        
           return await readOnlyAuthDBContext.UserTokenAssocs.AsNoTracking().Where(x => x.UserId == userId && x.StatusId == (short)Status.Active && x.OrgId == orgId && x.ExpiryDate > DateTime.UtcNow && x.JwtToken == token).Select(x => x.Id).FirstOrDefaultAsync();
                      
        }
        private async Task<long> GetTokenForUserTest(long userId, int orgId, string token, ReadOnlyAuthDBContext readOnlyAuthDBContext)
        {

            return await readOnlyAuthDBContext.UserTokenAssocs.AsNoTracking().Where(x => x.UserId == userId && x.StatusId == (short)Status.Active && x.OrgId == orgId && x.ExpiryDate > DateTime.UtcNow).Select(x => x.Id).FirstOrDefaultAsync();

        }


        public async Task<ApiResponse<AuthContext>> ValidateToken(string token, ReadOnlyAuthDBContext readOnlyAuthDBContext)
        {
            var apiresponse = new ApiResponse<AuthContext>();
            apiresponse.Result = new AuthContext();
            if (token == null)
                return apiresponse;

            var tokenHandler = new JwtSecurityTokenHandler();
            var jwtSecureKey = await _jwtSecurityTokenHelper.GetJWTSecureKey();
            var key = Encoding.ASCII.GetBytes(jwtSecureKey);
            try
            {
                ClaimsPrincipal principal = tokenHandler.ValidateToken(token, new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidateIssuer = true,
                    ValidateAudience = true,
                    ValidateActor = false,
                    ValidateLifetime = true,
                    ValidIssuer = _configuration["AppSettings:Issuer"],
                    ValidAudience = _configuration["AppSettings:Audience"],
                    // set clockskew to zero so tokens expire exactly at token expiration time (instead of 5 minutes later)
                    ClockSkew = TimeSpan.Zero
                }, out SecurityToken validatedToken);

                var jwtToken = (JwtSecurityToken)validatedToken;

                var oidClaim = jwtToken.Claims.FirstOrDefault(c => c.Type == "oid")?.Value;
                var ridClaim = jwtToken.Claims.FirstOrDefault(c => c.Type == "rid")?.Value;
                var uidClaim = jwtToken.Claims.FirstOrDefault(c => c.Type == "uid")?.Value;
                var ocodeClaim = jwtToken.Claims.FirstOrDefault(c => c.Type == "ocode")?.Value;

                if (oidClaim == null || ridClaim == null || uidClaim == null || ocodeClaim == null)
                {
                    apiresponse.StatusCode = StatusCodes.Status401Unauthorized;
                    apiresponse.Errors.Add("Token is missing required claims");
                    return apiresponse;
                }

                var response = new AuthContext()
                {
                    IsAuthenticated = true,
                    Oid = oidClaim,
                    Rid = ridClaim,
                    Uid = uidClaim,
                    Ocode = ocodeClaim
                };
                var userid = jwtToken.Claims.First(c => c.Type == "uid").Value;
                var orgid = jwtToken.Claims.First(c => c.Type == "oid").Value;
                var roleid = jwtToken.Claims.First(c => c.Type == "rid").Value;
                var orgCode = jwtToken.Claims.First(c => c.Type == "ocode").Value;
             
                string UserTokenKey = "_token_" + orgCode + "_" + orgid + "_" + userid;
                    
                string tokenFromCache = await _redisCache.GetFromCache<string>(UserTokenKey);

                if ( string.IsNullOrEmpty(tokenFromCache) || tokenFromCache != token)
                {
                    try 
                    { 
                        var tokenAssoId = await GetTokenForUser(Convert.ToInt64(userid), Convert.ToInt32(orgid), token, readOnlyAuthDBContext);
                        if (tokenAssoId <= default(long))
                        {
                            apiresponse.StatusCode = StatusCodes.Status401Unauthorized;
                            apiresponse.Errors.Add("Not valid Token, Token does not exist in DB");
                            return apiresponse;
                        }
                    }
                    catch (Exception ex)
                    {
                        apiresponse.StatusCode = StatusCodes.Status418ImATeapot;
                        apiresponse.Errors.Add("Fetch Token from DB Failed with Exception");
                        apiresponse.Errors.Add("StackTrace" + ex.StackTrace + "Message: " + ex.Message);
                        apiresponse.Errors.Add("InnerException" + ex.InnerException);
                        apiresponse.Errors.Add("Security Key" + key);
                        return apiresponse;

                    }
                     await _redisCache.SetIntoCache(token,UserTokenKey);
                }


                   
               
                response.InterServiceToken = await GenerateInterServiceToken(Convert.ToInt64(userid), Convert.ToInt32(orgid), Convert.ToInt32(roleid), orgCode);

                // return true from JWT token if validation successful
                apiresponse.StatusCode = StatusCodes.Status200OK;
                apiresponse.Result = response;
                return apiresponse;
            }
            catch (Exception ex)
            {
                // return null if validation fails
                apiresponse.StatusCode = StatusCodes.Status401Unauthorized;
                apiresponse.Errors.Add("Not valid Token,Token validation failed");
                apiresponse.Errors.Add("StackTrace" + ex.StackTrace +"Message: "+ex.Message);
                apiresponse.Errors.Add("InnerException" + ex.InnerException);
                apiresponse.Errors.Add("Security Key" + key);
                return apiresponse;
            }
        }

        /// <summary>
        /// Method to generate Interservice token
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="orgId"></param>
        /// <param name="roleId"></param>
        /// <returns></returns>
        private async Task<string> GenerateInterServiceToken(long userId, int orgId, int roleId, string orgCode)
        {
           
                var jwtSecureKey = await _jwtSecurityTokenHelper.GetJWTSecureKey(isInterServiceKey: true);
                var key = Encoding.ASCII.GetBytes(jwtSecureKey);
                double expire_second = Convert.ToDouble(_configuration["AppSettings:InterserviceTokenExpiresIn"]);
                var tokenHandeler = new JwtSecurityTokenHandler();
                var utcNow = DateTimeOffset.UtcNow;
                var iattime = (utcNow.ToUnixTimeSeconds()-30).ToString();
                var tokenDescriptor = new SecurityTokenDescriptor
                {
                    Subject = new ClaimsIdentity(new Claim[] {
                    new Claim("uid", userId.ToString()),
                    new Claim("oid", orgId.ToString()),
                    new Claim("rid", roleId.ToString()),
                    new Claim("ocode", orgCode),
                    new Claim("iat", iattime),
                    new Claim("nbf", iattime)
                }),
                    Expires = utcNow.AddSeconds(expire_second).UtcDateTime,
                    Issuer = _configuration["AppSettings:Issuer"],
                    
                    Audience = _configuration["AppSettings:Audience"],
                    SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
                };

                var token = tokenHandeler.CreateToken(tokenDescriptor);
                var finalToken = tokenHandeler.WriteToken(token);
                return finalToken;
            
            
        }

        public async Task<ApiResponse<AuthContext>> ValidateInterServiceToken(string token)
        {
            var apiresponse = new ApiResponse<AuthContext>();
            apiresponse.Result = new AuthContext();
            if (token == null)
                return apiresponse;

            var tokenHandler = new JwtSecurityTokenHandler();
            var jwtInterServiceKey = await _jwtSecurityTokenHelper.GetJWTSecureKey(isInterServiceKey: true);
            var utcNow = DateTimeOffset.UtcNow;
            var iattime = utcNow.ToUnixTimeSeconds();

            var key = Encoding.ASCII.GetBytes(jwtInterServiceKey);
            try
            {
                ClaimsPrincipal principal = tokenHandler.ValidateToken(token, new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidateIssuer = true,
                    ValidateAudience = true,
                    ValidateActor = false,
                    ValidateLifetime = true,
                    ValidIssuer = _configuration["AppSettings:Issuer"],
                    ValidAudience = _configuration["AppSettings:Audience"],
                    // set clockskew to zero so tokens expire exactly at token expiration time (instead of 5 minutes later)
                    ClockSkew = TimeSpan.Zero
                }, out SecurityToken validatedToken);

                var jwtToken = (JwtSecurityToken)validatedToken;
                var orgid = jwtToken.Claims.First(c => c.Type == "oid").Value;
                var response = new AuthContext()
                {
                    IsAuthenticated = true,
                    Oid = jwtToken.Claims.First(c => c.Type == "oid").Value,
                    Uid = jwtToken.Claims.First(c => c.Type == "uid").Value,
                    Ocode = jwtToken.Claims.First(c => c.Type == "ocode").Value,
                    Rid = jwtToken.Claims.First(c => c.Type == "rid").Value,
                    //  InterServiceToken = GenerateInterServiceToken(0, Convert.ToInt32(orgid), 0, jwtToken.Claims.First(c => c.Type == "ocode").Value)
                    InterServiceToken = token
                };
                // return true from JWT token if validation successful
                apiresponse.StatusCode = StatusCodes.Status200OK;
                apiresponse.Result = response;
                return apiresponse;
            }
            catch (Exception ex)
            {
                // return null if validation fails
                apiresponse.StatusCode = StatusCodes.Status401Unauthorized;               
                apiresponse.Errors.Add("Inter Service Token validation failed");
                apiresponse.Errors.Add("StackTrace ==" + ex.StackTrace + "Message: " + ex.Message);
                apiresponse.Errors.Add("InnerException ==" + ex.InnerException);
                apiresponse.Errors.Add("Security Key ==" + key);
                apiresponse.Errors.Add("Server Time ==" + DateTime.Now);
                apiresponse.Errors.Add("UTC Time ==" + iattime);
                apiresponse.Errors.Add("Inter Service Token ==" + token);
                return apiresponse;
            }
        }

        public async Task<ApiResponse<PublicAuthContext>> ValidatePublicToken(string token, ReadOnlyAuthDBContext readOnlyAuthDBContext)
        {
            var apiresponse = new ApiResponse<PublicAuthContext>();
            apiresponse.Result = new PublicAuthContext();
            if (token == null)
                return apiresponse;

            var tokenHandler = new JwtSecurityTokenHandler();
            var jwtSecureKey = await _jwtSecurityTokenHelper.GetJWTExternalRSASecureKey();
            var key = Encoding.ASCII.GetBytes(jwtSecureKey);
            try
            {
                ClaimsPrincipal principal = tokenHandler.ValidateToken(token, new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidateIssuer = true,
                    ValidateAudience = true,
                    ValidateActor = false,
                    ValidateLifetime = true,
                    ValidIssuer = _configuration["AppSettings:Issuer"],
                    ValidAudience = _configuration["AppSettings:Audience"],
                    // set clockskew to zero so tokens expire exactly at token expiration time (instead of 5 minutes later)
                    ClockSkew = TimeSpan.Zero
                }, out SecurityToken validatedToken);

                var jwtToken = (JwtSecurityToken)validatedToken;

                var response = new PublicAuthContext()
                {
                    IsAuthenticated = true,
                    prid = jwtToken.Claims.First(c => c.Type == "prid").Value,
                    Ocode = jwtToken.Claims.First(c => c.Type == "ocode").Value,
                    tt = jwtToken.Claims.First(c=>c.Type == "tt").Value,
                    oid = jwtToken.Claims.First(c => c.Type == "oid").Value,
                    fvt = jwtToken.Claims.First(c=> c.Type == "fvt").Value
                };
                
                /*var userid = jwtToken.Claims.First(c => c.Type == "uid").Value;
                var orgid = jwtToken.Claims.First(c => c.Type == "oid").Value;
                var roleid = jwtToken.Claims.First(c => c.Type == "rid").Value;
                var orgCode = jwtToken.Claims.First(c => c.Type == "ocode").Value;
                var tokenAssoId = await GetTokenForUser(Convert.ToInt64(userid), Convert.ToInt32(orgid), token, readOnlyAuthDBContext);
                if (tokenAssoId <= default(long))
                {
                    apiresponse.StatusCode = StatusCodes.Status401Unauthorized;
                    apiresponse.Errors.Add("Not valid Token");
                    return apiresponse;
                }*/

             
                // return true from JWT token if validation successful
                apiresponse.StatusCode = StatusCodes.Status200OK;
                apiresponse.Result = response;
                return apiresponse;
            }
            catch (Exception ex)
            {
                // return null if validation fails
                apiresponse.StatusCode = StatusCodes.Status401Unauthorized;
                apiresponse.Errors.Add("Validate public token failed");
                apiresponse.Errors.Add("StackTrace" + ex.StackTrace + "Message: " + ex.Message);
                apiresponse.Errors.Add("InnerException" + ex.InnerException);
                apiresponse.Errors.Add("Security Key" + key);
                return apiresponse;
            }
        }

    }
}
