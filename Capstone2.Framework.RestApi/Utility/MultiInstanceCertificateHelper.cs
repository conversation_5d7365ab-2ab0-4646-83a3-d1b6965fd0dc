using Capstone2.Framework.Business.Common;
using Microsoft.Extensions.Logging;
using System;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using System.Threading.Tasks;

namespace Capstone2.Framework.RestApi.Utility
{
    public interface IMultiInstanceCertificateHelper
    {
        Task<string> GetRSAPrivateKeyAsync(byte[] rsaCertificate);
        Task<string> GetInterServicePrivateKeyAsync(byte[] rsaCertificate);
        Task<string> GetPrivateKeyAsStringAsync(byte[] certificateBytes);
        Task<X509Certificate2> LoadCertificateAsync(byte[] certificateBytes);
    }

    public class MultiInstanceCertificateHelper : IMultiInstanceCertificateHelper
    {
        private readonly ILogger<MultiInstanceCertificateHelper> _logger;
        private readonly ICertificateHelper _certificateHelper;

        public MultiInstanceCertificateHelper(
            ILogger<MultiInstanceCertificateHelper> logger,
            ICertificateHelper certificateHelper)
        {
            _logger = logger;
            _certificateHelper = certificateHelper;
        }

        public async Task<string> GetRSAPrivateKeyAsync(byte[] rsaCertificate)
        {
            return await Task.Run(() =>
            {
                try
                {
                    return _certificateHelper.GetRSAPrivateKey(rsaCertificate);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error extracting RSA private key in multi-instance environment");
                    throw;
                }
            });
        }

        public async Task<string> GetInterServicePrivateKeyAsync(byte[] rsaCertificate)
        {
            return await Task.Run(() =>
            {
                try
                {
                    return _certificateHelper.GetInterServicePrivateKey(rsaCertificate);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error extracting inter-service private key in multi-instance environment");
                    throw;
                }
            });
        }

        public async Task<string> GetPrivateKeyAsStringAsync(byte[] certificateBytes)
        {
            return await Task.Run(() =>
            {
                try
                {
                    return _certificateHelper.GetPrivateKeyAsString(certificateBytes);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error extracting private key as string in multi-instance environment");
                    throw;
                }
            });
        }

        public async Task<X509Certificate2> LoadCertificateAsync(byte[] certificateBytes)
        {
            return await Task.Run(() =>
            {
                try
                {
                    // Use EphemeralKeySet to avoid key container issues in multi-instance Azure App Service
                    return new X509Certificate2(
                        certificateBytes,
                        string.Empty,
                        X509KeyStorageFlags.EphemeralKeySet | X509KeyStorageFlags.Exportable);
                }
                catch (CryptographicException ex) when (ex.Message.Contains("Keyset does not exist"))
                {
                    _logger.LogWarning(ex, "Keyset does not exist error, trying fallback approach");
                    
                    try
                    {
                        // Fallback: Try with UserKeySet
                        return new X509Certificate2(
                            certificateBytes,
                            string.Empty,
                            X509KeyStorageFlags.UserKeySet | X509KeyStorageFlags.Exportable);
                    }
                    catch (Exception fallbackEx)
                    {
                        _logger.LogError(fallbackEx, "Fallback certificate loading also failed");
                        
                        // Last resort: Try to export and reimport as PKCS12
                        try
                        {
                            var tempCert = new X509Certificate2(certificateBytes);
                            byte[] pfxData = tempCert.Export(X509ContentType.Pkcs12);
                            return new X509Certificate2(
                                pfxData,
                                (string)null,
                                X509KeyStorageFlags.EphemeralKeySet | X509KeyStorageFlags.Exportable);
                        }
                        catch (Exception lastResortEx)
                        {
                            _logger.LogError(lastResortEx, "All certificate loading methods failed");
                            throw new InvalidOperationException(
                                $"Unable to load certificate in multi-instance environment. " +
                                $"Original error: {ex.Message}, " +
                                $"Fallback error: {fallbackEx.Message}, " +
                                $"Last resort error: {lastResortEx.Message}", ex);
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error loading certificate in multi-instance environment");
                    throw;
                }
            });
        }
    }
}
