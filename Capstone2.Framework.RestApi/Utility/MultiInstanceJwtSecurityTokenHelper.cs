using Capstone2.Framework.Business.Common;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using System.Threading.Tasks;

namespace Capstone2.Framework.RestApi.Utility
{
    public interface IMultiInstanceJwtSecurityTokenHelper
    {
        Task<string> GetJwtSecureKey(string jwtCertificateName, bool isInterServiceKey = false);
        Task<string> GetECDSAJwtSecureKey(string jwtCertificateName);
        Task<string> GetJwtSecureKeyForTenant(string jwtCertificateName, bool isInterServiceKey = false);
    }

    public class MultiInstanceJwtSecurityTokenHelper : IMultiInstanceJwtSecurityTokenHelper
    {
        private readonly IMultiInstanceCacheHelper _multiInstanceCache;
        private readonly ICertificateHelper _certificateHelper;
        private readonly IKeyVaultHelper _keyVaultHelper;
        private readonly IConfiguration _configuration;
        private readonly ILogger<MultiInstanceJwtSecurityTokenHelper> _logger;

        public MultiInstanceJwtSecurityTokenHelper(
            IMultiInstanceCacheHelper multiInstanceCache,
            ICertificateHelper certificateHelper,
            IKeyVaultHelper keyVaultHelper,
            IConfiguration configuration,
            ILogger<MultiInstanceJwtSecurityTokenHelper> logger)
        {
            _multiInstanceCache = multiInstanceCache;
            _certificateHelper = certificateHelper;
            _keyVaultHelper = keyVaultHelper;
            _configuration = configuration;
            _logger = logger;
        }

        public async Task<string> GetJwtSecureKey(string jwtCertificateName, bool isInterServiceKey = false)
        {
            var cachedSecureKey = $"_jwtSecureKey_{jwtCertificateName}_{isInterServiceKey}";
            
            return await _multiInstanceCache.GetFromCacheWithFallback(
                cachedSecureKey,
                async () => await GenerateJwtSecureKey(jwtCertificateName, isInterServiceKey),
                TimeSpan.FromHours(22) // Cache for 22 hours
            );
        }

        public async Task<string> GetECDSAJwtSecureKey(string jwtCertificateName)
        {
            var cachedSecureKey = $"_ecdsaJwtSecureKey_{jwtCertificateName}";
            
            return await _multiInstanceCache.GetFromCacheWithFallback(
                cachedSecureKey,
                async () => await GenerateECDSAJwtSecureKey(jwtCertificateName),
                TimeSpan.FromHours(22) // Cache for 22 hours
            );
        }

        public async Task<string> GetJwtSecureKeyForTenant(string jwtCertificateName, bool isInterServiceKey = false)
        {
            var cachedSecureKey = $"_jwtSecureKeyTenant_{jwtCertificateName}_{isInterServiceKey}";
            
            return await _multiInstanceCache.GetFromCacheWithFallback(
                cachedSecureKey,
                async () => await GenerateJwtSecureKey(jwtCertificateName, isInterServiceKey),
                TimeSpan.FromHours(22) // Cache for 22 hours
            );
        }

        private async Task<string> GenerateJwtSecureKey(string jwtCertificateName, bool isInterServiceKey)
        {
            try
            {
                _logger.LogInformation($"Generating JWT secure key for certificate: {jwtCertificateName}, isInterService: {isInterServiceKey}");

                // Try to get existing certificate from Key Vault
                byte[] rsaCertificateBytes = await _keyVaultHelper.GetSetCertificateKey(
                    jwtCertificateName,
                    _configuration["AzureAD:KVTenantId"],
                    _configuration["AzureAD:KVClientId"],
                    _configuration["AzureAD:KVClientSecret"],
                    string.Format(_configuration["AzureAD:KeyVaultUrl"], _configuration["AzureAD:KeyVaultName"]),
                    null);

                if (rsaCertificateBytes != null)
                {
                    try
                    {
                        // Use EphemeralKeySet to avoid key container issues in multi-instance scenarios
                        X509Certificate2 certificate = new X509Certificate2(
                            rsaCertificateBytes, 
                            string.Empty, 
                            X509KeyStorageFlags.EphemeralKeySet | X509KeyStorageFlags.Exportable);
                        
                        DateTime certExpiry = Convert.ToDateTime(certificate.GetExpirationDateString());
                        if (certExpiry.Date >= DateTime.UtcNow.Date)
                        {
                            string jwtSecureKey = isInterServiceKey 
                                ? _certificateHelper.GetInterServicePrivateKey(rsaCertificateBytes) 
                                : _certificateHelper.GetRSAPrivateKey(rsaCertificateBytes);
                            
                            _logger.LogInformation($"Successfully extracted key from existing certificate: {jwtCertificateName}");
                            return jwtSecureKey;
                        }
                        else
                        {
                            _logger.LogWarning($"Certificate {jwtCertificateName} has expired, generating new one");
                        }
                    }
                    catch (CryptographicException ex)
                    {
                        _logger.LogError(ex, $"Error loading certificate {jwtCertificateName}, generating new one");
                    }
                }

                // Generate new certificate if existing one is invalid or doesn't exist
                _logger.LogInformation($"Generating new certificate for: {jwtCertificateName}");
                rsaCertificateBytes = _certificateHelper.GenerateRSACertificateBytes();
                
                // Store new certificate in Key Vault
                await _keyVaultHelper.GetSetCertificateKey(
                    jwtCertificateName,
                    _configuration["AzureAD:KVTenantId"],
                    _configuration["AzureAD:KVClientId"],
                    _configuration["AzureAD:KVClientSecret"],
                    string.Format(_configuration["AzureAD:KeyVaultUrl"], _configuration["AzureAD:KeyVaultName"]),
                    rsaCertificateBytes);

                string newJwtSecureKey = isInterServiceKey 
                    ? _certificateHelper.GetInterServicePrivateKey(rsaCertificateBytes) 
                    : _certificateHelper.GetRSAPrivateKey(rsaCertificateBytes);

                _logger.LogInformation($"Successfully generated new JWT secure key for: {jwtCertificateName}");
                return newJwtSecureKey;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error generating JWT secure key for certificate: {jwtCertificateName}");
                throw;
            }
        }

        private async Task<string> GenerateECDSAJwtSecureKey(string jwtCertificateName)
        {
            try
            {
                _logger.LogInformation($"Generating ECDSA JWT secure key for certificate: {jwtCertificateName}");

                // Try to get existing certificate from Key Vault
                byte[] ecdsaCertificateBytes = await _keyVaultHelper.GetSetCertificateKey(
                    jwtCertificateName,
                    _configuration["AzureAD:KVTenantId"],
                    _configuration["AzureAD:KVClientId"],
                    _configuration["AzureAD:KVClientSecret"],
                    string.Format(_configuration["AzureAD:KeyVaultUrl"], _configuration["AzureAD:KeyVaultName"]),
                    null);

                if (ecdsaCertificateBytes != null)
                {
                    try
                    {
                        // Use EphemeralKeySet to avoid key container issues in multi-instance scenarios
                        X509Certificate2 certificate = new X509Certificate2(
                            ecdsaCertificateBytes,
                            string.Empty,
                            X509KeyStorageFlags.EphemeralKeySet | X509KeyStorageFlags.Exportable);

                        DateTime certExpiry = Convert.ToDateTime(certificate.GetExpirationDateString());
                        if (certExpiry.Date >= DateTime.UtcNow.Date)
                        {
                            string jwtSecureKey = _certificateHelper.GetPrivateKeyAsString(ecdsaCertificateBytes);
                            _logger.LogInformation($"Successfully extracted ECDSA key from existing certificate: {jwtCertificateName}");
                            return jwtSecureKey;
                        }
                        else
                        {
                            _logger.LogWarning($"ECDSA Certificate {jwtCertificateName} has expired, generating new one");
                        }
                    }
                    catch (CryptographicException ex)
                    {
                        _logger.LogError(ex, $"Error loading ECDSA certificate {jwtCertificateName}, generating new one");
                    }
                }

                // Generate new ECDSA certificate if existing one is invalid or doesn't exist
                _logger.LogInformation($"Generating new ECDSA certificate for: {jwtCertificateName}");
                ecdsaCertificateBytes = _certificateHelper.CreateSelfSignedECDSACertificate();
                
                // Store new certificate in Key Vault
                await _keyVaultHelper.GetSetCertificateKey(
                    jwtCertificateName,
                    _configuration["AzureAD:KVTenantId"],
                    _configuration["AzureAD:KVClientId"],
                    _configuration["AzureAD:KVClientSecret"],
                    string.Format(_configuration["AzureAD:KeyVaultUrl"], _configuration["AzureAD:KeyVaultName"]),
                    ecdsaCertificateBytes);

                string newJwtSecureKey = _certificateHelper.GetPrivateKeyAsString(ecdsaCertificateBytes);
                _logger.LogInformation($"Successfully generated new ECDSA JWT secure key for: {jwtCertificateName}");
                return newJwtSecureKey;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error generating ECDSA JWT secure key for certificate: {jwtCertificateName}");
                throw;
            }
        }
    }
}
