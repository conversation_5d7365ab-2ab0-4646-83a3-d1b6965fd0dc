﻿using Capstone2.Framework.RestApi.Context;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Capstone2.Framework.RestApi.Utility
{
    public static class AuthServiceExtensions
    {
        public static IServiceCollection RegisterAuthUtilityDI(this IServiceCollection services)
        {
            services.AddDbContext<ReadOnlyAuthDBContext>(options => options.UseSqlServer());
            services.AddTransient<IAuthUtility, AuthUtility>();
            services.AddTransient<ICertificateHelper, CertificateHelper>();
            services.AddTransient<IKeyVaultHelper, KeyVaultHelper>();
            services.AddTransient<IJwtSecurityTokenHelper, JwtSecurityTokenHelper>();
            services.AddTransient<IASBMessageSenderHelper, ASBMessageSenderHelper>();
            return services;
        }
    }
}
