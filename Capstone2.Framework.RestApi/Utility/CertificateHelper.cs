using System;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using System.Text;

namespace Capstone2.Framework.RestApi.Utility
{
    public class CertificateHelper : ICertificateHelper
    {
        public byte[] GenerateRSACertificateBytes()
        {
            try
            {
                using (RSA parentRSA = RSA.Create(4096))
                using (RSA scopeRSA = RSA.Create(2048))
                {
                    CertificateRequest parentCertReq = new CertificateRequest("CN=Capstone Systems Certificate Authority",
                        parentRSA, HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1);
                    parentCertReq.CertificateExtensions.Add(new X509BasicConstraintsExtension(true, false, 0, true));
                    parentCertReq.CertificateExtensions.Add(new X509SubjectKeyIdentifierExtension(parentCertReq.PublicKey, false));
                    using (X509Certificate2 parentCert = parentCertReq.CreateSelfSigned(DateTimeOffset.UtcNow.AddDays(-2), DateTimeOffset.UtcNow.AddDays(45)))
                    {
                        CertificateRequest certRequest = new CertificateRequest("CN=Capstone,O=Capstone Systems,L=Canberra,S=ACT,C=AU",
                                                                    scopeRSA, HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1);
                        certRequest.CertificateExtensions.Add(new X509BasicConstraintsExtension(false, false, 0, false));
                        certRequest.CertificateExtensions.Add(new X509KeyUsageExtension(X509KeyUsageFlags.DigitalSignature | X509KeyUsageFlags.NonRepudiation, false));
                        certRequest.CertificateExtensions.Add(new X509EnhancedKeyUsageExtension(new OidCollection { new Oid("1.3.6.1.5.5.7.3.8") }, true));
                        certRequest.CertificateExtensions.Add(new X509SubjectKeyIdentifierExtension(certRequest.PublicKey, false));
                        using (X509Certificate2 x509Cert = certRequest.Create(parentCert, DateTimeOffset.UtcNow.AddDays(-1), DateTimeOffset.UtcNow.AddDays(44),
                            new byte[] { 1, 2, 3, 4, 5, 6, 7, 8, 9 }))
                        {
                            using (X509Certificate2 certNew = x509Cert.CopyWithPrivateKey(scopeRSA))
                            {
                                return certNew.Export(X509ContentType.Pfx);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                //TODO:AI Log
                return null;
            }

        }
        public string GetRSAPrivateKey(byte[] rsaCertificate)
        {
            // Try multiple approaches to handle different certificate storage scenarios
            Exception lastException = null;

            // Approach 1: Try with default flags and RSA parameters extraction
            try
            {
                X509Certificate2 certificate = new X509Certificate2(rsaCertificate, string.Empty, X509KeyStorageFlags.Exportable);

                if (!certificate.HasPrivateKey)
                {
                    throw new InvalidOperationException("The certificate does not contain a private key.");
                }

                using (RSA rsaPrivateKey = certificate.GetRSAPrivateKey())
                {
                    if (rsaPrivateKey != null)
                    {
                        // Try to get RSA parameters and reconstruct the key
                        try
                        {
                            RSAParameters parameters = rsaPrivateKey.ExportParameters(true);
                            using (RSA newRsa = RSA.Create())
                            {
                                newRsa.ImportParameters(parameters);
                                byte[] pvtKeyByte = newRsa.ExportPkcs8PrivateKey();
                                return Convert.ToBase64String(pvtKeyByte);
                            }
                        }
                        catch
                        {
                            // Fallback to direct export if parameters work
                            try
                            {
                                byte[] pvtKeyByte = rsaPrivateKey.ExportPkcs8PrivateKey();
                                return Convert.ToBase64String(pvtKeyByte);
                            }
                            catch
                            {
                                byte[] pvtKeyByte = rsaPrivateKey.ExportRSAPrivateKey();
                                return Convert.ToBase64String(pvtKeyByte);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                lastException = ex;
            }

            // Approach 2: Try with PKCS12 re-export approach (most reliable for multi-instance)
            try
            {
                // Load certificate with minimal flags
                X509Certificate2 certificate = new X509Certificate2(rsaCertificate, string.Empty, X509KeyStorageFlags.Exportable);

                if (certificate.HasPrivateKey)
                {
                    // Export to PKCS12 and re-import with EphemeralKeySet
                    byte[] pfxData = certificate.Export(X509ContentType.Pkcs12);

                    using (X509Certificate2 pfxCert = new X509Certificate2(
                        pfxData,
                        (string)null,
                        X509KeyStorageFlags.Exportable | X509KeyStorageFlags.EphemeralKeySet))
                    {
                        using (RSA rsa = pfxCert.GetRSAPrivateKey())
                        {
                            if (rsa != null)
                            {
                                // Try RSA parameters approach first
                                try
                                {
                                    RSAParameters parameters = rsa.ExportParameters(true);
                                    using (RSA newRsa = RSA.Create())
                                    {
                                        newRsa.ImportParameters(parameters);
                                        byte[] pvtKeyByte = newRsa.ExportPkcs8PrivateKey();
                                        return Convert.ToBase64String(pvtKeyByte);
                                    }
                                }
                                catch
                                {
                                    // Direct export fallback
                                    try
                                    {
                                        byte[] pvtKeyByte = rsa.ExportPkcs8PrivateKey();
                                        return Convert.ToBase64String(pvtKeyByte);
                                    }
                                    catch
                                    {
                                        byte[] pvtKeyByte = rsa.ExportRSAPrivateKey();
                                        return Convert.ToBase64String(pvtKeyByte);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                lastException = ex;
            }

            // Approach 3: Try with UserKeySet
            try
            {
                X509Certificate2 certificate = new X509Certificate2(
                    rsaCertificate,
                    string.Empty,
                    X509KeyStorageFlags.UserKeySet | X509KeyStorageFlags.Exportable);

                if (certificate.HasPrivateKey)
                {
                    using (RSA rsaPrivateKey = certificate.GetRSAPrivateKey())
                    {
                        if (rsaPrivateKey != null)
                        {
                            try
                            {
                                RSAParameters parameters = rsaPrivateKey.ExportParameters(true);
                                using (RSA newRsa = RSA.Create())
                                {
                                    newRsa.ImportParameters(parameters);
                                    byte[] pvtKeyByte = newRsa.ExportPkcs8PrivateKey();
                                    return Convert.ToBase64String(pvtKeyByte);
                                }
                            }
                            catch
                            {
                                try
                                {
                                    byte[] pvtKeyByte = rsaPrivateKey.ExportPkcs8PrivateKey();
                                    return Convert.ToBase64String(pvtKeyByte);
                                }
                                catch
                                {
                                    byte[] pvtKeyByte = rsaPrivateKey.ExportRSAPrivateKey();
                                    return Convert.ToBase64String(pvtKeyByte);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                lastException = ex;
            }

            // Approach 4: Try creating a new RSA key from the certificate's key material
            try
            {
                X509Certificate2 certificate = new X509Certificate2(rsaCertificate, string.Empty, X509KeyStorageFlags.Exportable);

                if (certificate.HasPrivateKey)
                {
                    using (RSA rsaKey = certificate.GetRSAPrivateKey())
                    {
                        if (rsaKey != null)
                        {
                            // Create a new RSA instance and copy the key
                            using (RSA newRsa = RSA.Create(rsaKey.KeySize))
                            {
                                // Try to copy the key material
                                RSAParameters parameters = rsaKey.ExportParameters(true);
                                newRsa.ImportParameters(parameters);

                                byte[] pvtKeyByte = newRsa.ExportPkcs8PrivateKey();
                                return Convert.ToBase64String(pvtKeyByte);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                lastException = ex;
            }

            // If all approaches failed, throw the last exception
            throw new InvalidOperationException($"Error extracting RSA private key from certificate using all available methods. Last error: {lastException?.Message}", lastException);
        }

        public string GetInterServicePrivateKey(byte[] rsaCertificate)
        {
            // Try multiple approaches to handle different certificate storage scenarios
            Exception lastException = null;

            // Approach 1: Try with default flags and RSA parameters extraction
            try
            {
                X509Certificate2 certificate = new X509Certificate2(rsaCertificate, string.Empty, X509KeyStorageFlags.Exportable);

                using (RSA rsaPublicKey = certificate.GetRSAPublicKey())
                {
                    if (rsaPublicKey != null)
                    {
                        // Try to get RSA parameters and reconstruct the key
                        try
                        {
                            RSAParameters parameters = rsaPublicKey.ExportParameters(false); // false for public key only
                            using (RSA newRsa = RSA.Create())
                            {
                                newRsa.ImportParameters(parameters);
                                byte[] pubKeyByte = newRsa.ExportRSAPublicKey();
                                return Convert.ToBase64String(pubKeyByte);
                            }
                        }
                        catch
                        {
                            // Fallback to direct export
                            byte[] pubKeyByte = rsaPublicKey.ExportRSAPublicKey();
                            return Convert.ToBase64String(pubKeyByte);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                lastException = ex;
            }

            // Approach 2: Try with EphemeralKeySet for multi-instance scenarios
            try
            {
                X509Certificate2 certificate = new X509Certificate2(
                    rsaCertificate,
                    string.Empty,
                    X509KeyStorageFlags.EphemeralKeySet | X509KeyStorageFlags.Exportable);

                using (RSA rsaPublicKey = certificate.GetRSAPublicKey())
                {
                    if (rsaPublicKey != null)
                    {
                        try
                        {
                            RSAParameters parameters = rsaPublicKey.ExportParameters(false);
                            using (RSA newRsa = RSA.Create())
                            {
                                newRsa.ImportParameters(parameters);
                                byte[] pubKeyByte = newRsa.ExportRSAPublicKey();
                                return Convert.ToBase64String(pubKeyByte);
                            }
                        }
                        catch
                        {
                            byte[] pubKeyByte = rsaPublicKey.ExportRSAPublicKey();
                            return Convert.ToBase64String(pubKeyByte);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                lastException = ex;
            }

            // Approach 3: Try with UserKeySet
            try
            {
                X509Certificate2 certificate = new X509Certificate2(
                    rsaCertificate,
                    string.Empty,
                    X509KeyStorageFlags.UserKeySet | X509KeyStorageFlags.Exportable);

                using (RSA rsaPublicKey = certificate.GetRSAPublicKey())
                {
                    if (rsaPublicKey != null)
                    {
                        try
                        {
                            RSAParameters parameters = rsaPublicKey.ExportParameters(false);
                            using (RSA newRsa = RSA.Create())
                            {
                                newRsa.ImportParameters(parameters);
                                byte[] pubKeyByte = newRsa.ExportRSAPublicKey();
                                return Convert.ToBase64String(pubKeyByte);
                            }
                        }
                        catch
                        {
                            byte[] pubKeyByte = rsaPublicKey.ExportRSAPublicKey();
                            return Convert.ToBase64String(pubKeyByte);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                lastException = ex;
            }

            // Approach 4: PKCS12 export/import approach
            try
            {
                X509Certificate2 certificate = new X509Certificate2(rsaCertificate, string.Empty, X509KeyStorageFlags.Exportable);
                byte[] pfxData = certificate.Export(X509ContentType.Pkcs12);

                using (X509Certificate2 pfxCert = new X509Certificate2(
                    pfxData,
                    (string)null,
                    X509KeyStorageFlags.Exportable))
                {
                    using (RSA rsa = pfxCert.GetRSAPublicKey())
                    {
                        if (rsa != null)
                        {
                            try
                            {
                                RSAParameters parameters = rsa.ExportParameters(false);
                                using (RSA newRsa = RSA.Create())
                                {
                                    newRsa.ImportParameters(parameters);
                                    byte[] pubKeyByte = newRsa.ExportRSAPublicKey();
                                    return Convert.ToBase64String(pubKeyByte);
                                }
                            }
                            catch
                            {
                                byte[] pubKeyByte = rsa.ExportRSAPublicKey();
                                return Convert.ToBase64String(pubKeyByte);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                lastException = ex;
            }

            // If all approaches failed, throw the last exception
            throw new InvalidOperationException($"Error extracting RSA public key from certificate using all available methods. Last error: {lastException?.Message}", lastException);
        }


        public byte[] CreateSelfSignedECDSACertificate()
        {
            using (ECDsa ecdsa = ECDsa.Create(ECCurve.NamedCurves.nistP256))
            {
                var certificateRequest = new CertificateRequest(
                    $"CN=Capstone Systems Certificate Authority",
                    ecdsa,
                    HashAlgorithmName.SHA256
                );

                // Add optional extensions, e.g., for Key Usage
                certificateRequest.CertificateExtensions.Add(

                    new X509KeyUsageExtension(                         
                        X509KeyUsageFlags.DigitalSignature | X509KeyUsageFlags.NonRepudiation,                       
                        critical: true
                    )
                ) ;

                var notBefore = DateTimeOffset.UtcNow;
                var notAfter = notBefore.AddYears(1);


                
                

                    // Create the self-signed certificate
                    using (X509Certificate2 certificate = certificateRequest.CreateSelfSigned(notBefore, notAfter))
                {

                       
                    // Export as PFX with no password
                    return certificate.Export(X509ContentType.Pfx);
                }
            }


          /*  // Create a new ECDsa key pair
            using (ECDsa ecdsa = ECDsa.Create(ECCurve.NamedCurves.nistP256))
            {
                // Define the certificate request with the subject name and the ECDSA key
                var request = new CertificateRequest(
                    new X500DistinguishedName("CN=Capstone Systems Certificate Authority"),
                    ecdsa,
                    HashAlgorithmName.SHA256
                );

                // Create the self-signed certificate, valid for 5 years
                var certificate = request.CreateSelfSigned(
                    DateTimeOffset.Now,
                    DateTimeOffset.Now.AddYears(5)

                );

                // Export the certificate to a PFX (PKCS#12) with the private key marked as exportable
                byte[] pfxBytes = certificate.Export(X509ContentType.Pfx, string.Empty);

                // Import the certificate back into an X509Certificate2 object with the exportable flag
                X509Certificate2 certificate1 = new(
                        pfxBytes,
                        string.Empty, 
                        X509KeyStorageFlags.Exportable | X509KeyStorageFlags.MachineKeySet | X509KeyStorageFlags.PersistKeySet
                    );

                return certificate1.Export(X509ContentType.Pfx);


            }*/
        }






    


        public string GetPrivateKeyAsString(byte[] certificateBytes)
        {
            try
            {
                // Load the certificate from the byte array without a password
                // Use EphemeralKeySet to avoid key container issues
                X509Certificate2 certificate = new X509Certificate2(
                    certificateBytes,
                    string.Empty, // No password
                    X509KeyStorageFlags.EphemeralKeySet | X509KeyStorageFlags.Exportable
                );

                // Extract the private key as an ECDsa object
                using (ECDsa ecdsa = certificate.GetECDsaPrivateKey())
                {
                    if (ecdsa == null)
                    {
                        throw new ArgumentException("The certificate does not contain an ECDSA private key.");
                    }

                    // Export the private key to a byte array in PKCS8 format
                    byte[] privateKeyBytes = ecdsa.ExportPkcs8PrivateKey();

                    // Convert the private key byte array to a PEM-formatted string
                    string privateKeyPem = ConvertToPem(privateKeyBytes, "EC PRIVATE KEY");

                    return privateKeyPem;
                }
            }
            catch (CryptographicException ex) when (ex.Message.Contains("Keyset does not exist"))
            {
                // Fallback approach for "Keyset does not exist" error
                try
                {
                    // Try with UserKeySet instead of MachineKeySet
                    X509Certificate2 certificate = new X509Certificate2(
                        certificateBytes,
                        string.Empty,
                        X509KeyStorageFlags.UserKeySet | X509KeyStorageFlags.Exportable);

                    // Try to export the certificate to PKCS12 and reimport
                    byte[] pfxData = certificate.Export(X509ContentType.Pkcs12);
                    using (X509Certificate2 pfxCert = new X509Certificate2(
                        pfxData,
                        (string)null,
                        X509KeyStorageFlags.Exportable | X509KeyStorageFlags.EphemeralKeySet))
                    {
                        using (ECDsa ecdsa = pfxCert.GetECDsaPrivateKey())
                        {
                            if (ecdsa == null)
                            {
                                throw new ArgumentException("The certificate does not contain an ECDSA private key.");
                            }

                            // Export the private key to a byte array in PKCS8 format
                            byte[] privateKeyBytes = ecdsa.ExportPkcs8PrivateKey();

                            // Convert the private key byte array to a PEM-formatted string
                            string privateKeyPem = ConvertToPem(privateKeyBytes, "EC PRIVATE KEY");

                            return privateKeyPem;
                        }
                    }
                }
                catch (Exception fallbackEx)
                {
                    throw new InvalidOperationException($"Unable to extract ECDSA private key from certificate. Original error: {ex.Message}, Fallback error: {fallbackEx.Message}", ex);
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Error extracting ECDSA private key from certificate: {ex.Message}", ex);
            }
        }

        private static string ConvertToPem(byte[] data, string header)
        {
            StringBuilder builder = new StringBuilder();
            builder.AppendLine($"-----BEGIN {header}-----");
            builder.AppendLine(Convert.ToBase64String(data, Base64FormattingOptions.InsertLineBreaks));
            builder.AppendLine($"-----END {header}-----");
            return builder.ToString();
        }

    }
}
