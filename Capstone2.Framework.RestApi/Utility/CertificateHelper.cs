﻿using System;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using System.Text;

namespace Capstone2.Framework.RestApi.Utility
{
    public class CertificateHelper : ICertificateHelper
    {
        public byte[] GenerateRSACertificateBytes()
        {
            try
            {
                using (RSA parentRSA = RSA.Create(4096))
                using (RSA scopeRSA = RSA.Create(2048))
                {
                    CertificateRequest parentCertReq = new CertificateRequest("CN=Capstone Systems Certificate Authority",
                        parentRSA, HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1);
                    parentCertReq.CertificateExtensions.Add(new X509BasicConstraintsExtension(true, false, 0, true));
                    parentCertReq.CertificateExtensions.Add(new X509SubjectKeyIdentifierExtension(parentCertReq.PublicKey, false));
                    using (X509Certificate2 parentCert = parentCertReq.CreateSelfSigned(DateTimeOffset.UtcNow.AddDays(-2), DateTimeOffset.UtcNow.AddDays(45)))
                    {
                        CertificateRequest certRequest = new CertificateRequest("CN=Capstone,O=Capstone Systems,L=Canberra,S=ACT,C=AU",
                                                                    scopeRSA, HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1);
                        certRequest.CertificateExtensions.Add(new X509BasicConstraintsExtension(false, false, 0, false));
                        certRequest.CertificateExtensions.Add(new X509KeyUsageExtension(X509KeyUsageFlags.DigitalSignature | X509KeyUsageFlags.NonRepudiation, false));
                        certRequest.CertificateExtensions.Add(new X509EnhancedKeyUsageExtension(new OidCollection { new Oid("1.3.6.1.5.5.7.3.8") }, true));
                        certRequest.CertificateExtensions.Add(new X509SubjectKeyIdentifierExtension(certRequest.PublicKey, false));
                        using (X509Certificate2 x509Cert = certRequest.Create(parentCert, DateTimeOffset.UtcNow.AddDays(-1), DateTimeOffset.UtcNow.AddDays(44),
                            new byte[] { 1, 2, 3, 4, 5, 6, 7, 8, 9 }))
                        {
                            using (X509Certificate2 certNew = x509Cert.CopyWithPrivateKey(scopeRSA))
                            {
                                return certNew.Export(X509ContentType.Pfx);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                //TODO:AI Log
                return null;
            }

        }
        public string GetRSAPrivateKey(byte[] rsaCertificate)
        {
            X509Certificate2 certificate = new X509Certificate2(rsaCertificate, string.Empty, X509KeyStorageFlags.MachineKeySet | X509KeyStorageFlags.PersistKeySet | X509KeyStorageFlags.Exportable);
            RSA rsaPrivateKey = certificate.GetRSAPrivateKey();
            byte[] pvtKeyByte = rsaPrivateKey.ExportRSAPrivateKey();
            string pvtKeyStr = Convert.ToBase64String(pvtKeyByte);
            return pvtKeyStr;
        }

        public string GetInterServicePrivateKey(byte[] rsaCertificate)
        {
            X509Certificate2 certificate = new X509Certificate2(rsaCertificate, string.Empty, X509KeyStorageFlags.MachineKeySet | X509KeyStorageFlags.PersistKeySet | X509KeyStorageFlags.Exportable);
            RSA rsaPrivateKey = certificate.GetRSAPublicKey();
            byte[] pvtKeyByte = rsaPrivateKey.ExportRSAPublicKey();
            string pvtKeyStr = Convert.ToBase64String(pvtKeyByte);
            return pvtKeyStr;
        }


        public byte[] CreateSelfSignedECDSACertificate()
        {
            using (ECDsa ecdsa = ECDsa.Create(ECCurve.NamedCurves.nistP256))
            {
                var certificateRequest = new CertificateRequest(
                    $"CN=Capstone Systems Certificate Authority",
                    ecdsa,
                    HashAlgorithmName.SHA256
                );

                // Add optional extensions, e.g., for Key Usage
                certificateRequest.CertificateExtensions.Add(

                    new X509KeyUsageExtension(                         
                        X509KeyUsageFlags.DigitalSignature | X509KeyUsageFlags.NonRepudiation,                       
                        critical: true
                    )
                ) ;

                var notBefore = DateTimeOffset.UtcNow;
                var notAfter = notBefore.AddYears(1);


                
                

                    // Create the self-signed certificate
                    using (X509Certificate2 certificate = certificateRequest.CreateSelfSigned(notBefore, notAfter))
                {

                       
                    // Export as PFX with no password
                    return certificate.Export(X509ContentType.Pfx);
                }
            }


          /*  // Create a new ECDsa key pair
            using (ECDsa ecdsa = ECDsa.Create(ECCurve.NamedCurves.nistP256))
            {
                // Define the certificate request with the subject name and the ECDSA key
                var request = new CertificateRequest(
                    new X500DistinguishedName("CN=Capstone Systems Certificate Authority"),
                    ecdsa,
                    HashAlgorithmName.SHA256
                );

                // Create the self-signed certificate, valid for 5 years
                var certificate = request.CreateSelfSigned(
                    DateTimeOffset.Now,
                    DateTimeOffset.Now.AddYears(5)

                );

                // Export the certificate to a PFX (PKCS#12) with the private key marked as exportable
                byte[] pfxBytes = certificate.Export(X509ContentType.Pfx, string.Empty);

                // Import the certificate back into an X509Certificate2 object with the exportable flag
                X509Certificate2 certificate1 = new(
                        pfxBytes,
                        string.Empty, 
                        X509KeyStorageFlags.Exportable | X509KeyStorageFlags.MachineKeySet | X509KeyStorageFlags.PersistKeySet
                    );

                return certificate1.Export(X509ContentType.Pfx);


            }*/
        }






    


        public string GetPrivateKeyAsString(byte[] certificateBytes)
        {
            // Load the certificate from the byte array without a password
            X509Certificate2 certificate = new X509Certificate2(
                                                                              certificateBytes,
                                                                              string.Empty, // No password
                                                                              X509KeyStorageFlags.MachineKeySet | X509KeyStorageFlags.PersistKeySet | X509KeyStorageFlags.Exportable
                                                                          );
            // Extract the private key as an ECDsa object
            using (ECDsa ecdsa = certificate.GetECDsaPrivateKey())
            {
                if (ecdsa == null)
                {
                    throw new ArgumentException("The certificate does not contain an ECDSA private key.");
                }

                // Export the private key to a byte array in PEM format
                //byte[] privateKeyBytes = ecdsa.ExportECPrivateKey();


                

                // Now you should be able to export the private key
                byte[] privateKeyBytes = certificate.GetECDsaPrivateKey().ExportPkcs8PrivateKey();

                // Convert the private key byte array to a PEM-formatted string
                string privateKeyPem = ConvertToPem(privateKeyBytes, "EC PRIVATE KEY");

                return privateKeyPem;
            }
        }

        private static string ConvertToPem(byte[] data, string header)
        {
            StringBuilder builder = new StringBuilder();
            builder.AppendLine($"-----BEGIN {header}-----");
            builder.AppendLine(Convert.ToBase64String(data, Base64FormattingOptions.InsertLineBreaks));
            builder.AppendLine($"-----END {header}-----");
            return builder.ToString();
        }

    }
}
