﻿using Capstone2.Framework.RestApi.Dtos;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;

namespace Capstone2.Framework.RestApi
{
    /// <summary>
    /// This class implements the basic methods of a REST client.
    /// </summary>
    public class RestClient
    {
        /// <summary>
        /// Instance of <see cref="HttpClient"/> to be used by this client.
        /// </summary>
        private readonly HttpClient client;


        /// <summary>
        /// Base <see cref="Uri"/> to be used by this client.
        /// </summary>
        private readonly Uri baseUri;
        private readonly AuthContext authContext;

        /// <summary>
        /// Shared http client as it is recommended to use a singleton instance of HttpClient class in an application
        /// https://docs.microsoft.com/en-us/dotnet/api/system.net.http.httpclient?redirectedfrom=MSDN&view=netcore-2.1
        /// </summary>
        private readonly static Dictionary<long, HttpClient> httpClients = new Dictionary<long, HttpClient>() { { 0, new HttpClient() } };

        public RestClient(string baseUri, Dictionary<string, string> headerKeys)
        {
            this.baseUri = new Uri(baseUri);
            client = new HttpClient();
            foreach (var header in headerKeys)
            {
                this.client.DefaultRequestHeaders.Add(header.Key, header.Value);
            }
            JsonSerializerSettings = new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver(),
                DateFormatHandling = DateFormatHandling.IsoDateFormat,
                DateTimeZoneHandling = DateTimeZoneHandling.RoundtripKind,
                DateParseHandling = DateParseHandling.DateTimeOffset
            };

            client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
        }

        public RestClient(string baseUri, AuthContext authContext, string token)
        {
            this.baseUri = new Uri(baseUri);
            this.authContext = authContext;
            if (this.authContext == null)
            {
                this.authContext = new AuthContext { Credentials = new AuthCredentials { AuthToken = token } };
            }
            client = new HttpClient();
            JsonSerializerSettings = new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver(),
                DateFormatHandling = DateFormatHandling.IsoDateFormat,
                DateTimeZoneHandling = DateTimeZoneHandling.RoundtripKind,
                DateParseHandling = DateParseHandling.DateTimeOffset
            };

            client.DefaultRequestHeaders
                .Accept
                .Add(new MediaTypeWithQualityHeaderValue("application/json"));
        }
        public RestClient(string baseUri, AuthContext authContext, string token, string interServiceToken)
        {
            this.baseUri = new Uri(baseUri);
            this.authContext = authContext;
            if (this.authContext == null)
            {
                this.authContext = new AuthContext { Credentials = new AuthCredentials { AuthToken = token }, InterServiceToken = interServiceToken };
            }
            client = new HttpClient();
            JsonSerializerSettings = new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver(),
                DateFormatHandling = DateFormatHandling.IsoDateFormat,
                DateTimeZoneHandling = DateTimeZoneHandling.RoundtripKind,
                DateParseHandling = DateParseHandling.DateTimeOffset
            };

            client.DefaultRequestHeaders
                .Accept
                .Add(new MediaTypeWithQualityHeaderValue("application/json"));
        }
        public RestClient(string baseUri, AuthContext authContext, string token, string interServiceToken, Dictionary<string, string> customHeaders)
        {
            this.baseUri = new Uri(baseUri);
            this.authContext = authContext;
            if (this.authContext == null)
            {
                this.authContext = new AuthContext { Credentials = new AuthCredentials { AuthToken = token }, InterServiceToken = interServiceToken };
            }
            client = new HttpClient();
            foreach (var header in customHeaders)
            {
                this.client.DefaultRequestHeaders.Add(header.Key, header.Value);
            }
            JsonSerializerSettings = new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver(),
                DateFormatHandling = DateFormatHandling.IsoDateFormat,
                DateTimeZoneHandling = DateTimeZoneHandling.RoundtripKind,
                DateParseHandling = DateParseHandling.DateTimeOffset
            };

            client.DefaultRequestHeaders
                .Accept
                .Add(new MediaTypeWithQualityHeaderValue("application/json"));
        }
        private HttpClient GetHttpClientWithTimeout(long timeoutMilliSeconds)
        {
            var customTimeoutHttpClient = new HttpClient();
            if (timeoutMilliSeconds > 0)
            {
                customTimeoutHttpClient.Timeout = TimeSpan.FromMilliseconds(timeoutMilliSeconds);
            }
            return customTimeoutHttpClient;
        }

        /// <summary>
        /// Gets or sets the <see cref="JsonSerializerSettings"/> used to serialize and deserialize the objects. 
        /// </summary>
        public JsonSerializerSettings JsonSerializerSettings { get; set; }

        /// <summary>
        /// Gets or sets the number of retries per request.
        /// </summary>
        public int RetryCount
        {
            get => this.RetryWaitTimes?.ToArray()
                       .Length ?? 0;

            set
            {
                if (value <= 0)
                {
                    throw new ArgumentOutOfRangeException(nameof(value));
                }

                this.RetryWaitTimes = Enumerable.Range(0, value)
                    .Select(i => new TimeSpan(0));
            }
        }

        /// <summary>
        /// Gets or sets the number of retries per request and the <see cref="TimeSpan"/> to wait between each request.
        /// </summary>
        public IEnumerable<TimeSpan> RetryWaitTimes { get; set; }

        /// <summary>
        /// Sends a DELETE request to the specified Uri as an asynchronous operation.
        /// </summary>
        /// <param name="resourceUri">A string containing the resource relative Uri.</param>
        /// <param name="requestBody">The request body to be sent to the server.</param>
        /// <param name="mediaType">The media-type to be sent to the server.</param>
        /// <typeparam name="T">Type of the result.</typeparam>
        /// <returns>The <see cref="Task"/> object representing the asynchronous operation.</returns>
        public async Task<T> DeleteAsync<T>(string resourceUri, object requestBody, string mediaType = "application/json")
        {
            return await this.SendAsync<T>(resourceUri, HttpMethod.Delete, requestBody, mediaType);
        }

        /// <summary>
        /// Sends a GET request to the specified Uri as an asynchronous operation.
        /// </summary>
        /// <param name="resourceUri">A string containing the resource relative Uri.</param>
        /// <typeparam name="T">Type of the result.</typeparam>
        /// <returns>The <see cref="Task"/> object representing the asynchronous operation.</returns>
        public async Task<T> GetAsync<T>(string resourceUri)
        {
            return await this.SendAsync<T>(resourceUri, HttpMethod.Get, null, null);
        }

        /// <summary>
        /// Sends a GET request to the specified Uri as an asynchronous operation.
        /// </summary>
        /// <param name="resourceUri">A string containing the resource relative Uri.</param>
        /// <param name="requestBody">The request body to be sent to the server.</param>
        /// <param name="mediaType">The media-type to be sent to the server.</param>
        /// <typeparam name="T">Type of the result.</typeparam>
        /// <returns>The <see cref="Task"/> object representing the asynchronous operation.</returns>
        public async Task<T> GetAsync<T>(string resourceUri, object requestBody, string mediaType = "application/json")
        {
            return await this.SendAsync<T>(resourceUri, HttpMethod.Get, requestBody, mediaType);
        }

        /// <summary>
        /// Sends a OPTIONS request to the specified Uri as an asynchronous operation.
        /// </summary>
        /// <param name="resourceUri">A string containing the resource relative Uri.</param>
        /// <typeparam name="T">Type of the result.</typeparam>
        /// <returns>The <see cref="Task"/> object representing the asynchronous operation.</returns>
        public async Task<T> OptionsAsync<T>(string resourceUri)
        {
            return await this.SendAsync<T>(resourceUri, HttpMethod.Options, null, null);
        }

        /// <summary>
        /// Sends a PATCH request to the specified Uri as an asynchronous operation.
        /// </summary>
        /// <param name="resourceUri">A string containing the resource relative Uri.</param>
        /// <param name="requestBody">The request body sent to the server.</param>
        /// <param name="mediaType">The media-type to be sent to the server.</param>
        /// <typeparam name="T">Type of the result.</typeparam>
        /// <returns>The <see cref="Task"/> object representing the asynchronous operation.</returns>
        public async Task<T> PatchAsync<T>(string resourceUri, object requestBody, string mediaType = "application/json")
        {
            return await this.SendAsync<T>(resourceUri, new HttpMethod("PATCH"), requestBody, mediaType);
        }

        /// <summary>
        /// Sends a POST request to the specified Uri as an asynchronous operation.
        /// </summary>
        /// <param name="resourceUri">A string containing the resource relative Uri.</param>
        /// <param name="requestBody">The request body to be sent to the server.</param>
        /// <param name="mediaType">The media-type to be sent to the server.</param>
        /// <typeparam name="T">Type of the result.</typeparam>
        /// <returns>The <see cref="Task"/> object representing the asynchronous operation.</returns>
        public async Task<T> PostAsync<T>(string resourceUri, object requestBody, string mediaType = "application/json")
        {
            return await this.SendAsync<T>(resourceUri, HttpMethod.Post, requestBody, mediaType);
        }

        /// <summary>
        /// Sends a PUT request to the specified Uri as an asynchronous operation.
        /// </summary>
        /// <param name="resourceUri">A string containing the resource relative Uri.</param>
        /// <param name="requestBody">The request to be body sent to the server.</param>
        /// <param name="mediaType">The media-type to be sent to the server.</param>
        /// <typeparam name="T">Type of the result.</typeparam>
        /// <returns>The <see cref="Task"/> object representing the asynchronous operation.</returns>
        public async Task<T> PutAsync<T>(string resourceUri, object requestBody, string mediaType = "application/json")
        {
            return await this.SendAsync<T>(resourceUri, HttpMethod.Put, requestBody, mediaType);
        }

        /// <summary>
        /// Gets a full resource <see cref="Uri"/>, based on the base Uri and the specified resource relative Uri.
        /// </summary>
        /// <param name="resourceUri">A string containing the resource relative Uri.</param>
        /// <returns>A full resource <see cref="Uri"/>, created based on the base Uri and the specified resource relative Uri.</returns>
        private Uri GetResourceUri(string resourceUri)
        {
            return new Uri(this.baseUri, resourceUri);
        }

        /// <summary>
        /// Sends a request to the specified Uri as an asynchronous operation.
        /// If one of the properties <see cref="RetryCount"/> or <see cref="RetryWaitTimes"/> has value, a policy <see cref="Policy"/> strategy will be used for retrying.
        /// </summary>
        /// <param name="resourceUri">A string containing the resource relative Uri.</param>
        /// <param name="method">The HTTP method to be used by the request.</param>
        /// <param name="requestBody">The request body to be sent to the server.</param>
        /// <param name="mediaType">The media-type to be sent to the server.</param>
        /// <typeparam name="T">Type of the result.</typeparam>
        /// <returns>The <see cref="Task"/> object representing the asynchronous operation.</returns>
        private async Task<T> SendAsync<T>(string resourceUri, HttpMethod method, object requestBody, string mediaType)
        {
            return await this.ExecuteAsync<T>(resourceUri, method, requestBody, mediaType);
        }

        /// <summary>
        /// Sends a request to the specified Uri as an asynchronous operation.
        /// </summary>
        /// <param name="resourceUri">A string containing the resource relative Uri.</param>
        /// <param name="method">The HTTP method to be used by the request.</param>
        /// <param name="requestBody">The request body to be sent to the server.</param>
        /// <param name="mediaType">The media-type to be sent to the server.</param>
        /// <typeparam name="T">Type of the result.</typeparam>
        /// <returns>The <see cref="Task"/> object representing the asynchronous operation.</returns>
        private async Task<T> ExecuteAsync<T>(string resourceUri, HttpMethod method, object requestBody, string mediaType)
        {
            try
            {
                var request = new HttpRequestMessage
                {
                    Method = method,
                    RequestUri = this.GetResourceUri(resourceUri)
                };

                if (requestBody != null)
                {
                    switch (mediaType)
                    {
                        case "application/json":
                            var contentBody = JsonConvert.SerializeObject(requestBody, Formatting.None, this.JsonSerializerSettings);
                            request.Content = new StringContent(contentBody, Encoding.UTF8, "application/json");

                            break;

                        case "application/x-www-form-urlencoded":
                            request.Content = new FormUrlEncodedContent(requestBody as IEnumerable<KeyValuePair<string, string>>);
                            break;

                        case "text/plain":
                            request.Content = new StringContent(requestBody as string, Encoding.UTF8, "text/plain");
                            break;

                        case "application/proda-json":
                            request.Content = new StringContent(requestBody as string, Encoding.UTF8, "application/json");
                            break;
                        case "application/proda-form":
                            request.Content = new StringContent(requestBody as string, Encoding.UTF8, "application/x-www-form-urlencoded");
                            break;


                    }
                }

                if (this.authContext != null && !this.authContext.IsAuthenticated)
                {
                    if (!string.IsNullOrWhiteSpace(this.authContext.InterServiceToken))
                        this.client.DefaultRequestHeaders.Add("X-Auth-Token", this.authContext.InterServiceToken);
                    else
                        this.client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", authContext.Credentials.AuthToken);
                }
                var response = await this.client.SendAsync(request);
                var responseBody = await response.Content.ReadAsStringAsync();
                var responseContentType = response.Content != null &&
                    response.Content.Headers != null &&
                    response.Content.Headers.ContentType != null ?
                    response.Content.Headers.ContentType.ToString() : string.Empty;

                if (!response.IsSuccessStatusCode)
                {
                    //throw new BusinessException($"url:{request.RequestUri}\nreq:{JsonConvert.SerializeObject(requestBody)}\nres:{JsonConvert.SerializeObject(responseBody)}", 411.99f);
                }

                var contentType = response.Content.Headers.ContentType != null ? response.Content.Headers.ContentType.MediaType : string.Empty;
                switch (contentType)
                {
                    case "text/html":
                    case "text/plain":
                    default:
                        if (mediaType == "application/proda-json" || mediaType == "application/proda-form")
                            return (T)Convert.ChangeType(response, typeof(T));
                        else
                            return JsonConvert.DeserializeObject<T>(responseBody);
                }

            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }
    }
}