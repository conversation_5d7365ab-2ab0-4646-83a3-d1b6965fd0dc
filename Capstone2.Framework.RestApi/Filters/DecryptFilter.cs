﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using Capstone2.Framework.Business;
using Capstone2.Framework.RestApi.Middlewares;
using Capstone2.Shared.Models.Common;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Capstone2.Framework.Business.Common;


namespace Capstone2.Framework.RestApi.Filters
{
    public class DecryptFilter : ActionFilterAttribute
    {
        public override void OnActionExecuting(ActionExecutingContext filterContext)
        {

            if (!filterContext.HttpContext.Request.QueryString.HasValue || (filterContext.HttpContext.Request.Query.Keys.Count > 0  && !filterContext.HttpContext.Request.Query.ContainsKey("df")))
            {
                if(filterContext.HttpContext.Request.Query.ContainsKey("f")){ 

                string longurl = "https://" + filterContext.HttpContext.Request.Host + filterContext.HttpContext.Request.Path + filterContext.HttpContext.Request.QueryString.ToString();
            //  string longurl1 = filterContext.HttpContext.Request.PathBase;
            //  string longurl = filterContext.HttpContext.QueryString.ToString();

                var uriBuilder = new UriBuilder(longurl);
                var query = HttpUtility.ParseQueryString(uriBuilder.Query);
                    //var filterQuery = filterContext.HttpContext.Request.Query["f"];
                    var filterQuery = "m/jWGJKT+oXUoVVFHW0TdrNF3LF1w6DcKZo1quPy30g=";
                    var decryptedValue = "";//AESCryptoHelper.DecryptFilter(filterQuery, "2r5u8x/A?D(G+KaP", null);                 
                query.Add("df", decryptedValue);
                uriBuilder.Query = query.ToString();
                longurl = uriBuilder.ToString();
                filterContext.Result = new RedirectResult(longurl);

                }

            }
         
        }
    }

}
