﻿using Capstone2.Framework.Business.Exceptions;
using Capstone2.Framework.RestApi.Context;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.ApplicationInsights;
using Microsoft.ApplicationInsights.DataContracts;
using Microsoft.ApplicationInsights.Extensibility;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;

namespace Capstone2.Framework.RestApi.Filters
{
    public class ApiExceptionFilter : ExceptionFilterAttribute
    {       
        public override void OnException(ExceptionContext context)
        {
            var _logger = (ILogger)context.HttpContext.RequestServices.GetService(typeof(ILogger<ApiExceptionFilter>));

            //TelemetryConfiguration configuration = new TelemetryConfiguration();
            //configuration.InstrumentationKey = context.HttpContext.Features.Get<RequestTelemetry>()?.Context?.InstrumentationKey;
            //var _telemetryClient = new TelemetryClient(configuration);

            string connectionString = context.HttpContext.RequestServices.GetRequiredService<IConfiguration>()["ApplicationInsights:ConnectionString"];

            TelemetryConfiguration configuration = new TelemetryConfiguration();
            configuration.ConnectionString = connectionString;

            var _telemetryClient = new TelemetryClient(configuration);

            ApiErrorResponse apiErrorResponse;
            if (context.Exception is BusinessException)
            {
                var ex = context.Exception as BusinessException;
                apiErrorResponse = CreateErrorResponse((EnumErrorCode)ex.ErrorCode, StatusCodes.Status500InternalServerError);
                context.HttpContext.Response.StatusCode = StatusCodes.Status500InternalServerError;
            }
            else if (context.Exception is UnauthorizedAccessException)
            {
                apiErrorResponse = CreateErrorResponse(EnumErrorCode.UnauthorizedAccess, StatusCodes.Status401Unauthorized);
                context.HttpContext.Response.StatusCode = StatusCodes.Status401Unauthorized;
            }
            else
            {
                var msg = context.Exception.GetBaseException().Message;
                var stack = context.Exception.StackTrace;

                apiErrorResponse = CreateErrorResponse(EnumErrorCode.InternalServerError, StatusCodes.Status500InternalServerError);
                context.HttpContext.Response.StatusCode = StatusCodes.Status500InternalServerError;

                _logger.LogError($"Message: {msg}, StackTrace: {stack}");

            }
            var aiProperties = new Dictionary<string, string> { ["Domain"] = TenantConnectionService.GetHostDomainName(context.HttpContext), ["Service"] = context.HttpContext.Request.Path, ["Exception Type"] = "API Exception" };
            _telemetryClient.TrackException(context.Exception, aiProperties);
            context.Result = new JsonResult(apiErrorResponse);
           // AITrackException(context);
            base.OnException(context);
        }

        private static ApiErrorResponse CreateErrorResponse(EnumErrorCode errorCode, int statusCode)
        {
            return new ApiErrorResponse
            {
                ErrorCode = (float)EnumErrorCode.InternalServerError,
                StatusCode = statusCode,
                Errors = new List<string> { errorCode.GetDescription() }
            };
        }

    }
}
