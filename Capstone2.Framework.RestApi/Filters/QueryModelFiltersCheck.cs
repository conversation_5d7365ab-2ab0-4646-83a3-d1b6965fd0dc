﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Capstone2.Framework.Business;
using Capstone2.Framework.RestApi.Middlewares;
using Capstone2.Shared.Models.Common;

namespace Capstone2.Framework.RestApi.Filters
{
    public class QueryModelFiltersCheck
    {
        public static void Validatefilterparameters(QueryModel queryModel,List<string> filterNames, List<string> sortNames)
        {
            //foreach (var filterParameter in queryModel.FilterParameters)
            //{
            //    if (filterParameter.IsLikeSearch && string.IsNullOrEmpty(filterParameter.FieldValue))
            //    {
            //        continue;
            //    }

            //    else if (filterParameter.FieldName.Trim().ToLower() == "string")
            //    {
            //        continue;
            //    }
            //    else if (filterNames.Contains(filterParameter.FieldName.Trim().ToLower()))
            //    {
            //        continue;
            //    }
            //    else
            //    {
                    
            //       throw new Exception("Not a Valid filter Parameter");
            //    }

            //}
            //foreach (var sortParameter in queryModel.SortParameters)
            //{
            //    if ( string.IsNullOrEmpty(sortParameter.FieldName))
            //    {
            //        continue;
            //    }
            //    else if(sortParameter.FieldName.Trim().ToLower() == "string")
            //    {
            //        continue;
            //    }
            //    else if (sortNames.Contains(sortParameter.FieldName.Trim().ToLower()))
            //    {
            //        continue;
            //    }
            //    else
            //    {
            //        throw new Exception("Not a Valid Sort Parameter");
            //    }

            //}

        }

    }
}
