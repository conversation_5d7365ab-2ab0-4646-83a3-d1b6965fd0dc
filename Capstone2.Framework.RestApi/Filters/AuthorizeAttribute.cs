﻿using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Capstone2.Framework.RestApi.Filters
{

    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method)]
    public class AuthorizeAttribute : Attribute, IAuthorizationFilter
    {
        public void OnAuthorization(AuthorizationFilterContext context)
        {
            if (context.HttpContext.Request.Path != "/Auth/Anonymous_Token" && context.HttpContext.Request.Path != "/Auth/ValidateToken" && context.HttpContext.Request.Path != "/Auth/ValidateInterServiceToken")
            {
                int orgId = Convert.ToInt32(context.HttpContext.Items["oid"]);
                if (HasAllowAnonymous(context)) return;
                if (orgId == default(int))
                {
                    var response = new ApiResponse<string>
                    {
                        StatusCode = StatusCodes.Status401Unauthorized,
                        Errors = new List<string> { "Failed In Authorize Filters :Invalid token has been passed." }
                    };

                    // not logged in
                    context.Result = new JsonResult(response);
                }
            }
            else
            {
                // do nothing continue to the controller
                if (HasAllowAnonymous(context)) return;
            }
        }

        private bool HasAllowAnonymous(AuthorizationFilterContext context)
        {
            bool hasAllowAnonymous = context.ActionDescriptor.EndpointMetadata.Any(em => em.GetType() == typeof(AllowAnonymousAttribute));
            if (hasAllowAnonymous)
                return true;
            return false;
        }
    }
}
