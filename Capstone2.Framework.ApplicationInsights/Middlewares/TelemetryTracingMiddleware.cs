﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.ApplicationInsights.Services;
using Microsoft.ApplicationInsights;
using Microsoft.ApplicationInsights.Channel;
using Microsoft.ApplicationInsights.DataContracts;
using Microsoft.ApplicationInsights.Extensibility;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Capstone2.Framework.ApplicationInsights.Middlewares
{
    public class TelemetryTracingMiddleware
    {
        private RequestDelegate _next;
        public TelemetryTracingMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task Invoke(HttpContext context)
        {
            var test = new RequestBodyLoggingService();
            var test1 = new ResponseBodyLoggingService();
            try
            {
                context.Request.EnableBuffering();
                await _next(context);
                var endpoint = context.Features.Get<IEndpointFeature>()?.Endpoint;
                var attribute = endpoint?.Metadata.GetMetadata<TelemetryTracingAttribute>();
                if (attribute != null && attribute.LogRequest)                  
                     await test.RequestTelemetry(context);
                if (attribute != null && attribute.LogResponse)
                    await test1.ResponseTelemetry(context);
            }

            catch (Exception ex)
            {
                TelemetryConfiguration configuration = new TelemetryConfiguration();
                string connectionString = context.RequestServices.GetRequiredService<IConfiguration>()["ApplicationInsights:ConnectionString"];
                //configuration.InstrumentationKey = context.Features.Get<RequestTelemetry>()?.Context?.InstrumentationKey;
                configuration.ConnectionString = connectionString;
                var _telemetryClient = new TelemetryClient(configuration);
                var aiProperties = new Dictionary<string, string> { ["Domain"] = ConstantsService.GetHostDomainName(context), ["Service"] = context.Request.Path, ["Exception Type"] = "API Exception" };
                _telemetryClient.TrackException(ex, aiProperties);
            }
        }
    }

    public class HealthProbeTelemetryProcessor : ITelemetryProcessor
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ITelemetryProcessor _nextProcessor;
        public static string HealthProbeHeaderName => "Auth/HealthCheck";
        public static string SwaggerUrl => "/swagger/index.html";



        public HealthProbeTelemetryProcessor(IHttpContextAccessor httpContextAccessor, ITelemetryProcessor nextProcessor)
        {
            _httpContextAccessor = httpContextAccessor;
            _nextProcessor = nextProcessor;
        }

        public void Process(ITelemetry item)
        {
            if (item == null) throw new ArgumentNullException(nameof(item));

            if (!string.IsNullOrWhiteSpace(item.Context.Operation.SyntheticSource))
                return;

            var isNotRequestTelemetry = !(item is RequestTelemetry);

            if ((isNotRequestTelemetry || _httpContextAccessor.HttpContext == null || !(_httpContextAccessor.HttpContext.Request?.Path.Value.Contains(SwaggerUrl)).GetValueOrDefault() || !(_httpContextAccessor.HttpContext.Request?.Path.Value.Contains(HealthProbeHeaderName)).GetValueOrDefault() || !(_httpContextAccessor.HttpContext.Request?.Headers.ContainsKey(HealthProbeHeaderName)).GetValueOrDefault()))
                _nextProcessor.Process(item);
        }
    }
}
