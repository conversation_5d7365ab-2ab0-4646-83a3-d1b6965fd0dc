apiVersion: apps/v1
kind: Deployment
metadata:
  name: applicationinsights-restservices-deployment
  labels:
    app: applicationinsights-restservices-dep
spec:
  replicas: $(Replicas)
  selector:
    matchLabels:
      app: applicationinsights-restservices-pod
  template:
    metadata:
      labels:
        app: applicationinsights-restservices-pod
    spec:
      containers:
      - name: applicationinsights-restservices-container
        image: $(Acr_Server)/capstonerestservicesapplicationinsights:$(Build.BuildNumber)
        imagePullPolicy: Always
        resources:
          requests:
            cpu: $(Request_CPU)
            memory: $(Request_Memory)
          limits:
            cpu: $(Limit_CPU)
            memory: $(Limit_Memory)
      imagePullSecrets:
      - name: dockerimgpullsecrets
---
apiVersion: v1
kind: Service
metadata:
  annotations:
    service.beta.kubernetes.io/azure-load-balancer-internal: "true"
  name: applicationinsights-restservices
  labels:
    run: applicationinsights-restservices-svc
spec:
  type: LoadBalancer
  ports:
  - port: 8090
    targetPort: 80
    protocol: TCP
    name: http
  selector:
    app: applicationinsights-restservices-pod
