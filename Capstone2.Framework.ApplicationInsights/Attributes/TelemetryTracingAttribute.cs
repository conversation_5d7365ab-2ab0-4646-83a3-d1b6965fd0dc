﻿using System;

namespace Capstone2.Framework.ApplicationInsights.Attributes
{
    public class TelemetryTracingAttribute : Attribute
    {
        public bool LogRequest { get; set; }
        public bool LogResponse { get; set; }
        public TelemetryTracingAttribute(bool logRequest, bool logResponse)
        {
            LogRequest = logRequest;
            LogResponse = logResponse;
        }
    }
}
