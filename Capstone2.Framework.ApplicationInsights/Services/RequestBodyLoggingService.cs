﻿using Microsoft.ApplicationInsights.DataContracts;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Features;
using System.IO;
using System.Security.Claims;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace Capstone2.Framework.ApplicationInsights.Services
{
    public class RequestBodyLoggingService
    {
        public  async Task RequestTelemetry(HttpContext context)
        {
            var originalBody = context.Request?.Body;
            if (originalBody?.CanRead == true)
            {
                using var reader = new StreamReader(context.Request.Body, Encoding.UTF8, detectEncodingFromByteOrderMarks: false, bufferSize: 512, leaveOpen: true);
                var requestBody = await reader.ReadToEndAsync();
                context.Request.Body.Position = 0;
                var requestTelemetry = context.Features.Get<RequestTelemetry>();
                var endpoint = context.Features.Get<IEndpointFeature>()?.Endpoint;
                requestTelemetry?.Properties.Add("DisplayName", endpoint.DisplayName);
                requestTelemetry?.Properties.Add("Domain", ConstantsService.GetHostDomainName(context));
                requestTelemetry?.Properties.Add("Service", context.Request.Path.ToUriComponent().Split('/')[2]);
                requestTelemetry?.Properties.Add("Controller", context.Request.Path.ToUriComponent().Split('/')[1]);
                requestTelemetry?.Properties.Add("RequestBody", requestBody);
                if (context.User.Identity is { IsAuthenticated: true })
                {
                    var email = context.User.FindFirst(x => x.Type == ClaimTypes.Email)?.Value;
                    var userId = context.User.FindFirst(x => x.Type == "UserId")?.Value ?? string.Empty;
                    var userName = context.User.Identity?.Name;
                    requestTelemetry?.Properties.Add("RequestedByUser", JsonSerializer.Serialize(new { userId, email, userName }));
                }
            }
        }
    }
}
