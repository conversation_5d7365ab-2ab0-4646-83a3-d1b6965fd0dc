﻿using Microsoft.ApplicationInsights;
using Microsoft.ApplicationInsights.DataContracts;
using Microsoft.ApplicationInsights.Extensibility;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;

namespace Capstone2.Framework.ApplicationInsights.Services
{
    public class AITelemetryTracingService : IAITelemetryTracingService
    {
        private TelemetryClient _telemetryClient;
        public AITelemetryTracingService(HttpContext context)
        {
            string connectionString = context.RequestServices.GetRequiredService<IConfiguration>()["ApplicationInsights:ConnectionString"];
            TelemetryConfiguration configuration = new TelemetryConfiguration();
            //configuration.InstrumentationKey = context.Features.Get<RequestTelemetry>()?.Context?.InstrumentationKey;
            configuration.ConnectionString = connectionString;
            _telemetryClient = new TelemetryClient(configuration);
        }
        public void TraceInfo(string info)
        {
            var telemetry = new TraceTelemetry(info);
            _telemetryClient.TrackTrace(telemetry);
        }
        public void TraceDependency(string dependencyName, string data, DateTime startTime, TimeSpan duration, bool success)
        {
            var telemetry = new DependencyTelemetry(dependencyName, data, startTime, duration, success);
            _telemetryClient.TrackDependency(telemetry);
        }
        public void LogError(Exception exception)
        {
            var telemetry = new ExceptionTelemetry(exception);
            telemetry.Properties.Add("StackTrace", exception.StackTrace);
            telemetry.Properties.Add("Exception Message", exception.Message);
            _telemetryClient.TrackException(telemetry);
        }
        public void LogErrorWithMessage(Exception exception, string message)
        {
            var telemetry = new ExceptionTelemetry(exception);
            telemetry.Properties.Add("Message", message);
            telemetry.Properties.Add("StackTrace", exception.StackTrace);
            telemetry.Properties.Add("Exception Message", exception.Message);
            _telemetryClient.TrackException(telemetry);
        }

    }
}
