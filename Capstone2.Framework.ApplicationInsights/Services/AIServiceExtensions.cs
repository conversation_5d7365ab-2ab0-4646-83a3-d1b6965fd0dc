﻿using Microsoft.Extensions.DependencyInjection;

namespace Capstone2.Framework.ApplicationInsights.Services
{
    public static class AIServiceExtensions
    {
        public static IServiceCollection RegisterAuthUtilityDI(this IServiceCollection services)
        {
            services.AddTransient<IAITelemetryTracingService, AITelemetryTracingService>();
            return services;
        }
    }
}
