﻿using System;

namespace Capstone2.Framework.ApplicationInsights.Services
{
    public interface IAITelemetryTracingService
    {
        void TraceInfo(string info);
        void TraceDependency(string dependencyName, string data, DateTime startTime, TimeSpan duration, bool success);
        void LogError(Exception exception);
        void LogErrorWithMessage(Exception exception, string message);
    }
}
