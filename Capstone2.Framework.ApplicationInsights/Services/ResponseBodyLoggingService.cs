﻿using Microsoft.ApplicationInsights.DataContracts;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Features;
using System.IO;
using System.Text;
using System.Threading.Tasks;

namespace Capstone2.Framework.ApplicationInsights.Services
{
    public class ResponseBodyLoggingService
    {
        public  async Task ResponseTelemetry(HttpContext context)
        {
            var originalBody = context.Response?.Body;
            if (originalBody?.CanRead == true)
            {
                using var reader = new StreamReader(context.Response.Body, Encoding.UTF8, detectEncodingFromByteOrderMarks: false, bufferSize: 512, leaveOpen: true);
                var responseBody = await reader.ReadToEndAsync();
                context.Response.Body.Position = 0;
                var requestTelemetry = context.Features.Get<RequestTelemetry>();
                var endpoint = context.Features.Get<IEndpointFeature>()?.Endpoint;
                requestTelemetry?.Properties.Add("DisplayName", endpoint.DisplayName);
                requestTelemetry?.Properties.Add("Domain", ConstantsService.GetHostDomainName(context));
                requestTelemetry?.Properties.Add("Service", context.Request.Path.ToUriComponent().Split('/')[2]);
                requestTelemetry?.Properties.Add("Controller", context.Request.Path.ToUriComponent().Split('/')[1]);
                requestTelemetry?.Properties.Add("ResponseBody", responseBody);
            }
        }
    }
}
