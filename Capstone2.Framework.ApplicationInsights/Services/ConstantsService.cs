﻿using Microsoft.AspNetCore.Http;

namespace Capstone2.Framework.ApplicationInsights.Services
{
    public static class ConstantsService
    {
        public static string GetHostDomainName(HttpContext httpContext)
        {
            var host = httpContext.Request.Host.Host;
            if (!string.IsNullOrWhiteSpace(host))
            {
                return host.Split('.')[0];
            }
            return "";
        }

    }
}
