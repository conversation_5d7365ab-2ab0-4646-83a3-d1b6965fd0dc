﻿{
  "ConnectionStrings": {
    "Capstone2ReadOnlyMasterDBConn": "Data Source=localhost;initial catalog=Capstone2Master;persist security info=True;integrated security=true;MultipleActiveResultSets=True;App=EntityFramework;Encrypt=True;TrustServerCertificate=True;Connection Timeout=30;Column Encryption Setting=enabled;",
    "AzureRedisConnection": "Capstone2-Dev-localhost.redis.cache.windows.net:6380,password=4HU8Bkx8KPnrOJOwZKfpJt5WrM9myaRk0AzCaH0JOlQ=,ssl=True,abortConnect=False",
    "RedisCacheInstanceName": "MasterInstance"
  },
  "ApplicationInsights": {
    "InstrumentationKey": "8edb2980-ca11-4ea3-8940-3acbf45e0340",
    "ConnectionString": "InstrumentationKey=8edb2980-ca11-4ea3-8940-3acbf45e0340;IngestionEndpoint=https://australiaeast-1.in.applicationinsights.azure.com/",
    "EnablePerformanceCounterCollectionModule": false
  },
  "AzureAD": {
    "KVClientId": "1463cbf5-ea84-4340-9708-e0410cd8c678",
    "KVClientSecret": "****************************************",
    "KVTenantId": "ef697159-b27e-4a23-bf9e-4ffb85045a6e",

    "KeyVaultName": "capstone2-dev2-kv-01",
    "KeyVaultUrl": "https://{0}.vault.azure.net",
    "JWTCertificateName": "JWTCertificatePrivateKeyDebug"
  },
  "AppSettings": {
  "JwtKey": "AewerwBsefCefweD",
    "Issuer": "CapstoneSystemDev",
    "Audience": "CapstoneFrontEndDev",
    "JwtInterServiceKey": "AewerwBsefCefweD",
    "InterserviceTokenExpiresIn": 31536000,
  "ApiUrls": {
	"FileServiceUrl": "https://localhost:44353",
	"AuthServiceUrl": "https://localhost:44391",
	"PatientServiceUrl": "https://localhost:44365"
	}
 }
}