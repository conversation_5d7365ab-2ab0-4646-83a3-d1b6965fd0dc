﻿{
  "ConnectionStrings": {
    "Capstone2ReadOnlyMasterDBConn": "Data Source=localhost;initial catalog=Capstone2Master;persist security info=True;integrated security=true;MultipleActiveResultSets=True;App=EntityFramework;Encrypt=True;TrustServerCertificate=True;Connection Timeout=30;Column Encryption Setting=enabled;",
    "AzureRedisConnection": "Capstone2-Dev-localhost.redis.cache.windows.net:6380,password=4HU8Bkx8KPnrOJOwZKfpJt5WrM9myaRk0AzCaH0JOlQ=,ssl=True,abortConnect=False",
     "RedisCacheInstanceName": "MasterInstance"
  },
  "ApplicationInsights": {
    "InstrumentationKey": "8edb2980-ca11-4ea3-8940-3acbf45e0340",
    "ConnectionString": "InstrumentationKey=8edb2980-ca11-4ea3-8940-3acbf45e0340;IngestionEndpoint=https://australiaeast-1.in.applicationinsights.azure.com/",
    "EnablePerformanceCounterCollectionModule": false
  },
  "AzureAD": {
    "KVClientId": "1463cbf5-ea84-4340-9708-e0410cd8c678",
    "KVClientSecret": "****************************************",
    "KVTenantId": "ef697159-b27e-4a23-bf9e-4ffb85045a6e",

    "KeyVaultName": "capstone2-dev2-kv-01",
    "KeyVaultUrl": "https://{0}.vault.azure.net",
    "JWTCertificateName": "JWTCertificatePrivateKeyDebug"
  },
  "AppSettings": {
    "AesEcryptionKey": "UjWnZr4u7x!A%D*G-KaPdSgVkYp2s5v8",
    "AesIV": "C*F)J@NcRfUjXn2r",
    "Issuer": "CapstoneSystemDev",
    "Audience": "CapstoneFrontEndDev",
    "JwtKey": "AewerwBsefCefweD",
    "JwtInterServiceKey": "AewerwBsefCefweD",
    //"JwtInterServiceKey": "ShVmYq3t6w9z$C&F",
    "ApiUrls": {
      "UserServiceUrl": "",
      "CommunicationServiceUrl": "https://localhost:44351",
      "AuthApiUrl": "https://localhost:44391",
      "ResetPasswordUrl": "#/login/reset?t=",
      "DomainUrl": "capstonesystemdev.com.au"
    }
  },
  "BasePathPrefix": ""
}