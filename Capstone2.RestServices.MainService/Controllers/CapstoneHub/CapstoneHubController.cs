﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.CapstoneHub.Interfaces;
using Capstone2.RestServices.CapstoneHub.Models;
using Capstone2.RestServices.MainService.Utility;
using Capstone2.Shared.Models.Entities.Hub;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Threading.Tasks;

namespace Capstone2.RestServices.CapstoneHub.Controllers
{
    [ApiController]
    [Route("capstonehub")]
    //[Authorize]
    [ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class CapstoneHubController : BaseController
    {
        private readonly ILogger<CapstoneHubController> _logger;
        private readonly ICapstoneHubBAL _capstoneHubBAL;

        public CapstoneHubController(IOptions<MvcOptions> options, ILogger<CapstoneHubController> logger, ICapstoneHubBAL capstoneHubBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _capstoneHubBAL = capstoneHubBAL;
            options.Value.InputFormatters.Insert(0, InputFormater.GetInputFormater());
        }
        /// <summary>
       
        [HttpGet("healthcheck")]
        [AllowAnonymous]
        [TelemetryTracing(logRequest: false, logResponse: false)]
        public IActionResult HealthCheck()
        {
            return Ok(StatusCodes.Status200OK);
        }
        [HttpPost("sendmessage")]
        public async Task<IActionResult> SendMessage()
        {
            var apiResponse = new ApiResponse<string>();
            try
            {
                apiResponse = await _capstoneHubBAL.SendMessage(baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Gst fee cannot be computed during this time.");
                _logger.LogError($"Gst fee cannot be computed during this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpPost("providers/search")]
        public async Task<IActionResult> SearchProvider([FromBody] ProviderSearch providerSearch)
        {
            var apiResponse = new ApiResponse<ProviderSearchResponse>();
            try
            {
                apiResponse = await _capstoneHubBAL.SearchProvider(baseHttpRequestContext, providerSearch);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Provider search cannot be completed during this time.");
                _logger.LogError($"Provider search cannot be completed during this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
        [HttpPost("sendreferral")]
        public async Task<IActionResult> SendReferral([FromBody] InputMessageRequestBody body)
        {
            var apiResponse = new ApiResponse<SendReferralResponse>();
            try
            {
                apiResponse = await _capstoneHubBAL.SendReferral(baseHttpRequestContext, body);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("SendReferral cannot be completed during this time.");
                _logger.LogError($"SendReferral cannot be completed during this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="hubSettingsStoreRequestBody"></param>
        /// <returns></returns>
       [ProducesResponseType(typeof(ApiResponse<HubSettingsStoreResponse>), StatusCodes.Status200OK)]
        [HttpPost("hub_settings")]
        public async Task<IActionResult> PostHubSettings([FromBody] HubSettingsStoreRequestHeader hubSettingsStoreRequestHeader)
        {
            var apiResponse = new ApiResponse<HubSettingsStoreResponse>();
            try
            {
                apiResponse = await _capstoneHubBAL.PostHubSettings(baseHttpRequestContext, hubSettingsStoreRequestHeader);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("PostHubSettings cannot be completed during this time.");
                _logger.LogError($"PostHubSettings cannot be completed during this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
      /// <summary>
      /// 
      /// </summary>
      /// <param name="messageId"></param>
      /// <returns></returns>
        [ProducesResponseType(typeof(ApiResponse<FetchMessageResponse>), StatusCodes.Status200OK)]
        [HttpGet("fetchMessage")]
        public async Task<IActionResult> FetchMessage([FromQuery]string messageId)
        {
            var apiResponse = new ApiResponse<FetchMessageResponse>();
            try
            {
                apiResponse = await _capstoneHubBAL.FetchMessage(baseHttpRequestContext, messageId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Message cannot be fetched during this time.");
                _logger.LogError($"Message cannot be fetched during this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
    }
}
