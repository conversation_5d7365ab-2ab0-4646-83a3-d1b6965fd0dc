﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.MainService.Utility;
using Capstone2.RestServices.PatientRegistration.Interfaces;
using Capstone2.RestServices.PatientRegistration.Models;
using Capstone2.RestServices.PatientRegistration.Services;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Capstone2.RestServices.PatientRegistration.Controllers
{
    [Route("patientregistration")]
    [ApiController]
    [ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class PatientRegController : PublicBaseController
    {
        private readonly ILogger<PatientRegController> _logger;
        private readonly IPatientRegBAL _PatientRegBAL;

        public PatientRegController(IOptions<MvcOptions> options, ILogger<PatientRegController> logger, IHttpContextAccessor httpContextAccessor ,IPatientRegBAL patientRegBAL) : base(httpContextAccessor)
        {
            _logger = logger;
            _PatientRegBAL = patientRegBAL;
            options.Value.InputFormatters.Insert(0, InputFormater.GetInputFormater());

        }

        /// <summary>
        /// Search for form fields
        /// </summary>
        /// <returns>Returns a list of Forms fileds</returns>
        [HttpGet]
        [Route("token_validation")]
        [ProducesResponseType(typeof(string), StatusCodes.Status200OK)]
        public async Task<IActionResult> tokenValidation()
        {
            var apiResponse = new ApiResponse<string>();
            try
            {
                // apiResponse = new ApiResponse<string> { StatusCode = StatusCodes.Status200OK,
                //                                           Result = "PatientRegistration" };

                apiResponse = await _PatientRegBAL.tokenValidation(publicbaseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Testing error");
                _logger.LogError($"Testing error {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


       

        [HttpGet("healthcheck")]
        [Microsoft.AspNetCore.Authorization.AllowAnonymous]
        [TelemetryTracing(logRequest: false, logResponse: false)]
        public IActionResult HealthCheck()
        {
            return Ok(StatusCodes.Status200OK);
        }


        /// <summary>
        /// Generate OTP 
        /// </summary>
        /// <returns>Sends OTP</returns>
        [HttpPost]
        [Route("patient/otp")]
        [ProducesResponseType(typeof(string), StatusCodes.Status200OK)]
        public async Task<IActionResult> GenerateOtp([FromBody] OtpObject otpObject)
        {
            var apiResponse = new ApiResponse<string>();

            try
            {
                apiResponse = await _PatientRegBAL.GenerateOtpBAL(otpObject, publicbaseHttpRequestContext);

                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Testing error");
                _logger.LogError($"Testing error {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


        /// <summary>
        /// Validate OTP 
        /// </summary>
        /// <returns>Sends Back Token with tt as ACC</returns>
        [HttpPost]
        [Route("patient/otp/verify")]
        [ProducesResponseType(typeof(string), StatusCodes.Status200OK)]
        public async Task<IActionResult> VerifyOtp([FromBody] VerifyObject otpObject)
        {
            var apiResponse = new ApiResponse<LoginResponse>();

            try
            {
                apiResponse = await _PatientRegBAL.ValidateOtpBAL(otpObject, publicbaseHttpRequestContext);

                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Testing error");
                _logger.LogError($"Testing error {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpPost]
        [Route("patient_draft_data/{prid}")]
        [ProducesResponseType(typeof(ApiResponse<long?>), StatusCodes.Status200OK)]
        public async Task<IActionResult> AddPatientDraftData(long prid ,[FromBody] PatientDraftData inputDraftData)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _PatientRegBAL.AddPatientDraftData(prid,publicbaseHttpRequestContext, inputDraftData);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while creating Patient Draft Data.Please check all variables");
                _logger.LogError($"Error while creating Patient Draft Data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to fetch a Patient Draft Data based on id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("patient_draft_data/{prid}")]
        [ProducesResponseType(typeof(ApiResponse<PatientDraftData>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetPatientDraftData(long prid)
        {
            var apiResponse = new ApiResponse<PatientDraftData>();
            try
            {
                apiResponse = await _PatientRegBAL.GetPatientDraftData(publicbaseHttpRequestContext, prid);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting Patient draft Data");
                _logger.LogError($"Error while getting Patient draft Data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to update a Patient Draft Data
        /// </summary>
        /// <param name="inputDraftData"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("patient_draft_data/{prid}")]
        [ProducesResponseType(typeof(ApiResponse<long?>), StatusCodes.Status200OK)]
        public async Task<IActionResult> EditPatientDraftData(long prid, [FromBody] PatientDraftData inputDraftData)
        {
            var apiResponse = new ApiResponse<long?>();

            try
            {
                apiResponse = await _PatientRegBAL.EditPatientDraftData(publicbaseHttpRequestContext, prid, inputDraftData);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Patient Draft Data cannot be Edited.Please try again later.");
                _logger.LogError($"Patient Draft Data  cannot be Edited {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to fetch a Patient Draft Data based on id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("master_data")]
        [ProducesResponseType(typeof(ApiResponse<MasterDataWrapper>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetMasterDataWarapper()
        {
            var apiResponse = new ApiResponse<MasterDataWrapper>();
            try
            {
                apiResponse = await _PatientRegBAL.GetMasterDataWarapper(publicbaseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting Master Data");
                _logger.LogError($"Error while getting Master Data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        ///Get Address Finder List  
        /// </summary>
        [HttpGet]
        [Route("address_finder")]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetAddress([FromQuery] string st)
        {
            var apiResponse = new ApiResponse<string>();
            try
            {
                apiResponse = await _PatientRegBAL.GetAddressBAL(publicbaseHttpRequestContext,st);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Could not get the addresses data");
                _logger.LogError($"Could not get the Address data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Get Address Finder By Id 
        /// </summary>
        [HttpGet]
        [Route("address_finder/{id}")]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetAddressById(string id)
        {
            var apiResponse = new ApiResponse<string>();
            try
            {
                apiResponse = await _PatientRegBAL.GetAddressByIdBAL(publicbaseHttpRequestContext, id);

                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Could not get the addresses data");
                _logger.LogError($"Could not get the Address data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

       

    }
}
