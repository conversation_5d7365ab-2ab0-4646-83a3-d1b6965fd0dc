﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.Communication.Services;
using Capstone2.RestServices.MainService.Utility;
using Capstone2.RestServices.PatientRegistration.Interfaces;
using Capstone2.RestServices.PatientRegistration.Models;
using Capstone2.RestServices.PatientRegistration.Services;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Capstone2.RestServices.PatientRegistration.Controllers
{
    [Route("patientregistration")]
    [ApiController]
    [ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class PatientWrapperController : PublicBaseController
    {
        private readonly ILogger<PatientWrapperController> _logger;
        private readonly IPatientWrapperBAL _patientWrapperBAL;

        public PatientWrapperController(IOptions<MvcOptions> options, ILogger<PatientWrapperController> logger, IHttpContextAccessor httpContextAccessor ,IPatientWrapperBAL patientWrapperBAL) : base(httpContextAccessor)
        {
            _logger = logger;
            _patientWrapperBAL = patientWrapperBAL;
            options.Value.InputFormatters.Insert(0, InputFormater.GetInputFormater());

        }

        /// <summary>
        /// Method to fetch a Patient Draft Data based on id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("patient_data/{prid}")]
        [ProducesResponseType(typeof(ApiResponse<PatientDataWrapper>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetMasterDataWarapper(long prid)
        {
            var apiResponse = new ApiResponse<PatientDataWrapper>();
            try
            {
                apiResponse = await _patientWrapperBAL.GetPatientDataWarapper(prid,publicbaseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting Patient Data");
                _logger.LogError($"Error while getting Patient Data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


        [HttpPost]
        [Route("patient_data/{prid}")]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
        public async Task<IActionResult> AddPatientData(long prid, [FromBody] InputPatientWrapper inputData)
        {
            var apiResponse = new ApiResponse<string>();
            try
            {
                apiResponse = await _patientWrapperBAL.AddPatientData(prid, publicbaseHttpRequestContext, inputData);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while creating Patient Data.Please check all variables");
                _logger.LogError($"Error while creating Patient Data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
        
        
        [HttpDelete]
        [Route("patient_token_assocs/{prid}")]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
        public async Task<IActionResult> DeletePatientToken(long prid)
        {  var apiResponse = new ApiResponse<string>();
            try
            {
               apiResponse = await _patientWrapperBAL.DeletePatientToken(prid, publicbaseHttpRequestContext);
                 return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while deleting Patient Token");
                _logger.LogError($"Error while deleting Patient Token {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpGet]
        [Route("file_data/{fileid}")]
        [ProducesResponseType(typeof(ApiResponse<FileDetailsOutputForId>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetFileDataWarapper(long fileid)
        {
            var apiResponse = new ApiResponse<FileDetailsOutputForId>();
            try
            {
                apiResponse = await _patientWrapperBAL.GetFileDataWarapper(fileid, publicbaseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting File Data");
                _logger.LogError($"Error while getting File Data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpPost]
        [Route("file_data")]
        [ProducesResponseType(typeof(ApiResponse<long>), StatusCodes.Status200OK)]
        public async Task<IActionResult> PostFileDataWarapper([FromBody] FileUploadObject fileUpload)
        {
            var apiResponse = new ApiResponse<long>();
            try
            {
                apiResponse = await _patientWrapperBAL.PostFileDataWarapper( publicbaseHttpRequestContext,fileUpload);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = 0;
                apiResponse.Errors.Add("Error while uploading the File Data");
                _logger.LogError($"Error while uploading File Data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpDelete]
        [Route("file_data/{fileid}")]
        [ProducesResponseType(typeof(ApiResponse<FileDetailsOutputForId>), StatusCodes.Status200OK)]
        public async Task<IActionResult> DeleteFileDataWarapper(long fileid)
        {
            var apiResponse = new ApiResponse<FileDetailsOutputForId>();
            try
            {
                apiResponse = await _patientWrapperBAL.DeleteFileDataWarapper(fileid, publicbaseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while Deleting File Data");
                _logger.LogError($"Error while Deleting File Data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


        [HttpPost]
        [Route("file_data/transfer")]
        [ProducesResponseType(typeof(ApiResponse<long>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetFileDetails([FromQuery] string stringFileDetailsId)
        {
            var apiResponse = new ApiResponse<long>();
            try
            {
                apiResponse = await _patientWrapperBAL.TransferFileDetails(publicbaseHttpRequestContext, stringFileDetailsId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    StatusCodes.Status403Forbidden => new ObjectResult(apiResponse) { StatusCode = StatusCodes.Status403Forbidden },
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = 0;
                apiResponse.Errors.Add("Error while transfering list of files");
                _logger.LogError($"Error while transfering list of files {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }






    }
}
