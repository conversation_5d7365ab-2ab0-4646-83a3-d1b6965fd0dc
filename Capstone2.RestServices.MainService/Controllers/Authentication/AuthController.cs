﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.Business.Common;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.Authentication.Interfaces;
using Capstone2.RestServices.Authentication.Models;
using Capstone2.Shared.Models.Dtos;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AuthorizeAttribute = Capstone2.Framework.RestApi.Filters.AuthorizeAttribute;

namespace Capstone2.RestServices.Authentication
{
    [Route("auth")]
    [ApiExceptionFilter]
    [Authorize]
   //[TelemetryTracing(logRequest: true, logResponse: true)]
    public class AuthController : BaseController
    {
        private readonly ILogger<AuthController> _logger;
        private readonly IOffsiteAccessBAL _offsiteAccessBAL;
        private readonly IPasswordAccessBAL _passwordAccessBAL;
        private readonly IAuthTokenBAL _authTokenBAL;
        private readonly IResetPasswordBAL _resetPasswordBAL;
        private readonly IAuthUserBAL _iAuthUserBAL;

        public AuthController(ILogger<AuthController> logger, IOffsiteAccessBAL offsiteAccessBAL, IPasswordAccessBAL passwordAccessBAL, IAuthTokenBAL authTokenBAL, IResetPasswordBAL resetPasswordBAL, IAuthUserBAL iAuthUserBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {

            _logger = logger;
            _offsiteAccessBAL = offsiteAccessBAL;
            _passwordAccessBAL = passwordAccessBAL;
            _authTokenBAL = authTokenBAL;
            _resetPasswordBAL = resetPasswordBAL;
            _iAuthUserBAL = iAuthUserBAL;
        }

        /// <summary>
        /// API Verifies the Username and Password.And also Validate Pin
        /// </summary>
        /// <returns>JWT Token on success in LoginResponse </returns>
        /// <response code="200">Returns LoginResponse </response>
        /// <remarks>  
        /// </remarks>
        /// 
        [HttpGet]
        [Route("Anonymous_Token")]
        [AllowAnonymous]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
        public IActionResult GetAnonymousToken(string orgCode)
        {
            var apiResponse = new ApiResponse<string>();
            try
            {
                apiResponse = _authTokenBAL.GenerateAnonymousToken(baseHttpRequestContext.OrgId, orgCode);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Anonymous Login Failed");
                _logger.LogError($"Anonymous Login Failed {Ex.Message} {Ex.StackTrace}");
                return Unauthorized(apiResponse);
            }
        }

        [HttpPost]
        [Route("login")]
        [ProducesResponseType(typeof(ApiResponse<LoginResponse>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Login([FromBody] List<LoginRequest> listLoginRequest)
        {
            var apiResponse = new ApiResponse<LoginResponse>();
            try
            {

                var ClientIpAddress = ClientIp.GetClientIpAddress(baseHttpRequestContext.RemoteIpAddress);
                if (HttpContext.Request.Headers.ContainsKey("X-Forwarded-For"))
                {
                    string headercontent  = HttpContext.Request.Headers["X-Forwarded-For"].FirstOrDefault()?.ToString();
                    if(headercontent!=null && headercontent.IndexOf(':') != -1)
                    {

                         ClientIpAddress = headercontent.Split(':').FirstOrDefault();
                    }

                }

                _logger.LogInformation($"Client IP Address: {ClientIpAddress}");

                apiResponse = await _passwordAccessBAL.LoginBAL(ClientIpAddress, listLoginRequest, baseHttpRequestContext.OrgId, baseHttpRequestContext.OrgCode);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Login Failed");
                _logger.LogError($"Login Failed {Ex.Message} {Ex.StackTrace}");
                return Unauthorized(apiResponse);
            }
        }

        [HttpPost]
        [Route("clientid/login")]
        [ProducesResponseType(typeof(ApiResponse<LoginResponse>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Login([FromBody] LoginRequest loginRequest)
        {
            var apiResponse = new ApiResponse<LoginResponse>();
           
            try
            {                
                apiResponse = await _passwordAccessBAL.ClientIdLogin(loginRequest, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("ClientId Login Failed ");
                _logger.LogError($"ClientId Login Failed {Ex.Message} {Ex.StackTrace}");
                return Unauthorized(apiResponse);
            }
        }

        [HttpGet]
        [Route("getClientIdAndSecretBySystemEmail")]
        [ProducesResponseType(typeof(ApiResponse<LoginResponse>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetClientIdAndSecret(string systemAdminEmail)
        {
            var apiResponse = new ApiResponse<ClientSecreatView>();
           
            try
            {
                apiResponse = await _passwordAccessBAL.ClientIdAndSecretLogin(systemAdminEmail, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("ClientId Login Failed ");
                _logger.LogError($"getClientIdAndSecretBySystemEmail Failed {Ex.Message} {Ex.StackTrace}");
                return Unauthorized(apiResponse);
            }
        }

        /// <summary>
        /// API Generates 2 Factor pin and sends to Email or SMS.
        /// </summary>
        /// <returns>Status of the Email or SMS sent </returns>
        /// <response code="200">Returns String </response>
        /// <remarks>  
        /// </remarks>
        [HttpPost]
        [Route("login/factor")]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetTwoFactorLoginPin([FromBody] OffSiteLoginRequest loginRequest)
        {
            var apiResponse = new ApiResponse<string>();
            try
            {
                apiResponse = await _offsiteAccessBAL.GetTwoFactorLoginPin(loginRequest, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while creating One Time Password");
                _logger.LogError($"Error while creating One Time Password {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// API will close the session and logout the User 
        /// </summary>
        /// <returns>status of logout </returns>
        /// <response code="200">Returns String </response>
        /// <remarks>  
        /// </remarks>
        [HttpDelete]
        [Route("logout")]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
        public async Task<IActionResult> LogOut()
        {
            var apiResponse = new ApiResponse<string>();
            try
            {
                apiResponse = await _authTokenBAL.DeleteToken(baseHttpRequestContext, (short)TokenType.Login);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Logout Failed");
                _logger.LogError($"Logout Failed {Ex.Message} {Ex.StackTrace}");
                return Unauthorized(apiResponse);
            }
        }

        /// <summary>
        /// method to geenrate refresh token
        /// </summary>
        /// <returns></returns>
        [HttpPut]
        [Route("login/refresh")]
        [ProducesResponseType(typeof(ApiResponse<LoginResponse>), StatusCodes.Status200OK)]
        public async Task<IActionResult> LoginRefresh()
        {
            var apiResponse = new ApiResponse<LoginResponse>();
            try
            {
                apiResponse = await _authTokenBAL.RefreshTokenBAL(baseHttpRequestContext.UserId, baseHttpRequestContext.OrgId, baseHttpRequestContext.RoleId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("LoginRefresh Failed");
                _logger.LogError($"LoginRefresh Failed {Ex.Message} {Ex.StackTrace}");
                return Unauthorized(apiResponse);
            }

        }
        [HttpGet("ValidateInterServiceToken")]
        [AllowAnonymous]
        [ProducesResponseType(typeof(ApiResponse<AuthContext>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ValidateInterServiceToken([FromHeader(Name = "Authorization")] string token)
        {
            try
            {
                var apiResponse = _authTokenBAL.ValidateInterServiceToken(token.Split(" ").Last());
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                _logger.LogError($"Error while validating the JWT Token {Ex.Message} {Ex.StackTrace}");

                return BadRequest(new ApiResponse<AuthContext>
                {
                    StatusCode = StatusCodes.Status400BadRequest,
                    Message = "",
                    Errors = new List<string> { "Error while validating the JWT Token." }
                });

            }
        }

        [HttpGet("ValidateToken")]
        [AllowAnonymous]
        [ProducesResponseType(typeof(ApiResponse<AuthContext>), StatusCodes.Status200OK)]
        public async Task<ActionResult<ApiResponse<AuthContext>>> ValidateToken([FromHeader(Name = "Authorization")] string token)
        {
            try
            {
                var apiResponse = await _authTokenBAL.ValidateJWTTokenAsync(token.Split(" ").Last());
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {   
                _logger.LogError($"Error while validating the JWT Token {Ex.Message} {Ex.StackTrace} {Ex.InnerException}");
                return BadRequest(new ApiResponse<AuthContext>
                {
                    StatusCode = StatusCodes.Status400BadRequest,
                    Message = "",
                    Errors = new List<string> { "Error while validating the JWT Token." }
                });
            }
        }

        /// <summary>
        /// Function to Reset the password on click of the Forget Password screen
        /// </summary>
        /// <param name="resetPasswordRequest"></param>
        /// <returns></returns>
        [HttpPut("login/factors")]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Factors([FromBody] ResetPasswordRequest resetPasswordRequest)
        {
            try
            {
                var apiResponse = await _resetPasswordBAL.ResetPasswordAPI(resetPasswordRequest, baseHttpRequestContext.UserId, baseHttpRequestContext.OrgId, baseHttpRequestContext.RoleId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                _logger.LogError($"Password cannot be reset at the moment {Ex.Message} {Ex.StackTrace}");

                return BadRequest(new ApiResponse<AuthContext>
                {
                    StatusCode = StatusCodes.Status400BadRequest,
                    Message = "",
                    Errors = new List<string> { "Password cannot be reset at the moment.Please contact administrator or try again later." }
                });

            }
        }

        /// <summary>
        /// Function to geenrate a reset password link and email the same to user
        /// </summary>
        /// <param name="resetPasswordRequest"></param>
        /// <returns></returns>
        [HttpPost("factors/reset_link")]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ResetLink([FromBody] ResetLinkRequest resetLinkRequest)
        {
            try
            {
                var apiResponse = await _resetPasswordBAL.ResetPasswordLink(resetLinkRequest, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                _logger.LogError($"Password cannot be reset at the moment {Ex.Message} {Ex.StackTrace}");

                return BadRequest(new ApiResponse<AuthContext>
                {
                    StatusCode = StatusCodes.Status400BadRequest,
                    Message = "",
                    Errors = new List<string> { "Password cannot be reset at the moment.Please contact administrator or try again later." }
                });

            }
        }

        /// <summary>
        /// Add User to Auth Tables
        /// </summary>
        /// <param name="resetPasswordRequest"></param>
        /// <returns></returns>
        [HttpPost("adduser")]
        [ProducesResponseType(typeof(ApiResponse<long>), StatusCodes.Status200OK)]
        public async Task<IActionResult> AddAuthUser([FromBody] AuthUserRequest userRequest)
        {
            try
            {
                var apiResponse = await _iAuthUserBAL.AddAuthUserBAL(userRequest, baseHttpRequestContext.OrgId, baseHttpRequestContext.UserId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                _logger.LogError($"User can not be created {Ex.Message} {Ex.StackTrace}");

                return BadRequest(new ApiResponse<AuthContext>
                {
                    StatusCode = StatusCodes.Status400BadRequest,
                    Message = "",
                    Errors = new List<string> { "User can not be created." }
                });

            }
        }
        /// <summary>
        /// Add External Provider to Auth Tables
        /// </summary>
        /// <param name="userRequest"></param>
        /// <returns></returns>
        [HttpPost("addextprovider")]
        [ProducesResponseType(typeof(ApiResponse<long>), StatusCodes.Status200OK)]
        public async Task<IActionResult> AddAuthExternalProvider([FromBody] AuthUserRequest userRequest)
        {
            try
            {
                var apiResponse = await _iAuthUserBAL.AddAuthExternalProvider(userRequest, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                _logger.LogError($"User can not be created {Ex.Message} {Ex.StackTrace}");

                return BadRequest(new ApiResponse<AuthContext>
                {
                    StatusCode = StatusCodes.Status400BadRequest,
                    Message = "",
                    Errors = new List<string> { "User can not be created." }
                });

            }
        }
        /// <summary>
        /// Edit User to Auth Tables
        /// </summary>
        /// <param name="resetPasswordRequest"></param>
        /// <returns></returns>
        [HttpPut("edituser/{Id}")]
        [ProducesResponseType(typeof(ApiResponse<long>), StatusCodes.Status200OK)]
        public async Task<IActionResult> EditAuthUser(long Id, [FromBody] AuthUserRequest userRequest)
        {
            try
            {
                var apiResponse = await _iAuthUserBAL.EditAuthUserBAL(Id, userRequest, baseHttpRequestContext.OrgId, baseHttpRequestContext.UserId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                _logger.LogError($"Error while Editing the User Details {Ex.Message} {Ex.StackTrace}");

                return BadRequest(new ApiResponse<AuthContext>
                {
                    StatusCode = StatusCodes.Status400BadRequest,
                    Message = "",
                    Errors = new List<string> { "Error while Editing the User Details" }
                });
            }
        }
        /// <summary>
        /// API yto fetch role for a given user
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpGet("users/roleId/{Id}")]
        [ProducesResponseType(typeof(ApiResponse<int>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetRoleIdForUser(long Id)
        {
            var apiResponse = new ApiResponse<int>();
            try
            {
                apiResponse = await _iAuthUserBAL.GetRoleIdForUser(Id,baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = default(short);
                apiResponse.Errors.Add("Role cannot be fetched.");
                _logger.LogError($"Role cannot be fetched. {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// API yto fetch info  given users
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpGet("users/info")]
        [ProducesResponseType(typeof(ApiResponse<List<Authentication.Models.User>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetUserInfoFromIds(string ids)
        {
            var apiResponse = new ApiResponse<List < Authentication.Models.User >> ();
            try
            {
                apiResponse = await _iAuthUserBAL.GetUserInfoFromIds(ids, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Role cannot be fetched.");
                _logger.LogError($"Role cannot be fetched. {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
        [HttpGet("healthcheck")]
        [AllowAnonymous]
        [TelemetryTracing(logRequest: false, logResponse: false)]
        public IActionResult HealthCheck()
        {
            return Ok(StatusCodes.Status200OK);
        }

    }
}
