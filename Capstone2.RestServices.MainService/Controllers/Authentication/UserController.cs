﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.Authentication.Interfaces;
using Capstone2.RestServices.Authentication.Models;
using Capstone2.Shared.Models.Dtos;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Authentication
{
    [Route("auth")]
    [ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class UserController : BaseController
    {
        private readonly ILogger<UserController> _logger;
        private readonly IUserActivateBAL _iUserActivateBAL;
        public UserController(ILogger<UserController> logger, IUserActivateBAL iUserActivateBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _iUserActivateBAL = iUserActivateBAL;
        }

        /// <summary>
        /// Function to geenrate a reset password link and email the same to user for  User Activation
        /// </summary>
        /// <param name="resetPasswordRequest"></param>
        /// <returns></returns>
        [HttpPost("factors/activate")]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Activate([FromBody] UserActivationRequest userActivationRequest)
        {
            try
            {
                var apiResponse = await _iUserActivateBAL.GenerateUserActivationLink(userActivationRequest, baseHttpRequestContext.OrgId, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                _logger.LogError($"Error while User Activation {Ex.Message} {Ex.StackTrace}");
                return BadRequest(new ApiResponse<AuthContext>
                {
                    StatusCode = StatusCodes.Status400BadRequest,
                    Message = "",
                    Errors = new List<string> { "Error while User Activation. Please check the variables" }
                });

            }
        }



        /// <summary>
        /// Function to geenrate a patient registration link and email the same to patient email address
        /// </summary>
        /// <param name="PatinetRegistrationRequest"></param>
        /// <returns></returns>
        [HttpPost("patient_registration")]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Activate([FromBody] PatientRegistrationRequest patientRegRequest)
        {
            try
            {
                var apiResponse = await _iUserActivateBAL.GeneratePatientRegLink(patientRegRequest, baseHttpRequestContext.OrgId, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                _logger.LogError($"Error while generation patient registration link {Ex.Message} {Ex.StackTrace}");
                return BadRequest(new ApiResponse<AuthContext>
                {
                    StatusCode = StatusCodes.Status400BadRequest,
                    Message = "",
                    Errors = new List<string> { "Error while generation patient registration link. Please check the variables" }
                });

            }
        }

        /// <summary>
        /// Function to Fetch the token from UserTokenAssocs
        /// </summary>
        /// <param name="tokenAssoc"></param>
        /// <returns></returns>
        [HttpGet("user_token_assoc")]
        [ProducesResponseType(typeof(ApiResponse<long>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetUserTokenAssoc()
        {
            var apiResponse = new ApiResponse<long>();
            try
            {
                apiResponse = await _iUserActivateBAL.GetUserTokenAssocBAL(baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = 0;
                apiResponse.Errors.Add("Role cannot be fetched.");
                _logger.LogError($"Role cannot be fetched. {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


    }
}
