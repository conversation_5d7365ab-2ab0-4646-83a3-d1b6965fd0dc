﻿using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.Communication.Exceptions;
using Capstone2.RestServices.Communication.Interfaces;
using Capstone2.RestServices.Communication.Models;
using Capstone2.Shared.Models.Common;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;


namespace Capstone2.RestServices.Communication.Controller
{
    [ApiController]
    [Route("[controller]")]
    [ApiExceptionFilter]
    //[Authorize]
    public class EmailController : BaseController
    {
        private readonly ILogger<EmailController> _logger;
        private readonly IEmailBAL _emailBAL;
        public EmailController(ILogger<EmailController> logger, IEmailBAL emailBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _emailBAL = emailBAL;
        }

        [HttpPost]
        [Route("SendEmail")]
        [ProducesResponseType(typeof(int), StatusCodes.Status200OK)]
        public async Task<IActionResult> SendEmail(EmailRequestModel requestModel)
        {
            var result = await _emailBAL.SendEmail(requestModel, baseHttpRequestContext.OrgId);
            return Ok(new ApiResponse<int>
            {
                StatusCode = StatusCodes.Status200OK,
                Result = result
            });
        }

        [HttpGet("healthcheck")]
        [AllowAnonymous]
        public IActionResult HealthCheck()
        {
            return Ok(StatusCodes.Status200OK);
        }

        /// <summary>
        /// Method to fetch EmailTemplates
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("email_templates")]
        public async Task<IActionResult> ListEmailTemplates([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new Capstone2.Shared.Models.Responses.ApiResponse<QueryResultList<EmailTemplate>>();
            try
            {
                apiResponse = await _emailBAL.ListEmailTemplates(baseHttpRequestContext, queryModel);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of EmailTemplates could not be fetched during this time.");
                _logger.LogError($"List of EmailTemplates could not be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
    }
}
