﻿using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.Communication.Exceptions;
using Capstone2.RestServices.Communication.Interfaces;
using Capstone2.RestServices.Communication.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Communication.Controller
{
    [ApiController]
    [Route("[controller]")]
    [ApiExceptionFilter]
    //[Authorize]
    public class SmsController : BaseController
    {
        private readonly ILogger<SmsController> _logger;
        private readonly ISmsBAL _smsBAL;
        public SmsController(ILogger<SmsController> logger, ISmsBAL smsBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _smsBAL = smsBAL;
        }

        [HttpPost]
        [Route("SendSms")]
        [ProducesResponseType(typeof(ApiResponse<long>), StatusCodes.Status200OK)]
        public async Task<IActionResult> SendSms(SMSRequestModel requestModel)
        {
            var response = await _smsBAL.CallSmsAPIAsync(requestModel, baseHttpRequestContext.OrgId);
            return Ok(response);
        }

        [HttpPost]
        [Route("SendSmsWithReply")]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
        public async Task<IActionResult> SendSmsWithReply(SMSRequestModel requestModel)
        {
            var response = await _smsBAL.SendSmsWithReply(requestModel, baseHttpRequestContext);
            return Ok(response);
        }

        [HttpPost]
        [Route("ResendSms/{smsRequestId}")]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ResendSms(int smsRequestId)
        {
            var response = await _smsBAL.ReSendSMS(smsRequestId, baseHttpRequestContext);
            return Ok(response);
        }

        [AllowAnonymous]
        [HttpGet]
        [Route("GetSmsReply")]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetSmsReply()
        {
            var response = new ApiResponse<string>();
            try
            {
                SMSReplyModel sMSReplyModel = new SMSReplyModel();
                sMSReplyModel.MessageId = Request.Query["message_id"];
                sMSReplyModel.MobileNumber = Request.Query["mobile"];
                sMSReplyModel.Reply = Request.Query["response"];
                response = await _smsBAL.AddSMSReply(sMSReplyModel, baseHttpRequestContext);
                return Ok(response);
            }
            catch(Exception Ex)
            {
                response.StatusCode = StatusCodes.Status500InternalServerError;
                response.Result = null;
                response.Errors.Add("Error while updating SMS reply.");
                _logger.LogError($"Error while updating SMS reply. {Ex.Message} {Ex.StackTrace}");
                return BadRequest(response);
            }
           
        }

        /// <summary>
        /// Method to Get SMS Exception Data
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("sms_requests")]
        [ProducesResponseType(typeof(List<ApiResponse<SmsRequestData>>), StatusCodes.Status200OK)]

        public async Task<IActionResult> GetSmsRequest([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<List<SmsRequestData>>();
            try
            {
                apiResponse = await _smsBAL.GetSMSException(queryModel, baseHttpRequestContext.OrgId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Sms Request could not be fetched during this time.");
                _logger.LogError($"Sms Request could not be fetched during this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

    }
}
