﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.Invoice.Interfaces;
using Capstone2.RestServices.Invoice.Models;
using Capstone2.RestServices.MainService.Utility;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using InvoiceDetailView = Capstone2.RestServices.Invoice.Models.InvoiceDetailView;

namespace Capstone2.RestServices.Invoice.Controllers
{
    [ApiController]
    [Route("invoice")]
    //[Authorize]
    [ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class InvoiceController : BaseController
    {
        private readonly ILogger<InvoiceController> _logger;
        private readonly IInvoiceBAL _invoiceBAL;

        public InvoiceController(IOptions<MvcOptions> options, ILogger<InvoiceController> logger, IInvoiceBAL invoiceBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _invoiceBAL = invoiceBAL;
            options.Value.InputFormatters.Insert(0, InputFormater.GetInputFormater());
        }

        [HttpGet("healthcheck")]
        [AllowAnonymous]
        [TelemetryTracing(logRequest: false, logResponse: false)]
        public IActionResult HealthCheck()
        {
            return Ok(StatusCodes.Status200OK);
        }

        /// <summary>
        /// Method to add  new Invoice Summary
        /// </summary>
        /// <param name="invoiceSummary"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("invoice_summaries")]
        [ProducesResponseType(typeof(ApiResponse<long>), StatusCodes.Status200OK)]
        public async Task<IActionResult> AddInvoiceSummary([FromBody] InvoiceSummary invoiceSummary)
        {
            var apiResponse = new ApiResponse<long>();

            try
            {

                apiResponse = await _invoiceBAL.AddInvoiceSummary(invoiceSummary, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = default(short);
                apiResponse.Errors.Add("New Invoice Summary cannot be added." );
                _logger.LogError($"New Invoice Summary cannot be added {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
        /// <summary>
        /// Method to add  new Invoice Summary
        /// </summary>
        /// <param name="invoiceSummary"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("invoice_summaries/{id}")]
        [ProducesResponseType(typeof(ApiResponse<InvoiceSummaryView>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetInvoiceSummary(long id)
        {
            var apiResponse = new ApiResponse<InvoiceSummaryView>();

            try
            {

                apiResponse = await _invoiceBAL.GetInvoiceSummary(id, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Invoice Summary cannot be fetched at this time.");
                _logger.LogError($"Invoice Summary cannot be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to update Invoice Summary
        /// </summary>
        /// <param name="invoiceSummary"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("invoice_summaries/{id}")]
        [ProducesResponseType(typeof(ApiResponse<long?>), StatusCodes.Status200OK)]
        public async Task<IActionResult> EditInvoiceSummary(long id, [FromBody] InvoiceSummaryInput invoiceSummary)
        {
            var apiResponse = new ApiResponse<long?>();

            try
            {

                apiResponse = await _invoiceBAL.EditInvoiceSummary(id, invoiceSummary, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Invoice Summary cannot be updated at this time.");
                _logger.LogError($"Invoice Summary cannot be updated {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to retireve list of Invoice Summaries
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("invoice_summaries")]
        [ProducesResponseType(typeof(ApiResponse<QueryResultList<ListInvoiceSummary>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ListInvoiceSummary([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<QueryResultList<ListInvoiceSummary>>();

            try
            {

                apiResponse = await _invoiceBAL.ListInvoiceSummary(queryModel, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of Invoice Summary cannot be retrieved at this time.");
                _logger.LogError($"List of Invoice Summary cannot be retrieved {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to patch Patient Data
        /// </summary>
        /// <param name="id"></param>
        /// <param name="patientUpdateView"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("invoice_summaries/patch/{id}")]
        public async Task<IActionResult> PartialUpdateInvoiceSummary(long id, [FromBody] InvoiceSummaryUpdateView invoiceSummaryUpdateView)
        {
            var apiResponse = new ApiResponse<string>();

            try
            {
                apiResponse = await _invoiceBAL.PartialUpdateInvoiceSummary(baseHttpRequestContext, id, invoiceSummaryUpdateView);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while updating Invoice Summary.");
                _logger.LogError($"Error while updating Invoice Summary {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to update Invoice Detail
        /// </summary>
        /// <param name="invoiceDetail"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("invoice_summaries/{summary_id}/invoice_details/{id}")]
        [ProducesResponseType(typeof(ApiResponse<InvoiceDetail>), StatusCodes.Status200OK)]
        public async Task<IActionResult> EditInvoiceDetail(long id, [FromBody] InvoiceDetailInput invoiceDetail)
        {
            var apiResponse = new ApiResponse<InvoiceDetail>();

            try
            {
                apiResponse = await _invoiceBAL.EditInvoiceDetail(id, invoiceDetail, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Invoice Detail cannot be updated at this time.");
                _logger.LogError($"Invoice Detail cannot be updated {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
        
        [HttpGet]
        [Route("invoice_summaries/{summary_id}/invoice_details/{id}")]
        [ProducesResponseType(typeof(ApiResponse<InvoiceDetailView>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetInvoiceDetail(long id,long summary_id)
        {
            var apiResponse = new ApiResponse<InvoiceDetailView>();

            try
            {

                apiResponse = await _invoiceBAL.GetInvoiceDetail(id, summary_id, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Invoice Detail cannot be fetched at this time.");
                _logger.LogError($"Invoice Detail cannot be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
        /// <summary>
        /// Method to create a new Invoice
        /// </summary>
        /// <param name="invoiceDetail"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("invoice_summaries/{summary_id}/invoice_details")]
        [ProducesResponseType(typeof(ApiResponse<InvoiceDetail>), StatusCodes.Status200OK)]
        public async Task<IActionResult> AddInvoiceDetail([FromBody] InvoiceDetail invoiceDetail)
        {
            var apiResponse = new ApiResponse<InvoiceDetail>();

            try
            {

                apiResponse = await _invoiceBAL.AddInvoiceDetail(invoiceDetail, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("New Invoice cannot be added.");
                _logger.LogError($"New Invoice cannot be added {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
        /// <summary>
        /// Method to add a new Estimate Template
        /// </summary>
        /// <param name="invoiceTemplate"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("invoice_templates")]
        [ProducesResponseType(typeof(ApiResponse<long>), StatusCodes.Status200OK)]
        public async Task<IActionResult> AddInvoiceTemplate([FromBody] InvoiceTemplate invoiceTemplate)
        {
            var apiResponse = new ApiResponse<long>();

            try
            {

                apiResponse = await _invoiceBAL.AddInvoiceTemplate(invoiceTemplate, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = default(short);
                apiResponse.Errors.Add("New Estimate Template cannot be added.");
                _logger.LogError($"New Estimate Template cannot be added {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to fetch Estimate Template
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("invoice_templates/{id}")]
        [ProducesResponseType(typeof(ApiResponse<InvoiceTemplateView>), StatusCodes.Status200OK)]
        public async Task<IActionResult> FetchInvoiceTemplate(long id)
        {
            var apiResponse = new ApiResponse<InvoiceTemplateView>();

            try
            {

                apiResponse = await _invoiceBAL.FetchInvoiceTemplate(id, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Estimate Template cannot be fetched at this time.");
                _logger.LogError($"Estimate Template cannot be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to retireve list of Estimate Templates
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("invoice_templates")]
        [ProducesResponseType(typeof(ApiResponse<QueryResultList<InvoiceTemplate>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ListInvoiceTemplate([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<QueryResultList<InvoiceTemplate>>();

            try
            {

                apiResponse = await _invoiceBAL.ListInvoiceTemplate(queryModel, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of Estimate Template cannot be retrieved at this time.");
                _logger.LogError($"List of Estimate Template cannot be retrieved {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
        /// <summary>
        /// Method to partial update Invoice Template
        /// </summary>
        /// <param name="id"></param>
        /// <param name="invoiceTemplateUpdateView"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("invoice_templates/patch/{id}")]
        [ProducesResponseType(typeof(ApiResponse<long>), StatusCodes.Status200OK)]
        public async Task<IActionResult> PartialUpdateInvoiceTemplate(long id, [FromBody] InvoiceTemplateUpdateView invoiceTemplateUpdateView)
        {
            var apiResponse = new ApiResponse<long>();

            try
            {

                apiResponse = await _invoiceBAL.PartialUpdateInvoiceTemplate(id, invoiceTemplateUpdateView, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = default(long);
                apiResponse.Errors.Add("Estimate Template cannot be updated at this time.");
                _logger.LogError($"Estimate Template cannot be updated {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to validate if the template name  already exists
        /// </summary>
        /// <param name="search_term"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("invoice_templates/name/verify")]
        [ProducesResponseType(typeof(ApiResponse<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> CheckTemplateName(string search_term)
        {
            var apiResponse = new ApiResponse<bool>();
            try
            {
                apiResponse = await _invoiceBAL.CheckTemplateName(search_term, baseHttpRequestContext.OrgId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error while verifying the template name.");
                _logger.LogError($"Error while verifying the template name {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }
        
        [HttpPut]
        [Route("invoice_templates/{id}")]
        [ProducesResponseType(typeof(ApiResponse<long?>), StatusCodes.Status200OK)]
        public async Task<IActionResult> UpdateInvoiceTemplate(long id, [FromBody] InvoiceTemplateInput invoiceTemplateInput)
        {
            var apiResponse = new ApiResponse<long?>();

            try
            {
                apiResponse = await _invoiceBAL.UpdateInvoiceTemplate(baseHttpRequestContext, id, invoiceTemplateInput);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while updating Estimate Template.");
                _logger.LogError($"Error while updating Estimate Template {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


        /// <summary>
        /// Method to delete a estimate template based on id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("invoice_templates/{id}")]
        [ProducesResponseType(typeof(ApiResponse<long?>), StatusCodes.Status200OK)]
        public async Task<IActionResult> DeleteInvoiceTemplate(long id, [FromQuery] DeleteObject deleteObject)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _invoiceBAL.DeleteInvoiceTemplate(baseHttpRequestContext, id, deleteObject);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while deleting Estimate template.");
                _logger.LogError($"Error while deleting Estimate template {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
        /// <summary>
        /// Method to override an existing template
        /// </summary>
        /// <param name="id"></param>
        /// <param name="invoiceTemplateInput"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("invoice_templates/override/{id}")]
        [ProducesResponseType(typeof(ApiResponse<long?>), StatusCodes.Status200OK)]
        public async Task<IActionResult> OverrrideInvoiceTemplate(long id, [FromBody] InvoiceTemplate invoiceTemplate)
        {
            var apiResponse = new ApiResponse<long?>();

            try
            {
                apiResponse = await _invoiceBAL.OverrrideInvoiceTemplate(baseHttpRequestContext, id, invoiceTemplate);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while Overriding Estimate Template.");
                _logger.LogError($"Error while Overriding Estimate Template {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


        /// <summary>
        /// Method to patch InvoiceDetail data
        /// </summary>
        /// <param name="id"></param>
        /// <param name="patientUpdateView"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("invoice_details/patch/{id}")]
        public async Task<IActionResult> PartialUpdateInvoiceDetail(long id, [FromBody] InvoiceDetailUpdate invoiceDetailUpdate)
        {
            var apiResponse = new ApiResponse<string>();

            try
            {
                apiResponse = await _invoiceBAL.PartialUpdateInvoiceDetail(baseHttpRequestContext, id, invoiceDetailUpdate);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while updating Invoice/Estimate.");
                _logger.LogError($"Error while updating Invoice/Estimate {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to add  new Invoice Summary
        /// </summary>
        /// <param name="invoiceSummaryId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("invoice_summaries/getInvoiceSummaryTags/{id}")]
        [ProducesResponseType(typeof(ApiResponse<InvoiceTemplateTagsData>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetInvoiceSummaryTagData(long id)
        {
            var apiResponse = new ApiResponse<InvoiceTemplateTagsData>();

            try
            {
                apiResponse = await _invoiceBAL.GetInvoiceSummaryTagData(id, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Invoice Summary Tags cannot be fetched at this time.");
                _logger.LogError($"Invoice Summary Tags cannot be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to add  new Invoice Summary
        /// </summary>
        /// <param name="invoiceSummaryId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("invoice_summaries/getInvoiceDetailedSummaryTags/{id}")]
        [ProducesResponseType(typeof(ApiResponse<InvoiceTagsDetails>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetInvoiceDetailedSummaryTags(long id)
        {
            var apiResponse = new ApiResponse<InvoiceTagsDetails>();

            try
            {
                apiResponse = await _invoiceBAL.GetInvoiceDetailedSummaryTags(id, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Invoice Summary Tags cannot be fetched at this time." + Ex.Message);
                _logger.LogError($"Invoice Summary Tags cannot be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to add  new Invoice Summary
        /// </summary>
        /// <param name="invoiceSummaryId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("invoice_Details/GetInvoiceDepositTagsDetails/{id}")]
        [ProducesResponseType(typeof(ApiResponse<InvoiceDepositTagsDetails>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetInvoiceDepositTagsDetails(long id)
        {
            var apiResponse = new ApiResponse<InvoiceDepositTagsDetails>();

            try
            {
                apiResponse = await _invoiceBAL.GetInvoiceDepositTagsDetails(id, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("GetInvoiceDepositTagsDetails cannot be fetched at this time." + Ex.Message);
                _logger.LogError($"GetInvoiceDepositTagsDetails cannot be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method for Invoice Adjustment
        /// </summary>
        /// <param name="inputPaymentAdjustmentDetail"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("invoice_Details/{id}/payment_adjustment")]
        [ProducesResponseType(typeof(ApiResponse<long>), StatusCodes.Status200OK)]
        public async Task<IActionResult> InvoiceAdjustment([FromBody] InputPaymentAdjustmentDetail inputPaymentAdjustmentDetail,long id)
        {
            var apiResponse = new ApiResponse<long>();

            try
            {
                apiResponse = await _invoiceBAL.InvoiceAdjustment(id, inputPaymentAdjustmentDetail,baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = default(long);
                apiResponse.Errors.Add("Invoice Adjustment cannot be performed at this time." + Ex.Message);
                _logger.LogError($"Invoice Adjustment cannot be performed {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method for Fetching Adjustment details
        /// </summary>
        /// <param name="id"></param>
        /// <param name="paymentDetailsId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("invoice_Details/{id}/payment_adjustment/{paymentDetailsId}")]
        [ProducesResponseType(typeof(ApiResponse<InputPaymentAdjustmentDetail>), StatusCodes.Status200OK)]
        public async Task<IActionResult> InvoiceAdjustment(long id,long paymentDetailsId)
        {
            var apiResponse = new ApiResponse<InputPaymentAdjustmentDetail>();

            try
            {
                apiResponse = await _invoiceBAL.FetchInvoiceAdjustment(id, paymentDetailsId, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Invoice Adjustment Details cannot be fetched at this time." + Ex.Message);
                _logger.LogError($"Invoice Adjustment Details cannot be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to get invoice custom billing receipt data
        /// </summary>
        /// <param name="invoiceSummaryId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("invoice_Details/GeInvoiceCustomBillingTagData/{id}")]
        [ProducesResponseType(typeof(ApiResponse<InvoiceDepositTagsDetails>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GeInvoiceCustomBillingTagData(long id)
        {
            var apiResponse = new ApiResponse<InvoiceDepositTagsDetails>();

            try
            {
                apiResponse = await _invoiceBAL.GeInvoiceCustomBillingTagData(id, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("GeInvoiceCustomBillingTagData cannot be fetched at this time." + Ex.Message);
                _logger.LogError($"GeInvoiceCustomBillingTagData cannot be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to GetAppointmentInvoiceList Data
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("invoice_Details/appointment/{appointmentDetailsId}")]
        [ProducesResponseType(typeof(ApiResponse<List<ListInvoiceDetails>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetAppointmentInvoiceList(long appointmentDetailsId, [FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<List<ListInvoiceDetails>>();

            try
            {
                apiResponse = await _invoiceBAL.GetAppointmentInvoiceList(queryModel, appointmentDetailsId,  baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("GetAppointmentInvoiceList cannot be fetched at this time." + Ex.Message);
                _logger.LogError($"GetAppointmentInvoiceList cannot be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to patch InvoiceMedicareAssoc
        /// </summary>
        /// <param name="id"></param>
        /// <param name="invoiceMedicareAssocUpdateView"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("invoice_medicareassoc/patch/{id}")]
        public async Task<IActionResult> PartialUpdateInvoiceMedicareAssoc(long id, [FromBody] InvoiceMedicareAssocUpdateView invoiceMedicareAssocUpdateView)
        {
            var apiResponse = new ApiResponse<string>();

            try
            {
                apiResponse = await _invoiceBAL.PartialUpdateInvoiceMedicareAssoc(baseHttpRequestContext, id, invoiceMedicareAssocUpdateView);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while updating InvoiceMedicareAssoc.");
                _logger.LogError($"Error while updating InvoiceMedicareAssoc {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

       /// <summary>
       /// Method to retrieve patient's high level finance summary
       /// </summary>
       /// <param name="patient_id"></param>
       /// <returns></returns>
        [HttpGet]
        [Route("{patient_id}/finance_summary")]
        [ProducesResponseType(typeof(ApiResponse<List<FinanceSummaryGroupView>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetFinanceSummary(long patient_id, [FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<List<FinanceSummaryGroupView>>();

            try
            {

                apiResponse = await _invoiceBAL.GetFinanceSummary(patient_id, baseHttpRequestContext, queryModel);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Finance Summary cannot be retrieved at this time.");
                _logger.LogError($"Finance Summary cannot be retrieved {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


        /// <summary>
        /// Method to get invoice custom billing receipt data
        /// </summary>
        /// <param name="invoiceSummaryId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("invoice_details_info/{id}")]
        [ProducesResponseType(typeof(ApiResponse<InvoiceDetailUpdate>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetInvoiceDetailInfoForPayment(long id)
        {
            var apiResponse = new ApiResponse<InvoiceDetailUpdate>();

            try
            {
                apiResponse = await _invoiceBAL.GetInvoiceDetailInfoForPayment(id, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("GetInvoiceDetailInfoForPayment cannot be fetched at this time." + Ex.Message);
                _logger.LogError($"GetInvoiceDetailInfoForPayment cannot be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
    }
}
