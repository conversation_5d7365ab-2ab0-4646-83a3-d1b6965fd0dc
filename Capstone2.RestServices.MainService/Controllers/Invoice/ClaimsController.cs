﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.Invoice.Interfaces;
using Capstone2.RestServices.Invoice.Models;
using Capstone2.RestServices.MainService.Utility;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Invoice.Controllers
{
    [ApiController]
    [Route("invoice")]
    //[Authorize]
    [ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class ClaimsController : BaseController
    {
        private readonly ILogger<ClaimsController> _logger;
        private readonly IClaimsBAL _claimsBAL;
        private readonly IEclipseClaimsBAL _eclipseClaimsBAL;

        public ClaimsController(IOptions<MvcOptions> options, ILogger<ClaimsController> logger, IClaimsBAL claimsBAL, IEclipseClaimsBAL eclipseClaimsBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _claimsBAL = claimsBAL;
            _eclipseClaimsBAL = eclipseClaimsBAL;
            options.Value.InputFormatters.Insert(0, InputFormater.GetInputFormater());
        }


        [HttpPost]
        [Route("medicare_patient_claim")]
        [ProducesResponseType(typeof(ApiResponse<dynamic>), StatusCodes.Status200OK)]
        public async Task<IActionResult> SubmitPCI([FromBody] InvoiceDetailIdObject invoiceDetailObj)
        {
            var apiResponse = new ApiResponse<dynamic>();

            try
            {

                apiResponse = await _claimsBAL.SubmitPCI(invoiceDetailObj, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = default(short);
                apiResponse.Errors.Add("Patient Claim cannot be raised.");
                _logger.LogError($"Patient Claim cannot be raised {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
        /// <summary>
        /// method to initiate same day delete
        /// </summary>
        /// <param name="invoiceDetailObj"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("medicare_sdd")]
        [ProducesResponseType(typeof(ApiResponse<dynamic>), StatusCodes.Status200OK)]
        public async Task<IActionResult> SubmitSDD([FromBody] InvoiceDetailIdObject invoiceDetailObj)
        {
            var apiResponse = new ApiResponse<dynamic>();

            try
            {

                apiResponse = await _claimsBAL.SubmitSDD(invoiceDetailObj, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = default(short);
                apiResponse.Errors.Add("Same Day Delete cannot be raised.");
                _logger.LogError($"Same Day Delete cannot be raised {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
        /// <summary>
        /// method to initiate same day delete
        /// </summary>
        /// <param name="invoiceDetailObj"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("medicare_patient_claim_resubmit")]
        [ProducesResponseType(typeof(ApiResponse<dynamic>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ResubmitPCI([FromBody] InvoiceDetailIdObject invoiceDetailObj)
        {
            var apiResponse = new ApiResponse<dynamic>();

            try
            {

                apiResponse = await _claimsBAL.ResubmitPCI(invoiceDetailObj, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = default(short);
                apiResponse.Errors.Add("Patient Claim cannot be resubmitted.");
                _logger.LogError($"Patient Claim cannot be resubmitted {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// method to initiate process claims
        /// </summary>
        /// <param name="invoiceDetailObj"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("process_claims")]
        [ProducesResponseType(typeof(ApiResponse<dynamic>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ProcessInvoiceForClaims([FromBody] InvoiceDetailIdObject invoiceDetailObj)
        {
            var apiResponse = new ApiResponse<dynamic>();
            try
            {
                apiResponse = await _claimsBAL.ProcessInvoiceForClaims(invoiceDetailObj, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = default(short);
                apiResponse.Errors.Add("Medicare Claim cannot be raised.");
                _logger.LogError($"Medicare Claim cannot be raised {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// method to initiate reprocess claims
        /// </summary>
        /// <param name="invoiceDetailObj"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("reprocess_claims")]
        [ProducesResponseType(typeof(ApiResponse<dynamic>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ReProcessInvoiceForClaims([FromBody] ClaimRequestObject claimRequestObj)
        {
            var apiResponse = new ApiResponse<dynamic>();
            try
            {
                apiResponse = await _claimsBAL.ReProcessInvoiceForClaims(claimRequestObj, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = default(short);
                apiResponse.Errors.Add("Medicare Claim cannot be reprocessed.");
                _logger.LogError($"Medicare Claim cannot be reprocessed {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpPost]
        [Route("oec")]
        [ProducesResponseType(typeof(ApiResponse<dynamic>), StatusCodes.Status200OK)]
        public async Task<IActionResult> SubmitOEC([FromBody] InvoiceDetailIdObject invoiceDetailObj)
        {
            var apiResponse = new ApiResponse<dynamic>();

            try
            {

                apiResponse = await _eclipseClaimsBAL.SubmitOEC(invoiceDetailObj.Id, invoiceDetailObj.PatientDetailsId, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = default(short);
                apiResponse.Errors.Add("Online Eligibility Check cannot be performed at this time.");
                _logger.LogError($"Online Eligibility Check cannot be performed {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpPost]
        [Route("claim/{claimTypeId}")]
        [ProducesResponseType(typeof(ApiResponse<dynamic>), StatusCodes.Status200OK)]
        public async Task<IActionResult> SubmitEclipseClaim([FromBody] InvoiceDetailIdObject invoiceDetailObj,short claimTypeId)
        {
            var apiResponse = new ApiResponse<dynamic>();

            try
            {

                apiResponse = await _eclipseClaimsBAL.SubmitEclipseClaim(invoiceDetailObj, baseHttpRequestContext,claimTypeId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = default(short);
                if (claimTypeId == (short)ClaimType.OEC)
                {
                    apiResponse.Errors.Add("Online Eligibility Check cannot be performed at this time.");
                    _logger.LogError($"Online Eligibility Check cannot be performed {Ex.Message} {Ex.StackTrace}");
                }
                else if (claimTypeId == (short)ClaimType.IMC)
                {
                    apiResponse.Errors.Add("IMC cannot be raised at this time.");
                    _logger.LogError($"IMC cannot be raised {Ex.Message} {Ex.StackTrace}");
                }
                else 
                {
                    apiResponse.Errors.Add("Eclipse claims cannot be raised at this time.");
                    _logger.LogError($"Eclipse claims cannot be raised {Ex.Message} {Ex.StackTrace}");

                }
                return BadRequest(apiResponse);
            }
        }
    }
}
