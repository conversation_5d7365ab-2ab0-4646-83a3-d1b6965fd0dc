﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.Migration.Interfaces;
using Capstone2.RestServices.Migration.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Migration.Controllers
{
    [ApiController]
    [Route("Migration")]
    //  [Authorize]
    [ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class MigrationController : BaseController
    {
        private readonly ILogger<MigrationController> _logger;
        private readonly IUploaderBAL _uploaderBAL;
        private readonly IMediaUploadBAL _mediaUploadBAL;
        private const int MAX_MEDIA_UPLOAD_OBJECTS = 100;

        public MigrationController(ILogger<MigrationController> logger, IUploaderBAL uploaderBAL, IMediaUploadBAL mediaUploadBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _uploaderBAL = uploaderBAL;
            _mediaUploadBAL = mediaUploadBAL;
        }

        [HttpGet("healthcheck")]
        [AllowAnonymous]
        [TelemetryTracing(logRequest: false, logResponse: false)]
        public IActionResult HealthCheck()
        {
            return Ok(StatusCodes.Status200OK);
        }

        /// <summary>
        /// Api to add a new uploader data
        /// </summary>
        /// <param name="inputMediaUploader"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("AddUpload")]
        public async Task<IActionResult> AddUpload([FromBody] Upload inputMediaUpload)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _uploaderBAL.AddUploadBAL(inputMediaUpload, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while creating Migration upload.Please check all variables");
                _logger.LogError($"Error while creating Migration upload {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Api to validate the staging data
        /// </summary>
        /// <param name="inputMediaUploader"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("ValidateData")]
        public async Task<IActionResult> ValidateData([FromBody] Upload inputMediaUpload)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _uploaderBAL.ValidateDataBAL(inputMediaUpload, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while validating staging data.");
                _logger.LogError($"Error while validating staging data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }

        /// <summary>
        /// Api to migrate the staging data
        /// </summary>
        /// <param name="inputMediaUploader"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("MigrateData")]
        public async Task<IActionResult> MigrateData([FromBody] Upload inputMediaUpload)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _uploaderBAL.MigrateDataBAL(inputMediaUpload, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while migrating staging data.");
                _logger.LogError($"Error while migrating staging data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }

        /// <summary>
        /// Method to fetch Migration Uploads 
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("ListUploads")]
        public async Task<IActionResult> ListUploads([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<QueryResultList<UploadView>>();
            try
            {
                apiResponse = await _uploaderBAL.ListUploadBAL(baseHttpRequestContext, queryModel);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of Migration uploads could not be fetched during this time.");
                _logger.LogError($"List of Migration uploads could not be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to fetch Migration Uploads Data
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("ListUploadData")]
        public async Task<IActionResult> ListUploadData([FromQuery] UploadInput inputMediaUpload)
        {
            var apiResponse = new ApiResponse<string>();
            try
            {
                apiResponse = await _uploaderBAL.ListUploadDataBAL(baseHttpRequestContext, inputMediaUpload);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of Migration uploads could not be fetched during this time.");
                _logger.LogError($"List of Migration uploads could not be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpPost]
        [Route("mediaupload_details")]
        public async Task<IActionResult> AddMediaUpload([FromBody] List<MediaUpload> listMediaUploads)
        {
            var apiResponse = new ApiResponse<List<MediaUpload>>();

            if(listMediaUploads.Count > MAX_MEDIA_UPLOAD_OBJECTS)
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = null;
                apiResponse.Errors.Add(string.Format("Maximum limit exceeded. Please provide up to {0} objects.", MAX_MEDIA_UPLOAD_OBJECTS.ToString()));
                return BadRequest(apiResponse);
            }

            try
            {
                apiResponse = await _mediaUploadBAL.AddMediaUploadBAL(listMediaUploads, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while creating Migration media upload.Please check all variables");
                _logger.LogError($"Error while creating Migration media upload {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpPut]
        [Route("mediaupload_details")]
        public async Task<IActionResult> UpdateMediaUpload([FromBody] List<MediaUpload> listMediaUploads)
        {
            var apiResponse = new ApiResponse<List<MediaUpload>>();

            if (listMediaUploads.Count > MAX_MEDIA_UPLOAD_OBJECTS)
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = null;
                apiResponse.Errors.Add(string.Format("Maximum limit exceeded. Please provide up to {0} objects.", MAX_MEDIA_UPLOAD_OBJECTS.ToString()));
                return BadRequest(apiResponse);
            }
            try
            {
                apiResponse = await _mediaUploadBAL.UpdateMediaUploadBAL(listMediaUploads, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while updating Migration media upload.Please check all variables");
                _logger.LogError($"Error while updating Migration media upload {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpDelete]
        [Route("mediaupload_details")]
        public async Task<IActionResult> DeleteMediaUpload([FromBody] List<long> listMediaUploads)
        {
            var apiResponse = new ApiResponse<long?>();

            if (listMediaUploads.Count > MAX_MEDIA_UPLOAD_OBJECTS)
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = null;
                apiResponse.Errors.Add(string.Format("Maximum limit exceeded. Please provide up to {0} objects.", MAX_MEDIA_UPLOAD_OBJECTS.ToString()));
                return BadRequest(apiResponse);
            }
            try
            {
                apiResponse = await _mediaUploadBAL.DeleteMediaUploadBAL(listMediaUploads, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while deleting Migration media upload.Please check all variables");
                _logger.LogError($"Error while deleting Migration media upload {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to fetch Migration Media Uploads 
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("mediaupload_details")]
        public async Task<IActionResult> ListMediaUploads([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<QueryResultList<MediaUploadView>>();
            try
            {
                apiResponse = await _mediaUploadBAL.ListMediaUploadBAL(baseHttpRequestContext, queryModel);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of Migration media uploads could not be fetched during this time.");
                _logger.LogError($"List of Migration media uploads could not be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to Fetch a Media Upload based on id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("mediaupload_details/{id}")]
        public async Task<IActionResult> GetMediaUploadDetails(long id)
        {
            var apiResponse = new ApiResponse<MediaUpload>();
            try
            {
                apiResponse = await _mediaUploadBAL.GetMediaUploadDetails(id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting media upload data");
                _logger.LogError($"Error while getting media upload data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to update the meta data of the uploaded media
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("mediaupload_details/metadata")]
        public async Task<IActionResult> UpdateMetaDataAsync()
        {
            var apiResponse = new ApiResponse<bool>();
            try
            {
                apiResponse = await _mediaUploadBAL.UpdateMetaDataAsync();
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = false;
                apiResponse.Errors.Add("Error while getting media upload data");
                _logger.LogError($"Error while getting media upload data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
    }
}
