﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.Utility.Interfaces;
using Capstone2.RestServices.Utility.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Utility.Controllers
{
    [ApiController]
    [Route("utility")]
    //  [Authorize]
    [ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class LetterTemplateController : BaseController
    {
        private readonly ILogger<SnippetController> _logger;
        private readonly ILetterTemplateBAL _letterTemplateBAL;

        public LetterTemplateController(ILogger<SnippetController> logger, ILetterTemplateBAL letterTemplateBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _letterTemplateBAL = letterTemplateBAL;
        }

        /// <summary>
        /// Api to add a new LetterTemplate
        /// </summary>
        /// <param name="inputLetterTemplate"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("letter_templates")]
        public async Task<IActionResult> AddLetterTemplate([FromBody] LetterTemplate inputLetterTemplate)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _letterTemplateBAL.AddLetterTemplateBAL(inputLetterTemplate, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while creating Letter Template.Please check all variables");
                _logger.LogError($"Error while creating Letter Template {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }

        /// <summary>
        /// Method to validate if the letter template name already exists
        /// </summary>
        /// <param name="templateTypeId">templateTypeId</param>
        /// <param name="search_term">search_term</param>
        /// <returns></returns>
        [HttpGet]
        [Route("letter_templates/{templateTypeId}/name/verify")]
        public async Task<IActionResult> CheckLetterTemplateName(long templateTypeId, string search_term)
        {
            var apiResponse = new ApiResponse<bool>();
            try
            {
                apiResponse = await _letterTemplateBAL.CheckLetterTemplateName(templateTypeId, search_term, baseHttpRequestContext.OrgId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error while verifying the letter template name");
                _logger.LogError($"Error while verifying the letter template name {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }


        /// <summary>
        /// Method to fetch a letter template based on id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("letter_templates/{id}")]
        public async Task<IActionResult> GetLetterTemplate(long id)
        {
            var apiResponse = new ApiResponse<LetterTemplateView>();
            try
            {
                apiResponse = await _letterTemplateBAL.GetLetterTemplate(baseHttpRequestContext, id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting Letter Template Details");
                _logger.LogError($"Error while getting Letter Template Details {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to fetch Letter Templates 
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("letter_templates")]
        public async Task<IActionResult> ListLetterTemplates([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<QueryResultList<ListLetterTemplate>>();
            try
            {
                apiResponse = await _letterTemplateBAL.ListLetterTemplatesBAL(baseHttpRequestContext, queryModel);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of Letter Templates could not be fetched during this time.");
                _logger.LogError($"List of Letter Templates could not be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to delete a letter template based on id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("letter_templates/{id}")]
        public async Task<IActionResult> DeleteLetterTemplate(long id, [FromQuery] LetterDeleteObject letterDelete)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _letterTemplateBAL.DeleteLetterTemplateBAL(baseHttpRequestContext, id, letterDelete);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while deleting Letter Templates");
                _logger.LogError($"Error while deleting Letter Templates {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to update a Letter Template 
        /// </summary>
        /// <param name="inputLetterTemplate"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("letter_templates/{id}")]
        public async Task<IActionResult> EditLetterTemplates(long id, [FromBody] LetterTemplate inputLetterTemplate)
        {
            var apiResponse = new ApiResponse<string>();
            try
            {
                apiResponse = await _letterTemplateBAL.EditLetterTemplateBAL(id, inputLetterTemplate, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Letter Template cannot be Edited.Please try again later.");
                _logger.LogError($"Letter Template cannot be Edited {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to fetch Letter Templates for given User
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("letter_templates/user/{id}/{templateTypeId}")]
        public async Task<IActionResult> LetterTemplatesForUser(long id, long templateTypeId)
        {
            var apiResponse = new ApiResponse<LetterTemplateForUser>();
            try
            {
                apiResponse = await _letterTemplateBAL.LetterTemplatesForUserBAL(baseHttpRequestContext, id, templateTypeId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Letter Templates for User can not be fetched at this time");
                _logger.LogError($"Letter Templates for User can not be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to fetch Letter Templates for given User
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("letter_templates/verifyTemplateExits/{templateTypeId}")]
        public async Task<IActionResult> VerifyTemplateExits(long templateSubTypeId)
        {
            var apiResponse = new ApiResponse<bool>();
            try
            {
                apiResponse = await _letterTemplateBAL.VerifyTemplateExits(templateSubTypeId, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = false;
                apiResponse.Errors.Add("VerifyTemplateExits not working.");
                _logger.LogError($"VerifyTemplateExits not working {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


        /// <summary>
        /// Method to fetch Letter Templates for given subtype
        /// </summary>
        /// 
        /// <returns></returns>
        [HttpGet]
        [Route("letter_templates/template_type/{templateTypeId}/template_subType/{templateSubTypeId}")]
        public async Task<IActionResult> FettchLetterTemplateFromSubType(short templateTypeId, short templateSubTypeId)
        {
            var apiResponse = new ApiResponse<LetterTemplate>();
            try
            {
                apiResponse = await _letterTemplateBAL.FettchLetterTemplateFromSubType(baseHttpRequestContext, templateSubTypeId, templateTypeId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Letter Templates  can not be fetched at this time");
                _logger.LogError($"Letter Templates  can not be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

    }
}
