﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.Utility.Interfaces;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Role = Capstone2.RestServices.Utility.Models.Role;
using RolesPermissionsAssoc = Capstone2.RestServices.Utility.Models.RolesPermissionsAssoc;

namespace Capstone2.RestServices.Utility.Controllers
{
    [ApiController]
    [Route("utility")]
    //[Authorize]
    [ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class RolesController : BaseController
    {
        private readonly ILogger<RolesController> _logger;
        private readonly IRolesBAL _rolesBAL;
        public RolesController(ILogger<RolesController> logger, IRolesBAL rolesBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _rolesBAL = rolesBAL;
        }

        /// <summary>
        /// Method to fetch Roles 
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("roles")]
        [ProducesResponseType(typeof(ApiResponse<List<Role>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ListRoles()
        {
            var apiResponse = new ApiResponse<List<Role>>();

            try
            {
                apiResponse = await _rolesBAL.ListRolesBAL(baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of Roles could not be fetched during this time.");
                _logger.LogError($"List of Roles could not be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to fetch RolesPermissionsAssocs 
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("roles_permissions_assocs")]
        [ProducesResponseType(typeof(ApiResponse<List<RolesPermissionsAssoc>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ListRolesPermissionAssocs([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<List<RolesPermissionsAssoc>>();

            try
            {
                apiResponse = await _rolesBAL.ListRolesPermissionsAssocsBAL(baseHttpRequestContext, queryModel);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of RolesPermissionsAssocs could not be fetched during this time.");
                _logger.LogError($"List of RolesPermissionsAssocs could not be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to update an RolesPermissionsAssocs 
        /// </summary>
        /// <param name="inputAssoc"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("roles/{roles_id}/roles_permissions_assocs")]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
        public async Task<IActionResult> EditRolesPermissionsAssocs([FromBody] RolesPermissionsAssoc[] inputAssocs , int roles_id)
        {
            var apiResponse = new ApiResponse<string>();

            try
            {

                apiResponse = await _rolesBAL.EditRolesPermissionsAssocsBAL(inputAssocs, baseHttpRequestContext, roles_id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("RolesPermissionsAssocs cannot be Edited.Please try again later.");
                _logger.LogError($"RolesPermissionsAssocs cannot be Edited {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
    }
}

