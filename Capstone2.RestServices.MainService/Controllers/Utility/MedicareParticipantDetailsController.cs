﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Microsoft.AspNetCore.Mvc;
using Capstone2.RestServices.Utility.Interfaces;
using Capstone2.RestServices.Utility.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using System.Collections.Generic;
using Capstone2.Shared.Models.Entities;

namespace Capstone2.RestServices.Utility.Controllers
{
    [ApiController]
    [Route("utility")]
    // [Authorize]
    [ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class MedicareParticipantDetailsController : BaseController
    {

        private readonly ILogger<MedicareParticipantDetailsController> _logger;
        private readonly IMedicareParticipantDetailsBAL _medicareParticipantsBAL;
        public MedicareParticipantDetailsController(ILogger<MedicareParticipantDetailsController> logger, IMedicareParticipantDetailsBAL medicareParticipantDetailsBAL, IHttpContextAccessor httpContextAccessor): base(httpContextAccessor)
        {
            _medicareParticipantsBAL = medicareParticipantDetailsBAL;
            _logger = logger;
        }

        [HttpGet]
        [Route("medical_contractor")]
        [ProducesResponseType(typeof(ApiResponse<QueryResultList<MedicalContractorList>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ListMedicareParticipants([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<QueryResultList<MedicalContractorList>>();

            try
            {

                apiResponse = await _medicareParticipantsBAL.GetMedicareParticipantDetails(baseHttpRequestContext,queryModel);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of Medicare participants cannot be retrieved at this time.");
                _logger.LogError($"List of Medicare participants cannot be retrieved {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


        /// <summary>
        /// Api to add a new Medical Contractor
        /// </summary>
        /// <param name="MedicalContractor"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("medical_contractor")]
        public async Task<IActionResult> AddMedicalContractor([FromBody] MedicalContractor inputmedicalContractor)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _medicareParticipantsBAL.AddMedContBAL(inputmedicalContractor, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while creating Medical Contractor.Please check all variables");
                _logger.LogError($"Error while creating Medical Contractor {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to add New MedicalContractorBandAssocs
        /// </summary>
        /// <param name="inputAction"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("medical_contractor/{id}/medicalcontractorbandassocs")]
        [ProducesResponseType(typeof(ApiResponse<long?>), StatusCodes.Status200OK)]
        public async Task<IActionResult> AddMCBandAssocs(int id, MCBandAssocInput mcBandAssocInput)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _medicareParticipantsBAL.AddMCBandAssocsBAL(id, mcBandAssocInput, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                // TODO: will remove once rectify the error
                apiResponse.Errors.Add("Error while creating MedicalContractorBandAssocs. Please check all variables.");
                _logger.LogError($"Error while creating MedicalContractorBandAssocs {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }

        /// <summary>
        /// Method to fetch medicalcontractorbandassocs based on id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("medical_contractor/{id}/medicalcontractorbandassocs")]
        public async Task<IActionResult> GetMedicalContractorBandAssoc(int id, [FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<List<MedicalContractorBandAssoc>>();
            try
            {
                apiResponse = await _medicareParticipantsBAL.GetMedContBandAssocBAL(baseHttpRequestContext, id, queryModel);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting MedicalContractorBandAssoc");
                _logger.LogError($"Error while getting MedicalContractorBandAssoc {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


        /// <summary>
        /// Method to Edit MedicalContractorBandAssocs
        /// </summary>
        /// <param name="mcBandassocs"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("medical_contractor/{id}/medicalcontractorbandassocs")]
        [ProducesResponseType(typeof(ApiResponse<long?>), StatusCodes.Status200OK)]
        public async Task<IActionResult> EditMCBandAssocs(int id, [FromBody] List<MedicalContractorBandAssoc> inputMCBandAssocs)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _medicareParticipantsBAL.EditMCBandAssocsBAL(id, inputMCBandAssocs, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                // TODO: will remove once rectify the error
                apiResponse.Errors.Add("Error while Editing MedicalContractorBandAssocs. Please check all variables.");
                _logger.LogError($"Error while Editing MedicalContractorBandAssocs {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }
       
        /// <summary>
        /// Method to Delete MedicalContractorBandAssocs
        /// </summary>
        /// <param name="mcBandassocIds"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("medical_contractor/{id}/medicalcontractorbandassocs")]
        [ProducesResponseType(typeof(ApiResponse<long?>), StatusCodes.Status200OK)]
        public async Task<IActionResult> DeleteMCBandAssocs(int id, [FromQuery] string deleteIds)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _medicareParticipantsBAL.DeleteMCBandAssocsBAL(id, deleteIds, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                // TODO: will remove once rectify the error
                apiResponse.Errors.Add("Error while Delete MedicalContractorBandAssocs. Please check all variables.");
                _logger.LogError($"Error while Delete MedicalContractorBandAssocs {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }

        /// <summary>
        /// Api to Edit Medical Contractor
        /// </summary>
        /// <param name="MedicalContractor"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("medical_contractor/{id}")]
        public async Task<IActionResult> EditMedicalContractor(int id,[FromBody] MedicalContractor inputmedicalContractor)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _medicareParticipantsBAL.EditMedContBAL(id,inputmedicalContractor, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while Editing Medical Contractor.Please check all variables");
                _logger.LogError($"Error while Editing Medical Contractor {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Api to GET Medical Contractor
        /// </summary>
        /// <param name="MedicalContractor"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("medical_contractor/{id}")]
        public async Task<IActionResult> GetMedicalContractorById(int id)
        {
            var apiResponse = new ApiResponse<MedicalContractorView>();
            try
            {
                apiResponse = await _medicareParticipantsBAL.GetMedContByIdBAL(id, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while Getting Medical Contractor.Please check all variables");
                _logger.LogError($"Error while Getting Medical Contractor {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to validate if the Medical Contractor name  already exists
        /// </summary>
        /// <param name="search_term"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("medical_contractor/name/verify")]
        [ProducesResponseType(typeof(ApiResponse<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> MedContNameVerify(string search_term)
        {
            var apiResponse = new ApiResponse<bool>();
            try
            {
                apiResponse = await _medicareParticipantsBAL.CheckMedContName(search_term, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error while verifying the Medical Contractor name.");
                _logger.LogError($"Error while verifying the Medical Contractor name {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }

        /// <summary>
        /// Method to add New medcontpersonaldetails
        /// </summary>
        /// <param name="inputMCPDetails"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("medical_contractor/{id}/medcontpersonaldetails")]
        [ProducesResponseType(typeof(ApiResponse<long?>), StatusCodes.Status200OK)]
        public async Task<IActionResult> AddMCPersonalDetails(int id, [FromBody] List<MedContPersonalDetail> inputMCPDetails)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _medicareParticipantsBAL.AddMCPersonalDetails(id, inputMCPDetails, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                // TODO: will remove once rectify the error
                apiResponse.Errors.Add("Error while creating medcontpersonaldetails. Please check all variables.");
                _logger.LogError($"Error while creating medcontpersonaldetails {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }


        /// <summary>
        /// Method to fetch medcontpersonaldetails based on id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("medical_contractor/{id}/medcontpersonaldetails")]
        public async Task<IActionResult> Getmedcontpersonaldetails(int id)
        {
            var apiResponse = new ApiResponse<List<MedContPersonalDetail>>();
            try
            {
                apiResponse = await _medicareParticipantsBAL.GetMedContPersonalDetails(baseHttpRequestContext, id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting MedContPersonalDetails");
                _logger.LogError($"Error while getting MedContPersonalDetails {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to Edit medcontpersonaldetails
        /// </summary>
        /// <param name="mcPersonalDetails"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("medical_contractor/{id}/medcontpersonaldetails")]
        [ProducesResponseType(typeof(ApiResponse<int?>), StatusCodes.Status200OK)]
        public async Task<IActionResult> EditMedContPersonalDetails(int id, [FromBody] List<MedContPersonalDetail> mcPersonalDetails)
        {
            var apiResponse = new ApiResponse<int?>();
            try
            {
                apiResponse = await _medicareParticipantsBAL.EditMCPersonalDetails(id, mcPersonalDetails, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                // TODO: will remove once rectify the error
                apiResponse.Errors.Add("Error while Editing MedContPersonalDetails. Please check all variables.");
                _logger.LogError($"Error while Editing MedContPersonalDetails {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }

        /// <summary>
        /// Method to Delete medcontpersonaldetails
        /// </summary>
        /// <param name="deleteIds"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("medical_contractor/{id}/medcontpersonaldetails")]
        [ProducesResponseType(typeof(ApiResponse<int?>), StatusCodes.Status200OK)]
        public async Task<IActionResult> DeleteMedContPersonalDetails(int id, [FromQuery] string deleteIds)
        {
            var apiResponse = new ApiResponse<int?>();
            try
            {
                apiResponse = await _medicareParticipantsBAL.DeleteMCPersonalDetails(id, deleteIds, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                // TODO: will remove once rectify the error
                apiResponse.Errors.Add("Error while Delete MedContPersonalDetails. Please check all variables.");
                _logger.LogError($"Error while Delete MedContPersonalDetails {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }


       /* /// <summary>
        /// Method to fetch medicalcontractorbandassocs based on id /participantId/MasterMedicalParticipantsId
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("medical_contractor/medicalcontractorbandassocs")]
        public async Task<IActionResult> GetMedicalContractorBandAssoc([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<List<MedicalContractorBandAssoc>>();
            try
            {
                apiResponse = await _medicareParticipantsBAL.GetMedContBandAssocFromFilter(baseHttpRequestContext, queryModel);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting MedicalContractorBandAssoc");
                _logger.LogError($"Error while getting MedicalContractorBandAssoc {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }*/

        /// <summary>
        /// Method to fetch medicalcontractorbandversions based on id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("medical_contractor/{id}/medicalcontractorbandversions")]
        public async Task<IActionResult> GetMCBandVersion(int id)
        {
            var apiResponse = new ApiResponse<List<MCBandVersionView>>();
            try
            {
                apiResponse = await _medicareParticipantsBAL.GetMedContBandVersionBAL(baseHttpRequestContext, id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting medicalcontractorbandversions");
                _logger.LogError($"Error while getting medicalcontractorbandversions {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to validate startdate and enddate for given MC 
        /// </summary>
        /// <param name="search_term"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("medical_contractor/{id}/versiondates/verify")]
        [ProducesResponseType(typeof(ApiResponse<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> MedContVersionDatesVerify(int id,DateTime startDate,DateTime? endDate)
        {
            var apiResponse = new ApiResponse<bool>();
            try
            {
                apiResponse = await _medicareParticipantsBAL.CheckMCVersionDatesBAL(id, baseHttpRequestContext, startDate,endDate);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error while verifying the Medical Contractor Version Dates.");
                _logger.LogError($"Error while verifying the Medical Contractor Version Dates {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }

        /// <summary>
        /// Method to fetch medicalcontractorbandassocs For Given ParticipantId
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("medical_contractor/medicalcontractorbandassocs")]
        [ProducesResponseType(typeof(ApiResponse<List<MedicalContractorBandAssoc>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetMCBandAssocByParticipantId([FromQuery]QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<List<MedicalContractorBandAssoc>>();
            try
            {
                apiResponse = await _medicareParticipantsBAL.GetMCBandAssocByPaticipantId(baseHttpRequestContext, queryModel);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting MedicalContractorBandAssoc");
                _logger.LogError($"Error while getting MedicalContractorBandAssoc {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
        /// <summary>
        /// Method to fetch medicalcontractorbandassocs for a list of ids 
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("medical_contractor/medicalcontractorbandassocsinfo")]
        [ProducesResponseType(typeof(ApiResponse<List<MedicalContractorBandAssocInfo>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ListMCBandAssocByIds([FromQuery] string Ids)
        {
            var apiResponse = new ApiResponse<List<MedicalContractorBandAssocInfo>>();
            try
            {
                apiResponse = await _medicareParticipantsBAL.ListMCBandAssocByIds(baseHttpRequestContext, Ids);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting MedicalContractorBandAssoc");
                _logger.LogError($"Error while getting MedicalContractorBandAssoc {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to validate if the Medical Contractor name  already exists
        /// </summary>
        /// <param name="search_term"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("medical_contractor/companydetailsid/verify")]
        [ProducesResponseType(typeof(ApiResponse<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> MedContCompanyVerify(int search_term)
        {
            var apiResponse = new ApiResponse<bool>();
            try
            {
                apiResponse = await _medicareParticipantsBAL.CheckMedContCompany(search_term, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error while verifying the Medical Contractor Company Id.");
                _logger.LogError($"Error while verifying the Medical Contractor Company Id {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }

        /// <summary>
        /// Method to Fetch address from MC based on participantid
        /// </summary>
        /// <param name="search_term"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("medical_contractor/medical_contractor_address")]
        [ProducesResponseType(typeof(ApiResponse<MedicalContractorInfo>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetMedicalContractorAddress([FromQuery] string strParticipantId)
        {
            var apiResponse = new ApiResponse<MedicalContractorInfo>();
            try
            {
                apiResponse = await _medicareParticipantsBAL.GetMedicalContractorAddress(strParticipantId, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error while fetching MC Address.");
                _logger.LogError($"Error while fetching MC Address.{Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }
    }


}
