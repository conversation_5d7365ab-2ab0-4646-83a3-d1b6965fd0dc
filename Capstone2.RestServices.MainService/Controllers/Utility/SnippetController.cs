﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.Utility.Interfaces;
using Capstone2.RestServices.Utility.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Utility.Controllers
{
    [ApiController]
    [Route("utility")]
  //  [Authorize]
    [ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class SnippetController : BaseController
    {
        private readonly ILogger<SnippetController> _logger;
        private readonly ISnippetBAL _snippetBAL;

        public SnippetController(ILogger<SnippetController> logger, ISnippetBAL snippetBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _snippetBAL = snippetBAL;
        }

        /// <summary>
        /// Api to add a new Snippet
        /// </summary>
        /// <param name="inputSnippetDetail"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("snippet_details")]
        public async Task<IActionResult> AddSnippetDetails([FromBody] SnippetDetail inputSnippetDetail)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _snippetBAL.AddSnippetDetails(inputSnippetDetail, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while creating Snippet.Please check all variables");
                _logger.LogError($"Error while creating Snippet {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }

        /// <summary>
        /// Method to validate if the snippet shortcut  already exists
        /// </summary>
        /// <param name="search_term"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("snippet_details/shortcut/verify")]
        public async Task<IActionResult> CheckSnippetShortcut(string search_term)
        {
            var apiResponse = new ApiResponse<bool>();
            try
            {
                apiResponse = await _snippetBAL.CheckSnippetShortcut(search_term, baseHttpRequestContext.OrgId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error while verifying the Snippet shortcut");
                _logger.LogError($"Error while verifying the Snippet shortcut {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }

        /// <summary>
        /// Method to validate if the snippet shortcut  already exists
        /// </summary>
        /// <param name="search_term"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("snippet_details/name/verify")]
        public async Task<IActionResult> CheckSnippetName(string search_term)
        {
            var apiResponse = new ApiResponse<bool>();
            try
            {
                apiResponse = await _snippetBAL.CheckSnippetName(search_term, baseHttpRequestContext.OrgId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error while verifying the Snippet Name");
                _logger.LogError($"Error while verifying the Snippet Name {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }

        /// <summary>
        /// Method to fetch snippets
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("snippet_details")]
        public async Task<IActionResult> ListSnippets([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<QueryResultList<SnippetDetail>>();
            try
            {
                apiResponse = await _snippetBAL.ListSnippets(baseHttpRequestContext, queryModel);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of Snippets could not be fetched during this time.");
                _logger.LogError($"List of Snippets could not be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to fetch a snippet based on id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("snippet_details/{id}")]
        public async Task<IActionResult> GetSnippetDetails(long id)
        {
            var apiResponse = new ApiResponse<InputSnippetDetail>();
            try
            {
                apiResponse = await _snippetBAL.GetSnippetDetails(baseHttpRequestContext, id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting Snippet Details");
                _logger.LogError($"Error while getting Snippet Details {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to delete a snippet based on id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("snippet_details/{id}")]
        public async Task<IActionResult> DeleteSnippet(long id, [FromQuery] DeleteObject deleteObject)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _snippetBAL.DeleteSnippet(baseHttpRequestContext, id, deleteObject);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while deleting Snippet Details");
                _logger.LogError($"Error while deleting Snippet Details {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to update a snippet
        /// </summary>
        /// <param name="inputSnippetDetail"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("snippet_details/{id}")]
        public async Task<IActionResult> EditSnippetDetails(long id, [FromBody] SnippetDetail inputSnippetDetail)
        {
            var apiResponse = new ApiResponse<string>();
            try
            {
                apiResponse = await _snippetBAL.EditSnippetDetails(id, inputSnippetDetail, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Snippet cannot be Edited.Please try again later.");
                _logger.LogError($"Snippet cannot be Edited {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

    }
}
