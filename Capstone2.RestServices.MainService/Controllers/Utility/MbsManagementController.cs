﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.Utility.Interfaces;
using Capstone2.RestServices.Utility.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AssignCategoryInput = Capstone2.Shared.Models.Entities.AssignCategoryInput;
using ListMbsData = Capstone2.Shared.Models.Entities.ListMbsData;
using MbsDataView = Capstone2.Shared.Models.Entities.MbsDataView;
using MbsItemDetail = Capstone2.Shared.Models.Entities.MbsItemDetail;

namespace Capstone2.RestServices.Utility.Controllers
{
    [ApiController]
    [Route("utility")]
    //[Authorize]
    [ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class MbsManagementController : BaseController
    {
        private readonly ILogger<RolesController> _logger;
        private readonly IMbsBAL _mbsBAL;
        public MbsManagementController(ILogger<RolesController> logger, IMbsBAL mbsBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _mbsBAL = mbsBAL;
        }


        [HttpPost]
        [Route("mbs_itemdetails")]
        [ProducesResponseType(typeof(ApiResponse<long>), StatusCodes.Status200OK)]
        public async Task<IActionResult> AddMbsItem([FromBody] MbsItemDetail mbsItemDetail)
        {
            var apiResponse = new ApiResponse<long>();

            try
            {

                apiResponse = await _mbsBAL.AddMbsItem(mbsItemDetail, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = default(short);
                apiResponse.Errors.Add("New Mbs Item cannot be added at this time.");
                _logger.LogError($"New Mbs Item cannot be added {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


        /// <summary>
        /// Method to retireve list of mbs data 
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("mbs_data")]
        [ProducesResponseType(typeof(ApiResponse<QueryResultList<ListMbsData>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ListMbsData([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<QueryResultList<ListMbsData>>();

            try
            {

                apiResponse = await _mbsBAL.ListMbsDataBAL(queryModel, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of Episode items cannot be retrieved at this time.");
                _logger.LogError($"List of Episode items cannot be retrieved {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to retireve list of Clinical categories
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("clinical_categories")]
        [ProducesResponseType(typeof(ApiResponse<QueryResultList<MbsCategoryList>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ListClinicalCategories([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<QueryResultList<MbsCategoryList>>();
            try
            {

                apiResponse = await _mbsBAL.ListClinicalCategories(queryModel, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Can not fetch the list of clinical categories at this time.");
                _logger.LogError($"Can not fetch the list of clinical categories {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to edit Mbs item based on id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("mbs_itemdetails/{id}")]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
        public async Task<IActionResult> EditMbsItemDetails([FromBody] MbsItemDetail mbsItemDetail, int id)
        {
            var apiResponse = new ApiResponse<string>();

            try
            {

                apiResponse = await _mbsBAL.EditMbsItemDetailsBAL(id, mbsItemDetail, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Mbs item cannot be edited at this time.");
                _logger.LogError($"Mbs item cannot be edited at this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to fetch Mbs item based on itemnum
        /// </summary>
        /// <param name="itemnum"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("mbs_data/{itemnum}")]
        [ProducesResponseType(typeof(ApiResponse<MbsDataView>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetEpisodeItemDetails(long itemnum)
        {
            var apiResponse = new ApiResponse<MbsDataView>();

            try
            {

                apiResponse = await _mbsBAL.GetMbsDataBAL(itemnum, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Mbs Data cannot be fetched at this time.");
                _logger.LogError($"Mbs Data cannot be fetched at this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpPost]
        [Route("mbs_itemdetails/assigncategory/{itemnum}")]
        [ProducesResponseType(typeof(ApiResponse<long>), StatusCodes.Status200OK)]
        public async Task<IActionResult> AssignCategoryMbsItem(long itemnum,[FromBody] AssignCategoryInput mbsItemDetail)
        {
            var apiResponse = new ApiResponse<string>();

            try
            {

                apiResponse = await _mbsBAL.AssignCategoryBAL(itemnum,mbsItemDetail, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Mbs itemnum cannot be assigned to category");
                _logger.LogError($"Mbs itemnum cannot be assigned to category {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpPost]
        [Route("mbs_clinicalcategories/updatestatus/{categoryid}")]

        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
        public async Task<IActionResult> UpdateMbsCategory(short categoryid, [FromBody] MbsClinicalCategoryInput mbsInput)
        {
            var apiResponse = new ApiResponse<string>();

            try
            {

                apiResponse = await _mbsBAL.UpdateMbsCategoryBAL(categoryid, mbsInput, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Mbs category  cannot be assigned to category");
                _logger.LogError($"Mbs category  cannot be assigned to category {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }



    }
}

