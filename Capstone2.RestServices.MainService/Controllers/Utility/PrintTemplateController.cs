﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.Utility.Interfaces;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Utility.Controllers
{
    [ApiController]
    [Route("utility")]
    [ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class PrintTemplateController : BaseController
    {
        private readonly ILogger<PrintTemplateController> _logger;
        private readonly IPrintTemplateBAL _printTemplateBAL;

        public PrintTemplateController(ILogger<PrintTemplateController> logger, IPrintTemplateBAL printTemplateBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _printTemplateBAL = printTemplateBAL;
        }

        //Names starts with Is For checkboxes in the printtemplates
        /// <summary>
        /// Api to add a new PrintTemplate
        /// </summary>
        /// <param name="inputPrintTemplate"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("print_template")]
        [ProducesResponseType(typeof(ApiResponse<long?>), StatusCodes.Status200OK)]
        public async Task<IActionResult> AddPrintTemplate([FromBody] PrintTemplate inputPrintTemplate)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _printTemplateBAL.AddPrintTemplate(baseHttpRequestContext, inputPrintTemplate);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while creating PrintTemplate.Please check all variables");
                _logger.LogError($"Error while creating PrintTemplate {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to fetch PrintTemplate
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("print_template")]
        [ProducesResponseType(typeof(ApiResponse<QueryResultList<PrintTemplateList>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ListPrintTemplate([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<QueryResultList<PrintTemplateList>>();
            try
            {
                apiResponse = await _printTemplateBAL.ListPrintTemplate(baseHttpRequestContext, queryModel);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of PrintTemplate could not be fetched during this time.");
                _logger.LogError($"List of PrintTemplate could not be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to fetch a PrintTemplate based on id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("print_template/{id}")]
        [ProducesResponseType(typeof(ApiResponse<PrintTemplateView>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetPrintTemplate(long id)
        {
            var apiResponse = new ApiResponse<PrintTemplateView>();
            try
            {
                apiResponse = await _printTemplateBAL.GetPrintTemplate(baseHttpRequestContext, id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting PrintTemplate");
                _logger.LogError($"Error while getting PrintTemplate {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to update a PrintTemplate
        /// </summary>
        /// <param name="inputPrintTemplate"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("print_template/{id}")]
        [ProducesResponseType(typeof(ApiResponse<long?>), StatusCodes.Status200OK)]
        public async Task<IActionResult> EditPrintTemplate(long id, [FromBody] PrintTemplate inputPrintTemplate)
        {
            var apiResponse = new ApiResponse<long?>();

            try
            {
                apiResponse = await _printTemplateBAL.EditPrintTemplate(baseHttpRequestContext, id, inputPrintTemplate);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("PrintTemplate cannot be Edited.Please try again later.");
                _logger.LogError($"PrintTemplate cannot be Edited {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to validate if the Appointment Type  already exists
        /// </summary>
        /// <param name="search_term"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("print_template/name/verify")]
        [ProducesResponseType(typeof(ApiResponse<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> CheckPrintTemplateName(string search_term)
        {
            var apiResponse = new ApiResponse<bool>();

            try
            {
                apiResponse = await _printTemplateBAL.CheckPrintTemplateName(baseHttpRequestContext, search_term);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error while verifying the Print Template Name");
                _logger.LogError($"Error while verifying the Print Template Name {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
    }
}
