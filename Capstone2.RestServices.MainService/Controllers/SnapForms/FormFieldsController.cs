﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.MainService.Utility;
using Capstone2.RestServices.Snapforms.Interfaces;
using Capstone2.RestServices.Snapforms.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Snapforms.Controllers
{
    [Route("snapforms")]
    [ApiController]
    [ApiExceptionFilter]
    [Authorize]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class FormFieldsController : BaseController
    {
        private readonly ILogger<FormFieldsController> _logger;
        private readonly IFormFieldsBAL _formFieldsBAL;
        public FormFieldsController(IOptions<MvcOptions> options, ILogger<FormFieldsController> logger, IFormFieldsBAL formFieldsBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _formFieldsBAL = formFieldsBAL;
            options.Value.InputFormatters.Insert(0, InputFormater.GetInputFormater());
        }

        /// <summary>
        /// Search for form fields
        /// </summary>
        /// <returns>Returns a list of Forms fileds</returns>
        [HttpGet]
        [Route("form")]
        [ProducesResponseType(typeof(List<FormFieldDetails>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetAllForms()
        {
            var apiResponse = new ApiResponse<List<FormFieldDetails>>();
            try
            {
                apiResponse = await _formFieldsBAL.GetAllForms(baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Snapforms fields cannot be fetched at this time.");
                _logger.LogError($"Snapforms fields cannot be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Search request by slug
        /// </summary>
        /// <param name="slug">slug</param>
        /// <returns>Return form fields  - type, name, label and required</returns>
        [HttpGet]
        [Route("form/{slug}/fields")]
        [ProducesResponseType(typeof(List<ViewFormFields>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetViewFormFields(string slug)
        {
            var apiResponse = new ApiResponse<List<ViewFormFields>>();
            try
            {
                apiResponse = await _formFieldsBAL.GetViewFormFields(slug, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Snapforms ViewFormFields cannot be fetched at this time.");
                _logger.LogError($"Snapforms ViewFormFields cannot be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Search for Form fields
        /// </summary>
        /// <param name="slug">slug</param>
        /// <param name="queryModel">paramsRequest</param>
        /// <returns>Returns a list of Forms fileds matching the given search criteria</returns>
        [HttpGet]
        [Route("form/{slug}/responses")]
        [ProducesResponseType(typeof(SearchResponse), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetSearchResponse(string slug, [FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<SearchResponse>();
            try
            {
                apiResponse = await _formFieldsBAL.GetSearchResponse(slug, queryModel, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Snapforms SearchResponse cannot be fetched at this time.");
                _logger.LogError($"Snapforms SearchResponse cannot be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Search request by slug and response_id
        /// </summary>
        /// <param name="slug">Replace {{slug}} with your unique Form identifier</param>
        /// <param name="responseId">Replace {{response_id}} with the internal Response identifier</param>
        /// <returns>Retrieve a Form Response using a particular response identifier</returns>
        [HttpGet]
        [Route("form/{slug}/responses/{response_id}")]
        [ProducesResponseType(typeof(SingleResponse), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetSingleResponse(string slug, string response_id)
        {
            var apiResponse = new ApiResponse<SingleResponse>();
            try
            {
                apiResponse = await _formFieldsBAL.GetSingleResponse(slug, response_id, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Snapforms SingleResponse cannot be fetched at this time.");
                _logger.LogError($"Snapforms SingleResponse cannot be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// POST forms by slug with payload 
        /// </summary>
        /// <param name="slug">slug</param>
        /// <returns></returns>
        [HttpPost]
        [Route("form/{slug}/responses")]
        [ProducesResponseType(typeof(Response), StatusCodes.Status200OK)]
        public async Task<IActionResult> PostSingleResponse(string slug, [FromBody] FormDataWrapper queryModel)
        {
            var apiResponse = new ApiResponse<Response>();
            try
            {
                apiResponse = await _formFieldsBAL.PostSingleResponse(slug, queryModel, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Snapforms PostSingleResponse cannot be fetched at this time.");
                _logger.LogError($"Snapforms PostSingleResponse cannot be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


        /// <summary>
        /// Delete forms by slug and response_id
        /// </summary>
        /// <param name="slug">Replace {{slug}} with your unique Form identifier</param>
        /// <param name="responseId">Replace {{response_id}} with the internal Response identifier</param>
        /// <returns></returns>
        [HttpDelete]
        [Route("form/{slug}/responses/{response_id}")]
        [ProducesResponseType(typeof(SingleResponse), StatusCodes.Status200OK)]
        public async Task<IActionResult> DeleteSingleResponse(string slug, string response_id)
        {
            var apiResponse = new ApiResponse<Response>();
            try
            {
                apiResponse = await _formFieldsBAL.DeleteSingleResponse(slug, response_id, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Snapforms DeleteSingleResponse cannot be fetched at this time.");
                _logger.LogError($"Snapforms DeleteSingleResponse cannot be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Search request by slug and response_id
        /// </summary>
        /// <param name="slug">Replace {{slug}} with your unique Form identifier</param>
        /// <param name="responseId">Replace {{response_id}} with the internal Response identifier</param>
        /// <returns>Retrieve a temporary PDF url that can be used to download the PDF with the response</returns>
        [HttpGet]
        [Route("form/{slug}/responses/{response_id}/pdf-url")]
        [ProducesResponseType(typeof(PDFUrl), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetPDFURL(string slug, string response_id, bool isFile = false)
        {
            var apiResponse = new ApiResponse<PDFUrl>();
            try
            {
                apiResponse = await _formFieldsBAL.GetPDFURL(slug, response_id, isFile, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Snapforms PDFUrl cannot be fetched at this time.");
                _logger.LogError($"Snapforms PDFUrl cannot be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// View logs by from_date and to_date
        /// </summary>
        /// <param name="queryModel">queryModel</param>
        /// <returns></returns>
        [HttpGet]
        [Route("logs")]
        [ProducesResponseType(typeof(ViewLogs), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetViewLogs([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<ViewLogs>();
            try
            {
                apiResponse = await _formFieldsBAL.GetViewLogs(queryModel, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Snapforms ViewLogs cannot be fetched at this time.");
                _logger.LogError($"Snapforms ViewLogs cannot be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpGet("healthcheck")]
        [Microsoft.AspNetCore.Authorization.AllowAnonymous]
        [TelemetryTracing(logRequest: false, logResponse: false)]
        public IActionResult HealthCheck()
        {
            return Ok(StatusCodes.Status200OK);
        }

    }
}
