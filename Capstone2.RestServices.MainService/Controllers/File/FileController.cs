﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.File.Interfaces;
using Capstone2.RestServices.File.Models;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Capstone2.RestServices.File.Controller
{
    [ApiController]
    [Route("file")]
    [ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: false)]
    public class FileController : BaseController
    {
        private readonly ILogger<FileController> _logger;
        private readonly IFileBAL _fileBAL;

        public FileController(ILogger<FileController> logger, IFileBAL fileBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _fileBAL = fileBAL;
        }

        [HttpPost]
        [Route("file_details")]
        [ProducesResponseType(typeof(ApiResponse<Capstone2.RestServices.File.Models.FileDetailsOutputForId>), StatusCodes.Status200OK)]
        public async Task<IActionResult> PostFileDetails([FromBody] FileTypeInput fileTypeInput)
        {
            var apiResponse = new ApiResponse<Capstone2.RestServices.File.Models.FileDetailsOutputForId>();
            try
            {
                apiResponse = await _fileBAL.PostFileDetailsBAL(baseHttpRequestContext, fileTypeInput.FileModuleTypeId, fileTypeInput.FileEnumId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    StatusCodes.Status403Forbidden => new ObjectResult(apiResponse) { StatusCode = StatusCodes.Status403Forbidden },
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while posting the File Data.");
                _logger.LogError($"Error while posting the File Data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpGet]
        [Route("file_details/{id}")]
        [ProducesResponseType(typeof(ApiResponse<Capstone2.RestServices.File.Models.FileDetailsOutputForId>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetFileDetails(long id)
        {
            var apiResponse = new ApiResponse<Capstone2.RestServices.File.Models.FileDetailsOutputForId>();
            try
            {
                apiResponse = await _fileBAL.GetFileDetailsBAL(baseHttpRequestContext, id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    StatusCodes.Status403Forbidden => new ObjectResult(apiResponse) { StatusCode = StatusCodes.Status403Forbidden },
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting File Data");
                _logger.LogError($"Error while getting File Data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpPut]
        [Route("file_details/{id}")]
        [ProducesResponseType(typeof(ApiResponse<Capstone2.RestServices.File.Models.FileDetailsOutputForId>), StatusCodes.Status200OK)]
        public async Task<IActionResult> PutFileDetails(long id, [FromBody] Capstone2.RestServices.File.Models.FileDetails filedetails)
        {
            var apiResponse = new ApiResponse<Capstone2.RestServices.File.Models.FileDetailsOutputForId>();
            try
            {
                apiResponse = await _fileBAL.PutFileDetailsBAL(baseHttpRequestContext, id, filedetails, baseHttpRequestContext.UserId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    StatusCodes.Status403Forbidden => new ObjectResult(apiResponse) { StatusCode = StatusCodes.Status403Forbidden },
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while Editing File Data");
                _logger.LogError($"Error while Editing File Data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpDelete]
        [Route("file_details/{id}")]
        [ProducesResponseType(typeof(ApiResponse<Capstone2.RestServices.File.Models.FileDetailsOutputForId>), StatusCodes.Status200OK)]
        public async Task<IActionResult> DeleteFileDetails(long id)
        {
            var apiResponse = new ApiResponse<Capstone2.RestServices.File.Models.FileDetailsOutputForId>();
            try
            {
                apiResponse = await _fileBAL.DeleteFileDetailsBAL(baseHttpRequestContext, id, baseHttpRequestContext.UserId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    StatusCodes.Status403Forbidden => new ObjectResult(apiResponse) { StatusCode = StatusCodes.Status403Forbidden },
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while deleting file.");
                _logger.LogError($"Error while deleting file {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpGet]
        [Route("file_details_list")]
        [ProducesResponseType(typeof(ApiResponse<Capstone2.RestServices.File.Models.FileDetailsOutputForId>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetFileDetails([FromQuery] string stringFileDetailsId,int sasTokenExpiry=0)
        {
            var apiResponse = new ApiResponse<List<Capstone2.RestServices.File.Models.FileDetailsOutputForId>>();
            try
            {
                apiResponse = await _fileBAL.ListGetFileDetails(baseHttpRequestContext, stringFileDetailsId, sasTokenExpiry);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    StatusCodes.Status403Forbidden => new ObjectResult(apiResponse) { StatusCode = StatusCodes.Status403Forbidden },
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting list of fileDetails");
                _logger.LogError($"Error while getting list of fileDetails {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
        [HttpDelete]
        [Route("file_details_list")]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
        public async Task<IActionResult> DeleteBulkFileDetails([FromQuery] string stringFileDetailsId)
        {
            var apiResponse = new ApiResponse<string>();
            try
            {
                apiResponse = await _fileBAL.DeleteBulkFileDetails(baseHttpRequestContext, stringFileDetailsId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    StatusCodes.Status403Forbidden => new ObjectResult(apiResponse) { StatusCode = StatusCodes.Status403Forbidden },
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while deleting files");
                _logger.LogError($"Error while deleting files {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpPost]
        [Route("fileupload")]
        [ProducesResponseType(typeof(ApiResponse<long>), StatusCodes.Status200OK)]
        public async Task<IActionResult> PostFile([FromBody] FileUploadObject fileUpload)
        {
            var apiResponse = new ApiResponse<long>();
            try
            {
                apiResponse = await _fileBAL.PostFileBAL(baseHttpRequestContext, fileUpload);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error while uploading the file");
                _logger.LogError($"Error while uploading the file {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpGet]
        [Route("filedownload")]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
        public async Task<IActionResult> DownloadFile(long Id)
        {
            var apiResponse = new ApiResponse<string>();
            try
            {
                apiResponse = await _fileBAL.DownloadFile(baseHttpRequestContext, Id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error while downloading the file");
                _logger.LogError($"Error while downloading the file {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpGet("healthcheck")]
        [AllowAnonymous]
        [TelemetryTracing(logRequest: false, logResponse: false)]
        public IActionResult HealthCheck()
        {
            return Ok(StatusCodes.Status200OK);
        }

        [HttpPost]
        [Route("filetransfer/{filedetailsId}")]
        [ProducesResponseType(typeof(ApiResponse<long>), StatusCodes.Status200OK)]
        public async Task<IActionResult> TransferFile(long filedetailsId)
        {
            var apiResponse = new ApiResponse<long>();
            try
            {
                apiResponse = await _fileBAL.TransferFileBAL(baseHttpRequestContext, filedetailsId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error while Transfering the file");
                _logger.LogError($"Error while Transfering the file {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        // this is allowed only for patientRegtemp folder 
    /*      [HttpDelete]
        [Route("file_details/{filedetailsId}")]
        [ProducesResponseType(typeof(ApiResponse<long>), StatusCodes.Status200OK)]
        public async Task<IActionResult> DeleteTempFile(long filedetailsId)
        {
            var apiResponse = new ApiResponse<long>();
            try
            {
                apiResponse = await _fileBAL.DeleteTempFileBAL(baseHttpRequestContext, filedetailsId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error while Deleting the file");
                _logger.LogError($"Error while Deleting the file {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }*/
    }
}
