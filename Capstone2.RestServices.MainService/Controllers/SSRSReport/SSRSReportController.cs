﻿//using BoldReports.Models.ReportViewer;
using Azure.Storage;
using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Azure.Storage.Blobs.Specialized;
using Azure.Storage.Sas;
using BoldReports.Web;
using BoldReports.Web.ReportViewer;
using BoldReports.Writer;
//using BoldReports.Web.ReportViewer;
using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.SSRSReport.Interfaces;
using Capstone2.RestServices.SSRSReport.Model;
//using Capstone2.RestServices.Report.Models;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Xml.Linq;
using System.Xml.Serialization;
using AuthorizeAttribute = Capstone2.Framework.RestApi.Filters.AuthorizeAttribute;
//using BoldReports.Web.ReportViewer;
//using BoldReports.Writer;
//using BoldReportsCore.Models;
//using BoldReports.Web;


namespace Capstone2.RestServices.SSRSReport.Controllers
{

    //[ApiController]
    [Route("ssrsreport")]
    [Authorize] 
    [ApiExceptionFilter]
    //[TelemetryTracing(logRequest: true, logResponse: true)]
    public class SSRSReportController : BaseController, IReportController, IReportHelperSettings, IReportLogger
    {
        private readonly ILogger<SSRSReportController> _logger;
        private ISSRSReportBAL _iSSRSReportBAL;
        Dictionary<string, object> jsonArray = null;
        public readonly AppSettings _appSettings;
        private Microsoft.Extensions.Caching.Memory.IMemoryCache _cache;
       // private Microsoft.AspNetCore.Hosting.IWebHostEnvironment _hostingEnvironment;
        public IConfiguration Configuration { get; }
        public SSRSReportController(ILogger<SSRSReportController> logger, IHttpContextAccessor httpContextAccessor, ISSRSReportBAL iSSRSReportBAL, Microsoft.Extensions.Caching.Memory.IMemoryCache memoryCache, Microsoft.AspNetCore.Hosting.IWebHostEnvironment hostingEnvironment, IOptions<AppSettings> appSettings, IConfiguration configuration) : base(httpContextAccessor)
        {
            _logger = logger;
            _iSSRSReportBAL = iSSRSReportBAL;
            _cache = memoryCache;
            _appSettings = appSettings.Value;
            Configuration = configuration;
        }

        [HttpGet("healthcheck")]
        [AllowAnonymous]
        [TelemetryTracing(logRequest: false, logResponse: false)]
        public IActionResult HealthCheck()
        {
            return Ok(StatusCodes.Status200OK);
        }

        //[HttpPost("GetReports")]
        //public object GetReports()
        //{
        //    //ReportModel reportModel = new ReportModel(_hostingEnvironment.WebRootPath + "/Resources/Report");
        //    ReportModel reportModel = new ReportModel("https://capstone2dev2storageacc.blob.core.windows.net/capstonereports");
        //    return reportModel.GetReports();
        //}

        [Route("GetResource")]
        [AcceptVerbs("GET")]

        public object GetResource(ReportResource resource)
        {
            //return ReportHelper.GetResource(resource, this, _cache);
            return null;
          
        }

        [NonAction]
        public object GetResource(string key, string resourcetype, bool isPrint)
        {
            // Implement the method to satisfy the IReportController interface
            // This is a placeholder implementation, you should replace it with actual logic
            return null;
        }


        [NonAction]
        public void OnInitReportOptions(ReportViewerOptions reportOption)
        {
            try
            {
                _logger.LogError("OnInitReportOptions: ");
                if (reportOption.ReportModel.IsDrillthroughReport)
                    return;

                reportOption.ReportModel.ProcessingMode = BoldReports.Web.ReportViewer.ProcessingMode.Local;
                //reportOption.ReportModel.EnableVirtualEvaluation = false;
                _logger.LogError("OnInitReportOptions => jsonArray: " + JsonConvert.SerializeObject(jsonArray));
                var isParameters = jsonArray.ContainsKey("parameters");
                var parameter1 = jsonArray["parameters"].ToString();
                var parameters = JsonConvert.DeserializeObject<List<ReportParameter>>(parameter1);
                ReportRequestFilter reportRequestFilter = new ReportRequestFilter();
                reportRequestFilter.orgId = baseHttpRequestContext.OrgId;
                reportRequestFilter.userId = baseHttpRequestContext.UserId;
                reportRequestFilter.reportId = int.Parse(parameters.Where(param => param.Name == "ReportId").FirstOrDefault().Values[0]);
                _logger.LogError("OnInitReportOptions => reportRequestFilter: " + JsonConvert.SerializeObject(reportRequestFilter));
                string fileName = _iSSRSReportBAL.GetReportNameByReportId(reportRequestFilter);
                _logger.LogError("OnInitReportOptions => fileName: " + fileName);
                _logger.LogError("BlobStorageURL: " + Configuration["AppSettings:BlobStorageURL"]);
                reportOption.ReportModel.Stream = ReadFiles(Configuration["AppSettings:BlobStorageURL"] +fileName);
                _logger.LogError("Stream: ");
            }
            catch(Exception)
            {

                throw;
            }
        }


        [NonAction]
        public void OnReportLoaded(ReportViewerOptions reportOption)
        {
            try
            {
              //  _logger.LogError("OnReportLoaded: " + JsonConvert.SerializeObject(jsonArray));
                if (reportOption.ReportModel.PDFOptions == null)
                {
                    reportOption.ReportModel.PDFOptions = new PDFOptions();
                }
                reportOption.ReportModel.DataSources.Clear();
              //  _logger.LogError("OnReportLoaded => jsonArray: " + JsonConvert.SerializeObject(jsonArray));
                var isParameters = jsonArray.ContainsKey("parameters");
                var parameter1 = jsonArray["parameters"].ToString();
                var parameters = JsonConvert.DeserializeObject<List<ReportParameter>>(parameter1);
                ReportRequestFilter reportRequestFilter = new ReportRequestFilter();
                reportRequestFilter.orgId = baseHttpRequestContext.OrgId;
                reportRequestFilter.userId = baseHttpRequestContext.UserId;
                reportRequestFilter.reportId = int.Parse(parameters.Where(param => param.Name == "ReportId").FirstOrDefault().Values[0]);
                //_logger.LogError("OnReportLoaded => reportRequestFilter: " + JsonConvert.SerializeObject(reportRequestFilter));
                reportOption.ReportModel.DataSources = _iSSRSReportBAL.ListAppointmentDayDetails(reportRequestFilter).Result;
                // _logger.LogError("OnReportLoaded => DataSources: " + JsonConvert.SerializeObject(reportOption.ReportModel.DataSources));
            }
            catch (Exception Ex)
            {
                _logger.LogError($"OnReportLoaded Error {Ex.Message} {Ex.StackTrace}");
                throw;
            }

        }

        [HttpPost]
        [Route("addReportFilter")]
        public async Task<IActionResult> AddReportFilter([FromBody] UserReportFilter userReportFilter)
        {
            var apiResponse = new ApiResponse<long>();
            try
            {
                _logger.LogWarning("AddReportFilter Started: " + JsonConvert.SerializeObject(userReportFilter));
                _logger.LogError("AddReportFilter Started: " + JsonConvert.SerializeObject(userReportFilter));
                userReportFilter.UserId = baseHttpRequestContext.UserId;
                userReportFilter.CreatedDate = DateTime.Now;
                userReportFilter.ModifiedDate = DateTime.Now;
                _logger.LogError("AddReportFilter Started 1: " + JsonConvert.SerializeObject(userReportFilter));
                apiResponse = await _iSSRSReportBAL.AddReportFilter(userReportFilter);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                _logger.LogError("AddReportFilter Started 1: " + JsonConvert.SerializeObject(userReportFilter));
                _logger.LogWarning("AddReportFilter Exception : " + Ex.ToString());
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error while add report filter.Please check all variables.");
                return BadRequest(apiResponse);
            }

        }
        [HttpGet]
        [Route("Export")]
        public IActionResult Export(string name)
        {
            BoldReports.Writer.ReportWriter writer = new BoldReports.Writer.ReportWriter();
            //writer.ReportingServer = this.Server;
            writer.ReportPath = name;
            MemoryStream memoryStream = new MemoryStream();
            writer.Save(memoryStream, WriterFormat.PDF);
            memoryStream.Position = 0;
            // Download the generated export document to the client side.
            memoryStream.Position = 0;
            FileStreamResult fileStreamResult = new FileStreamResult(memoryStream, "application/pdf");
            fileStreamResult.FileDownloadName = name + ".pdf";
            memoryStream.Dispose();
            return fileStreamResult;
        }


        [HttpPost]
        [Route("PostReportAction")]
        [TelemetryTracing(logRequest: false, logResponse: false)]
        public object PostReportAction([FromBody] Dictionary<string, object> jsonResult)
        {
            object report = null;
            try
            {

                jsonArray = jsonResult;
                report = ReportHelper.ProcessReport(jsonResult, this, _cache);
            }
            catch (Exception Ex)
            {
               
                _logger.LogError($"PostReportAction Error {Ex.Message} {Ex.StackTrace}");
                throw;
            }

            return report;

        }

        [HttpPost]
        [Route("PostFormReportAction")]
        [AllowAnonymous]
        public object PostFormReportAction()
        {
            return ReportHelper.ProcessReport(null, this, _cache);
        }

        [NonAction]
        public void InitializeSettings(ReportHelperSettings helperSettings)
        {
            //helperSettings.ReportingServer = this.Server;
            //  throw new NotImplementedException();
        }

        private Stream ReadFiles(string filePath)
        {
            //StorageSharedKeyCredential storagekey = new StorageSharedKeyCredential("capstone2dev2storageacc", "7MriVFsBb592jZ+95rTtvGlz61RXhppD5yF4r3vKb4TXdzXC5rHKrTvQ/kzMIR5TcDILzhJxz2dMAZk280FRFw==");
            _logger.LogError("StorageAccountName: " + Configuration["AppSettings:StorageAccountName"]);
            _logger.LogError("StorageAccountKey: " + Configuration["AppSettings:StorageAccountKey"]);
            StorageSharedKeyCredential storagekey = new StorageSharedKeyCredential(Configuration["AppSettings:StorageAccountName"], Configuration["AppSettings:StorageAccountKey"]);
            _logger.LogError("filePath: " + filePath);
            Uri bloburi = new Uri(filePath);
            _logger.LogError("bloburi: " + JsonConvert.SerializeObject(bloburi));
            BlobClient newblob = new BlobClient(bloburi, storagekey);
            var fileSasUrl = GetServiceSasUriForBlob(newblob, 3);
            _logger.LogError("fileSasUrl: " + JsonConvert.SerializeObject(fileSasUrl));
            //Uri bloburi = fileSasUrl;
            BlobClient newblob1 = new BlobClient(fileSasUrl);
            var downloadResult = newblob1.DownloadStreaming();

            MemoryStream memStream = new MemoryStream();
            memStream.SetLength(downloadResult.Value.Details.ContentLength);

            downloadResult.Value.Content.CopyTo(memStream);
            memStream.Position = 0;
            //downloadResult.Value.Content.Read(memStream.GetBuffer(), 0, (int)downloadResult.Value.Details.ContentLength);
            //memStream.CopyTo(downloadResult.Value.Content.CopyTo());
            return memStream;
        }

        private static Uri GetServiceSasUriForBlob(BlobClient blobClient, int sasTokenExpiryTime, string storedPolicyName = null)
        {
            // Create a SAS token that's valid for one hour.
            BlobSasBuilder sasBuilder = new BlobSasBuilder()
            {
                BlobContainerName = blobClient.GetParentBlobContainerClient().Name,
                BlobName = blobClient.Name,
                Resource = "b"
            };

            if (storedPolicyName == null)
            {
                sasBuilder.ExpiresOn = DateTimeOffset.UtcNow.AddMinutes(sasTokenExpiryTime);
                sasBuilder.SetPermissions(BlobSasPermissions.Read);
            }
            else
            {
                sasBuilder.Identifier = storedPolicyName;
            }

            Uri sasUri = blobClient.GenerateSasUri(sasBuilder);
            return sasUri;
        }

        [NonAction]
        public void LogError(string message, Exception exception, MethodBase methodType, ErrorType errorType)
        {
           // WriteLogs(string.Format("Error Message: {0} \n Stack Trace: {1}", message, exception.StackTrace));
            _logger.LogError(string.Format("LogError :: Error Message: {0} ::: Stack Trace: {1}", message, exception.StackTrace));
        }
        [NonAction]
        public void LogError(string errorCode, string message, Exception exception, string errorDetail, string methodName, string className)
        {
            _logger.LogError(string.Format("LogError ::: Class Name: {0} ::: Method Name: {1} ::: Error Message: {2} ::: Stack Trace: {3}", className, methodName, errorDetail, exception.StackTrace));
           // WriteLogs(string.Format("Class Name: {0} \n Method Name: {1} \n Error Message: {2} \n Stack Trace: {3}", className, methodName, errorDetail, exception.StackTrace));
        }
    }
}


