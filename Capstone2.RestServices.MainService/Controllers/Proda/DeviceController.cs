﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.MainService.Utility;
using Capstone2.RestServices.Proda.Interfaces;
using Capstone2.RestServices.Proda.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Proda.Controllers
{
    [Route("proda")]
    [ApiController]
    [ApiExceptionFilter]
    [Authorize]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class DeviceController : BaseController
    {
        private readonly IProdaService _prodaService;
        private readonly ILogger<DeviceController> _logger;
        public DeviceController(IOptions<MvcOptions> options, ILogger<DeviceController> logger, IProdaService prodaService, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _prodaService = prodaService;
            options.Value.InputFormatters.Insert(0, InputFormater.GetInputFormater());
        }

        /// <summary>
        /// Method to activate device
        /// </summary>
        /// <returns></returns>
        [HttpPut]
        [Route("Activate")]
        public async Task<IActionResult> Activate()
        {
            var apiResponse = new ApiResponse<dynamic>();
            try
            {
                apiResponse = await _prodaService.ActivateDevice(baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Something wrong in proda activate service.");
                _logger.LogError($"Something wrong in proda activate service {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to refresh device key
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("RefreshPublicKey")]
        public async Task<IActionResult> RefreshPublicKey()
        {
            var apiResponse = new ApiResponse<dynamic>();
            try
            {
                apiResponse = await _prodaService.RefreshPublicKey(baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Something wrong in proda refresh key service.");
                _logger.LogError($"Something wrong in proda refresh key service {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to fetch access token from proda
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("AccessToken")]
        public async Task<IActionResult> GetAuthenticationToken()
        {
            var apiResponse = new ApiResponse<dynamic>();
            try
            {
                apiResponse = await _prodaService.GetAuthenticationToken(baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Something wrong in proda access token service.");
                _logger.LogError($"Something wrong in proda access token service {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to fetch access token from proda
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("MedicareAccessToken")]
        public async Task<IActionResult> GetMedicareAuthenticationToken()
        {
            var apiResponse = new ApiResponse<dynamic>();
            try
            {
                string minorId = HttpContext.Request.Headers["MinorId"].FirstOrDefault();
                if (!string.IsNullOrWhiteSpace(minorId))
                {
                    apiResponse = await _prodaService.GetMedicareAuthToken(baseHttpRequestContext, minorId);
                    return apiResponse.StatusCode switch
                    {
                        StatusCodes.Status200OK => Ok(apiResponse),
                        StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                        StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                        _ => BadRequest(apiResponse),
                    };
                }
                else
                {
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Result = null;
                    apiResponse.Errors.Add("MinorId is required.");
                    return BadRequest(apiResponse);
                }
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Something wrong in medicare access token service.");
                _logger.LogError($"Something wrong in medicare access token service {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to update a device expiry
        /// </summary>
        /// <param name="inputProdaConfig"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("prodaconfig")]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
        public async Task<IActionResult> EditProdaConfig([FromBody] ProdaConfigsModel inputProdaConfig)
        {
            var apiResponse = new ApiResponse<string>();
            try
            {
                apiResponse = await _prodaService.EditProdaConfig(inputProdaConfig);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("ProdaConfig cannot be edited.Please try again later.");
                _logger.LogError($"ProdaConfig cannot be edited {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Health check endpoint
        /// </summary>
        /// <returns></returns>
        [HttpGet("healthcheck")]
        [Microsoft.AspNetCore.Authorization.AllowAnonymous]
        [TelemetryTracing(logRequest: false, logResponse: false)]
        public IActionResult HealthCheck()
        {
            return Ok(StatusCodes.Status200OK);
        }

    }
}
