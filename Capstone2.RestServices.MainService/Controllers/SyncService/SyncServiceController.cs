﻿using Azure.Core;
using Azure.Messaging.WebPubSub;
using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.SyncService.Hubs;
using Capstone2.RestServices.SyncService.Interfaces;
using Capstone2.RestServices.SyncService.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;
using System;
using System.Threading;
using System.Threading.Tasks;
using AuthorizeAttribute = Capstone2.Framework.RestApi.Filters.AuthorizeAttribute;

namespace Capstone2.RestServices.SyncService.Controllers
{
    [ApiController]
    [Route("syncservice")]
    [Authorize]
    [ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class SyncServiceController : BaseController
    {
        private readonly ILogger<SyncServiceController> _logger;
        private readonly ISyncServiceBAL _syncserviceBAL;

        public SyncServiceController(ILogger<SyncServiceController> logger, ISyncServiceBAL syncserviceBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _syncserviceBAL = syncserviceBAL;
        }

        /// <summary>
        /// get ClientPubSubToken
        /// </summary>
        /// <param name="HubName"></param>
        /// <returns></returns>
        [HttpGet("GetClientPUBSUBToken")]
        public async Task<ApiResponse<PubSubConnectionInfo>> GetClientPUBSUBToken(string screenname)
        {
           return await _syncserviceBAL.GetClientPUBSUBToken(screenname, baseHttpRequestContext.OrgCode);
        }

        /// <summary>
        /// Send data to hub 
        /// </summary>
        /// <param name="HubName"></param>
        /// <returns></returns>
        [HttpPost("SendDataToHubGeneric")]
        public ApiResponse<string> SendDataToHubGeneric(PubSubGenericResponse hubData)
        {
            return _syncserviceBAL.SendDataToHubGeneric(hubData,baseHttpRequestContext.OrgCode);
        }

        [HttpGet("healthcheck")]
        [AllowAnonymous]
        [TelemetryTracing(logRequest: false, logResponse: false)]
        public IActionResult HealthCheck()
        {
            return Ok(StatusCodes.Status200OK);
        }

    }
}
