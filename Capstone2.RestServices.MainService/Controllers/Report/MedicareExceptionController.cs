﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.Report.Interfaces;
using Capstone2.RestServices.Report.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Report.Controllers
{
    [ApiController]
    [Route("report")]
    [Authorize]
    [ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class MedicareExceptionController : BaseController
    {
        private readonly ILogger<MedicareExceptionController> _logger;
        private readonly IMedicareExceptionBAL _medicareExceptionBAL;

        public MedicareExceptionController(ILogger<MedicareExceptionController> logger, IMedicareExceptionBAL medicareExceptionBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _medicareExceptionBAL = medicareExceptionBAL;
        }

        /// <summary>
        /// Method to list the medicare exceptions
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("medicare_exceptions")]
        [ProducesResponseType(typeof(ApiResponse<QueryResultList<MedicareException>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ListMedicareExceptions([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<QueryResultList<MedicareException>>();
            try
            {
                apiResponse = await _medicareExceptionBAL.ListMedicareExceptions(queryModel, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Medicare Exceptions cannot be retrieved at this time.");
                _logger.LogError($"Medicare Exceptions cannot be retrieved at this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to delete the medicare exceptions
        /// </summary>
        /// <param name="id">id</param>
        /// <returns>status</returns>
        [HttpDelete]
        [Route("medicare_exceptions/{id}")]
        [ProducesResponseType(typeof(ApiResponse<QueryResultList<string>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> DeleteMedicareExceptions(long id)
        {
            var apiResponse = new ApiResponse<QueryResultList<string>>();
            try
            {
                apiResponse = await _medicareExceptionBAL.DeleteMedicareExceptions(id, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Something went wrong while deleting MedicareExceptions, please try after sometime.");
                _logger.LogError($"Something went wrong while deleting MedicareExceptions {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }
    }
}
