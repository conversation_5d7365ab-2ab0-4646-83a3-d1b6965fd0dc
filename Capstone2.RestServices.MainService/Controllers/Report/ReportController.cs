﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.Report.Interfaces;
using Capstone2.RestServices.Report.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AuthorizeAttribute = Capstone2.Framework.RestApi.Filters.AuthorizeAttribute;

namespace Capstone2.RestServices.Report.Controllers
{
    [ApiController]
    [Route("report")]
    [Authorize]
    [ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class ReportController : BaseController
    {
        private readonly ILogger<ReportController> _logger;
        private readonly IReportBAL _reportBAL;

        public ReportController(ILogger<ReportController> logger, IReportBAL reportBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _reportBAL = reportBAL;
        }

        [HttpGet("healthcheck")]
        [AllowAnonymous]
        [TelemetryTracing(logRequest: false, logResponse: false)]
        public IActionResult HealthCheck()
        {
            return Ok(StatusCodes.Status200OK);
        }

        /// <summary>
        /// Method to get the total amount of EOD Report
        /// </summary>
        /// <param name="paymentDate"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("eod_report_total")]
        [ProducesResponseType(typeof(ApiResponse<EODReportTotal>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetEODReportTotal([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<EODReportTotal>();

            try
            {

                apiResponse = await _reportBAL.GetEODReportTotal(queryModel, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    StatusCodes.Status403Forbidden => new ObjectResult(apiResponse) { StatusCode = StatusCodes.Status403Forbidden },
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Total EODReport cannot be retrieved at this time.");
                _logger.LogError($"Total EODReport cannot be retrieved at this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to list all the payments against an invoice
        /// </summary>
        /// <param name="queryModel"></param>
        /// <param name="invoicedetail_id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("payment_transactions")]
        [ProducesResponseType(typeof(ApiResponse<List<Eodreport>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ListPaymentTransactions([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<QueryResultList<Eodreport>>();

            try
            {

                apiResponse = await _reportBAL.ListPaymentTransactions(queryModel, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    StatusCodes.Status403Forbidden => new ObjectResult(apiResponse) { StatusCode = StatusCodes.Status403Forbidden },
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of EOD Payments cannot be retrieved at this time.");
                _logger.LogError($"List of EOD Payments cannot be retrieved at this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to list all the payments against an invoice group by entity and paymentMethodType
        /// </summary>
        /// <param name="queryModel"></param>
        /// <param name="invoicedetail_id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("entity_type")]
        [ProducesResponseType(typeof(ApiResponse<List<Eodreport>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ListGroupedPayments([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<dynamic>();

            try
            {

                apiResponse = await _reportBAL.ListGroupedPayments(queryModel, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of EOD Payments cannot be retrieved at this time.");
                _logger.LogError($"List of EOD Payments cannot be retrieved at this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to list all the payments against an invoice
        /// </summary>
        /// <param name="queryModel"></param> 
        /// <returns></returns>
        [HttpGet]
        [Route("provider_income")]
        [ProducesResponseType(typeof(ApiResponse<List<ProviderIncomeReport>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ListProviderIncome([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<dynamic>();

            try
            {

                apiResponse = await _reportBAL.ListProviderIncome(queryModel, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of Provider Income cannot be retrieved at this time.");
                _logger.LogError($"List of Provider Income cannot be retrieved at this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to get the total amount of Provider's income
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("provider_income_total")]
        [ProducesResponseType(typeof(ApiResponse<ProviderIncomeReportTotal>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetProviderIncomeTotal([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<ProviderIncomeReportTotal>();

            try
            {

                apiResponse = await _reportBAL.GetProviderIncomeTotal(queryModel, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Total EODReport cannot be retrieved at this time.");
                _logger.LogError($"Total EODReport cannot be retrieved at this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
        ///// <summary>
        ///// Method to get the total amount of Provider's income
        ///// </summary>
        ///// <returns></returns>
        //[HttpGet]
        //[Route("revenue_report")]
        //[ProducesResponseType(typeof(ApiResponse<QueryResultList<RevenueReport>>), StatusCodes.Status200OK)]
        //public async Task<IActionResult> GetRevenueReport([FromQuery] QueryModel queryModel)
        //{
        //    var apiResponse = new ApiResponse<QueryResultList<RevenueReport>>();

        //    try
        //    {

        //        apiResponse = await _reportBAL.ListRevenueReport(queryModel, baseHttpRequestContext);
        //        return apiResponse.StatusCode switch
        //        {
        //            StatusCodes.Status200OK => Ok(apiResponse),
        //            StatusCodes.Status400BadRequest => BadRequest(apiResponse),
        //            StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
        //            _ => BadRequest(apiResponse),
        //        };
        //    }
        //    catch (Exception Ex)
        //    {
        //        apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
        //        apiResponse.Result = null;
        //        apiResponse.Errors.Add("Revenue Report cannot be retrieved at this time.");
        //        _logger.LogError($"Revenue Report cannot be retrieved at this time {Ex.Message} {Ex.StackTrace}");
        //        return BadRequest(apiResponse);
        //    }
        //}


        /// <summary>
        /// Finance Dashboard- For BAS charts
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("revenue_report/company/group")]
        [ProducesResponseType(typeof(ApiResponse<RevenueReportGroupModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ListRevenueReportGroupByCompany([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<RevenueReportGroupModel>();

            try
            {

                apiResponse = await _reportBAL.ListRevenueReportGroupByCompany(queryModel, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Consolidated Revenue Report cannot be retrieved at this time.");
                _logger.LogError($"Consolidated Revenue Report cannot be retrieved at this time. {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
        /// <summary>
        /// Finance Dashboard- For BAS charts
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("sales_report/company/group")]
        [ProducesResponseType(typeof(ApiResponse<List<SalesReportGroupModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ListSalesReportGroupByCompanyEpisode([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<List<SalesReportGroupModel>>();

            try
            {

                apiResponse = await _reportBAL.ListSalesReportGroupByCompanyEpisode(queryModel, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Consolidated Sales Report cannot be retrieved at this time.");
                _logger.LogError($"Consolidated Sales Report cannot be retrieved at this time. {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Finance Dashboard- For BAS charts
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("business_activity_cash")]
        [ProducesResponseType(typeof(ApiResponse<BusinessActivity>), StatusCodes.Status200OK)]
        public async Task<IActionResult> BusinessActivityCash([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<BusinessActivity>();

            try
            {

                apiResponse = await _reportBAL.BusinessActivityCashBAL(queryModel, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Business Activity Cash cannot be retrieved at this time.");
                _logger.LogError($"Business Activity Cash cannot be retrieved at this time. {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Finance Dashboard- For Medical referrals report charts
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("medicalreferrals_report")]
        [ProducesResponseType(typeof(ApiResponse<List<MedicalReferralReport>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetMedicalReferralsReport([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<List<MedicalReferralReport>>();
            try
            {
                apiResponse = await _reportBAL.ListMedicalReferralReport(queryModel, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Medical Referrals Report cannot be retrieved at this time.");
                _logger.LogError($"Medical Referrals Report cannot be retrieved at this time. {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Finance Dashboard- For Non Medical referrals report charts
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("non_medicalreferrals_report")]
        [ProducesResponseType(typeof(ApiResponse<List<NonMedicalReferralReport>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetNonMedicalReferralsReport([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<List<NonMedicalReferralReport>>();
            try
            {
                apiResponse = await _reportBAL.ListNonMedicalReferralReport(queryModel, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Non Medical Referrals Report cannot be retrieved at this time.");
                _logger.LogError($"Non Medical Referrals Report cannot be retrieved at this time. {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to add a eod notes for a particular payment date
        /// </summary>
        /// <param name="eodReportNote"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("eod_report_notes")]
        public async Task<IActionResult> AddEODReportNotes([FromBody] EODReportNote eodReportNote)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _reportBAL.AddEODReportNotes(eodReportNote, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error while creating EODReport Notes.Please check all variables.");
                _logger.LogError($"Error while creating EODReport Notes {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }

        /// <summary>
        /// Method to update a eod notes
        /// </summary>
        /// <param name="eodReportNote"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("eod_report_notes")]
        public async Task<IActionResult> EditEODReportNotes([FromBody] EODReportNote eodReportNote)
        {
            var apiResponse = new ApiResponse<string>();
            try
            {
                apiResponse = await _reportBAL.UpdateEODReportNotes(eodReportNote, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    StatusCodes.Status403Forbidden => new ObjectResult(apiResponse) { StatusCode = StatusCodes.Status403Forbidden },
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("EODReport Notes cannot be Edited.Please try again later.");
                _logger.LogError($"EODReport Notes cannot be Edited {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to fetch a eod notes
        /// </summary>
        /// <param name="payment_date"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("eod_report_notes")]
        public async Task<IActionResult> GetEODReportNotes(string payment_date)
        {
            var apiResponse = new ApiResponse<EODReportNote>();
            try
            {
                apiResponse = await _reportBAL.GetEODReportNotes(payment_date, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("EODReport Notes could not be fetched during this time.");
                _logger.LogError($"EODReport Notes could not be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
    }
}
