﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.Report.Interfaces;
using Capstone2.RestServices.Report.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AuthorizeAttribute = Capstone2.Framework.RestApi.Filters.AuthorizeAttribute;

namespace Capstone2.RestServices.Report.Controllers
{
    [ApiController]
    [Route("report")]
    [Authorize]
    [ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class ProcessingReportController : BaseController
    {
        private readonly ILogger<ProcessingReportController> _logger;
        private readonly IProcessingReportBAL _processingReportBAL;
        public readonly IIMCProcessingReportBAL _iMCProcessingReportBAL;
        public ProcessingReportController(ILogger<ProcessingReportController> logger, IProcessingReportBAL processingReportBAL, IIMCProcessingReportBAL iMCProcessingReportBAL,IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _processingReportBAL = processingReportBAL;
            _iMCProcessingReportBAL = iMCProcessingReportBAL;
        }

        /// <summary>
        /// Method to list all the processing report against Claims
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("claims_processingreport")]
        [ProducesResponseType(typeof(ApiResponse<List<ProcessingReport>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetProcessingReportList([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<QueryResultList<ProcessingReport>>();
            try
            {
                apiResponse = await _processingReportBAL.GetProcessingReportList(queryModel, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of ProcessingReport cannot be retrieved at this time.");
                _logger.LogError($"List of ProcessingReport cannot be retrieved at this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to the processing details against Claims
        /// </summary>
        /// <param name="transactionId">transactionId</param>
        /// <returns></returns>
        [HttpGet]
        [Route("claims_processingdetails/{transactionId}")]
        [ProducesResponseType(typeof(ApiResponse<ClaimRequest>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetProcessingClaimDetails(string transactionId)
        {
            var apiResponse = new ApiResponse<ClaimRequest>();
            try
            {
                apiResponse = await _processingReportBAL.GetProcessingClaimDetails(transactionId, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("ProcessingClaimDetails cannot be retrieved at this time.");
                _logger.LogError($"ProcessingClaimDetails cannot be retrieved at this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


        /// <summary>
        /// Method to list all the processing report against IMC Claims
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("imc_processingreport")]
        [ProducesResponseType(typeof(ApiResponse<List<ImcprocessingReport>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetIMCProcessingReportList([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<QueryResultList<ImcprocessingReport>>();
            try
            {
                apiResponse = await _iMCProcessingReportBAL.GetProcessingReportList(queryModel, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of IMC Claims cannot be retrieved at this time.");
                _logger.LogError($"List of IMC Claims cannot be retrieved at this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to the processing details against IMC Claims
        /// </summary>
        /// <param name="transactionId">transactionId</param>
        /// <returns></returns>
        [HttpGet]
        [Route("imc_processingreport/{transactionId}")]
        [ProducesResponseType(typeof(ApiResponse<ClaimRequest>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetIMCProcessingClaimDetails(string transactionId)
        {
            var apiResponse = new ApiResponse<ClaimRequest>();
            try
            {
                apiResponse = await _iMCProcessingReportBAL.GetProcessingClaimDetails(transactionId, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("ProcessingClaimDetails for IMC cannot be retrieved at this time.");
                _logger.LogError($"ProcessingClaimDetails for IMC cannot be retrieved at this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
    }
}
