﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.Master.Interfaces;
using Capstone2.RestServices.Master.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Master.Controller
{
    [ApiController]
    [Route("master")]
    //[Authorize]
    [ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class MasterController : BaseController
    {
        private readonly ILogger<MasterController> _logger;
        private readonly IMasterBAL _masterBAL;

        public MasterController(ILogger<MasterController> logger, IMasterBAL masterBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _masterBAL = masterBAL;
        }

        [HttpGet]
        [Route("countries")]
        [ProducesResponseType(typeof(ApiResponse<List<CountryMaster>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetCountry(string search_term)
        {
            var apiResponse = new ApiResponse<List<CountryMaster>>();
            try
            {
                apiResponse = await _masterBAL.GetCountries(search_term);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Could not get countries data");
                _logger.LogError($"Could not get countries data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpGet]
        [Route("states")]
        [ProducesResponseType(typeof(ApiResponse<List<StateMaster>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetStates(string search_term, short country_id)
        {
            var apiResponse = new ApiResponse<List<StateMaster>>();
            try
            {
                apiResponse = await _masterBAL.GetStatesBAL(search_term, country_id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Could not get the states data");
                _logger.LogError($"Could not get the states data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("languages")]
        [ProducesResponseType(typeof(ApiResponse<List<LanguageMaster>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Getlanguages([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<List<LanguageMaster>>();
            try
            {
                apiResponse = await _masterBAL.GetLanguagesBAL(queryModel);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Could not get the languages data");
                _logger.LogError($"Could not get the languages data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpGet]
        [Route("timezones")]
        [ProducesResponseType(typeof(ApiResponse<List<TimeZoneMaster>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetTimeZones([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<List<TimeZoneMaster>>();
            try
            {
                apiResponse = await _masterBAL.GetTimeZonesBAL(queryModel);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Could not get the timezones data");
                _logger.LogError($"Could not get the timezones data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
        [HttpGet]
        [Route("nationalities")]
        [ProducesResponseType(typeof(ApiResponse<List<NationalityMaster>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Getnationalities([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<List<NationalityMaster>>();
            try
            {
                apiResponse = await _masterBAL.GetNationalitiesBAL(queryModel);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Could not get the nationalities data");
                _logger.LogError($"Could not get the nationalities data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpGet]
        [Route("system_constants/{type}")]
        [ProducesResponseType(typeof(ApiResponse<List<SystemConstant>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetSystemConsants(string type)
        {
            var apiResponse = new ApiResponse<List<SystemConstant>>();
            try
            {
                apiResponse = await _masterBAL.GetSystemConstantsBAL(type);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Could not get the System Constants data");
                _logger.LogError($"Could not get the System Constants data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpGet]
        [Route("datafields")]
        [ProducesResponseType(typeof(ApiResponse<List<DataFieldsView>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetDataFields([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<List<DataFieldsView>>();
            try
            {
                apiResponse = await _masterBAL.GetDataFielsBAL(queryModel);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Could not get Data Fields");
                _logger.LogError($"Could not get Data Fields {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpGet]
        [Route("removecache")]
        public async Task<IActionResult> RemoveCache(string cachekey)
        {
            await _masterBAL.RemoveCache(cachekey);
            return Ok(StatusCodes.Status200OK);
        }

        [HttpGet("healthcheck")]
        [AllowAnonymous]
        [TelemetryTracing(logRequest: false, logResponse: false)]
        public IActionResult HealthCheck()
        {
            return Ok(StatusCodes.Status200OK);
        }

        /// <summary>
        /// Fetch the list of RolesCategories
        /// </summary>
        [HttpGet]
        [Route("roles_categories")]
        [ProducesResponseType(typeof(ApiResponse<List<RolesCategory>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetRolesCategories()
        {
            var apiResponse = new ApiResponse<List<RolesCategory>>();
            try
            {
                apiResponse = await _masterBAL.GetRolesCategoriesBAL();
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Could not get the roles categories data");
                _logger.LogError($"Could not get the roles categories data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Fetch the list of RolesPermissions
        /// </summary>
        [HttpGet]
        [Route("roles_permissions")]
        [ProducesResponseType(typeof(ApiResponse<List<RolesPermission>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetRolesPermissions()
        {
            var apiResponse = new ApiResponse<List<RolesPermission>>();
            try
            {
                apiResponse = await _masterBAL.GetRolesPermissionsBAL();
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Could not get the roles permissions data");
                _logger.LogError($"Could not get the roles permissions data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Fetch the list of RolesPermissions
        /// </summary>
        [HttpGet]
        [Route("medicare_participants")]
        [ProducesResponseType(typeof(ApiResponse<List<MedicareParticipants>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetMedicareParticipants([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<List<MedicareParticipants>>();
            try
            {
                apiResponse = await _masterBAL.GetMedicareParticipants(queryModel);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Could not get the MedicareParticipants data");
                _logger.LogError($"Could not get the MedicareParticipants data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Add the list of RolesPermissions
        /// </summary>
        [HttpPost]
        [Route("medicare_participants")]
        [ProducesResponseType(typeof(ApiResponse<int>), StatusCodes.Status200OK)]
        public async Task<IActionResult> AddtMedicareParticipants([FromBody] ICollection<MedicareParticipants> participants)
        {
            var apiResponse = new ApiResponse<int>();
            try
            {
                apiResponse = await _masterBAL.AddMedicareParticipants(baseHttpRequestContext, participants);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = 0;
                apiResponse.Errors.Add("Could not post MedicareParticipants data");
                _logger.LogError($"Could not post MedicareParticipants data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Update the list of RolesPermissions
        /// </summary>
        [HttpPut]
        [Route("medicare_participants")]
        [ProducesResponseType(typeof(ApiResponse<int>), StatusCodes.Status200OK)]
        public async Task<IActionResult> UpdatetMedicareParticipants([FromBody] ICollection<MedicareParticipants> participants)
        {
            var apiResponse = new ApiResponse<int>();
            try
            {
                apiResponse = await _masterBAL.UpdateMedicareParticipants(baseHttpRequestContext, participants);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = 0;
                apiResponse.Errors.Add("Could not update MedicareParticipants data");
                _logger.LogError($"Could not update MedicareParticipants data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Fetch the list of MedicareConstants
        /// </summary>
        [HttpGet]
        [Route("medicare_constants/{type}")]
        [ProducesResponseType(typeof(ApiResponse<List<MedicareConstant>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GeMedicareConstants(string type)
        {
            var apiResponse = new ApiResponse<List<MedicareConstant>>();
            try
            {
                apiResponse = await _masterBAL.GeMedicareConstants(type);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Could not get  medicare constants data");
                _logger.LogError($"Could not get medicare constants data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Fetch the list of MedicareErrors
        /// </summary>
        [HttpGet]
        [Route("medicare_errors/{type}")]
        [ProducesResponseType(typeof(ApiResponse<List<MedicareErrors>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetMedicareErrors(short type, [FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<List<MedicareErrors>>();
            try
            {
                apiResponse = await _masterBAL.GetMedicareErrors(type, queryModel);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Could not get the medicare errors data");
                _logger.LogError($"Could not get the medicare errors data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Fetch the list of ApiPermissionsAssoc
        /// </summary>
        [HttpGet]
        [Route("apipermissionsassocs")]
        [ProducesResponseType(typeof(ApiResponse<List<ApiPermissionsAssoc>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetApiPermissionsAssocs([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<List<ApiPermissionsAssoc>>();
            try
            {
                apiResponse = await _masterBAL.GetApiPermissionsBAL(queryModel);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Could not get the ApiPermissionsAssoc data");
                _logger.LogError($"Could not get the ApiPermissionsAssoc data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
        /// <summary>
        /// Fetch the list of Presenting Illness Codes
        /// </summary>
        [HttpGet]
        [Route("presentingillness_codes")]
        [ProducesResponseType(typeof(ApiResponse<List<PresentingIllnessCode>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ListPresentingIllnessCodes(string searchTerm)
        {
            var apiResponse = new ApiResponse<List<PresentingIllnessCode>>();
            try
            {
                apiResponse = await _masterBAL.ListPresentingIllnessCodes(searchTerm);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Could not get the PresentingIllnessCodes data");
                _logger.LogError($"Could not get the PresentingIllnessCodes data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Fetch the  Presenting Illness Codes from code
        /// </summary>
        [HttpGet]
        [Route("presentingillness_codes/{code}")]
        [ProducesResponseType(typeof(ApiResponse<PresentingIllnessCode>), StatusCodes.Status200OK)]
        public async Task<IActionResult>GetPresentingIllnessCodes(string code)
        {
            var apiResponse = new ApiResponse<PresentingIllnessCode>();
            try
            {
                apiResponse = await _masterBAL.GetPresentingIllnessCodes(code);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Could not get the PresentingIllnessCodes data");
                _logger.LogError($"Could not get the PresentingIllnessCodes data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("languages/{id}")]
        [ProducesResponseType(typeof(ApiResponse<LanguageMaster>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetlanguagesById(long id)
        {
            var apiResponse = new ApiResponse<LanguageMaster>();
            try
            {
                apiResponse = await _masterBAL.GetLanguagesByIdBAL(id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Could not get the language by Id data");
                _logger.LogError($"Could not get the languages by Id data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpGet]
        [Route("MedicalContractorBand")]
        [ProducesResponseType(typeof(ApiResponse<List<MedicalContractorBand>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetMedicalContractorBand([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<List<MedicalContractorBand>>();
            try
            {
                apiResponse = await _masterBAL.GetMedContBandBAL(queryModel);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Could not get the MedicalContractorBand data");
                _logger.LogError($"Could not get the MedicalContractorBand data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Fetch the medicare_participants
        /// </summary>
        [HttpGet]
        [Route("medicare_participants/{id}")]
        [ProducesResponseType(typeof(ApiResponse<MedicareParticipants>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetMedicareParticipantsById(int id)
        {
            var apiResponse = new ApiResponse<MedicareParticipants>();
            try
            {
                apiResponse = await _masterBAL.GetMedParticipantsById(id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Could not get the MedicareParticipants data");
                _logger.LogError($"Could not get the MedicareParticipants data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
        /// <summary>
        /// Fetch the medicare_participants from participantid 
        /// </summary>
        [HttpGet]
        [Route("medicare_participants/participantId")]
        [ProducesResponseType(typeof(ApiResponse<List<MedicareParticipantsInfo>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetMedicareParticipantsByParticipantIds([FromQuery] string ParticipantIds)
        {
            var apiResponse = new ApiResponse<List<MedicareParticipantsInfo>>();
            try
            {
                apiResponse = await _masterBAL.GetMedicareParticipantsByParticipantIds(ParticipantIds);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Could not get the MedicareParticipants data");
                _logger.LogError($"Could not get the MedicareParticipants data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Fetch all SystemConstants
        /// </summary>
        [HttpGet]
        [Route("system_constants")]
        [ProducesResponseType(typeof(ApiResponse<List<SystemConstant>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetAllSystemConsants()
        {
            var apiResponse = new ApiResponse<List<SystemConstant>>();
            try
            {
                apiResponse = await _masterBAL.GetAllSystemConstantsBAL();
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Could not get the System Constants data");
                _logger.LogError($"Could not get the System Constants data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Fetch the list of All MedicareConstants
        /// </summary>
        [HttpGet]
        [Route("medicare_constants")]
        [ProducesResponseType(typeof(ApiResponse<List<MedicareConstant>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GeAllMedicareConstants()
        {
            var apiResponse = new ApiResponse<List<MedicareConstant>>();
            try
            {
                apiResponse = await _masterBAL.GetAllMedicareConstants();
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Could not get  medicare constants data");
                _logger.LogError($"Could not get medicare constants data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


        /// <summary>
        ///Address Finder API 
        /// </summary>
        [HttpGet]
        [Route("address_finder")]
       [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetAddress([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<string>();
            try
            {
                apiResponse = await _masterBAL.GetAddressBAL(baseHttpRequestContext, queryModel.SearchTerm);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Could not get the addresses data");
                _logger.LogError($"Could not get the Address data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


        /// <summary>
        ///Address Finder API 
        /// </summary>
        [HttpGet]
        [Route("address_finder/{id}")]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetAddressById(string id)
        {
            var apiResponse = new ApiResponse<string>();
            try
            {
                apiResponse = await _masterBAL.GetAddressByIdBAL(baseHttpRequestContext, id);
             
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Could not get the addresses data");
                _logger.LogError($"Could not get the Address data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
    }
}
