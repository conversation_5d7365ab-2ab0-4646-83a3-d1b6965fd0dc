﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.Master.Interfaces;
using Capstone2.RestServices.Master.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Master.Controller
{
    [ApiController]
    [Route("master")]
    //[Authorize]
    [ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class HealthFundController : BaseController
    {
        private readonly ILogger<MasterController> _logger;
        private readonly IHealthFundBAL _healthFundBAL;

        public HealthFundController(ILogger<MasterController> logger, IHealthFundBAL healthFundBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _healthFundBAL = healthFundBAL;
        }

        /// <summary>
        /// Api to add a new HealthFundGroup
        /// </summary>
        /// <param name="inputHealthFundGroup"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("healthfund_group")]
        public async Task<IActionResult> AddHealthFundGroup([FromBody] HealthFundGroup inputHealthFundGroup)
        {
            var apiResponse = new ApiResponse<int?>();
            try
            {
                apiResponse = await _healthFundBAL.AddHFGroup(inputHealthFundGroup, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while creating Health Fund Group.Please check all variables");
                _logger.LogError($"Error while creating Health Fund Group {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


        [HttpGet]
        [Route("healthfund_group")]
        public async Task<IActionResult> ListHealthFundGroups([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<QueryResultList<HealthFundGroup>>();

            try
            {

                apiResponse = await _healthFundBAL.GetHealthFundGroupsBAL(baseHttpRequestContext, queryModel);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of Health Fund Group cannot be retrieved at this time.");
                _logger.LogError($"List of Health Fund Group cannot be retrieved {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to add New GroupToHealthFundAssocs
        /// </summary>
        /// <param name="inputAction"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("healthfund_group/{id}/grouptohealthfundassocs")]
        [ProducesResponseType(typeof(ApiResponse<int?>), StatusCodes.Status200OK)]
        public async Task<IActionResult> AddGroupToHFAssocs(int id, [FromBody] List<GroupToHealthFundAssoc> inputHFAssocs)
        {
            var apiResponse = new ApiResponse<int?>();
            try
            {
                apiResponse = await _healthFundBAL.AddGroupToHFAssocsBAL(id, inputHFAssocs, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error while creating GroupToHealthFundAssocs. Please check all variables.");
                _logger.LogError($"Error while creating GroupToHealthFundAssocs {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }

        /// <summary>
        /// Api to Edit HealthFund Group
        /// </summary>
        /// <param name="HealthFundGroup"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("healthfund_group/{id}")]
        public async Task<IActionResult> EditHealthFundGroup(int id, [FromBody] HealthFundGroup inputHealthFund)
        {
            var apiResponse = new ApiResponse<int?>();
            try
            {
                apiResponse = await _healthFundBAL.EditHealthFundGroupBAL(id, inputHealthFund, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while Editing HealthFundGroup .Please check all variables");
                _logger.LogError($"Error while Editing HealthFundGroup {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


        /// <summary>
        /// Method to Delete HealthFund Group
        /// </summary>
        /// <returns></returns>
        [HttpDelete]
        [Route("healthfund_group/{id}")]
        [ProducesResponseType(typeof(ApiResponse<int?>), StatusCodes.Status200OK)]
        public async Task<IActionResult> DeleteMedContPersonalDetails(int id)
        {
            var apiResponse = new ApiResponse<int?>();
            try
            {
                apiResponse = await _healthFundBAL.DeleteHFGroupBAL(id,  baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error while Delete HealthFund Group. Please check all variables.");
                _logger.LogError($"Error while Delete HealthFund Group {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }

        /// <summary>
        /// Method to Delete GroupToHealthFundAssocs
        /// </summary>
        /// <param name="deleteIds"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("healthfund_group/{id}/grouptohealthfundassocs")]
        [ProducesResponseType(typeof(ApiResponse<int?>), StatusCodes.Status200OK)]
        public async Task<IActionResult> DeleteMCBandAssocs(int id, [FromQuery] string deleteIds)
        {
            var apiResponse = new ApiResponse<int?>();
            try
            {
                apiResponse = await _healthFundBAL.DeleteGHFAssocsBAL(id, deleteIds, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                // TODO: will remove once rectify the error
                apiResponse.Errors.Add("Error while Delete GroupToHealthFundAssocs. Please check all variables.");
                _logger.LogError($"Error while Delete GroupToHealthFundAssocs {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }


        /// <summary>
        /// Api to get HealthFund Group BY Id
        /// </summary>
        /// <param name="HealthFundGroup"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("healthfund_group/{id}")]
        public async Task<IActionResult> GetHealthFundGroup(int id)
        {
            var apiResponse = new ApiResponse<HealthFundGroup>();
            try
            {
                apiResponse = await _healthFundBAL.GetHealthFundGroupByIdBAL(id, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while Fetching HealthFundGroup .Please check all variables");
                _logger.LogError($"Error while Fetching HealthFundGroup {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpGet]
        [Route("healthfund_data")]
        public async Task<IActionResult> ListHealthFundData([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<QueryResultList<HealthFundDataList>>();

            try
            {

                apiResponse = await _healthFundBAL.GetHealthFundDataBAL(baseHttpRequestContext, queryModel);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of Health Fund Data cannot be retrieved at this time.");
                _logger.LogError($"List of Health Fund Data cannot be retrieved {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to validate Health fund group name already exists
        /// </summary>
        /// <param name="search_term"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("healthfund_group/name/verify")]
        [ProducesResponseType(typeof(ApiResponse<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> CheckHFGroupName(string search_term)
        {
            var apiResponse = new ApiResponse<bool>();
            try
            {
                apiResponse = await _healthFundBAL.CheckHFGroupNameBAL(search_term, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error while verifying the Health fund group name.");
                _logger.LogError($"Error while verifying the Health fund group name {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to validate Health fund group name already exists
        /// </summary>
        /// <param name="search_term"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("grouptohealthfundassocs/name/verify")]
        [ProducesResponseType(typeof(ApiResponse<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> CheckHFName(string search_term)
        {
            var apiResponse = new ApiResponse<bool>();
            try
            {
                apiResponse = await _healthFundBAL.CheckHFNameBAL(search_term, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error while verifying the Health fund name.");
                _logger.LogError($"Error while verifying the Health fund name {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }



    }
}
