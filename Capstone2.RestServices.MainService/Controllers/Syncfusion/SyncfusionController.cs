﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.Syncfusion.Interfaces;
using Capstone2.RestServices.Syncfusion.Models;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using Syncfusion.Pdf.Parsing;
using System;
using System.Threading.Tasks;
using AuthorizeAttribute = Capstone2.Framework.RestApi.Filters.AuthorizeAttribute;

namespace Capstone2.RestServices.Syncfusion.Controllers
{
    [ApiController]
    [Route("syncfusion")]
    [Authorize]
    [ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class SyncfusionController : BaseController
    {
        private readonly ILogger<SyncfusionController> _logger;
        private readonly ISyncfusionBAL _syncfusionBAL;

        public SyncfusionController(ILogger<SyncfusionController> logger, ISyncfusionBAL syncfusionBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _syncfusionBAL = syncfusionBAL;
        }

        [HttpPost]
        [Route("convertToJson")]
        public async Task<ApiResponse<JObject>> ConvertToJson([FromBody] DocInput data)
        {
            return await _syncfusionBAL.ConvertToJsonBAL(data);
        }
       
        [HttpGet("healthcheck")]
        [AllowAnonymous]
        public IActionResult HealthCheck()
        {

            return Ok(StatusCodes.Status200OK);
        }

        /// <summary>
        /// ConvertSDFTtoPDF
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [ProducesResponseType(typeof(ApiResponse<FileUploadObject>), StatusCodes.Status200OK)]
        [HttpPost]
        [Route("ConvertDocToPDF")]
        public async Task<ApiResponse<FileUploadObject>> ConvertSFDToPDF([FromBody] string data)
        {
                return await _syncfusionBAL.ConvertSFDToPDF(data);
        }

        /// <summary>
        /// Convert HTML text to SFDT Text.
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [ProducesResponseType(typeof(ApiResponse<JObject>), StatusCodes.Status200OK)]
        [HttpPost]
        [Route("ConvertHTMLTOSFDT")]
        public async Task<IActionResult> ConvertHTMLTOSFDT([FromBody] string data)
        {
            var apiResponse = new ApiResponse<JObject>();
            try
            {
                apiResponse = await _syncfusionBAL.ConvertHTMLTOSFDT(data);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("ConvertHTMLTOSFDT failed.Please try again later.");
                _logger.LogError($"ConvertHTMLTOSFDT failed.Please try again later. {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }
    }

}
