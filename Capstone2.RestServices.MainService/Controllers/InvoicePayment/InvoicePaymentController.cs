﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.InvoicePayment.Interfaces;
using Capstone2.RestServices.InvoicePayment.Models;
using Capstone2.RestServices.MainService.Utility;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json.Serialization;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.ObjectPool;
using System.Buffers;

namespace Capstone2.RestServices.InvoicePayment.Controllers
{
    [ApiController]
    [Route("invoicepayment")]
    //[Authorize]
    [ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class InvoicePaymentController : BaseController
    {
        private readonly ILogger<InvoicePaymentController> _logger;
        private readonly IPaymentBAL _paymentBAL;

        public InvoicePaymentController(IOptions<MvcOptions> options, ILogger<InvoicePaymentController> logger, IPaymentBAL paymentBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _paymentBAL = paymentBAL;
            options.Value.InputFormatters.Insert(0, InputFormater.GetInputFormater());
        }

        [HttpGet("healthcheck")]
        [AllowAnonymous]
        [TelemetryTracing(logRequest: false, logResponse: false)]
        public IActionResult HealthCheck()
        {
            return Ok(StatusCodes.Status200OK);
        }

        /// <summary>
        /// Method to add Payments to InvoiceDetails
        /// </summary>
        /// <param name="inputPaymentDetail"></param>
        /// <param name="invoicedetail_id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("{patient_id}/{invoicedetail_id}/payment_details")]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
        public async Task<IActionResult> AddInvoicePayments([FromBody] List<PaymentDetail> lstPaymentDetail, long patient_id, long invoicedetail_id)
        {
            var apiResponse = new ApiResponse<string>();
            try
            {
                apiResponse = await _paymentBAL.AddInvoicePayments(lstPaymentDetail, invoicedetail_id, baseHttpRequestContext,patient_id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Payments cannot be added at this time.Please try again later.");
                _logger.LogError($"Payments cannot be added at this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
        /// <summary>
        /// Method to fetch details of a payment
        /// </summary>
        /// <param name="invoicedetail_id"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("{invoicedetail_id}/payment_details/{id}")]
        [ProducesResponseType(typeof(ApiResponse<PaymentDetail>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetInvoicePaymentDetail(long invoicedetail_id, long id)
        {
            var apiResponse = new ApiResponse<PaymentDetail>();
            try
            {
                apiResponse = await _paymentBAL.GetInvoicePaymentDetail(id, invoicedetail_id, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Payment Detail cannot be fetched at this time.Please try again later.");
                _logger.LogError($"Payment Detail cannot be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
        /// <summary>
        /// Method to fetch details of a payment with splitup
        /// </summary>
        /// <param name="invoicedetail_id"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("{invoicedetail_id}/payment_details_splitup/{id}")]
        [ProducesResponseType(typeof(ApiResponse<PaymentDetail>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetInvoicePaymentDetailSplitUp(long invoicedetail_id, long id)
        {
            var apiResponse = new ApiResponse<PaymentDetail>();
            try
            {
                apiResponse = await _paymentBAL.GetInvoicePaymentDetail(id, invoicedetail_id, baseHttpRequestContext, true);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Payment Detail cannot be fetched at this time.Please try again later.");
                _logger.LogError($"Payment Detail cannot be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
        /// <summary>
        /// Method to add Payments to InvoiceDetails
        /// </summary>
        /// <param name="inputPaymentDetail"></param>
        /// <param name="invoicedetail_id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("{invoicedetail_id}/payment_details_adjustment/{patient_id}")]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
        public async Task<IActionResult> AddInvoiceAdjustmentPayments([FromBody] PaymentDetail paymentDetail, long invoicedetail_id,long patient_id)
        {
            var apiResponse = new ApiResponse<long>();
            try
            {
                apiResponse = await _paymentBAL.AddInvoiceAdjustmentPayments(paymentDetail, invoicedetail_id, baseHttpRequestContext, patient_id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = default(long);
                apiResponse.Errors.Add("Payments cannot be added at this time.Please try again later.");
                _logger.LogError($"Payments cannot be added {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to update Invoice Payment partially
        /// </summary>
        /// <param name="paymentdetail_id"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("payment_details/patch")]
        public async Task<IActionResult> PartialUpdateInvoicePayments([FromBody] List<PaymentDetailView> paymentDetails)
        {
            var apiResponse = new ApiResponse<string>();

            try
            {
                apiResponse = await _paymentBAL.PartialUpdateInvoicePayments(baseHttpRequestContext, paymentDetails);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while updating invoice payment.");
                _logger.LogError($"Error while updating invoice payment {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to update reconcile and bankdate of Invoice payments.
        /// </summary>
        /// <param name="paymentdetail_id"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("reconcile_payments")]
        public async Task<IActionResult> UpdateReconcilePayments([FromBody] ReconcilePaymentDetail reconcilePaymentDetail)
        {
            var apiResponse = new ApiResponse<string>();

            try
            {
                apiResponse = await _paymentBAL.UpdateReconcilePayments(baseHttpRequestContext, reconcilePaymentDetail);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while updating invoice payment.");
                _logger.LogError($"Error while updating invoice payment {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to list all the payments against an invoice
        /// </summary>
        /// <param name="queryModel"></param>
        /// <param name="invoicedetail_id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("payment_details")]
        [ProducesResponseType(typeof(ApiResponse<QueryResultList<ListPaymentDetail>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ListPaymentTransactions([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<QueryResultList<ListPaymentDetail>>();

            try
            {

                apiResponse = await _paymentBAL.ListPaymentTransactions(queryModel, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of Invoice Payments cannot be retrieved at this time.");
                _logger.LogError($"List of Invoice Payments cannot be retrieved {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


     /// <summary>
     /// 
     /// </summary>
     /// <param name="invoiceObj"></param>
     /// <returns></returns>
        [HttpPost]
        [Route("payment_details/invoice_conversion")]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
        public async Task<IActionResult> AddUpdateInvoicePaymentsOnConversion([FromBody]InvoiceDetailIdObject invoiceObj)
        {
            var apiResponse = new ApiResponse<string>();
            try
            {
                apiResponse = await _paymentBAL.AddUpdateInvoicePaymentsOnConversion(invoiceObj, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Payments cannot be added at this time.Please try again later.");
                _logger.LogError($"Payments cannot be added at this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to add Payments to InvoiceDetails
        /// </summary>
        /// <param name="inputPaymentDetail"></param>
        /// <param name="invoicedetail_id"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("payment_details/{id}")]
        [ProducesResponseType(typeof(ApiResponse<PaymentDetail>), StatusCodes.Status200OK)]
        public async Task<IActionResult> EditInvoicePayments([FromBody] PaymentDetail paymentDetail, long id)
        {
            var apiResponse = new ApiResponse<PaymentDetail>();
            try
            {
                apiResponse = await _paymentBAL.EditInvoicePayment(paymentDetail, id, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Payments cannot be updated at this time.Please try again later.");
                _logger.LogError($"Payments cannot be updated at this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


        /// <summary>
        /// Method to delete a payment based on id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("payment_details/{id}")]
        [ProducesResponseType(typeof(ApiResponse<long?>), StatusCodes.Status200OK)]
        public async Task<IActionResult> DeleteInvoicePayment(long id, [FromQuery] Shared.Models.Entities.DeleteObject deleteObject)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _paymentBAL.DeleteInvoicePayment(baseHttpRequestContext, id, deleteObject,null);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while deleting payments.");
                _logger.LogError($"Error while deleting payments {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


        /// <summary>
        /// Method to delete a payment based on id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("payment_details/{id}/transfer")]
        [ProducesResponseType(typeof(ApiResponse<long?>), StatusCodes.Status200OK)]
        public async Task<IActionResult> TransferInvoicePayment(long id, [FromQuery]TransferObject transferObject)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _paymentBAL.DeleteInvoicePayment(baseHttpRequestContext, id, null,transferObject);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while Transferring payments.");
                _logger.LogError($"Error while Transferring payments {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


        /// <summary>
        /// Method to fetch details of a Total Patient Credit
        /// </summary>
        /// <param name="invoicedetail_id"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("patient_credit/{patient_id}/total")]
        [ProducesResponseType(typeof(ApiResponse<decimal?>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetPatinetCreditTotal([FromQuery] string companyDetailsId,long patient_id)
        {
            var apiResponse = new ApiResponse<decimal?>();
            try
            {
                apiResponse = await _paymentBAL.GetPatinetCreditTotalBAL( patient_id, baseHttpRequestContext, companyDetailsId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Patient Credit cannot be fetched at this time.Please try again later.");
                _logger.LogError($"Patient Credit cannot be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

    }
}
