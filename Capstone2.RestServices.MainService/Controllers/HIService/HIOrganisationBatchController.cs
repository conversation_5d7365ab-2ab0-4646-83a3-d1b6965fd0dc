﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.HIService.Interfaces;
using Capstone2.RestServices.HIService.Models;
using Capstone2.RestServices.MainService.Utility;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Threading.Tasks;
using BatchAsync = nehta.mcaR51.ProviderBatchAsyncSearchForProviderOrganisation;

namespace Capstone2.RestServices.HIService.Controllers
{
    [Route("hiservice")]
    [ApiController]
    [ApiExceptionFilter]
    [Authorize]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class HIOrganisationBatchController : BaseController
    {
        private readonly ILogger<HIOrganisationBatchController> _logger;
        private readonly IOrganisationBatchSearchBAL _organisationBatchSearchBAL;
        public HIOrganisationBatchController(IOptions<MvcOptions> options, ILogger<HIOrganisationBatchController> logger, IOrganisationBatchSearchBAL organisationBatchSearchBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _organisationBatchSearchBAL = organisationBatchSearchBAL;
            options.Value.InputFormatters.Insert(0, InputFormater.GetInputFormater());
        }

        [HttpPost]
        [Route("submithpiobatchsearch")]
        [ProducesResponseType(typeof(ApiResponse<HPIOBatchSubmitResponse<BatchAsync.ServiceMessagesType>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> SubmitSearchHPIOBatchAsync([FromBody] HPIOBatchSearchRequest requestType)
        {
            var apiResponse = new ApiResponse<HPIOBatchSubmitResponse<BatchAsync.ServiceMessagesType>>();
            try
            {
                apiResponse = await _organisationBatchSearchBAL.SubmitBatchSearchAsync(requestType, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Healthcare Submit BatchAsync Organisation (HPI-O) cannot be fetched at this time.");
                _logger.LogError($"Healthcare Submit BatchAsync Organisation (HPI-O) cannot be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpPost]
        [Route("getretrievehpiobatchsearch")]
        [ProducesResponseType(typeof(ApiResponse<HPIIBatchRetrieveResponse<BatchAsync.ServiceMessagesType, BatchAsync.BatchSearchForProviderOrganisationResultType>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetRetrieveHPIOBatchSearch([FromBody] HPIOBatchSearchStatus requestType)
        {
            var apiResponse = new ApiResponse<HPIOBatchRetrieveResponse<BatchAsync.ServiceMessagesType, BatchAsync.BatchSearchForProviderOrganisationResultType>>();
            try
            {
                apiResponse = await _organisationBatchSearchBAL.GetRetrieveHPIOBatchSearch(requestType, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Healthcare RetrieveHPIOBatchSearch Organisation (HPI-O) cannot be fetched at this time.");
                _logger.LogError($"Healthcare RetrieveHPIOBatchSearch Organisation (HPI-O) cannot be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

    }
}
