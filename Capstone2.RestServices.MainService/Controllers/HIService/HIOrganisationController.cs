﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.HIService.Common;
using Capstone2.RestServices.HIService.Interfaces;
using Capstone2.RestServices.HIService.Models;
using Capstone2.RestServices.MainService.Utility;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Threading.Tasks;
using DirectoryForOrganisation = nehta.mcaR32.ProviderSearchHIProviderDirectoryForOrganisation;
using ProviderOrganisation = nehta.mcaR50.ProviderSearchForProviderOrganisation;

namespace Capstone2.RestServices.HIService.Controllers
{
    [Route("hiservice")]
    [ApiController]
    [ApiExceptionFilter]
    [Authorize]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class HIOrganisationController : BaseController
    {
        private readonly ILogger<HIOrganisationController> _logger;
        private readonly IOrganisationSearchBAL _organisationSearchBAL;
        public HIOrganisationController(IOptions<MvcOptions> options, ILogger<HIOrganisationController> logger, IOrganisationSearchBAL organisationSearchBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _organisationSearchBAL = organisationSearchBAL;
            options.Value.InputFormatters.Insert(0, InputFormater.GetInputFormater());
        }

        [HttpPost]
        [Route("gethpiostatus")]
        [ProducesResponseType(typeof(ApiResponse<HPIOSearchResponse<ProviderOrganisation.ServiceMessagesType, object>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetIdentifier([FromBody] HPIORequestType requestType)
        {
            var apiResponse = new ApiResponse<HPIOSearchResponse<ProviderOrganisation.ServiceMessagesType, object>>();
            if (!requestType.IsValid) return BadRequest(ModelValidator.ReturnValidationError(requestType.ValidationError.ToString()));
            try
            {
                apiResponse = await _organisationSearchBAL.GetIdentifier(requestType, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Healthcare Organisation (HPI-O) cannot be fetched at this time.");
                _logger.LogError($"Healthcare Organisation (HPI-O) cannot be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpPost]
        [Route("gethpiodirectoryentry")]
        [ProducesResponseType(typeof(ApiResponse<HPIOSearchResponse<DirectoryForOrganisation.ServiceMessagesType, DirectoryForOrganisation.OrganisationProviderDirectoryEntryType>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetDirectoryEntry([FromBody] HPIORequestType requestType)
        {
            var apiResponse = new ApiResponse<HPIOSearchResponse<DirectoryForOrganisation.ServiceMessagesType, DirectoryForOrganisation.OrganisationProviderDirectoryEntryType>>();
            if (!requestType.IsValid) return BadRequest(ModelValidator.ReturnValidationError(requestType.ValidationError.ToString()));
            try
            {
                apiResponse = await _organisationSearchBAL.GetDirectoryEntry(requestType, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Healthcare Organisation Directory (HPI-O) cannot be fetched at this time.");
                _logger.LogError($"Healthcare Organisation Directory (HPI-O) cannot be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

    }
}
