﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.HIService.Common;
using Capstone2.RestServices.HIService.Interfaces;
using Capstone2.RestServices.HIService.Models;
using Capstone2.RestServices.MainService.Utility;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Threading.Tasks;
using DirectoryForIndividual = nehta.mcaR32.ProviderSearchHIProviderDirectoryForIndividual;
using ProviderIndividual = nehta.mcaR50.ProviderSearchForProviderIndividual;

namespace Capstone2.RestServices.HIService.Controllers
{
    [Route("hiservice")]
    [ApiController]
    [ApiExceptionFilter]
    [Authorize]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class HIIndividualController : BaseController
    {
        private readonly ILogger<HIIndividualController> _logger;
        private readonly IIndividualSearchBAL _individualSearchBAL;
        public HIIndividualController(IOptions<MvcOptions> options, ILogger<HIIndividualController> logger, IIndividualSearchBAL individualSearchBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _individualSearchBAL = individualSearchBAL;
            options.Value.InputFormatters.Insert(0, InputFormater.GetInputFormater());
        }

        [HttpPost]
        [Route("gethpiistatus")]
        [ProducesResponseType(typeof(ApiResponse<HPIISearchResponse<ProviderIndividual.ServiceMessagesType, object>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetIdentifier([FromBody] HPIIRequestType requestType)
        {
            var apiResponse = new ApiResponse<HPIISearchResponse<ProviderIndividual.ServiceMessagesType, object>>();
            if (!requestType.IsValid) return BadRequest(ModelValidator.ReturnValidationError(requestType.ValidationError.ToString()));
            try
            {
                apiResponse = await _individualSearchBAL.GetIdentifier(requestType, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Healthcare Individual (HPI-I) cannot be fetched at this time.");
                _logger.LogError($"Healthcare Individual (HPI-I) cannot be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpPost]
        [Route("gethpiidirectoryentry")]
        [ProducesResponseType(typeof(ApiResponse<HPIISearchResponse<DirectoryForIndividual.ServiceMessagesType, DirectoryForIndividual.IndividualProviderDirectoryEntryType>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetDirectoryEntry([FromBody] HPIIRequestType requestType)
        {
            var apiResponse = new ApiResponse<HPIISearchResponse<DirectoryForIndividual.ServiceMessagesType, DirectoryForIndividual.IndividualProviderDirectoryEntryType>>();
            if (!requestType.IsValid) return BadRequest(ModelValidator.ReturnValidationError(requestType.ValidationError.ToString()));
            try
            {
                apiResponse = await _individualSearchBAL.GetDirectoryEntry(requestType, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Healthcare Individual Directory (HPI-I) cannot be fetched at this time.");
                _logger.LogError($"Healthcare Individual Directory (HPI-I) cannot be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

    }
}
