﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.HIService.Common;
using Capstone2.RestServices.HIService.Interfaces;
using Capstone2.RestServices.HIService.Models;
using Capstone2.RestServices.MainService.Utility;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using nehta.mcaR3.ConsumerSearchIHI;
using System;
using System.Threading.Tasks;

namespace Capstone2.RestServices.HIService.Controllers
{
    [Route("hiservice")]
    [ApiController]
    [ApiExceptionFilter]
    [Authorize]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class HIIdentifierController : BaseController
    {
        private readonly ILogger<HIIdentifierController> _logger;
        private readonly IIdentifierSearchBAL _identifierSearchBAL;
        public HIIdentifierController(IOptions<MvcOptions> options, ILogger<HIIdentifierController> logger, IIdentifierSearchBAL identifierSearchBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _identifierSearchBAL = identifierSearchBAL;
            options.Value.InputFormatters.Insert(0, InputFormater.GetInputFormater());
        }

        [HttpPost]
        [Route("getihinumber")]
        [ProducesResponseType(typeof(ApiResponse<IHISearchResponse<ServiceMessagesType>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetIHINumber([FromBody] IHIRequestType requestType)
        {
            var apiResponse = new ApiResponse<IHISearchResponse<ServiceMessagesType>>();
            if (!requestType.IsValid) return BadRequest(ModelValidator.ReturnValidationError(requestType.ValidationError.ToString()));
            try
            {
                apiResponse = await _identifierSearchBAL.GetIdentifier(requestType, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Healthcare Identifier (IHI) cannot be fetched at this time.");
                _logger.LogError($"Healthcare Identifier (IHI) cannot be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


        [HttpGet("healthcheck")]
        [Microsoft.AspNetCore.Authorization.AllowAnonymous]
        [TelemetryTracing(logRequest: false, logResponse: false)]
        public IActionResult HealthCheck()
        {
            return Ok(StatusCodes.Status200OK);
        }

    }
}
