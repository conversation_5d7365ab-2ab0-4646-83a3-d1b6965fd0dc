﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.HIService.Interfaces;
using Capstone2.RestServices.HIService.Models;
using Capstone2.RestServices.MainService.Utility;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Threading.Tasks;
using BatchAsync = nehta.mcaR51.ProviderBatchAsyncSearchForProviderIndividual;

namespace Capstone2.RestServices.HIService.Controllers
{
    [Route("hiservice")]
    [ApiController]
    [ApiExceptionFilter]
    [Authorize]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class HIIndividualBatchController : BaseController
    {
        private readonly ILogger<HIIndividualBatchController> _logger;
        private readonly IIndividualBatchSearchBAL _individualBatchSearchBAL;
        public HIIndividualBatchController(IOptions<MvcOptions> options, ILogger<HIIndividualBatchController> logger, IIndividualBatchSearchBAL individualBatchSearchBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _individualBatchSearchBAL = individualBatchSearchBAL;
            options.Value.InputFormatters.Insert(0, InputFormater.GetInputFormater());
        }

        [HttpPost]
        [Route("submithpiibatchsearch")]
        [ProducesResponseType(typeof(ApiResponse<HPIIBatchSubmitResponse<BatchAsync.ServiceMessagesType>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> SubmitSearchHPIIBatchAsync([FromBody] HPIIBatchSearchRequest requestType)
        {
            var apiResponse = new ApiResponse<HPIIBatchSubmitResponse<BatchAsync.ServiceMessagesType>>();
            try
            {
                apiResponse = await _individualBatchSearchBAL.SubmitBatchSearchAsync(requestType, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Healthcare Submit BatchAsync Individuals (HPI-I) cannot be fetched at this time.");
                _logger.LogError($"Healthcare Submit BatchAsync Individuals (HPI-I) cannot be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpPost]
        [Route("getretrievehpiibatchsearch")]
        [ProducesResponseType(typeof(ApiResponse<HPIIBatchRetrieveResponse<BatchAsync.ServiceMessagesType, BatchAsync.BatchSearchForProviderIndividualResultType>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetRetrieveHPIIBatchSearch([FromBody] HPIIBatchSearchStatus requestType)
        {
            var apiResponse = new ApiResponse<HPIIBatchRetrieveResponse<BatchAsync.ServiceMessagesType, BatchAsync.BatchSearchForProviderIndividualResultType>>();
            try
            {
                apiResponse = await _individualBatchSearchBAL.GetRetrieveHPIIBatchSearch(requestType, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Healthcare RetrieveHPIIBatchSearch Individuals (HPI-I) cannot be fetched at this time.");
                _logger.LogError($"Healthcare RetrieveHPIIBatchSearch Individuals (HPI-I) cannot be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

    }
}
