﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.HIService.Interfaces;
using Capstone2.RestServices.HIService.Models;
using Capstone2.RestServices.MainService.Utility;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Threading.Tasks;
using BatchAsync = nehta.mcaR3.ConsumerSearchIHIBatchAsync;

namespace Capstone2.RestServices.HIService.Controllers
{
    [Route("hiservice")]
    [ApiController]
    [ApiExceptionFilter]
    [Authorize]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class HIIdentifierBatchController : BaseController
    {
        private readonly ILogger<HIIdentifierBatchController> _logger;
        private readonly IIdentifierBatchSearchBAL _identifierBatchSearchBAL;
        public HIIdentifierBatchController(IOptions<MvcOptions> options, ILogger<HIIdentifierBatchController> logger, IIdentifierBatchSearchBAL identifierBatchSearchBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _identifierBatchSearchBAL = identifierBatchSearchBAL;
            options.Value.InputFormatters.Insert(0, InputFormater.GetInputFormater());
        }

        [HttpPost]
        [Route("submitihibatchsearchasync")]
        [ProducesResponseType(typeof(ApiResponse<IHIBatchSubmitResponse<BatchAsync.ServiceMessagesType>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> SubmitSearchIHIBatchAsync([FromBody] IHIBatchSearchRequest requestType)
        {
            var apiResponse = new ApiResponse<IHIBatchSubmitResponse<BatchAsync.ServiceMessagesType>>();
            try
            {
                apiResponse = await _identifierBatchSearchBAL.SubmitBatchSearchAsync(requestType, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Healthcare Submit BatchAsync Identifier (IHI) cannot be fetched at this time.");
                _logger.LogError($"Healthcare Submit BatchAsync Identifier (IHI) cannot be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpPost]
        [Route("getsearchihibatchstatus")]
        [ProducesResponseType(typeof(ApiResponse<IHIBatchStatusResponse<BatchAsync.ServiceMessageType>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetSearchIHIBatchStatus([FromBody] IHIBatchSearchStatus requestType)
        {
            var apiResponse = new ApiResponse<IHIBatchStatusResponse<BatchAsync.ServiceMessageType>>();
            try
            {
                apiResponse = await _identifierBatchSearchBAL.GetSearchIHIBatchStatus(requestType, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Healthcare Status IHIBatch Identifier (IHI) cannot be fetched at this time.");
                _logger.LogError($"Healthcare Status IHIBatch Identifier (IHI) cannot be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpPost]
        [Route("getretrievesearchihibatch")]
        [ProducesResponseType(typeof(ApiResponse<IHIBatchRetrieveResponse<BatchAsync.ServiceMessagesType, BatchAsync.SearchIHIResultType>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetRetrieveSearchIHIBatch([FromBody] IHIBatchSearchStatus requestType)
        {
            var apiResponse = new ApiResponse<IHIBatchRetrieveResponse<BatchAsync.ServiceMessagesType, BatchAsync.SearchIHIResultType>>();
            try
            {
                apiResponse = await _identifierBatchSearchBAL.GetRetrieveSearchIHIBatch(requestType, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Healthcare RetrieveSearchIHIBatch Identifier (IHI) cannot be fetched at this time.");
                _logger.LogError($"Healthcare RetrieveSearchIHIBatch Identifier (IHI) cannot be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpPost]
        [Route("deletesearchihibatch")]
        [ProducesResponseType(typeof(ApiResponse<IHIBatchDeleteResponse<BatchAsync.ServiceMessageType>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> DeleteSearchIHIBatch([FromBody] IHIBatchSearchStatus requestType)
        {
            var apiResponse = new ApiResponse<IHIBatchDeleteResponse<BatchAsync.ServiceMessageType>>();
            try
            {
                apiResponse = await _identifierBatchSearchBAL.DeleteSearchIHIBatch(requestType, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Healthcare DeleteSearchIHIBatch Identifier (IHI) cannot be fetched at this time.");
                _logger.LogError($"Healthcare DeleteSearchIHIBatch Identifier (IHI) cannot be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
    }
}
