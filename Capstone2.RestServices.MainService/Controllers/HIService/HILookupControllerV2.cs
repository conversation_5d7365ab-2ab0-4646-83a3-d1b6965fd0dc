﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.HIService.Interfaces;
using Capstone2.RestServices.MainService.Utility;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Capstone2.RestServices.HIService.Controllers
{
    [Route("hiservice")]
    [ApiController]
    [ApiExceptionFilter]
    [Authorize]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class HILookupControllerV2 : BaseController
    {
        private readonly ILogger<HILookupControllerV2> _logger;
        private readonly IHILookupBal _hILookupBal;

        public HILookupControllerV2(IOptions<MvcOptions> options, ILogger<HILookupControllerV2> logger, IHILookupBal hILookupBal, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _hILookupBal = hILookupBal;
            options.Value.InputFormatters.Insert(0, InputFormater.GetInputFormater());
        }

        [HttpPost]
        [Route("v2/lookup")]
        [ProducesResponseType(typeof(Task<ApiResponse<object>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> LookupV2([FromBody] Dictionary<string, object> request)
        {
            var apiResponse = new ApiResponse<object>();
            try
            {
                apiResponse = await _hILookupBal.LookupV2(request, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex) {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("HI Lookup cannot be fetched at this time.");
                _logger.LogError($"HI Lookup cannot be fetched at this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
    }
}
