﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.Business.Common;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.Appointment.Interfaces;
using Capstone2.RestServices.Appointment.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AppointmentView = Capstone2.RestServices.Appointment.Models.AppointmentView;

namespace Capstone2.RestServices.Appointment.Controllers
{
    [ApiController]
    [Route("appointment")]
    //[Authorize]
    [ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class AppointmentController : BaseController
    {
        private readonly ILogger<AppointmentController> _logger;
        private readonly IAppointmentBAL _appointmentBAL;

        public AppointmentController(ILogger<AppointmentController> logger, IAppointmentBAL appointmentBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _appointmentBAL = appointmentBAL;
        }

        /// <summary>
        /// Api to add a new AppointmentType
        /// </summary>
        /// <param name="inputAppointmentType"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("appointment_details")]
        public async Task<ActionResult<ApiResponse<long?>>> AddAppointmentType([FromBody] AppointmentDetailsInfo inputAppointmentDetail)
        {
            var apiResponse = new ApiResponse<long?>();

            try
            {
                apiResponse = await _appointmentBAL.AddAppointmentDetail(inputAppointmentDetail.AppointmentDetails, baseHttpRequestContext, inputAppointmentDetail.DiaryUsersId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while creating Appointment.Please check all variables");
                _logger.LogError($"Error while creating Appointment {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
        /// <summary>
        /// Method to Get the Appointment details
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>

        [HttpGet]
        [Route("appointment_details/{id}")]
        [ProducesResponseType(typeof(ApiResponse<AppointmentView>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetAppointmentDetails( long id)
        {
            var apiResponse = new ApiResponse<AppointmentView>();

            try
            {

                apiResponse = await _appointmentBAL.GetAppointmentDetails( id, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting Appointment Details.");
                _logger.LogError($"Error while getting Appointment Details {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpGet]
        [Route("hcp_summary_list")]
        [ProducesResponseType(typeof(ApiResponse<AppointmentHcpsummaryView>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ListAppointmentHCPSummary([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<QueryResultList<AppointmentHcpsummaryView>>();

            try
            {
                apiResponse = await _appointmentBAL.ListAppointmentHCPSummary(queryModel, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting Appointment HCP Summary Details.");
                _logger.LogError($"Error while getting Appointment HCP Summary Details {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to fetch List of Appointments
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("appointment_details")]
        public async Task<IActionResult> ListAppointments([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<QueryResultList<AppointmentList>>();

            try
            {
                apiResponse = await _appointmentBAL.ListAppointmentsBAL(queryModel, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of Appointment could not be fetched during this time.");
                _logger.LogError($"List of Appointment could not be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to fetch List of Activities
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("appointment_checklist_activity_assoc")]
        public async Task<IActionResult> ListAppointmentChecklistActivityAssocs([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<QueryResultList<AppointmentChecklistActivityAssocView>>();

            try
            {
                apiResponse = await _appointmentBAL.ListAppointmentChecklistActivityAssocsBAL(queryModel, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of Appointment ChecklistActivityAssocs could not be fetched during this time.");
                _logger.LogError($"List of Appointment ChecklistActivityAssocs could not be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


        /// <summary>
        /// Appointment SMS Reminders
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("appointment_sms_reminders")]
        [ProducesResponseType(typeof(ApiResponse<QueryResultList<SmsExceptionData>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetSmsExceptionData([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<QueryResultList<SmsExceptionData>>();

            try
            {
                apiResponse = await _appointmentBAL.GetSmsExceptionData(queryModel, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of SMS Exception could not be fetched during this time.");
                _logger.LogError($"List of SMS Exception could not be fetched during this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


        /// <summary>
        /// Appointment Waiting List
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("appointment_waiting_list")]
        [ProducesResponseType(typeof(ApiResponse<QueryResultList<WaitingListData>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetWaitingListData([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<QueryResultList<WaitingListData>>();
            try
            {
                apiResponse = await _appointmentBAL.GetWaitingListData(queryModel, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of Waiting List could not be fetched during this time.");
                _logger.LogError($"List of Waiting List could not be fetched during this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to update a Waiting List Data based on id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("appointment_details/waiting_data")]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
        public async Task<IActionResult> UpdateWaitingListData([FromBody] WaitingDataUpdateRequest waitingDataUpdateRequest)
        {
            var apiResponse = new ApiResponse<string>();
            try
            {
                apiResponse = await _appointmentBAL.UpdateWaitingListData(waitingDataUpdateRequest, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while updating Appointment");
                _logger.LogError($"Error while updating Appointment {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to update a Link Appointment 
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("appointment_details/update_link_appointment")]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
        public async Task<IActionResult> UpdateLinkAppointment([FromBody] UpdateLinkRequest updateLinkRequest)
        {
            var apiResponse = new ApiResponse<string>();
            try
            {
                apiResponse = await _appointmentBAL.UpdateLinkAppointment(updateLinkRequest, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while updating Appointment");
                _logger.LogError($"Error while updating Appointment {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


        [HttpPost]
        [Route("sms_activity_logs")]
        [ProducesResponseType(typeof(ApiResponse<long?>), StatusCodes.Status200OK)]
        public async Task<IActionResult> AddActivityLog([FromBody] SmsActivityLog smsActivityLog)
        {
            var apiResponse = new ApiResponse<bool>();
            try
            {
                apiResponse = await _appointmentBAL.AddSMSActivityLog(smsActivityLog, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = false;
                apiResponse.Errors.Add("Error while creating new Activity Log.Please check all variables");
                _logger.LogError($"Error while creating new Activity Log {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }


        /// <summary>
        /// Update Appointment SMS Reminder Inactive
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("appointment_sms_reminders_inactive/{id}")]
        [ProducesResponseType(typeof(ApiResponse<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> UpdateAppointmentSmsReminderInactive(long id)
        {
            var apiResponse = new ApiResponse<bool>();
            try
            {
                apiResponse = await _appointmentBAL.UpdateAppointmentSmsReminderInActive(id, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = false;
                apiResponse.Errors.Add("Error while updating appointment sms reminder inactive");
                _logger.LogError($"Error while updating appointment sms reminder inactive {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
        /// <summary>
        /// Method to update the appointment_checklist_activity_assoc
        /// </summary>
        /// <returns></returns>
        [HttpPut]
        [Route("appointment_checklist_activity_assoc/{id}")]
        public async Task<IActionResult> UpdateAppointmentChecklistActivityAssoc(long id, [FromBody] AppointmentChecklistActivityAssoc inputAppointmentChecklistActivityAssoc)
        {
            var apiResponse = new ApiResponse<AppointmentChecklistActivityAssocView>();
            try
            {
                apiResponse = await _appointmentBAL.UpdateAppointmentChecklistActivityAssocBAL(id, inputAppointmentChecklistActivityAssoc, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while updating appointmen checklist activity assoc");
                _logger.LogError($"Error while updating appointmen checklist activity assoc {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


        /// <summary>
        /// Method to verify if there are any conflicts prior to booking an appointment
        /// </summary>
        /// <param name="inputAppointmentDetail"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("appointment_details/verify")]
        [ProducesResponseType(typeof(ApiResponse<AppointmentDetails>), StatusCodes.Status200OK)]
        public async Task<IActionResult> VerifySchedules([FromBody] AppointmentDetailsInfo inputAppointmentDetail)
        {
            var apiResponse = new ApiResponse<List<AppointmentDetails>>();

            try
            {

                apiResponse = await _appointmentBAL.VerifyAppointmentConflicts(baseHttpRequestContext, inputAppointmentDetail.AppointmentDetails, inputAppointmentDetail.DiaryUsersId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while checking for conflicts in appointments.");
                _logger.LogError($"Error while checking for conflicts in appointments {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to delete a Appointment based on id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("appointment_details/{id}")]
        public async Task<IActionResult> DeleteAppointment(long id, [FromQuery] DeleteObject deleteObject)
        {
            var apiResponse = new ApiResponse<string>();

            try
            {

                apiResponse = await _appointmentBAL.DeleteAppointBAL(baseHttpRequestContext, id, deleteObject);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while deleting Appointment");
                _logger.LogError($"Error while deleting Appointment {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// Method to test Patch Appointment
        /// </summary>
        /// <param name="id"></param>
        /// <param name="lstPatchDto"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("appointment_details/patch/{id}")]
        public async Task<IActionResult> PartialUpdateAppointment1(long id, [FromBody] AppointmentUpdateView appointmentUpdateView)
        {
            var apiResponse = new ApiResponse<string>();

            try
            {
                apiResponse = await _appointmentBAL.PartialUpdateAppointment(baseHttpRequestContext, id, appointmentUpdateView);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while updating Appointment.");
                _logger.LogError($"Error while updating Appointment {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to update Appointment
        /// </summary>
        /// <param name="id"></param>
        /// <param name="lstPatchDto"></param>
        /// <returns></returns>
        [HttpPatch]
        [Route("appointment_details/{id}")]
        public async Task<IActionResult> PartialUpdateAppointment(long id, [FromBody] AppointmentUpdateView appointmentUpdateView )
        {
            var apiResponse = new ApiResponse<string>();

            try
            {
                apiResponse = await _appointmentBAL.PartialUpdateAppointment(baseHttpRequestContext, id, appointmentUpdateView);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while updating Appointment.");
                _logger.LogError($"Error while updating Appointment {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to fetch cunt of checklists based on id.
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("appointment_checklist_activity_assoc/notifications_count/{user_id}")]
        [ProducesResponseType(typeof(ApiResponse<int>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetChecklistsNotificationCount(long user_id)
        {
            var apiResponse = new ApiResponse<int>();

            try
            {
                apiResponse = await _appointmentBAL.GetChecklistsNotificationCount(baseHttpRequestContext, user_id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = 0;
                apiResponse.Errors.Add("Error while getting Notification Count");
                _logger.LogError($"Error while getting Notification Count {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to update a Appointment based on id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("appointment_details/{id}")]
        public async Task<IActionResult> UpdateAppointment(long id, [FromBody] AppointmentDetailsInfo inputAppointmentDetail)
        {
            var apiResponse = new ApiResponse<string>();
            try
            {
                apiResponse = await _appointmentBAL.UpdateAppointmentBAL(id, inputAppointmentDetail.AppointmentDetails, baseHttpRequestContext, inputAppointmentDetail.DiaryUsersId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while updating Appointment");
                _logger.LogError($"Error while updating Appointment {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


        /// <summary>
        /// Method to update a Appointment based on id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [ProducesResponseType(typeof(ApiResponse<bool>), StatusCodes.Status200OK)]
        [HttpPost]
        [Route("appointmentsSMSReminder/{id}")]
        public async Task<IActionResult> AddAppointmentSMSReminder(long id)
        {
            var apiResponse = new ApiResponse<bool>();
            try
            {
                apiResponse = await _appointmentBAL.AddAppointmentSMSReminders(id, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = false;
                apiResponse.Errors.Add("Error while Adding SMS Reminder");
                _logger.LogError($"Error while Adding SMS Reminder {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to update a Appointment based on id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [ProducesResponseType(typeof(ApiResponse<bool>), StatusCodes.Status200OK)]
        [HttpPost]
        [Route("sendAppointmentSMSConfirmation/{id}")]
        public async Task<IActionResult> SendAppointmentSMSConfirmation(long id)
        {
            var apiResponse = new ApiResponse<bool>();
            try
            {
                apiResponse = await _appointmentBAL.SendConfirmationSMS(id, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = false;
                apiResponse.Errors.Add("Error while Sending Confirmation SMS");
                _logger.LogError($"Error while Sending Confirmation SMS {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to update a Appointment based on id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [ProducesResponseType(typeof(ApiResponse<bool>), StatusCodes.Status200OK)]
        [HttpPost]
        [Route("SendAppointmentDataToPubSub")]
        public async Task<IActionResult> SendAppointmentDataToPubSub(List<AppointmentDetails> appointmentDetails)
        {
            var apiResponse = new ApiResponse<bool>();
            try
            {
                apiResponse = await _appointmentBAL.SendAppointmentDataToPubSub(baseHttpRequestContext, appointmentDetails);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = false;
                apiResponse.Errors.Add("Error while Sending Appointment PubSubData");
                _logger.LogError($"Error while Sending Appointment PubSubData {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// Method to update Appointment by SMSId
        /// </summary>
        /// <param name="id"></param>
        /// <param name="lstPatchDto"></param>
        /// <returns></returns>
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
        [HttpPut]
        [Route("appointment_details/updateByMessageId/{id}")]
        public async Task<IActionResult> UpdateAppointmentByMessageId(long id, [FromBody] AppointmentUpdateView appointmentUpdateView)
        {
            var apiResponse = new ApiResponse<string>();

            try
            {
                apiResponse = await _appointmentBAL.UpdateAppointmentByMessageId(baseHttpRequestContext, id, appointmentUpdateView);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while updating Appointment.");
                _logger.LogError($"Error while updating Appointment {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// Method to VerifyIfReplyReceived SMSId
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [ProducesResponseType(typeof(ApiResponse<bool>), StatusCodes.Status200OK)]
        [HttpGet]
        [Route("appointmentSMSReminder/verify_reply_received/{smsRequestId}")]
        public async Task<IActionResult> VerifyIfReplyReceived(long smsRequestId)
        {
            var apiResponse = new ApiResponse<bool>();

            try
            {
                apiResponse = await _appointmentBAL.VerifyIfReplyReceived(baseHttpRequestContext, smsRequestId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = false;
                apiResponse.Errors.Add("Error while VerifyIfReplyReceived.");
                _logger.LogError($"Error while VerifyIfReplyReceived {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
        /// <summary>
        /// Method to fetch List of Appointments with minimal info
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("appointment_details_info")]
        public async Task<IActionResult> ListAppointmentsInfo([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<QueryResultList<Capstone2.Shared.Models.Entities.AppointmentView>>();

            try
            {
                apiResponse = await _appointmentBAL.ListAppointmentsInfo(queryModel, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of Appointment could not be fetched during this time.");
                _logger.LogError($"List of Appointment could not be fetched during this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
        /// <summary>
        /// Method to fetch an Appointment with minimal info inc Referral,Hospital if any
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("appointment_details_info_referral_hospital/{id}")]
        public async Task<IActionResult> FetchAppointmentInfoIncReferral(long id)
        {
            var apiResponse = new ApiResponse<Capstone2.Shared.Models.Entities.AppointmentViewWithHospitalInfo>();

            try
            {
                apiResponse = await _appointmentBAL.FetchAppointmentInfoIncReferral(id, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of Appointment could not be fetched during this time.");
                _logger.LogError($"List of Appointment could not be fetched during this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
        /// <summary>
        /// Method to fetch List of Appointments with minimal info inc Referral if any
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("appointment_details_info_referral")]
        public async Task<IActionResult> ListAppointmentsInfoIncReferral([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<QueryResultList<Capstone2.Shared.Models.Entities.AppointmentView>>();

            try
            {
                apiResponse = await _appointmentBAL.ListAppointmentsInfoWithReferral(queryModel, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of Appointment could not be fetched during this time.");
                _logger.LogError($"List of Appointment could not be fetched during this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
        [HttpGet("healthcheck")]
        [AllowAnonymous]
        [TelemetryTracing(logRequest: false, logResponse: false)]
        public IActionResult HealthCheck()
        {
            //var ClientIpAddress = ClientIp.GetClientIpAddress(baseHttpRequestContext.RemoteIpAddress);
            //if (HttpContext.Request.Headers.ContainsKey("X-Forwarded-For"))

            //{
            //   string headercontent = HttpContext.Request.Headers["X-Forwarded-For"].FirstOrDefault()?.ToString();
            //   if (headercontent != null && headercontent.IndexOf(':') != -1)
            //    {
            //        var ClientIpAddress2 = headercontent.Split(':').FirstOrDefault();
            //    }
            //}

            return Ok(StatusCodes.Status200OK);
        }

        /// <summary>
        /// Method to Get the Appointment details For LetterTemplate
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>

        [HttpGet]
        [Route("appointment_details_for_letter/{id}")]
        [ProducesResponseType(typeof(ApiResponse<AppointmentTagsForLetter>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetAppointmentForLetter(long id)
        {
            var apiResponse = new ApiResponse<AppointmentTagsForLetter>();

            try
            {

                apiResponse = await _appointmentBAL.GetAppointmentForLetter(id, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting Appointment Details For Letter");
                _logger.LogError($"Error while getting Appointment Details For Letter {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
        /// <summary>
        /// Method to fetch minimal info of an appointment
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("appointment_details_view")]
        [ProducesResponseType(typeof(ApiResponse<List<AppointmentViewInfo>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetAppointmentViewInfo([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<List<AppointmentViewInfo>>();

            try
            {

                apiResponse = await _appointmentBAL.GetAppointmentViewInfo(queryModel, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting Appointment Details");
                _logger.LogError($"Error while getting Appointment Details {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
       
       
    }
}
