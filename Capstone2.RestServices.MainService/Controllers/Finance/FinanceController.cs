﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.Finance.Interfaces;
using Capstone2.RestServices.Finance.Models;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Finance.Controllers
{
    [ApiController]
    [Route("finance")]
    //[Authorize]
    [ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class FinanceController : BaseController
    {
        private readonly ILogger<FinanceController> _logger;
        private readonly IFinanceBAL _financeBAL;

        public FinanceController(ILogger<FinanceController> logger, IFinanceBAL financeBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _financeBAL = financeBAL;
        }
        /// <summary>
        /// api to calcuclate the gst fees from fee exc gst
        /// </summary>
        /// <param name="id"></param>
        /// <param name="fee"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("calculategst")]
        [ProducesResponseType(typeof(ApiResponse<decimal>), StatusCodes.Status200OK)]
        public async Task<IActionResult> CalculateGst([FromQuery] GSTCalculateModel gstCalculateModel)
        {
            var apiResponse = new ApiResponse<decimal>();
            try
            {
                apiResponse = await _financeBAL.CalculateGst(baseHttpRequestContext, gstCalculateModel);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = default(decimal);
                apiResponse.Errors.Add("Gst fee cannot be computed during this time.");
                _logger.LogError($"Gst fee cannot be computed during this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
        [HttpGet]
        [Route("rounding")]
        [ProducesResponseType(typeof(ApiResponse<decimal>), StatusCodes.Status200OK)]
        public IActionResult Rounding([FromQuery]RoundingModel rounding)
        {
            var apiResponse = new ApiResponse<decimal>();
            try
            {
                apiResponse =  _financeBAL.Rounding(rounding);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = default(decimal);
                apiResponse.Errors.Add("Rounding cannot be computed during this time.");
                _logger.LogError($"Rounding cannot be computed during this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
        [HttpGet("healthcheck")]
        [AllowAnonymous]
        [TelemetryTracing(logRequest: false, logResponse: false)]
        public IActionResult HealthCheck()
        {
            return Ok(StatusCodes.Status200OK);
        }

        /// <summary>
        /// api to calcuclate the gst fees from Fee inc gst
        /// </summary>
        /// <param name="id"></param>
        /// <param name="fee"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("calculategstFromFeeIncGst")]
        [ProducesResponseType(typeof(ApiResponse<GSTCalculateModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> CalculategstFromFeeIncGst([FromQuery] GSTCalculateModel gstCalculateModel)
        {
            var apiResponse = new ApiResponse<decimal>();
            try
            {
                apiResponse = await _financeBAL.CalculateGstFromFeeIncGst(baseHttpRequestContext, gstCalculateModel);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = default(decimal);
                apiResponse.Errors.Add("Gst fee cannot be computed during this time.");
                _logger.LogError($"Gst fee cannot be computed during this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
    }
}
