﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.Hcp.Interfaces;
using Capstone2.RestServices.Hcp.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Hcp.Controller
{
    [ApiController]
    [Route("hcp")]
    [ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: false)]
    public class HcpController : BaseController
    {
        private readonly ILogger<HcpController> _logger;
        private readonly IHcpBAL _hcpBAL;

        public HcpController(ILogger<HcpController> logger, IHcpBAL hcpBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _hcpBAL = hcpBAL;
        }

      
        [HttpGet("healthcheck")]
        [AllowAnonymous]
        [TelemetryTracing(logRequest: false, logResponse: false)]
        public IActionResult HealthCheck()
        {
            return Ok(StatusCodes.Status200OK);
        }


        [HttpGet]
        [Route("hcp_summary")]
        [ProducesResponseType(typeof(QueryResultList<HcpSummaryView>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ListAppointmentHCPSummary([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<QueryResultList<HcpSummaryView>>();

            try
            {
                apiResponse = await _hcpBAL.ListHCPSummary(queryModel, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting Appointment HCP Summary Details.");
                _logger.LogError($"Error while getting Appointment HCP Summary Details {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


        [HttpPut]
        [Route("hcpsummary/{id}")]
        [ProducesResponseType(typeof(ApiResponse<Hcpsummary>), StatusCodes.Status200OK)]
        public async Task<IActionResult> EditHCPSummary([FromBody] Hcpsummary hcpsummary, long id)
        {
            var apiResponse = new ApiResponse<Hcpsummary>();

            try
            {
                apiResponse = await _hcpBAL.EditHCPSummary(id, hcpsummary, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while updating Appointment HCP Summary Details.");
                _logger.LogError($"Error while updating Appointment HCP Summary Details {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpPut]
        [Route("hcpsummary_validate/{id}")]
        [ProducesResponseType(typeof(ApiResponse<Hcpsummary>), StatusCodes.Status200OK)]
        public async Task<IActionResult> SaveAndValidateHCPSummary([FromBody] HcpsummaryViewInfo hcpsummary, long id)
        {
            var apiResponse = new ApiResponse<Hcpsummary>();

            try
            {
                apiResponse = await _hcpBAL.SaveAndValidateHCPSummary(id, hcpsummary, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while updating Appointment HCP Summary Details.");
                _logger.LogError($"Error while updating Appointment HCP Summary Details {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
        /// <summary>
        /// Fetch AppointmentHCPSummary from id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("hcpsummary/{id}")]
        [ProducesResponseType(typeof(ApiResponse<HcpsummaryViewInfo>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetAppointmentHCPSummaryFromId(long id)
        {
            var apiResponse = new ApiResponse<HcpsummaryViewInfo>();

            try
            {

                apiResponse = await _hcpBAL.GetHCPSummaryFromId(id, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting AppointmentHCPSummary.");
                _logger.LogError($"Error while getting AppointmentHCPSummary {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpPost]
        [Route("hcp_claim")]
        [ProducesResponseType(typeof(ApiResponse<Hcpsummary>), StatusCodes.Status200OK)]
        public async Task<IActionResult> AddHcpClaim([FromBody] HcpClaim hcpClaim)
        {
            var apiResponse = new ApiResponse<HcpClaim>();

            try
            {
                apiResponse = await _hcpBAL.AddHcpClaim(hcpClaim, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while adding new Hcp Claim.");
                _logger.LogError($"Error while adding new Hcp Claim. {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpGet]
        [Route("hcp_claim")]
        [ProducesResponseType(typeof(QueryResultList<HcpClaimView>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ListHCPExtractClaims([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<QueryResultList<HcpClaimView>>();

            try
            {
                apiResponse = await _hcpBAL.ListHCPExtractClaims(queryModel, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while HCP claim Details.");
                _logger.LogError($"Error while HCP claim Details. {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpPut]
        [Route("hcp_summary/patch/{id}")]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
        public async Task<IActionResult> PartialUpdateHcpSummary([FromBody] HcpSummaryUpdateView hcpSummaryUpdateView,long id)
        {
            var apiResponse = new ApiResponse<string>();

            try
            {
                apiResponse = await _hcpBAL.PartialUpdateHcpSummary(hcpSummaryUpdateView, id,baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while updating hcp summary.");
                _logger.LogError($"Error while updating hcp summary. {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpPut]
        [Route("hcp_claim/patch/{id}")]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
        public async Task<IActionResult> PartialUpdateHcpClaim([FromBody] HcpClaimUpdateView hcpClaimUpdateView, long id)
        {
            var apiResponse = new ApiResponse<string>();

            try
            {
                apiResponse = await _hcpBAL.PartialUpdateHcpClaim(hcpClaimUpdateView, id, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while updating hcp claim.");
                _logger.LogError($"Error while updating hcp claim. {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
    }
}

