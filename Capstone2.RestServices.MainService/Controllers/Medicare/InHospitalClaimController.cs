﻿using Capstone2.Common.MedicareOnline.IHCW;
using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.MainService.Utility;
using Capstone2.RestServices.Medicare.Interfaces;
using Capstone2.RestServices.Medicare.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Medicare.Controllers
{
    [Route("medicare")]
    [ApiController]
    [ApiExceptionFilter]
    [Authorize]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class InHospitalClaimController : BaseController
    {
        private readonly IInHospitalClaimService _inHospitalClaimService;
        private readonly ILogger<InHospitalClaimController> _logger;
        public InHospitalClaimController(IOptions<MvcOptions> options, ILogger<InHospitalClaimController> logger,
            IInHospitalClaimService inHospitalClaimService, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _inHospitalClaimService = inHospitalClaimService;
            options.Value.InputFormatters.Insert(0, InputFormater.GetInputFormater());
        }

        [HttpPost]
        [Route("submitinhospitalclaim")]
        public async Task<IActionResult> SubmitInHospitalClaim([FromBody] InHospitalClaimRequestType hospitalClaimRequest)
        {
            var apiResponse = new ApiResponse<dynamic>();
            try
            {
                string minorId = HttpContext.Request.Headers["MinorId"].FirstOrDefault();
                apiResponse = await _inHospitalClaimService.SubmitInHospitalClaim(hospitalClaimRequest, baseHttpRequestContext,  minorId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Something wrong in medicare SubmitInHospitalClaim service.");
                _logger.LogError($"Something wrong in medicare SubmitInHospitalClaim service {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
    }
}
