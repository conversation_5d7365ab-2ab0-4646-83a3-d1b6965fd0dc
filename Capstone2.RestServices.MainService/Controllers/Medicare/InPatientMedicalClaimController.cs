﻿using Capstone2.Common.MedicareOnline.IMCW;
using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.MainService.Utility;
using Capstone2.RestServices.Medicare.Interfaces;
using Capstone2.RestServices.Medicare.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Medicare.Controllers
{
    [Route("medicare")]
    [ApiController]
    [ApiExceptionFilter]
    [Authorize]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class InPatientMedicalClaimController : BaseController
    {
        private readonly IInPatientClaimService _inPatientClaimService;
        private readonly ILogger<InPatientMedicalClaimController> _logger;
        public InPatientMedicalClaimController(IOptions<MvcOptions> options, ILogger<InPatientMedicalClaimController> logger, IInPatientClaimService inPatientClaimService, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _inPatientClaimService = inPatientClaimService;
            options.Value.InputFormatters.Insert(0, InputFormater.GetInputFormater());
        }

        [HttpPost]
        [Route("submitinpatientmedicalclaim")]
        public async Task<IActionResult> SubmitInPatientMedicalClaim([FromBody] InPatientMedicalClaimRequestType medicalClaimRequest)
        {
            var apiResponse = new ApiResponse<dynamic>();
            try
            {
                string minorId = HttpContext.Request.Headers["MinorId"].FirstOrDefault();
                apiResponse = await _inPatientClaimService.SubmitInPatientMedicalClaim(medicalClaimRequest, baseHttpRequestContext,  minorId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Something wrong in medicare SubmitInPatientMedicalClaim service.");
                _logger.LogError($"Something wrong in medicare SubmitInPatientMedicalClaim service {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
    }
}
