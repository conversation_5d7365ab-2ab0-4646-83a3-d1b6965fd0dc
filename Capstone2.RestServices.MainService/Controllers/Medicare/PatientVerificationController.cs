﻿using Capstone2.Common.MedicareOnline.OPV;
using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.MainService.Utility;
using Capstone2.RestServices.Medicare.Interfaces;
using Capstone2.RestServices.Medicare.Models;
using Capstone2.Shared.Models.Enum;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Text.Json;
using System.Threading.Tasks;
using static System.Net.Mime.MediaTypeNames;

namespace Capstone2.RestServices.Medicare.Controllers
{
    [Route("medicare")]
    [ApiController]
    [ApiExceptionFilter]
    [Authorize]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class PatientVerificationController : BaseController
    {
        private readonly IPatientVerificationService _patientVerificationService;
        private readonly ILogger<PatientVerificationController> _logger;
        public PatientVerificationController(IOptions<MvcOptions> options, ILogger<PatientVerificationController> logger, IPatientVerificationService patientVerificationService, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _logger.LogInformation("PatientVerificationController START");
            _patientVerificationService = patientVerificationService;
            options.Value.InputFormatters.Insert(0, InputFormater.GetInputFormater());
        }

        [HttpPost]
        [Route("MockMedicareValidate")]
        public async Task<IActionResult> MockMedicareValidate([FromBody] PatientVerificationRequestType patientVerificationRequest)
        {
            var apiResponse = new ApiResponse<dynamic>();
            var validMedicareNums = new List<string>
            {
                "**********",
                "**********",
                "**********",
                "**********",
                "**********",
                "**********",
                "**********",
                "**********",
                "**********",
                "**********",
                "**********",
                "**********",
                "**********",
                "**********",
                "**********",
                "**********",
                "**********",
                "**********",
                "**********",
                "**********",
                "**********",
                "**********",
                "**********",
                "**********",
                "**********",
                "**********",
                "**********",
                "**********",
                "**********",
                "**********",
                "**********",
                "**********",
                "**********",
                "**********",
                "**********",
                "**********",
                "**********",
                "**********",
                "**********",
                "**********",
                "**********",
                "**********",
                "**********",
                "**********"
            };
            try
            {
                if (patientVerificationRequest != null &&
                    patientVerificationRequest.Patient != null &&
                    patientVerificationRequest.Patient.Medicare != null &&
                    !string.IsNullOrEmpty(patientVerificationRequest.Patient.Medicare.MemberNumber) &&
                    validMedicareNums.Contains(patientVerificationRequest.Patient.Medicare.MemberNumber))
                {
                    var medicareStatus = new {
                        medicareStatus = new
                        {
                            status = new
                            {
                                code = 0,
                                text = "Valid Medicare"
                            },
                            VerificationMessage = "Valid Medicare",
                            currentMember = new
                            {
                                familyName = "Unknown",
                                givenName = "Unknown"
                            },
                            currentMembership = new
                            {
                                EntitlementCode = "Unknown",
                                VeteranNumber = "Unknown",
                                MemberNumber = patientVerificationRequest.Patient.Medicare.MemberNumber,
                                MemberRefNumber = patientVerificationRequest.Patient.Medicare.MemberRefNumber,
                            }
                        }
                    };
                    apiResponse = new ApiResponse<dynamic>
                    {
                        StatusCode = StatusCodes.Status200OK,
                        Message = "Success",
                        Result = JsonSerializer.Serialize(medicareStatus)
                    };
                }
                else
                {
                    var medicareStatus = new
                    {
                        medicareStatus = new
                        {
                            status = new
                            {
                                code = -1,
                                text = "Invalid Medicare"
                            },
                            VerificationMessage = "Invalid Medicare",
                            currentMember = new
                            {
                                familyName = "Unknown",
                                givenName = "Unknown"
                            },
                            currentMembership = new
                            {
                                EntitlementCode = "Unknown",
                                VeteranNumber = "Unknown",
                                MemberNumber = "Unknown",
                                MemberRefNumber = "Unknown",
                            }
                        }
                    };
                    apiResponse = new ApiResponse<dynamic>
                    {
                        StatusCode = StatusCodes.Status200OK,
                        Message = "Failed",
                        Result = JsonSerializer.Serialize(medicareStatus)
                    };
                }

                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Something wrong in medicare CheckPatientVerification service.");
                _logger.LogError($"Something wrong in medicare CheckPatientVerification service {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpPost]
        [Route("CheckPatientVerification")]
        public async Task<IActionResult> CheckPatientVerification([FromBody] PatientVerificationRequestType patientVerificationRequest)
        {
            _logger.LogInformation("CheckPatientVerification START");
            var apiResponse = new ApiResponse<dynamic>();
            try
            {
                apiResponse = await _patientVerificationService.IsMedicareValid(patientVerificationRequest, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Something wrong in medicare CheckPatientVerification service.");
                _logger.LogError($"Something wrong in medicare CheckPatientVerification service {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Health check endpoint
        /// </summary>
        /// <returns></returns>
        [HttpGet("healthcheck")]
        [Microsoft.AspNetCore.Authorization.AllowAnonymous]
        [TelemetryTracing(logRequest: false, logResponse: false)]
        public IActionResult HealthCheck()
        {
            return Ok(StatusCodes.Status200OK);
        }

    }
}
