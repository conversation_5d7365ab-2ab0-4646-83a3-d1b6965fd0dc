﻿using Capstone.Common.MedicareOnline.ERAW;
using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.MainService.Utility;
using Capstone2.RestServices.Medicare.Interfaces;
using Capstone2.RestServices.Medicare.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Medicare.Controllers
{
    [Route("medicare")]
    [ApiController]
    [ApiExceptionFilter]
    [Authorize]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class EclipseRemittanceAdviceController : BaseController
    {
        private readonly IERAWService _eRAWService;
        private readonly ILogger<EclipseRemittanceAdviceController> _logger;
        public EclipseRemittanceAdviceController(IOptions<MvcOptions> options, ILogger<EclipseRemittanceAdviceController> logger, IERAWService eRAWService, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _eRAWService = eRAWService;
            options.Value.InputFormatters.Insert(0, InputFormater.GetInputFormater());
        }

        [HttpPost]
        [Route("EclipseRemittanceAdviceReport")]
        public async Task<IActionResult> CallEclipseRemittanceAdviceReport([FromBody] RetrieveReportRequestType retrieveReportRequestType)
        {
            var apiResponse = new ApiResponse<dynamic>();
            try
            {
                string minorId = HttpContext.Request.Headers["MinorId"].FirstOrDefault();
                apiResponse = await _eRAWService.SubmitERemittanceAdviceReport(retrieveReportRequestType, baseHttpRequestContext, minorId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.Result = null;
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error in submitting ERA Report");
                _logger.LogError($"Error in submitting ERA Report {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }

    }
}
