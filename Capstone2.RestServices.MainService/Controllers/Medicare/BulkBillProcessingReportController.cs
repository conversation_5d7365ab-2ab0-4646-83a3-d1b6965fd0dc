﻿using Capstone.Common.MedicareOnline.BulkBill.ProcessingReport;
using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.MainService.Utility;
using Capstone2.RestServices.Medicare.Interfaces;
using Capstone2.RestServices.Medicare.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Medicare.Controllers
{
    [Route("medicare")]
    [ApiController]
    [ApiExceptionFilter]
    [Authorize]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class BulkBillProcessingReportController : BaseController
    {
        private readonly IBPRWService _bPRWService;
        private readonly ILogger<BulkBillProcessingReportController> _logger;
        public BulkBillProcessingReportController(IOptions<MvcOptions> options, ILogger<BulkBillProcessingReportController> logger, IBPRWService bPRWService, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _bPRWService = bPRWService;
            options.Value.InputFormatters.Insert(0, InputFormater.GetInputFormater());
        }

        [HttpPost]
        [Route("BulkBillProcessingReport")]
        public async Task<IActionResult> CallBulkBillProcessingReport([FromBody] BBSReportRequestType bBSReportRequestType)
        {
            var apiResponse = new ApiResponse<dynamic>();

            try
            {
                string correlationId = HttpContext.Request.Headers["TransactionId"].FirstOrDefault();
                string minorId = HttpContext.Request.Headers["MinorId"].FirstOrDefault();
                if (!string.IsNullOrWhiteSpace(correlationId))
                {
                    apiResponse = await _bPRWService.SubmitBulkBillProcessignReport(bBSReportRequestType, baseHttpRequestContext, correlationId, minorId);
                    return apiResponse.StatusCode switch
                    {
                        StatusCodes.Status200OK => Ok(apiResponse),
                        StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                        StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                        _ => BadRequest(apiResponse),
                    };
                }
                else
                {
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Result = null;
                    apiResponse.Errors.Add("TransactionId is required.");
                    return BadRequest(apiResponse);
                }
            }
            catch (Exception Ex)
            {
                apiResponse.Result = null;
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error in submitting BulkBill Processing Report");
                _logger.LogError($"Error in submitting BulkBill Processing Report {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }

    }
}
