﻿using Capstone2.Common.MedicareOnline.SDDClaim;
using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.MainService.Utility;
using Capstone2.RestServices.Medicare.Interfaces;
using Capstone2.RestServices.Medicare.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Medicare.Controllers
{
    [Route("medicare")]
    [ApiController]
    [ApiExceptionFilter]
    [Authorize]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class SameDayDeleteClaimController : BaseController
    {
        private readonly ISDDClaimService _sameDayDeleteClaimService;
        private readonly ILogger<SameDayDeleteClaimController> _logger;
        public SameDayDeleteClaimController(IOptions<MvcOptions> options, ILogger<SameDayDeleteClaimController> logger, ISDDClaimService sameDayDeleteClaimService, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _sameDayDeleteClaimService = sameDayDeleteClaimService;
            options.Value.InputFormatters.Insert(0, InputFormater.GetInputFormater());
        }

        [HttpPost]
        [Route("SubmitSameDayDeleteClaim")]
        public async Task<IActionResult> SubmitSameDayDeleteClaim([FromBody] SameDayDeleteRequestType sameDayDeleteRequest)
        {
            var apiResponse = new ApiResponse<dynamic>();
            try
            {                
                string correlationId = HttpContext.Request.Headers["TransactionId"].FirstOrDefault();
                string minorId = HttpContext.Request.Headers["MinorId"].FirstOrDefault();

                if (!string.IsNullOrWhiteSpace(correlationId))
                {
                    apiResponse = await _sameDayDeleteClaimService.SubmitForSameDayDeleteClaim(sameDayDeleteRequest, baseHttpRequestContext, correlationId, minorId);
                    return apiResponse.StatusCode switch
                    {
                        StatusCodes.Status200OK => Ok(apiResponse),
                        StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                        StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                        _ => BadRequest(apiResponse),
                    };
                }
                else
                {
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Result = null;
                    apiResponse.Errors.Add("TransactionId is required.");
                    return BadRequest(apiResponse);
                }
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Something wrong in medicare SubmitSameDayDeleteClaim service.");
                _logger.LogError($"Something wrong in medicare SubmitSameDayDeleteClaim service {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
    }
}
