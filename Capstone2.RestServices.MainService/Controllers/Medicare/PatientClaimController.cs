﻿using Capstone2.Common.MedicareOnline.PCI.General;
using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.MainService.Utility;
using Capstone2.RestServices.Medicare.Interfaces;
using Capstone2.RestServices.Medicare.Models;
using Capstone2.Shared.Models.Entities;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Medicare.Controllers
{
    [Route("medicare")]
    [ApiController]
    [ApiExceptionFilter]
    [Authorize]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class PatientClaimController : BaseController
    {
        private readonly IPatientClaimInterService _patientClaimInterService;
        private readonly ILogger<PatientClaimController> _logger;
        public PatientClaimController(IOptions<MvcOptions> options, ILogger<PatientClaimController> logger, IPatientClaimInterService patientClaimInterService, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _patientClaimInterService = patientClaimInterService;
            options.Value.InputFormatters.Insert(0, InputFormater.GetInputFormater());
        }

        [HttpPost]
        [Route("SubmitClaim/{serviceType}")]
        public async Task<IActionResult> PatientClaimGeneral([FromBody] PatientClaimInteractiveRequestType patientClaimRequest, string serviceType)
        {
            var apiResponse = new ApiResponse<dynamic>();
            try
            {
                string correlationId = HttpContext.Request.Headers["TransactionId"].FirstOrDefault();
                string minorId = HttpContext.Request.Headers["MinorId"].FirstOrDefault();
                apiResponse = await _patientClaimInterService.SubmitPatientClaimIGeneral(patientClaimRequest, serviceType, baseHttpRequestContext, correlationId, minorId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Something wrong in medicare PatientClaimGeneral service.");
                _logger.LogError($"Something wrong in medicare PatientClaimGeneral service {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpGet]
        [Route("GetLodgmentAndStatementTags/{invoicementDetailsId}/{transactionId}")]
        public async Task<IActionResult> GetLodgmentAndStatementTags(long invoicementDetailsId, string transactionId)
        {
            var apiResponse = new ApiResponse<LodgmentAndStatementTemplateTags>();
            try
            {
                apiResponse = await _patientClaimInterService.GetLodgmentAndStatementTags(invoicementDetailsId, transactionId, baseHttpRequestContext);

                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Something wrong in medicare GetLodgmentAndStatementTags service.");
                _logger.LogError($"Something wrong in medicare GetLodgmentAndStatementTags service {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
    }
}
