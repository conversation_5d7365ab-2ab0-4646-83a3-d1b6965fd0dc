﻿using Capstone2.Common.MedicareOnline.DVAW;
using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.MainService.Utility;
using Capstone2.RestServices.Medicare.Interfaces;
using Capstone2.RestServices.Medicare.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Medicare.Controllers
{
    [Route("medicare")]
    [ApiController]
    [ApiExceptionFilter]
    [Authorize]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class VeteranClaimController : BaseController
    {
        public readonly IDVAClaimService _dVAClaimService;
        private readonly ILogger<VeteranClaimController> _logger;

        public VeteranClaimController(IOptions<MvcOptions> options, IDVAClaimService dVAClaimService, ILogger<VeteranClaimController> logger, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _dVAClaimService = dVAClaimService;
            _logger = logger;
            options.Value.InputFormatters.Insert(0, InputFormater.GetInputFormater());
        }
        [HttpPost]
        [Route("CallVeteranClaim/{serviceType}")]
        public async Task<IActionResult> CallVeteranClaim([FromBody] DVAClaimRequestType dVAClaimRequestType, string serviceType)
        {
            var apiResponse = new ApiResponse<dynamic>();

            try
            {
                string minorId = HttpContext.Request.Headers["MinorId"].FirstOrDefault();
                apiResponse = await _dVAClaimService.SubmitVeteranClaimGeneral(dVAClaimRequestType, serviceType, baseHttpRequestContext, minorId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.Result = null;
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error during DVA claim submission");
                _logger.LogError($"Error during DVA claim submission {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }

    }
}
