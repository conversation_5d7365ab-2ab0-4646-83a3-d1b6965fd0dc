﻿using Capstone2.Common.MedicareOnline.OVVW;
using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.MainService.Utility;
using Capstone2.RestServices.Medicare.Interfaces;
using Capstone2.RestServices.Medicare.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Medicare.Controllers
{
    [Route("medicare")]
    [ApiController]
    [ApiExceptionFilter]
    [Authorize]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class VeteranPatientVerificationController : BaseController
    {
        private readonly IVeteranPatientVerificationService _veteranPatientVerificationService;
        private readonly ILogger<VeteranPatientVerificationController> _logger;
        public VeteranPatientVerificationController(IOptions<MvcOptions> options, ILogger<VeteranPatientVerificationController> logger, IVeteranPatientVerificationService veteranPatientVerificationService, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _veteranPatientVerificationService = veteranPatientVerificationService;
            options.Value.InputFormatters.Insert(0, InputFormater.GetInputFormater());
        }

        [HttpPost]
        [Route("CheckVeteranVerification")]
        public async Task<IActionResult> CheckVeteranVerification([FromBody] VeteranVerificationRequestType veteranVerificationRequest)
        {
            var apiResponse = new ApiResponse<dynamic>();
            try
            {
                apiResponse = await _veteranPatientVerificationService.IsVeteranValid(veteranVerificationRequest, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Something wrong in medicare CheckVeteranVerification service.");
                _logger.LogError($"Something wrong in medicare CheckVeteranVerification service {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

    }
}
