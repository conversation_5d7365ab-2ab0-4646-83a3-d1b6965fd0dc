﻿using Capstone2.Common.MedicareOnline.GPRW;
using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.MainService.Utility;
using Capstone2.RestServices.Medicare.Interfaces;
using Capstone2.RestServices.Medicare.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Medicare.Controllers
{
    [Route("medicare")]
    [ApiController]
    [ApiExceptionFilter]
    [Authorize]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class ParticipantFundsController : BaseController
    {
        private readonly IParticipantFundsService _participantFundsService;
        private readonly ILogger<ParticipantFundsController> _logger;
        public ParticipantFundsController(IOptions<MvcOptions> options, ILogger<ParticipantFundsController> logger, IParticipantFundsService participantFundsService, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _participantFundsService = participantFundsService;
            options.Value.InputFormatters.Insert(0, InputFormater.GetInputFormater());
        }

        [HttpPost]
        [Route("GetParticipants")]
        public async Task<IActionResult> GetParticipants([FromBody] GetParticipantsRequestType getParticipantsRequestType)
        {
            var apiResponse = new ApiResponse<dynamic>();
            try
            {
                apiResponse = await _participantFundsService.SubmitGetParticipants(getParticipantsRequestType, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Something wrong in medicare GetParticipants service.");
                _logger.LogError($"Something wrong in medicare GetParticipants service {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

    }
}
