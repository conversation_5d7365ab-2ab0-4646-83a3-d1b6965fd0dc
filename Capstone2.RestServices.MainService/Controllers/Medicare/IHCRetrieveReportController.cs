﻿using Capstone.Common.MedicareOnline.RTVW.IHC;
using Capstone2.Common.MedicareOnline.RTVW;
using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.MainService.Utility;
using Capstone2.RestServices.Medicare.Interfaces;
using Capstone2.RestServices.Medicare.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Medicare.Controllers
{
    [Route("medicare")]
    [ApiController]
    [ApiExceptionFilter]
    [Authorize]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class RetrieveReportIHCController : BaseController
    {
        private readonly IIHCRetrieveReportService _retrieveReportService;
        private readonly ILogger<RetrieveReportIHCController> _logger;
        public RetrieveReportIHCController(IOptions<MvcOptions> options, ILogger<RetrieveReportIHCController> logger, IIHCRetrieveReportService retrieveReportService, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _retrieveReportService = retrieveReportService;
            options.Value.InputFormatters.Insert(0, InputFormater.GetInputFormater());
        }

        [HttpPost]
        [Route("RetrieveReportIHC")]
        public async Task<IActionResult> CallRetrieveReport([FromBody] IHCRetrieveReportRequestType retrieveReportRequestType)
        {
            var apiResponse = new ApiResponse<dynamic>();

            try
            {
                string minorId = HttpContext.Request.Headers["MinorId"].FirstOrDefault();
                apiResponse = await _retrieveReportService.SubmitRetrieveReport(retrieveReportRequestType, baseHttpRequestContext, minorId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };

            }
            catch (Exception Ex)
            {
                apiResponse.Result = null;
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error in submitting Retrieve Report");
                _logger.LogError($"Error in submitting Retrieve Report {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }

    }
}
