﻿using Capstone2.Common.MedicareOnline.OEC;
using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.MainService.Utility;
using Capstone2.RestServices.Medicare.Interfaces;
using Capstone2.RestServices.Medicare.Models;
using Capstone2.Shared.Models.Entities;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Medicare.Controllers
{
    [Route("medicare")]
    [ApiController]
    [ApiExceptionFilter]
    [Authorize]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class OnlineEligibilityCheckController : BaseController
    {
        private readonly IOnlineEligibilityCheckService _onlineEligibilityCheckService;
        private readonly ILogger<PatientClaimController> _logger;
        public OnlineEligibilityCheckController(IOptions<MvcOptions> options, ILogger<PatientClaimController> logger, IOnlineEligibilityCheckService onlineEligibilityCheckService, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _onlineEligibilityCheckService = onlineEligibilityCheckService;
            options.Value.InputFormatters.Insert(0, InputFormater.GetInputFormater());
        }


        /// <summary>
        /// Patient Online Eligibility Check Claim(OEC)/(ECF)/(ECM)
        /// </summary>
        /// <param name="patientOECClaimRequest">patientOECClaimRequest</param>
        /// <returns></returns>
        [HttpPost]
        [Route("onlineeligibilitycheck")]
        public async Task<IActionResult> CallOnlineEligibilityCheckClaim([FromBody] OnlineEligibilityCheckRequestType patientOECClaimRequest)
        {
            var apiResponse = new ApiResponse<dynamic>();
            try
            {
                string minorId = HttpContext.Request.Headers["MinorId"].FirstOrDefault();

                apiResponse = await _onlineEligibilityCheckService.SubmitPatientOECClaim(patientOECClaimRequest, baseHttpRequestContext, minorId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Something wrong in medicare CallOnlineEligibilityCheckClaim service.");
                _logger.LogError($"Something wrong in medicare CallOnlineEligibilityCheckClaim service {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Patient Online Eligibility Check Claim Template Tags Data
        /// </summary>
        /// <param name="patientOECClaimRequest">patientOECClaimRequest</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetOECTemplateTagsData/{invoicementDetailsId}/{transactionId}")]
        public async Task<IActionResult> GetOECTemplateTagsData(long invoicementDetailsId, string transactionId)
        {
            var apiResponse = new ApiResponse<OECTemplateTags>();
            try
            {
                apiResponse = await _onlineEligibilityCheckService.GetOECTemplateTagsData(invoicementDetailsId, transactionId, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Something wrong in medicare CallOnlineEligibilityCheckClaim service.");
                _logger.LogError($"Something wrong in medicare CallOnlineEligibilityCheckClaim service {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
    }
}
