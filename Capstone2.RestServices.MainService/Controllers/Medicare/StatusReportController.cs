﻿using Capstone2.Common.MedicareOnline.STSW;
using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.MainService.Utility;
using Capstone2.RestServices.Medicare.Interfaces;
using Capstone2.RestServices.Medicare.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Medicare.Controllers
{
    [Route("medicare")]
    [ApiController]
    [ApiExceptionFilter]
    [Authorize]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class StatusReportController : BaseController
    {
        private readonly IStatusReportService _statusReportService;
        private readonly ILogger<StatusReportController> _logger;
        public StatusReportController(IOptions<MvcOptions> options, ILogger<StatusReportController> logger, IStatusReportService statusReportService, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _statusReportService = statusReportService;
            options.Value.InputFormatters.Insert(0, InputFormater.GetInputFormater());
        }

        [HttpPost]
        [Route("StatusReport")]
        public async Task<IActionResult> CallStatusReport([FromBody] StatusReportRequestType statusReportRequestType)
        {
            var apiResponse = new ApiResponse<dynamic>();

            try
            {
                string minorId = HttpContext.Request.Headers["MinorId"].FirstOrDefault();
                apiResponse = await _statusReportService.SubmitStatusReport(statusReportRequestType, baseHttpRequestContext, minorId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
                
            }
            catch (Exception Ex)
            {
                apiResponse.Result = null;
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error in submitting Status Report");
                _logger.LogError($"Error in submitting Status Report {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }

    }
}
