﻿using Capstone2.Common.MedicareOnline.BBSWG;
using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.MainService.Utility;
using Capstone2.RestServices.Medicare.Interfaces;
using Capstone2.RestServices.Medicare.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Medicare.Controllers
{
    [Route("medicare")]
    [ApiController]
    [ApiExceptionFilter]
    [Authorize]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class BulkBillStoreForwardController : BaseController
    {
        private readonly IBulkBillService _bulkBillService;
        private readonly ILogger<BulkBillStoreForwardController> _logger;
        public BulkBillStoreForwardController(IOptions<MvcOptions> options, ILogger<BulkBillStoreForwardController> logger, IBulkBillService bulkBillService, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _bulkBillService = bulkBillService;
            options.Value.InputFormatters.Insert(0, InputFormater.GetInputFormater());
        }

        [HttpPost]
        [Route("CallBulkBill/{serviceType}")]
        public async Task<IActionResult> CallBulkBill([FromBody] BulkBillStoreForwardRequestType bulkBillStoreForwardRequestType, string serviceType)
        {
            var apiResponse = new ApiResponse<dynamic>();

            try
            {
                string minorId = HttpContext.Request.Headers["MinorId"].FirstOrDefault();
                apiResponse = await _bulkBillService.SubmitBulkBillGeneral(bulkBillStoreForwardRequestType, serviceType, baseHttpRequestContext, minorId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.Result = null;
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error in submitting BulkBill Claim");
                _logger.LogError($"Error in submitting BulkBill Claim {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }

    }
}
