﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.Company.Interfaces;
using Capstone2.RestServices.Company.Models;
using Capstone2.RestServices.MainService.Utility;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Company.Controllers
{
    [ApiController]
    [Route("company")]
    //[Authorize]
    [ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class CompanyController : BaseController
    {
        private readonly ILogger<CompanyController> _logger;
        private readonly ICompanyDetailsBAL _companyDetailsBAL;
        private readonly ICategoryMasterBAL _categoryMasterBAL;
        public CompanyController(IOptions<MvcOptions> options, ILogger<CompanyController> logger, ICompanyDetailsBAL companyDetailsBAL, ICategoryMasterBAL categoryMasterBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _categoryMasterBAL = categoryMasterBAL;
            _companyDetailsBAL = companyDetailsBAL;
            options.Value.InputFormatters.Insert(0, InputFormater.GetInputFormater());
        }

        /// <summary>
        /// Method to fetch internal companies
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("internal_companies")]
        [ProducesResponseType(typeof(ApiResponse<List<CompanyResponse>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetInternalCompanies()
        {
            var apiResponse = new ApiResponse<List<CompanyResponse>>();
            try
            {
                apiResponse = await _companyDetailsBAL.GetCompanies(baseHttpRequestContext.OrgId, (short)CompanyType.InternalCompany);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Internal Companies cannot be fetched at this time.");
                _logger.LogError($"Internal Companies cannot be fetched at this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to add a new company
        /// </summary>
        /// <param name="companyDetail"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("company_details")]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
        public async Task<IActionResult> AddCompany([FromBody] Models.CompanyDetail companyDetail)
        {
            var apiResponse = new ApiResponse<string>();
            try
            {
                apiResponse = await _companyDetailsBAL.AddCompany(companyDetail, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    StatusCodes.Status403Forbidden => new ObjectResult(apiResponse) { StatusCode = StatusCodes.Status403Forbidden },
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Company cannot be added at this time.Please try again later.");
                _logger.LogError($"Company cannot be added at this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to update a company
        /// </summary>
        /// <param name="inputInternalCompanyDetail"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("company_details/{id}")]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
        public async Task<IActionResult> EditCompany(int id, [FromBody] Models.CompanyDetail inputCompanyDetail)
        {
            var apiResponse = new ApiResponse<string>();
            try
            {
                apiResponse = await _companyDetailsBAL.EditCompany(id, inputCompanyDetail, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    StatusCodes.Status403Forbidden => new ObjectResult(apiResponse) { StatusCode = StatusCodes.Status403Forbidden },
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Company cannot be Edited.Please try again later.");
                _logger.LogError($"Company cannot be Edited {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to validate if the HPIO number  already exists
        /// </summary>
        /// <param name="search_term"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("company_details/hpionumber/verify")]
        [ProducesResponseType(typeof(ApiResponse<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> CheckHPIONumber(Int64 search_term)
        {
            var apiResponse = new ApiResponse<bool>();
            try
            {
                apiResponse = await _companyDetailsBAL.CheckHPIONumber(search_term, baseHttpRequestContext.OrgId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error while verifying the HPIO number.");
                _logger.LogError($"Error while verifying the HPIO number {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }

        /// <summary>
        /// Method to validate if the ABN number  already exists
        /// </summary>
        /// <param name="search_term"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("company_details/abn/verify")]
        [ProducesResponseType(typeof(ApiResponse<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> CheckABN(Int64 search_term)
        {
            var apiResponse = new ApiResponse<bool>();
            try
            {
                apiResponse = await _companyDetailsBAL.CheckABN(search_term, baseHttpRequestContext.OrgId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error while verifying the ABN.");
                _logger.LogError($"Error while verifying the ABN {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to validate if the ACN   already exists
        /// </summary>
        /// <param name="search_term"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("company_details/acn/verify")]
        [ProducesResponseType(typeof(ApiResponse<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> CheckACN(Int64 search_term)
        {
            var apiResponse = new ApiResponse<bool>();
            try
            {
                apiResponse = await _companyDetailsBAL.CheckACN(search_term, baseHttpRequestContext.OrgId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error while verifying the ACN.");
                _logger.LogError($"Error while verifying the ACN {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to fetch internal companies
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("company_details/internalcompany")]
        [ProducesResponseType(typeof(ApiResponse<QueryResultList<CompanyListModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ListInternalCompanies([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<QueryResultList<CompanyListModel>>();
            try
            {
                apiResponse = await _companyDetailsBAL.ListInternalCompanies(baseHttpRequestContext.OrgId, queryModel, (short)CompanyType.InternalCompany, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of Internal Companies could not be fetched during this time.");
                _logger.LogError($"List of Internal Companies could not be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


        /// <summary>
        /// Method to Get internal company details
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("company_details/{id}")]
        [ProducesResponseType(typeof(ApiResponse<InputGetCompany>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetCompanyDetails(long id)
        {
            var apiResponse = new ApiResponse<InputGetCompany>();
            try
            {
                apiResponse = await _companyDetailsBAL.GetCompanyDetailsAsync(baseHttpRequestContext.OrgId, id, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    StatusCodes.Status403Forbidden => new ObjectResult(apiResponse) { StatusCode = StatusCodes.Status403Forbidden },
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add(" Company details could not be fetched during this time.");
                _logger.LogError($"Company details could not be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to add a new Label
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("label")]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
        public async Task<IActionResult> AddLabel([FromBody] Label inputLabel)
        {
            var apiResponse = new ApiResponse<string>();
            try
            {
                apiResponse = await _companyDetailsBAL.AddLabel(inputLabel, baseHttpRequestContext.OrgId, baseHttpRequestContext.UserId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Labels cannot be added at this time.Please try again later.");
                _logger.LogError($"Labels cannot be added at this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to List of Labels
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("labels")]
        [ProducesResponseType(typeof(ApiResponse<List<Label>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetLabels(string st)
        {
            var apiResponse = new ApiResponse<List<Label>>();
            try
            {
                apiResponse = await _companyDetailsBAL.GetLabels(st, baseHttpRequestContext.OrgId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Could not get the Labels at this time.");
                _logger.LogError($"Could not get the Labels at this time {Ex.Message} {Ex.StackTrace}");
                return Unauthorized(apiResponse);
            }
        }

        /// <summary>
        /// fetch list of categories from CategoryMaster
        /// </summary>
        /// <param name="st"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("category_master")]
        [ProducesResponseType(typeof(ApiResponse<QueryResultList<CategoryMaster>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ListCategoryMaster([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<QueryResultList<CategoryMaster>>();
            try
            {
                apiResponse = await _categoryMasterBAL.ListCategoryMaster(queryModel, baseHttpRequestContext.OrgId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Category Types could not be fetched.");
                _logger.LogError($"Category Types could not be fetched {Ex.Message} {Ex.StackTrace}");
                return Unauthorized(apiResponse);
            }
        }

        /// <summary>
        /// Method to add a new Label
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("category_master")]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
        public async Task<IActionResult> AddCategoryMaster([FromBody] CategoryMaster categoryMaster)
        {
            var apiResponse = new ApiResponse<string>();
            try
            {
                apiResponse = await _categoryMasterBAL.AddCategoryMaster(categoryMaster, baseHttpRequestContext.OrgId, baseHttpRequestContext.UserId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Category Cannot be added at this time.Please try again later.");
                _logger.LogError($"Category Cannot be added at this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to fetch External companies
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("company_details")]
        [ProducesResponseType(typeof(ApiResponse<QueryResultList<ListCompanyDetails>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ListCompanies([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<QueryResultList<ListCompanyDetails>>();
            try
            {
                apiResponse = await _companyDetailsBAL.ListCompanies(baseHttpRequestContext, queryModel);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    StatusCodes.Status403Forbidden => new ObjectResult(apiResponse) { StatusCode = StatusCodes.Status403Forbidden },
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of Companies could not be fetched during this time.");
                _logger.LogError($"List of Companies could not be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to list providers for a company
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("user_details/{companyId}")]
        [ProducesResponseType(typeof(ApiResponse<QueryResultList<Models.UserDetail>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ListProviders([FromQuery] QueryModel queryModel, int companyId)
        {
            var apiResponse = new ApiResponse<QueryResultList<Models.UserDetail>>();
            try
            {
                apiResponse = await _companyDetailsBAL.ListProviders(companyId, baseHttpRequestContext.OrgId, queryModel);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of Providers could not be fetched during this time.");
                _logger.LogError($"List of Providers could not be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to get company details by user id
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("usercompanyassoc_details/{userId}")]
        [ProducesResponseType(typeof(ApiResponse<Models.UserCompanyAddress>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetProviderCompanyAddress(int userId)
        {
            var apiResponse = new ApiResponse<Models.UserCompanyAddress>();
            try
            {
                apiResponse = await _companyDetailsBAL.FetchProviderCompanyAddress(userId, baseHttpRequestContext.OrgId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Provider company detail could not be fetched during this time.");
                _logger.LogError($"Provider company detail could not be fetched during this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to Get Organisation Details
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("Organisation/{id}")]
        [ProducesResponseType(typeof(ApiResponse<OrganisationView>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetOrganisation(long id)
        {
            var apiResponse = new ApiResponse<OrganisationView>();
            try
            {
                apiResponse = await _companyDetailsBAL.GetOrganisationAsync(baseHttpRequestContext.OrgId, id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add(" Organisation could not be fetched during this time.");
                _logger.LogError($"Organisation could not be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
        /// <summary>
        /// api to calcuclate the gst inc fees
        /// </summary>
        /// <param name="id"></param>
        /// <param name="fee"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("organisation/{id}/calculateGst")]
        [ProducesResponseType(typeof(ApiResponse<decimal>), StatusCodes.Status200OK)]
        public async Task<IActionResult> CalculateGst(long id,[FromQuery] decimal fee)
        {
            var apiResponse = new ApiResponse<decimal>();
            try
            {
                apiResponse = await _companyDetailsBAL.CalculateGst(baseHttpRequestContext, id,fee);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = default(decimal);
                apiResponse.Errors.Add("Gst included fee cannot be computed during this time.");
                _logger.LogError($"Gst included fee cannot be computed during this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
        [HttpGet("healthcheck")]
        [AllowAnonymous]
        [TelemetryTracing(logRequest: false, logResponse: false)]
        public IActionResult HealthCheck()
        {
            return Ok(StatusCodes.Status200OK);
        }

        /// <summary>
        /// Method to fetch minimal details of company like id,name etc
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("company_details_info")]
        [ProducesResponseType(typeof(ApiResponse<List<CompanyDetailInfo>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ListCompanyDetailInfo([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<List<CompanyDetailInfo>>();
            try
            {
                apiResponse = await _companyDetailsBAL.ListCompanyDetailInfo(baseHttpRequestContext.OrgId, queryModel);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of Companies could not be fetched during this time.");
                _logger.LogError($"List of Companies could not be fetched during this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpGet]
        [Route("organisation/{id}/estimate_validity")]
        [ProducesResponseType(typeof(ApiResponse<short>), StatusCodes.Status200OK)]
        public async Task<IActionResult> FetchEstimateValidity(long id)
        {
            var apiResponse = new ApiResponse<short>();
            try
            {
                apiResponse = await _companyDetailsBAL.FetchEstimateValidity(id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = default(short);
                apiResponse.Errors.Add("EstimateValidity cannot be fetched at this time.");
                _logger.LogError($"EstimateValidity cannot be fetched at this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


        /// <summary>
        /// Method to get list of HPIOServiceDetails by CompanyID
        /// </summary>
        /// <param name="company_id">CompanyID</param>
        /// <returns>List of HPIOServiceDetails</returns>
        [HttpGet]
        [Route("{company_id}/hiservice/hpio_details")]
        public async Task<IActionResult> GetHPIOServiceDetails(int company_id)
        {
            var apiResponse = new ApiResponse<List<HIServiceDetailLogs>>();
            try
            {
                apiResponse = await _companyDetailsBAL.GetHIServiceDetails(baseHttpRequestContext, company_id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting HPIOServiceDetails Data");
                _logger.LogError($"Error while getting HPIOServiceDetails Data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to add a new HPIOServiceDetails
        /// </summary>
        /// <param name="company_id"></param>
        /// <param name="inputHIServiceDetail"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("{company_id}/hiservice/hpionumber")]
        public async Task<IActionResult> AddHPIOServiceDetails(int company_id, [FromBody] InputHIServiceDetails inputHIServiceDetail)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _companyDetailsBAL.AddHIServiceDetail(baseHttpRequestContext, company_id, inputHIServiceDetail);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while creating HPIOServiceDetails.Please check all variables");
                _logger.LogError($"Error while creating HPIOServiceDetails {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to update a HPIOServiceDetails
        /// </summary>
        /// <param name="company_id">company_id</param>
        /// <param name="id"></param>
        /// <param name="inputHIServiceDetail"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("{company_id}/hiservice/hpionumber/{id}")]
        public async Task<IActionResult> EditHPIOServiceDetails(int company_id, long id, [FromBody] InputHIServiceDetails inputHIServiceDetail)
        {
            var apiResponse = new ApiResponse<string>();

            try
            {
                apiResponse = await _companyDetailsBAL.EditHIServiceDetail(baseHttpRequestContext, company_id, id, inputHIServiceDetail);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("HPIOServiceDetails cannot be Edited.Please try again later.");
                _logger.LogError($"HPIOServiceDetails cannot be Edited {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

    }

}
