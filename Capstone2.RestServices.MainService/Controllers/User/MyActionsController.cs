﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.User.Interfaces;
using Capstone2.RestServices.User.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Capstone2.RestServices.User.Controller
{

    [ApiController]
    [Route("user")]
    [ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class MyActionsController : BaseController
    {
        private readonly ILogger<UserController> _logger;
        private readonly IMyActionBAL _myActionBAL;
        public MyActionsController(ILogger<UserController> logger, IMyActionBAL myActionBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _myActionBAL = myActionBAL;
        }

        /// <summary>
        /// Method to add an Action task
        /// </summary>
        /// <param name="inputAction"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("my_actions")]
        public async Task<IActionResult> AddMyAction(long user_id, [FromBody] MyAction inputAction)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _myActionBAL.AddMyActionBAL(user_id, inputAction, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error while creating MyActions. Please check all variables.");
                _logger.LogError($"Error while creating MyActions {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }

        [HttpPost]
        [Route("my_actions/{action_id}/hide")]
        public async Task<IActionResult> HideMyAction(long action_id)
        {
            var apiResponse = new ApiResponse<bool>();
            try
            {
                apiResponse = await _myActionBAL.HideMyActionBAL(action_id, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    StatusCodes.Status404NotFound => NotFound(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error while hiding MyAction.");
                _logger.LogError($"Error while hiding MyAction {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to add a Comment
        /// </summary>
        /// <param name="inputAction"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("my_actions/{action_id}/comments")]
        public async Task<IActionResult> AddComment(long action_id, [FromBody] Comment inputComment)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _myActionBAL.AddCommentBAL(action_id, inputComment, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error while creating Comment. Please check all variables.");
                _logger.LogError($"Error while creating Comment {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }

        /// <summary>
        /// Method to fetch List of Comments
        /// </summary>
        /// <param name="action_id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("{action_id}/comments")]
        public async Task<IActionResult> ListComments(long action_id)
        {
            var apiResponse = new ApiResponse<List<CommentView>>();

            try
            {
                apiResponse = await _myActionBAL.GetCommentsBAL(action_id, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of Comments could not be fetched during this time.");
                _logger.LogError($"List of Comments could not be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to fetch List of my Actions
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("my_actions")]
        public async Task<IActionResult> ListMyActions([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<QueryResultList<MyActionList>>();

            try
            {
                apiResponse = await _myActionBAL.GetMyActionsBAL(queryModel, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    StatusCodes.Status403Forbidden => new ObjectResult(apiResponse) { StatusCode = StatusCodes.Status403Forbidden },
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of my actions could not be fetched during this time.");
                _logger.LogError($"List of my actions could not be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to update an Action task
        /// </summary>
        /// <param name="inputAction"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("my_actions/{id}")]
        public async Task<IActionResult> EditMyAction(long id, [FromBody] MyAction inputAction)
        {
            var apiResponse = new ApiResponse<string>();
            try
            {

                apiResponse = await _myActionBAL.EditMyActionBAL(id, inputAction, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Action cannot be Edited.Please try again later.");
                _logger.LogError($"Action cannot be Edited {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to fetch an action based on id.
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("my_actions/{id}")]
        public async Task<IActionResult> GetMyAction(long id)
        {
            var apiResponse = new ApiResponse<MyActionList>();

            try
            {
                apiResponse = await _myActionBAL.GetMyActionBAL(baseHttpRequestContext, id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting Action Details");
                _logger.LogError($"Error while getting Action Details {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to fetch an action based on id.
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("my_actions/notifications_count/{user_id}")]
        [ProducesResponseType(typeof(ApiResponse<int>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetMyActionsNotificationCount(long user_id)
        {
            var apiResponse = new ApiResponse<int>();

            try
            {
                apiResponse = await _myActionBAL.GetMyActionsNotificationCount(baseHttpRequestContext, user_id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = 0;
                apiResponse.Errors.Add("Error while getting Notification Count");
                _logger.LogError($"Error while getting Notification Count {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
    }
}
