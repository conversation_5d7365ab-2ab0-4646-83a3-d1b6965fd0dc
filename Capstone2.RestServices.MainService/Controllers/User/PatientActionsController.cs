﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.User.Interfaces;
using Capstone2.RestServices.User.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Capstone2.RestServices.User.Controller
{
    [ApiController]
    [Route("user")]
    [ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class PatientActionsController : BaseController
    {
        private readonly ILogger<PatientActionsController> _logger;
        private readonly IPatientActionsBAL _PatientActionsBAL;
        public PatientActionsController(ILogger<PatientActionsController> logger, IPatientActionsBAL PatientActionsBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _PatientActionsBAL = PatientActionsBAL;
        }

        /// <summary>
        /// Method to add an Action task
        /// </summary>
        /// <param name="inputAction"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("patient_actions")]
        [ProducesResponseType(typeof(ApiResponse<long?>), StatusCodes.Status200OK)]
        public async Task<IActionResult> AddPatientActions([FromBody] List<PatientAction> inputAction)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _PatientActionsBAL.AddPatientActions(inputAction, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                // TODO: will remove once rectify the error
                apiResponse.Errors.Add("Error while creating PatientActionss. Please check all variables.");
                _logger.LogError($"Error while creating PatientActionss {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }

        [HttpPost]
        [Route("patient_actions/{action_id}/hide")]
        [ProducesResponseType(typeof(ApiResponse<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> HidePatientAction(long action_id)
        {
            var apiResponse = new ApiResponse<bool>();
            try
            {
                apiResponse = await _PatientActionsBAL.HidePatientActionBAL(action_id, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    StatusCodes.Status404NotFound => NotFound(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error while hiding PatientAction.");
                _logger.LogError($"Error while hiding PatientAction {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to fetch List of my Actions
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("patient_actions")]
        [ProducesResponseType(typeof(ApiResponse<QueryResultList<PatientActionsView>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ListPatientActionss([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<QueryResultList<PatientActionsView>>();

            try
            {
                apiResponse = await _PatientActionsBAL.GetPatientActions(queryModel, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    StatusCodes.Status403Forbidden => new ObjectResult(apiResponse) { StatusCode = StatusCodes.Status403Forbidden },
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of actions could not be fetched during this time.");
                _logger.LogError($"List of actions could not be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

       /// <summary>
        /// Method to update multiple Actions
        /// </summary>
        /// <param name="inputAction"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("patient_actions")]
        [ProducesResponseType(typeof(ApiResponse<long?>), StatusCodes.Status200OK)]
        public async Task<IActionResult> EditPatientActions([FromBody] List<PatientAction> inputAction)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _PatientActionsBAL.EditPatientActions(inputAction, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Action cannot be Edited.Please try again later.");
                _logger.LogError($"Action cannot be Edited {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to update multiple Actions
        /// </summary>
        /// <param name="inputAction"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("patient_actions_by_pathology_id")]
        [ProducesResponseType(typeof(ApiResponse<long?>), StatusCodes.Status200OK)]
        public async Task<IActionResult> UpdatePatientActionsByPathologyID([FromQuery] long id, [FromQuery] long oAid, [FromQuery] long nAid)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _PatientActionsBAL.UpdatePatientActionsByPathologyID(id, oAid, nAid, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Action cannot be Edited.Please try again later.");
                _logger.LogError($"Action cannot be Edited {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


        /// <summary>
        /// Delete multiple PatientActions
        /// </summary>
        /// <param name="pathologyRequestId"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("patient_actions_by_pathology_id")]
        [ProducesResponseType(typeof(ApiResponse<long?>), StatusCodes.Status200OK)]
        public async Task<IActionResult> DeletePatientActionsByPathologyID([FromQuery] long id)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _PatientActionsBAL.DeletePatientActionsByPathologyId(id, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Action cannot be Deleted.Please try again later.");
                _logger.LogError($"Action cannot be Deleted {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Delete multiple PatientActions
        /// </summary>
        /// <param name="deleteIds"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("patient_actions")]
        [ProducesResponseType(typeof(ApiResponse<long?>), StatusCodes.Status200OK)]
        public async Task<IActionResult> DeletePatientActions([FromQuery] string deleteIds)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _PatientActionsBAL.DeletePatientActions(deleteIds, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Action cannot be Deleted.Please try again later.");
                _logger.LogError($"Action cannot be Deleted {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// ListPatientActionsByMediaId
        /// </summary>
        /// <param name="media_id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("patient_actions_by_media_id/{media_id}")]
        [ProducesResponseType(typeof(ApiResponse<List<PatientAction>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ListPatientActionsByMediaId(long media_id)
        {
            var apiResponse = new ApiResponse<List<PatientAction>>();

            try
            {
                apiResponse = await _PatientActionsBAL.GetPatientActionsByMediaId(media_id, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of actions could not be fetched during this time.");
                _logger.LogError($"Error while creating MyActions {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// ListPatientActionsByPathologyId
        /// </summary>
        /// <param name="pathology_id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("patient_actions_by_pathology_id/{pathology_id}")]
        [ProducesResponseType(typeof(ApiResponse<List<PatientActionsView>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ListPatientActionsByPathologyId(long pathology_id)
        {   
            var apiResponse = new ApiResponse<List<PatientActionsView>>();

            try
            {
                apiResponse = await _PatientActionsBAL.GetPatientActionsByPathologyId(pathology_id, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of actions could not be fetched during this time.");
                _logger.LogError($"List of actions could not be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Get PatientActions By LetterId
        /// </summary>
        /// <param name="letter_id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("patient_actions_by_letter_id/{letter_id}")]
        [ProducesResponseType(typeof(ApiResponse<List<PatientAction>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ListPatientActionsByLetterId(long letter_id)
        {
            var apiResponse = new ApiResponse<List<PatientAction>>();

            try
            {
                apiResponse = await _PatientActionsBAL.GetPatientActionsByLetterId(letter_id, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of actions could not be fetched during this time.");
                _logger.LogError($"List of actions could not be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// GetPatientActionsNotificationCount
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("patient_actions/notification_count")]
        [ProducesResponseType(typeof(ApiResponse<List<PatientActionsNotificationCount>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetPatientActionsNotificationCount()
        {
            var apiResponse = new ApiResponse<List<PatientActionsNotificationCount>>();

            try
            {
                apiResponse = await _PatientActionsBAL.GetPatientActionsNotificationCount(baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Actions count could not be fetched during this time.");
                _logger.LogError($"Actions count could not be fetched  {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// GetPatientActionsNotificationCountByUser
        /// </summary>
        /// <param name="user_id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("patient_actions/user_notification_count/{user_id}")]
        [ProducesResponseType(typeof(ApiResponse<List<PatientActionsNotificationCount>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetPatientActionsNotificationCountByUser(long user_id)
        {
            var apiResponse = new ApiResponse<List<PatientActionsNotificationCount>>();

            try
            {
                apiResponse = await _PatientActionsBAL.GetPatientActionsNotificationCountByUser(user_id, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("actions count could not be fetched during this time.");
                _logger.LogError($"actions count could not be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// EditPatientActionStatus by pathology_request_id
        /// </summary>
        /// <param name="pathology_request_id"></param>
        /// <param name="statusId"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("patient_actions/parent_status/{pathology_request_id}")]
        [ProducesResponseType(typeof(ApiResponse<long?>), StatusCodes.Status200OK)]
        public async Task<IActionResult> EditParentPatientActionStatus(long pathology_request_id, [FromBody] short statusId)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _PatientActionsBAL.EditParentPatientActionStatus(pathology_request_id, statusId, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Action cannot be Edited.Please try again later.");
                _logger.LogError($"Action cannot be Edited {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to add a PatientActionComment
        /// </summary>
        /// <param name="inputAction"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("patient_actions/{action_id}/comments")]
        public async Task<IActionResult> AddPatientActionsComment(long action_id, [FromBody] PatientActionComment inputComment)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _PatientActionsBAL.AddPACommentBAL(action_id, inputComment, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error while creating Comment. Please check all variables.");
                _logger.LogError($"Error while creating Comment {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }

        /// <summary>
        /// Method to fetch an Patient action based on id.
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("patient_actions/{id}")]
        public async Task<IActionResult> GetPatientAction(long id)
        {
            var apiResponse = new ApiResponse<PatientActionsView>();

            try
            {
                apiResponse = await _PatientActionsBAL.GetPatientActionBAL(baseHttpRequestContext, id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting Patient Action Details");
                _logger.LogError($"Error while getting Patient Action Details {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
    }
}
