﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.Business.Common;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.User.Interfaces;
using Capstone2.RestServices.User.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.RestServices.User.Controller
{
    [ApiController]
    [Route("user")]
    [ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class UserController : BaseController
    {
        private readonly ILogger<UserController> _logger;
        private readonly IUserBAL _UserBAL;
        private readonly IListUserBAL _listUserBAL;
        private readonly IExtProviderBAL _extProviderBAL;
        public UserController(ILogger<UserController> logger, IUserBAL usersBAL, IListUserBAL listUserBAL, IExtProviderBAL extProviderBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _UserBAL = usersBAL;
            _listUserBAL = listUserBAL;
            _extProviderBAL = extProviderBAL;
        }

        [HttpGet]
        [Route("user_details/{id}")]
        public async Task<IActionResult> GetUserDetails(long id)
        {
            var apiResponse = new ApiResponse<UserDetailInput>();
            try
            {
                apiResponse = await _UserBAL.GetUserDetailsBAL(baseHttpRequestContext.OrgId, id, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting users Data");
                _logger.LogError($"Error while getting users Data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("user_details")]
        public async Task<IActionResult> AddUserDetails(UserDetailInput user)
        {
            var apiResponse = new ApiResponse<UserDetail>();
            try
            {
                apiResponse = await _UserBAL.AddUserDetailsBAL(user, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while creating User.Please check all variables");
                _logger.LogError($"Error while creating User {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to validate if the user loginemail  already exists
        /// </summary>
        /// <param name="search_term"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("user_details/email/verify")]
        public async Task<IActionResult> CheckUserEmail(string search_term)
        {
            var apiResponse = new ApiResponse<bool>();
            try
            {
                apiResponse = await _UserBAL.CheckUserEmailBAL(search_term, baseHttpRequestContext.OrgId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error while verifying the Email");
                _logger.LogError($"Error while verifying the Email {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }

        /// <summary>
        /// Method to validate if the user loginemail  already exists
        /// </summary>
        /// <param name="search_term"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("user_details/mobile/verify")]
        public async Task<IActionResult> CheckUserMobile(string search_term)
        {
            var apiResponse = new ApiResponse<bool>();
            try
            {
                apiResponse = await _UserBAL.CheckUserMobileBAL(search_term, baseHttpRequestContext.OrgId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error while verifying the Mobile");
                _logger.LogError($"Error while verifying the Mobile {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to fetch users
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("user_details")]
        public async Task<IActionResult> ListUser([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<QueryResultList<UserInfo>>();
            try
            {
                apiResponse = await _listUserBAL.ListUser(baseHttpRequestContext.OrgId, queryModel, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of Users could not be fetched during this time.");
                _logger.LogError($"List of Users could not be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpPut]
        [Route("user_details/{Id}")]
        public async Task<IActionResult> EditUserDetails(long Id, [FromBody] UserDetailInput user)
        {
            var apiResponse = new ApiResponse<UserDetail>();
            try
            {
                apiResponse = await _UserBAL.EditUserDetailsBAL(Id, user, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while Editing User.Please check all variables");
                _logger.LogError($"Error while Editing User {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }

        /// <summary>
        /// Method to save External Provider in User Details 
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("external_provider")]
        public async Task<IActionResult> AddExternalProvider(ExternalProvider externalProvider)
        {
            var apiResponse = new ApiResponse<UserDetail>();
            try
            {
                apiResponse = await _extProviderBAL.AddExternalProvider(externalProvider, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while creating External Provider.Please check all variables");
                _logger.LogError($"Error while creating External Provider {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }

        [HttpPut]
        [Route("external_provider/{Id}")]
        public async Task<IActionResult> EditExternalProvider(long Id, [FromBody] ExternalProvider externalProvider)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _extProviderBAL.EditExternalProvider(Id, externalProvider, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while Editing User.Please check all variables");
                _logger.LogError($"Error while Editing User {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }

        /// <summary>
        /// Method to fetch External providers
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("external_provider")]
        public async Task<IActionResult> ListExternalProviders([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<QueryResultList<ExternalProviderListModel>>();
            try
            {
                apiResponse = await _extProviderBAL.ListExternalProviders(baseHttpRequestContext.OrgId, queryModel);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of ExternalProviders could not be fetched during this time.");
                _logger.LogError($"List of ExternalProviders could not be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to fetch all active providers
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("user_companyassocs")]
        public async Task<IActionResult> ListProviders([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<QueryResultList<ProviderInfo>>();
            try
            {
                apiResponse = await _listUserBAL.ListProviders(baseHttpRequestContext.OrgId, queryModel);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of Providers could not be fetched during this time.");
                _logger.LogError($"List of Providers could not be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpDelete]
        [Route("{companyId}/usercompanyassocs")]
        public async Task<IActionResult> DeleteLinkedProviders(int companyId)
        {
            var apiResponse = new ApiResponse<string>();
            try
            {
                apiResponse = await _UserBAL.DeleteLinkedProviders(companyId, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of Providers could not be fetched during this time.");
                _logger.LogError($"List of Providers could not be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpGet("healthcheck")]
        [AllowAnonymous]
        public IActionResult HealthCheck()
        {
            //var ClientIpAddress = ClientIp.GetClientIpAddress(baseHttpRequestContext.RemoteIpAddress);
            //_logger.LogInformation("Client IP1" + ClientIpAddress);
            //if (HttpContext.Request.Headers.ContainsKey("X-Forwarded-For"))

            //{
            //    // _logger.LogInformation("Login headerContent:" + HttpContext.Request.Headers["X-Forwarded-For"].FirstOrDefault()?.ToString());
            //    string headercontent = HttpContext.Request.Headers["X-Forwarded-For"].FirstOrDefault()?.ToString();
            //    if (headercontent != null && headercontent.IndexOf(':') != -1)
            //    {
            //        var ClientIpAddress2 = headercontent.Split(':').FirstOrDefault();
            //        _logger.LogInformation("client IP2" + ClientIpAddress2);
            //    }
            //}

            return Ok(StatusCodes.Status200OK);
        }

        /// <summary>
        /// Method to fetch few fields of a user
        /// </summary>
        /// <param name=""></param>
        /// <returns></returns>
        [HttpGet]
        [Route("user_detailinfo/{id}")]
        [ProducesResponseType(typeof(ApiResponse<Capstone2.Shared.Models.Entities.UserInfoView>), StatusCodes.Status200OK)]

        public async Task<IActionResult> GetUserDetailInfo(long id)
        {
            var apiResponse = new ApiResponse<Capstone2.Shared.Models.Entities.UserInfoView>();
            try
            {
                apiResponse = await _UserBAL.GetUserDetailInfo(baseHttpRequestContext.OrgId, id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("User could not be fetched during this time.");
                _logger.LogError($"User could not be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
        /// <summary>
        /// Method to fetch few fields of a user
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("user_detailinfo")]
        [ProducesResponseType(typeof(List<ApiResponse<Capstone2.Shared.Models.Entities.UserInfoView>>), StatusCodes.Status200OK)]

        public async Task<IActionResult> GetUserDetailInfo([FromQuery] string Ids)
        {
            var apiResponse = new ApiResponse<List<Capstone2.Shared.Models.Entities.UserInfoView>>();
            try
            {
                apiResponse = await _UserBAL.ListUserDetailInfo(baseHttpRequestContext.OrgId, Ids);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("User could not be fetched during this time.");
                _logger.LogError($"User could not be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
        /// <summary>
        /// Method to fetch few fields of a user
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("user_detailinfo/usercompanyassocs")]
        [ProducesResponseType(typeof(List<ApiResponse<Capstone2.Shared.Models.Entities.UserInfoView>>), StatusCodes.Status200OK)]

        public async Task<IActionResult> ListUserDetailInfoWithCompanyAssocs([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<List<Capstone2.Shared.Models.Entities.UserInfoView>>();
            try
            {
                apiResponse = await _listUserBAL.ListUserDetailInfoWithCompanyAssocs(baseHttpRequestContext.OrgId, queryModel);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("User could not be fetched during this time.");
                _logger.LogError($"User could not be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to fetch user's quicknote
        /// </summary>
        /// <param name="id"></param>
        /// <returns>quicknote object</returns>
        [HttpGet]
        [Route("{user_id}/quicknote")]
        [ProducesResponseType(typeof(ApiResponse<QuickNote>), StatusCodes.Status200OK)]

        public async Task<IActionResult> GetUserQuickNote(long user_id)
        {
            var apiResponse = new ApiResponse<QuickNote>();
            try
            {
                apiResponse = await _UserBAL.GetQuickNote(baseHttpRequestContext.OrgId, user_id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("User quicknote could not be fetched during this time.");
                _logger.LogError($"User quicknote could not be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method is used for quicknote add/update
        /// </summary>
        /// <param name="user_id">user_id</param>
        /// <param name="note">note</param>
        /// <returns>quicknote object</returns>
        [HttpPut]
        [Route("{user_id}/quicknote")]
        public async Task<IActionResult> UpsertQuickNote(long user_id, [FromBody] QuickNoteInput note)
        {
            var apiResponse = new ApiResponse<QuickNote>();
            try
            {
                apiResponse = await _UserBAL.UpsertQuickNote(user_id, note, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while editing quicknote.Please check all variables");
                _logger.LogError($"Error while editing quicknote {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to fetch all whatson message by user
        /// </summary>
        /// <param name="user_id">user_id</param>
        /// <returns>whatson message object</returns>
        [HttpGet]
        [Route("{user_id}/whatsonmsg")]
        [ProducesResponseType(typeof(ApiResponse<List<WhatsOnMessages>>), StatusCodes.Status200OK)]

        public async Task<IActionResult> GetWhatsonMessage(long user_id)
        {
            var apiResponse = new ApiResponse<List<WhatsOnMessages>>();
            try
            {
                apiResponse = await _UserBAL.GetWhatsOnMessages(baseHttpRequestContext.OrgId, user_id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Whats-on message could not be fetched during this time.");
                _logger.LogError($"Whats-on message could not be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method is used for whatson message add
        /// </summary>
        /// <param name="user_id">user_id</param>
        /// <param name="messages">messages</param>
        /// <returns>whats-on message object</returns>
        [HttpPost]
        [Route("{user_id}/whatsonmsg")]
        public async Task<IActionResult> AddWhatsOnMessage(long user_id, [FromBody] WhatsOnMessagesInput messages)
        {
            var apiResponse = new ApiResponse<WhatsOnMessages>();
            try
            {
                apiResponse = await _UserBAL.UpsertWhatsOnMessages(user_id, messages, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while adding whats-on message.Please check all variables");
                _logger.LogError($"Error while adding whats-on message {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method is used for whatson message update
        /// </summary>
        /// <param name="user_id">user_id</param>
        /// <param name="messages">messages</param>
        /// <returns>whats-on message object</returns>
        [HttpPut]
        [Route("{user_id}/whatsonmsg")]
        public async Task<IActionResult> EditWhatsOnMessage(long user_id, [FromBody] WhatsOnMessagesInput messages)
        {
            var apiResponse = new ApiResponse<WhatsOnMessages>();
            try
            {
                apiResponse = await _UserBAL.UpsertWhatsOnMessages(user_id, messages, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while editing whats-on message.Please check all variables");
                _logger.LogError($"Error while editing whats-on message {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method is used for whatson message delete
        /// </summary>
        /// <param name="user_id"></param>
        /// <param name="messages"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("{user_id}/whatsonmsg")]
        public async Task<IActionResult> DeleteWhatsOnMessage(long user_id, [FromQuery] WhatsOnMessagesInput messages)
        {
            var apiResponse = new ApiResponse<string>();
            try
            {
                apiResponse = await _UserBAL.DeleteWhatsOnMessages(user_id, messageId: messages.Id.HasValue ? messages.Id.Value : 0, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while deleting WhatsOnMessage");
                _logger.LogError($"Error while deleting WhatsOnMessage {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


      /// <summary>
      /// Method to fetch user health fund assoc using participantid
      /// </summary>
      /// <param name="user_id"></param>
      /// <param name="participantId"></param>
      /// <returns></returns>
        [HttpGet]
        [Route("{user_id}/user_healthfundassoc")]
        [ProducesResponseType(typeof(UserHealthFundAssoc), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetUserHealthHundAssocFromUserId(long user_id, [FromQuery] string participantId)
        {
            var apiResponse = new ApiResponse<UserHealthFundAssoc>();
            try
            {
                apiResponse = await _UserBAL.GetUserHealthHundAssocFromUserId(user_id, participantId, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while fetching User Health Fund Association");
                _logger.LogError($"Error while fetching User Health Fund Association {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


        /// <summary>
        /// Method to get list of HPIIServiceDetails by UserID
        /// </summary>
        /// <param name="user_id">UserID</param>
        /// <returns>List of HPIIServiceDetails</returns>
        [HttpGet]
        [Route("{user_id}/hiservice/hpii_details")]
        public async Task<IActionResult> GetHPIIServiceDetails(long user_id)
        {
            var apiResponse = new ApiResponse<List<HIServiceDetailLogs>>();
            try
            {
                apiResponse = await _UserBAL.GetHIServiceDetails(baseHttpRequestContext, user_id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting HPIIServiceDetails Data");
                _logger.LogError($"Error while getting HPIIServiceDetails Data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to add a new HPIIServiceDetails
        /// </summary>
        /// <param name="user_id"></param>
        /// <param name="inputHIServiceDetail"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("{user_id}/hiservice/hpiinumber")]
        public async Task<IActionResult> AddHPIIServiceDetails(long user_id, [FromBody] InputHIServiceDetails inputHIServiceDetail)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _UserBAL.AddHIServiceDetail(baseHttpRequestContext, user_id, inputHIServiceDetail);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while creating HPIIServiceDetails.Please check all variables");
                _logger.LogError($"Error while creating HPIIServiceDetails {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to update a HPIIServiceDetails
        /// </summary>
        /// <param name="user_id">user_id</param>
        /// <param name="id"></param>
        /// <param name="inputHIServiceDetail"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("{user_id}/hiservice/hpiinumber/{id}")]
        public async Task<IActionResult> EditHPIIServiceDetails(long user_id, long id, [FromBody] InputHIServiceDetails inputHIServiceDetail)
        {
            var apiResponse = new ApiResponse<string>();

            try
            {
                apiResponse = await _UserBAL.EditHIServiceDetail(baseHttpRequestContext, user_id, id, inputHIServiceDetail);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("HPIIServiceDetails cannot be Edited.Please try again later.");
                _logger.LogError($"HPIIServiceDetails cannot be Edited {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
    }
}
