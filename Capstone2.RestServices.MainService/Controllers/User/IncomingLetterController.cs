﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.User.Interfaces;
using Capstone2.RestServices.User.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Capstone2.RestServices.User.Controller
{
    [ApiController]
    [Route("user")]
    [ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class IncomingLetterController : BaseController
    {
        private readonly ILogger<IncomingLetterController> _logger;
        private readonly IIncomingLetterBAL _incomingLetterBAL;

        public IncomingLetterController(ILogger<IncomingLetterController> logger, IIncomingLetterBAL incomingLetterBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _incomingLetterBAL = incomingLetterBAL;
        }

        [HttpPost]
        [Route("incoming_letters")]
        [ProducesResponseType(typeof(ApiResponse<IncomingLetterPostView>), StatusCodes.Status200OK)]
        public async Task<IActionResult> AddIncomingLetter([FromBody] IncomingLetter inputLetter)
        {
            var apiResponse = new ApiResponse<IncomingLetterPostView>();
            try
            {
                apiResponse = await _incomingLetterBAL.AddIncomingLetter(inputLetter, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error while creating IncomingLetter. Please check all variables.");
                _logger.LogError($"Error while creating IncomingLetter {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpGet]
        [Route("incoming_letters/{id}")]
        [ProducesResponseType(typeof(ApiResponse<IncomingLetter>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetIncomingLetterById(long id)
        {
            var apiResponse = new ApiResponse<IncomingLetter>();

            try
            {
                    apiResponse = await _incomingLetterBAL.GetIncomingLetterById(id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    StatusCodes.Status404NotFound => NotFound(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while fetching IncomingLetter details");
                _logger.LogError($"Error while fetching IncomingLetter details {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpGet]
        [Route("incoming_letters")]
        [ProducesResponseType(typeof(ApiResponse<List<IncomingLetter>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetIncomingLettersByOrgId([FromQuery] int orgId)
        {
            var apiResponse = new ApiResponse<List<IncomingLetter>>();

            try
            {
                apiResponse = await _incomingLetterBAL.GetIncomingLettersByOrgId(orgId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while fetching IncomingLetters");
                _logger.LogError($"Error while fetching IncomingLetters {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpPut]
        [Route("incoming_letters")]
        [ProducesResponseType(typeof(ApiResponse<long?>), StatusCodes.Status200OK)]
        public async Task<IActionResult> EditIncomingLetter([FromBody] IncomingLetter inputLetter)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _incomingLetterBAL.EditIncomingLetter(inputLetter, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while editing IncomingLetter. Please try again later.");
                _logger.LogError($"Error while editing IncomingLetter {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpDelete]
        [Route("incoming_letters/{id}")]
        [ProducesResponseType(typeof(ApiResponse<long?>), StatusCodes.Status200OK)]
        public async Task<IActionResult> DeleteIncomingLetter(long id)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _incomingLetterBAL.DeleteIncomingLetter(id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while deleting IncomingLetter. Please try again later.");
                _logger.LogError($"Error while deleting IncomingLetter {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
    }
}
