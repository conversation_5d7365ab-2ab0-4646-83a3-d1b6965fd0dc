﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.User.Interfaces;
using Capstone2.RestServices.User.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Transactions;

namespace Capstone2.RestServices.User.Controller
{
    [ApiController]
    [Route("user")]
    [ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class IncomingLetterPatientActionsController : BaseController
    {
        private readonly ILogger<IncomingLetterPatientActionsController> _logger;
        private readonly IIncomingLetterPatientActionsBAL _incomingLetterPatientActionsBAL;

        public IncomingLetterPatientActionsController(ILogger<IncomingLetterPatientActionsController> logger, IIncomingLetterPatientActionsBAL incomingLetterPatientActionsBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _incomingLetterPatientActionsBAL = incomingLetterPatientActionsBAL;
        }

        [HttpPost]
        [Route("incoming_letter_patient_action_multiuser")]
        public async Task<ApiResponse<long?>> AddIncomingLetterMultiUserPatientAction([FromBody] List<IncomingLetterPatientAction> incomingLetterPatientAction)
        {
            ApiResponse<long?> apiResponse = new();
            var userId = baseHttpRequestContext.UserId;
            var orgId = baseHttpRequestContext.OrgId;

            apiResponse = await _incomingLetterPatientActionsBAL.AddIncomingLetterMultiUserPatientAction(incomingLetterPatientAction, baseHttpRequestContext);
            if (apiResponse.Result > 0)
            {
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = null;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("Action cannot be added at this time. Please try again later.");
            }
            return apiResponse;



        }

        [HttpPost]
        [Route("incoming_letter_patient_action")]
        [ProducesResponseType(typeof(ApiResponse<long?>), StatusCodes.Status200OK)]
        public async Task<IActionResult> AddIncomingLetterPatientAction([FromBody] IncomingLetterPatientAction incomingLetterPatientAction)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _incomingLetterPatientActionsBAL.AddIncomingLetterPatientAction(incomingLetterPatientAction, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error while editing IncomingLetterPatientAction. Please try again later.");
                _logger.LogError($"Error while editing IncomingLetterPatientAction {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


        [HttpGet]
        [Route("incoming_letter_patient_action/{id}/{selectedActionId}")]
        [ProducesResponseType(typeof(ApiResponse<IncomingLetterPatientActionView>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetIncomingLetterPatientActionById(long id, short selectedActionId)
        {
            var apiResponse = new ApiResponse<IncomingLetterPatientActionView>();
            try
            {
                apiResponse = await _incomingLetterPatientActionsBAL.GetIncomingLetterPatientActionById(id, selectedActionId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    StatusCodes.Status404NotFound => NotFound(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error while fetching IncomingLetterPatientAction. Please try again later.");
                _logger.LogError($"Error while fetching IncomingLetterPatientAction {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpGet]
        [Route("incoming_letter_patient_action/{activityLogId}")]
        [ProducesResponseType(typeof(ApiResponse<IncomingLetterPatientActionView>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetIncomingLetterPatientActionByActivityLogId(short activityLogId)
        {
            var apiResponse = new ApiResponse<IncomingLetterPatientActionView>();
            try
            {
                apiResponse = await _incomingLetterPatientActionsBAL.GetIncomingLetterPatientActionByActivityLogId(activityLogId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    StatusCodes.Status404NotFound => NotFound(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error while fetching IncomingLetterPatientAction. Please try again later.");
                _logger.LogError($"Error while fetching IncomingLetterPatientAction {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpGet]
        [Route("incoming_letter_patient_actions")]
        [ProducesResponseType(typeof(ApiResponse<QueryResultList<IncomingLetterPatientActionView>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetIncomingLetterPatientActions([FromQuery] QueryModel queryModel)
        {

            var apiResponse = new ApiResponse<QueryResultList<IncomingLetterPatientActionView>>();
            try
            {
                apiResponse = await _incomingLetterPatientActionsBAL.GetIncomingLetterPatientActions(queryModel, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    StatusCodes.Status403Forbidden => new ObjectResult(apiResponse) { StatusCode = StatusCodes.Status403Forbidden },
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of actions could not be fetched during this time.");
                _logger.LogError($"List of actions could not be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        private IncomingLetterActionsFilterModel PrepareFilterParameters(string filter)
        {
            if (!(String.IsNullOrEmpty(filter)))
            {
                return JsonConvert.DeserializeObject<IncomingLetterActionsFilterModel>(filter);
            }
            else
            {
                return null;
            }
        }

        [HttpPut]
        [Route("incoming_letter_patient_action")]
        [ProducesResponseType(typeof(ApiResponse<long?>), StatusCodes.Status200OK)]
        public async Task<IActionResult> EditIncomingLetterPatientAction([FromBody] IncomingLetterPatientAction incomingLetterPatientAction)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _incomingLetterPatientActionsBAL.EditIncomingLetterPatientAction(incomingLetterPatientAction, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error while editing IncomingLetterPatientAction. Please try again later.");
                _logger.LogError($"Error while editing IncomingLetterPatientAction {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// GetPatientActionsNotificationCount
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("incoming_letter_patient_action/notification_count")]
        [ProducesResponseType(typeof(ApiResponse<List<IncomingLetterPatientActionsNotificationCount>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetPatientActionsNotificationCount()
        {
            var apiResponse = new ApiResponse<List<IncomingLetterPatientActionsNotificationCount>>();

            try
            {
                apiResponse = await _incomingLetterPatientActionsBAL.GetIncomingLetterPatientActionsNotificationCount(baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Actions count could not be fetched during this time.");
                _logger.LogError($"Actions count could not be fetched  {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// GetPatientActionsNotificationCountByUser
        /// </summary>
        /// <param name="user_id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("incoming_letter_patient_action/user_notification_count/{user_id}")]
        [ProducesResponseType(typeof(ApiResponse<List<IncomingLetterPatientActionsNotificationCount>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetPatientActionsNotificationCountByUser(long user_id)
        {
            var apiResponse = new ApiResponse<List<IncomingLetterPatientActionsNotificationCount>>();

            try
            {
                apiResponse = await _incomingLetterPatientActionsBAL.GetIncomingLetterPatientActionsNotificationCountByUser(user_id, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("actions count could not be fetched during this time.");
                _logger.LogError($"actions count could not be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpPut]
        [Route("incoming_letter_patient_action_accepted")]
        public async Task<ApiResponse<long?>> IncomingLetterAccepted([FromBody] int incomingLetterId)
        {
            ApiResponse<long?> apiResponse = new();
            var userId = baseHttpRequestContext.UserId;
            var orgId = baseHttpRequestContext.OrgId;

            apiResponse = await _incomingLetterPatientActionsBAL.IncomingLetterAccepted(incomingLetterId, baseHttpRequestContext);
            if (apiResponse.Result > 0)
            {
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = null;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("Action cannot be added at this time. Please try again later.");
            }
            return apiResponse;



        }

    }
}
