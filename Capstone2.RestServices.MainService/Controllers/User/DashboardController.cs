﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.User.Interfaces;
using Capstone2.RestServices.User.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Capstone2.RestServices.User.Controller
{

    [ApiController]
    [Route("user")]
    [ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class DashboardController : BaseController
    {
        private readonly ILogger<DashboardController> _logger;
        private readonly IDashboardBAL _dashboardBAL;
        public DashboardController(ILogger<DashboardController> logger, IDashboardBAL dashboardBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _dashboardBAL = dashboardBAL;
        }

        /// <summary>
        /// Method to add Dashboard preferences
        /// </summary>
        /// <param name="inputAction"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("{user_id}/preferences")]
        public async Task<IActionResult> AddPreferences(long user_id, [FromBody] Preference inputPreference)
        {
            var apiResponse = new ApiResponse<int>();
            try
            {
                apiResponse = await _dashboardBAL.AddPreferencesBAL(user_id, inputPreference, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error while creating DashboardPreferences. Please check all variables.");
                _logger.LogError($"Error while creating DashboardPreferences {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to fetch a dashboard preference based on id.
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("{user_id}/preferences/{screen_type}")]
        public async Task<IActionResult> GetPreference(long user_id, short screen_type)
        {
            var apiResponse = new ApiResponse<Preference>();

            try
            {
                apiResponse = await _dashboardBAL.GetPreferencesBAL(baseHttpRequestContext, user_id, screen_type);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting dashboard preference Details");
                _logger.LogError($"Error while getting dashboard preference Details {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to update a user preference
        /// </summary>
        /// <param name="dashboardPreference"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("preferences/{id}")]
        public async Task<IActionResult> EditPreferences(int id, [FromBody] Preference inputPreference)
        {
            var apiResponse = new ApiResponse<string>();
            try
            {

                apiResponse = await _dashboardBAL.EditPreferencesBAL(id, inputPreference, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("DashboardPreference cannot be Edited.Please try again later.");
                _logger.LogError($"DashboardPreference cannot be Edited {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
    }
}
