﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.User.Interfaces;
using Capstone2.RestServices.User.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace Capstone2.RestServices.User.Controller
{

    [ApiController]
    [Route("user")]
    [ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class ScheduleController : BaseController
    {
        private readonly ILogger<UserController> _logger;
        private readonly IScheduleBAL _scheduleBAL;
        public ScheduleController(ILogger<UserController> logger, IScheduleBAL scheduleBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _scheduleBAL = scheduleBAL;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="InputSchedule"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("{user_id}/Schedules")]
        public async Task<IActionResult> AddUserSchedule(long user_id, [FromBody] ScheduleInput inputSchedule)
        {
            var apiResponse = new ApiResponse<string>();
            try
            {
                apiResponse = await _scheduleBAL.AddScheduleBAL(user_id, inputSchedule, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error while creating Schedules.Please check all variables.");
                _logger.LogError($"Error while creating Schedules {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }

        /// <summary>
        /// Method to fetch List of Schedules
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("Schedules")]
        public async Task<IActionResult> ListUser([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<QueryResultList<ScheduleList>>();
            try
            {
                apiResponse = await _scheduleBAL.ListScheduleBAL(queryModel, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of Schedules could not be fetched during this time.");
                _logger.LogError($"List of Schedules could not be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to Get the schedule details
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("{user_id}/Schedules/{id}")]
        public async Task<IActionResult> GetScheduleDetails(long user_id, long id)
        {
            var apiResponse = new ApiResponse<ScheduleView>();
            try
            {
                apiResponse = await _scheduleBAL.GetScheduleBAL(user_id, id, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting Schedule Details Data");
                _logger.LogError($"Error while getting Schedule Details Data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to delete a schedule based on ids
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("{user_id}/schedules")]
        public async Task<IActionResult> DeleteSchedules(long user_id, [FromQuery] RecurrenceDeleteObject recurrenceDelete)
        {
            var apiResponse = new ApiResponse<string>();
            try
            {
                apiResponse = await _scheduleBAL.DeleteSchedulesBAL(user_id, recurrenceDelete, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while deleting schedules");
                _logger.LogError($"Error while deleting schedules {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to update a schedule based on id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("{user_id}/Schedules/{id}")]
        public async Task<IActionResult> UpdateSchedules(long user_id, long id, [FromBody] ScheduleInput inputSchedule)
        {
            var apiResponse = new ApiResponse<string>();
            try
            {
                apiResponse = await _scheduleBAL.UpdateSchedulesBAL(user_id, id, baseHttpRequestContext, inputSchedule);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while updating schedules");
                _logger.LogError($"Error while updating schedules {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to check a user's availability
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("{user_id}/schedules/verify")]
        public async Task<IActionResult> VerifySchedules(long user_id, [FromBody] ScheduleInput inputSchedule)
        {
            var apiResponse = new ApiResponse<bool?>();
            try
            {
                apiResponse = await _scheduleBAL.VerifySchedules(user_id, baseHttpRequestContext, inputSchedule);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while checking for conflicts in user schedules.");
                _logger.LogError($"Error while checking for conflicts in user schedules {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

    }
}
