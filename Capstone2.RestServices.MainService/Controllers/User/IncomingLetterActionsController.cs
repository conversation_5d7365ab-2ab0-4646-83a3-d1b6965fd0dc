﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.User.Interfaces;
using Capstone2.RestServices.User.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Capstone2.RestServices.User.Controller
{
    [ApiController]
    [Route("user")]
    [ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class IncomingLetterActionsController : BaseController
    {
        private readonly ILogger<IncomingLetterActionsController> _logger;
        private readonly IIncomingLetterActionsBAL _incomingLetterActionsBAL;

        public IncomingLetterActionsController(ILogger<IncomingLetterActionsController> logger, IIncomingLetterActionsBAL incomingLetterActionsBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _incomingLetterActionsBAL = incomingLetterActionsBAL;
        }

        [HttpPost]
        [Route("incoming_letter_action")]
        [ProducesResponseType(typeof(ApiResponse<long?>), StatusCodes.Status200OK)]
        public async Task<IActionResult> AddIncomingLetterAction([FromBody] IncomingLetterActions incomingLetterAction)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _incomingLetterActionsBAL.AddIncomingLetterAction(incomingLetterAction, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error while creating IncomingLetterAction. Please check all variables.");
                _logger.LogError($"Error while creating IncomingLetterAction {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpGet]
        [Route("incoming_letter_action/{id}")]
        [ProducesResponseType(typeof(ApiResponse<List<IncomingLetterActions>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetIncomingLetterActionById(long id, int orgId)
        {
            var apiResponse = new ApiResponse<IncomingLetterActions>();
            try
            {
                apiResponse = await _incomingLetterActionsBAL.GetIncomingLetterActionById(id, orgId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    StatusCodes.Status404NotFound => NotFound(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error while fetching IncomingLetterAction. Please try again later.");
                _logger.LogError($"Error while fetching IncomingLetterAction {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpGet]
        [Route("incoming_letter_actions")]
        [ProducesResponseType(typeof(ApiResponse<List<IncomingLetterActions>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetIncomingLetterActionsByOrgId([FromQuery] int orgId)
        {
            var apiResponse = new ApiResponse<List<IncomingLetterActions>>();
            try
            {
                apiResponse = await _incomingLetterActionsBAL.GetIncomingLetterActionsByOrgId(orgId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error while fetching IncomingLetterActions. Please try again later.");
                _logger.LogError($"Error while fetching IncomingLetterActions {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpPut]
        [Route("incoming_letter_action")]
        [ProducesResponseType(typeof(ApiResponse<long?>), StatusCodes.Status200OK)]
        public async Task<IActionResult> EditIncomingLetterAction([FromBody] IncomingLetterActions incomingLetterAction)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _incomingLetterActionsBAL.EditIncomingLetterAction(incomingLetterAction, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error while editing IncomingLetterAction. Please try again later.");
                _logger.LogError($"Error while editing IncomingLetterAction {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpDelete]
        [Route("incoming_letter_action/{id}")]
        [ProducesResponseType(typeof(ApiResponse<long?>), StatusCodes.Status200OK)]
        public async Task<IActionResult> DeleteIncomingLetterAction(long id)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _incomingLetterActionsBAL.DeleteIncomingLetterAction(id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error while deleting IncomingLetterAction. Please try again later.");
                _logger.LogError($"Error while deleting IncomingLetterAction {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
    }
}
