﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.MedicalSchedule.Interfaces;
using Capstone2.RestServices.MedicalSchedule.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace Capstone2.RestServices.MedicalSchedule.Controllers
{
    [ApiController]
    [Route("MedicalSchedule")]
    //  [Authorize]
    [ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class UploaderController : BaseController
    {
        private readonly ILogger<UploaderController> _logger;
        private readonly IUploaderBAL _uploaderBAL;

        public UploaderController(ILogger<UploaderController> logger, IUploaderBAL uploaderBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _uploaderBAL = uploaderBAL;
        }

        [HttpGet("healthcheck")]
        [AllowAnonymous]
        [TelemetryTracing(logRequest: false, logResponse: false)]
        public IActionResult HealthCheck()
        {
            return Ok(StatusCodes.Status200OK);
        }

        /// <summary>
        /// Api to add a new uploader data
        /// </summary>
        /// <param name="inputMediaUploader"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("Uploads")]
        public async Task<IActionResult> AddUploads([FromBody] Upload inputMediaUpload)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _uploaderBAL.AddUploadBAL(inputMediaUpload, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while creating Medicare upload.Please check all variables");
                _logger.LogError($"Error while creating Medicare upload {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }

        /// <summary>
        /// Method to fetch Medicare Uploads 
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("Uploads")]
        public async Task<IActionResult> ListUploads([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<QueryResultList<UploadView>>();
            try
            {
                apiResponse = await _uploaderBAL.ListUploadBAL(baseHttpRequestContext, queryModel);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of Medicare uploads could not be fetched during this time.");
                _logger.LogError($"List of Medicare uploads could not be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


        /// <summary>
        /// Api to add a new HFuploader data
        /// </summary>
        /// <param name="inputHfUpload"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("hf_uploads")]
        public async Task<IActionResult> AddHFUploads([FromBody] HFUpload inputHfUpload)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _uploaderBAL.AddHfUploadBAL(inputHfUpload, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while creating HealthFund upload.Please check all variables");
                _logger.LogError($"Error while creating HealthFund upload {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }

        /// <summary>
        /// Method to fetch HealthFund Uploads 
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("hf_uploads")]
        public async Task<IActionResult> ListHFUploads([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<QueryResultList<HFUploadView>>();
            try
            {
                apiResponse = await _uploaderBAL.ListHFUploadBAL(baseHttpRequestContext, queryModel);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of Medicare uploads could not be fetched during this time.");
                _logger.LogError($"List of Medicare uploads could not be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


    }
}
