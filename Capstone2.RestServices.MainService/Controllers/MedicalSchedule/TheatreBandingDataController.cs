﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.MedicalSchedule.Interfaces;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace Capstone2.RestServices.MedicalSchedule.Controllers
{
    [ApiController]
    [Route("MedicalSchedule")]
    //  [Authorize]
    [ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class TheatreBandingDataController : BaseController
    {
        private readonly ITheatreBandingDataBAL _theatreBandingDataBAL;
        private readonly ILogger<TheatreBandingDataController> _logger;


        public TheatreBandingDataController(ILogger<TheatreBandingDataController> logger,ITheatreBandingDataBAL theatreBandingDataBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _theatreBandingDataBAL = theatreBandingDataBAL;
            _logger = logger;
        }

        /// <summary>
        /// Method to fetch list of TheatreBanding data 
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("theatreBandingData")]
        public async Task<IActionResult> ListTheatreBandingData([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<QueryResultList<ListTheatreBandingData>>();
            try
            {
                apiResponse = await _theatreBandingDataBAL.GetTheatreBandingDataListBAL(baseHttpRequestContext, queryModel);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of TheatreBandingData could not be fetched during this time.");
                _logger.LogError($"List of TheatreBandingData could not be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


    }
}
