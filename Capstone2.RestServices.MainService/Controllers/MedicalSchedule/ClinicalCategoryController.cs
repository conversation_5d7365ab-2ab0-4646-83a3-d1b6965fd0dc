﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.MedicalSchedule.Interfaces;
using Capstone2.RestServices.MedicalSchedule.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace Capstone2.RestServices.MedicalSchedule.Controllers
{
    [ApiController]
    [Route("MedicalSchedule")]
    //  [Authorize]
    [ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class ClinicalCategoryController : BaseController
    {
        private readonly IClinicalCategoryBAL _clinicalCategoryBAL;
        private readonly ILogger<ClinicalCategoryController> _logger;

        public ClinicalCategoryController(ILogger<ClinicalCategoryController> logger,IClinicalCategoryBAL clinicalCategoryBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _clinicalCategoryBAL = clinicalCategoryBAL;
        }

        /// <summary>
        /// Method to fetch list of ClinicalCategory data 
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("clinicalCategoryData")]
        public async Task<IActionResult> ListClinicalCategoryData([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<QueryResultList<ClinicalCategory>>();
            try
            {
                apiResponse = await _clinicalCategoryBAL.GetClinicalCategoryDataListBAL(baseHttpRequestContext, queryModel);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of ClinicalCategory data could not be fetched during this time.");
                _logger.LogError($"List of ClinicalCategory data could not be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


    }
}
