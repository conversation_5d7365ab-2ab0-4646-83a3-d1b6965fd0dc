﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.MedicalSchedule.Interfaces;
using Capstone2.RestServices.MedicalSchedule.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Capstone2.RestServices.MedicalSchedule.Controllers
{
    [ApiController]
    [Route("MedicalSchedule")]
    //  [Authorize]
    [ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class MbsClinicalCategoryDataController : BaseController
    {
        private readonly IMbsClinicalCategoryDataBAL _mbsClinicalCategoryDataBAL;
        private readonly ILogger<MbsClinicalCategoryDataController> _logger;

        public MbsClinicalCategoryDataController(ILogger<MbsClinicalCategoryDataController> logger,IMbsClinicalCategoryDataBAL mbsClinicalCategoryDataBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _mbsClinicalCategoryDataBAL = mbsClinicalCategoryDataBAL;
        }

        /// <summary>
        /// Method to fetch list of MbsClinicalCategory data 
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("mbsClinicalCategoryData")]
        public async Task<IActionResult> ListMbsClinicalCategoryData([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<QueryResultList<MbsClinicalCategoryData>>();
            try
            {
                apiResponse = await _mbsClinicalCategoryDataBAL.GetMbsClinicalCategoryDataListBAL(baseHttpRequestContext, queryModel);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of MbsClinicalCategory data could not be fetched during this time.");
                _logger.LogError($"List of MbsClinicalCategory data could not be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to fetch list of MbsClinicalCategory data item no.
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("mbsClinicalCategoryData/{categoryId}")]
        public async Task<IActionResult> ListItemNum(short categoryId)
        {
            var apiResponse = new ApiResponse<List<long>>();
            try
            {
                apiResponse = await _mbsClinicalCategoryDataBAL.GetItemNumsList(baseHttpRequestContext, categoryId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of MbsClinicalCategory item nums could not be fetched during this time.");
                return BadRequest(apiResponse);
            }
        }
    }
}
