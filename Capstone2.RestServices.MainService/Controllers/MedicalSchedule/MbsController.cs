﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.MedicalSchedule.Interfaces;
using Capstone2.RestServices.MedicalSchedule.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Capstone2.RestServices.MedicalSchedule.Controllers
{
    [ApiController]
    [Route("MedicalSchedule")]
    //  [Authorize]
    [ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class MbsController : BaseController
    {
        private readonly IMbsBAL _mbsBAL;
        private readonly ILogger<MbsController> _logger;

        public MbsController(ILogger<MbsController> logger,IMbsBAL mbsBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _mbsBAL = mbsBAL;
        }

        /// <summary>
        /// Method to fetch list of Mbs Data 
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("mbs")]
        public async Task<IActionResult> ListMbsData([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<QueryResultList<ListMbsData>>();
            try
            {
                apiResponse = await _mbsBAL.GetMbsDataListBAL(baseHttpRequestContext, queryModel);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of Mbsdata could not be fetched during this time.");
                _logger.LogError($"List of Mbsdata could not be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
        /// <summary>
        /// Method to fetch Mbs item based on itemnum
        /// </summary>
        /// <param name="itemnum"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("mbs_data/{itemnum}")]
        public async Task<IActionResult> GetMbsItemDetails(long itemnum)
        {
            var apiResponse = new ApiResponse<MbsDataView>();

            try
            {

                apiResponse = await _mbsBAL.GetMbsDataBAL(itemnum, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Mbs Data cannot be fetched at this time.");
                _logger.LogError($"Mbs Data cannot be fetched at this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to fetch mbs data based on filter
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("mbs_data")]
        public async Task<IActionResult> GetMbsItemDetails([FromQuery]QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<List<MbsDataView>>();

            try
            {

                apiResponse = await _mbsBAL.ListMbsDataBAL(queryModel, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Mbs Data cannot be fetched at this time.");
                _logger.LogError($"Mbs Data cannot be fetched at this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
    }
}
