﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.MainService.Utility;
using Capstone2.RestServices.MIMS.Interfaces;
using Capstone2.RestServices.MIMS.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Capstone2.RestServices.MIMS.Controllers
{
    [Route("mims")]
    [ApiController]
    [ApiExceptionFilter]
    [Authorize]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class ProductController : BaseController
    {
        private readonly ILogger<ProductController> _logger;
        private readonly IProductBAL _productBAL;
        public ProductController(IOptions<MvcOptions> options, ILogger<ProductController> logger, IProductBAL productBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _productBAL = productBAL;
            options.Value.InputFormatters.Insert(0, InputFormater.GetInputFormater());
        }

        ///// <summary>
        ///// Search for Products
        ///// </summary>
        ///// <param name="paramsRequest">paramsRequest</param>
        ///// <returns>Returns a list of Product Names and Product IDs matching the given search criteria</returns>
        //[HttpGet]
        //[Route("get")]
        //[ProducesResponseType(typeof(List<Models.Product>), StatusCodes.Status200OK)]
        //public async Task<IActionResult> GetProducts([FromQuery] SearchParam paramsRequest)
        //{
        //    var apiResponse = new ApiResponse<List<Models.Product>>();
        //    try
        //    {
        //        apiResponse = await _productBAL.GetProducts(paramsRequest);
        //        return apiResponse.StatusCode switch
        //        {
        //            StatusCodes.Status200OK => Ok(apiResponse),
        //            StatusCodes.Status400BadRequest => BadRequest(apiResponse),
        //            StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
        //            _ => BadRequest(apiResponse),
        //        };
        //    }
        //    catch (Exception Ex)
        //    {
        //        apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
        //        apiResponse.Result = null;
        //        apiResponse.Errors.Add("MIMS Product cannot be fetched at this time.");
        //        _logger.LogError($"MIMS Product cannot be fetched {Ex.Message} {Ex.StackTrace}");
        //        return BadRequest(apiResponse);
        //    }
        //}

        /// <summary>
        /// Search for Products
        /// </summary>
        /// <param name="queryModel">paramsRequest</param>
        /// <returns>Returns a list of Product Names and Product IDs matching the given search criteria</returns>
        [HttpGet]
        [Route("get")]
        [ProducesResponseType(typeof(List<Models.Product>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetProducts([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<List<Models.Product>>();
            try
            {
                apiResponse = await _productBAL.GetProducts(queryModel);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("MIMS Product cannot be fetched at this time.");
                _logger.LogError($"MIMS Product cannot be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


        /// <summary>
        /// Search for Product Details
        /// </summary>
        /// <param name="paramsRequest">paramsRequest</param>
        /// <returns>Returns Product Details using the given Product ID</returns>
        [HttpGet]
        [Route("detail")]
        [ProducesResponseType(typeof(Models.ProductDetail), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetProductDetails(string productId, string fields)
        {
            var apiResponse = new ApiResponse<Models.ProductDetail>();
            try
            {
                apiResponse = await _productBAL.GetProductDetails(productId, fields);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("MIMS Product detail cannot be fetched at this time.");
                _logger.LogError($"MIMS Product detail cannot be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// List Allergy Classes
        /// </summary>
        /// <returns>Get a list of the most commonly reported Allergy Classes to be used as pick list</returns>
        [HttpGet]
        [Route("getallergy")]
        [ProducesResponseType(typeof(Models.AllergyClasses), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetAllergyClasses()
        {
            var apiResponse = new ApiResponse<List<Models.AllergyClasses>>();
            try
            {
                apiResponse = await _productBAL.GetAllergyClasses();
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("MIMS Product detail cannot be fetched at this time.");
                _logger.LogError($"MIMS Product detail cannot be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }


        /// <summary>
        /// Get the Version
        /// </summary>
        /// <returns>Returns the data version of MIMS API</returns>
        [HttpGet]
        [Route("getversion")]
        [ProducesResponseType(typeof(Models.VersionInfo), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetVersion()
        {
            var apiResponse = new ApiResponse<Models.VersionInfo>();
            try
            {
                apiResponse = await _productBAL.GetVersion();
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("MIMS Data Version cannot be fetched at this time.");
                _logger.LogError($"MIMS data version cannot be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Search PBS item by PBS code
        /// </summary>
        /// <param name="pbsCode">pbsCode</param>
        /// <param name="fields">fields=pbsProgram,prescriberTypes,pbsBrands,mpps || Use fields parameter to limit the PBS details to be returned in the response. Specify the required field names in a comma separated format. If this parameter is not specified, all fields will be returned in the response</param>
        /// <param name="manufacturerCode">manufacturerCode=AF || Used to limit the brand specific PBS information to a specific manufacturer</param>
        /// <returns>Returns PBS details using the given PBS code</returns>
        [HttpGet]
        [Route("pbsitems/{pbsCode}")]
        [ProducesResponseType(typeof(PBSDetail), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetPBSItemsDetails(string pbsCode, string fields, string manufacturerCode)
        {
            var apiResponse = new ApiResponse<PBSDetail>();
            try
            {
                apiResponse = await _productBAL.GetPBSDetails(pbsCode, fields, manufacturerCode);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("MIMS PBSItems Details cannot be fetched at this time.");
                _logger.LogError($"MIMS PBSItems Details cannot be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Search Full PI details by fullPI Id
        /// </summary>
        /// <param name="fullPI_Id">fullPI_Id</param>
        /// <returns>Returns the Full Product Information (Full PI) Details using the given Full PI ID</returns>
        [HttpGet]
        [Route("fullPIs/{fullPI_Id}")]
        [ProducesResponseType(typeof(FullPIDetail), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetFullPIDetails(string fullPI_Id)
        {
            var apiResponse = new ApiResponse<FullPIDetail>();
            try
            {
                apiResponse = await _productBAL.GetFullPIDetails(fullPI_Id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("MIMS FullPI Details cannot be fetched at this time.");
                _logger.LogError($"MIMS FullPI Details cannot be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Search CMI details by CMI Id
        /// </summary>
        /// <param name="cmiId">cmiId</param>
        /// <param name="format">Valid formats: pdf, reducedpdf, html, xml, json || By default is JSON, if nothing is specified</param>
        /// <param name="offmarket">true, if format=xml is specified </param>
        /// <returns>Returns CMI Details using the given CMI ID</returns>
        [HttpGet]
        [Route("cmi/{cmiId}")]
        [ProducesResponseType(typeof(CMIDetail), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetCMIDetails(string cmiId, string format = "json", bool offmarket = false)
        {
            var apiResponse = new ApiResponse<CMIDetail>();
            try
            {
                apiResponse = await _productBAL.GetCMIDetails(cmiId, format, offmarket);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("MIMS CMI Details cannot be fetched at this time.");
                _logger.LogError($"MIMS CMI Details cannot be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpGet("healthcheck")]
        [Microsoft.AspNetCore.Authorization.AllowAnonymous]
        [TelemetryTracing(logRequest: false, logResponse: false)]
        public IActionResult HealthCheck()
        {
            return Ok(StatusCodes.Status200OK);
        }

    }
}
