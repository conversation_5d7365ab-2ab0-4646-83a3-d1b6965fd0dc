﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.RestServices.Patient.Interfaces;
using Capstone2.RestServices.Patient.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Patient.Controllers
{
    [ApiController]
    [Route("patient")]
   // [Authorize]
    [Framework.RestApi.Filters.ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class HealthCareTeamController : BaseController
    {
        private readonly ILogger<HealthCareTeamController> _logger;
        private readonly IHealthCareTeamBAL _HealthCareBAL;

        public HealthCareTeamController(ILogger<HealthCareTeamController> logger, IHealthCareTeamBAL HealthCareBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _HealthCareBAL = HealthCareBAL;
        }

        /// <summary>
        /// Api to add a new HealthCareData
        /// </summary>
        /// <param name="inputHealthCareData"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("{patient_id}/healthcare_team")]
        public async Task<IActionResult> AddHealthCareData(long patient_id, [FromBody] HealthCareTeamDetails inputHealthCareData)
        {
            var apiResponse = new ApiResponse<string>();
            try
            {
                apiResponse = await _HealthCareBAL.AddHealthCareData(baseHttpRequestContext, patient_id, inputHealthCareData,false);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while creating HealthCareData.Please check all variables");
                _logger.LogError($"Error while creating HealthCareData {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to fetch a HealthCareData based on id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("{patient_id}/healthcare_team")]
        public async Task<IActionResult> GetHealthCareData(long patient_id)
        {
            var apiResponse = new ApiResponse<InputHealthCareTeamDetails>();
            try
            {
                apiResponse = await _HealthCareBAL.GetHealthCareData(baseHttpRequestContext, patient_id,false);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting HealthCareData");
                _logger.LogError($"Error while getting HealthCareData {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to update a HealthCareData
        /// </summary>
        /// <param name="inputHealthCareData"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("{patient_id}/healthcare_team")]
        public async Task<IActionResult> EditHealthCareData(long patient_id, [FromBody] HealthCareTeamDetails inputHealthCareData)
        {
            var apiResponse = new ApiResponse<string>();

            try
            {
                apiResponse = await _HealthCareBAL.EditHealthCareData(baseHttpRequestContext, patient_id, inputHealthCareData,false);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("HealthCareData cannot be Edited.Please try again later.");
                _logger.LogError($"HealthCareData cannot be Edited {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to fetch a HealthCareData based on id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("referral_details")]
        [ProducesResponseType(typeof(ApiResponse<List<ReferralDetailInfo>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetReferralDetails([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<List<ReferralDetailInfo>>();
            try
            {
                apiResponse = await _HealthCareBAL.GetReferralDetails(baseHttpRequestContext, queryModel);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting ReferralDetails");
                _logger.LogError($"Error while getting ReferralDetails {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        // <summary>
        /// Method to fetch ReferralDetails based on query
        /// </summary>
        [HttpGet]
        [Route("referral/{patientId}")]
        [ProducesResponseType(typeof(ApiResponse<List<ReferralDetailsPatient>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetReferralDetailsPatient(long patientId)
        {
            var apiResponse = new ApiResponse<List<ReferralDetailsPatient>>();
            try
            {
                apiResponse = await _HealthCareBAL.GetReferralDetailsPatient(baseHttpRequestContext.OrgId, patientId);
                return Ok(apiResponse);
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting ReferralDetails");
                _logger.LogError($"Error while getting ReferralDetails {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to insert new ReferralDetail
        /// </summary>
        [HttpPost]
        [Route("referral/{patientId}")]
        [ProducesResponseType(typeof(ApiResponse<ReferralDetailsPatient>), StatusCodes.Status200OK)]
        public async Task<IActionResult> InsertReferralDetails(long patientId, [FromBody] List<ReferralDetailsPatient> model)
        {
            var apiResponse = new ApiResponse<string>();
            try
            {
                apiResponse = await _HealthCareBAL.InsertReferralDetails(baseHttpRequestContext, patientId, model);
                return Ok(apiResponse);
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while adding ReferralDetails");
                _logger.LogError($"Error while adding ReferralDetails {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to update an existing ReferralDetail
        /// </summary>
        [HttpPut]
        [Route("referral")]
        [ProducesResponseType(typeof(ApiResponse<ReferralDetailsPatient>), StatusCodes.Status200OK)]
        public async Task<IActionResult> EditReferralDetails(long patientId, [FromBody] List<ReferralDetailsPatient> model)
        {
            var apiResponse = new ApiResponse<string>();
            try
            {
                apiResponse = await _HealthCareBAL.EditReferralDetails(baseHttpRequestContext, patientId, model);
                return Ok(apiResponse);
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while updating ReferralDetails");
                _logger.LogError($"Error while updating ReferralDetails {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        // <summary>
        /// Method to fetch ReferralDetails based on query
        /// </summary>
        [HttpGet]
        [Route("referralsource")]
        [ProducesResponseType(typeof(ApiResponse<List<ReferralSourceModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetAllReferralSource()
        {
            var apiResponse = new ApiResponse<List<ReferralSourceModel>>();
            try
            {
                apiResponse = await _HealthCareBAL.GetAllReferralSource(baseHttpRequestContext.OrgId);
                return Ok(apiResponse);
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting GetAllReferralSource");
                _logger.LogError($"Error while getting GetAllReferralSource {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpGet]
        [Route("referralsourcesubtype")]
        [ProducesResponseType(typeof(ApiResponse<List<ReferralSourceDetails>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetAllReferralSourceSubType()
        {
            var apiResponse = new ApiResponse<List<ReferralSourceDetails>>();
            try
            {
                apiResponse = await _HealthCareBAL.GetAllReferralSourceSubType(baseHttpRequestContext.OrgId);
                return Ok(apiResponse);
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting GetAllReferralSourceSubType");
                _logger.LogError($"Error while getting GetAllReferralSourceSubType {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


        /// <summary>
        /// Api to add a new Referal details 
        /// </summary>
        /// <param name="inputHealthCareData"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("{patient_id}/healthcare_team_patreg")]
        public async Task<IActionResult> AddHealthCareDataPatReg(long patient_id, [FromBody] List<ReferralDetail> referalDetails)
        {
            var apiResponse = new ApiResponse<string>();
            try
            {
                HealthCareTeamDetails inputHealthCareData = new HealthCareTeamDetails();
                inputHealthCareData.ReferralDetails = referalDetails;

                apiResponse = await _HealthCareBAL.AddHealthCareData(baseHttpRequestContext, patient_id, inputHealthCareData, true);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while creating Referral Details.Please check all variables");
                _logger.LogError($"Error while creating Referral Details {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to fetch a Referral Details 
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("{patient_id}/healthcare_team_patreg")]
        public async Task<IActionResult> GetHealthCareDataPatReg(long patient_id)
        {
            var apiResponse = new ApiResponse<List<InputReferralDetail>>();
            try
            {
                 
                var apiResponsebackend = await _HealthCareBAL.GetHealthCareData(baseHttpRequestContext, patient_id, true);
                apiResponse.Result = apiResponsebackend.Result.ActiveReferralDetails;
                apiResponse.StatusCode = apiResponsebackend.StatusCode;
                apiResponse.Message = apiResponsebackend.Message;
                apiResponse.Errors = apiResponsebackend.Errors;


                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting Referral Data");
                _logger.LogError($"Error while getting Referral Data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to update a HealthCareData
        /// </summary>
        /// <param name="inputHealthCareData"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("{patient_id}/healthcare_team_patreg")]
        public async Task<IActionResult> EditHealthCareDatapatreg(long patient_id, [FromBody] List<ReferralDetail> referalDetails)
        {
            var apiResponse = new ApiResponse<string>();

            try
            {
                HealthCareTeamDetails inputHealthCareData = new HealthCareTeamDetails();
                inputHealthCareData.ReferralDetails = referalDetails;

                apiResponse = await _HealthCareBAL.EditHealthCareData(baseHttpRequestContext, patient_id, inputHealthCareData, true);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Referral Data cannot be Edited.Please try again later.");
                _logger.LogError($"Referral Data cannot be Edited {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }






    }
}
