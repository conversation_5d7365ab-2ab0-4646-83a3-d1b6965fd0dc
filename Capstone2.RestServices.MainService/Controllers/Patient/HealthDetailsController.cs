﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.RestServices.Patient.Interfaces;
using Capstone2.RestServices.Patient.Models;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Patient.Controllers
{
    [ApiController]
    [Route("patient")]
   // [Authorize]
    [Framework.RestApi.Filters.ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class HealthDetailsController : BaseController
    {
        private readonly ILogger<HealthDetailsController> _logger;
        private readonly IHealthDetailsBAL _healthDetailsBAL;

        public HealthDetailsController(ILogger<HealthDetailsController> logger, IHealthDetailsBAL HealthDetailsBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _healthDetailsBAL = HealthDetailsBAL;
        }

        /// <summary>
        /// Api to add a new HealthDetails
        /// </summary>
        /// <param name="inputHealthDetails"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("{patient_id}/health_details")]
        public async Task<IActionResult> AddHealthDetails(long patient_id, [FromBody] HealthDetails inputHealthDetails)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _healthDetailsBAL.AddHealthDetails(baseHttpRequestContext, patient_id, inputHealthDetails);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while creating HealthDetails.Please check all variables");
                _logger.LogError($"Error while creating HealthDetails {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to fetch a HealthDetails based on id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("{patient_id}/health_details")]
        public async Task<IActionResult> GetHealthDetails(long patient_id)
        {
            var apiResponse = new ApiResponse<HealthDetails>();
            try
            {
                apiResponse = await _healthDetailsBAL.GetHealthDetails(baseHttpRequestContext, patient_id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting HealthDetails");
                _logger.LogError($"Error while getting HealthDetails {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to update a HealthDetails
        /// </summary>
        /// <param name="inputHealthDetails"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("{patient_id}/health_details/{health_details_id}")]
        public async Task<IActionResult> EditHealthDetails(long patient_id, long health_details_id, [FromBody] HealthDetails inputHealthDetails)
        {
            var apiResponse = new ApiResponse<string>();
            try
            {
                apiResponse = await _healthDetailsBAL.EditHealthDetails(baseHttpRequestContext, patient_id, health_details_id, inputHealthDetails);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("HealthDetails cannot be Edited.Please try again later.");
                _logger.LogError($"HealthDetails cannot be Edited {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
    }
}
