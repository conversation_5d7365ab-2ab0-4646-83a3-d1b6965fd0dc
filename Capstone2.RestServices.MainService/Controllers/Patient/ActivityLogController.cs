﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.Patient.Interfaces;
using Capstone2.RestServices.Patient.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Patient.Controllers
{
    [ApiController]
    [Route("patient")]
    //[Authorize]
    [ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class ActivityLogController :BaseController
    {
        private readonly ILogger<ActivityLogController> _logger;
        private readonly IActivityLogBAL _activityLogBAL;
        public ActivityLogController(ILogger<ActivityLogController> logger, IActivityLogBAL activityLogBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _activityLogBAL = activityLogBAL;
        }

       
        [HttpPost]
        [Route("{patient_id}/activity_logs")]
        [ProducesResponseType(typeof(ApiResponse<long?>), StatusCodes.Status200OK)]
        public async Task<IActionResult> AddActivityLog(long patient_id, [FromBody] ActivityLog activityLog)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _activityLogBAL.AddActivityLog(activityLog, baseHttpRequestContext, patient_id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while creating new Activity Log.Please check all variables");
                _logger.LogError($"Error while creating new Activity Log {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }
        /// <summary>
        /// Updating Activity log
        /// </summary>
        /// <param name="patient_id"></param>
        /// <param name="id"></param>
        /// <param name="activityLog"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("{patient_id}/activity_logs/{id}")]
        [ProducesResponseType(typeof(ApiResponse<long?>), StatusCodes.Status200OK)]
        public async Task<IActionResult> UpdateActivityLog(long patient_id,long id ,[FromBody] ActivityLog activityLog)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _activityLogBAL.UpdateActivityLog(activityLog, baseHttpRequestContext, patient_id,id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while updating Activity Log.Please check all variables");
                _logger.LogError($"Error while updating Activity Log {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }

        /// <summary>
        /// Updating Activity log
        /// </summary>
        /// <param name="patient_id"></param>
        /// <param name="id"></param>
        /// <param name="activityLog"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("activity_logs/activitylogchild_entries/{statusid}/{filedetailsid}")]
        [ProducesResponseType(typeof(ApiResponse<long?>), StatusCodes.Status200OK)]
        public async Task<IActionResult> UpdateActivityLogChild(int statusid, int filedetailsid, [FromBody] ActivityLogChildEntryInfo activityLogChildView)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _activityLogBAL.UpdateActivityLogChild(activityLogChildView, baseHttpRequestContext, statusid, filedetailsid);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while updating Activity Log.Please check all variables");
                _logger.LogError($"Error while updating Activity Log {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }

        /// <summary>
        /// Method to fetch list of Activity logs
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("{patient_id}/activity_logs")]
        public async Task<IActionResult> ListActivityLogs(long patient_id, [FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<QueryResultList<ActivityLogList>>();
            try
            {
                apiResponse = await _activityLogBAL.ListActivityLogs(baseHttpRequestContext, queryModel, patient_id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    StatusCodes.Status403Forbidden => new ObjectResult(apiResponse) { StatusCode = StatusCodes.Status403Forbidden },
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Activty logs could not be fetched during this time");
                _logger.LogError($"Activty logs could not be fetched during this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
        [HttpPost]
        [Route("{patient_id}/activity_logs/activitylogchild_entries")]
        public async Task<IActionResult> AddActivityLogChild(long patient_id, [FromBody] ActivityLogChildEntryInfo activityLogChildView)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _activityLogBAL.AddActivityLogChild(baseHttpRequestContext, activityLogChildView, patient_id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Activity log entry cannot be added successfully");
                _logger.LogError($"Activity log entry cannot be added successfully {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpGet]
        [Route("activity_logs/{activitylog_id}/activitylogchild_entries")]
        public async Task<IActionResult> ListActivityLogChildEntries(long activitylog_id, [FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<QueryResultList<ActivityLogChildList>>();
            try
            {
                apiResponse = await _activityLogBAL.ListActivityLogChildBAL(baseHttpRequestContext, queryModel, activitylog_id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of Child Activty logs could not be fetched during this time");
                _logger.LogError($"List of Child Activty logs could not be fetched during this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to fetch Activity log by Id
        /// </summary>
        /// <param name="">Id</param>
        /// <returns></returns>
        [HttpGet]
        [Route("{patient_id}/activity_logs/{id}")]
        public async Task<IActionResult> GetActivityLogById(long patient_id,long id)
        {
            var apiResponse = new ApiResponse<ActivityLogList>();
            try
            {
                apiResponse = await _activityLogBAL.GetActivityLogById(baseHttpRequestContext, patient_id,id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    StatusCodes.Status403Forbidden => new ObjectResult(apiResponse) { StatusCode = StatusCodes.Status403Forbidden },
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Activty log could not be fetched during this time");
                _logger.LogError($"Error while creating AccountHolder {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


    }
}
