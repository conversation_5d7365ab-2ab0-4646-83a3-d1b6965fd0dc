﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.Patient.Interfaces;
using Capstone2.RestServices.Patient.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Patient.Controllers
{
    [ApiController]
    [Route("patient")]
  //  [Authorize]
    [ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class MediaLibraryController : BaseController
    {
        private readonly ILogger<MediaLibraryController> _logger;
        private readonly IMediaLibraryBAL _mediaLibraryBAL;

        public MediaLibraryController(ILogger<MediaLibraryController> logger, IMediaLibraryBAL mediaLibraryBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _mediaLibraryBAL = mediaLibraryBAL;
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="inputTag"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("tags")]
        public async Task<IActionResult> AddTag([FromBody] Tag inputTag)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _mediaLibraryBAL.AddTag(inputTag, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Tags cannot be added at this time.Please try again later.");
                _logger.LogError($"Tags cannot be added at this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to retrieve list of tags
        /// </summary>
        /// <param name="st"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("tags")]
        public async Task<IActionResult> ListTags(string st)
        {
            var apiResponse = new ApiResponse<List<Tag>>();
            try
            {
                apiResponse = await _mediaLibraryBAL.ListTags(st, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Tags cannot be fetched at this time.Please try again later.");
                _logger.LogError($"Tags cannot be fetched at this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to add  media to patient media library
        /// </summary>
        /// <param name="media"></param>
        /// <param name="patient_id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("{patient_id}/patientmedia_assocs")]
        public async Task<IActionResult> AddPatientMedias([FromBody] InputPatientMediaAssoc media, long patient_id)
        {
            var apiResponse = new ApiResponse<string>();
            try
            {
                apiResponse = await _mediaLibraryBAL.AddPatientMedias(media, patient_id, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Media cannot be added at this time.Please try again later.");
                _logger.LogError($"Media cannot be added at this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to fetch  media to patient media library
        /// </summary>
        /// <param name="media"></param>
        /// <param name="patient_id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("{patient_id}/patientmedia_assocs/{id}")]
        public async Task<IActionResult> GetPatientMedia(long id, long patient_id)
        {
            var apiResponse = new ApiResponse<InputPatientMedia>();
            try
            {
                apiResponse = await _mediaLibraryBAL.GetPatientMedia(id, patient_id, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Media cannot be fetched at this time.Please try again later.");
                _logger.LogError($"Media cannot be fetched at this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to fetch  media to patient media library
        /// </summary>
        /// <param name="media"></param>
        /// <param name="patient_id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("{patient_id}/patientmedia_assocs/file_details")]
        public async Task<IActionResult> GetPatientMedias([FromQuery] string Ids, long patient_id)
        {
            var apiResponse = new ApiResponse<List<ListPatientMediaAssoc>>();
            try
            {
                apiResponse = await _mediaLibraryBAL.GetMultiplePatientMedias(patient_id, baseHttpRequestContext, Ids);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Media cannot be fetched at this time.Please try again later.");
                _logger.LogError($"Media cannot be fetched at this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to list media library 
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("{patient_id}/patientmedia_assocs")]
        public async Task<IActionResult> ListMedia(long patient_id, [FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<QueryResultList<ListPatientMediaAssoc>>();
            try
            {
                apiResponse = await _mediaLibraryBAL.ListMediaLibrary(baseHttpRequestContext, queryModel, patient_id);

                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Media Library could not be fetched during this time.");
                _logger.LogError($"Media Library could not be fetched during this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to delete a media
        /// </summary>
        /// <param name="patient_id"></param>
        /// <param name="id"></param>
        /// <param name="deleteObject"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("{patient_id}/patientmedia_assocs")]
        public async Task<IActionResult> DeleteMedia(long patient_id, [FromQuery] DeleteObject deleteObject)
        {
            var apiResponse = new ApiResponse<string>();
            try
            {
                apiResponse = await _mediaLibraryBAL.DeleteMediaColl(baseHttpRequestContext, patient_id, deleteObject);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    StatusCodes.Status403Forbidden => new ObjectResult(apiResponse) { StatusCode = StatusCodes.Status403Forbidden },
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {                
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Media could not be deleted during this time.");
                _logger.LogError($"Media could not be deleted during this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to update a media 
        /// </summary>
        /// <param name="inputPatientMedia"></param>
        /// <param name="patient_id"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("{patient_id}/patientmedia_assocs")]
        public async Task<IActionResult> EditPatientMediaColl([FromBody] InputPatientMediaAssoc inputPatientMediaAssocs, long patient_id)
        {
            var apiResponse = new ApiResponse<string>();
            try
            {
                apiResponse = await _mediaLibraryBAL.EditPatientMediaColl(inputPatientMediaAssocs, baseHttpRequestContext, patient_id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    StatusCodes.Status403Forbidden => new ObjectResult(apiResponse) { StatusCode = StatusCodes.Status403Forbidden },
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Media cannot be updated during this time.");
                _logger.LogError($"Media cannot be updated during this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// GetMedia By passing FileDetailsId
        /// </summary>
        /// <param name="patient_id"></param>
        /// <param name="id"></param>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("{patient_id}/patientmedia_assocs_by_filedetailsid/{id}")]
        public async Task<IActionResult> GetPatientMediaByFileDetailsId(long patient_id, long id)
        {
            var apiResponse = new ApiResponse<InputPatientMedia>();
            try
            {
                apiResponse = await _mediaLibraryBAL.GetPatientMediaByFileDetailsId(id, patient_id, baseHttpRequestContext);

                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Media Library could not be fetched during this time.");
                _logger.LogError($"Media Library could not be fetched during this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


        /// <summary>
        /// GetMedia By passing FileDetailsId
        /// </summary>
        /// <param name="patient_id"></param>
        /// <param name="id"></param>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("{patient_id}/bulk_media_email")]
        public async Task<IActionResult> SendBulkMediaEmail(long patient_id, [FromBody]Email email)
        {
            var apiResponse = new ApiResponse<Email>();
            try
            {
                apiResponse = await _mediaLibraryBAL.SendBulkMediaEmail(patient_id, email, baseHttpRequestContext);

                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Email cannot be sent during this time.");
                _logger.LogError($"Email cannot be sent during this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to update a media 
        /// </summary>
        /// <param name="inputPatientMedia"></param>
        /// <param name="patient_id"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("{patient_id}/patientmedia_assocs_tags_patients")]
        public async Task<IActionResult> EditPatientMediaTags([FromBody] PatientMediaTagsUpdate patientMediaTagsUpdate, long patient_id)
        {
            var apiResponse = new ApiResponse<string>();
            try
            {
                apiResponse = await _mediaLibraryBAL.EditPatientMediaTags(baseHttpRequestContext, patient_id, patientMediaTagsUpdate);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    StatusCodes.Status403Forbidden => new ObjectResult(apiResponse) { StatusCode = StatusCodes.Status403Forbidden },
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Media cannot be updated during this time.");
                _logger.LogError($"Media cannot be updated during this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

    }
}
