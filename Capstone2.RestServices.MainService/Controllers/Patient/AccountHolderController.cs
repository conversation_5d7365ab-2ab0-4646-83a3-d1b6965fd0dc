﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.RestServices.Patient.Interfaces;
using Capstone2.RestServices.Patient.Models;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Patient.Controllers
{
    [ApiController]
    [Route("patient")]
    // [Authorize]
    [Framework.RestApi.Filters.ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class AccountHolderController : BaseController
    {
        private readonly ILogger<AccountHolderController> _logger;
        private readonly IAccountHolderBAL _accountHolderBAL;

        public AccountHolderController(ILogger<AccountHolderController> logger, IAccountHolderBAL AccountHolderBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _accountHolderBAL = AccountHolderBAL;
        }

        /// <summary>
        /// Api to add a new AccountHolder
        /// </summary>
        /// <param name="inputAccountHolder"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("{patient_id}/account_holder")]
        public async Task<IActionResult> AddAccountHolder(long patient_id, [FromBody] AccountHolder inputAccountHolder)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _accountHolderBAL.AddAccountHolderData(baseHttpRequestContext, patient_id, inputAccountHolder);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while creating AccountHolder");
                _logger.LogError($"Error while creating AccountHolder {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to fetch a AccountHolder based on id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("{patient_id}/account_holder")]
        public async Task<IActionResult> GetAccountHolder(long patient_id)
        {
            var apiResponse = new ApiResponse<AccountHolder>();
            try
            {
                apiResponse = await _accountHolderBAL.GetAccountHolderData(baseHttpRequestContext, patient_id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting AccountHolder");
                _logger.LogError($"Error while getting AccountHolder {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpPut]
        [Route("update_ihi/{patient_id}")]
        public async Task<IActionResult>UpdateIHI(long patient_id, [FromBody] Dictionary<string, object> request)
        {
            var apiResponse = new ApiResponse<string>();
            try
            {
                apiResponse = await _accountHolderBAL.UpdateIHI(baseHttpRequestContext, patient_id, request);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting AccountHolder");
                _logger.LogError($"Error while getting AccountHolder {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to update a AccountHolder
        /// </summary>
        /// <param name="inputAccountHolder"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("{patient_id}/account_holder/{account_holder_id}")]
        public async Task<IActionResult> EditAccountHolder(long patient_id, long account_holder_id, [FromBody] AccountHolder inputAccountHolder)
        {
            var apiResponse = new ApiResponse<string>();

            try
            {
                apiResponse = await _accountHolderBAL.EditAccountHolderData(baseHttpRequestContext, patient_id, account_holder_id, inputAccountHolder);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while editing AccountHolder");
                _logger.LogError($"Error while editing AccountHolder {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


        /// <summary>
        /// Method to fetch a AccountHolder with patient details based on patientid
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("{patient_id}/account_holder_info")]
        public async Task<IActionResult> GetAccountHolderInfo(long patient_id)
        {
            var apiResponse = new ApiResponse<ClaimantPatientDetailInfo>();
            try
            {
                apiResponse = await _accountHolderBAL.GetAccountHolderInfo(baseHttpRequestContext, patient_id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting AccountHolder");
                _logger.LogError($"Error while getting AccountHolder {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
    }
}
