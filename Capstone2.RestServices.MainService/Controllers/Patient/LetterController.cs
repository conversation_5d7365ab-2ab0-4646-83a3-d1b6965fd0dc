﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.Patient.Interfaces;
using Capstone2.RestServices.Patient.Models;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Entities.Hub;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Patient.Controllers
{
    [ApiController]
    [Route("patient")]
   // [Authorize]
    [ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class LetterController : BaseController
    {
        private readonly ILogger<LetterController> _logger;
        private readonly ILetterBAL _letterBAL;
        public LetterController(ILogger<LetterController> logger, ILetterBAL letterBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _letterBAL = letterBAL;
        }

        /// <summary>
        /// Api to add a new Letter
        /// </summary>
        /// <param name="patient_id"></param>
        /// <param name="inputLetter"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("{patient_id}/letters")]
        public async Task<IActionResult> AddLetter(long patient_id, [FromBody] Letter inputLetter)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _letterBAL.AddLetterBAL(patient_id, inputLetter, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error while creating Letter");
                _logger.LogError($"Error while creating Letter {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }

        /// <summary>
        /// Method to Fetch a letter based on id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("{patient_id}/letters/{id}")]
        public async Task<IActionResult> GetLetter(long patient_id, long id)
        {
            var apiResponse = new ApiResponse<LetterOutput>();
            try
            {
                apiResponse = await _letterBAL.GetLetterBAL(patient_id, baseHttpRequestContext, id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting Letter");
                _logger.LogError($"Error while getting Letter {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to update a letter
        /// </summary>
        /// <param name="patient_id"></param>
        /// <param name="id"></param>
        /// <param name="inputLetter"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("{patient_id}/letters/{id}")]
        public async Task<IActionResult> EditLetter(long patient_id, long id, [FromBody] EmailLetter inputLetter)
        {
            var apiResponse = new ApiResponse<string>();
            try
            {

                apiResponse = await _letterBAL.EditLetterBAL(patient_id, id, inputLetter, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while editing Letter");
                _logger.LogError($"Error while editing Letter {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to delete a letter based on id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("{patient_id}/letters/{id}")]
        public async Task<IActionResult> DeleteLetter(long patient_id, long id, [FromQuery] DeleteObject deleteObject)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _letterBAL.DeleteLetterBAL(patient_id, id, baseHttpRequestContext, deleteObject);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while deleting Letter");
                _logger.LogError($"Error while deleting Letter {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to delete a letter based on id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("downloadFinanceLetter/{patient_id}/letters/{id}")]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
        public async Task<IActionResult> DownloadFinanceLetter(long patient_id, long id, [FromBody] EmailLetterView emailLetterView)
        {
            var apiResponse = new ApiResponse<string>();
            try
            {
                apiResponse = await _letterBAL.DownloadFinanceLetter(patient_id, id, emailLetterView, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while downloading Finance Letter");
                _logger.LogError($"Error while downloading Finance Letter {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        //** LETTER ACTIONS  **//

        /// <summary>
        /// AddLetterActions
        /// </summary>
        /// <param name="letter_id"></param>
        /// <param name="letterActions"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("letters/{letter_id}/actions")]
        [ProducesResponseType(typeof(ApiResponse<long?>), StatusCodes.Status200OK)]
        public async Task<IActionResult> AddLetterActions(long letter_id, [FromBody] LetterActions letterActions)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _letterBAL.AddLetterActions(letter_id, letterActions, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error while creating Letter Actions");
                _logger.LogError($"Error while creating Letter Actions {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// GetLetterActions
        /// </summary>
        /// <param name="letter_id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("letters/{letter_id}/actions")]
        [ProducesResponseType(typeof(ApiResponse<LetterActions>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetLetterActions(long letter_id)
        {
            var apiResponse = new ApiResponse<LetterActions>();
            try
            {
                apiResponse = await _letterBAL.GetLetterActions(letter_id, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error while getting Letter Actions");
                _logger.LogError($"Error while getting Letter Actions {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// EditLetterActions
        /// </summary>
        /// <param name="letter_id"></param>
        /// <param name="letterActions"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("letters/{letter_id}/actions")]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
        public async Task<IActionResult> EditLetterActions(long letter_id, [FromBody] LetterActions letterActions)
        {
            var apiResponse = new ApiResponse<string>();
            try
            {
                apiResponse = await _letterBAL.EditLetterActions(letter_id, letterActions, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error while updating Letter Actions");
                _logger.LogError($"Error while updating Letter Actions. {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        //** PATIENT ACTIONS - LETTER DETAILS  **//

        /// <summary>
        /// get letter_details_by only letter id
        /// </summary>
        /// <param name="letter_id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("letter_details_by_id/{letter_id}")]
        [ProducesResponseType(typeof(ApiResponse<PatientActionsLetterDetails>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetPatientActionsLetterDetails(long letter_id)
        {
            var apiResponse = new ApiResponse<PatientActionsLetterDetails>();
            try
            {
                apiResponse = await _letterBAL.GetPatientActionsLetterDetails(letter_id, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting Letter Details");
                _logger.LogError($"Error while getting Letter Details. {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


       /// <summary>
       /// Method to send email
       /// </summary>
       /// <param name="patient_id"></param>
       /// <param name="email"></param>
       /// <returns></returns>
        [HttpPost]
        [Route("email/{patient_id}")]
        [ProducesResponseType(typeof(ApiResponse<Email>), StatusCodes.Status200OK)]
        public async Task<IActionResult> SendPatientEmail(long patient_id,[FromBody] EmailView email)
        {
            var apiResponse = new ApiResponse<Email>();
            try
            {
                apiResponse = await _letterBAL.SendPatientEmail(patient_id,email, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while sending Email.");
                _logger.LogError($"Error while sending Email. {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to add child entry for letter operations like Print,Download and Email
        /// </summary>
        /// <param name="patient_id"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("{patient_id}/letters/{id}/activitylogchild_entries")]
        public async Task<IActionResult> AddLetterActivityChild(long patient_id, long id, [FromBody] LetterActivityChild letterActivity)
        {
            var apiResponse = new ApiResponse<long>();
            try
            {

                apiResponse = await _letterBAL.AddLetterActivityChild(patient_id, id, letterActivity, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = 0;
                apiResponse.Errors.Add("Letter Activity Child can not be added");
                _logger.LogError($"Error while adding Letter Activity Child {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


        /// <summary>
        /// Method to send SMS(phone)
        /// </summary>
        /// <param name="patient_id"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("{patient_id}/sendadhocsms")]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
        public async Task<IActionResult> SendPatientSMS(long patient_id, [FromBody] SmsInput sms)
        {
            var apiResponse = new ApiResponse<string>();
            try
            {
                apiResponse = await _letterBAL.SendPatientSms(patient_id, sms, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while sending Sms.");
                _logger.LogError($"Error while sending Sms. {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
      
        [HttpPost]
        [Route("ehealth/{patient_id}")]
        [ProducesResponseType(typeof(ApiResponse<EHealthMessage>), StatusCodes.Status200OK)]
        public async Task<IActionResult> InitiateEHealthAcitivty(long patient_id, [FromBody] InputMessageRequestBody inputMessageRequestBody)
        {
            var apiResponse = new ApiResponse<EHealthMessage>();
            try
            {
                apiResponse = await _letterBAL.InitiateEHealthAcitivty(patient_id, inputMessageRequestBody, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while initiating Ehealth acitivity.");
                _logger.LogError($"Error while initiating Ehealth acitivity. {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


    }
}
