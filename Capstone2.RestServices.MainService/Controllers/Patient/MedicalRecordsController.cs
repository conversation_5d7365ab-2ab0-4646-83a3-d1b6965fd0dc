﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.RestServices.Patient.Interfaces;
using Capstone2.RestServices.Patient.Models;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Patient.Controllers
{
    [ApiController]
    [Route("patient")]
    [Framework.RestApi.Filters.ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class MedicalRecordsController : BaseController
    {
        private readonly ILogger<MedicalRecordsController> _logger;
        private readonly IMedicalRecordsBAL _medicalRecordsBAL;

        public MedicalRecordsController(ILogger<MedicalRecordsController> logger, IMedicalRecordsBAL medicalRecordsBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _medicalRecordsBAL = medicalRecordsBAL;
        }

        /// <summary>
        /// Method to fetch a MedicalRecords based on patient id
        /// </summary>
        /// <param name="patient_id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("{patient_id}/medical_records")]
        public async Task<IActionResult> GetMedicalRecords(long patient_id)
        {
            var apiResponse = new ApiResponse<MedicalRecords>();
            try
            {
                apiResponse = await _medicalRecordsBAL.GetMedicalRecords(baseHttpRequestContext, patient_id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting MedicalRecords");
                _logger.LogError($"Error while getting MedicalRecords {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Api to add a new MedicalRecords
        /// </summary>
        /// <param name="patient_id"></param>
        /// <param name="inputMedicalRecords"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("{patient_id}/medical_records")]
        public async Task<IActionResult> AddMedicalRecords(long patient_id, [FromBody] MedicalProcedures inputMedicalRecords)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _medicalRecordsBAL.AddMedicalRecord(baseHttpRequestContext, patient_id, inputMedicalRecords);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while creating MedicalRecords.Please check all variables");
                _logger.LogError($"Error while creating MedicalRecords {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to update a MedicalRecords
        /// </summary>
        /// <param name="patient_id"></param>
        /// <param name="id"></param>
        /// <param name="inputMedicalRecords"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("{patient_id}/medical_records/{id}")]
        public async Task<IActionResult> EditMedicalRecords(long patient_id, long id, [FromBody] MedicalProcedures inputMedicalRecords)
        {
            var apiResponse = new ApiResponse<string>();

            try
            {
                apiResponse = await _medicalRecordsBAL.EditMedicalRecord(baseHttpRequestContext, patient_id, id, inputMedicalRecords);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("MedicalRecords cannot be Edited.Please try again later.");
                _logger.LogError($"MedicalRecords cannot be Edited {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Api to Delete a new MedicalRecords
        /// </summary>
        /// <param name="patient_id"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("{patient_id}/medical_records/{id}")]
        public async Task<IActionResult> DeleteMedicalRecords(long patient_id, long id)
        {
            var apiResponse = new ApiResponse<string>();
            try
            {
                apiResponse = await _medicalRecordsBAL.DeleteMedicalRecord(baseHttpRequestContext, patient_id, id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while deleting MedicalRecords.Please check all variables");
                _logger.LogError($"Error while deleting MedicalRecords {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to verify a MedicalRecords
        /// </summary>
        /// <param name="patient_id"></param>
        /// <param name="inputVerificationStatus"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("{patient_id}/medical_records/verify")]
        public async Task<IActionResult> VerifyMedicalRecords(long patient_id, [FromBody] VerificationStatus inputVerificationStatus)
        {
            var apiResponse = new ApiResponse<string>();

            try
            {
                apiResponse = await _medicalRecordsBAL.VerifyMedicalRecord(baseHttpRequestContext, patient_id, inputVerificationStatus);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("MedicalRecords cannot be Edited.Please try again later.");
                _logger.LogError($"MedicalRecords cannot be Edited {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to Validate a Medical Procedure Name
        /// </summary>
        /// <param name="patient_id"></param>
        /// <param name="type"></param>
        /// <param name="record_id"></param>
        /// <param name="search_term"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("{patient_id}/medical_records/validate")]
        public async Task<IActionResult> ValidateMedicalProcedureName(long patient_id, [FromQuery] short type, [FromQuery] long record_id, [FromQuery] string search_term)
        {
            var apiResponse = new ApiResponse<bool>();

            try
            {
                apiResponse = await _medicalRecordsBAL.ValidateMedicalProcedureName(baseHttpRequestContext, patient_id, type, record_id, search_term);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = false;
                apiResponse.Errors.Add("MedicalRecords cannot be Edited.Please try again later.");
                _logger.LogError($"MedicalRecords cannot be Edited {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to update a Medical Procedure Status
        /// </summary>
        /// <param name="patient_id"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("{patient_id}/medical_records/{id}/status")]
        public async Task<IActionResult> UpdateProcedureStatus(long patient_id, long id, [FromBody] ProcedureStatusUpdateRequest procedureStatusUpdateRequest)
        {
            var apiResponse = new ApiResponse<string>();

            try
            {
                apiResponse = await _medicalRecordsBAL.UpdateProcedureStatus(baseHttpRequestContext, patient_id, id, procedureStatusUpdateRequest);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("MedicalRecords cannot be Edited.Please try again later.");
                _logger.LogError($"MedicalRecords cannot be Edited {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
    }
}
