﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.Patient.Interfaces;
using Capstone2.RestServices.Patient.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Patient.Controllers
{
    [ApiController]
    [Route("patient")]
    //[Authorize]
    [ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class PatientController : BaseController
    {
        private readonly ILogger<PatientController> _logger;
        private readonly IPatientBAL _patientBAL;
        private readonly ICommBAL _commBAL;
        public PatientController(ILogger<PatientController> logger, IPatientBAL patientBAL, ICommBAL commBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _patientBAL = patientBAL;
            _commBAL = commBAL;
        }

        /// <summary>
        /// Api to add a new Patient
        /// </summary>
        /// <param name="inputPatientDetail"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("patient_details")]
        public async Task<IActionResult> AddPatientDetails([FromBody] PatientDetail inputPatientDetail)
        {
            var apiResponse = new ApiResponse<long>();
            try
            {
                apiResponse = await _patientBAL.AddPatientDetailsBAL(inputPatientDetail, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Error while creating Patient.Please check all variables.");
                _logger.LogError($"Error while creating Patient {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }

        /// <summary>
        /// Method to fetch patients
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("patient_details")]
        public async Task<IActionResult> ListPatient([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<QueryResultList<PatientSearch>>();
            try
            {
                apiResponse = await _patientBAL.ListPatient(baseHttpRequestContext, queryModel);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of Patients could not be fetched during this time.");
                _logger.LogError($"List of Patients could not be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to Fetch a patient based on id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("patient_details/{id}")]
        public async Task<IActionResult> GetPatientDetails(long id)
        {
            var apiResponse = new ApiResponse<InputPatientDetail>();
            try
            {
                apiResponse = await _patientBAL.GetPatientDetails(baseHttpRequestContext, id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting patient Data");
                _logger.LogError($"Error while getting patient Data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to update a company
        /// </summary>
        /// <param name="inputPatientDetail"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("patient_details/{id}")]
        public async Task<IActionResult> EditPatientDetails(long id, [FromBody] PatientDetail inputPatientDetail)
        {
            var apiResponse = new ApiResponse<string>();

            try
            {
                apiResponse = await _patientBAL.EditPatientDetails(id, inputPatientDetail, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    StatusCodes.Status403Forbidden => new ObjectResult(apiResponse) { StatusCode = StatusCodes.Status403Forbidden },

                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Patient cannot be Edited.Please try again later.");
                _logger.LogError($"Patient cannot be Edited {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Api to add a new communication notes
        /// </summary>
        /// <param name="inputCommNote"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("{patient_id}/communication_notes")]
        public async Task<IActionResult> AddCommNote(long patient_id, [FromBody] CommunicationNote inputCommNote)
        {
            var apiResponse = new ApiResponse<string>();
            try
            {
                apiResponse = await _commBAL.AddCommNoteBAL(inputCommNote, baseHttpRequestContext.OrgId, baseHttpRequestContext.UserId, patient_id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while creating Communication Note.Please check all variables");
                _logger.LogError($"Error while creating Communication Note {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }

        /// <summary>
        /// Method to Fetch a Communication Notes based on id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("{patient_id}/communication_notes/{id}")]
        public async Task<IActionResult> GetCommNote(long patient_id, long id)
        {
            var apiResponse = new ApiResponse<CommunicationNote>();
            try
            {
                apiResponse = await _commBAL.GetCommNote(baseHttpRequestContext.OrgId, id, patient_id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting Communication Notes Data");
                _logger.LogError($"Error while getting Communication Notes Data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to fetch list of Communication notes 
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("{patient_id}/communication_notes")]
        public async Task<IActionResult> ListCommNotes(long patient_id, [FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<QueryResultList<ListCommunicationNote>>();
            try
            {
                apiResponse = await _commBAL.ListCommNotes(baseHttpRequestContext.OrgId, queryModel, patient_id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of Communication notes could not be fetched during this time.");
                _logger.LogError($"List of Communication notes could not be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


        /// <summary>
        /// Api to edit communication notes
        /// </summary>
        /// <param name="inputCommNote"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("{patient_id}/communication_notes/{id}")]
        public async Task<IActionResult> EditCommNote(long patient_id, long id, [FromBody] CommunicationNote inputCommNote)
        {
            var apiResponse = new ApiResponse<string>();
            try
            {
                apiResponse = await _commBAL.EditCommNoteBAL(inputCommNote, baseHttpRequestContext.OrgId, baseHttpRequestContext.UserId, id, patient_id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while editing Communication Note.Please check all variables");
                _logger.LogError($"Error while editing Communication Note {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }

        }

        /// <summary>
        /// Method to Fetch a All version Communication Notes based on id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("{patient_id}/communication_notes/{id}/allversions")]
        public async Task<IActionResult> GetCommNoteAllVersions(long patient_id, long id)
        {
            var apiResponse = new ApiResponse<List<CommunicationNote>>();
            try
            {
                apiResponse = await _commBAL.GetCommNoteAllVersion(baseHttpRequestContext.OrgId, id, patient_id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting Communication Notes Data");
                _logger.LogError($"Error while getting Communication Notes Data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Get the letter template for a given patient
        /// </summary>
        /// <param name="letter"></param>
        /// <param name="patient_id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("{patient_id}/letter_template/{letter_template_id}")]
        public async Task<IActionResult> GetLetterTemplate(long patient_id, long letter_template_id)
        {
            var apiResponse = new ApiResponse<LetterTemplateView>();
            try
            {
                apiResponse = await _patientBAL.GetLetterTemplateBAL(patient_id, letter_template_id, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Letter Template Data can not be fetched.Please try again later.");
                _logger.LogError($"Letter Template Data can not be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to patch Patient Data
        /// </summary>
        /// <param name="id"></param>
        /// <param name="patientUpdateView"></param>
        /// <returns></returns>
        [HttpPatch]
        [Route("patient_details/{id}")]
        public async Task<IActionResult> PartialUpdateAppointment(long id, [FromBody] PatientUpdateView patientUpdateView)
        {
            var apiResponse = new ApiResponse<string>();

            try
            {
                apiResponse = await _patientBAL.PartialUpdatePatient(baseHttpRequestContext, id, patientUpdateView);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while updating Appointment.");
                _logger.LogError($"Error while updating Appointment {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
        /// <summary>
        /// Method to patch Patient Data
        /// </summary>
        /// <param name="id"></param>
        /// <param name="patientUpdateView"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("patient_details/patch/{id}")]
        public async Task<IActionResult> PartialUpdateAppointment1(long id, [FromBody] PatientUpdateView patientUpdateView)
        {
            var apiResponse = new ApiResponse<string>();

            try
            {
                apiResponse = await _patientBAL.PartialUpdatePatient(baseHttpRequestContext, id, patientUpdateView);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while updating Appointment.");
                _logger.LogError($"Error while updating Appointment {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
        /// <summary>
        /// Method to match patients
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("check_duplicate")]
        public async Task<IActionResult> CheckDuplicate([FromBody] PatientVerificationRequest patientdetails)
        {
            var apiResponse = new ApiResponse<PatientVerificationResponse>();
            try
            {
                apiResponse = await _patientBAL.CheckDuplicate(patientdetails);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of Patients could not be fetched during this time.");
                _logger.LogError($"List of Patients could not be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to Fetch a patient based on id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("patient_detailsById/{id}")]
        public async Task<IActionResult> GetPatientDetailsById(long id)
        {
            var apiResponse = new ApiResponse<PatientDetailInfo>();
            try
            {
                _logger.LogError("GetPatientDetails");
                apiResponse = await _patientBAL.GetPatientDetailsById(baseHttpRequestContext, id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting patient Data");
                _logger.LogError($"Error while getting patient Data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Get the letter template for a given patient
        /// </summary>
        /// <param name="letter"></param>
        /// <param name="patient_id"></param>
        /// <returns></returns>
        [ProducesResponseType(typeof(ApiResponse<PatientTagsModel>), StatusCodes.Status200OK)]
        [HttpGet]
        [Route("GetLetterTemplateData")]
        public async Task<IActionResult> GetLetterTemplateData([FromQuery] PatientLetterTemplateFilterModel patientLetterTemplateFilterModel)
        {
            var apiResponse = new ApiResponse<PatientTagsModel>();
            try
            {
                apiResponse = await _patientBAL.GetLetterTemplateData(patientLetterTemplateFilterModel, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Letter template Tags cannot be found at this time.");
                _logger.LogError($"Letter template Tags cannot be found at this time {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        [HttpGet("healthcheck")]
        [AllowAnonymous]
        [TelemetryTracing(logRequest: false, logResponse: false)]
        public IActionResult HealthCheck()
        {
            return Ok(StatusCodes.Status200OK);
        }


        /// <summary>
        /// Method to fetch a AccountHolder & Referral details based on patientid
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("{patient_id}/referral_account_holder_info")]
        public async Task<IActionResult> GetReferralAndAccountHolderInfo(long patient_id)
        {
            var apiResponse = new ApiResponse<ClaimantPatientDetailInfo>();
            try
            {
                apiResponse = await _patientBAL.GetReferralAndAccountHolderInfo(baseHttpRequestContext, patient_id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting AccountHolder");
                _logger.LogError($"Error while getting AccountHolder {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to Get Deposit letter tempalte Data.
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("invoiceDepositReceipt/{patient_id}/invoice/{invoiceDetailsId}")]
        [ProducesResponseType(typeof(ApiResponse<DepositInvoiceLetterTemplate>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetPaymentDepositORInvoiceReceipt(long patient_id, long invoiceDetailsId)
        {
            var apiResponse = new ApiResponse<DepositInvoiceLetterTemplate>();
            try
            {
                apiResponse = await _patientBAL.GetPaymentDepositORInvoiceReceipt(patient_id, invoiceDetailsId, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while Get Payment Deposit OR InvoiceReceipt Finance Letter");
                _logger.LogError($"Error while Get Payment Deposit OR InvoiceReceipt Finance Letter {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to Get Deposit letter tempalte Data for lodgment or statement .
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("invoice/LodgmentAndStatement/{patient_id}/invoice/{invoiceDetailsId}/{transactionId}")]
        [ProducesResponseType(typeof(ApiResponse<DepositInvoiceLetterTemplate>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetLodgmentAndStatementReceipt(long patient_id, long invoiceDetailsId, string transactionId)
        {
            var apiResponse = new ApiResponse<DepositInvoiceLetterTemplate>();
            try
            {
                _logger.LogError("GetLodgmentAndStatementReceipt Started");
                apiResponse = await _patientBAL.GetLodgmentAndStatementReceipt(patient_id, invoiceDetailsId, transactionId, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while Get LodgmentAndStatement Finance Letter");
                _logger.LogError($"Error while Get LodgmentAndStatement Finance Letter {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


        /// <summary>
        /// Method to Get Invoice custom billing letter tempalte Data 
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("invoice/GetInvoiceCustomBilling/{patient_id}/invoice/{invoiceDetailsId}")]
        [ProducesResponseType(typeof(ApiResponse<DepositInvoiceLetterTemplate>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetInvoiceCustomBilling(long patient_id, long invoiceDetailsId)
        {
            var apiResponse = new ApiResponse<DepositInvoiceLetterTemplate>();
            try
            {
                apiResponse = await _patientBAL.GetInvoiceCustomBilling(patient_id, invoiceDetailsId, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while Get LodgmentAndStatement Finance Letter");
                _logger.LogError($"Error while Get LodgmentAndStatement Finance Letter {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to Get OECTemplate Data 
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("invoice/GetOECTemplate/{patient_id}/invoice/{invoiceDetailsId}/{transactionId}")]
        [ProducesResponseType(typeof(ApiResponse<DepositInvoiceLetterTemplate>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetOECTemplate(long patient_id, long invoiceDetailsId, string transactionId)
        {
            var apiResponse = new ApiResponse<DepositInvoiceLetterTemplate>();
            try
            {
                apiResponse = await _patientBAL.GetOECTemplate(patient_id, invoiceDetailsId, transactionId, baseHttpRequestContext);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while Get GetOECTemplate");
                _logger.LogError($"Error while Get GetOECTemplate {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


        /// <summary>
        /// Method to Fetch a patient info for hcp - patient consent
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("hcp/patient_consent/{id}")]
        public async Task<IActionResult> GetPatientDetailsForHCPPatientConsent(long id)
        {
            var apiResponse = new ApiResponse<PatientViewForClaims>();
            try
            {
                apiResponse = await _patientBAL.GetPatientDetailsForHCPPatientConsent(baseHttpRequestContext, id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting patient Data");
                _logger.LogError($"Error while getting patient Data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to Fetch a patient info -recordid
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("patient_details/record_id/{recordId}")]
        public async Task<IActionResult> GetPatientDetailsFromRecordId(long recordId)
        {
            var apiResponse = new ApiResponse<PatientInfo>();
            try
            {
                apiResponse = await _patientBAL.GetPatientDetailsFromRecordId(baseHttpRequestContext, recordId);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting patient Data");
                _logger.LogError($"Error while getting patient Data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to get list of HIServiceDetails by PatientID
        /// </summary>
        /// <param name="patient_id">PatientID</param>
        /// <returns>List of HIServiceDetails</returns>
        [HttpGet]
        [Route("{patient_id}/hiservice/hiservice_details")]
        public async Task<IActionResult> GetHIServiceDetails(long patient_id)
        {
            var apiResponse = new ApiResponse<List<HIServiceDetailLogs>>();
            try
            {
                apiResponse = await _patientBAL.GetHIServiceDetails(baseHttpRequestContext, patient_id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting HIServiceDetails Data");
                _logger.LogError($"Error while getting HIServiceDetails Data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to add a new HIServiceDetails
        /// </summary>
        /// <param name="patient_id"></param>
        /// <param name="inputHIServiceDetail"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("{patient_id}/hiservice/ihinumber")]
        public async Task<IActionResult> AddHIServiceDetails(long patient_id, [FromBody] HIServiceDetail inputHIServiceDetail)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _patientBAL.AddHIServiceDetail(baseHttpRequestContext, patient_id, inputHIServiceDetail);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while creating HIServiceDetails.Please check all variables");
                _logger.LogError($"Error while creating HIServiceDetails {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to update a HIServiceDetails
        /// </summary>
        /// <param name="patient_id"></param>
        /// <param name="id"></param>
        /// <param name="inputHIServiceDetail"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("{patient_id}/hiservice/ihinumber/{id}")]
        public async Task<IActionResult> EditHIServiceDetails(long patient_id, long id, [FromBody] HIServiceDetail inputHIServiceDetail)
        {
            var apiResponse = new ApiResponse<string>();

            try
            {
                apiResponse = await _patientBAL.EditHIServiceDetail(baseHttpRequestContext, patient_id, id, inputHIServiceDetail);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("HIServiceDetails cannot be Edited.Please try again later.");
                _logger.LogError($"HIServiceDetails cannot be Edited {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to fetch a Patient Draft Data based on id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("patient_draft_data/{prid}")]
        [ProducesResponseType(typeof(ApiResponse<PatientDraftData>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetPatientDraftData(long prid)
        {
            var apiResponse = new ApiResponse<PatientDraftData>();
            try
            {
                apiResponse = await _patientBAL.GetPatientDraftData(baseHttpRequestContext, prid);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting Patient draft Data");
                _logger.LogError($"Error while getting Patient draft Data {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

    }
}
