﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.RestServices.Patient.Interfaces;
using Capstone2.RestServices.Patient.Models;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Patient.Controllers
{
    [ApiController]
    [Route("patient")]
    [Framework.RestApi.Filters.ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class PathologyRequestController : BaseController
    {
        private readonly ILogger<PathologyRequestController> _logger;
        private readonly IPathologyRequestBAL _PathologyRequestBAL;

        public PathologyRequestController(ILogger<PathologyRequestController> logger, IPathologyRequestBAL PathologyRequestBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _PathologyRequestBAL = PathologyRequestBAL;
        }

        /// <summary>
        /// Api to add a new PathologyRequestData
        /// </summary>
        /// <param name="inputPathologyRequestData"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("{patient_id}/pathology_request")]
        [ProducesResponseType(typeof(ApiResponse<long?>), StatusCodes.Status200OK)]
        public async Task<IActionResult> AddPathologyRequestData(long patient_id, [FromBody] PathologyRequest inputPathologyRequestData)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _PathologyRequestBAL.AddPathologyRequestData(baseHttpRequestContext, patient_id, inputPathologyRequestData);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while creating PathologyRequestData.Please check all variables");
                _logger.LogError($"Error while creating PathologyRequestData {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to fetch a PathologyRequestData based on id
        /// </summary>
        /// <param name="patient_id"></param>
        /// <param name="communicationNotesId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("{patient_id}/pathology_request/{pathology_id}")]
        [ProducesResponseType(typeof(ApiResponse<PathologyRequest>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetPathologyRequestData(long patient_id, long pathology_id)
        {
            var apiResponse = new ApiResponse<PathologyRequestView>();
            try
            {
                apiResponse = await _PathologyRequestBAL.GetPathologyRequestData(baseHttpRequestContext, patient_id, pathology_id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting PathologyRequestData");
                _logger.LogError($"Error while getting PathologyRequestData {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to fetch a PathologyRequestData based on id
        /// </summary>
        /// <param name="patient_id"></param>
        /// <param name="communicationNotesId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("{patient_id}/pathology_request/by_communication_notes_id/{communication_notes_id}")]
        [ProducesResponseType(typeof(ApiResponse<PathologyRequest>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetPathologyRequestDataByCommunicationNotesId(long patient_id, long communication_notes_id)
        {
            var apiResponse = new ApiResponse<PathologyRequestView>();
            try
            {
                apiResponse = await _PathologyRequestBAL.GetPathologyRequestDataByCommunicationNotesId(baseHttpRequestContext, patient_id, communication_notes_id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting PathologyRequestData");
                _logger.LogError($"Error while getting PathologyRequestData {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to update a PathologyRequestData
        /// </summary>
        /// <param name="patient_id"></param>
        /// <param name="id"></param>
        /// <param name="inputPathologyRequestData"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("{patient_id}/pathology_request/{pathology_id}")]
        [ProducesResponseType(typeof(ApiResponse<long?>), StatusCodes.Status200OK)]
        public async Task<IActionResult> EditPathologyRequestData(long patient_id, long pathology_id, [FromBody] PathologyRequest inputPathologyRequestData)
        {
            var apiResponse = new ApiResponse<long?>();

            try
            {
                apiResponse = await _PathologyRequestBAL.EditPathologyRequestData(baseHttpRequestContext, patient_id, pathology_id, inputPathologyRequestData);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("PathologyRequestData cannot be Edited.Please try again later.");
                _logger.LogError($"PathologyRequestData cannot be Edited {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to delete a PathologyRequestData
        /// </summary>
        /// <param name="patient_id"></param>
        /// <param name="id"></param>
        /// <param name="inputPathologyRequestData"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("{patient_id}/pathology_request/{pathology_id}")]
        [ProducesResponseType(typeof(ApiResponse<long?>), StatusCodes.Status200OK)]
        public async Task<IActionResult> DeletePathologyRequestData(long patient_id, long pathology_id)
        {
            var apiResponse = new ApiResponse<long?>();

            try
            {
                apiResponse = await _PathologyRequestBAL.DeletePathologyRequestData(baseHttpRequestContext, patient_id, pathology_id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("PathologyRequestData cannot be deleted.Please try again later.");
                _logger.LogError($"PathologyRequestData cannot be deleted {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


        //** MEDIA  **//

        /// <summary>
        /// Add medias
        /// </summary>
        /// <param name="patient_id"></param>
        /// <param name="pathology_id"></param>
        /// <param name="inputPathologyRequestData"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("{patient_id}/pathology_request/{pathology_id}/media")]
        [ProducesResponseType(typeof(ApiResponse<List<PathologyRequestMediaAssocsIds>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> AddPathologyRequestMedia(long patient_id, long pathology_id, [FromBody] List<PathologyRequestMediaAssocs> inputPathologyRequestData)
        {
            var apiResponse = new ApiResponse<List<PathologyRequestMediaAssocsIds>>();
            try
            {
                apiResponse = await _PathologyRequestBAL.AddPathologyRequestMedia(baseHttpRequestContext, pathology_id, inputPathologyRequestData);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while creating PathologyRequestData.Please check all variables");
                _logger.LogError($"Error while creating PathologyRequestData {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Fetch list of files
        /// </summary>
        /// <param name="patient_id"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("{patient_id}/pathology_request/{pathology_id}/media")]
        [ProducesResponseType(typeof(ApiResponse<PathologyRequestMediaAssocsView>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetPathologyRequestMedia(long patient_id, long pathology_id)
        {
            var apiResponse = new ApiResponse<List<PathologyRequestMediaAssocsView>>();
            try
            {
                apiResponse = await _PathologyRequestBAL.GetPathologyRequestMedia(baseHttpRequestContext, pathology_id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting PathologyRequestData");
                _logger.LogError($"Error while getting PathologyRequestData {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Delete Pathology erquest media
        /// </summary>
        /// <param name="patient_id"></param>
        /// <param name="pathology_id"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("{patient_id}/pathology_request/{pathology_id}/media/{media_id}")]
        [ProducesResponseType(typeof(ApiResponse<long?>), StatusCodes.Status200OK)]
        public async Task<IActionResult> DeletePathologyRequestMedia(long patient_id, long pathology_id, long media_id)
        {
            var apiResponse = new ApiResponse<long?>();

            try
            {
                apiResponse = await _PathologyRequestBAL.DeletePathologyRequestMedia(baseHttpRequestContext, pathology_id, media_id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("PathologyRequestData cannot be deleted.Please try again later.");
                _logger.LogError($"PathologyRequestData cannot be deleted {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


        //** PATHOLOGY RESULTS REVIEW  **//

        /// <summary>
        /// Add Pathology Results
        /// </summary>
        /// <param name="patient_id"></param>
        /// <param name="result_id"></param>
        /// <param name="inputPathologyResponseReview"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("{patient_id}/pathology_result/{result_media_id}")]
        [ProducesResponseType(typeof(ApiResponse<long?>), StatusCodes.Status200OK)]
        public async Task<IActionResult> AddPathologyResult(long patient_id, long result_media_id, [FromBody] PathologyResponseReview inputPathologyResponseReview)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _PathologyRequestBAL.AddPathologyResponseData(baseHttpRequestContext, result_media_id, inputPathologyResponseReview);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while creating PathologyRequestData.Please check all variables");
                _logger.LogError($"Error while creating PathologyRequestData {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Get Pathology Result data
        /// </summary>
        /// <param name="patient_id"></param>
        /// <param name="result_id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("{patient_id}/pathology_result/{result_media_id}")]
        [ProducesResponseType(typeof(ApiResponse<PathologyResponseReview>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetPathologyResult(long patient_id, long result_media_id)
        {
            var apiResponse = new ApiResponse<PathologyResponseReview>();
            try
            {
                apiResponse = await _PathologyRequestBAL.GetPathologyResponseData(baseHttpRequestContext, result_media_id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting PathologyRequestData");
                _logger.LogError($"Error while getting PathologyRequestData {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Edit Pathology responses
        /// </summary>
        /// <param name="patient_id"></param>
        /// <param name="result_id"></param>
        /// <param name="inputPathologyResponseReview"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("{patient_id}/pathology_result/{result_media_id}")]
        [ProducesResponseType(typeof(ApiResponse<long?>), StatusCodes.Status200OK)]
        public async Task<IActionResult> EditPathologyResult(long patient_id, long result_media_id, [FromBody] PathologyResponseReview inputPathologyResponseReview)
        {
            var apiResponse = new ApiResponse<long?>();

            try
            {
                apiResponse = await _PathologyRequestBAL.EditPathologyReponseData(baseHttpRequestContext, result_media_id, inputPathologyResponseReview);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("PathologyRequestData cannot be Edited.Please try again later.");
                _logger.LogError($"PathologyRequestData cannot be Edited {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }


        //** PATIENT ACTIONS - PATHOLOGY DETAILS  **//

        /// <summary>
        /// GetPatientActionsPathologyDetailsById
        /// </summary>
        /// <param name="pathology_request_id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("pathology_details_by_id/{pathology_request_id}")]
        [ProducesResponseType(typeof(ApiResponse<PatientActionsPathologyDetails>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetPatientActionsPathologyDetailsById(long pathology_request_id)
        {
            var apiResponse = new ApiResponse<PatientActionsPathologyDetails>();
            try
            {
                apiResponse = await _PathologyRequestBAL.GetPatientActionsPathologyDetailsById(baseHttpRequestContext, pathology_request_id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting PathologyRequestData");
                _logger.LogError($"Error while getting PathologyRequestData {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// GetPatientActionsPathologyDetailsByMediaId
        /// </summary>
        /// <param name="pathology_response_media_id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("pathology_details_by_media_id/{pathology_response_media_id}")]
        [ProducesResponseType(typeof(ApiResponse<PatientActionsPathologyDetails>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetPatientActionsPathologyDetailsByMediaId(long pathology_response_media_id)
        {
            var apiResponse = new ApiResponse<PatientActionsPathologyDetails>();
            try
            {
                apiResponse = await _PathologyRequestBAL.GetPatientActionsPathologyDetailsByMediaId(baseHttpRequestContext, pathology_response_media_id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting PathologyRequestData");
                _logger.LogError($"Error while getting PathologyRequestData {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
    }
}
