﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.RestServices.Patient.Interfaces;
using Capstone2.RestServices.Patient.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Patient.Controllers
{
    [ApiController]
    [Route("patient")]
    // [Authorize]
    [Framework.RestApi.Filters.ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class SmsController : BaseController
    {
        private readonly ILogger<SmsController> _logger;
        private readonly ISmsBAL _smsBAL;

        public SmsController(ILogger<SmsController> logger, ISmsBAL smsBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _smsBAL = smsBAL;
        }

        /// <summary>
        /// Method to fetch List of Sms History
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("sms_history")]
        public async Task<IActionResult> ListSmsHistory([FromQuery] QueryModel queryModel)
        {
            var apiResponse = new ApiResponse<QueryResultList<SmsHistoryView>>();

            try
            {
                apiResponse = await _smsBAL.ListSmsHistoryBAL(baseHttpRequestContext, queryModel);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("List of Sms History could not be fetched during this time.");
                _logger.LogError($"List of Sms History could not be fetched {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Function to Fetch the token from UserTokenAssocs
        /// </summary>
        /// <param name="tokenAssoc"></param>
        /// <returns></returns>
        [HttpGet("user_token_assoc")]
        [ProducesResponseType(typeof(ApiResponse<long>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetUserTokenAssoc()
        {
            var apiResponse = new ApiResponse<long>();
            try
            {
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Result = 1;
                apiResponse.Message = "success";

                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = 0;
                apiResponse.Errors.Add("User Token cannot be fetched.");
                _logger.LogError($"User Token cannot be fetched. {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }



    }
}
