﻿using Capstone2.Framework.ApplicationInsights.Attributes;
using Capstone2.Framework.RestApi.Controllers;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.RestServices.Patient.Interfaces;
using Capstone2.RestServices.Patient.Models;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Patient.Controllers
{
    [ApiController]
    [Route("patient")]
   // [Authorize]
    [Framework.RestApi.Filters.ApiExceptionFilter]
    [TelemetryTracing(logRequest: true, logResponse: true)]
    public class ConsultNoteTemplateController : BaseController
    {
        private readonly ILogger<ConsultNoteTemplateController> _logger;
        private readonly IConsultNoteTemplateBAL _consultNoteTemplateBAL;

        public ConsultNoteTemplateController(ILogger<ConsultNoteTemplateController> logger, IConsultNoteTemplateBAL consultNoteTemplateBAL, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _logger = logger;
            _consultNoteTemplateBAL = consultNoteTemplateBAL;
        }

        /// <summary>
        /// Api to add a new ConsultNoteTemplateData
        /// </summary>
        /// <param name="inputConsultNoteTemplateData"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("{patient_id}/consult_note_template")]
        public async Task<IActionResult> AddConsultNoteTemplateData(long patient_id, [FromBody] ConsultNotes inputConsultNoteTemplateData)
        {
            var apiResponse = new ApiResponse<long?>();
            try
            {
                apiResponse = await _consultNoteTemplateBAL.AddConsultNoteTemplateData(baseHttpRequestContext, patient_id, inputConsultNoteTemplateData);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {               
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while creating ConsultNoteTemplateData.Please check all variables");
                _logger.LogError($"Error while creating ConsultNoteTemplateData {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to fetch a ConsultNoteTemplateData based on id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("{patient_id}/consult_note_template/{id}/template_id")]
        public async Task<IActionResult> GetConsultNoteTemplateId(long patient_id, long id)
        {
            var apiResponse = new ApiResponse<ConsultNoteTemplateIds>();
            try
            {
                apiResponse = await _consultNoteTemplateBAL.GetConsultNoteTemplateId(baseHttpRequestContext, patient_id, id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting ConsultNoteTemplateData");
                _logger.LogError($"Error while getting ConsultNoteTemplateData {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to fetch a ConsultNoteTemplateData based on id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("{patient_id}/consult_note_template/{id}")]
        [ProducesResponseType(typeof(ApiResponse<ConsultNotesView>), StatusCodes.Status200OK)]

        public async Task<IActionResult> GetConsultNoteTemplateData(long patient_id, long id)
        {
            var apiResponse = new ApiResponse<ConsultNotesView>();
            try
            {
                apiResponse = await _consultNoteTemplateBAL.GetConsultNoteTemplateData(baseHttpRequestContext, patient_id, id);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting ConsultNoteTemplateData");
                _logger.LogError($"Error while getting ConsultNoteTemplateData {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }

        /// <summary>
        /// Method to update a ConsultNoteTemplateData
        /// </summary>
        /// <param name="inputConsultNoteTemplateData"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("{patient_id}/consult_note_template/{id}")]
        [ProducesResponseType(typeof(ApiResponse<long?>), StatusCodes.Status200OK)]

        public async Task<IActionResult> EditConsultNoteTemplateData(long patient_id, long id, [FromBody] ConsultNotes inputConsultNoteTemplateData)
        {
            var apiResponse = new ApiResponse<long?>();

            try
            {
                apiResponse = await _consultNoteTemplateBAL.EditConsultNoteTemplateData(baseHttpRequestContext, patient_id, id, inputConsultNoteTemplateData);
                return apiResponse.StatusCode switch
                {
                    StatusCodes.Status200OK => Ok(apiResponse),
                    StatusCodes.Status400BadRequest => BadRequest(apiResponse),
                    StatusCodes.Status401Unauthorized => Unauthorized(apiResponse),
                    _ => BadRequest(apiResponse),
                };
            }
            catch (Exception Ex)
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Result = null;
                apiResponse.Errors.Add("ConsultNoteTemplateData cannot be Edited.Please try again later.");
                _logger.LogError($"ConsultNoteTemplateData cannot be Edited {Ex.Message} {Ex.StackTrace}");
                return BadRequest(apiResponse);
            }
        }
    }
}
