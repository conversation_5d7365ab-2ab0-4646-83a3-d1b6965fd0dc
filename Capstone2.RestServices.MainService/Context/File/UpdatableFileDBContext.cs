﻿using Capstone2.Framework.RestApi.Context;
using Capstone2.RestServices.File.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace Capstone2.RestServices.File.Context
{
    public class UpdatableFileDBContext: EncryptionDbContext
    {
        private TenantConnection _tenantReference;

        public UpdatableFileDBContext(TenantConnection tenantReference, IConfiguration configuration) : base(configuration)
        {
            _tenantReference = tenantReference;
        }
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseSqlServer(_tenantReference.ClientConnectionString);
        }

        public virtual DbSet<FileDetails> FileDetails { get; set; }
        public virtual DbSet<FileEnums> FileEnums { get; set; }
        public virtual DbSet<FileRepository> FileRepository { get; set; }
        public virtual DbSet<Organisation> Organisation { get; set; }


        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.HasDefaultSchema("File");
            modelBuilder.Entity<Organisation>().ToTable("Organisation", "Company");
        }



    }
}
