﻿using Capstone2.Framework.RestApi.Context;
using Capstone2.RestServices.File.Models;
using Capstone2.Shared.Models.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace Capstone2.RestServices.File.Context
{
    public class ReadOnlyFileDBContext : EncryptionDbContext
    {
        private TenantConnection _tenantReference;

        public ReadOnlyFileDBContext(TenantConnection tenantReference, IConfiguration configuration) : base(configuration)
        {
            _tenantReference = tenantReference;
        }
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseSqlServer(_tenantReference.ClientConnectionString);
        }

        public virtual DbSet<Models.FileDetails> FileDetails { get; set; }
        public virtual DbSet<FileEnums> FileEnums { get; set; }
        public virtual DbSet<Models.FileRepository> FileRepository { get; set; }
        public virtual DbSet<Models.Organisation> Organisation { get; set; }
        public DbSet<RolesPermissionsAssoc> RolesPermissionsAssocs { get; set; }



        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.HasDefaultSchema("File");
            modelBuilder.Entity<Models.Organisation>().ToTable("Organisation", "Company");
            modelBuilder.Entity<RolesPermissionsAssoc>().ToTable("RolesPermissionsAssocs", "Utility");

        }

    }
}
