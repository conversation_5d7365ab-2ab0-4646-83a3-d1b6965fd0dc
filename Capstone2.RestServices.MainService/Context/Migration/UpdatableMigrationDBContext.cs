﻿using Microsoft.EntityFrameworkCore;
using Capstone2.RestServices.Migration.Models;
using Capstone2.Shared.Models.Entities;
using Capstone2.Framework.RestApi.Context;
using Microsoft.Extensions.Configuration;

namespace Capstone2.RestServices.Migration.Context
{
    public class UpdatableMigrationDBContext : EncryptionDbContext
    {
        private TenantConnection _tenantReference;

        public UpdatableMigrationDBContext(TenantConnection tenantReference, IConfiguration configuration) : base(configuration)
        {
            _tenantReference = tenantReference;
        }
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseSqlServer(_tenantReference.ClientConnectionString);
        }
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            //Configure default schema
            modelBuilder.HasDefaultSchema("Migration");
        }
        public virtual DbSet<Upload> Uploads { get; set; }
        public virtual DbSet<MediaUpload> MediaUploads { get; set; }

    }
}
