﻿using Capstone2.Framework.RestApi.Context;
using Capstone2.Shared.Models.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Capstone2.RestServices.Migration.Models;

namespace Capstone2.RestServices.Migration.Context
{
    public class ReadOnlyMigrationDBContext : EncryptionDbContext
    {
        private TenantConnection _tenantReference;

        public ReadOnlyMigrationDBContext(TenantConnection tenantReference, IConfiguration configuration) : base(configuration)
        {
            _tenantReference = tenantReference;
        }
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseSqlServer(_tenantReference.ClientConnectionString);
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            //Configure default schema
            modelBuilder.HasDefaultSchema("Migration");
            modelBuilder.Entity<Upload>().ToTable("Uploads", "Migration");
            modelBuilder.Entity<MediaUpload>().ToTable("MediaUploads", "Migration");
            modelBuilder.Entity<Template>().ToTable("Templates", "Migration");
            modelBuilder.Entity<TemplateField>().ToTable("TemplateFields", "Migration");

            modelBuilder.Entity<AllergyImport>(entity =>
            {
                entity.HasKey(e => e.RecordId).HasName("PK__AllergyI__FBDF78C9023F772D");
                entity.ToTable("AllergyImport", "Migration");
            });

            modelBuilder.Entity<AppointmentTypesImport>(entity =>
            {
                entity.HasKey(e => e.RecordId).HasName("PK__Appointm__FBDF78C96B5535E0");
                entity.ToTable("AppointmentTypesImport", "Migration");
            });

            modelBuilder.Entity<AppointmentsImport>(entity =>
            {
                entity.HasKey(e => e.RecordId).HasName("PK__Appointm__FBDF78C98B0949F2");
                entity.ToTable("AppointmentsImport", "Migration");
            });

            modelBuilder.Entity<ConsultsImport>(entity =>
            {
                entity.HasKey(e => e.RecordId).HasName("PK__Consults__FBDF78C94721429E");
                entity.ToTable("ConsultsImport", "Migration");
            });

            modelBuilder.Entity<MeasurementImport>(entity =>
            {
                entity.HasKey(e => e.RecordId).HasName("PK__Measurem__FBDF78C9108F8D67");
                entity.ToTable("MeasurementImport", "Migration");
            });

            modelBuilder.Entity<PastHistoryImport>(entity =>
            {
                entity.HasKey(e => e.RecordId).HasName("PK__PastHist__FBDF78C9FBC017C9");
                entity.ToTable("PastHistoryImport", "Migration");
            });

            modelBuilder.Entity<PatientImport>(entity =>
            {
                entity.HasKey(e => e.RecordId).HasName("PK__PatientI__FBDF78C97517C67D");
                entity.ToTable("PatientImport", "Migration");
            });

            modelBuilder.Entity<PatientClinicalImport>(entity =>
            {
                entity.HasKey(e => e.RecordId).HasName("PK__PatientI__FBDF78C96553036F");
                entity.ToTable("PatientClinicalImport", "Migration");
            });

            modelBuilder.Entity<PrescriptionImport>(entity =>
            {
                entity.HasKey(e => e.RecordId).HasName("PK__Prescrip__FBDF78C960B22C6B");
                entity.ToTable("PrescriptionImport", "Migration");
            });

            modelBuilder.Entity<AccountHoldersImport>(entity =>
            {
                entity.HasKey(e => e.RecordId).HasName("PK__tmp_ms_x__FBDF78C9477A6670");
                entity.ToTable("AccountHoldersImport", "Migration");
            });

            modelBuilder.Entity<AddressBookImport>(entity =>
            {
                entity.HasKey(e => e.RecordId).HasName("PK__tmp_ms_x__FBDF78C9BFD3A939");
                entity.ToTable("AddressBookImport", "Migration");
            });

            modelBuilder.Entity<EmployersImport>(entity =>
            {
                entity.HasKey(e => e.RecordId).HasName("PK__tmp_ms_x__FBDF78C976A2885F");
                entity.ToTable("EmployersImport", "Migration");
            });


            modelBuilder.Entity<ReferralsImport>(entity =>
            {
                entity.HasKey(e => e.RecordId).HasName("PK__tmp_ms_x__FBDF78C9C0AB26F1");
                entity.ToTable("ReferralsImport", "Migration");
            });

            modelBuilder.Entity<UserDetail>().ToTable("UserDetails", "User");
            modelBuilder.Entity<UserCompanyAssoc>().ToTable("UserCompanyAssocs", "User");
            modelBuilder.Entity<FileDetails>().ToTable("FileDetails", "File");
            modelBuilder.Entity<FileRepository>().ToTable("FileRepository", "File");
            modelBuilder.Entity<Organisation>().ToTable("Organisation", "Company");


        }

        public virtual DbSet<UserDetail> UserDetails { get; set; }
        public virtual DbSet<CompanyDetail> CompanyDetails { get; set; }
        public virtual DbSet<UserCompanyAssoc> UserCompanyAssocs { get; set; }

        public virtual DbSet<FileDetails> FileDetails { get; set; }
        public virtual DbSet<FileRepository> FileRepository { get; set; }
        public virtual DbSet<Organisation> Organisation { get; set; }
        public virtual DbSet<Upload> Uploads { get; set; }
        public virtual DbSet<MediaUpload> MediaUploads { get; set; }
        public virtual DbSet<Template> Templates { get; set; }
        public virtual DbSet<TemplateField> TemplateFields { get; set; }
        public virtual DbSet<AllergyImport> AllergyImports { get; set; }
        public virtual DbSet<AppointmentTypesImport> AppointmentTypesImports { get; set; }
        public virtual DbSet<AppointmentsImport> AppointmentsImports { get; set; }
        public virtual DbSet<ConsultsImport> ConsultsImports { get; set; }
        public virtual DbSet<MeasurementImport> MeasurementImports { get; set; }
        public virtual DbSet<PastHistoryImport> PastHistoryImports { get; set; }
        public virtual DbSet<PatientImport> PatientImports { get; set; }
        public virtual DbSet<PatientClinicalImport> PatientClinicalImports { get; set; }
        public virtual DbSet<PrescriptionImport> PrescriptionImports { get; set; }
        public virtual DbSet<AccountHoldersImport> AccountHoldersImports { get; set; }
        public virtual DbSet<AddressBookImport> AddressBookImports { get; set; }
        public virtual DbSet<EmployersImport> EmployersImports { get; set; }
        public virtual DbSet<ReferralsImport> ReferralsImports { get; set; }

    }
}
