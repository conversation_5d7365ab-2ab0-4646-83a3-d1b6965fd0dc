﻿using Microsoft.EntityFrameworkCore;
using Capstone2.RestServices.Utility.Models;
using Capstone2.Shared.Models.Entities;
using Capstone2.Framework.RestApi.Context;
using MbsItemDetail = Capstone2.Shared.Models.Entities.MbsItemDetail;
using MbsItemCustomFeeAssoc = Capstone2.Shared.Models.Entities.MbsItemCustomFeeAssoc;
using Microsoft.Extensions.Configuration;
using RolesPermissionsAssoc = Capstone2.RestServices.Utility.Models.RolesPermissionsAssoc;

namespace Capstone2.RestServices.Utility.Context
{
    public class UpdatableUtilityDBContext : EncryptionDbContext
    {
        private TenantConnection _tenantReference;

        public UpdatableUtilityDBContext(TenantConnection tenantReference, IConfiguration configuration) : base(configuration)
        {
            _tenantReference = tenantReference;
        }
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseSqlServer(_tenantReference.ClientConnectionString);
        }
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            //Configure default schema
            modelBuilder.HasDefaultSchema("Utility");   
        }
        public virtual DbSet<SnippetDetail> SnippetDetails { get; set; }
        public virtual DbSet<SnippetUserAssoc> SnippetUserAssocs { get; set; }
        public virtual DbSet<LetterTemplate> LetterTemplates { get; set; }
        public virtual DbSet<LetterTemplateUserAssoc> LetterTemplateUserAssocs { get; set; }

        public virtual DbSet<Models.AppointmentTypes> AppointmentTypes { get; set; }
        public virtual DbSet<AppointmentTypesHospitalAssocs> AppointmentTypesHospitalAssocs { get; set; }
        public virtual DbSet<AppointmentTypesAnaesthetistAssocs> AppointmentTypesAnaesthetistAssocs { get; set; }
        public virtual DbSet<AppointmentTypesAssistantAssocs> AppointmentTypesAssistantAssocs { get; set; }

        public virtual DbSet<ConsultNoteTemplate> ConsultNoteTemplate { get; set; }
        public virtual DbSet<Category> Categories { get; set; }
        public virtual DbSet<ActivityUserAssoc> ActivityUserAssocs { get; set; }
        public virtual DbSet<Activity> Activities { get; set; }
        public virtual DbSet<Checklist> Checklists { get; set; }
        public virtual DbSet<ChecklistActivityAssoc> ChecklistActivityAssocs { get; set; }
        public virtual DbSet<RolesPermissionsAssoc> RolesPermissionsAssocs { get; set; }

        public virtual DbSet<EpisodeCategory> EpisodeCategories { get; set; }
        public virtual DbSet<EpisodeItemCustomFeeAssoc> EpisodeItemCustomFeeAssocs { get; set; }
        public virtual DbSet<EpisodeItemDetail> EpisodeItemDetails { get; set; }
        public virtual DbSet<MbsItemCustomFeeAssoc> MbsItemCustomFeeAssocs { get; set; }
        public virtual DbSet<MbsItemDetail> MbsItemDetails { get; set; }
        public virtual DbSet<MbsClinicalCategory> MbsClinicalCategories { get; set; }
        public virtual DbSet<ConsultNoteTemplateMedia> ConsultNoteTemplateMedia { get; set; }
        public virtual DbSet<Episode> Episodes { get; set; }
        public virtual DbSet<PrintTemplate> PrintTemplate { get; set; }
        public virtual DbSet<PrintTemplateControls> PrintTemplateControls { get; set; }

        public virtual DbSet<MedicalContractor> MedicalContractors { get; set; }
        public virtual DbSet<MedicalContractorBandAssoc> MedicalContractorBandAssocs { get; set; }
        public virtual DbSet<MedicalContractorAddress> MedicalContractorAddresses { get; set; }
        public virtual DbSet<MedContPersonalDetail> MedContPersonalDetails { get; set; }
        public virtual DbSet<MedicalContractorBandVersion> MedicalContractorBandVersions { get; set; }
    }
}
