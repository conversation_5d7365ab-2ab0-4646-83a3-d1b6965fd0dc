﻿using Capstone2.Framework.RestApi.Context;
using Capstone2.RestServices.CapstoneHub.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace Capstone2.RestServices.CapstoneHub.Context
{
    public class ReadOnlyCapstoneHubDBContext : EncryptionDbContext
    {
        private TenantConnection _tenantReference;

        public ReadOnlyCapstoneHubDBContext(TenantConnection tenantReference, IConfiguration configuration) : base(configuration)
        {
            _tenantReference = tenantReference;
        }
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseSqlServer(_tenantReference.ClientConnectionString);
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            //Configure default schema
            modelBuilder.HasDefaultSchema("CapstoneHub");           

        }
        public virtual DbSet<RequestLog> RequestLogs { get; set; }
        public virtual DbSet<TenantConfiguration> TenantConfigurations { get; set; }

    }
}
