﻿using Capstone2.Framework.RestApi.Context;
using Capstone2.RestServices.SSRSReport.Model;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.RestServices.SSRSReport.Context
{
    public class UpdatableSSRSReportDBContext : EncryptionDbContext
    {
        private TenantConnection _tenantReference;

        public UpdatableSSRSReportDBContext(TenantConnection tenantReference, IConfiguration configuration) : base(configuration)
        {
            _tenantReference = tenantReference;
        }
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseSqlServer(_tenantReference.ClientConnectionString);
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            //Configure default schema
            modelBuilder.HasDefaultSchema("SSRS");
        }

        public virtual DbSet<UserReportFilter> UserReportFilter { get; set; }

    }
}
