﻿using Capstone2.Framework.RestApi.Context;
using Capstone2.RestServices.SSRSReport.Model;
using Capstone2.Shared.Models.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace Capstone2.RestServices.SSRSReport.Context
{
    public class ReadOnlySSRSReportDBContext : EncryptionDbContext
    {
        private TenantConnection _tenantReference;

        public ReadOnlySSRSReportDBContext(TenantConnection tenantReference, IConfiguration configuration) : base(configuration)
        {
            _tenantReference = tenantReference;
        }
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseSqlServer(_tenantReference.ClientConnectionString);
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            //Configure default schema
            modelBuilder.HasDefaultSchema("SSRS");
            modelBuilder.Entity<UserDetail>().ToTable("UserDetails", "User");
        }

        public virtual DbSet<UserReportFilter> UserReportFilter { get; set; }
    }
}
