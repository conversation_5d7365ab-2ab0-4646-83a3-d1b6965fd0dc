﻿using Capstone2.Framework.RestApi.Context;
using Capstone2.RestServices.InvoicePayment.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace Capstone2.RestServices.InvoicePayment.Context
{
    public class UpdatableInvoicePaymentDBContext : EncryptionDbContext
    {
        private TenantConnection _tenantReference;

        public UpdatableInvoicePaymentDBContext(TenantConnection tenantReference, IConfiguration configuration) : base(configuration)
        {
            _tenantReference = tenantReference;
        }
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseSqlServer(_tenantReference.ClientConnectionString);
        }
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            //Configure default schema
            modelBuilder.HasDefaultSchema("InvoicePayment");

        }
        public virtual DbSet<PaymentDetail> PaymentDetails { get; set; }
        public virtual DbSet<PaymentInvoiceEpisodeItemsAssoc> PaymentInvoiceEpisodeItemsAssocs { get; set; }
        public virtual DbSet<PatientCredit> PatientCredit { get; set; }


    }
}
