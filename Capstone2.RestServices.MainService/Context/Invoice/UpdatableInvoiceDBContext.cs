﻿using Capstone2.Framework.RestApi.Context;
using Capstone2.RestServices.Invoice.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace Capstone2.RestServices.Invoice.Context
{
    public class UpdatableInvoiceDBContext : EncryptionDbContext
    {
        private TenantConnection _tenantReference;

        public UpdatableInvoiceDBContext(TenantConnection tenantReference, IConfiguration configuration) : base(configuration)
        {
            _tenantReference = tenantReference;
        }
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseSqlServer(_tenantReference.ClientConnectionString);
        }
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            //Configure default schema
            modelBuilder.HasDefaultSchema("Invoice");

        }
        public virtual DbSet<InvoiceDetail> InvoiceDetails { get; set; }
        public virtual DbSet<InvoiceEpisodeItemAssoc> InvoiceEpisodeItemAssocs { get; set; }
        public virtual DbSet<InvoiceSummary> InvoiceSummaries { get; set; }
        public virtual DbSet<InvoiceSummaryExpiryDetail> InvoiceSummaryExpiryDetails { get; set; }
        public virtual DbSet<InvoiceTemplate> InvoiceTemplates { get; set; }
        public virtual DbSet<InvoiceEpisodeItemsIndicatorAssoc> InvoiceEpisodeItemsIndicatorAssocs { get; set; }
        public virtual DbSet<InvoiceMedicareAssoc> InvoiceMedicareAssocs { get; set; }
        public virtual DbSet<InvoiceItemsMedicareAssoc> InvoiceItemsMedicareAssocs { get; set; }
        public virtual DbSet<InvoiceAdditionalDetail> InvoiceAdditionalDetails { get; set; }
        public virtual DbSet<InvoicePatientEftAssoc> InvoicePatientEftAssocs { get; set; }

    }
}
