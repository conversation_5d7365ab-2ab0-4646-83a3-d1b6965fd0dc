﻿using System;
using Microsoft.EntityFrameworkCore;
using Capstone2.RestServices.User.Models;
using Capstone2.Framework.RestApi.Context;
using Microsoft.Extensions.Configuration;

namespace Capstone2.RestServices.User.Context
{
    public class UpdatableUserDBContext : EncryptionDbContext
    {
        private TenantConnection _tenantReference;

        public UpdatableUserDBContext(TenantConnection tenantReference, IConfiguration configuration) : base(configuration)
        {
            _tenantReference = tenantReference;
        }
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseSqlServer(_tenantReference.ClientConnectionString);
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            //Configure default schema
            modelBuilder.HasDefaultSchema("User");

            //modelBuilder.Entity<IncomingLetterPatientAction>(entity =>
            //{
            //    entity.HasMany(s => s.IncomingLetterPatientActionsUserAssocs)
            //    .WithOne(s => s.IncomingLetterPatientAction)
            //    .HasForeignKey(s => s.IncomingLetterPatientActionsId);
            //});
        }


        public DbSet<UserDetail> UserDetails { get; set; }
        public DbSet<Address> Addresses { get; set; }
        public DbSet<UserCompanyAssoc> UserCompanyAssocs { get; set; }
        public DbSet<Schedule> Schedules { get; set; }
        public DbSet<ScheduleRecurrence> ScheduleRecurrences { get; set; }
        public DbSet<MyAction> MyActions { get; set; }

        public DbSet<Comment> Comments { get; set; }
        public DbSet<Preference> Preferences { get; set; }

        public DbSet<PatientAction> PatientActions { get; set; }
        public DbSet<QuickNote> QuickNote { get; set; }
        public DbSet<WhatsOnMessages> WhatsOnMessages { get; set; }
        public DbSet<PatientActionComment> PatientActionComments { get; set; }
        public DbSet<UserHealthFundAssoc> UserHealthFundAssocs { get; set; }
        public DbSet<IncomingLetter> IncomingLetter { get; set; }
        public DbSet<IncomingLetterActions> IncomingLetterActions { get; set; }
        public DbSet<IncomingLetterPatientAction> IncomingLetterPatientActions { get; set; }
        public DbSet<PatientActionUserAssoc> PatientActionUserAssocs { get; set; }
        public DbSet<MyActionUserAssoc> MyActionUserAssocs { get; set; }
        public DbSet<IncomingLetterPatientActionsUserAssocs> IncomingLetterPatientActionsUserAssocs { get; set; }
        public virtual DbSet<HIServiceDetail> HIServiceDetails { get; set; }
    }
}
