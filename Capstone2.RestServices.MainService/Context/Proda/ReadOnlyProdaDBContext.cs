﻿using Capstone2.Framework.RestApi.Context;
using Capstone2.Shared.Models.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace Capstone2.RestServices.Proda.Context
{
    public class ReadOnlyProdaDBContext : EncryptionDbContext
    {
        private TenantConnection _tenantReference;

        public ReadOnlyProdaDBContext(TenantConnection tenantReference, IConfiguration configuration) : base(configuration)
        {
            _tenantReference = tenantReference;
        }
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseSqlServer(_tenantReference.ClientConnectionString);
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            //Configure default schema
            modelBuilder.Entity<UserCompanyAssoc>().ToTable("UserCompanyAssocs", "User");

        }
        public virtual DbSet<Organisation> Organisation { get; set; }
        public virtual DbSet<UserCompanyAssoc> UserCompanyAssocs { get; set; }
    }
}
