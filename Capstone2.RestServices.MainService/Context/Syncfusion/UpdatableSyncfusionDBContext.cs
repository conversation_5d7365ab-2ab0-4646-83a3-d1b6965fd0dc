﻿using Microsoft.EntityFrameworkCore;
using Capstone2.Framework.RestApi.Context;
using Microsoft.Extensions.Configuration;

namespace Capstone2.RestServices.Syncfusion.Context
{
    public class UpdatableSyncfusionDBContext : EncryptionDbContext
    {
        private TenantConnection _tenantReference;

        public UpdatableSyncfusionDBContext(TenantConnection tenantReference, IConfiguration configuration) : base(configuration)
        {
            _tenantReference = tenantReference;
        }
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseSqlServer(_tenantReference.ClientConnectionString);
        }
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            //Configure default schema
        }
    }
}
