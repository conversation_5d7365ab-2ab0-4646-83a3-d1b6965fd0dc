﻿using Capstone2.Framework.RestApi.Context;
using Capstone2.RestServices.Medicare.Models;
using Capstone2.Shared.Models.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace Capstone2.RestServices.Medicare.Context
{
    public class ReadOnlyMedicareDBContext : EncryptionDbContext
    {
        private TenantConnection _tenantReference;

        public ReadOnlyMedicareDBContext(TenantConnection tenantReference, IConfiguration configuration) : base(configuration)
        {
            _tenantReference = tenantReference;
        }
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseSqlServer(_tenantReference.ClientConnectionString);
        }
                
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            //Configure default schema
            modelBuilder.Entity<RequestLogs>().ToTable("RequestLogs", "Medicare");

            modelBuilder.Entity<UserCompanyAssoc>().ToTable("UserCompanyAssocs", "User");
            modelBuilder.Entity<CompanyDetail>().ToTable("CompanyDetails", "Company");

        }
        public virtual DbSet<Organisation> Organisation { get; set; }
        public virtual DbSet<RequestLogs> RequestLogs { get; set; }
        public virtual DbSet<UserCompanyAssoc> UserCompanyAssocs { get; set; }
        public virtual DbSet<CompanyDetail> CompanyDetails { get; set; }
    }
}
