﻿using Microsoft.EntityFrameworkCore;
using Capstone2.RestServices.SyncService.Models;
using Capstone2.Shared.Models.Entities;
using Capstone2.Framework.RestApi.Context;
using Microsoft.Extensions.Configuration;

namespace Capstone2.RestServices.SyncService.Context
{
    public class UpdatableSyncServiceDBContext : EncryptionDbContext
    {
        private TenantConnection _tenantReference;

        public UpdatableSyncServiceDBContext(TenantConnection tenantReference, IConfiguration configuration) : base(configuration)
        {
            _tenantReference = tenantReference;
        }
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseSqlServer(_tenantReference.ClientConnectionString);
        }
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            //Configure default schema
            modelBuilder.HasDefaultSchema("Appointment");      


        }
     
        public virtual DbSet<Models.AppointmentTypes> AppointmentTypes { get; set; }
        public virtual DbSet<AppointmentDetails> AppointmentDetails { get; set; }
        public virtual DbSet<AppointmentHospitalAssocs> AppointmentHospitalAssocs { get; set; }
        public virtual DbSet<AppointmentAnaesthetistAssocs> AppointmentAnaesthetistAssocs { get; set; }
        public virtual DbSet<AppointmentAssistantAssocs> AppointmentAssistantAssocs { get; set; }


    }
}
