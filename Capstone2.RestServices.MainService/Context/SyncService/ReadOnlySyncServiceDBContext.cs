﻿using Capstone2.Framework.RestApi.Context;
using Capstone2.RestServices.SyncService.Models;
using Capstone2.Shared.Models.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace Capstone2.RestServices.SyncService.Context
{
    public class ReadOnlySyncServiceDBContext : EncryptionDbContext
    {
        private TenantConnection _tenantReference;

        public ReadOnlySyncServiceDBContext(TenantConnection tenantReference, IConfiguration configuration) : base(configuration)
        {
            _tenantReference = tenantReference;
        }
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseSqlServer(_tenantReference.ClientConnectionString);
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            //Configure default schema
            modelBuilder.HasDefaultSchema("Appointment");
            modelBuilder.Entity<UserDetail>().ToTable("UserDetails", "User");
            modelBuilder.Entity<CompanyDetail>().ToTable("CompanyDetails", "Company");
            modelBuilder.Entity<UserCompanyAssoc>().ToTable("UserCompanyAssocs", "User");
            modelBuilder.Entity<Models.AppointmentTypes>().ToTable("AppointmentTypes", "Utility");
            modelBuilder.Entity<PatientDetailInfo>().ToTable("PatientDetails", "Patient");
            modelBuilder.Entity<ChecklistDetails>().ToTable("Checklists", "Utility");
            modelBuilder.Entity<ActivityLogInfo>().ToTable("ActivityLogs", "Patient");


        }

        public virtual DbSet<UserDetail> UserDetails { get; set; }
        public virtual DbSet<CompanyDetail> CompanyDetails { get; set; }

        public virtual DbSet<UserCompanyAssoc> UserCompanyAssocs { get; set; }

        public virtual DbSet<ActivityLogInfo> ActivityLogs { get; set; }
        public virtual DbSet<Models.AppointmentTypes> AppointmentTypes { get; set; }
        public virtual DbSet<AppointmentDetails> AppointmentDetails { get; set; }
        public virtual DbSet<AppointmentHospitalAssocs> AppointmentHospitalAssocs { get; set; }
        public virtual DbSet<AppointmentAnaesthetistAssocs> AppointmentAnaesthetistAssocs { get; set; }
        public virtual DbSet<AppointmentAssistantAssocs> AppointmentAssistantAssocs { get; set; }

        public virtual DbSet<PatientDetailInfo> PatientDetails { get; set; }
        public virtual DbSet<ChecklistDetails> Checklists { get; set; }



    }
}
