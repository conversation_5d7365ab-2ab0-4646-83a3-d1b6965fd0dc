﻿using Capstone2.Framework.RestApi.Context;
using Capstone2.RestServices.Appointment.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace Capstone2.RestServices.Appointment.Context
{
    public class UpdatableAppointmentsDBContext : EncryptionDbContext
    {
        private TenantConnection _tenantReference;

        public UpdatableAppointmentsDBContext(TenantConnection tenantReference, IConfiguration configuration) : base(configuration)
        {
            _tenantReference = tenantReference;
        }
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseSqlServer(_tenantReference.ClientConnectionString);
        }
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            //Configure default schema
            modelBuilder.HasDefaultSchema("Appointment");
            modelBuilder.Entity<SmsHistory>().ToTable("SmsHistory", "Patient");


        }

        public virtual DbSet<Models.AppointmentTypes> AppointmentTypes { get; set; }
        public virtual DbSet<AppointmentDetails> AppointmentDetails { get; set; }
        public virtual DbSet<AppointmentHcpsummary> AppointmentHcpSummary { get; set; }
        public virtual DbSet<AppointmentHospitalAssocs> AppointmentHospitalAssocs { get; set; }
        public virtual DbSet<AppointmentAnaesthetistAssocs> AppointmentAnaesthetistAssocs { get; set; }
        public virtual DbSet<AppointmentAssistantAssocs> AppointmentAssistantAssocs { get; set; }
        public virtual DbSet<AppointmentChecklistActivityAssoc> AppointmentChecklistActivityAssocs { get; set; }
        public virtual DbSet<AppointmentSMSReminders> AppointmentSMSReminders { get; set; }
        public virtual DbSet<AppointmentEpisodeItemAssoc> AppointmentEpisodeItemAssocs { get; set; }

        public virtual DbSet<SmsHistory> SmsHistory { get; set; }


    }
}
