﻿using Capstone2.Framework.RestApi.Context;
using Capstone2.RestServices.Appointment.Models;
using Capstone2.Shared.Models.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace Capstone2.RestServices.Appointment.Context
{
    public class ReadOnlyAppointmentsDBContext : EncryptionDbContext
    {
        private TenantConnection _tenantReference;

        public ReadOnlyAppointmentsDBContext(TenantConnection tenantReference, IConfiguration configuration) : base(configuration)
        {
            _tenantReference = tenantReference;
        }
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseSqlServer(_tenantReference.ClientConnectionString);
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            //Configure default schema
            modelBuilder.HasDefaultSchema("Appointment");
            modelBuilder.Entity<UserDetail>().ToTable("UserDetails", "User");
            modelBuilder.Entity<CompanyDetail>().ToTable("CompanyDetails", "Company");
            modelBuilder.Entity<UserCompanyAssoc>().ToTable("UserCompanyAssocs", "User");
            modelBuilder.Entity<Models.AppointmentTypes>().ToTable("AppointmentTypes", "Utility");
            modelBuilder.Entity<PatientDetailInfo>().ToTable("PatientDetails", "Patient");
            modelBuilder.Entity<AccountHolderInfo>().ToTable("AccountHolder", "Patient");
            modelBuilder.Entity<AccountHolderHealthfundAssoc>().ToTable("AccountHolderHealthfundAssocs", "Patient");
            modelBuilder.Entity<ChecklistDetails>().ToTable("Checklists", "Utility");
            modelBuilder.Entity<ActivityLogInfo>().ToTable("ActivityLogs", "Patient");
            modelBuilder.Entity<ActivityDetails>().ToTable("Activities", "Utility");
            modelBuilder.Entity<LetterTemplates>().ToTable("LetterTemplates", "Utility");
            modelBuilder.Entity<AppointmentTypesSMSAssocs>().ToTable("AppointmentTypesSMSAssocs", "Utility");
            //modelBuilder.Entity<ChecklistActivityAssocDetails>().ToTable("ChecklistActivityAssocs", "Utility");
            modelBuilder.Entity<InputChecklistActivityAssocDetails>().ToTable("ChecklistActivityAssocs", "Utility");
            modelBuilder.Entity<SmsRequest>().ToTable("SmsRequests", "Communication");
            modelBuilder.Entity<SmsHistory>().ToTable("SmsHistory", "Patient");

        }

        public virtual DbSet<UserDetail> UserDetails { get; set; }
        public virtual DbSet<CompanyDetail> CompanyDetails { get; set; }

        public virtual DbSet<UserCompanyAssoc> UserCompanyAssocs { get; set; }

        public virtual DbSet<ActivityLogInfo> ActivityLogs { get; set; }
        public virtual DbSet<Models.AppointmentTypes> AppointmentTypes { get; set; }
        public virtual DbSet<AppointmentDetails> AppointmentDetails { get; set; }
        public virtual DbSet<AppointmentHcpsummary> AppointmentHcpSummary { get; set; }
        public virtual DbSet<AppointmentHospitalAssocs> AppointmentHospitalAssocs { get; set; }
        public virtual DbSet<AppointmentAnaesthetistAssocs> AppointmentAnaesthetistAssocs { get; set; }
        public virtual DbSet<AppointmentAssistantAssocs> AppointmentAssistantAssocs { get; set; }

        public virtual DbSet<PatientDetailInfo> PatientDetails { get; set; }
        public virtual DbSet<AccountHolderInfo> AccountHolder { get; set; }
        public virtual DbSet<AccountHolderHealthfundAssoc> AccountHolderHealthfundAssoc { get; set; }
        public virtual DbSet<ChecklistDetails> Checklists { get; set; }

        public virtual DbSet<AppointmentChecklistActivityAssoc> AppointmentChecklistActivityAssocs { get; set; }

        public virtual DbSet<InputChecklistActivityAssocDetails> InputChecklistActivityAssocs { get; set; }
        //public virtual DbSet<ChecklistActivityAssocDetails> ChecklistActivityAssocs { get; set; }
        public virtual DbSet<ActivityDetails> Activities { get; set; }
        public virtual DbSet<AppointmentSMSReminders> AppointmentSMSReminders { get; set; }

        public virtual DbSet<SmsRequest> SmsRequests { get; set; }

        public virtual DbSet<AppointmentTypesSMSAssocs> AppointmentTypesSMSAssocs { get; set; }

        public virtual DbSet<LetterTemplates> LetterTemplates { get; set; }

        public virtual DbSet<AppointmentEpisodeItemAssoc> AppointmentEpisodeItemAssocs { get; set; }

        public virtual DbSet<SmsHistory> SmsHistory { get; set; }


    }
}
