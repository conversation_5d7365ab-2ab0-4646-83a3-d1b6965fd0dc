﻿using Microsoft.EntityFrameworkCore;
using Capstone2.Framework.RestApi.Context;
using Microsoft.Extensions.Configuration;
using Capstone2.RestServices.Report.Models;

namespace Capstone2.RestServices.Report.Context
{
    public class UpdatableReportDBContext : EncryptionDbContext
    {
        private TenantConnection _tenantReference;

        public UpdatableReportDBContext(TenantConnection tenantReference, IConfiguration configuration) : base(configuration)
        {
            _tenantReference = tenantReference;
        }
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseSqlServer(_tenantReference.ClientConnectionString);
        }
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            //Configure default schema
            modelBuilder.HasDefaultSchema("Report");
        }

        public virtual DbSet<MedicareException> MedicareExceptions { get; set; }
        public virtual DbSet<ImcprocessingReport> ImcprocessingReports { get; set; }
        public virtual DbSet<InvoiceMedicareAssocsProcessingImc> InvoiceMedicareAssocsProcessingImcs { get; set; }
        public virtual DbSet<EODReportNote> EODReportNotes { get; set; }
    }
}
