﻿using Capstone2.Framework.RestApi.Context;
using Capstone2.RestServices.Report.Models;
using Capstone2.Shared.Models.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace Capstone2.RestServices.Report.Context
{
    public class ReadOnlyReportDBContext : EncryptionDbContext
    {
        private TenantConnection _tenantReference;

        public ReadOnlyReportDBContext(TenantConnection tenantReference, IConfiguration configuration) : base(configuration)
        {
            _tenantReference = tenantReference;
        }
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseSqlServer(_tenantReference.ClientConnectionString);
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            //Configure default schema
            modelBuilder.HasDefaultSchema("Report");
            modelBuilder.Entity<RequestLogs>().ToTable("RequestLogs", "Medicare");
            modelBuilder.Entity<RolesPermissionsAssoc>().ToTable("RolesPermissionsAssocs", "Utility");
        }
        public virtual DbSet<Eodreport> EODReport { get; set; }
        public virtual DbSet<ProviderIncomeReport> ProviderIncomeReport { get; set; }
        public virtual DbSet<MedicareException> MedicareExceptions { get; set; }
        public virtual DbSet<ProcessingReport> ProcessingReport { get; set; }
        public virtual DbSet<InvoiceMedicareAssocsProcessing> InvoiceMedicareAssocsProcessing { get; set; }
        public virtual DbSet<RequestLogs> RequestLogs { get; set; }
        public virtual DbSet<RolesPermissionsAssoc> RolesPermissionsAssocs { get; set; }
        public virtual DbSet<ImcprocessingReport> ImcprocessingReport { get; set; }
        public virtual DbSet<InvoiceMedicareAssocsProcessingImc> InvoiceMedicareAssocsProcessingImc { get; set; }
        public virtual DbSet<RevenueReport> RevenueReport { get; set; }
        public virtual DbSet<InvoiceEpisodeItemsReport> InvoiceEpisodeItemsReport { get; set; }
        public virtual DbSet<EODReportNote> EODReportNotes { get; set; }

    }
}
