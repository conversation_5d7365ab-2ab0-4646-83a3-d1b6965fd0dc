﻿using Capstone2.Framework.RestApi.Context;
using Capstone2.RestServices.HIService.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace Capstone2.RestServices.HIService.Context
{
    public class UpdatableHIServiceDBContext : EncryptionDbContext
    {
        private TenantConnection _tenantReference;

        public UpdatableHIServiceDBContext(TenantConnection tenantReference, IConfiguration configuration) : base(configuration)
        {
            _tenantReference = tenantReference;
        }
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseSqlServer(_tenantReference.ClientConnectionString);
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            //Configure default schema
            modelBuilder.Entity<RequestLogs>().ToTable("RequestLogs", "HIService");
        }
        public virtual DbSet<RequestLogs> RequestLogs { get; set; }

    }
}
