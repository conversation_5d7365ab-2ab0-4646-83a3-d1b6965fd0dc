﻿using Capstone2.Framework.RestApi.Context;
using Capstone2.RestServices.HIService.Models;
using Capstone2.Shared.Models.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace Capstone2.RestServices.HIService.Context
{
    public class ReadOnlyHIServiceDBContext : EncryptionDbContext
    {
        private TenantConnection _tenantReference;

        public ReadOnlyHIServiceDBContext(TenantConnection tenantReference, IConfiguration configuration) : base(configuration)
        {
            _tenantReference = tenantReference;
        }
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseSqlServer(_tenantReference.ClientConnectionString);
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            //Configure default schema
            modelBuilder.Entity<RequestLogs>().ToTable("RequestLogs", "HIService");

            modelBuilder.Entity<Organisation>().ToTable("Organisation", "Company");            
        }

        public virtual DbSet<RequestLogs> RequestLogs { get; set; }

        public virtual DbSet<Organisation> Organisation { get; set; }       
    }
}
