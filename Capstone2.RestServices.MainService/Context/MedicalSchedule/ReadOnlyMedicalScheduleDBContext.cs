﻿using Capstone2.Framework.RestApi.Context;
using Capstone2.RestServices.MedicalSchedule.Models;
using Capstone2.Shared.Models.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace Capstone2.RestServices.MedicalSchedule.Context
{
    public class ReadOnlyMedicalScheduleDBContext : EncryptionDbContext
    {
        private TenantConnection _tenantReference;

        public ReadOnlyMedicalScheduleDBContext(TenantConnection tenantReference, IConfiguration configuration) : base(configuration)
        {
            _tenantReference = tenantReference;
        }
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseSqlServer(_tenantReference.ClientConnectionString);
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            //Configure default schema
            modelBuilder.HasDefaultSchema("MedicalSchedule");
            modelBuilder.Entity<UserDetail>().ToTable("UserDetails", "User");
            modelBuilder.Entity<UserCompanyAssoc>().ToTable("UserCompanyAssocs", "User");
            modelBuilder.Entity<FileDetails>().ToTable("FileDetails", "File");
            modelBuilder.Entity<FileRepository>().ToTable("FileRepository", "File");
            modelBuilder.Entity<Organisation>().ToTable("Organisation", "Company");


        }

        public virtual DbSet<UserDetail> UserDetails { get; set; }
        public virtual DbSet<CompanyDetail> CompanyDetails { get; set; }
        public virtual DbSet<UserCompanyAssoc> UserCompanyAssocs { get; set; }

        public virtual DbSet<FileDetails> FileDetails { get; set; }
        public virtual DbSet<FileRepository> FileRepository { get; set; }
        public virtual DbSet<Organisation> Organisation { get; set; }



        public virtual DbSet<Upload> Uploads { get; set; }
        public virtual DbSet<HFUpload> HFUploads { get; set; }

    }
}
