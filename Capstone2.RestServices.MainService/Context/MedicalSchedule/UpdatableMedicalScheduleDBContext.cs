﻿using Microsoft.EntityFrameworkCore;
using Capstone2.RestServices.MedicalSchedule.Models;
using Capstone2.Shared.Models.Entities;
using Capstone2.Framework.RestApi.Context;
using Microsoft.Extensions.Configuration;

namespace Capstone2.RestServices.MedicalSchedule.Context
{
    public class UpdatableMedicalScheduleDBContext : EncryptionDbContext
    {
        private TenantConnection _tenantReference;

        public UpdatableMedicalScheduleDBContext(TenantConnection tenantReference, IConfiguration configuration) : base(configuration)
        {
            _tenantReference = tenantReference;
        }
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseSqlServer(_tenantReference.ClientConnectionString);
        }
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            //Configure default schema
            modelBuilder.HasDefaultSchema("MedicalSchedule");   
        }
        public virtual DbSet<Upload> Uploads { get; set; }
        public virtual DbSet<HFUpload> HFUploads { get; set; }

    }
}
