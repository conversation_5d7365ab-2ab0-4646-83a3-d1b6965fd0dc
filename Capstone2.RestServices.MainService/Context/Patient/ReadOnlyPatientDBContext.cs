﻿using Capstone2.Framework.RestApi.Context;
using Capstone2.RestServices.Patient.Models;
using Capstone2.Shared.Models.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace Capstone2.RestServices.Patient.Context
{
    public class ReadOnlyPatientDBContext : EncryptionDbContext
    {
        private TenantConnection _tenantReference;

        public ReadOnlyPatientDBContext(TenantConnection tenantReference, IConfiguration configuration) : base(configuration)
        {
            _tenantReference = tenantReference;
        }
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseSqlServer(_tenantReference.ClientConnectionString);
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            //Configure default schema
            modelBuilder.HasDefaultSchema("Patient");
            modelBuilder.Entity<UserCompanyAssoc>().ToTable("UserCompanyAssocs", "User");
            modelBuilder.Entity<UserDetail>().ToTable("UserDetails", "User");
            modelBuilder.Entity<UserAddress>().ToTable("Addresses", "User");
            modelBuilder.Entity<Authentication.Models.User>().ToTable("Users", "Authentication");
            modelBuilder.Entity<CompanyDetail>().ToTable("CompanyDetails", "Company");
            modelBuilder.Entity<Organisation>().ToTable("Organisation", "Company");
            modelBuilder.Entity<CompanyAddress>().ToTable("Addresses", "Company");
            modelBuilder.Entity<FileDetails>().ToTable("FileDetails", "File");
            modelBuilder.Entity<LetterTemplate>().ToTable("LetterTemplates", "Utility");
            modelBuilder.Entity<ConsultNoteTemplate>().ToTable("ConsultNoteTemplate", "Utility");
            modelBuilder.Entity<ConsultNoteTemplateControls>().ToTable("ConsultNoteTemplateControls", "Utility");
            modelBuilder.Entity<FileRepository>().ToTable("FileRepository", "File");
            modelBuilder.Entity<AppointmentDetailInfo>().ToTable("AppointmentDetails", "Appointment");
            modelBuilder.Entity<AppointmentTypes>().ToTable("AppointmentTypes", "Utility");
            modelBuilder.Entity<RolesPermissionsAssoc>().ToTable("RolesPermissionsAssocs", "Utility");

            modelBuilder.Entity<ReferralSourceModel>().ToTable("ReferralSource", "Patient");
            modelBuilder.Entity<ReferralSourceDetails>().ToTable("ReferralSource_SubType", "Patient");
            modelBuilder.Entity<PatientDraftData>().ToTable("PatientDraftData", "PatientRegistration");
            modelBuilder.Entity<SmsRequest>().ToTable("SmsRequests", "Communication");

        }

        public virtual DbSet<Address> Addresses { get; set; }
        public virtual DbSet<CompanyAddress> CompanyAddresses { get; set; }
        public virtual DbSet<UserAddress> UserAddresses { get; set; }
        public virtual DbSet<PatientDetail> PatientDetails { get; set; }
        public virtual DbSet<CommunicationNote> CommunicationNotes { get; set; }
        public virtual DbSet<ReferralDetail> ReferralDetails { get; set; }
        public virtual DbSet<HealthCareTeam> HealthCareTeams { get; set; }
        public virtual DbSet<HealthDetails> HealthDetails { get; set; }
        public virtual DbSet<UserDetail> UserDetails { get; set; }
        public virtual DbSet<UserCompanyAssoc> UserCompanyAssocs { get; set; }
        public virtual DbSet<CompanyDetail> CompanyDetails { get; set; }
        public virtual DbSet<Authentication.Models.User> Users { get; set; }
        public virtual DbSet<FileDetails> FileDetails { get; set; }
        public virtual DbSet<PatientMediaAssoc> PatientMediaAssocs { get; set; }
        public virtual DbSet<PatientMediaTagsAssoc> PatientMediaTagsAssocs { get; set; }
        public virtual DbSet<Tag> Tags { get; set; }
        public virtual DbSet<LetterTemplate> LetterTemplates { get; set; }
        public virtual DbSet<Letter> Letters { get; set; }
        public virtual DbSet<LetterActions> LetterActions { get; set; }
        public virtual DbSet<ConsultNoteTemplate> ConsultNoteTemplate { get; set; }
        public virtual DbSet<ConsultNoteTemplateControls> ConsultNoteTemplateControls { get; set; }
        public virtual DbSet<ConsultNotes> ConsultNotes { get; set; }
        public virtual DbSet<ConsultNotesControls> ConsultNotesControls { get; set; }
        public virtual DbSet<ConsultNoteMediaAssoc> ConsultNoteMediaAssocs { get; set; }
        public virtual DbSet<Organisation> Organisation { get; set; }
        public virtual DbSet<Models.ActivityLog> ActivityLogs { get; set; }
        public virtual DbSet<Models.ActivityLogChildEntry> ActivityLogChildEntries { get; set; }
        public virtual DbSet<AppointmentDetailInfo> AppointmentDetails { get; set; }
        public virtual DbSet<AppointmentTypes> AppointmentTypes { get; set; }
        public virtual DbSet<AccountHolder> AccountHolder { get; set; }
        public virtual DbSet<AccountHolderAssocs> AccountHolderAssocs { get; set; }
        public virtual DbSet<AccountHolderAddresses> AccountHolderAddresses { get; set; }
        public virtual DbSet<AccountHolderHealthfundAssocs> AccountHolderHealthfundAssocs { get; set; }
        public virtual DbSet<AccountHolderParentGuardianAssocs> AccountHolderParentGuardianAssocs { get; set; }
        public virtual DbSet<AccountHolderWorkersCompAssocs> AccountHolderWorkersCompAssocs { get; set; }
        public virtual DbSet<MedicalProcedures> MedicalProcedures { get; set; }
        public virtual DbSet<MedicalHistory> MedicalHistory { get; set; }
        public virtual DbSet<PathologyRequest> PathologyRequest { get; set; }
        public virtual DbSet<PathologyRequestDoctorsAssocs> PathologyRequestDoctorsAssocs { get; set; }
        public virtual DbSet<PathologyRequestMediaAssocs> PathologyRequestMediaAssocs { get; set; }
        public virtual DbSet<PathologyResponseReview> PathologyResponseReview { get; set; }
        public DbSet<RolesPermissionsAssoc> RolesPermissionsAssocs { get; set; }
        public virtual DbSet<Email> Emails { get; set; }
        public virtual DbSet<LetterEmailCcAssoc> LetterEmailCcAssocs { get; set; }
        public virtual DbSet<HIServiceDetail> HIServiceDetails { get; set; }
        public virtual DbSet<ReferralSourceModel> ReferralSource { get; set; }
        public virtual DbSet<ReferralSourceDetails> ReferralSource_SubType { get; set; }
        public virtual DbSet<ReferralDetailsPatient> ReferralSource_Patient { get; set; }
        public virtual DbSet<EHealthMessage> EHealthMessages { get; set; }

        public virtual DbSet<PatientDraftData> PatientDraftData { get; set; }

        public virtual DbSet<SmsHistory> SmsHistory { get; set; }

        public virtual DbSet<SmsRequest> SmsRequests { get; set; }



    }
}
