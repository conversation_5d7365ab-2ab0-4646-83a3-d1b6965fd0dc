﻿using Capstone2.Framework.RestApi.Context;
using Capstone2.RestServices.Patient.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace Capstone2.RestServices.Patient.Context
{
    public class UpdatablePatientDBContext : EncryptionDbContext
    {
        private TenantConnection _tenantReference;

        public UpdatablePatientDBContext(TenantConnection tenantReference, IConfiguration configuration) : base(configuration)
        {
            _tenantReference = tenantReference;
        }
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseSqlServer(_tenantReference.ClientConnectionString);
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            //Configure default schema
            modelBuilder.HasDefaultSchema("Patient");    
        }

        public virtual DbSet<Address> Addresses { get; set; }
        public virtual DbSet<PatientDetail> PatientDetails { get; set; }
        public virtual DbSet<CommunicationNote> CommunicationNotes { get; set; }
        public virtual DbSet<ReferralDetail> ReferralDetails { get; set; }
        public virtual DbSet<HealthCareTeam> HealthCareTeams { get; set; }
        public virtual DbSet<HealthDetails> HealthDetails { get; set; }
        public virtual DbSet<PatientMediaAssoc> PatientMediaAssocs { get; set; }
        public virtual DbSet<PatientMediaTagsAssoc> PatientMediaTagsAssocs { get; set; }
        public virtual DbSet<Tag> Tags { get; set; }
        public virtual DbSet<Letter> Letters { get; set; }
        public virtual DbSet<LetterActions> LetterActions { get; set; }
        public virtual DbSet<ConsultNotes> ConsultNotes { get; set; }
        public virtual DbSet<ConsultNotesControls> ConsultNotesControls { get; set; }
        public virtual DbSet<ConsultNoteMediaAssoc> ConsultNoteMediaAssocs { get; set; }
        public virtual DbSet<ActivityLog> ActivityLogs { get; set; }
        public virtual DbSet<ActivityLogChildEntry> ActivityLogChildEntries { get; set; }
        public virtual DbSet<AccountHolder> AccountHolder { get; set; }
        public virtual DbSet<AccountHolderAssocs> AccountHolderAssocs { get; set; }
        public virtual DbSet<AccountHolderHealthfundAssocs> AccountHolderHealthfundAssocs { get; set; }
        public virtual DbSet<AccountHolderParentGuardianAssocs> AccountHolderParentGuardianAssocs { get; set; }
        public virtual DbSet<AccountHolderWorkersCompAssocs> AccountHolderWorkersCompAssocs { get; set; }
        public virtual DbSet<MedicalProcedures> MedicalProcedures { get; set; }
        public virtual DbSet<MedicalHistory> MedicalHistory { get; set; }
        public virtual DbSet<PathologyRequest> PathologyRequest { get; set; }
        public virtual DbSet<PathologyRequestDoctorsAssocs> PathologyRequestDoctorsAssocs { get; set; }
        public virtual DbSet<PathologyRequestMediaAssocs> PathologyRequestMediaAssocs { get; set; }
        public virtual DbSet<PathologyResponseReview> PathologyResponseReview { get; set; }
        public virtual DbSet<Email> Emails { get; set; }
        public virtual DbSet<LetterEmailCcAssoc> LetterEmailCcAssocs { get; set; }
        public virtual DbSet<HIServiceDetail> HIServiceDetails { get; set; }
        public virtual DbSet<ReferralDetailsPatient> ReferralSource_Patient { get; set; }
        public virtual DbSet<EHealthMessage> EHealthMessages { get; set; }

        public virtual DbSet<SmsHistory> SmsHistory { get; set; }

    }
}
