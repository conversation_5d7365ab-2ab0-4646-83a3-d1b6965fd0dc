﻿using System;
using Capstone2.Framework.RestApi.Context;
using Capstone2.RestServices.Communication.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace Capstone2.RestServices.Communication.Context
{
    public class UpdatableCommunicationDBContext : EncryptionDbContext
    {
        private TenantConnection _tenantReference;

        public UpdatableCommunicationDBContext(TenantConnection tenantReference, IConfiguration configuration) : base(configuration)
        {
            _tenantReference = tenantReference;
        }
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseSqlServer(_tenantReference.ClientConnectionString);
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            //Configure default schema
            modelBuilder.HasDefaultSchema("Communication");
        }
        public virtual DbSet<SmsRequest> SmsRequests { get; set; }
        public virtual DbSet<EmailRequest> EmailRequests { get; set; }
        public virtual DbSet<EmailTemplate> EmailTemplates { get; set; }


    }
}
