﻿using Capstone2.Framework.RestApi.Context;
using Capstone2.RestServices.Communication.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace Capstone2.RestServices.Communication.Context
{
    public class ReadOnlyCommunicationDBContext : EncryptionDbContext
    {
        private TenantConnection _tenantReference;

        public ReadOnlyCommunicationDBContext(TenantConnection tenantReference, IConfiguration configuration) : base(configuration)
        {
            _tenantReference = tenantReference;
        }
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseSqlServer(_tenantReference.ClientConnectionString);
        }
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            //Configure default schema
            modelBuilder.HasDefaultSchema("Communication");
            modelBuilder.Entity<SmsRequest>().ToTable("SmsRequests", "Communication");
            modelBuilder.Entity<EmailTemplate>().ToTable("EmailTemplates", "Communication");
            modelBuilder.Entity<Organisation>().ToTable("Organisation", "Company");
        }

        public virtual DbSet<SmsRequest> SmsRequests { get; set; }
        public virtual DbSet<EmailTemplate> EmailTemplates { get; set; }

        public virtual DbSet<Organisation> Organisation { get; set; }


    }
}
