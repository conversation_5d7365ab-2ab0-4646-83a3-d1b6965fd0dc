﻿using Capstone2.Framework.RestApi.Context;
using Capstone2.RestServices.Hcp.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace Capstone2.RestServices.Hcp.Context
{
    public class UpdatableHcpDBContext : EncryptionDbContext
    {
        private TenantConnection _tenantReference;

        public UpdatableHcpDBContext(TenantConnection tenantReference, IConfiguration configuration) : base(configuration)
        {
            _tenantReference = tenantReference;
        }
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseSqlServer(_tenantReference.ClientConnectionString);
        }


        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.HasDefaultSchema("Hcp");
        }

        public virtual DbSet<Hcpsummary> HcpSummary { get; set; }
        public virtual DbSet<HcpcertificateAssoc> HcpcertificateAssocs { get; set; }
        public virtual DbSet<HcpdiagnosesAssoc> HcpdiagnosesAssocs { get; set; }
        public virtual DbSet<HcphospitalAccommodationAssoc> HcphospitalAccommodationAssocs { get; set; }
        public virtual DbSet<HcpotherDetail> HcpotherDetails { get; set; }
        public virtual DbSet<HcpotherServicesAssoc> HcpotherServicesAssocs { get; set; }
        public virtual DbSet<HcpproceduresAssoc> HcpproceduresAssocs { get; set; }
        public virtual DbSet<HcpsameDayAccommodationDetail> HcpsameDayAccommodationDetails { get; set; }
        public virtual DbSet<HcptheatreAndMbsdetailsAssoc> HcptheatreAndMbsdetailsAssocs { get; set; }
        public virtual DbSet<HcpTheatreTimeAssoc> HcpTheatreTimeAssocs { get; set; }
        public virtual DbSet<HcpdiagnosesDetail> HcpDiagnosesDetails { get; set; }
        public virtual DbSet<HcpdocumentAssoc> HcpdocumentAssocs { get; set; }
        public virtual DbSet<HcpSummaryAssoc> HcpSummaryAssocs { get; set; }
        public virtual DbSet<HcpClaim> HcpClaim { get; set; }
        public virtual DbSet<HcpcertificateDetail> HcpcertificateDetails { get; set; }

    }
}
