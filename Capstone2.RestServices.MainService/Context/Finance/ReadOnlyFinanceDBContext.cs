﻿using Capstone2.Framework.RestApi.Context;
using Capstone2.Shared.Models.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace Capstone2.RestServices.Finance.Context
{
    public class ReadOnlyFinanceDBContext : EncryptionDbContext
    {
        private TenantConnection _tenantReference;

        public ReadOnlyFinanceDBContext(TenantConnection tenantReference, IConfiguration configuration) : base(configuration)
        {
            _tenantReference = tenantReference;
        }
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseSqlServer(_tenantReference.ClientConnectionString);
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            //Configure default schema
            modelBuilder.HasDefaultSchema("Finance");
            modelBuilder.Entity<Organisation>().ToTable("Organisation", "Company");

        }
        public virtual DbSet<Organisation> Organisation { get; set; }


    }
}
