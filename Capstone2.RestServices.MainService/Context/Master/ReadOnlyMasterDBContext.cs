﻿using Microsoft.EntityFrameworkCore;
using Capstone2.RestServices.Master.Models;
using Capstone2.Framework.RestApi.Context;
using Microsoft.Extensions.Configuration;

namespace Capstone2.RestServices.Master.Context
{
    public class ReadOnlyMasterDBContext : EncryptionDbContext
    {
        //private TenantConnection _tenantReference;

        //public ReadOnlyDBContext(TenantConnection tenantReference)
        //{
        //    _tenantReference = tenantReference;
        //}
        //protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        //{
        //    optionsBuilder.UseSqlServer(_tenantReference.ClientConnectionString);
        //}
        public ReadOnlyMasterDBContext(DbContextOptions<ReadOnlyMasterDBContext> options, IConfiguration configuration) : base(options, configuration)
        {
            //options.use

        }

        public virtual DbSet<SystemConstant> SystemConstant { get; set; }
        public virtual DbSet<CountryMaster> CountryMaster { get; set; }
        public virtual DbSet<StateMaster> StateMaster { get; set; }
        public virtual DbSet<NationalityMaster> NationalityMaster { get; set; }
        public virtual DbSet<LanguageMaster> LanguageMaster { get; set; }
        public virtual DbSet<DataFields> DataFields { get; set; }
        public virtual DbSet<RolesCategory> RolesCategories { get; set; }
        public virtual DbSet<RolesPermission> RolesPermissions { get; set; }
        public virtual DbSet<MedicareParticipants> ParticipantDetails { get; set; }
        public virtual DbSet<MedicareConstant> MedicareConstants { get; set; }
        public virtual DbSet<MedicareErrors> MedicareErrors { get; set; }
        public virtual DbSet<TimeZoneMaster> TimeZoneMaster { get; set; }
        public virtual DbSet<ApiPermissionsAssoc> ApiPermissionsAssocs { get; set; }
        public virtual DbSet<PresentingIllnessCode> PresentingIllnessCodes { get; set; }
        public virtual DbSet<MedicalContractorBand> MedicalContractorBand { get; set; }
        public virtual DbSet<HealthFundGroup> HealthFundGroups { get; set; }
        public virtual DbSet<GroupToHealthFundAssoc> GroupToHealthFundAssocs { get; set; }
        public virtual DbSet<HealthFundData> HealthFundData { get; set; }
        public virtual DbSet<HFDataParticipantIdAssoc> HFDataParticipantIdAssocs { get; set; }


        public virtual DbSet<Shared.Models.Entities.MbsData> MbsData { get; set; }
        public virtual DbSet<Shared.Models.Entities.ClinicalCategory> ClinicalCategories { get; set; }
        public virtual DbSet<Shared.Models.Entities.DvaData> DvaData { get; set; }
        public virtual DbSet<Shared.Models.Entities.MbsClinicalCategoryData> MbsClinicalCategoryData { get; set; }
        public virtual DbSet<Shared.Models.Entities.TheatreBandingData> TheatreBandingData { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            //Configure default schema
            modelBuilder.HasDefaultSchema("Common");
            modelBuilder.Entity<MedicareParticipants>().ToTable("ParticipantDetails", "Medicare");
            modelBuilder.Entity<MedicareErrors>().ToTable("Errors", "Medicare");
            modelBuilder.Entity<PresentingIllnessCode>().ToTable("PresentingIllnessCodes", "Medicare");
            modelBuilder.Entity<MedicalContractorBand>().ToTable("MedicalContractorBand", "Medicare");
            modelBuilder.Entity<HealthFundGroup>().ToTable("HealthFundGroups", "Medicare");
            modelBuilder.Entity<GroupToHealthFundAssoc>().ToTable("GroupToHealthFundAssocs", "Medicare");
            modelBuilder.Entity<HealthFundData>().ToTable("HealthFundData", "MedicalSchedule");
            modelBuilder.Entity<HFDataParticipantIdAssoc>().ToTable("HFDataParticipantIdAssocs", "MedicalSchedule");

            // Temporarily added

            modelBuilder.Entity<Shared.Models.Entities.DvaData>().ToTable("DvaData", "MedicalSchedule");
            modelBuilder.Entity<Shared.Models.Entities.MbsData>().ToTable("MbsData", "MedicalSchedule");
            modelBuilder.Entity<Shared.Models.Entities.ClinicalCategory>().ToTable("ClinicalCategories", "MedicalSchedule");
            modelBuilder.Entity<Shared.Models.Entities.MbsClinicalCategoryData>().ToTable("MbsClinicalCategoryData", "MedicalSchedule");
            modelBuilder.Entity<Shared.Models.Entities.TheatreBandingData>().ToTable("TheatreBandingData", "MedicalSchedule");
        }

    }
}
