﻿using System;
using Capstone2.Framework.RestApi.Context;
using Capstone2.RestServices.Master.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace Capstone2.RestServices.Master.Context
{
    public class UpdatableMasterDBContext : EncryptionDbContext
    {
        //private TenantConnection _tenantReference;

        //public UpdatableDBContext(TenantConnection tenantReference)
        //{
        //    _tenantReference = tenantReference;
        //}
        //protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        //{
        //    optionsBuilder.UseSqlServer(_tenantReference.ClientConnectionString);
        //}

        public UpdatableMasterDBContext(DbContextOptions<UpdatableMasterDBContext> options, IConfiguration configuration) : base(options, configuration)
        {
            //options.use

        }

        public virtual DbSet<MedicareParticipants> ParticipantDetails { get; set; }
        public virtual DbSet<HealthFundGroup> HealthFundGroups { get; set; }
        public virtual DbSet<GroupToHealthFundAssoc> GroupToHealthFundAssocs { get; set; }


        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            //Configure default schema
            modelBuilder.HasDefaultSchema("Common");
            modelBuilder.Entity<MedicareParticipants>().ToTable("ParticipantDetails", "Medicare");
            modelBuilder.Entity<MedicareErrors>().ToTable("Errors", "Medicare");
            modelBuilder.Entity<HealthFundGroup>().ToTable("HealthFundGroups", "Medicare");
            modelBuilder.Entity<GroupToHealthFundAssoc>().ToTable("GroupToHealthFundAssocs", "Medicare");

        }


    }
}
