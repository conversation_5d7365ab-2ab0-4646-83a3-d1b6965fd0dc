﻿using Capstone2.Framework.RestApi.Context;
using Capstone2.RestServices.Snapforms.Models;
using Capstone2.Shared.Models.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace Capstone2.RestServices.Snapforms.Context
{
    public class ReadOnlySnapformsDBContext : EncryptionDbContext
    {
        private TenantConnection _tenantReference;

        public ReadOnlySnapformsDBContext(TenantConnection tenantReference, IConfiguration configuration) : base(configuration)
        {
            _tenantReference = tenantReference;
        }
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseSqlServer(_tenantReference.ClientConnectionString);
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            //Configure default schema
            modelBuilder.Entity<SnapformsConfig>().ToTable("SnapformsConfigs", "Snapforms");

            modelBuilder.Entity<Organisation>().ToTable("Organisation", "Company");

        }
        public virtual DbSet<SnapformsConfig> SnapformsConfigs { get; set; }

        public virtual DbSet<Organisation> Organisation { get; set; }
    }
}
