﻿using Capstone2.Framework.RestApi.Context;
using Capstone2.RestServices.Snapforms.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace Capstone2.RestServices.Snapforms.Context
{
    public class UpdatableSnapformsDBContext : EncryptionDbContext
    {
        private TenantConnection _tenantReference;

        public UpdatableSnapformsDBContext(TenantConnection tenantReference, IConfiguration configuration) : base(configuration)
        {
            _tenantReference = tenantReference;
        }
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseSqlServer(_tenantReference.ClientConnectionString);
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            //Configure default schema
            modelBuilder.Entity<SnapformsConfig>().ToTable("SnapformsConfigs", "Snapforms");
        }

        public virtual DbSet<SnapformsConfig> SnapformsConfigs { get; set; }

    }
}
