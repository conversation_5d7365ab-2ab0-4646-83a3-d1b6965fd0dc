﻿using Capstone2.Framework.RestApi.Context;
using Capstone2.RestServices.Company.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace Capstone2.RestServices.Company.Context
{
    public class UpdatableCompanyDBContext : EncryptionDbContext
    {
        private TenantConnection _tenantReference;

        public UpdatableCompanyDBContext(TenantConnection tenantReference, IConfiguration configuration) : base(configuration)
        {
            _tenantReference = tenantReference;
        }
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseSqlServer(_tenantReference.ClientConnectionString);
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            //Configure default schema
            modelBuilder.HasDefaultSchema("Company");
            modelBuilder.Entity<UserCompanyAssoc>().ToTable("UserCompanyAssocs", "User");
            modelBuilder.Entity<UserDetail>().ToTable("UserDetails", "User");
        }

        public virtual DbSet<CompanyDetail> CompanyDetails { get; set; }
        public virtual DbSet<Address> Addresses { get; set; }
        public virtual DbSet<Label> Labels { get; set; }
        public virtual DbSet<CompanyLabelAssoc> CompanyLabelAssocs { get; set; }
        public virtual DbSet<CategoryMaster> CategoryMaster { get; set; }
        public virtual DbSet<UserDetail> UserDetails { get; set; }
        public virtual DbSet<UserCompanyAssoc> UserCompanyAssocs { get; set; }
        public virtual DbSet<HIServiceDetail> HIServiceDetails { get; set; }

    }
}
