﻿using Capstone2.Framework.RestApi.Context;
using Capstone2.RestServices.Company.Models;
using Capstone2.Shared.Models.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace Capstone2.RestServices.Company.Context
{
    public class ReadOnlyCompanyDBContext : EncryptionDbContext
    {
        private TenantConnection _tenantReference;

        public ReadOnlyCompanyDBContext(TenantConnection tenantReference, IConfiguration configuration) : base(configuration)
        {
            _tenantReference = tenantReference;
        }
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseSqlServer(_tenantReference.ClientConnectionString);
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            //Configure default schema
            modelBuilder.HasDefaultSchema("Company");
            modelBuilder.Entity<Models.UserCompanyAssoc>().ToTable("UserCompanyAssocs", "User");
            modelBuilder.Entity<Models.UserDetail>().ToTable("UserDetails", "User");
            modelBuilder.Entity<UserModel>().ToTable("Users", "Authentication");
            modelBuilder.Entity<RolesPermissionsAssoc>().ToTable("RolesPermissionsAssocs", "Utility");

        }
        // public virtual DbSet<InternalCompany> InternalCompanies { get; set; }
        public virtual DbSet<Models.CompanyDetail> CompanyDetails { get; set; }
        public virtual DbSet<Address> Addresses { get; set; }
        public virtual DbSet<Label> Labels { get; set; }
        public virtual DbSet<CompanyLabelAssoc> CompanyLabelAssocs { get; set; }
        public virtual DbSet<CategoryMaster> CategoryMaster { get; set; }
        public virtual DbSet<Models.UserDetail> UserDetails { get; set; }
        public virtual DbSet<Models.UserCompanyAssoc> UserCompanyAssocs { get; set; }
        public virtual DbSet<UserModel> Users { get; set; }
        public virtual DbSet<Organisation> Organisation { get; set; }
        public virtual DbSet<RolesPermissionsAssoc> RolesPermissionsAssocs { get; set; }
        public virtual DbSet<HIServiceDetail> HIServiceDetails { get; set; }
    }
}
