﻿using Capstone2.Framework.RestApi.Context;
using Capstone2.RestServices.Authentication.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace Capstone2.RestServices.Authentication.Context
{
    public class UpdatableAuthenticationDBContext : EncryptionDbContext
    {
        private TenantConnection _tenantReference;

        public UpdatableAuthenticationDBContext(TenantConnection tenantReference, IConfiguration configuration) : base(configuration)
        {
            _tenantReference = tenantReference;
        }
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseSqlServer(_tenantReference.ClientConnectionString);
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            //Configure default schema
            modelBuilder.HasDefaultSchema("Authentication");
        }
        public virtual DbSet<Authentication.Models.User> Users { get; set; }
        public virtual DbSet<UserFactor> UserFactors { get; set; }
        public virtual DbSet<UserIdentifier> UserIdentifiers { get; set; }
        public virtual DbSet<JwtTokenModel> UserTokenAssocs { get; set; }
        public virtual DbSet<UserAccessToken> UserAccessTokens { get; set; }
        public virtual DbSet<LoginLogoutLog> LoginLogoutLog { get; set; }
        public virtual DbSet<PatientTokenAssoc> PatientTokenAssocs { get; set; }

    }
}
