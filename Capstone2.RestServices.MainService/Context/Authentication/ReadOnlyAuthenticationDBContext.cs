﻿using Capstone2.Framework.RestApi.Context;
using Capstone2.RestServices.Authentication.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace Capstone2.RestServices.Authentication.Context
{
    public class ReadOnlyAuthenticationDBContext : EncryptionDbContext
    {
        private TenantConnection _tenantReference;

        public ReadOnlyAuthenticationDBContext(TenantConnection tenantReference, IConfiguration configuration) : base(configuration)
        {
            _tenantReference = tenantReference;
        }
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseSqlServer(_tenantReference.ClientConnectionString);
        }

        public virtual DbSet<Authentication.Models.User> Users { get; set; }
        public virtual DbSet<UserFactor> UserFactors { get; set; }
        public virtual DbSet<UserIdentifier> UserIdentifiers { get; set; }
        public virtual DbSet<LoginLogoutLog> LoginLogoutLog { get; set; }
        public virtual DbSet<JwtTokenModel> UserTokenAssocs { get; set; }
        public virtual DbSet<EmailTemplate> EmailTemplates { get; set; }
        public virtual DbSet<SmsTemplate> SmsTemplates { get; set; }
        public virtual DbSet<Organisation> Organisation { get; set; }
        public virtual DbSet<UserDetail> UserDetails { get; set; }
        public virtual DbSet<UserAccessToken> UserAccessTokens { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {

            //Configure default schema
            modelBuilder.HasDefaultSchema("Authentication");
            modelBuilder.Entity<EmailTemplate>().ToTable("EmailTemplates", "Communication");
            modelBuilder.Entity<SmsTemplate>().ToTable("SmsTemplates", "Communication");
            modelBuilder.Entity<Organisation>().ToTable("Organisation", "Company");
            modelBuilder.Entity<UserDetail>().ToTable("UserDetails", "User");
        }
    }
}
