﻿using Capstone2.Framework.RestApi.Context;
using Capstone2.RestServices.PatientRegistration.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace Capstone2.RestServices.PatientRegistration.Context
{
    public class UpdatablePatientRegistrationDBContext : EncryptionDbContext
    {
        private TenantConnection _tenantReference;

        public UpdatablePatientRegistrationDBContext(TenantConnection tenantReference, IConfiguration configuration) : base(configuration)
        {
            _tenantReference = tenantReference;
        }
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseSqlServer(_tenantReference.ClientConnectionString);
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            //Configure default schema
            modelBuilder.HasDefaultSchema("PatientRegistration");
            modelBuilder.Entity<PatientTokenAssoc>().ToTable("PatientTokenAssocs", "Authentication");

        }

        public virtual DbSet<Factor> Factors { get; set; }

        public virtual DbSet<PatientDraftData> PatientDraftData { get; set; }

        public virtual DbSet<PatientTokenAssoc> PatientTokenAssocs { get; set; }


    }
}
