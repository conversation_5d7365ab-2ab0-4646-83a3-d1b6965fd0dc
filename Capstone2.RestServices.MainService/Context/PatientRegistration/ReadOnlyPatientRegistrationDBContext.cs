﻿using Capstone2.Framework.RestApi.Context;
using Capstone2.RestServices.PatientRegistration.Models;
using Capstone2.Shared.Models.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace Capstone2.RestServices.PatientRegistration.Context
{
    public class ReadOnlyPatientRegistrationDBContext : EncryptionDbContext
    {
        private TenantConnection _tenantReference;

        public ReadOnlyPatientRegistrationDBContext(TenantConnection tenantReference, IConfiguration configuration) : base(configuration)
        {
            _tenantReference = tenantReference;
        }
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseSqlServer(_tenantReference.ClientConnectionString);
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            //Configure default schema
           
            modelBuilder.Entity<Organisation>().ToTable("Organisation", "Company");
            modelBuilder.HasDefaultSchema("PatientRegistration");
            modelBuilder.Entity<EmailTemplate>().ToTable("EmailTemplates", "Communication");
            modelBuilder.Entity<SmsTemplate>().ToTable("SmsTemplates", "Communication");
            modelBuilder.Entity<UserDetail>().ToTable("UserDetails", "User");
            modelBuilder.Entity<Authentication.Models.User>().ToTable("Users", "Authentication");
            modelBuilder.Entity<UserIdentifier>().ToTable("UserIdentifiers", "Authentication");
            modelBuilder.Entity<UserFactor>().ToTable("UserFactors", "Authentication");
            modelBuilder.Entity<PatientTokenAssoc>().ToTable("PatientTokenAssocs", "Authentication");

        }
     
        public virtual DbSet<Organisation> Organisation { get; set; }
        public virtual DbSet<Factor> Factors { get; set; }

        public virtual DbSet<EmailTemplate> EmailTemplates { get; set; }

        public virtual DbSet<SmsTemplate> SmsTemplates { get; set; }

        public virtual DbSet<UserDetail> UserDetails { get; set; }

        public virtual DbSet<Authentication.Models.User> Users { get; set; }
        public virtual DbSet<UserFactor> UserFactors { get; set; }
        public virtual DbSet<UserIdentifier> UserIdentifiers { get; set; }
        public virtual DbSet<PatientTokenAssoc> PatientTokenAssocs { get; set; }

        public virtual DbSet<PatientDraftData> PatientDraftData { get; set; }
    }
}
