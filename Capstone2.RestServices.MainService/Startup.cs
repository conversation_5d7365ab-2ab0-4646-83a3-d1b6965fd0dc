using Capstone2.Framework.Business.Common;
using Capstone2.Framework.RestApi.Context;
using Capstone2.Framework.RestApi.Filters;
using Capstone2.Framework.RestApi.Middlewares;
using Capstone2.Framework.RestApi.Utility;
using Capstone2.RestServices.Appointment.Context;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.Finance.Context;
using Capstone2.RestServices.Finance.Interfaces;
using Capstone2.RestServices.Finance.Services;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using FluentValidation.AspNetCore;
using Microsoft.ApplicationInsights.AspNetCore.Extensions;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json.Serialization;
using System;
using System.Linq;
using Capstone2.RestServices.Appointment.Services;
using Capstone2.RestServices.Appointment.Interfaces;
using Capstone2.RestServices.Authentication.Context;
using Capstone2.RestServices.CapstoneHub.Context;
using Capstone2.RestServices.File.Context;
using Capstone2.RestServices.Hcp.Context;
using Capstone2.RestServices.Invoice.Context;
using Capstone2.RestServices.InvoicePayment.Context;
using Capstone2.RestServices.Master.Context;
using Capstone2.RestServices.Communication.Context;
using Capstone2.RestServices.Company.Context;
using Capstone2.RestServices.HIService.Context;
using Capstone2.RestServices.MedicalSchedule.Context;
using Capstone2.RestServices.Medicare.Context;
using Capstone2.RestServices.Migration.Context;
using Capstone2.RestServices.MIMS.Context;
using Capstone2.RestServices.Patient.Context;
using Capstone2.RestServices.PatientRegistration.Context;
using Capstone2.RestServices.Proda.Context;
using Capstone2.RestServices.Report.Context;
using Capstone2.RestServices.Snapforms.Context;
using Capstone2.RestServices.SSRSReport.Context;
using Capstone2.RestServices.Syncfusion.Context;
using Capstone2.RestServices.SyncService.Context;
using Capstone2.RestServices.User.Context;
using Capstone2.RestServices.Utility.Context;
using Capstone2.RestServices.Authentication.Interfaces;
using Capstone2.RestServices.Authentication.Services;
using Capstone2.RestServices.CapstoneHub.Interfaces;
using Capstone2.RestServices.CapstoneHub.Services;
using Capstone2.RestServices.Communication.Interfaces;
using Capstone2.RestServices.Communication.Services;
using Capstone2.RestServices.File.Interfaces;
using Capstone2.RestServices.File.Services;
using Capstone2.RestServices.Hcp.Interfaces;
using Capstone2.RestServices.Hcp.Services;
using Capstone2.RestServices.HIService.Interfaces;
using Capstone2.RestServices.HIService.Services;
using Capstone2.RestServices.Invoice.Interfaces;
using Capstone2.RestServices.Invoice.Services;
using Capstone2.RestServices.InvoicePayment.Interfaces;
using Capstone2.RestServices.InvoicePayment.Services;
using Capstone2.RestServices.Master.Interfaces;
using Capstone2.RestServices.Master.Services;
using Capstone2.RestServices.MedicalSchedule.Interfaces;
using Capstone2.RestServices.MedicalSchedule.Services;
using Capstone2.RestServices.Medicare.Interfaces;
using Capstone2.RestServices.Medicare.Services;
using Capstone2.RestServices.Migration.Interfaces;
using Capstone2.RestServices.Migration.Services;
using Capstone2.RestServices.MIMS.Interfaces;
using Capstone2.RestServices.MIMS.Services;
using Capstone2.RestServices.Patient.Interfaces;
using Capstone2.RestServices.Patient.Services;
using Capstone2.RestServices.PatientRegistration.Interfaces;
using Capstone2.RestServices.PatientRegistration.Services;
using Capstone2.RestServices.Proda.Utility;
using Capstone2.RestServices.Proda.Interfaces;
using Capstone2.RestServices.Proda.Services;
using Capstone2.RestServices.Report.Interfaces;
using Capstone2.RestServices.Report.Services;
using Capstone2.RestServices.Snapforms.Interfaces;
using Capstone2.RestServices.Snapforms.Services;
using Capstone2.RestServices.SSRSReport.Interfaces;
using Capstone2.RestServices.SSRSReport.Services;
using Capstone2.RestServices.Syncfusion.Interfaces;
using Capstone2.RestServices.Syncfusion.Services;
using Capstone2.RestServices.SyncService.Interfaces;
using Capstone2.RestServices.SyncService.Services;
using System.Collections.Generic;
using BoldReports.Web;
using Capstone2.RestServices.Proda.Models;
using Capstone2.RestServices.MIMS.Models;
using Capstone2.RestServices.Authentication.Middlewares;
using Capstone2.RestServices.PatientRegistration.Middlewares;
using StackExchange.Redis;
using Capstone2.RestServices.Snapforms.Models;
using Capstone2.RestServices.HIService.Models;
using Newtonsoft.Json;
using Syncfusion.Licensing;

namespace Capstone2.RestServices.MainService
{
    public class Startup
    {
        public Startup(IConfiguration configuration, IHostEnvironment hostEnvironment)
        {
            Configuration = configuration;
            HostEnvironment = hostEnvironment;
        }

        public IConfiguration Configuration { get; }
        public IHostEnvironment HostEnvironment { get; }

        public void ConfigureServices(IServiceCollection services)
        {
            var connectionString = Configuration.GetConnectionString("Capstone2ReadOnlyMasterDBConn");

            services.AddControllers(options =>
            {
                options.Filters.Add<ApiExceptionFilter>();
            }).AddFluentValidation(fv =>
            {
                fv.RunDefaultMvcValidationAfterFluentValidationExecutes = false;
                fv.RegisterValidatorsFromAssemblyContaining<Startup>();
            })
            .AddNewtonsoftJson(options =>
            {
                options.SerializerSettings.ReferenceLoopHandling = ReferenceLoopHandling.Ignore;
                options.SerializerSettings.ContractResolver = new DefaultContractResolver();
                options.SerializerSettings.NullValueHandling = NullValueHandling.Include;
            });

            services.AddApplicationInsightsTelemetry(new ApplicationInsightsServiceOptions
            {
                EnableActiveTelemetryConfigurationSetup = true,
                InstrumentationKey = Configuration["ApplicationInsights:InstrumentationKey"],
                ConnectionString = Configuration["ApplicationInsights:ConnectionString"]
            });

            services.AddScoped(prov => prov.GetService<IHttpContextAccessor>()?.HttpContext?.GetTenantConnection(Configuration));

            services.AddDbContext<ReadOnlyDBMasterContext>(options => options.UseSqlServer(Configuration.GetConnectionString("Capstone2ReadOnlyMasterDBConn"), sqlOptions =>
            {
                // Configure SQL retry logic
                sqlOptions.EnableRetryOnFailure(
                    maxRetryCount: 3, // Retry up to 3 times
                    maxRetryDelay: TimeSpan.FromSeconds(2), // Delay between retries
                    errorNumbersToAdd: null); // Retry for all transient errors
            }));
            services.AddDbContext<UpdatableDBMasterContext>(options => options.UseSqlServer(Configuration.GetConnectionString("Capstone2ReadOnlyMasterDBConn"), sqlOptions =>
            {
                // Configure SQL retry logic
                sqlOptions.EnableRetryOnFailure(
                    maxRetryCount: 3, // Retry up to 3 times
                    maxRetryDelay: TimeSpan.FromSeconds(2), // Delay between retries
                    errorNumbersToAdd: null); // Retry for all transient errors
            }));

            //Finance DB Context
            services.AddDbContext<UpdatableFinanceDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);

            }));
            services.AddDbContext<ReadOnlyFinanceDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);
            }));


            // Appointments DB Context

            services.AddDbContext<UpdatableAppointmentsDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);

            }));
            services.AddDbContext<ReadOnlyAppointmentsDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);
            }));

            // Authentication DB Context

            services.AddDbContext<UpdatableAuthenticationDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);

            }));
            services.AddDbContext<ReadOnlyAuthenticationDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);
            }));

            // Capstonehub DB Context

            services.AddDbContext<UpdatableCapstoneHubDBContextDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);

            }));
            services.AddDbContext<ReadOnlyCapstoneHubDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);
            }));

            //Communication DB Context

            services.AddDbContext<UpdatableCommunicationDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);

            }));
            services.AddDbContext<ReadOnlyCommunicationDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);
            }));

            //Company DB Context

            services.AddDbContext<UpdatableCompanyDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);

            }));
            services.AddDbContext<ReadOnlyCompanyDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);
            }));

            //File DB Context

            services.AddDbContext<UpdatableFileDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);

            }));
            services.AddDbContext<ReadOnlyFileDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);
            }));



            //HCP DB Context

            services.AddDbContext<UpdatableHcpDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);

            }));
            services.AddDbContext<ReadOnlyHcpDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);
            }));




            //HiService DB contect

            services.AddDbContext<UpdatableHIServiceDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);

            }));
            services.AddDbContext<ReadOnlyHIServiceDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);
            }));

            //Invoice DB Context
            services.AddDbContext<UpdatableInvoiceDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);

            }));
            services.AddDbContext<ReadOnlyInvoiceDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);
            }));

            //Invoicepayment DB Context

            services.AddDbContext<UpdatableInvoicePaymentDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);

            }));
            services.AddDbContext<ReadOnlyInvoicePaymentDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);
            }));

            //Master DB Context

            services.AddDbContext<UpdatableMasterDBContext>(options => options.UseSqlServer(Configuration.GetConnectionString("Capstone2MasterDBConn"), sqlOptions =>
            {
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);

            }));
            services.AddDbContext<ReadOnlyMasterDBContext>(options => options.UseSqlServer(Configuration.GetConnectionString("Capstone2ReadOnlyMasterDBConn"), sqlOptions =>
            {
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);
            }));

            // Medical schedule

            services.AddDbContext<UpdatableMedicalScheduleDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);

            }));
            services.AddDbContext<ReadOnlyMedicalScheduleDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);
            }));

            // Medicare DB Context

            services.AddDbContext<UpdatableMedicareDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);

            }));
            services.AddDbContext<ReadOnlyMedicareDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);
            }));

            //Migration db context

            services.AddDbContext<UpdatableMigrationDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);

            }));
            services.AddDbContext<ReadOnlyMigrationDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);
            }));

            //MIMS DB Context

            services.AddDbContext<UpdatableMIMSDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);

            }));
            services.AddDbContext<ReadOnlyMIMSDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);
            }));

            //Patient db context

            services.AddDbContext<UpdatablePatientDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);

            }));
            services.AddDbContext<ReadOnlyPatientDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);
            }));

            //Patinet Registration db context

            services.AddDbContext<UpdatablePatientRegistrationDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);

            }));
            services.AddDbContext<ReadOnlyPatientRegistrationDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);
            }));

            // Proda db context

            services.AddDbContext<UpdatableProdaDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);

            }));
            services.AddDbContext<ReadOnlyProdaDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);
            }));

            //Report db context

            services.AddDbContext<UpdatableReportDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);

            }));
            services.AddDbContext<ReadOnlyReportDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);
            }));

            //Snapform db context

            services.AddDbContext<UpdatableSnapformsDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);

            }));
            services.AddDbContext<ReadOnlySnapformsDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);
            }));

            // SSRS Report db context

            services.AddDbContext<UpdatableSSRSReportDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);

            }));
            services.AddDbContext<ReadOnlySSRSReportDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);
            }));

            //Syncfusion db context

            services.AddDbContext<UpdatableSyncfusionDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);

            }));
            services.AddDbContext<ReadOnlySyncfusionDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);
            }));


            //Syncservice db context

            services.AddDbContext<UpdatableSyncServiceDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);

            }));
            services.AddDbContext<ReadOnlySyncServiceDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);
            }));

            //User Db Context

            services.AddDbContext<UpdatableUserDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);

            }));
            services.AddDbContext<ReadOnlyUserDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);
            }));

            // Utility Db context

            services.AddDbContext<UpdatableUtilityDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);

            }));
            services.AddDbContext<ReadOnlyUtilityDBContext>(options => options.UseSqlServer(sqlOptions =>
            {
                
                sqlOptions.EnableRetryOnFailure(maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(2),
                errorNumbersToAdd: null);
            }));

            if (!HostEnvironment.IsDevelopment())
            {
                var redisConfigurationOptions = ConfigurationOptions.Parse(Configuration.GetConnectionString("AzureRedisConnection"));
                redisConfigurationOptions.ConnectTimeout = 15000;
                redisConfigurationOptions.SyncTimeout = 15000;
                services.AddStackExchangeRedisCache(options =>
                {
                    options.ConfigurationOptions = redisConfigurationOptions;
                    options.InstanceName = Configuration.GetConnectionString("RedisCacheInstanceName");
                });
            }

            //services.AddStackExchangeRedisCache(options =>
            //{
            //    options.Configuration = Configuration.GetConnectionString("AzureRedisConnection");
            //    options.InstanceName = Configuration.GetConnectionString("RedisCacheInstanceName");
            //});
            // inject App setting
            var appSettingSection = Configuration.GetSection("AppSettings");
            var appsetting = appSettingSection.Get<AppSettings>();
            var appPRODASettings = Configuration.GetSection("AppSettings:PRODASettings");
            var appMIMSApiSetting = Configuration.GetSection("AppSettings:MIMSApiSettings");
            var snapformsAPISetting = Configuration.GetSection("AppSettings:SnapformsAPISettings");
            var appHIServiceSetting = Configuration.GetSection("AppSettings:HIServiceSettings");
            var appHubIntegrationSettingsV2 = Configuration.GetSection("AppSettings:HubIntegrationSettingsV2");

            services.Configure<AppSettings>(appSettingSection);
            services.Configure<PRODASettings>(appPRODASettings);
            services.Configure<AppSettings>(appSettingSection);
            services.Configure<MIMSApiSetting>(appMIMSApiSetting);
            services.Configure<SnapformsAPISettings>(snapformsAPISetting);
            services.Configure<HIServiceSettings>(appHIServiceSetting);
            services.Configure<HubIntegrationSettingsV2>(appHubIntegrationSettingsV2);


            //Finance
            services.AddScoped<IFinanceBAL, FinanceBAL>();
            services.AddScoped<IFinanceDAL, FinanceDAL>();

            //User
            services.AddScoped<User.Interfaces.IAddressDAL, User.Services.AddressDAL>();
            services.AddScoped<User.Interfaces.IUserBAL, User.Services.UserBAL>();
            services.AddScoped<User.Interfaces.IUserDAL, User.Services.UserDAL>();
            services.AddScoped<User.Interfaces.IScheduleBAL, User.Services.ScheduleBAL>();
            services.AddScoped<User.Interfaces.IScheduleDAL, User.Services.ScheduleDAL>();
            services.AddScoped<User.Interfaces.IListUserBAL, User.Services.ListUserBAL>();
            services.AddScoped<User.Interfaces.IListUserDAL, User.Services.ListUserDAL>();
            services.AddScoped<User.Interfaces.IUserCompanyDAL, User.Services.UserCompanyDAL>();
            services.AddScoped<User.Interfaces.IExtProviderBAL, User.Services.ExtProviderBAL>();
            services.AddScoped<User.Interfaces.IMyActionBAL, User.Services.MyActionBAL>();
            services.AddScoped<User.Interfaces.IMyActionDAL, User.Services.MyActionDAL>();
            services.AddScoped<User.Interfaces.IDashboardBAL, User.Services.DashboardBAL>();
            services.AddScoped<User.Interfaces.IDashboardDAL, User.Services.DashboardDAL>();
            services.AddScoped<User.Interfaces.IPatientActionsBAL, User.Services.PatientActionsBAL>();
            services.AddScoped<User.Interfaces.IPatientActionsDAL, User.Services.PatientActionsDAL>();
            services.AddScoped<User.Interfaces.IIncomingLetterBAL, User.Services.IncomingLetterBAL>();
            services.AddScoped<User.Interfaces.IIncomingLetterDAL, User.Services.IncomingLetterDAL>();
            services.AddScoped<User.Interfaces.IIncomingLetterActionsBAL, User.Services.IncomingLetterActionsBAL>();
            services.AddScoped<User.Interfaces.IIncomingLetterActionsDAL, User.Services.IncomingLetterActionsDAL>();
            services.AddScoped<User.Interfaces.IIncomingLetterPatientActionsBAL, User.Services.IncomingLetterPatientActionsBAL>();
            services.AddScoped<User.Interfaces.IIncomingLetterPatientActionsDAL, User.Services.IncomingLetterPatientActionsDAL>();

            //Appointment

            services.AddScoped<IAppointmentBAL, AppointmentBAL>();
            services.AddScoped<IAppointmentDAL, AppointmentDAL>();

            //Authentication

            services.AddScoped<IOffsiteAccessBAL, OffSiteAccessBAL>();
            services.AddScoped<IOffsiteAccessDAL, OffSiteAccessDAL>();
            services.AddScoped<IPasswordAccessBAL, PasswordAccessBAL>();
            services.AddScoped<IPasswordAccessDAL, PasswordAccessDAL>();
            services.AddScoped<IAuthTokenBAL, AuthTokenBAL>();
            services.AddScoped<IAuthTokenDAL, AuthTokenDAL>();
            services.AddScoped<IResetPasswordBAL, ResetPasswordBAL>();
            services.AddScoped<IResetPasswordDAL, ResetPasswordDAL>();
            services.AddScoped<IAuthUserBAL, AuthUserBAL>();
            services.AddScoped<IAuthUserDAL, AuthUserDAL>();
            services.AddScoped<IUserActivateDAL, UserActivateDAL>();
            services.AddScoped<IUserActivateBAL, UserActivateBAL>();
            // services.AddTransient<RequestBodyLoggingMiddleware>();


            // Capstone hub

            services.AddScoped<ICapstoneHubBAL, CapstoneHubBAL>();
            services.AddScoped<ICapstoneHubDAL, CapstoneHubDAL>();

            //Communication
            services.AddScoped<IEmailBAL, EmailBAL>();
            services.AddScoped<Communication.Interfaces.ISmsBAL, Communication.Services.SmsBAL>();
            services.AddScoped<Communication.Interfaces.ISmsDAL, Communication.Services.SmsDAL>();
            services.AddScoped<Communication.Interfaces.ISmsBAL, Communication.Services.SmsBAL>();
            services.AddScoped<Communication.Interfaces.ISmsDAL, Communication.Services.SmsDAL>();
            services.AddScoped<IEmailDAL, EmailDAL>();

            //Company
            services.AddScoped<Company.Interfaces.ICompanyDetailsBAL, Company.Services.CompanyDetailBAL>();
            services.AddScoped<Company.Interfaces.ICompanyDetailsDAL, Company.Services.CompanyDetailsDAL>();
            services.AddScoped<Company.Interfaces.IAddressDAL, Company.Services.AddressDAL>();
            services.AddScoped<Company.Interfaces.ILabelDAL, Company.Services.LabelDAL>();
            services.AddScoped<Company.Interfaces.ICategoryMasterBAL, Company.Services.CategoryMasterBAL>();
            services.AddScoped<Company.Interfaces.ICategoryMasterDAL, Company.Services.CategoryMasterDAL>();

            //File

            services.AddScoped<IFileBAL, FileBAL>();
            services.AddScoped<IFileDAL, FileDAL>();

            // Hcp
            services.AddScoped<IHcpBAL, HcpBAL>();
            services.AddScoped<IHcpDAL, HcpDAL>();

            //His service
            services.AddScoped<HIService.Interfaces.IRequestLogsBAL, HIService.Services.RequestLogsBAL>();
            services.AddScoped<HIService.Interfaces.IRequestLogsDAL, HIService.Services.RequestLogsDAL>();
            services.AddScoped<IIdentifierSearchBAL, IdentifierSearchBAL>();
            services.AddScoped<IIdentifierSearchService, IdentifierSearchService>();
            services.AddScoped<IIdentifierBatchSearchBAL, IdentifierBatchSearchBAL>();
            services.AddScoped<IIdentifierBatchSearchService, IdentifierBatchSearchService>();
            services.AddScoped<IIndividualSearchBAL, IndividualSearchBAL>();
            services.AddScoped<IIndividualSearchService, IndividualSearchService>();
            services.AddScoped<IIndividualBatchSearchBAL, IndividualBatchSearchBAL>();
            services.AddScoped<IIndividualBatchSearchService, IndividualBatchSearchService>();
            services.AddScoped<IOrganisationSearchBAL, OrganisationSearchBAL>();
            services.AddScoped<IOrganisationSearchService, OrganisationSearchService>();
            services.AddScoped<IOrganisationBatchSearchBAL, OrganisationBatchSearchBAL>();
            services.AddScoped<IOrganisationBatchSearchService, OrganisationBatchSearchService>();
            services.AddScoped<HIService.Interfaces.IHILookupBal, HIService.Services.HILookupBal>();
            services.AddScoped<HIService.Interfaces.IHILookupDal, HIService.Services.HILookupDal>();

            // Invoice

            services.AddScoped<IInvoiceBAL, InvoiceBAL>();
            services.AddScoped<IInvoiceDAL, InvoiceDAL>();
            services.AddScoped<IClaimsBAL, ClaimsBAL>();
            services.AddScoped<IEclipseClaimsBAL, EclipseClaimsBAL>();

            // Invoice Payment

            services.AddScoped<IPaymentBAL, PaymentBAL>();
            services.AddScoped<IPaymentDAL, PaymentDAL>();

            // Master
            services.AddScoped<IMasterBAL, MasterBAL>();
            services.AddScoped<IMasterDAL, MasterDAL>();
            services.AddScoped<IHealthFundBAL, HealthFundBAL>();
            services.AddScoped<IHealthFundDAL, HealthFundDAL>();

            // Medical Schedule
            services.AddScoped<MedicalSchedule.Interfaces.IUploaderDAL, MedicalSchedule.Services.UploaderDAL>();
            services.AddScoped<IMbsDAL, MbsDAL>();
            services.AddScoped<MedicalSchedule.Interfaces.IUploaderBAL, MedicalSchedule.Services.UploaderBAL>();
            services.AddScoped<IMbsBAL, MbsBAL>();
            services.AddScoped<IDvaDAL, DvaDAL>();
            services.AddScoped<IDvaBAL, DvaBAL>();
            services.AddScoped<IClinicalCategoryDAL, ClinicalCategoryDAL>();
            services.AddScoped<IClinicalCategoryBAL, ClinicalCategoryBAL>();
            services.AddScoped<IMbsClinicalCategoryDataDAL, MbsClinicalCategoryDataDAL>();
            services.AddScoped<IMbsClinicalCategoryDataBAL, MbsClinicalCategoryDataBAL>();
            services.AddScoped<ITheatreBandingDataDAL, TheatreBandingDataDAL>();
            services.AddScoped<ITheatreBandingDataBAL, TheatreBandingDataBAL>();
            services.AddScoped<IDvaBAL, DvaBAL>();
            services.AddScoped<IDvaBAL, DvaBAL>();

            // Medicare
            services.AddScoped<Medicare.Interfaces.IRequestLogsBAL, Medicare.Services.RequestLogsBAL>();
            services.AddScoped<Medicare.Interfaces.IRequestLogsDAL, Medicare.Services.RequestLogsDAL>();
            services.AddScoped<IBulkBillService, BulkBillService>();
            services.AddScoped<IDVAClaimService, DVAClaimService>();
            services.AddScoped<IDVYWService, DVYWService>();
            services.AddScoped<IDVRWService, DVRWService>();
            services.AddScoped<IBPYWService, BPYWService>();
            services.AddScoped<IBPRWService, BPRWService>();
            services.AddScoped<Medicare.Interfaces.ISubscriberDetailsBAL, Medicare.Services.SubscriberDetailsBAL>();
            services.AddScoped<Medicare.Interfaces.ISubscriberDetailsDAL, Medicare.Services.SubscriberDetailsDAL>();
            services.AddScoped<IPatientVerificationService, PatientVerificationService>();
            services.AddScoped<IVeteranPatientVerificationService, VeteranPatientVerificationService>();
            services.AddScoped<IParticipantFundsService, ParticipantFundsService>();
            services.AddScoped<IPatientClaimInterService, PatientClaimInterService>();
            services.AddScoped<ISDDClaimService, SDDClaimService>();
            services.AddScoped<IOnlineEligibilityCheckService, OnlineEligibilityCheckService>();
            services.AddScoped<IStatusReportService, StatusReportService>();
            services.AddScoped<IERAWService, ERAWService>();
            services.AddScoped<IAccessTokenHelper, AccessTokenHelper>();
            services.AddScoped<IInPatientClaimService, InPatientClaimService>();
            services.AddScoped<IInHospitalClaimService, InHospitalClaimService>();
            services.AddScoped<IRetrieveReportService, RetrieveReportService>();
            services.AddTransient<IIHCRetrieveReportService, IHCRetrieveReportService>();

            //Migration
            services.AddScoped<Migration.Interfaces.IUploaderBAL, Migration.Services.UploaderBAL>();
            services.AddScoped<Migration.Interfaces.IUploaderDAL, Migration.Services.UploaderDAL>();
            services.AddScoped<IMediaUploadBAL, MediaUploadBAL>();
            services.AddScoped<IMediaUploadDAL, MediaUploadDAL>();

            //MIMS
            services.AddScoped<MIMS.Interfaces.IHelperService, MIMS.Services.HelperService>();
            services.AddScoped<IProductBAL, ProductBAL>();
            services.AddScoped<IProductDAL, ProductDAL>();

            //Patient
            services.AddScoped<IPatientBAL, PatientBAL>();
            services.AddScoped<IPatientDAL, PatientDAL>();
            services.AddScoped<ICommBAL, CommBAL>();
            services.AddScoped<ICommDAL, CommDAL>();
            services.AddScoped<IReferralDAL, ReferralDAL>();
            services.AddScoped<IHealthCareTeamDAL, HealthCareTeamDAL>();
            services.AddScoped<IMediaLibraryBAL, MediaLibraryBAL>();
            services.AddScoped<IMediaLibraryDAL, MediaLibraryDAL>();
            services.AddScoped<ILetterBAL, LetterBAL>();
            services.AddScoped<ILetterDAL, LetterDAL>();
            services.AddScoped<IConsultNoteTemplateBAL, ConsultNoteTemplateBAL>();
            services.AddScoped<IConsultNoteTemplateDAL, ConsultNoteTemplateDAL>();
            services.AddScoped<IActivityLogBAL, ActivityLogBAL>();
            services.AddScoped<IActivityLogDAL, ActivityLogDAL>();
            services.AddScoped<IHealthCareTeamBAL, HealthCareTeamBAL>();
            services.AddScoped<IHealthCareTeamDAL, HealthCareTeamDAL>();
            services.AddScoped<IAccountHolderBAL, AccountHolderBAL>();
            services.AddScoped<IAccountHolderDAL, AccountHolderDAL>();
            services.AddScoped<IHealthDetailsBAL, HealthDetailsBAL>();
            services.AddScoped<IHealthDetailsDAL, HealthDetailsDAL>();
            services.AddScoped<IMedicalRecordsBAL, MedicalRecordsBAL>();
            services.AddScoped<IMedicalRecordsDAL, MedicalRecordsDAL>();
            services.AddScoped<IPathologyRequestBAL, PathologyRequestBAL>();
            services.AddScoped<IPathologyRequestDAL, PathologyRequestDAL>();
            services.AddScoped<Patient.Interfaces.ISmsBAL, Patient.Services.SmsBAL>();
            services.AddScoped<Patient.Interfaces.ISmsDAL, Patient.Services.SmsDAL>();

            // Patient registration
            services.AddScoped<IPatientRegBAL, PatientRegBAL>();
            services.AddScoped<IPatientRegDAL, PatientRegDAL>();
            services.AddScoped<IPatientWrapperBAL, PatientWrapperBAL>();
            services.AddScoped<IPatientWrapperDAL, PatientWrapperDAL>();

            //Proda
            services.AddScoped<IProdaKeyVaultHelper, ProdaKeyVaultHelper>();
            services.AddScoped<IProdaCertificateHelper, ProdaCertificateHelper>();
            services.AddScoped<IProdaConfigsBAL, ProdaConfigsBAL>();
            services.AddScoped<IProdaConfigsDAL, ProdaConfigsDAL>();
            services.AddScoped<Proda.Interfaces.IRequestLogsBAL, Proda.Services.RequestLogsBAL>();
            services.AddScoped<Proda.Interfaces.IRequestLogsDAL, Proda.Services.RequestLogsDAL>();
            services.AddScoped<IProdaService, ProdaService>();
            services.AddScoped<Proda.Interfaces.ISubscriberDetailsBAL, Proda.Services.SubscriberDetailsBAL>();
            services.AddScoped<Proda.Interfaces.ISubscriberDetailsDAL, Proda.Services.SubscriberDetailsDAL>();

            //Reports
            services.AddScoped<IReportBAL, ReportBAL>();
            services.AddScoped<IReportDAL, ReportDAL>();
            services.AddScoped<IMedicareExceptionDAL, MedicareExceptionDAL>();
            services.AddScoped<IMedicareExceptionBAL, MedicareExceptionBAL>();
            services.AddScoped<IProcessingReportBAL, ProcessingReportBAL>();
            services.AddScoped<IProcessingReportDAL, ProcessingReportDAL>();
            services.AddScoped<IIMCProcessingReportBAL, IMCProcessingReportBAL>();
            services.AddScoped<IIMCProcessingReportDAL, IMCProcessingReportDAL>();
            services.AddScoped<Report.Interfaces.IRequestLogsBAL, Report.Services.RequestLogsBAL>();
            services.AddScoped<Report.Interfaces.IRequestLogsDAL, Report.Services.RequestLogsDAL>();

            //Snapforms
            services.AddScoped<Snapforms.Interfaces.IHelperService, Snapforms.Services.HelperService>();
            services.AddScoped<IFormFieldsBAL, FormFieldsBAL>();
            services.AddScoped<IFormFieldsService, FormFieldsService>();
            services.AddScoped<ISnapformsConfigBAL, SnapformsConfigBAL>();
            services.AddScoped<ISnapformsConfigDAL, SnapformsConfigDAL>();

            //SSRS Report
            services.AddScoped<ISSRSReportBAL, SSRSReportBAL>();
            services.AddScoped<ISSRSReportDAL, SSRSReportDAL>();

            //Syncfusion
            services.AddScoped<ISyncfusionBAL, SyncfusionBAL>();

            //SyncService
            services.AddScoped<ISyncServiceBAL, SyncServiceBAL>();
            services.AddScoped<ISyncServiceDAL, SyncServiceDAL>();

            //Utility
            services.AddScoped<Capstone2.RestServices.Utility.Interfaces.IMbsBAL, Capstone2.RestServices.Utility.Services.MbsBAL>();
            services.AddScoped<Capstone2.RestServices.Utility.Interfaces.IMbsDAL, Capstone2.RestServices.Utility.Services.MbsDAL>();
            services.AddScoped<Capstone2.RestServices.Utility.Interfaces.ISnippetBAL, Capstone2.RestServices.Utility.Services.SnippetBAL>();
            services.AddScoped<Capstone2.RestServices.Utility.Interfaces.ISnippetDAL, Capstone2.RestServices.Utility.Services.SnippetDAL>();
            services.AddScoped<Capstone2.RestServices.Utility.Interfaces.IActivityBAL, Capstone2.RestServices.Utility.Services.ActivityBAL>();
            services.AddScoped<Capstone2.RestServices.Utility.Interfaces.IActivityDAL, Capstone2.RestServices.Utility.Services.ActivityDAL>();
            services.AddScoped<Capstone2.RestServices.Utility.Interfaces.IChecklistBAL, Capstone2.RestServices.Utility.Services.ChecklistBAL>();
            services.AddScoped<Capstone2.RestServices.Utility.Interfaces.IChecklistDAL, Capstone2.RestServices.Utility.Services.ChecklistDAL>();
            services.AddScoped<Capstone2.RestServices.Utility.Interfaces.IRolesBAL, Capstone2.RestServices.Utility.Services.RolesBAL>();
            services.AddScoped<Capstone2.RestServices.Utility.Interfaces.IRolesDAL, Capstone2.RestServices.Utility.Services.RolesDAL>();
            services.AddScoped<Capstone2.RestServices.Utility.Interfaces.ILetterTemplateBAL, Capstone2.RestServices.Utility.Services.LetterTemplateBAL>();
            services.AddScoped<Capstone2.RestServices.Utility.Interfaces.ILetterTemplateDAL, Capstone2.RestServices.Utility.Services.LetterTemplateDAL>();
            services.AddScoped<Capstone2.RestServices.Utility.Interfaces.IAppointmentTypeBAL, Capstone2.RestServices.Utility.Services.AppointmentTypeBAL>();
            services.AddScoped<Capstone2.RestServices.Utility.Interfaces.IAppointmentTypeDAL, Capstone2.RestServices.Utility.Services.AppointmentTypeDAL>();
            services.AddScoped<Capstone2.RestServices.Utility.Interfaces.IEpisodeBAL, Capstone2.RestServices.Utility.Services.EpisodeBAL>();
            services.AddScoped<Capstone2.RestServices.Utility.Interfaces.IEpisodeDAL, Capstone2.RestServices.Utility.Services.EpisodeDAL>();
            services.AddScoped<Capstone2.RestServices.Utility.Interfaces.IConsultNoteTemplateBAL, Capstone2.RestServices.Utility.Services.ConsultNoteTemplateBAL>();
            services.AddScoped<Capstone2.RestServices.Utility.Interfaces.IConsultNoteTemplateDAL, Capstone2.RestServices.Utility.Services.ConsultNoteTemplateDAL>();
            services.AddScoped<Capstone2.RestServices.Utility.Interfaces.IConsultNoteTemplateMediaBAL, Capstone2.RestServices.Utility.Services.ConsultNoteTemplateMediaBAL>();
            services.AddScoped<Capstone2.RestServices.Utility.Interfaces.IConsultNoteTemplateMediaDAL, Capstone2.RestServices.Utility.Services.ConsultNoteTemplateMediaDAL>();
            services.AddScoped<Capstone2.RestServices.Utility.Interfaces.IPrintTemplateBAL, Capstone2.RestServices.Utility.Services.PrintTemplateBAL>();
            services.AddScoped<Capstone2.RestServices.Utility.Interfaces.IPrintTemplateDAL, Capstone2.RestServices.Utility.Services.PrintTemplateDAL>();
            services.AddScoped<Capstone2.RestServices.Utility.Interfaces.IMedicareParticipantDetailsBAL, Capstone2.RestServices.Utility.Services.MedicareParticipantDetailsBAL>();
            services.AddScoped<Capstone2.RestServices.Utility.Interfaces.IMedicareParticipantDetailsDAL, Capstone2.RestServices.Utility.Services.MedicareParticipantDetailsDAL>();

            services.AddTransient<IDistributedCacheHelper, DistributedCacheHelper>();

            services.AddTransient<Authentication.Middlewares.SecurityMiddleware>();

            services.AddCors();
            services.AddAutoMapper(typeof(Startup));
            services.RegisterAuthUtilityDI();
            services.AddMvc(setupAction =>
                {
                    setupAction.EnableEndpointRouting = false;
                });
            //).AddNewtonsoftJson(options => options.SerializerSettings.ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore
            //).AddNewtonsoftJson(options => options.SerializerSettings.ContractResolver = new DefaultContractResolver())
            //.AddNewtonsoftJson(options => options.SerializerSettings.NullValueHandling = Newtonsoft.Json.NullValueHandling.Include);




            Bold.Licensing.BoldLicenseProvider.RegisterLicense(Configuration["AppSettings:BoldReportKey"]);
            ReportConfig.DefaultSettings = new ReportSettings().RegisterExtensions(new List<string> { "BoldReports.Data.WebData",
                                                                                                      "BoldReports.Data.Excel",
                                                                                                       "BoldReports.Data.Csv"});

            AddApiBehavioorOptions(services);

            var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Local";

            services.AddSwaggerGen(c =>
            {

                var swaggerTitle = environment switch
                {
                    "UAT" => "Main Service - UAT",
                    "Production" => "Main Service - Production",
                    "Development" => "Main Service - Development",
                    _ => "Main Service - Local"
                };

                c.SwaggerDoc("v1", new OpenApiInfo { Title = swaggerTitle, Version = "v1" });

                c.CustomSchemaIds(Type => Type.FullName);
                c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
                {
                    In = ParameterLocation.Header,
                    Description = "Please enter JWT with Bearer into field",
                    Name = "Authorization",
                    Type = SecuritySchemeType.ApiKey
                });
                c.CustomSchemaIds(Type => Type.FullName);
                c.AddSecurityRequirement(new OpenApiSecurityRequirement
                {
                    {
                          new OpenApiSecurityScheme
                            {
                                Reference = new OpenApiReference
                                {
                                    Type = ReferenceType.SecurityScheme,
                                    Id = "Bearer"
                                }
                            },
                            new string[] {}
                    }
                });
            });
        }



        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.


        public void SetupMiddlewares(IApplicationBuilder app)
        {
            var middlewareMappings = new (string[] Paths, Type Middleware)[]
            {
                (new[] { "/appointment", "/auth", "/capstonehub", "/email", "/sms", "/company", "/file", "/finance", "/hcp", "/hiservice", "/invoice", "/invoicepayment", "/master", "/MedicalSchedule", "/medicare", "/Migration", "/mims", "/patient", "/proda", "/report", "/snapforms", "/ssrsreport", "/syncfusion", "/syncservice", "/user", "/utility" }, typeof(TenantConnectionMiddleware)),
                (new[] { "/appointment", "/capstonehub", "/email", "/sms", "/company", "/file", "/finance", "/hcp", "/hiservice", "/invoice", "/invoicepayment", "/master", "/MedicalSchedule", "/medicare", "/Migration", "/mims", "/patient", "/proda", "/report", "/snapforms", "/ssrsreport", "/syncfusion", "/syncservice", "/user", "/utility" }, typeof(AuthenticationMiddleware)),
                (new[] { "/patientregistration" }, typeof(PublicTenantConnectionMiddleware)),
                (new[] { "/patientregistration" }, typeof(PublicAuthenticationMiddleware)),
                (new[] { "/company", "/file", "/hcp", "/hiservice", "/invoice", "/invoicepayment", "/MedicalSchedule", "/medicare", "/Migration", "/mims", "/patient", "/proda", "/report", "/snapformss", "/user", "/utility" }, typeof(RoleAuthorizeMiddleware)),
                (new[] { "/auth" }, typeof(SecurityMiddleware))
            };

            foreach (var (paths, middleware) in middlewareMappings)
            {
                app.UseWhen(context => paths.Any(path => context.Request.Path.StartsWithSegments(path, StringComparison.OrdinalIgnoreCase)),
                    appBuilder => appBuilder.UseMiddleware(middleware));
            }
        }

        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            app.UseForwardedHeaders(new ForwardedHeadersOptions
            {
                ForwardedHeaders = ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto,
                ForwardLimit = null,
                KnownNetworks = { }, // Optional: allow all
                KnownProxies = { }   // Optional: allow all
            });

            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }
            app.UseSwagger();
            app.UseSwaggerUI(c => c.SwaggerEndpoint("/swagger/v1/swagger.json", "Main Service v1"));

            //app.UseRequestBodyLogging();

            app.UseHttpsRedirection();
           
            SyncfusionLicenseProvider.RegisterLicense(Configuration["AppSettings:SyncfusionLicenseKey"]);

            app.UseRouting();

            app.UseCors(
                options => options.SetIsOriginAllowed(x => _ = true).AllowAnyMethod().AllowAnyHeader().AllowCredentials()
            );

            app.UseAuthorization();

            this.SetupMiddlewares(app);

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
            });


        }
        #region Private Methods

        /// <summary>
        /// Options used to configure behavior for types annotated with Microsoft.AspNetCore.Mvc.ApiControllerAttribute.
        /// </summary>
        /// <param name="services"></param>
        private void AddApiBehavioorOptions(IServiceCollection services)
        {
            services.Configure<ApiBehaviorOptions>(options =>
            {
                options.InvalidModelStateResponseFactory = actionContext =>
                {
                    var actionExecutingContext = actionContext as ActionExecutingContext;
                    var apiErrorRespose = new ApiErrorResponse
                    {
                        ErrorCode = (float)EnumErrorCode.BadRequest,
                        Errors = actionContext.ModelState.Keys.SelectMany(key => actionContext.ModelState[key].Errors.Select(x => key + ": " + x.ErrorMessage)).ToList(),
                        StatusCode = StatusCodes.Status400BadRequest
                    };

                    return new BadRequestObjectResult(apiErrorRespose);
                };
            });
        }
        #endregion



    }
}
