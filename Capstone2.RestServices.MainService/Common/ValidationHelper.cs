﻿using Capstone2.Shared.Models.Enum;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Hcp.Common
{
    public static class ValidationHelper
    {
        public static bool isValidEnum(string code ,Type enumType)
        {
            if (isValidNumber(code))
            {
                if (Enum.IsDefined(enumType, Convert.ToInt32(code))) return true;
            }
            return false;
        }
        private static bool isValidNumber(string x)
        {
            if (!string.IsNullOrWhiteSpace(x) && int.TryParse(x, out int result)) return true;
            return false;
        }
    }
}
