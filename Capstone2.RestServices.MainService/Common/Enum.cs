﻿namespace Capstone2.RestServices.Communication.Common
{
    public enum IdentifierType
    {
        Email_Pin = 24,
        Mobile_Pin = 25,
        Email_Password = 26,
        AD=27
    }
    public enum FactorType
    {
        Password=28,
        OTP=29
    }
    public enum Status
    {
        Active=30,
        Inactive=31,
        Deleted=32
    }
    public enum TemplateType
    {
        EmailPassword = 33,
        EmailRegistration = 34,
        SMSPassword = 35,
            
    }
    public enum UserStatus
    {
        Active = 20,
        InActive = 21,
        Archived = 22,
        AccountLocked = 23,
        AccountSuspended = 36

    }
    public enum EmailStatus
    {
        InQueue = 411,
        InProgress = 412,
        Sent = 413,
        Failed = 414
    }

}
