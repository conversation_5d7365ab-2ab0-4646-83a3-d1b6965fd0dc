﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Appointment.Common
{
    public class ActivityLogConstants
    {
        public const string APPOINTMENTSTATUSID = "Status";
        public const string USERDETAILSID = "Provider";
        public const string APPOINTMENTTYPESID = "Appointment Type";
        public const string DATEOFAPPOINTMENT = "Date Of Appointment";
        public const string ISREFERRALRECEIVED = "Referral Received";
        public const string STARTTIME = "Start Time";
        public const string ENDTIME = "End Time";
        public const string DURATION = "Duration";
        public const string ADMISSIONDATE = "Admission Date";
        public const string ADMISSIONTIME = "Admission Time";
    }
}
