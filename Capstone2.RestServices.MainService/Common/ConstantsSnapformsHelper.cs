﻿using System.Collections.Generic;
using System.Linq;

namespace Capstone2.RestServices.Snapforms.Common
{
    public static class ConstantsHelper
    {
        public const string SERVICE_ENDPOINT = "";
        public enum TypeofSnapformsAPI
        {
            AuthToken = 1,
            AllResponses = 2,
            SearchResponses = 3,
            SingleResponse = 4,
            Post_SingleResponse = 5,
            Put_SingleResponse = 6,
            Delete_SingleResponse = 7,
            ViewFormFields = 8,
            AllForms = 9,
            PDFUrl = 10, 
            FileUrl = 11,
            ViewLogs = 12
        }

        public static Dictionary<string, string> GetApiEndpoints(string apiEndPoints)
        {
            var endpointDict = apiEndPoints.Split(';').ToDictionary(e => e.Substring(0, e.IndexOf('=')), e => e.Substring(e.IndexOf('=') + 1));
            return endpointDict;
        }

        public static string GetEndpointByKey(string apiEndPoints, string key)
        {
            var endpoints = GetApiEndpoints(apiEndPoints);
            if (endpoints.TryGetValue(key, out string endpoint))
            {
                return endpoint;
            }
            return null; // or handle as needed
        }
    }
}
