﻿using Capstone2.RestServices.HIService.Models;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using System;
using System.Linq;
using System.Text;
using static Capstone2.RestServices.HIService.Common.ConstantsHelper;

namespace Capstone2.RestServices.HIService.Common
{
    public class ModelValidator
    {
        [JsonIgnore]
        public bool IsValid { get; set; } = true;
        [JsonIgnore]
        public StringBuilder ValidationError { get; set; } = new StringBuilder();

        public static dynamic ReturnValidationError(string errorMessage)
        {
            var apiResponse = new ApiResponse<dynamic>();
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            apiResponse.Result = null;
            apiResponse.Errors.Add(errorMessage);
            return apiResponse;
        }

        #region IHI service
        public void ValidateBaseProperty(string familyName = "", bool isFamilyName = false, DateTime? dateOfBirth = null, bool isDateOfBirth = false, string sexValue = "", bool isSexValue = false)
        {
            if (isFamilyName)
            {
                ValidateFamilyName(familyName);
            }

            if (isDateOfBirth)
            {
                ValidateDateOfBirth(dateOfBirth);
            }

            if (isSexValue)
            {
                ValidateSexValue(sexValue);
            }
        }

        private void ValidateFamilyName(string familyName)
        {
            if (string.IsNullOrWhiteSpace(familyName))
            {
                ValidationError.Append("FamilyName is required.");
                IsValid = false;
            }
            else if (familyName.Length > 40)
            {
                ValidationError.Append("FamilyName should be less than 40 characters.");
                IsValid = false;
            }
        }

        private void ValidateDateOfBirth(DateTime? dateOfBirth)
        {
            if (dateOfBirth == null)
            {
                ValidationError.Append("Date of birth is required.");
                IsValid = false;
            }
            else if (dateOfBirth.Value.Year < 1800)
            {
                ValidationError.Append("The birth year must not be less than 1800.");
                IsValid = false;
            }
            else if (dateOfBirth > DateTime.Now)
            {
                ValidationError.Append("The date of birth must not be in the future.");
                IsValid = false;
            }
        }

        private void ValidateSexValue(string sexValue)
        {
            if (string.IsNullOrWhiteSpace(sexValue))
            {
                ValidationError.Append("Sex value is required.");
                IsValid = false;
            }
            else if (sexValue.Length != 1)
            {
                ValidationError.Append("Sex value should be a single character.");
                IsValid = false;
            }
        }
        public void ValidateIHINumber(string propValue)
        {
            if (string.IsNullOrWhiteSpace(propValue))
            {
                ValidationError.Append("IHINumber is required.");
                IsValid = false;
            }
            else if (propValue.Length != 16)
            {
                ValidationError.Append("IHINumber must be 16 characters long.");
                IsValid = false;
            }
        }

        public void ValidateMedicareCardNumber(string propValue)
        {
            if (string.IsNullOrWhiteSpace(propValue))
            {
                ValidationError.Append("MedicareCardNumber is required.");
                IsValid = false;
            }
            else if (propValue.Length != 10 || !MedicareValidityCheck(propValue))
            {
                ValidationError.Append("MedicareCardNumber must be 10 characters long.");
                IsValid = false;
            }
        }
        public void ValidateDVAFileNumber(string propValue)
        {
            if (string.IsNullOrWhiteSpace(propValue))
            {
                ValidationError.Append("DVAFileNumber is required.");
                IsValid = false;
            }
            else if (propValue.Length < 2 && propValue.Length > 9)
            {
                ValidationError.Append("DVAFileNumber must be between 2 and 9 characters.");
                IsValid = false;
            }
        }

        private bool MedicareValidityCheck(string medicareNumber)
        {
            if (!(medicareNumber?.Length == 10) || !medicareNumber.All(char.IsDigit))
                return false;

            var medArray = medicareNumber.Select(c => (int)char.GetNumericValue(c)).ToArray();
            if (medArray[9] == 0)
            {
                return false;
            }
            int checkSum = medArray.Zip(new[] { 1, 3, 7, 9, 1, 3, 7, 9 }, (m, d) => m * d).Sum() % 10;
            if (checkSum == medArray[8])
            {
                return true;
            }
            return false;
        }

        public void ValidateAddress(AddressRequest address, TypeofHISearch _type)
        {
            if (address == null)
            {
                ValidationError.Append("Address is required.");
                IsValid = false;
            }

            switch (_type)
            {
                case TypeofHISearch.PostalAddress:
                    if (string.IsNullOrWhiteSpace(address.PostalDeliveryType) || string.IsNullOrWhiteSpace(address.Suburb) || string.IsNullOrWhiteSpace(address.State) || string.IsNullOrWhiteSpace(address.Postcode))
                    {
                        ValidationError.Append("PostalAddress is required.");
                        IsValid = false;
                    }
                    break;
                case TypeofHISearch.StreetAddress:
                case TypeofHISearch.HPIIStreetAddress:
                case TypeofHISearch.HPIDirectoryStreetAddress:
                case TypeofHISearch.HPIODirectoryStreetAddress:
                    if (string.IsNullOrWhiteSpace(address.StreetName) || (string.IsNullOrWhiteSpace(address.StreetNumber) && string.IsNullOrWhiteSpace(address.LotNumber)) || string.IsNullOrWhiteSpace(address.Suburb) || string.IsNullOrWhiteSpace(address.State) || string.IsNullOrWhiteSpace(address.Postcode))
                    {
                        ValidationError.Append("StreetAddress is required.");
                        IsValid = false;
                    }
                    break;
                case TypeofHISearch.UnstructuredStreetAddress:
                    if ((string.IsNullOrWhiteSpace(address.AddressLineOne) && string.IsNullOrWhiteSpace(address.AddressLineTwo)) || string.IsNullOrWhiteSpace(address.Suburb) || string.IsNullOrWhiteSpace(address.State) || string.IsNullOrWhiteSpace(address.Postcode))
                    {
                        ValidationError.Append("UnstructuredStreetAddress is required.");
                        IsValid = false;
                    }
                    break;
                default:
                    ValidationError.Append("Invalid search type value");
                    IsValid = false;
                    break;
            }
        }

        #endregion

        #region HPII service
        public void ValidateHPIINumber(string propValue)
        {
            if (string.IsNullOrWhiteSpace(propValue))
            {
                ValidationError.Append("HPIINumber is required.");
                IsValid = false;
            }
            else if (propValue.Length < 1)
            {
                ValidationError.Append("HPIINumber must be greater than 0 characters.");
                IsValid = false;
            }
        }

        public void ValidateRegistrationNumber(string propValue)
        {
            if (string.IsNullOrWhiteSpace(propValue))
            {
                ValidationError.Append("RegistrationNumber is required.");
                IsValid = false;
            }
            else if (propValue.Length < 1 && propValue.Length > 20)
            {
                ValidationError.Append("RegistrationNumber must be between 1 and 20 characters.");
                IsValid = false;
            }
        }

        public void ValidateHPIIProvider(string typeCode, string specialtyCode)
        {
            if (string.IsNullOrWhiteSpace(typeCode) || string.IsNullOrWhiteSpace(specialtyCode))
            {
                ValidationError.Append("Provider Type Code OR Provider Specialty could not be blank.");
                IsValid = false;
            }
        }

        #endregion

        #region HPIO service
        public void ValidateHPIONumber(string propValue)
        {
            if (string.IsNullOrWhiteSpace(propValue))
            {
                ValidationError.Append("HPIONumber is required.");
                IsValid = false;
            }
            else if (propValue.Length < 1)
            {
                ValidationError.Append("HPIONumber must be greater than 0 characters.");
                IsValid = false;
            }
        }
        public void ValidateODirectoryOrg(string name, string orgName, string serviceType = "Ignore")
        {
            if (string.IsNullOrWhiteSpace(name) || string.IsNullOrWhiteSpace(orgName))
            {
                ValidationError.Append("Organisation details is required.");
                IsValid = false;
            }
            else if (string.IsNullOrWhiteSpace(serviceType) && serviceType.Contains("Ignore"))
            {
                ValidationError.Append("Organisation details is required.");
                IsValid = false;
            }
        }

        #endregion

    }
}
