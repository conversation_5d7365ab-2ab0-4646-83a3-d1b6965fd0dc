﻿using System.Collections.Generic;

namespace Capstone2.RestServices.MainService.Common
{
    public class AppSettings
    {
       public Dictionary<string, string> ApiUrls { get; set; }

        //public Dictionary<string, string> ApiUrls { get; set; }
        //Authentication

        public string LoginPinExpiryInMins { get; set; }
        public string UserActivationTokenExpiresIn { get; set; }
        public string AesEcryptionKey { get; set; }
        public string AesIV { get; set; }
        public string Audience { get; set; }
        public string Issuer { get; set; }
        public int TokenExpiresIn { get; set; }
        public string RsaPublicKey { get; set; }
        public string RsaPrivateKey { get; set; }
        public string JwtKey { get; set; }
        public int AnonymousTokenExpiresIn { get; set; }
        public long InterserviceTokenExpiresIn { get; set; }
        public string JwtInterServiceKey { get; set; }
        public string ClientIdTokenExpiresIn { get; set; }
        public string PatinetRegistrationJwtKey { get; set; }

        public string Hub<PERSON><PERSON><PERSON>ey { get; set; }
        public string HubBaseUrl { get; set; }
        public int HubFileSasTokenExpiry { get; set; }

        //communication
        public string SMSAPIEndPoint { get; set; }
        public string SMSAPIUserName { get; set; }
        public string SMSAPIPassword { get; set; }
        public string SendGridApiKey { get; set; }
        public string SendGridFromEmailAddress { get; set; }
        public string CapsAPIUrl { get; set; }
        public string ClientId { get; set; }
        public string SystemAdminEmail { get; set; }
        public string SmsAdminEmail { get; set; }
        public string ClientSecret { get; set; }

        //File

        public string StorageAccountName { get; set; }
        public string StorageAccountKey { get; set; }
        public int SasTokenExpiryTime { get; set; }

        //HCp

        public string HCPTestFlag { get; set; }
        public string HCPVersion { get; set; }
        public string ICDVersion { get; set; }

        //Masster

        public string AddressFinderUrl { get; set; }
        public string AddressFinderKey { get; set; }
        public string AddressFinderAuth { get; set; }

        //Medical schedule
        public string EventGridKey { get; set; }
        public string EventGridEndpoint { get; set; }

        //Medicare
        public string MedicareBaseUrl { get; set; }
        public string MediCareClientAPIKey { get; set; }
        public string ProdaApplicationName { get; set; }

        //patient registration

        //public string Audience { get; set; }
        //public string Issuer { get; set; }
        public string PatientRegistrationTokenExpiresIn { get; set; }
        public string PatientRegPinExpiryInMins { get; set; }
        public string PRAdminEmail { get; set; }
    }
}
