﻿using static Capstone2.RestServices.HIService.Common.ConstantsHelper;
using System.ComponentModel.DataAnnotations;
using System;

namespace Capstone2.RestServices.HIService.Common
{
    [AttributeUsage(AttributeTargets.Property, AllowMultiple = false)]
    public class ConditionalRequiredAttribute : ValidationAttribute
    {
        private readonly string _typePropertyName;
        private readonly TypeofHISearch _requiredForType;

        public ConditionalRequiredAttribute(string typePropertyName, TypeofHISearch requiredForType)
        {
            _typePropertyName = typePropertyName;
            _requiredForType = requiredForType;
        }

        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            var typeProperty = validationContext.ObjectType.GetProperty(_typePropertyName);
            if (typeProperty == null)
            {
                return new ValidationResult($"Unknown property: {_typePropertyName}");
            }

            var typePropertyValue = typeProperty.GetValue(validationContext.ObjectInstance, null);
            if (typePropertyValue == null || !Enum.IsDefined(typeof(TypeofHISearch), typePropertyValue))
            {
                return new ValidationResult($"Invalid type value: {typePropertyValue}");
            }

            if ((TypeofHISearch)typePropertyValue == _requiredForType && value == null)
            {
                return new ValidationResult($"{validationContext.DisplayName} is required when type is {_requiredForType}");
            }

            return ValidationResult.Success;
        }
    }
}
