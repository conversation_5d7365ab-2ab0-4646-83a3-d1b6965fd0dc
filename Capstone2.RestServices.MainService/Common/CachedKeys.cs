﻿ using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Master.Common
{
    public static class CachedKeys
    {
        public const string Tbl_CountryMaster = "_Tbl_CountryMaster";
        public const string Tbl_StateMaster = "_Tbl_StateMaster";        
        public const string Tbl_LanguageMaster = "_Tbl_LanguageMaster";
        public const string Tbl_NationalityMaster = "_Tbl_NationalityMaster";        
        public const string Tbl_SystemConstant = "_Tbl_SystemConstant";
        public const string Tbl_DataFields = "_Tbl_DataFields";
        public const string Tbl_MedicareConstants = "_Tbl_MedicareConstants";
        public const string Tbl_TimeZoneMaster = "_Tbl_TimeZoneMaster";
        public const string Tbl_MedicareErrors = "_Tbl_MedicareErrors";
        public const string Tbl_ApiPermissionsAssocs = "_Tbl_ApiPermissionsAssocs";
        public const string Tbl_PresentingIllnessCodes = "_Tbl_PresentingIllnessCodes";
        public const string Tbl_MedicalContractorBand = "_Tbl_MedicalContractorBand";
        public const string Tbl_MedicalParticipants = "_Tbl_MedicalParticipants";


        public const string Tbl_MbsData = "_Tbl_MbsData";
        public const string Tbl_DvaData = "_Tbl_DvaData";
        public const string Tbl_ClinicalCategory = "_Tbl_ClinicalCategory";
        public const string Tbl_MbsClinicalCategoryData = "_Tbl_MbsClinicalCategoryData";
        public const string Tbl_TheatreBandingData = "_Tbl_TheatreBandingData";


        //utility
        public const string Tbl_RolesPermissionsAssoc_Org_RoleID_Module = "_Tbl_RolePerm_Org{0}_RoleID{1}_Module{2}";
        public const string Tbl_RolesPermissionsAssoc_Org_RoleID_AllModule = "_Tbl_RolesPermissionsAssoc_Org{0}_RoleID{1}_AllModule";
        public const string Tbl_RolesPermissionsAssoc_Org_RoleID = "_Tbl_RolePerm_Org{0}_RoleID{1}";
        //public const string Tbl_MedicalContractorBand = "_Tbl_MedicalContractorBand";
    }
}
