﻿using System;
using System.IO;
using System.Linq;
using System.Xml;
using System.Xml.Linq;
using System.Xml.Serialization;

namespace Capstone2.RestServices.HIService.Common
{
    public static class ConstantsHelper
    {
        public const string VENDOR_SERVICE_ENDPOINT = "https://www5.medicareaustralia.gov.au/cert/soap/services/";
        public const string SERVICE_MESSAGE_SOAPENV = "http://www.w3.org/2003/05/soap-envelope";
        public const string SERVICE_MESSAGE_XNS = "http://ns.electronichealth.net.au/hi/xsd/common/CommonCoreElements/3.0";
        public const string Status_Complete = "COMPLETE";
        public const string Status_Error = "ERROR";
        public const string Status_Success = "Success";
        public const string Status_Failed = "Failed";
        public const string HI_Identifier = "Identifier";
        public const string HI_Individual = "Individual";
        public const string HI_Organisation = "Organisation";
        public const string HI_Identifier_Batch_Submit_Async = "Identifier-Batch-Submit-Async";
        public const string HI_Identifier_Batch_Status = "Identifier-Batch-Status";
        public const string HI_Identifier_Batch_Retrieve = "Identifier-Batch-Retrieve";
        public const string HI_Identifier_Batch_Delete = "Identifier-Batch-Delete";
        public const string HI_Individual_Batch_Submit_Async = "Individual-Batch-Submit-Async";
        public const string HI_Individual_Batch_Retrieve = "Individual-Batch-Retrieve";
        public const string HI_Organisation_Batch_Submit_Async = "Organisation-Batch-Submit-Async";
        public const string HI_Organisation_Batch_Retrieve = "Organisation-Batch-Retrieve";

        public enum TypeofHIService
        {
            IHI = 1,
            HPII = 2,
            HPIO = 3
        }

        public enum TypeofHISearchV2
        {
            AustralianUnstructuredStreetAddressSearch,
            ConsumerMedicareSearch,
            ConsumerDVASearch,
            ConsumerIHIBasicSearch,
            ProviderIHIBasicSearch,
            OrganisationIHIDirectorySearch,
        }

        public enum TypeofHISearch
        {
            IHINumber,
            MedicareCardNumber,
            DVAFileNumber,
            PostalAddress,
            StreetAddress,
            UnstructuredStreetAddress,
            IHISubmitBatchAsync,

            HPIINumber,
            HPIRegistrationNumber,
            HPIIStreetAddress,
            HPIDirectory,
            HPIDirectoryByProvider,
            HPIDirectoryStreetAddress,

            HPIONumber,
            HPIODirectory,
            HPIODirectoryOrg,
            HPIODirectoryStreetAddress,

            // version 3.0

            AustralianUnstructuredStreetAddressSearch,
            ConsumerMedicareSearch,
            ConsumerDVASearch,
            ConsumerIHIBasicSearch,
            ProviderIHIBasicSearch,
            OrganisationIHIDirectorySearch,
        }
        public enum IHIRecordStatusType
        {
            Provisional,
            Unverified,
            Verified
        }

        public enum IHIStatusType
        {
            Active,
            Deceased,
            Expired,
            Resolved,
            Retired
        }
        public enum SexType
        {
            F,
            I,
            M,
            N
        }
        public enum SeverityType
        {
            Fatal,
            Error,
            Warning,
            Informational
        }

        public static string GenerateCorrelationId(string subscriberKey)
        {
            int noOfCharLength = 24;
            string dhs_correlationId = $"{subscriberKey}{DateTime.UtcNow.ToString("yyMMddHHmmssffff")}";
            if (!string.IsNullOrWhiteSpace(subscriberKey) && dhs_correlationId.Length > noOfCharLength)
                dhs_correlationId = dhs_correlationId.Substring(0, noOfCharLength);
            else if (dhs_correlationId.Length < noOfCharLength)
                dhs_correlationId = dhs_correlationId.PadRight(noOfCharLength, '0');
            return dhs_correlationId;
        }

        public static string RemoveEncodingDeclaration(string xml)
        {
            if (xml.StartsWith("<?xml"))
            {
                int end = xml.IndexOf("?>");
                if (end > 0)
                {
                    xml = xml.Substring(end + 2);
                }
            }
            return xml.Trim();
        }

        public static T DeserializeServiceMsgXml<T>(string inputXml)
        {
            string serviceMsgXML = GetServiceMessagesElement(inputXml);
            if (string.IsNullOrWhiteSpace(serviceMsgXML))
                return default(T);

            XmlSerializer serializer = new XmlSerializer(typeof(T), new XmlRootAttribute { ElementName = "serviceMessages", Namespace = SERVICE_MESSAGE_XNS });
            using (StringReader reader = new StringReader(serviceMsgXML))
            {
                using (XmlReader xmlReader = XmlReader.Create(reader, new XmlReaderSettings { IgnoreWhitespace = true }))
                {
                    return (T)serializer.Deserialize(xmlReader);
                }
            }
        }

        public static string GetServiceMessagesElement(string inputSoapXml)
        {
            XDocument doc = XDocument.Parse(inputSoapXml);
            XNamespace soapenv = SERVICE_MESSAGE_SOAPENV;

            XElement bodyElement = doc.Descendants(soapenv + "Body").FirstOrDefault();
            if (bodyElement != null)
            {
                XNamespace ce = SERVICE_MESSAGE_XNS;
                XElement serviceMessagesElement = bodyElement.Descendants(ce + "serviceMessages").FirstOrDefault();
                return Convert.ToString(serviceMessagesElement);
            }

            return string.Empty;
        }
    }
}
