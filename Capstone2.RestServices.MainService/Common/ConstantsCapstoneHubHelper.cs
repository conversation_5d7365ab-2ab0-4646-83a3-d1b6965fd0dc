﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.RestServices.CapstoneHub.Common
{
    public static class ConstantsCapstoneHubHelper
    {
        public const string SERVICE_ENDPOINT_PROVIDER_SEARCH = "/capstone/v1/mo/providers/search";
        public const string SERVICE_NAME_PROVIDER_SEARCH = "ProviderSearch";
        public const string SERVICE_ENDPOINT_SEND_REFERRAL = "/capstone/v1/referral/send";
        public const string SERVICE_NAME_SEND_REFERRAL = "SendReferral";
        public const string SERVICE_NAME_SETTINGS_STORE = "SettingsStore";
        public const string SERVICE_ENDPOINT_SETTINGS_STORE = "/capstone/v1/hub/settings/store";
        public const string SERVICE_ENDPOINT_FETCH_MESSAGE = "/capstone/v1/message/fetch";
        public const string SERVICE_NAME_FETCH_MESSAGE = "FetchMessage";


    }
}
