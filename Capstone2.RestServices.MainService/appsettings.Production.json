{
    "Logging": {
        "LogLevel": {
            "Default": "Information",
            "Microsoft": "Warning",
            "Microsoft.Hosting.Lifetime": "Information"
        },
        "ApplicationInsights": {
            "LogLevel": {
                "Default": "Information",
                "Microsoft": "Warning",
                "Microsoft.Hosting.Lifetime": "Information"
            }
        }
    },
  "ConnectionStrings": {
    "Capstone2ReadOnlyMasterDBConn": "Data Source=capstone2prodsql.database.windows.net,1433;Initial Catalog=Capstone2Master;Persist Security Info=True;User ID=<EMAIL>;Password=***************;Encrypt=True;TrustServerCertificate=True;Connection Timeout=3000;Column Encryption Setting=enabled;Authentication=Active Directory Password;",
    "AzureRedisConnection": "Capstone2ProdRedis.redis.cache.windows.net:6380,password=6AqK3UYguvytZ4utP54NpIFFN8decU185AzCaAiltAc=,ssl=True,abortConnect=False",
    "RedisCacheInstanceName": "ProdCache",
    "Capstone2MasterDBConn": "Data Source=capstone2prodsql.database.windows.net,1433;Initial Catalog=Capstone2Master;Persist Security Info=True;User ID=<EMAIL>;Password=***************;Encrypt=True;TrustServerCertificate=True;Connection Timeout=3000;Column Encryption Setting=enabled;Authentication=Active Directory Password;",
    "Capstone2UpdatableMasterDBConn": "Data Source=capstone2prodsql.database.windows.net,1433;Initial Catalog=Capstone2Master;Persist Security Info=True;User ID=<EMAIL>;Password=***************;Encrypt=True;TrustServerCertificate=True;Connection Timeout=3000;Column Encryption Setting=enabled;Authentication=Active Directory Password;",
    "AzurePubSubConnection": "Endpoint=https://capstone2prodpubsub.webpubsub.azure.com;AccessKey=4caqcmiefmza2AIZimrPRJ6m0R9v+D91c9lEWccx2C4=;Version=1.0;"
  },
  "ApplicationInsights": {
    "InstrumentationKey": "b69a8144-acf4-4bf6-a2be-9a65e6cd2bd7",
    "ConnectionString": "InstrumentationKey=b69a8144-acf4-4bf6-a2be-9a65e6cd2bd7;IngestionEndpoint=https://australiaeast-1.in.applicationinsights.azure.com/;LiveEndpoint=https://australiaeast.livediagnostics.monitor.azure.com/;ApplicationId=7db6a740-317c-4c53-bf1a-8d3e3302dc66"
  },
  "AzureAD": {
    "KVClientId": "a86a7d3e-8591-4cc0-be56-78c2dd268ab5",
    "KVClientSecret": "****************************************",
    "KVTenantId": "ef697159-b27e-4a23-bf9e-4ffb85045a6e",
    "KeyVaultName": "Capstone2ProdKV2",
    "KeyVaultUrl": "https://{0}.vault.azure.net",
    "JWTCertificateName": "JWTCertificatePrivateKey",
    "AzSBConnStringPatient": "Endpoint=sb://capstone2prodservicebus.servicebus.windows.net/;SharedAccessKeyName=listen-send;SharedAccessKey=2OG7MT5kfwLFdv3ESSwfOf5gS0mOkpisOO1p6yLLwms=",
    "ASBTopicPatient": "sbt-patient",
    "ASBSubNameActivityLog": "sub-activitylog",
    "AzSBConnStringAppointment": "Endpoint=sb://capstone2prodservicebus.servicebus.windows.net/;SharedAccessKeyName=listen-send;SharedAccessKey=v0TdpYOPXxcnx+4BF/30FPxazCZS1M6Gq+ASbHcKcuQ=",
    "ASBTopicAppointment": "sbt-appointment",
    "ASBSubNameAppointment": "sub-appointment",
    "ASBSubNameHCP": "sub-hcp",
    "JWTExternalCertName": "JWTExternalCertificatePrivateKey",
    "ASBTopicCapstoneHub": "#{ASBTopicCapstoneHub}#",
    "AzSBConnStringCapstoneHub": "#{AzSBConnStringCapstoneHub}#",
    "ASBSubNameReferrals": "#{ASBSubNameReferrals}#",
    "AzSBConnStringCompany": "Endpoint=sb://capstone2prodservicebus.servicebus.windows.net/;SharedAccessKeyName=listen-send;SharedAccessKey=ODWs322s+o/LD9NxzUblcLXdx1ePrBT22tVG+sPF/58=",
    "ASBTopicCompany": "sbt-company",
    "ASBSubNameCompanyEOD": "sub-companydetails",
    "HICertificateName": "HIServiceSignInCertificate",
    "AzSBConnStringMedicare": "Endpoint=sb://capstone2prodservicebus.servicebus.windows.net/;SharedAccessKeyName=listen-send;SharedAccessKey=zBC6tWe7P0TYofo3AamkbYX6AggthfExT2HbYTLhNNY=",
    "ASBTopicMedicare": "sbt-medicare",
    "ASBSubNameMedicare": "sub-medicare",
    "AzSBConnStringInvoice": "Endpoint=sb://capstone2prodservicebus.servicebus.windows.net/;SharedAccessKeyName=listen-send;SharedAccessKey=4BDdf5tkpT7l4z7eFRJ+ZGcieIJYg6P08S9jytEPfWw=",
    "ASBSubNameInvoice": "sub-invoicedetails",
    "ASBTopicInvoice": "sbt-invoice",
    "ASBSubNameInvoicePayment": "sub-invoicepayment",
    "AzSBConnStringPayment": "Endpoint=sb://capstone2prodservicebus.servicebus.windows.net/;SharedAccessKeyName=listen-send;SharedAccessKey=MnxXkPHC3xkzwo9eS0sf92lWSElk3wnZjqKaAuBgaa4=",
    "ASBTopicPayment": "sbt-paymentdetail",
    "ASBSubNameIMCWClaims": "sub-imcwclaim",
    "ASBSubNameClaimStatus": "sub-reclaimstatus",
    "ASBSubNameInvoiceSummary": "sub-invoicesummary",
    "ASBSubNameEODReport": "sub-eodreport",
    "ASBSubNameBulkUpdateEOD": "sub-bulkupdateeodreport",
    "ASBSubNameDepositReport": "sub-depositreport",
    "ASBSubNamePatientEOD": "sub-patientdetails",
    "ASBSubNamePatientEmail": "sub-patientemails",
    "ASBSubNamePatientSearch": "sub-patientsearch",
    "ASBSubNameEhealth": "sub-ehealth",
    "ASBSubNameAppointmentType": "sub-appointmenttype",
    "AzSBConnStringUser": "Endpoint=sb://capstone2prodservicebus.servicebus.windows.net/;SharedAccessKeyName=listen-send;SharedAccessKey=TA/frCDhipzbFbHkiDLKdtQf0mRyc/cq1Itf8OCeX+k=",
    "ASBSubNameUserEOD": "sub-userdetails",
    "ASBTopicUser": "sbt-user"
  },
  "AppSettings": {
    "JwtKey": "AewerwBsefCefweD",
    "Issuer": "CapstoneSystemProd",
    "Audience": "CapstoneFrontEndProd",
    "JwtInterServiceKey": "AewerwBsefCefweD",
    "InterserviceTokenExpiresIn": 31536000,
    "ApiUrls": {
      "FileServiceUrl": "https://csapi.capstonesystem.com.au",
      "AuthServiceUrl": "https://csapi.capstonesystem.com.au",
      "PatientServiceUrl": "https://csapi.capstonesystem.com.au",
      "SyncServiceUrl": "https://csapi.capstonesystem.com.au",
      "UtilityServiceUrl": "https://csapi.capstonesystem.com.au",
      "CommunicationServiceUrl": "https://csapi.capstonesystem.com.au",
      "AppointmentServiceUrl": "https://csapi.capstonesystem.com.au",
      "CompanyServiceUrl": "https://csapi.capstonesystem.com.au",
      "ResetPasswordUrl": "login/reset?t=",
      "PatinetRegistrationUrl": "public/patient/login?t=",
      "DomainUrl": "capstonesystem.com.au",
      "UserServiceUrl": "https://csapi.capstonesystem.com.au",
      "MedicareServiceUrl": "https://csapi.capstonesystem.com.au",
      "MedicalScheduleServiceUrl": "https://csapi.capstonesystem.com.au",
      "InvoicePaymentServiceUrl": "https://csapi.capstonesystem.com.au",
      "InvoiceServiceUrl": "https://csapi.capstonesystem.com.au",
      "ProdaServiceUrl": "https://csapi.capstonesystem.com.au",
      "SyncFusionServiceUrl": "https://csapi.capstonesystem.com.au",
      "MasterServiceUrl": "https://csapi.capstonesystem.com.au"
    },
    "LoginPinExpiryInMins": "5",
    "AesEcryptionKey": "#{AesEcryptionKey}#",
    "AesIV": "#{AesIV}#",
    "TokenExpiresIn": 86400,
    "ResetPasswordTokenExpiresIn": 1800,
    "UserActivationTokenExpiresIn": 86400,
    "AnonymousTokenExpiresIn": 2592000,
    "ClientIdTokenExpiresIn": 432000,
    "PatinetRegistrationJwtKey": "BewerwBsefCefweB", // Doesn't exist?
    "SystemAdminEmail": "<EMAIL>", // Turns out this user is needed for SMS Replies to work...
    "HubApiKey": "7aeb01cd415e4ee8a3a3f59be29f8600",
    "HubBaseUrl": "https://apim.capstonesystem.com.au", // Prod key points to ******************************** which doesn't seem correct?!
    "HubFileSasTokenExpiry": 36000,
    "SendGridApiKey": "*********************************************************************",
    "SMSAPIEndPoint": "https://app.wholesalesms.com.au/api/v2/send-sms.json",
    "SMSAPIUserName": "qv5u8qBLVLn5ABLeGGAl",
    "SMSAPIPassword": "1Odp7RSsc19AwiBLozrLDghUUECxy3",
    "CapsAPIUrl": "https://csapi.capstonesystem.com.au",
    "ClientId": "#{ClientId}#",
    "ClientSecret": "#{ClientSecret}#",
    "SmsAdminEmail": "<EMAIL>",
    "StorageAccountName": "capstone2prodstorageacc",
    "StorageAccountKey": "****************************************************************************************",
    "SasTokenExpiryTime": 15,
    "HCPTestFlag": "T",
    "HCPVersion": "1100",
    "ICDVersion": "1012",
    "CachedHICertificateKey": "HIServiceCertificateKey",
    "HIServiceSettingsV2": {
      "BaseApi": "********************************/capstone/v1/adha/hilookup",
      "XApiKey": "f15c1b9dc8624bc082e17b1ed760328b"
    },
    "HIServiceSettings": { // These dont exist in Prod?!
      "QualifierId": "WBD00000",
      "OSPlatform": "Windows 10 Pro",
      "ProductName": "Capstone",
      "ProductVersion": "v2.0",
      "HIBaseWebServiceUrl": "https://www5.medicareaustralia.gov.au/cert/soap/services/",
      "VendorQualifierValue": "http://ns.electronichealth.net.au/id/hi/vendorid/1.0",
      "UserQualifierValueUrl": "http://ns.{0}/id/{1}.{2}/userid/1.0",
      "UserQualifierValue": "capstonesystemuat.com.au",
      "HPIOQualifierValueUrl": "http://ns.{0}/id/hi/hpio/1.0",
      "HICertificateIssuedTo": "general.8003624900038636.id.electronichealth.net.au"
    },
    "AddressFinderUrl": "https://api.addressfinder.io/api/au/address",
    "AddressFinderKey": "BY9GKT83LMWHCRAQFUX7",
    "AddressFinderAuth": "R93D7LQAHJBTC6NKYUEG",
    "EventGridEndpoint": "https://capstone2prodmedicalscheduletopic.australiaeast-1.eventgrid.azure.net/api/events",
    "EventGridKey": "spbzP+NpKNKmx+bUulzboJA7cgqisfVbN+9uGOvxO0U=",
    "MedicareBaseUrl": "https://healthclaiming.api.humanservices.gov.au/claiming/ext-prd",
    "MediCareClientAPIKey": "33300bbfed52cef407d980611c04c596",
    "ProdaApplicationName": "Capstone.v2.0",
    "CachedMIMSAuthTokenKey": "MIMSAuthTokenKey", // Doesn't exist in Prod
    "MIMSApiSettings": {
      "BaseApiUrl": "https://api.mims.com",
      "Api_Key": "2d9b9918177e4b5d8cc3afcb2acfaafe",
      "Client_Id": "c3bb5c36-00f6-4db6-b010-74946ab406c9",
      "Client_Secret": "*************************************",
      "Grant_Type": "client_credentials"
    },
    "PatientRegistrationTokenExpiresIn": 24,
    "PatientRegPinExpiryInMins": 5,
    "PRAdminEmail": "<EMAIL>",
    "PRODASettings": {
      "ProdaSubjectType": "http://ns.humanservices.gov.au/audit/type/proda/device",
      "ProdaAuditType": "http://ns.humanservices.gov.au/audit/type/proda/organisation",
      "ProdaAccessTokenAudience": "https://proda.humanservices.gov.au",
      "ProdaDeviceActivationUrl": "https://5.rsp.humanservices.gov.au/piaweb/api/b2b/v1/devices/{0}/jwk",
      "ProdaDeviceKeyRefreshUrl": "https://5.rsp.humanservices.gov.au/piaweb/api/b2b/v1/orgs/{0}/devices/{1}/jwk",
      "ProdaAuthEndPoint": "https://proda.humanservices.gov.au/mga/sps/oauth/oauth20/token",
      "ProdaDeviceName": "Caps2Prod",
      "ProdaDeviceActivationCode": "ihAZAo3nTH",
      "ProdaOrganisationRA": "**********",
      "ProdaClientId": "WBD00003",
      "ProdaApplicationName": "Capstone.v2.0",
      "ProdaNotificationMailTo": "<EMAIL>",
      "MedicareTokenAudience": "https://medicareaustralia.gov.au/MCOL",
      "KVClientId": "a86a7d3e-8591-4cc0-be56-78c2dd268ab5",
      "KVClientSecret": "****************************************",
      "KVTenantId": "ef697159-b27e-4a23-bf9e-4ffb85045a6e",
      "ProdaKeyVaultName": "Capstone2ProdKV2",
      "KeyVaultUrl": "https://{0}.vault.azure.net",
      "KVCertificateName": "ProdaCertificatePrivateKey",
      "AuthContentType": "application/x-www-form-urlencoded",
      "GrantType": "urn:ietf:params:oauth:grant-type:jwt-bearer",
      "NotificationBeforeInDays": "10"
    },
    "CachedSnapformsAuthTokenKey": "SnapformsAuthTokenKey",
    "SnapformsAPISettings": {
      "BaseApiUrl": "https://user.snapforms.com.au",
      "APIEndPoints": "AuthToken=oauth/token;AllResponses=api/forms/{0}/responses?date_from&date_to&date_modified_from&date_modified_to&limit&offset;SearchResponses=api/forms/{0}/responses;Post_SingleResponse=api/forms/{0}/responses;Put_SingleResponse=api/forms/{0}/responses/{1};SingleResponse=api/forms/{0}/responses/{1};Delete_SingleResponse=api/forms/{0}/responses/{1};ViewFormFields=api/forms/{0}/fields;AllForms=api/forms;PDFUrl=api/forms/{0}/responses/{1}/temporary-pdf-url;FileUrl=api/forms/{0}/responses/{1}/temporary-file-url/{2};ViewLogs=api/logs?date_from={0}&date_to={1}&limit={2}&page={3}",
      "Grant_Type": "password"
    },
    "BlobStorageURL": "https://capstone2prodstorageacc.blob.core.windows.net/capstonereports/",
    "BoldReportKey": "Zg1t3gJiuZHzP+gz34ZM8FEM4LoPA///Qx1J6dlrQpY=",
    "SyncfusionLicenseKey": "Mgo+DSMBMAY9C3t2UVhhQlVFfV5AQmBIYVp/TGpJfl96cVxMZVVBJAtUQF1hTH5Xd0RjW3tXcHNXQ2JUWkZy"
  },
    "AllowedHosts": "*",
    "BasePathPrefix": ""
}
