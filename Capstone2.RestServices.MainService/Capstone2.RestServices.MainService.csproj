<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <UserSecretsId>0d26c4fa-98af-4624-93e4-907d26823b46</UserSecretsId>
    <NoWarn>$(NoWarn);NU1107</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Properties\PublishProfiles\**" />
    <Content Remove="Properties\PublishProfiles\**" />
    <EmbeddedResource Remove="Properties\PublishProfiles\**" />
    <None Remove="Properties\PublishProfiles\**" />
    <_WebToolingArtifacts Remove="Properties\PublishProfiles\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
    <PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.3.2" />
    <PackageReference Include="Azure.Messaging.EventGrid" Version="4.10.0" />
    <PackageReference Include="Azure.Messaging.WebPubSub" Version="1.0.0" />
    <PackageReference Include="Azure.Storage.Blobs" Version="12.14.1" />
    <PackageReference Include="Azure.Storage.Files.DataLake" Version="12.8.0" />
    <PackageReference Include="BoldReports.AspNet.Core" Version="4.2.87" />
    <PackageReference Include="BoldReports.Data.Csv" Version="4.2.87" />
    <PackageReference Include="BoldReports.Data.Excel" Version="4.2.87" />
    <PackageReference Include="BoldReports.Data.WebData" Version="4.2.87" />
    <PackageReference Include="BoldReports.Net.Core" Version="4.2.87" />
    <PackageReference Include="FluentValidation.AspNetCore" Version="10.3.6" />
    <PackageReference Include="IcgSoftware.RecurrenceRuleToText" Version="1.0.2" />
    <PackageReference Include="jose-jwt" Version="3.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="5.0.13" />
    <PackageReference Include="Microsoft.CodeAnalysis.CSharp" Version="4.8.0" />
    <PackageReference Include="Microsoft.CodeAnalysis.Common" Version="4.8.0" />
    <PackageReference Include="Microsoft.CodeAnalysis.VisualBasic" Version="4.8.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.1" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.1" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.1">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Nehta.VendorLibrary.Common" Version="4.3.0" />
    <PackageReference Include="Nehta.VendorLibrary.HI" Version="1.8.2" />
    <PackageReference Include="SendGrid" Version="9.28.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.2.3" />
    <PackageReference Include="Syncfusion.DocIORenderer.Net.Core" Version="24.2.9" />
    <PackageReference Include="Syncfusion.EJ2.AspNet.Core" Version="24.2.9" />
    <PackageReference Include="Syncfusion.EJ2.WordEditor.AspNet.Core" Version="24.2.9" />
    <PackageReference Include="Syncfusion.Pdf.Net.Core" Version="24.2.9" />
    <PackageReference Include="Syncfusion.XlsIO.Net.Core" Version="24.2.9" />
    <PackageReference Include="Syncfusion.XlsIORenderer.Net.Core" Version="24.2.9" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Capstone2.Framework.ApplicationInsights\Capstone2.Framework.ApplicationInsights.csproj">
      <GlobalPropertiesToRemove></GlobalPropertiesToRemove>
    </ProjectReference>
    <ProjectReference Include="..\Capstone2.Framework.RestApi\Capstone2.Framework.RestApi.csproj">
      <GlobalPropertiesToRemove></GlobalPropertiesToRemove>
    </ProjectReference>
    <ProjectReference Include="..\Capstone2.Shared.Network\Capstone2.Shared.Network.csproj">
      <GlobalPropertiesToRemove></GlobalPropertiesToRemove>
    </ProjectReference>
  </ItemGroup>

  <ItemGroup>
    <Content Update="appsettings.Development.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
  </ItemGroup>
</Project>
