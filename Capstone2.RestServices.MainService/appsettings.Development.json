{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}, "ApplicationInsights": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}}, "ConnectionStrings": {"Capstone2ReadOnlyMasterDBConn": "Data Source=sql-capstone-dev.database.windows.net,1433;Initial Catalog=db-capstone-master-dev;Persist Security Info=True;User id=<EMAIL>;Password=*************;Encrypt=True;TrustServerCertificate=True;Connection Timeout=3000;Column Encryption Setting=enabled;Authentication=Active Directory Password;", "AzureRedisConnection": "Capstone2-UAT-Redis.redis.cache.windows.net:6380,password=qEYs4hgE38vxd45JxT65YxkBpgX4z5FZpAzCaEJCns8=,ssl=True,abortConnect=False", "RedisCacheInstanceName": "UatCache", "Capstone2MasterDBConn": "Data Source=sql-capstone-dev.database.windows.net,1433;Initial Catalog=db-capstone-master-dev;Persist Security Info=True;User id=<EMAIL>;Password=*************;Encrypt=True;TrustServerCertificate=True;Connection Timeout=3000;Column Encryption Setting=enabled;Authentication=Active Directory Password;", "Capstone2UpdatableMasterDBConn": "Data Source=sql-capstone-dev.database.windows.net,1433;Initial Catalog=db-capstone-master-dev;Persist Security Info=True;User id=<EMAIL>;Password=*************;Encrypt=True;TrustServerCertificate=True;Connection Timeout=3000;Column Encryption Setting=enabled;Authentication=Active Directory Password;", "AzurePubSubConnection": "Endpoint=https://capstone2-uat-webpubsub.webpubsub.azure.com;AccessKey=nn6Z7Vo1Pg9I3ts3g1LBHGbX50eyFCjltubzUOB7pLY=;Version=1.0;"}, "ApplicationInsights": {"InstrumentationKey": "baf7ee2d-f1fb-4abe-a2c6-5752450c0003", "ConnectionString": "InstrumentationKey=baf7ee2d-f1fb-4abe-a2c6-5752450c0003;IngestionEndpoint=https://australiaeast-1.in.applicationinsights.azure.com/"}, "AzureAD": {"KVClientId": "ce183b31-8484-4e0e-aba7-61e4e61f795f", "KVClientSecret": "****************************************", "KVTenantId": "ef697159-b27e-4a23-bf9e-4ffb85045a6e", "KeyVaultName": "Capstone2-UAT-KV-01", "KeyVaultUrl": "https://{0}.vault.azure.net", "JWTCertificateName": "JWTCertificatePrivateKey", "AzSBConnStringPatient": "Endpoint=sb://capstone2uatservicebus.servicebus.windows.net/;SharedAccessKeyName=listen-send;SharedAccessKey=SD+QXgchfcEg7G16u4dIvS9IO7skRMEY0w9bpOugmlA=", "ASBTopicPatient": "sbt-patient", "ASBSubNameActivityLog": "sub-activitylog", "AzSBConnStringAppointment": "Endpoint=sb://capstone2uatservicebus.servicebus.windows.net/;SharedAccessKeyName=listen-send;SharedAccessKey=a50d3TIzTLzfEN3a/6yScKDsIbu19FZEt+ASbPYZ2NA=", "ASBTopicAppointment": "sbt-appointment", "ASBSubNameAppointment": "sub-appointment", "ASBSubNameHCP": "sub-hcp", "JWTExternalCertName": "JWTExternalCertificatePrivateKey", "ASBTopicCapstoneHub": "#{ASBTopicCapstoneHub}#", "AzSBConnStringCapstoneHub": "#{AzSBConnStringCapstoneHub}#", "ASBSubNameReferrals": "#{ASBSubNameReferrals}#", "AzSBConnStringCompany": "Endpoint=sb://capstone2uatservicebus.servicebus.windows.net/;SharedAccessKeyName=listen-send;SharedAccessKey=5Bbp0iG5xH3yNaIohj4xxQnDLIysrErwHklQFjF6KGk=", "ASBTopicCompany": "sbt-company", "ASBSubNameCompanyEOD": "sub-companydetails", "HICertificateName": "HIServiceSignInCertificate", "AzSBConnStringMedicare": "Endpoint=sb://capstone2uatservicebus.servicebus.windows.net/;SharedAccessKeyName=listen-send;SharedAccessKey=2c+T4I4wliUdbljbF1WCFEw12OLSGphheiQZe4+buhQ=", "ASBTopicMedicare": "sbt-medicare", "ASBSubNameMedicare": "sub-medicare", "AzSBConnStringInvoice": "Endpoint=sb://capstone2uatservicebus.servicebus.windows.net/;SharedAccessKeyName=listen-send;SharedAccessKey=hYnVQ9QyhFu6NmKDcd3C5m+PvkDkxD0o9sfFoKwimsE=", "ASBSubNameInvoice": "sub-invoicedetails", "ASBTopicInvoice": "sbt-invoice", "ASBSubNameInvoicePayment": "sub-invoicepayment", "AzSBConnStringPayment": "Endpoint=sb://capstone2uatservicebus.servicebus.windows.net/;SharedAccessKeyName=listen-send;SharedAccessKey=6qOpDqU53TPHKQvR96Ruodlu/orV1x3WijtLzx/tEqI=", "ASBTopicPayment": "sbt-paymentdetail", "ASBSubNameIMCWClaims": "sub-imcwclaim", "ASBSubNameClaimStatus": "sub-reclaimstatus", "ASBSubNameInvoiceSummary": "sub-invoicesummary", "ASBSubNameEODReport": "sub-eodreport", "ASBSubNameBulkUpdateEOD": "sub-bulkupdateeodreport", "ASBSubNameDepositReport": "sub-depositreport", "ASBSubNamePatientEOD": "sub-patientdetails", "ASBSubNamePatientEmail": "sub-patientemails", "ASBSubNamePatientSearch": "sub-patientsearch", "ASBSubNameEhealth": "sub-ehealth", "ASBSubNameAppointmentType": "sub-appointmenttype", "AzSBConnStringUser": "Endpoint=sb://capstone2uatservicebus.servicebus.windows.net/;SharedAccessKeyName=listen-send;SharedAccessKey=8adx9r0PnWgYeWOhpy+/DGt4AdaULRqvjAl+KQZfHcs=", "ASBSubNameUserEOD": "sub-userdetails", "ASBTopicUser": "sbt-user"}, "AppSettings": {"JwtKey": "AewerwBsefCefweD", "Issuer": "CapstoneSystemUat", "Audience": "CapstoneFrontEndUat", "JwtInterServiceKey": "AewerwBsefCefweD", "InterserviceTokenExpiresIn": 31536000, "ApiUrls": {"FileServiceUrl": "https://csapi.capstonesystemdev.com.au", "AuthServiceUrl": "https://csapi.capstonesystemdev.com.au", "PatientServiceUrl": "https://csapi.capstonesystemdev.com.au", "SyncServiceUrl": "https://csapi.capstonesystemdev.com.au", "UtilityServiceUrl": "https://csapi.capstonesystemdev.com.au", "CommunicationServiceUrl": "https://csapi.capstonesystemdev.com.au", "AppointmentServiceUrl": "https://csapi.capstonesystemdev.com.au", "CompanyServiceUrl": "https://csapi.capstonesystemdev.com.au", "ResetPasswordUrl": "login/reset?t=", "PatinetRegistrationUrl": "public/patient/login?t=", "DomainUrl": "capstonesystemdev.com.au", "UserServiceUrl": "https://csapi.capstonesystemdev.com.au", "MedicareServiceUrl": "https://csapi.capstonesystemdev.com.au", "MedicalScheduleServiceUrl": "https://csapi.capstonesystemdev.com.au", "InvoicePaymentServiceUrl": "https://csapi.capstonesystemdev.com.au", "InvoiceServiceUrl": "https://csapi.capstonesystemdev.com.au", "ProdaServiceUrl": "https://csapi.capstonesystemdev.com.au", "SyncFusionServiceUrl": "https://csapi.capstonesystemdev.com.au", "MasterServiceUrl": "https://csapi.capstonesystemdev.com.au"}, "LoginPinExpiryInMins": "5", "AesEcryptionKey": "#{AesEcryptionKey}#", "AesIV": "#{AesIV}#", "TokenExpiresIn": 86400, "ResetPasswordTokenExpiresIn": 1800, "UserActivationTokenExpiresIn": 86400, "AnonymousTokenExpiresIn": 2592000, "ClientIdTokenExpiresIn": 432000, "PatinetRegistrationJwtKey": "BewerwBsefCefweB", "SystemAdminEmail": "<EMAIL>", "HubApiKey": "f54d85e51785463fad7c30a1365b40bc", "HubBaseUrl": "https://apim.capstonesystem.com.au", "HubFileSasTokenExpiry": 36000, "SendGridApiKey": "*********************************************************************", "SMSAPIEndPoint": "https://app.wholesalesms.com.au/api/v2/send-sms.json", "SMSAPIUserName": "qv5u8qBLVLn5ABLeGGAl", "SMSAPIPassword": "1Odp7RSsc19AwiBLozrLDghUUECxy3", "CapsAPIUrl": "https://csapi.capstonesystemdev.com.au", "ClientId": "#{ClientId}#", "ClientSecret": "#{ClientSecret}#", "SmsAdminEmail": "<EMAIL>", "StorageAccountName": "capstone2uatstorageacc", "StorageAccountKey": "****************************************************************************************", "SasTokenExpiryTime": 15, "HCPTestFlag": "T", "HCPVersion": "1100", "ICDVersion": "1012", "CachedHICertificateKey": "HIServiceCertificateKey", "HIServiceSettingsV2": {"BaseApi": "********************************/capstone/v1/adha/hilookup", "XApiKey": "f15c1b9dc8624bc082e17b1ed760328b"}, "HIServiceSettings": {"QualifierId": "WBD00000", "OSPlatform": "Windows 10 Pro", "ProductName": "Capstone", "ProductVersion": "v2.0", "HIBaseWebServiceUrl": "https://www5.medicareaustralia.gov.au/cert/soap/services/", "VendorQualifierValue": "http://ns.electronichealth.net.au/id/hi/vendorid/1.0", "UserQualifierValueUrl": "http://ns.{0}/id/{1}.{2}/userid/1.0", "UserQualifierValue": "capstonesystemuat.com.au", "HPIOQualifierValueUrl": "http://ns.{0}/id/hi/hpio/1.0", "HICertificateIssuedTo": "general.****************.id.electronichealth.net.au"}, "AddressFinderUrl": "https://api.addressfinder.io/api/au/address", "AddressFinderKey": "3M9NCKQYRJFAGUW4L6BP", "AddressFinderAuth": "LM38BRWDVHGNQK4F69EC", "EventGridEndpoint": "https://medicalscheduletopicuat.australiaeast-1.eventgrid.azure.net/api/events", "EventGridKey": "f7tBMYNRV10fjDVOPLIMriMAYFMNYWIq+/qQWnNoQJw=", "MedicareBaseUrl": "https://test.healthclaiming.api.humanservices.gov.au/claiming/ext-vnd", "MediCareClientAPIKey": "5ca809e6fbbc519158d7f1803fcb45d3", "ProdaApplicationName": "Capstone.v2.0", "CachedMIMSAuthTokenKey": "MIMSAuthToken<PERSON>ey", "MIMSApiSettings": {"BaseApiUrl": "https://stagingapi.mims.com", "Api_Key": "d193048bc7a7462ba0dc7805bfb7f1f4", "Client_Id": "c3bb5c36-00f6-4db6-b010-74946ab406c9", "Client_Secret": "*************************************", "Grant_Type": "client_credentials"}, "PatientRegistrationTokenExpiresIn": 24, "PatientRegPinExpiryInMins": 5, "PRAdminEmail": "<EMAIL>", "PRODASettings": {"ProdaSubjectType": "http://ns.humanservices.gov.au/audit/type/proda/device", "ProdaAuditType": "http://ns.humanservices.gov.au/audit/type/proda/organisation", "ProdaAccessTokenAudience": "https://proda.humanservices.gov.au", "ProdaDeviceActivationUrl": "https://test.5.rsp.humanservices.gov.au/piaweb/api/b2b/v1/devices/{0}/jwk", "ProdaDeviceKeyRefreshUrl": "https://test.5.rsp.humanservices.gov.au/piaweb/api/b2b/v1/orgs/{0}/devices/{1}/jwk", "ProdaAuthEndPoint": "https://vnd.proda.humanservices.gov.au/mga/sps/oauth/oauth20/token", "ProdaDeviceName": "CapstoneDev01", "ProdaDeviceActivationCode": "pjV5Uy86vC", "ProdaOrganisationRA": "**********", "ProdaClientId": "soape-testing-client-v2", "ProdaApplicationName": "Capstone.v2.0", "ProdaNotificationMailTo": "<EMAIL>", "MedicareTokenAudience": "https://medicareaustralia.gov.au/MCOL", "KVClientId": "ce183b31-8484-4e0e-aba7-61e4e61f795f", "KVClientSecret": "****************************************", "KVTenantId": "ef697159-b27e-4a23-bf9e-4ffb85045a6e", "ProdaKeyVaultName": "Capstone2-UAT-KV-01", "KeyVaultUrl": "https://{0}.vault.azure.net", "KVCertificateName": "ProdaCertificatePrivateKey", "AuthContentType": "application/x-www-form-urlencoded", "GrantType": "urn:ietf:params:oauth:grant-type:jwt-bearer", "NotificationBeforeInDays": "10"}, "CachedSnapformsAuthTokenKey": "SnapformsAuthTokenKey", "SnapformsAPISettings": {"BaseApiUrl": "https://user.snapforms.com.au", "APIEndPoints": "AuthToken=oauth/token;AllResponses=api/forms/{0}/responses?date_from&date_to&date_modified_from&date_modified_to&limit&offset;SearchResponses=api/forms/{0}/responses;Post_SingleResponse=api/forms/{0}/responses;Put_SingleResponse=api/forms/{0}/responses/{1};SingleResponse=api/forms/{0}/responses/{1};Delete_SingleResponse=api/forms/{0}/responses/{1};ViewFormFields=api/forms/{0}/fields;AllForms=api/forms;PDFUrl=api/forms/{0}/responses/{1}/temporary-pdf-url;FileUrl=api/forms/{0}/responses/{1}/temporary-file-url/{2};ViewLogs=api/logs?date_from={0}&date_to={1}&limit={2}&page={3}", "Grant_Type": "password"}, "BlobStorageURL": "https://capstone2uatstorageacc.blob.core.windows.net/capstonereports/", "BoldReportKey": "Zg1t3gJiuZHzP+gz34ZM8FEM4LoPA///Qx1J6dlrQpY=", "SyncfusionLicenseKey": "Mgo+DSMBMAY9C3t2UVhhQlVFfV5AQmBIYVp/TGpJfl96cVxMZVVBJAtUQF1hTH5Xd0RjW3tXcHNXQ2JUWkZy"}, "AllowedHosts": "*", "BasePathPrefix": ""}