﻿using Capstone2.RestServices.Migration.Context;
using Capstone2.RestServices.Migration.Interfaces;
using Capstone2.RestServices.Migration.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Rest.Azure.OData;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Security.Cryptography;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Migration.Services
{
    public class UploaderDAL : IUploaderDAL
    {
        public readonly ReadOnlyMigrationDBContext _readOnlyDbContext;
        public readonly UpdatableMigrationDBContext _updatableDBContext;
        public UploaderDAL(ReadOnlyMigrationDBContext readOnlyDbContext, UpdatableMigrationDBContext updatableDBContext)
        {
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
        }

        /// <summary>
        /// Method to add migration data to Upload table
        /// </summary>
        /// <param name="inputUpload"></param>
        /// <returns></returns>
        public async Task<long> AddUploadAsync(Upload inputUpload)
        {
            await _updatableDBContext.Uploads.AddAsync(inputUpload);
            int rows = await _updatableDBContext.SaveChangesAsync();
            if (rows > 0)
                return inputUpload.Id;
            return 0;
        }

        /// <summary>
        /// Method to add validate the data in staging table
        /// </summary>
        /// <param name="inputUpload"></param>
        /// <returns></returns>
        public async Task UpdateUploadAsync(Upload inputUpload)
        {
            _updatableDBContext.Uploads.Update(inputUpload);
            await _updatableDBContext.SaveChangesAsync();
        }


        /// <summary>
        /// Method to retrieve list of letter templates 
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        public async Task<QueryResultList<UploadView>> ListUploadDAL(int orgId, QueryModel queryModel, GenieUploaderFilter filterModel)

        {
            var migrationQuery = from MU in _readOnlyDbContext.Uploads
                                 join UD in _readOnlyDbContext.UserDetails on MU.ModifiedBy equals UD.Id
                                where MU.OrgId == orgId
                                select new UploadView
                                {
                                    Id = MU.Id,
                                    OrgId = MU.OrgId,
                                    FileName = MU.FileName,
                                    FileDetailsId = MU.FileDetailsId,
                                    TemplateID = MU.TemplateId,
                                    GenieUploaderTypeId = MU.UploaderTypeId,
                                    GenieUploaderStatusId = MU.UploaderStatusId,
                                    ErrorDetails = MU.ErrorDetails,
                                    CreatedDate = MU.CreatedDate,
                                    ModifiedDate = MU.ModifiedDate,
                                    ModifiedBy = MU.ModifiedBy,
                                    Modifier = new UserDetails
                                    {
                                        Id = UD.Id,
                                        OrgId = UD.OrgId,
                                        FirstName = UD.FirstName,
                                        SurName = UD.SurName,
                                        TitleId = UD.TitleId
                                    },

                                };

            if (filterModel is not null)
            {
                if (filterModel.GenieUploaderTypeId is not null && filterModel.GenieUploaderTypeId.Any())
                {
                    migrationQuery = migrationQuery.Where(x => filterModel.GenieUploaderTypeId.Contains(x.GenieUploaderTypeId));
                }
            }



            if (!(string.IsNullOrWhiteSpace(queryModel.SortOrder)) && !(string.IsNullOrWhiteSpace(queryModel.SortTerm)))
            {
                migrationQuery = SortUpload(migrationQuery, queryModel.SortTerm, queryModel.SortOrder);
            }

            var paginatedList = await CreatePaginatedListAsync(migrationQuery, queryModel);
            QueryResultList<UploadView> queryList = new QueryResultList<UploadView>();
            if (paginatedList != null)
            {
                queryList.ItemRecords = paginatedList;
                queryList.CurrentCount = paginatedList.Count();
            }
            else
            {
                queryList.ItemRecords = null;
                queryList.CurrentCount = 0;
            }
            queryList.TotalCount = migrationQuery.Count();
            queryList.PageNumber = queryModel.PageNumber;
            queryList.PageSize = queryModel.PageSize;

            return queryList;
        }

        private async Task<List<UploadView>> CreatePaginatedListAsync(IQueryable<UploadView> migrationQuery, QueryModel queryModel)
        {
            if (queryModel.PageNumber > 0 && queryModel.PageSize > 0)
            {
                if (migrationQuery.Any())
                {
                    List<UploadView> paginatedList = await migrationQuery.Skip((queryModel.PageNumber - 1) * queryModel.PageSize)
                 .Take(queryModel.PageSize).ToListAsync();

                    return paginatedList;
                }
            }
            return null;
        }


        /// <summary>
        /// Method to retrieve list of upload data 
        /// </summary>
        /// <param name="uploaderTypeID"></param>
        /// <returns></returns>
        public async Task<string> ListUploadDataDAL(UploadInput input)
        {
            string output = string.Empty;
            if (input.UploaderTypeId == (short)GenieUploaderType.Appointment_Types)
            {

                var dataQuery = from UD in _readOnlyDbContext.AppointmentTypesImports
                                     where UD.UploadId == input.UploadID
                                select UD;

                List<AppointmentTypesImport> paginatedList = await dataQuery.Skip((input.PageNumber - 1) * input.PageSize)
                .Take(input.PageSize).ToListAsync();

                output = JsonConvert.SerializeObject(paginatedList, Formatting.None, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });

            }
            else if (input.UploaderTypeId == (short)GenieUploaderType.Patients)
            {

                var dataQuery = from UD in _readOnlyDbContext.PatientImports
                                where UD.UploadId == input.UploadID
                                select UD;
                List<PatientImport> paginatedList = await dataQuery.Skip((input.PageNumber - 1) * input.PageSize)
                .Take(input.PageSize).ToListAsync();

                output = JsonConvert.SerializeObject(paginatedList, Formatting.None, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });

            }
            else if (input.UploaderTypeId == (short)GenieUploaderType.PatientClinical)
            {

                var dataQuery = from UD in _readOnlyDbContext.PatientClinicalImports
                                where UD.UploadId == input.UploadID
                                select UD;
                List<PatientClinicalImport> paginatedList = await dataQuery.Skip((input.PageNumber - 1) * input.PageSize)
                .Take(input.PageSize).ToListAsync();

                output = JsonConvert.SerializeObject(paginatedList, Formatting.None, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            }
            else if (input.UploaderTypeId == (short)GenieUploaderType.Measurements)
            {

                var dataQuery = from UD in _readOnlyDbContext.MeasurementImports
                                where UD.UploadId == input.UploadID
                                select UD;
                List<MeasurementImport> paginatedList = await dataQuery.Skip((input.PageNumber - 1) * input.PageSize)
                .Take(input.PageSize).ToListAsync();

                output = JsonConvert.SerializeObject(paginatedList, Formatting.None, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });

            }
            else if (input.UploaderTypeId == (short)GenieUploaderType.Prescriptions)
            {

                var dataQuery = from UD in _readOnlyDbContext.PrescriptionImports
                                where UD.UploadId == input.UploadID
                                select UD;
                List<PrescriptionImport> paginatedList = await dataQuery.Skip((input.PageNumber - 1) * input.PageSize)
                .Take(input.PageSize).ToListAsync();

                output = JsonConvert.SerializeObject(paginatedList, Formatting.None, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });

            }
            else if (input.UploaderTypeId == (short)GenieUploaderType.PastHistory)
            {

                var dataQuery = from UD in _readOnlyDbContext.PastHistoryImports
                                where UD.UploadId == input.UploadID
                                select UD;
                List<PastHistoryImport> paginatedList = await dataQuery.Skip((input.PageNumber - 1) * input.PageSize)
                .Take(input.PageSize).ToListAsync();

                output = JsonConvert.SerializeObject(paginatedList, Formatting.None, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });

            }
            else if (input.UploaderTypeId == (short)GenieUploaderType.Allergies)
            {

                var dataQuery = from UD in _readOnlyDbContext.AllergyImports
                                where UD.UploadId == input.UploadID
                                select UD;
                List<AllergyImport> paginatedList = await dataQuery.Skip((input.PageNumber - 1) * input.PageSize)
                .Take(input.PageSize).ToListAsync();

                output = JsonConvert.SerializeObject(paginatedList, Formatting.None, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });

            }
            else if (input.UploaderTypeId == (short)GenieUploaderType.Consults)
            {

                var dataQuery = from UD in _readOnlyDbContext.ConsultsImports
                                where UD.UploadId == input.UploadID
                                select UD;
                List<ConsultsImport> paginatedList = await dataQuery.Skip((input.PageNumber - 1) * input.PageSize)
                .Take(input.PageSize).ToListAsync();

                output = JsonConvert.SerializeObject(paginatedList, Formatting.None, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            }
            else if (input.UploaderTypeId == (short)GenieUploaderType.Appointments)
            {

                var dataQuery = from UD in _readOnlyDbContext.AppointmentsImports
                                where UD.UploadId == input.UploadID
                                select UD;
                List<AppointmentsImport> paginatedList = await dataQuery.Skip((input.PageNumber - 1) * input.PageSize)
                .Take(input.PageSize).ToListAsync();

                output = JsonConvert.SerializeObject(paginatedList, Formatting.None, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            }
            else if (input.UploaderTypeId == (short)GenieUploaderType.AddressBook)
            {

                var dataQuery = from UD in _readOnlyDbContext.AddressBookImports
                                where UD.UploadId == input.UploadID
                                select UD;
                List<AddressBookImport> paginatedList = await dataQuery.Skip((input.PageNumber - 1) * input.PageSize)
                .Take(input.PageSize).ToListAsync();

                output = JsonConvert.SerializeObject(paginatedList, Formatting.None, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            }
            else if (input.UploaderTypeId == (short)GenieUploaderType.AccountHolders)
            {

                var dataQuery = from UD in _readOnlyDbContext.AccountHoldersImports
                                where UD.UploadId == input.UploadID
                                select UD;
                List<AccountHoldersImport> paginatedList = await dataQuery.Skip((input.PageNumber - 1) * input.PageSize)
                .Take(input.PageSize).ToListAsync();

                output = JsonConvert.SerializeObject(paginatedList, Formatting.None, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            }
            else if (input.UploaderTypeId == (short)GenieUploaderType.Employers)
            {

                var dataQuery = from UD in _readOnlyDbContext.EmployersImports
                                where UD.UploadId == input.UploadID
                                select UD;
                List<EmployersImport> paginatedList = await dataQuery.Skip((input.PageNumber - 1) * input.PageSize)
                .Take(input.PageSize).ToListAsync();

                output = JsonConvert.SerializeObject(paginatedList, Formatting.None, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            }
            else if (input.UploaderTypeId == (short)GenieUploaderType.Referrals)
            {

                var dataQuery = from UD in _readOnlyDbContext.ReferralsImports
                                where UD.UploadId == input.UploadID
                                select UD;
                List<ReferralsImport> paginatedList = await dataQuery.Skip((input.PageNumber - 1) * input.PageSize)
                .Take(input.PageSize).ToListAsync();

                output = JsonConvert.SerializeObject(paginatedList, Formatting.None, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            }



            return output;
        }

        private IQueryable<UploadView> SortUpload(IQueryable<UploadView> migrationQuery, string sortTerm, string sortOrder)
        {
            switch (sortTerm.ToLower())
            {
                case "id":
                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            migrationQuery = migrationQuery.OrderBy(x => x.Id);
                        }
                        else
                        {
                            migrationQuery = migrationQuery.OrderByDescending(x => x.Id);
                        }
                        break;
                    }
                case "ModifiedDate":
                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            migrationQuery = migrationQuery.OrderBy(x => x.ModifiedDate);
                        }
                        else
                        {
                            migrationQuery = migrationQuery.OrderByDescending(x => x.ModifiedDate);
                        }
                        break;
                    }
                default:
                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            migrationQuery = migrationQuery.OrderBy(x => x.ModifiedDate);
                        }
                        else
                        {
                            migrationQuery = migrationQuery.OrderByDescending(x => x.ModifiedDate);
                        }
                        break;
                    }
            }

            return migrationQuery;
        }

        public async Task<Upload> GetLatestUploadDAL(Upload inputUpload)
        {
            var migrationQuery = await (from UP in _readOnlyDbContext.Uploads
                                       where UP.UploaderTypeId == inputUpload.UploaderTypeId
                                       select UP).OrderByDescending(m => m.Id).FirstOrDefaultAsync();

            return migrationQuery;

        }

        public async Task<Upload> GetUploadDAL(Upload inputUpload)
        {
            var migrationQuery = await (from UP in _readOnlyDbContext.Uploads
                                        where UP.Id == inputUpload.Id
                                        select UP).OrderByDescending(m => m.Id).FirstOrDefaultAsync();

            return migrationQuery;

        }

        public async Task<long> GetTemplateID(Int16 genieUploaderTypeID)
        {
            var template = await (from UP in _readOnlyDbContext.Templates
                                  where UP.GenieUploaderTypeID == genieUploaderTypeID
                                  select UP).OrderByDescending(m => m.Id).FirstOrDefaultAsync();

            return template.Id;

        }

        public async Task<string> GetTemplateName(long templateID)
        {
            var template = await (from UP in _readOnlyDbContext.Templates
                                        where UP.Id == templateID
                                        select UP).OrderByDescending(m => m.Id).FirstOrDefaultAsync();

            return template.TemplateName;

        }

        public async Task<FileDetails> GetFileDetails(int orgId, long Id)
        {
            return await _readOnlyDbContext.FileDetails
                    .Where(p => p.Id == Id).FirstOrDefaultAsync();
        }

        public async Task<FileRepository> GetFileRepositoryById(long Id)
        {
            return await _readOnlyDbContext.FileRepository
                    .Where(p => p.Id == Id).FirstOrDefaultAsync();
        }
    }

}
