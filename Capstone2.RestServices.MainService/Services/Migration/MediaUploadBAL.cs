﻿using AutoMapper;
using Azure;
//using Azure.Messaging.EventGrid;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.Framework.RestApi.Utility;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.Migration.Controllers;
using Capstone2.RestServices.Migration.Interfaces;
using Capstone2.RestServices.Migration.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Migration.Services
{
    public class MediaUploadBAL : IMediaUploadBAL
    {

        private readonly ILogger<MediaUploadBAL> _logger;
        public readonly IMediaUploadDAL _mediaUploadDAL;
        public IMapper _mapper;
        public readonly AppSettings _appSettings;
        private readonly HttpClient client;
        private readonly IConfiguration _configuration;

        public MediaUploadBAL(ILogger<MediaUploadBAL> logger, IMediaUploadDAL mediaUploadDAL, IMapper mapper, IOptions<AppSettings> appSettings, IConfiguration configuration)
        {
            _logger = logger;
            _mediaUploadDAL = mediaUploadDAL;
            _mapper = mapper;
            _appSettings = appSettings.Value;
            this._configuration = configuration;
        }

        public async Task<ApiResponse<List<MediaUpload>>> AddMediaUploadBAL(List<MediaUpload> inputMediaUploads, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<List<MediaUpload>> apiResponse = new();

            long loggedInUser = baseHttpRequestContext.UserId;

            foreach (var mediaUpload in inputMediaUploads)
            {
                mediaUpload.ModifiedBy = loggedInUser;
                mediaUpload.CreatedDate = DateTime.UtcNow;
            }

            List<MediaUpload> insertedRows = await _mediaUploadDAL.AddMediaUploadAsync(inputMediaUploads);

            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Media Upload records saved successfully";
            apiResponse.Result = insertedRows;
            return apiResponse;
        }


        public async Task<ApiResponse<List<MediaUpload>>> UpdateMediaUploadBAL(List<MediaUpload> inputMediaUploads, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<List<MediaUpload>> apiResponse = new();

            long loggedInUser = baseHttpRequestContext.UserId;

            foreach (var mediaUpload in inputMediaUploads)
            {
                mediaUpload.ModifiedBy = loggedInUser;
                mediaUpload.ModifiedDate = DateTime.UtcNow;
            }

            List<MediaUpload> updatedRows = await _mediaUploadDAL.UpdateMediaUploadAsync(inputMediaUploads);

            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Media Upload records updated successfully";
            apiResponse.Result = updatedRows;
            return apiResponse;
        }

        public async Task<ApiResponse<long?>> DeleteMediaUploadBAL(List<long> inputMediaUploads, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<long?> apiResponse = new();
            long loggedInUser = baseHttpRequestContext.UserId;

            long rows = await _mediaUploadDAL.DeleteMediaUploadAsync(inputMediaUploads, loggedInUser);

            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Media Upload records deleted successfully";
            apiResponse.Result = rows;
            return apiResponse;
        }


        /// <summary>
        /// Method to generate a paginated list of Migration Media Uploads based on search criteria
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        public async Task<ApiResponse<QueryResultList<MediaUploadView>>> ListMediaUploadBAL(BaseHttpRequestContext baseHttpRequestContext, QueryModel queryModel)
        {

            ApiResponse<QueryResultList<MediaUploadView>> apiResponse = new();
            MediaUploadFilterModel filterModel = PrepareFilterParameters(queryModel.Filter);

            var queryList = await _mediaUploadDAL.ListMediaUploadDAL(queryModel, filterModel);
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result = queryList;
            return apiResponse;
        }

        /// <summary>
        /// Method to fetch a media upload based on Id
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<MediaUpload>> GetMediaUploadDetails(long id)
        {
            ApiResponse<MediaUpload> apiResposne = new();
            MediaUpload mediaUpload = await _mediaUploadDAL.GetMediaUploadDetails(id);
            
            apiResposne.Result = mediaUpload;
            apiResposne.StatusCode = StatusCodes.Status200OK;
            apiResposne.Message = "Success";
            return apiResposne;
        }


        /// <summary>
        /// Method to update the meta data of the uploaded media
        /// </summary>
        /// <returns></returns>
        public async Task<ApiResponse<bool>> UpdateMetaDataAsync()
        {
            ApiResponse<bool> apiResposne = new();
            bool result = await _mediaUploadDAL.UpdateMetaDataAsync();

            apiResposne.Result = result;
            apiResposne.StatusCode = StatusCodes.Status200OK;
            apiResposne.Message = "Success";
            return apiResposne;
        }

        /// <summary>
        /// method to generate the filter parameters from the request
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        private MediaUploadFilterModel PrepareFilterParameters(string filter)
        {
            if (!(string.IsNullOrEmpty(filter)))
                return JsonConvert.DeserializeObject<MediaUploadFilterModel>(filter);
            else
                return null;
        }

    }
}
