﻿using AutoMapper;
using Azure;
//using Azure.Messaging.EventGrid;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.Framework.RestApi.Utility;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.Migration.Controllers;
using Capstone2.RestServices.Migration.Interfaces;
using Capstone2.RestServices.Migration.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Migration.Services
{
    public class UploaderBAL : IUploaderBAL
    {
        private readonly ILogger<UploaderBAL> _logger;
        public readonly IUploaderDAL _uploaderDAL;
        public IMapper _mapper;
        public readonly AppSettings _appSettings;
        //public readonly IMbsDAL _mbsDAL;
        private readonly HttpClient client;
        private readonly IConfiguration _configuration;
        private IASBMessageSenderHelper _asbMessageSenderHelper;


        //public UploaderBAL(IUploaderDAL uploaderDAL, IMapper mapper, IOptions<AppSettings> appSettings, IMbsDAL mbsDAL)
        public UploaderBAL(ILogger<UploaderBAL> logger, IUploaderDAL uploaderDAL, IMapper mapper, IOptions<AppSettings> appSettings, IConfiguration configuration, IASBMessageSenderHelper asbMessageSenderHelper)
        {
            _logger = logger;
            _uploaderDAL = uploaderDAL;
            _mapper = mapper;
            _appSettings = appSettings.Value;
            this._configuration = configuration;
            this._asbMessageSenderHelper = asbMessageSenderHelper;
            //_mbsDAL = mbsDAL;
        }

        /// <summary>
        /// Method to save a entry in Upload
        /// </summary>
        /// <param name="inputUpload"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> AddUploadBAL(Upload inputUpload, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<long?> apiResponse = new();

            long templateID = await _uploaderDAL.GetTemplateID(inputUpload.UploaderTypeId);
            int orgId = baseHttpRequestContext.OrgId;
            long loggedInUser = baseHttpRequestContext.UserId;
            inputUpload.ModifiedBy = loggedInUser;
            inputUpload.OrgId = orgId;
            inputUpload.UploaderStatusId = (short)GenieUploaderStatus.Uploading;
            inputUpload.ModifiedDate = DateTime.UtcNow;
            inputUpload.TemplateId = templateID;

            // check the status of previous upload of same uploadertype
            var latestUpload =await _uploaderDAL.GetLatestUploadDAL(inputUpload);
            if (latestUpload != null && latestUpload != null && latestUpload.UploaderStatusId == (short)GenieUploaderStatus.Uploading)
            {

                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Previous Upload of the Type " + (GenieUploaderType)inputUpload.UploaderTypeId + " Still In Progress.So Next File can not be uploaded";
                apiResponse.Result = null;
                return apiResponse;
            }

            long id = await _uploaderDAL.AddUploadAsync(inputUpload);

            inputUpload.Id = id;

            if (id > 0)
            {
                await StoreUploadToStagingMessage(inputUpload, baseHttpRequestContext.OrgCode);

                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Message successfully added to service bus queue";
                apiResponse.Result = id;
                return apiResponse;

            }      
            else
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failure";
                apiResponse.Result = null;
                return apiResponse;
            }
        }

        /// <summary>
        /// Method to save a entry in Upload
        /// </summary>
        /// <param name="inputUpload"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> ValidateDataBAL(Upload inputUpload, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<long?> apiResponse = new();

            int orgId = baseHttpRequestContext.OrgId;
            long loggedInUser = baseHttpRequestContext.UserId;
            
            var uploadRecord = await _uploaderDAL.GetUploadDAL(inputUpload);

            uploadRecord.ModifiedBy = loggedInUser;
            uploadRecord.OrgId = orgId;
            uploadRecord.UploaderStatusId = (short)GenieUploaderStatus.Validating;
            uploadRecord.ModifiedDate = DateTime.UtcNow;

            await _uploaderDAL.UpdateUploadAsync(uploadRecord);

            await StoreValidateStagingDataMessage(uploadRecord, baseHttpRequestContext.OrgCode);

            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Data validation is initiated, it may take some time to complete";
            return apiResponse;
        }


        /// <summary>
        /// Method to save a entry in Upload
        /// </summary>
        /// <param name="inputUpload"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> MigrateDataBAL(Upload inputUpload, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<long?> apiResponse = new();

            int orgId = baseHttpRequestContext.OrgId;
            long loggedInUser = baseHttpRequestContext.UserId;

            var uploadRecord = await _uploaderDAL.GetUploadDAL(inputUpload);

            uploadRecord.ModifiedBy = loggedInUser;
            uploadRecord.OrgId = orgId;
            uploadRecord.UploaderStatusId = (short)GenieUploaderStatus.Migrating;
            uploadRecord.ModifiedDate = DateTime.UtcNow;

            await _uploaderDAL.UpdateUploadAsync(uploadRecord);

            await StoreMigrateStagingDataMessage(uploadRecord, baseHttpRequestContext.OrgCode);

            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Data migration is initiated, it may take some time to complete";
            return apiResponse;
        }

        /// <summary>
        /// Method to generate a paginated list of Medicare Uploads based on search criteria
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        public async Task<ApiResponse<QueryResultList<UploadView>>> ListUploadBAL(BaseHttpRequestContext baseHttpRequestContext, QueryModel queryModel)
        {
            ApiResponse<QueryResultList<UploadView>> apiResponse = new();
            int orgId = baseHttpRequestContext.OrgId;
            long loggedInUser = baseHttpRequestContext.UserId;

            GenieUploaderFilter filterModel = PrepareFilterParameters(queryModel.Filter);

            var queryList = await _uploaderDAL.ListUploadDAL(orgId, queryModel, filterModel);
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result = queryList;
            return apiResponse;
        }


        /// <summary>
        /// Method to generate a paginated list of Medicare Uploads based on search criteria
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        public async Task<ApiResponse<string>> ListUploadDataBAL(BaseHttpRequestContext baseHttpRequestContext, UploadInput inputMediaUpload)
        {
            ApiResponse<string> apiResponse = new();
            string dataList = await _uploaderDAL.ListUploadDataDAL(inputMediaUpload);
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result = dataList;
            return apiResponse;
        }

        private GenieUploaderFilter PrepareFilterParameters(string filter)
        {
            if (!(String.IsNullOrEmpty(filter)))
            {
                return JsonConvert.DeserializeObject<GenieUploaderFilter>(filter);
            }
            else
            {
                return null;
            }
        }

        private async Task StoreUploadToStagingMessage(Upload input, string orgCode)
        {
            var fileFromDB = await _uploaderDAL.GetFileDetails(input.OrgId, (long)input.FileDetailsId);
            var RepoDetails = await _uploaderDAL.GetFileRepositoryById(fileFromDB.FileRepositoryId);
            
            //Get the Url of the file 
            var foldername = ((FileModuleType)RepoDetails.FileModuleTypeId).ToString().ToLower();
            string fileUrl = RepoDetails.Path + "/" + orgCode + "/" + foldername + "/" + fileFromDB.FileName;
            
            MessageInput messageInput = new()
            {
                UploaderTypeId = input.UploaderTypeId,
                UploadId = input.Id,
                TemplateName = await _uploaderDAL.GetTemplateName(input.TemplateId),
                OrgCode = orgCode,
                ModifiedBy = input.ModifiedBy.ToString(),
                Url = fileUrl
            };

            Dictionary<string, string> userPros = new()
            {
                { "RequestType","GenieMigration"},
                { "to",_configuration["AzureAD:ASBSubNameGenie"]}
            };

            byte[] jsonContentByteArr = Encoding.GetEncoding("iso-8859-1").GetBytes(JsonConvert.SerializeObject(messageInput));
            await _asbMessageSenderHelper.MessageSenderTopic(jsonContentByteArr, userPros, _configuration["AzureAD:AzSBConnStringMigration"], _configuration["AzureAD:ASBTopicMigration"]);
        }

        private async Task StoreValidateStagingDataMessage(Upload input, string orgCode)
        {   
            MessageInput messageInput = new()
            {
                UploadId = input.Id,
                TemplateName = await _uploaderDAL.GetTemplateName(input.TemplateId),
                OrgCode = orgCode,
                ModifiedBy = input.ModifiedBy.ToString()
            };

            Dictionary<string, string> userPros = new()
            {
                { "RequestType","GenieMigration"},
                { "to",_configuration["AzureAD:ASBSubNameGenieValidation"]}
            };

            var a = _configuration["AzureAD:ASBSubNameGenieValidation"];
            var b = _configuration["AzureAD:AzSBConnStringMigration"];
            var c = _configuration["AzureAD:ASBTopicMigration"];

            _logger.LogInformation($"ASBSubNameGenieValidation : {a}");
            _logger.LogInformation($"AzSBConnStringMigration : {b}");
            _logger.LogInformation($"ASBTopicMigration : {c}");
            _logger.LogInformation($"Custom object : {JsonConvert.SerializeObject(messageInput)}");
            _logger.LogInformation($"userPros : {JsonConvert.SerializeObject(userPros)}");


            byte[] jsonContentByteArr = Encoding.GetEncoding("iso-8859-1").GetBytes(JsonConvert.SerializeObject(messageInput));
            await _asbMessageSenderHelper.MessageSenderTopic(jsonContentByteArr, userPros, _configuration["AzureAD:AzSBConnStringMigration"], _configuration["AzureAD:ASBTopicMigration"]);
        }

        private async Task StoreMigrateStagingDataMessage(Upload input, string orgCode)
        {
            MessageInput messageInput = new()
            {
                UploadId = input.Id,
                TemplateName = await _uploaderDAL.GetTemplateName(input.TemplateId),
                OrgCode = orgCode,
                ModifiedBy = input.ModifiedBy.ToString()
            };

            Dictionary<string, string> userPros = new()
            {
                { "RequestType","GenieMigration"},
                { "to",_configuration["AzureAD:ASBSubNameGenieMigration"]}
            };

            var a = _configuration["AzureAD:ASBSubNameGenieMigration"];
            var b = _configuration["AzureAD:AzSBConnStringMigration"];
            var c = _configuration["AzureAD:ASBTopicMigration"];

            _logger.LogInformation($"ASBSubNameGenieMigration : {a}");
            _logger.LogInformation($"AzSBConnStringMigration : {b}");
            _logger.LogInformation($"ASBTopicMigration : {c}");
            _logger.LogInformation($"Custom object : {JsonConvert.SerializeObject(messageInput)}");
            _logger.LogInformation($"userPros : {JsonConvert.SerializeObject(userPros)}");

            
            byte[] jsonContentByteArr = Encoding.GetEncoding("iso-8859-1").GetBytes(JsonConvert.SerializeObject(messageInput));
            await _asbMessageSenderHelper.MessageSenderTopic(jsonContentByteArr, userPros, _configuration["AzureAD:AzSBConnStringMigration"], _configuration["AzureAD:ASBTopicMigration"]);
        }

    }
}
