﻿using Capstone2.RestServices.Migration.Context;
using Capstone2.RestServices.Migration.Interfaces;
using Capstone2.RestServices.Migration.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Rest.Azure.OData;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Security.Cryptography;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Migration.Services
{
    public class MediaUploadDAL : IMediaUploadDAL
    {
        public readonly ReadOnlyMigrationDBContext _readOnlyDbContext;
        public readonly UpdatableMigrationDBContext _updatableDBContext;
        public MediaUploadDAL(ReadOnlyMigrationDBContext readOnlyDbContext, UpdatableMigrationDBContext updatableDBContext)
        {
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
        }

        public async Task<List<MediaUpload>> AddMediaUploadAsync(List<MediaUpload> inputMediaUploads)
        {
            await _updatableDBContext.MediaUploads.AddRangeAsync(inputMediaUploads);
            int rows = await _updatableDBContext.SaveChangesAsync();
            
            return inputMediaUploads;
        }

        public async Task<List<MediaUpload>> UpdateMediaUploadAsync(List<MediaUpload> inputMediaUploads)
        {
            foreach (var mediaUpload in inputMediaUploads)
            {
                _updatableDBContext.Entry(mediaUpload).State = EntityState.Modified;
            }

            int rows = await _updatableDBContext.SaveChangesAsync();

            return inputMediaUploads;
        }

        public async Task<long> DeleteMediaUploadAsync(List<long> inputMediaUploads, long userId)
        {
            // Retrieve all media uploads to be marked as deleted
            var mediaUploadsToDelete = await _updatableDBContext.MediaUploads
                .Where(mu => inputMediaUploads.Contains(mu.Id))
                .ToListAsync();

            foreach (var mediaUpload in mediaUploadsToDelete)
            {
                mediaUpload.ModifiedBy = userId;
                mediaUpload.ModifiedDate = DateTime.UtcNow;
                mediaUpload.InactiveInd = true;
                _updatableDBContext.Entry(mediaUpload).State = EntityState.Modified;
            }

            int rows = await _updatableDBContext.SaveChangesAsync();

            return rows;
        }

        /// <summary>
        /// Method to retrieve list of letter templates 
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        public async Task<QueryResultList<MediaUploadView>> ListMediaUploadDAL(QueryModel queryModel, MediaUploadFilterModel filterModel)
        {
            var allRecordsQuery = from MU in _readOnlyDbContext.MediaUploads
                                  join UD in _readOnlyDbContext.UserDetails on MU.ModifiedBy equals UD.Id
                                  where MU.InactiveInd == false
                                  select new MediaUploadView
                                  {
                                     Id = MU.Id,
                                     OriginalFilePath = MU.OriginalFilePath,
                                     OriginalFileName = MU.OriginalFileName,
                                     PatientDetailsId = MU.PatientDetailsId,
                                     PatientRecordId = MU.PatientRecordId,
                                     UploadFileName = MU.UploadFileName,
                                     FileDetailsId = MU.FileDetailsId,
                                     StatusId = MU.StatusId,
                                     Status = EnumExtensions.GetDescription((UploaderStatus)MU.StatusId),
                                     StatusMessage = MU.StatusMessage,
                                     StateInfo = MU.StateInfo,
                                     CreatedDate = MU.CreatedDate,
                                     ModifiedDate = MU.ModifiedDate,
                                     ModifiedBy = MU.ModifiedBy,                                     
                                     Modifier = new UserDetails
                                     {
                                          Id = UD.Id,
                                          OrgId = UD.OrgId,
                                          FirstName = UD.FirstName,
                                          SurName = UD.SurName,
                                          TitleId = UD.TitleId
                                     },
                                  };

            if (filterModel is not null)
            {
                if (filterModel.Status is not null && filterModel.Status.Any())
                {
                    allRecordsQuery = allRecordsQuery.Where(x => filterModel.Status.Contains(x.StatusId));
                }

                if (filterModel.OriginalFilePath is not null && filterModel.OriginalFilePath.Any())
                {
                    allRecordsQuery = allRecordsQuery.Where(x => filterModel.OriginalFilePath.Contains(x.OriginalFilePath));
                }

                if (filterModel.PatientID is not null && filterModel.PatientID.Any())
                {
                    allRecordsQuery = allRecordsQuery.Where(x => filterModel.PatientID.Contains(x.PatientRecordId));
                }
            }

            if (!(string.IsNullOrWhiteSpace(queryModel.SortOrder)) && !(string.IsNullOrWhiteSpace(queryModel.SortTerm)))
            {
                allRecordsQuery = SortMediaUploads(allRecordsQuery, queryModel.SortTerm, queryModel.SortOrder);
            }

            var paginatedList = await CreatePaginatedListAsync(allRecordsQuery, queryModel);
            QueryResultList<MediaUploadView> queryList = new QueryResultList<MediaUploadView>();
            if (paginatedList != null)
            {
                queryList.ItemRecords = paginatedList;
                queryList.CurrentCount = paginatedList.Count();
            }
            else
            {
                queryList.ItemRecords = null;
                queryList.CurrentCount = 0;
            }
            queryList.TotalCount = allRecordsQuery.Count();
            queryList.PageNumber = queryModel.PageNumber;
            queryList.PageSize = queryModel.PageSize;

            return queryList;
        }

        /// <summary>
        /// Method to get a patent from database based on id
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<MediaUpload> GetMediaUploadDetails(long id)
        {
            var mediaUploadDetail = await _readOnlyDbContext.MediaUploads.AsNoTracking()
                                        .FirstOrDefaultAsync(p => p.Id == id && p.InactiveInd == false);
            return mediaUploadDetail;
        }


        /// <summary>
        /// Method to update the meta data of the uploaded media
        /// </summary>
        /// <returns></returns>
        public async Task<bool> UpdateMetaDataAsync()
        {
            var result = await _readOnlyDbContext.Database.ExecuteSqlRawAsync("EXEC [Migration].[UpdateGraphicsMetaData]");

            // Check if the stored procedure executed successfully
            return result >= 0;
        }

        private async Task<List<MediaUploadView>> CreatePaginatedListAsync(IQueryable<MediaUploadView> userQuery, QueryModel queryModel)
        {
            if (queryModel.PageNumber > 0 && queryModel.PageSize > 0)
            {
                if (userQuery.Any())
                {
                    List<MediaUploadView> paginatedList = await userQuery.Skip((queryModel.PageNumber - 1) * queryModel.PageSize)
                    .Take(queryModel.PageSize).ToListAsync();
                    return paginatedList;
                }
            }
            return null;
        }


        /// <summary>
        /// Method to add orderby clause to query to fetch users
        /// </summary>
        /// <param name="userQuery"></param>
        /// <param name="sortTerm"></param>
        /// <param name="sortOrder"></param>
        /// <returns></returns>
        private IQueryable<MediaUploadView> SortMediaUploads(IQueryable<MediaUploadView> userQuery, string sortTerm, string sortOrder)
        {
            switch (sortTerm.ToLower())
            {
                case "id":
                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            userQuery = userQuery.OrderBy(x => x.Id);
                        }
                        else
                        {
                            userQuery = userQuery.OrderByDescending(x => x.Id);
                        }
                        break;
                    }
                case "originalpath":
                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            userQuery = userQuery.OrderBy(x => x.OriginalFilePath);
                        }
                        else
                        {
                            userQuery = userQuery.OrderByDescending(x => x.OriginalFilePath);
                        }
                        break;
                    }
                case "originalfilename":
                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            userQuery = userQuery.OrderBy(x => x.OriginalFileName);
                        }
                        else
                        {
                            userQuery = userQuery.OrderByDescending(x => x.OriginalFileName);
                        }
                        break;
                    }
                case "uploadfilename":
                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            userQuery = userQuery.OrderBy(x => x.UploadFileName);
                        }
                        else
                        {
                            userQuery = userQuery.OrderByDescending(x => x.UploadFileName);
                        }
                        break;
                    }
                case "patiendid":
                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            userQuery = userQuery.OrderBy(x => x.PatientRecordId);
                        }
                        else
                        {
                            userQuery = userQuery.OrderByDescending(x => x.PatientRecordId);
                        }
                        break;
                    }
            }

            return userQuery;
        }


    }
}
