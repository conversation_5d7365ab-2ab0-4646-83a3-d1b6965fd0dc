﻿using Capstone2.RestServices.Report.Interfaces;
using Capstone2.RestServices.Report.Models;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Report.Services
{
    public class RequestLogsBAL : IRequestLogsBAL
    {
        private readonly IRequestLogsDAL _requestLogsDAL;
        public RequestLogsBAL(IRequestLogsDAL requestLogsDAL)
        {
            _requestLogsDAL = requestLogsDAL;
        }

        public async Task<RequestLogs> GetRequestLogsByTransactionID(string transaction_id)
        {
            return await _requestLogsDAL.GetRequestLogsByTransactionID(transaction_id);
        }

    }
}
