﻿using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.Report.Interfaces;
using Capstone2.RestServices.Report.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Report.Services
{
    public class MedicareExceptionBAL : IMedicareExceptionBAL
    {
        public IMedicareExceptionDAL _medicareExceptionDAL;

        public MedicareExceptionBAL(IMedicareExceptionDAL medicareExceptionDAL)//, IOptions<AppSettings> appSettings)
        {
            _medicareExceptionDAL = medicareExceptionDAL;

        }
        public async Task<ApiResponse<QueryResultList<string>>> DeleteMedicareExceptions(long exceptionId, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<QueryResultList<string>> apiResponse = new();
            await _medicareExceptionDAL.DeleteMedicareExceptions(exceptionId, baseHttpRequestContext.UserId);
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result = null;
            return apiResponse;
        }

        public async Task<ApiResponse<QueryResultList<MedicareException>>> ListMedicareExceptions(QueryModel queryModel, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<QueryResultList<MedicareException>> apiResponse = new();
            MedicareExceptionFilterModel filterModel = PrepareFilterParameters(queryModel.Filter);
            int orgId = baseHttpRequestContext.OrgId;
            QueryResultList<MedicareException> lstMedciareException = await _medicareExceptionDAL.ListMedicareExceptions(orgId, queryModel, filterModel);

            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result = lstMedciareException;
            return apiResponse;
        }

        private MedicareExceptionFilterModel PrepareFilterParameters(string filter)
        {
            if (!(string.IsNullOrEmpty(filter)))
            {
                return JsonConvert.DeserializeObject<MedicareExceptionFilterModel>(filter);
            }
            else
            {
                return null;
            }
        }
    }
}
