﻿using AutoMapper;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.Report.Interfaces;
using Capstone2.RestServices.Report.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Linq;
using System.Threading.Tasks;


namespace Capstone2.RestServices.Report.Services
{
    public class IMCProcessingReportBAL : IIMCProcessingReportBAL
    {
        public IIMCProcessingReportDAL _imcProcessingReportDAL;
        public IRequestLogsDAL _requestLogsDAL;
        public readonly AppSettings _appSettings;
        public IMapper _mapper;

        public IMCProcessingReportBAL(IIMCProcessingReportDAL imcProcessingReportDAL, IRequestLogsDAL requestLogsDAL, IOptions<AppSettings> appSettings, IMapper mapper)
        {
            _imcProcessingReportDAL = imcProcessingReportDAL;
            _requestLogsDAL = requestLogsDAL;
            _appSettings = appSettings.Value;
            _mapper = mapper;

        }

        /// <summary>
        ///  Method to list all the Claims at Processing Report
        /// </summary>
        /// <param name="queryModel"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<QueryResultList<ImcprocessingReport>>> GetProcessingReportList(QueryModel queryModel, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<QueryResultList<ImcprocessingReport>> apiResponse = new();
            int orgId = baseHttpRequestContext.OrgId;
            ProcessingReportFilterModel filterModel = PrepareFilterParameters(queryModel.Filter);
            QueryResultList<ImcprocessingReport> processingReportList = await _imcProcessingReportDAL.GetProcessingReportList(orgId, queryModel, filterModel);
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result = processingReportList;
            return apiResponse;
        }

        private ProcessingReportFilterModel PrepareFilterParameters(string filter)
        {
            if (!(string.IsNullOrEmpty(filter)))
            {
                return JsonConvert.DeserializeObject<ProcessingReportFilterModel>(filter);
            }
            else
            {
                return null;
            }
        }

        public async Task<ApiResponse<ClaimRequest>> GetProcessingClaimDetails(string transactionId, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<ClaimRequest> apiResponse = new();
            ClaimRequest claimRequest = new ClaimRequest();
            ImcprocessingReport processingReport = await _imcProcessingReportDAL.GetProcessingReportDetails(baseHttpRequestContext.OrgId, transactionId);
            RequestLogs requestLogs = await _requestLogsDAL.GetRequestLogsByTransactionID(transactionId);
            EclipseClaimRequest eclipseClaimRequest = null;
            if (processingReport is not null && requestLogs is not null)
            {
                if (processingReport.ClaimTypeId == (short)ClaimType.IMC)
                {
                    eclipseClaimRequest = Newtonsoft.Json.JsonConvert.DeserializeObject<EclipseClaimRequest>(requestLogs.Request);
                    claimRequest = _mapper.Map<EclipseClaimRequest, ClaimRequest>(eclipseClaimRequest);
                }
                else
                {
                    claimRequest = Newtonsoft.Json.JsonConvert.DeserializeObject<ClaimRequest>(requestLogs.Request);

                }
                claimRequest.ErrorText = processingReport.ErrorText;
                foreach (var medicalEvent in claimRequest.claim.MedicalEvent)
                {
                    var itmMediAssPR = processingReport.InvoiceMedicareAssocsProcessingImcs.FirstOrDefault(x => !string.IsNullOrWhiteSpace(x.MedicalEventId) && x.MedicalEventId.Equals(medicalEvent.Id));
                    if (itmMediAssPR != null)
                    {
                        medicalEvent.InvoiceClaimDetail = new InvoiceClaimDetail();
                        medicalEvent.InvoiceClaimDetail.InvoiceDetailsId = itmMediAssPR.InvoiceDetailsId;
                        medicalEvent.InvoiceClaimDetail.InvoiceRecordId = itmMediAssPR.InvoiceRecordId;
                        medicalEvent.InvoiceClaimDetail.PatientDetailsId = itmMediAssPR.PatientDetailsId;
                        medicalEvent.InvoiceClaimDetail.InvoiceSummaryId = itmMediAssPR.InvoiceSummaryId;
                        medicalEvent.InvoiceClaimDetail.InvoiceSummaryRecordId = itmMediAssPR.InvoiceSummaryRecordId;
                        medicalEvent.InvoiceClaimDetail.FinanceTypeId = itmMediAssPR.FinanceTypeId;
                        medicalEvent.InvoiceClaimDetail.ClaimStatusId = itmMediAssPR.ClaimStatusId;
                        medicalEvent.InvoiceClaimDetail.ErrorText = itmMediAssPR.ErrorText;
                        if (processingReport.ClaimTypeId == (short)ClaimType.IMC)
                        {
                            medicalEvent.Patient = (eclipseClaimRequest is not null && eclipseClaimRequest.claim is not null && eclipseClaimRequest.claim.Patient is not null) ? eclipseClaimRequest.claim.Patient : null;
                        }
                    }
                }
            }

            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result = claimRequest;
            return apiResponse;
        }
    }
}
