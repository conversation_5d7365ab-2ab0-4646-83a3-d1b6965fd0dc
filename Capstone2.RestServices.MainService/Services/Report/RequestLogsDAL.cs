﻿using Capstone2.RestServices.Report.Context;
using Capstone2.RestServices.Report.Interfaces;
using Capstone2.RestServices.Report.Models;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Report.Services
{
    public class RequestLogsDAL : IRequestLogsDAL
    {

        public readonly ReadOnlyReportDBContext _readOnlyDbContext;
        public RequestLogsDAL(ReadOnlyReportDBContext readOnlyDbContext)
        {
            _readOnlyDbContext = readOnlyDbContext;
        }
        public async Task<RequestLogs> GetRequestLogsByTransactionID(string transaction_id)
        {
            return await _readOnlyDbContext.RequestLogs.OrderBy(x=> x.CreatedDate).Where(w => w.CorrelationId == transaction_id).FirstOrDefaultAsync();
        }
    }
}
