﻿using Capstone2.RestServices.Report.Context;
using Capstone2.RestServices.Report.Interfaces;
using Capstone2.RestServices.Report.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Report.Services
{
    public class ReportDAL : IReportDAL
    {
        public readonly ReadOnlyReportDBContext _readOnlyDbContext;
        public UpdatableReportDBContext _updatableDBContext;

        public ReportDAL(ReadOnlyReportDBContext readOnlyDbContext, UpdatableReportDBContext updatableDBContext)
        {
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
        }

        /// <summary>
        /// Method to get payment from PaymentDetails table
        /// </summary>
        /// <param name="paymentDetailId"></param>
        /// <returns></returns>
        public async Task<EODReportTotal> GetEODReportTotal(int orgId, EODReportFilterModel filterModel)
        {
            EODReportTotal eodReport = new EODReportTotal();
            List<Eodreport> eodReportList = await _readOnlyDbContext.EODReport.Where(x => x.PaymentDate >= filterModel.StartDate && x.PaymentDate <= filterModel.EndDate && x.InvoiceStatusId != (short)InvoiceStatus.Void && x.StatusId != (short)Status.Deleted).ToListAsync();
            if (eodReportList is not null && eodReportList.Count > 0)
            {
                eodReportList.ForEach(eod =>
                {
                    eod.Amount = ((eod.CRAmount == null || eod.CRAmount == 0) && eod.DRAmount != null) ? eod.DRAmount * -1 : eod.CRAmount;
                });
            }
            if (filterModel != null && eodReportList != null && eodReportList.Count > 0)
            {
                if (filterModel.PaymentMethodTypeId != null && filterModel.PaymentMethodTypeId.Count > 0)
                {
                    eodReportList = eodReportList.Where(x => filterModel.PaymentMethodTypeId.Contains(x.PaymentMethodTypeId)).ToList();
                }
                if (filterModel.CompanyDetailsId != null && filterModel.CompanyDetailsId.Count > 0)
                {
                    eodReportList = eodReportList.Where(x => filterModel.CompanyDetailsId.Contains(x.CompanyDetailsId)).ToList();
                }
                if (filterModel.ProviderId != null && filterModel.ProviderId.Count > 0)
                {
                    eodReportList = eodReportList.Where(x => filterModel.ProviderId.Contains(x.ProviderId)).ToList();
                }
                if (filterModel.Reconcile != null)
                {
                    eodReportList = eodReportList.Where(x => filterModel.Reconcile == x.Reconcile).ToList();
                }
            }

            if (eodReportList != null && eodReportList.Count > 0)
            {
                eodReport = new EODReportTotal
                {
                    CashTotal = eodReportList.Where(x => x.PaymentMethodTypeId == (short)PaymentMethodType.Cash && x.Amount != null).Select(x => (decimal)x.Amount).Sum(),
                    EFTPOS = eodReportList.Where(x => x.PaymentMethodTypeId == (short)PaymentMethodType.Credit_Card && x.Amount != null).Select(x => (decimal)x.Amount).Sum() + eodReportList.Where(x => x.PaymentMethodTypeId == (short)PaymentMethodType.Debit_Card && x.Amount != null).Select(x => (decimal)x.Amount).Sum(),
                    EFTPOSSurcharge = eodReportList.Where(x => x.PaymentMethodTypeId == (short)PaymentMethodType.Credit_Card && x.Surcharge != null).Select(x => (decimal)x.Surcharge).Sum() + eodReportList.Where(x => x.PaymentMethodTypeId == (short)PaymentMethodType.Debit_Card && x.Surcharge != null).Select(x => (decimal)x.Surcharge).Sum(),
                    Cheque = eodReportList.Where(x => x.PaymentMethodTypeId == (short)PaymentMethodType.Cheque && x.Amount != null).Select(x => (decimal)x.Amount).Sum(),
                    DirectDeposit = eodReportList.Where(x => x.PaymentMethodTypeId == (short)PaymentMethodType.Direct_Deposit && x.Amount != null).Select(x => (decimal)x.Amount).Sum(),
                    TotalAmount = eodReportList.Where(x => x.Amount != null).Select(x => (decimal)x.Amount).Sum(),
                    IsCashMarkForReviewed = (eodReportList.Where(x => x.PaymentMethodTypeId == (short)PaymentMethodType.Cash && x.MarkForReview == true && x.Reconcile == false).Select(x => x.MarkForReview).Count() > 0) ? true : false,
                    IsEFTPOSMarkForReviewed = ((eodReportList.Where(x => x.PaymentMethodTypeId == (short)PaymentMethodType.Credit_Card && x.MarkForReview == true && x.Reconcile == false).Select(x => x.MarkForReview).Count() + eodReportList.Where(x => x.PaymentMethodTypeId == (short)PaymentMethodType.Debit_Card && x.MarkForReview == true && x.Reconcile == false).Select(x => x.MarkForReview).Count()) > 0) ? true : false,
                    IsChequeMarkForReviewed = (eodReportList.Where(x => x.PaymentMethodTypeId == (short)PaymentMethodType.Cheque && x.MarkForReview == true && x.Reconcile == false).Select(x => x.MarkForReview).Count() > 0) ? true : false,
                    IsDirectDepositMarkForReviewed = (eodReportList.Where(x => x.PaymentMethodTypeId == (short)PaymentMethodType.Direct_Deposit && x.MarkForReview == true && x.Reconcile == false).Select(x => x.MarkForReview).Count() > 0) ? true : false
                };
            }

            return eodReport;
        }

        /// <summary>
        /// Method to fetch a permission
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="rid"></param>
        public async Task<List<RolesPermissionsAssoc>> GetPermissionsOnRoleId(int orgId, int rid, List<int> pids)
        {
            List<RolesPermissionsAssoc> permissionsList = _readOnlyDbContext.RolesPermissionsAssocs.Where(x => x.RolesId == rid && x.StatusId == (short)Status.Active && x.OrgId == orgId && pids.Contains(x.PermissionsId)).ToList();
            return permissionsList;
        }

        /// <summary>
        /// Method to get payment from PaymentDetails table
        /// </summary>
        /// <param name="paymentDetailId"></param>
        /// <returns></returns>
        public async Task<List<ProviderIncomeReport>> GetProviderIncomeTotal(int orgId, ProviderIncomeReportFilterModel filterModel)
        {
            ProviderIncomeReportTotal providerIncomeReport = new ProviderIncomeReportTotal();
            List<ProviderIncomeReport> providerIncomeReportList = await _readOnlyDbContext.ProviderIncomeReport.Where(x => x.InvoiceStatusId != (short)InvoiceStatus.Void && x.StatusId != (short)Status.Deleted).ToListAsync();
            if (filterModel != null && providerIncomeReportList != null && providerIncomeReportList.Count > 0)
            {
                if (filterModel.ProviderId != null && filterModel.ProviderId.Count > 0)
                {
                    providerIncomeReportList = providerIncomeReportList.Where(x => filterModel.ProviderId.Contains(x.ProviderId)).ToList();
                }
            }
            return providerIncomeReportList;
        }
        /// <summary>
        /// Method to list all the payments based on filter
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="queryModel"></param>
        /// <param name="filterModel"></param>
        /// <returns></returns>
        public async Task<QueryResultList<Eodreport>> ListPaymentTransactions(int orgId, QueryModel queryModel, EODReportFilterModel filterModel)
        {
            IQueryable<Eodreport> paymentQuery = from payment in _readOnlyDbContext.EODReport
                                                 where payment.OrgId == orgId && (payment.PaymentDate >= filterModel.StartDate && payment.PaymentDate <= filterModel.EndDate) && payment.InvoiceStatusId != (short)InvoiceStatus.Void && payment.StatusId != (short)Status.Deleted
                                                 select new Eodreport
                                                 {
                                                     Id = payment.Id,
                                                     OrgId = payment.OrgId,
                                                     InvoiceDetailsId = payment.InvoiceDetailsId,
                                                     InvoiceSummaryId = payment.InvoiceSummaryId,
                                                     PaymentDetailsId = payment.PaymentDetailsId,
                                                     InvoiceNo = payment.InvoiceNo,
                                                     PaymentDate = payment.PaymentDate,
                                                     BankDate = payment.BankDate,
                                                     PaymentTypeId = payment.PaymentTypeId,
                                                     FinanceTypeId = payment.FinanceTypeId,
                                                     PaymentMethodTypeId = payment.PaymentMethodTypeId,
                                                     PaymentMethodType = payment.PaymentMethodType,
                                                     PaymentType = payment.PaymentType,
                                                     //Amount = payment.Amount,
                                                     CRAmount = payment.CRAmount == null ? 0 : payment.CRAmount,
                                                     DRAmount = payment.DRAmount == null ? 0 : payment.DRAmount,
                                                     Surcharge = payment.Surcharge,
                                                     CompanyName = payment.CompanyName,
                                                     CompanyDetailsId = payment.CompanyDetailsId,
                                                     ProviderId = payment.ProviderId,
                                                     ProviderName = payment.ProviderName,
                                                     PatientId = payment.PatientId,
                                                     PatientName = payment.PatientName,
                                                     PatientRecordId = payment.PatientRecordId,
                                                     Notes = payment.Notes,
                                                     MarkForReview = payment.MarkForReview,
                                                     Reconcile = payment.Reconcile,
                                                     MarkForReviewId = payment.MarkForReviewId,
                                                     MarkForReviewDescription = payment.MarkForReviewDescription,
                                                     StatusId = payment.StatusId,
                                                     ReceiptedBy = payment.ReceiptedBy,
                                                     CreatedBy = payment.CreatedBy,
                                                     CreatedDate = payment.CreatedDate,
                                                     ModifiedDate = payment.ModifiedDate,
                                                     ModifiedBy = payment.ModifiedBy,
                                                     PaymentReferance = payment.PaymentReferance
                                                 };


            if (filterModel != null)
            {
                if (filterModel.PaymentMethodTypeId != null && filterModel.PaymentMethodTypeId.Count > 0)
                {
                    paymentQuery = paymentQuery.Where(x => filterModel.PaymentMethodTypeId.Contains(x.PaymentMethodTypeId));
                }
                if (filterModel.CompanyDetailsId != null && filterModel.CompanyDetailsId.Count > 0)
                {
                    paymentQuery = paymentQuery.Where(x => filterModel.CompanyDetailsId.Contains(x.CompanyDetailsId));
                }
                if (filterModel.ProviderId != null && filterModel.ProviderId.Count > 0)
                {
                    paymentQuery = paymentQuery.Where(x => filterModel.ProviderId.Contains(x.ProviderId));
                }
                if (filterModel.MarkForReview != null)
                {
                    paymentQuery = paymentQuery.Where(x => filterModel.MarkForReview == x.MarkForReview);
                }
                if (filterModel.Reconcile != null)
                {
                    paymentQuery = paymentQuery.Where(x => filterModel.Reconcile == x.Reconcile);
                }
            }
            paymentQuery = SortTransactions(paymentQuery, queryModel.SortTerm, queryModel.SortOrder);
            QueryResultList<Eodreport> paginatedList = await PaginatedResultListForPaymentTransactions(paymentQuery, queryModel);

            return paginatedList;
        }

        /// <summary>
        /// Method to list all the provider income on a given date
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="queryModel"></param>
        /// <param name="filterModel"></param>
        /// <returns></returns>
        public async Task<QueryResultList<ProviderIncomeReport>> ListProviderIncome(int orgId, QueryModel queryModel, ProviderIncomeReportFilterModel filterModel, DateTime StartDateUTC, DateTime EndDateUTC)
        {
            IQueryable<ProviderIncomeReport> paymentQuery = from payment in _readOnlyDbContext.ProviderIncomeReport
                                                            where payment.OrgId == orgId && payment.InvoiceStatusId != (short)InvoiceStatus.Void && payment.StatusId != (short)Status.Deleted && ((payment.PaymentDate >= filterModel.StartDate && payment.PaymentDate <= filterModel.EndDate) || ((payment.PaymentDate == null) && payment.CreatedDate >= StartDateUTC && payment.CreatedDate <= EndDateUTC))
                                                            select new ProviderIncomeReport
                                                            {
                                                                Id = payment.Id,
                                                                OrgId = payment.OrgId,
                                                                InvoiceDetailsId = payment.InvoiceDetailsId,
                                                                InvoicedAmount = payment.InvoicedAmount == null ? 0 : payment.InvoicedAmount,
                                                                ReceiptedAmount = payment.ReceiptedAmount == null ? 0 : payment.ReceiptedAmount,
                                                                OutstandingAmount = payment.OutstandingAmount == null ? 0 : payment.OutstandingAmount,
                                                                PaymentDate = payment.PaymentDate,
                                                                PaymentTypeId = payment.PaymentTypeId,
                                                                InvoiceTypeId = payment.InvoiceTypeId,
                                                                ProviderId = payment.ProviderId,
                                                                ProviderName = payment.ProviderName,
                                                                PatientId = payment.PatientId,
                                                                PatientName = payment.PatientName,
                                                                StatusId = payment.StatusId,
                                                                CreatedBy = payment.CreatedBy,
                                                                CreatedDate = payment.CreatedDate,
                                                                ModifiedDate = payment.ModifiedDate,
                                                                ModifiedBy = payment.ModifiedBy
                                                            };
            if (filterModel != null)
            {
                if (filterModel.ProviderId != null && filterModel.ProviderId.Count > 0)
                {
                    paymentQuery = paymentQuery.Where(x => filterModel.ProviderId.Contains(x.ProviderId));
                    paymentQuery = paymentQuery.OrderByDescending(x => x.Id);
                }
            }
            //paymentQuery = SortTransactions(paymentQuery, queryModel.SortTerm, queryModel.SortOrder);
            QueryResultList<ProviderIncomeReport> paginatedList = await PaginatedResultListForProviderIncome(paymentQuery, queryModel);

            return paginatedList;
        }

        /// <summary>
        /// Method to list all the grouped payments by entity and  based on filter
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="queryModel"></param>
        /// <param name="filterModel"></param>
        /// <returns></returns>
        public async Task<QueryResultList<Eodreport>> ListGroupedPayments(int orgId, QueryModel queryModel, EODReportFilterModel filterModel)
        {
            IQueryable<Eodreport> paymentQuery = from payment in _readOnlyDbContext.EODReport
                                                 where payment.OrgId == orgId && payment.StatusId != (short)Status.Deleted && payment.InvoiceStatusId != (short)InvoiceStatus.Void && payment.PaymentDate >= filterModel.StartDate && payment.PaymentDate <= filterModel.EndDate && payment.Reconcile == false
                                                 select new Eodreport
                                                 {
                                                     Id = payment.Id,
                                                     OrgId = payment.OrgId,
                                                     InvoiceDetailsId = payment.InvoiceDetailsId,
                                                     PaymentDetailsId = payment.PaymentDetailsId,
                                                     InvoiceNo = payment.InvoiceNo,
                                                     PaymentDate = payment.PaymentDate,
                                                     BankDate = payment.BankDate,
                                                     PaymentTypeId = payment.PaymentTypeId,
                                                     PaymentMethodTypeId = payment.PaymentMethodTypeId,
                                                     PaymentMethodType = (payment.PaymentMethodTypeId == (short)PaymentMethodType.Credit_Card || payment.PaymentMethodTypeId == (short)PaymentMethodType.Debit_Card) ? "EFTPOS" : payment.PaymentMethodType,
                                                     PaymentType = payment.PaymentType,
                                                     Amount = payment.Amount,
                                                     CRAmount = payment.CRAmount == null ? 0 : payment.CRAmount,
                                                     DRAmount = payment.DRAmount == null ? 0 : payment.DRAmount,
                                                     Surcharge = (payment.Surcharge == null) ? 0 : payment.Surcharge,
                                                     CompanyName = payment.CompanyName,
                                                     CompanyDetailsId = payment.CompanyDetailsId,
                                                     ProviderId = payment.ProviderId,
                                                     ProviderName = payment.ProviderName,
                                                     PatientId = payment.PatientId,
                                                     PatientRecordId = payment.PatientRecordId,
                                                     Notes = payment.Notes,
                                                     MarkForReview = payment.MarkForReview,
                                                     Reconcile = payment.Reconcile,
                                                     MarkForReviewId = payment.MarkForReviewId,
                                                     MarkForReviewDescription = payment.MarkForReviewDescription,
                                                     StatusId = payment.StatusId,
                                                     ReceiptedBy = payment.ReceiptedBy,
                                                     CreatedBy = payment.CreatedBy,
                                                     CreatedDate = payment.CreatedDate,
                                                     ModifiedDate = payment.ModifiedDate,
                                                     ModifiedBy = payment.ModifiedBy,
                                                     PaymentReferance = payment.PaymentReferance
                                                 };

            if (filterModel != null)
            {
                if (filterModel.PaymentMethodTypeId != null && filterModel.PaymentMethodTypeId.Count > 0)
                {
                    paymentQuery = paymentQuery.Where(x => filterModel.PaymentMethodTypeId.Contains(x.PaymentMethodTypeId));
                }
                if (filterModel.CompanyDetailsId != null && filterModel.CompanyDetailsId.Count > 0)
                {
                    paymentQuery = paymentQuery.Where(x => filterModel.CompanyDetailsId.Contains(x.CompanyDetailsId));
                }
                if (filterModel.ProviderId != null && filterModel.ProviderId.Count > 0)
                {
                    paymentQuery = paymentQuery.Where(x => filterModel.ProviderId.Contains(x.ProviderId));
                }
                if (filterModel.MarkForReview != null)
                {
                    paymentQuery = paymentQuery.Where(x => filterModel.MarkForReview == x.MarkForReview);
                }
                if (filterModel.Reconcile != null)
                {
                    paymentQuery = paymentQuery.Where(x => filterModel.MarkForReview == x.Reconcile);
                }
            }
            //QueryResultList<Eodreport> paginatedList = await PaginatedResultListForPaymentTransactions(paymentQuery, queryModel);
            QueryResultList<Eodreport> paginatedList = await PaginatedResultListForPaymentTransactionsGrouped(paymentQuery, queryModel);

            return paginatedList;
        }
        private IQueryable<Eodreport> SortTransactions(IQueryable<Eodreport> paymentQuery, string sortTerm, string sortOrder)
        {
            sortTerm = (sortTerm is null) ? string.Empty : sortTerm;
            sortOrder = (sortOrder is null) ? string.Empty : sortOrder;
            switch (sortTerm.ToLower())
            {

                case "markforreview":

                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            paymentQuery = paymentQuery.OrderBy(x => x.MarkForReview).ThenByDescending(x => x.CreatedDate);
                        }
                        else
                        {

                            paymentQuery = paymentQuery.OrderByDescending(x => x.MarkForReview).ThenByDescending(x => x.CreatedDate);

                        }
                        break;
                    }
                case "companyname":

                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            paymentQuery = paymentQuery.OrderBy(x => x.CompanyName).ThenByDescending(x => x.CreatedDate);
                        }
                        else
                        {

                            paymentQuery = paymentQuery.OrderByDescending(x => x.CompanyName).ThenByDescending(x => x.CreatedDate);

                        }
                        break;
                    }
                case "providername":

                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            paymentQuery = paymentQuery.OrderBy(x => x.ProviderName).ThenByDescending(x => x.CreatedDate);
                        }
                        else
                        {

                            paymentQuery = paymentQuery.OrderByDescending(x => x.ProviderName).ThenByDescending(x => x.CreatedDate);

                        }
                        break;
                    }
                case "companydetailsid":

                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            paymentQuery = paymentQuery.OrderBy(x => x.CompanyDetailsId).ThenByDescending(x => x.CreatedDate);
                        }
                        else
                        {

                            paymentQuery = paymentQuery.OrderByDescending(x => x.CompanyDetailsId).ThenByDescending(x => x.CreatedDate);

                        }
                        break;
                    }
                case "providerid":

                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            paymentQuery = paymentQuery.OrderBy(x => x.ProviderId).ThenByDescending(x => x.CreatedDate);
                        }
                        else
                        {

                            paymentQuery = paymentQuery.OrderByDescending(x => x.ProviderId).ThenByDescending(x => x.CreatedDate);

                        }
                        break;
                    }
                case "patientid":

                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            paymentQuery = paymentQuery.OrderBy(x => x.PatientRecordId).ThenByDescending(x => x.CreatedDate);
                        }
                        else
                        {

                            paymentQuery = paymentQuery.OrderByDescending(x => x.PatientRecordId).ThenByDescending(x => x.CreatedDate);

                        }
                        break;
                    }
                case "patientname":

                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            paymentQuery = paymentQuery.OrderBy(x => x.PatientName).ThenByDescending(x => x.CreatedDate);
                        }
                        else
                        {

                            paymentQuery = paymentQuery.OrderByDescending(x => x.PatientName).ThenByDescending(x => x.CreatedDate);

                        }
                        break;
                    }
                default:
                    paymentQuery = paymentQuery.OrderByDescending(x => x.CreatedDate);

                    break;
            }
            return paymentQuery;
        }
        private async Task<QueryResultList<Eodreport>> PaginatedResultListForPaymentTransactionsGrouped(IQueryable<Eodreport> paymentQuery, QueryModel queryModel)
        {
            QueryResultList<Eodreport> queryList = new QueryResultList<Eodreport>();
            List<Eodreport> paginatedList = null;
            if (queryModel.PageNumber > 0 && queryModel.PageSize > 0)
            {
                if (paymentQuery.Any())
                {
                    paginatedList = await paymentQuery.ToListAsync();

                }
            }

            if (paginatedList != null)
            {
                queryList.ItemRecords = paginatedList;
                queryList.PageNumber = queryModel.PageNumber;
                queryList.PageSize = queryModel.PageSize;
                queryList.CurrentCount = paginatedList.Count();
            }
            else
            {
                queryList.ItemRecords = null;
                queryList.CurrentCount = 0;
            }
            queryList.TotalCount = paymentQuery.Count();
            queryList.PageNumber = queryModel.PageNumber;
            queryList.PageSize = queryModel.PageSize;

            return queryList;
        }
        private async Task<QueryResultList<Eodreport>> PaginatedResultListForPaymentTransactions(IQueryable<Eodreport> paymentQuery, QueryModel queryModel)
        {
            QueryResultList<Eodreport> queryList = new QueryResultList<Eodreport>();
            List<Eodreport> paginatedList = null;
            if (queryModel.PageNumber > 0 && queryModel.PageSize > 0)
            {
                if (paymentQuery.Any())
                {
                    paginatedList = await paymentQuery.Skip((queryModel.PageNumber - 1) * queryModel.PageSize)
                .Take(queryModel.PageSize).ToListAsync();
                }
            }

            if (paginatedList != null)
            {
                queryList.ItemRecords = paginatedList;
                queryList.PageNumber = queryModel.PageNumber;
                queryList.PageSize = queryModel.PageSize;
                queryList.CurrentCount = paginatedList.Count();
            }
            else
            {
                queryList.ItemRecords = null;
                queryList.CurrentCount = 0;
            }
            queryList.TotalCount = paymentQuery.Count();
            queryList.PageNumber = queryModel.PageNumber;
            queryList.PageSize = queryModel.PageSize;

            return queryList;
        }
        private async Task<QueryResultList<ProviderIncomeReport>> PaginatedResultListForProviderIncome(IQueryable<ProviderIncomeReport> paymentQuery, QueryModel queryModel)
        {
            QueryResultList<ProviderIncomeReport> queryList = new QueryResultList<ProviderIncomeReport>();
            List<ProviderIncomeReport> paginatedList = null;
            if (queryModel.PageNumber > 0 && queryModel.PageSize > 0)
            {
                if (paymentQuery.Any())
                {
                    paginatedList = await paymentQuery.Skip((queryModel.PageNumber - 1) * queryModel.PageSize)
                 .Take(queryModel.PageSize).ToListAsync();
                }
            }

            if (paginatedList != null)
            {
                queryList.ItemRecords = paginatedList;
                queryList.PageNumber = queryModel.PageNumber;
                queryList.PageSize = queryModel.PageSize;
                queryList.CurrentCount = paginatedList.Count();
            }
            else
            {
                queryList.ItemRecords = null;
                queryList.CurrentCount = 0;
            }
            queryList.TotalCount = paymentQuery.Count();
            queryList.PageNumber = queryModel.PageNumber;
            queryList.PageSize = queryModel.PageSize;

            return queryList;
        }
        public async Task<List<RevenueReport>> ListRevenueReportWithFilters(QueryModel queryModel, RevenueReportFilterModel filterModel, int orgId)
        {
            IQueryable<RevenueReport> reportQuery = from report in _readOnlyDbContext.RevenueReport
                                                        // where
                                                        // //(filterModel.StartDate ==null || report.DateInvoiced >=filterModel.StartDate) &
                                                    where (report.InvoiceStatusId != null && report.InvoiceStatusId != (short)InvoiceStatus.Void && report.InvoiceStatusId != (short)InvoiceStatus.Deleted)
                                                    && report.AdjustmentTypeId == null
                                                    select report;
            // return await reportQuery.ToListAsync();
            if (filterModel != null)
            {
                if (filterModel.ProviderId != null && filterModel.ProviderId.Count > 0)
                {
                    reportQuery = reportQuery.Where(x => filterModel.ProviderId.Contains(x.UserDetailsId));
                }
                if (filterModel.CompanyDetailsId != null && filterModel.CompanyDetailsId.Count > 0)
                {
                    reportQuery = reportQuery.Where(x => filterModel.CompanyDetailsId.Contains(x.CompanyDetailsId));
                }
                if (filterModel.StartDate != null)
                {
                    reportQuery = reportQuery.Where(x => x.DateOfService != null && x.DateOfService >= filterModel.StartDate);
                }
                if (filterModel.EndDate != null)
                {
                    reportQuery = reportQuery.Where(x => x.DateOfService != null && x.DateOfService <= filterModel.EndDate);
                }
            }

            return await reportQuery.ToListAsync();
        }

        public async Task<QueryResultList<RevenueReport>> ListRevenueReport(QueryModel queryModel, RevenueReportFilterModel filterModel, int orgId)
        {
            IQueryable<RevenueReport> reportQuery = from report in _readOnlyDbContext.RevenueReport

                                                    select report;

            if (filterModel != null)
            {
                if (filterModel.ProviderId != null && filterModel.ProviderId.Count > 0)
                {
                    reportQuery = reportQuery.Where(x => filterModel.ProviderId.Contains(x.UserDetailsId));
                }
                if (filterModel.CompanyDetailsId != null && filterModel.CompanyDetailsId.Count > 0)
                {
                    reportQuery = reportQuery.Where(x => filterModel.CompanyDetailsId.Contains(x.CompanyDetailsId));
                }
                if (filterModel.StartDate != null)
                {
                    reportQuery = reportQuery.Where(x => x.DateInvoiced != null && x.DateInvoiced >= filterModel.StartDate);
                }
                if (filterModel.EndDate != null)
                {
                    reportQuery = reportQuery.Where(x => x.DateInvoiced != null && x.DateInvoiced <= filterModel.EndDate);
                }
            }
            QueryResultList<RevenueReport> paginatedList = await PaginatedResultListRevenueReport(reportQuery, queryModel);

            return paginatedList;
        }


        private async Task<QueryResultList<RevenueReport>> PaginatedResultListRevenueReport(IQueryable<RevenueReport> reportQuery, QueryModel queryModel)
        {
            QueryResultList<RevenueReport> queryList = new();
            List<RevenueReport> paginatedList = null;
            if (queryModel.PageNumber > 0 && queryModel.PageSize > 0)
            {
                if (reportQuery.Any())
                {
                    paginatedList = await reportQuery.ToListAsync();
                }
            }

            if (paginatedList != null)
            {
                queryList.ItemRecords = paginatedList;
                queryList.PageNumber = queryModel.PageNumber;
                queryList.PageSize = queryModel.PageSize;
                queryList.CurrentCount = paginatedList.Count();
            }
            else
            {
                queryList.ItemRecords = null;
                queryList.CurrentCount = 0;
            }
            queryList.TotalCount = reportQuery.Count();
            queryList.PageNumber = queryModel.PageNumber;
            queryList.PageSize = queryModel.PageSize;

            return queryList;
        }

        public async Task<List<SalesReportView>> ListSalesReportWithFilters(QueryModel queryModel, RevenueReportFilterModel filterModel, int orgId)
        {
            IQueryable<SalesReportView> reportQuery = from revenue in _readOnlyDbContext.RevenueReport
                                                      join iE in _readOnlyDbContext.InvoiceEpisodeItemsReport on revenue.Id equals iE.RevenueReportId into JIE
                                                      from iE in JIE

                                                      select new SalesReportView
                                                      {

                                                          DateOfService = revenue.DateOfService,
                                                          CompanyDetailsId = revenue.CompanyDetailsId,
                                                          CompanyName = revenue.CompanyName,
                                                          EpisodeTypeId = iE.EpisodeTypeId,
                                                          Level1Id = iE.Level1Id,
                                                          Level2Id = iE.Level2Id,
                                                          Level3Id = iE.Level2Id,
                                                          InvoiceDetailsId = revenue.InvoiceDetailsId,
                                                          Quantity = (short)((revenue.AdjustmentTypeId == null) ? revenue.Quantity : (revenue.AdjustmentTypeId == (short)AdjustmentType.Refund) ? revenue.Quantity * -1 : revenue.Quantity),
                                                          AdjustmentTypeId = revenue.AdjustmentTypeId,
                                                          Fee = (revenue.AdjustmentTypeId == null) ? revenue.Fee : (revenue.AdjustmentTypeId == (short)AdjustmentType.Refund) ? revenue.Fee * -1 : revenue.Fee,
                                                          Gst = (revenue.AdjustmentTypeId == null) ? revenue.Gst : (revenue.AdjustmentTypeId == (short)AdjustmentType.Refund) ? revenue.Gst * -1 : revenue.Gst,

                                                          totalFee = (((revenue.AdjustmentTypeId == null) ? revenue.Fee : (revenue.AdjustmentTypeId == (short)AdjustmentType.Refund) ? revenue.Fee * -1 : revenue.Fee) + ((revenue.AdjustmentTypeId == null) ? revenue.Gst : (revenue.AdjustmentTypeId == (short)AdjustmentType.Refund) ? revenue.Gst * -1 : revenue.Gst))
                                               ,
                                                          InvoiceEpisodeItemAssocsId = revenue.InvoiceEpisodeItemAssocsId,
                                                          InvoiceTypeId = revenue.InvoiceTypeId,
                                                      }
                                                        ;
            // return await reportQuery.ToListAsync();
            if (filterModel != null)
            {

                if (filterModel.CompanyDetailsId != null && filterModel.CompanyDetailsId.Count > 0)
                {
                    reportQuery = reportQuery.Where(x => filterModel.CompanyDetailsId.Contains(x.CompanyDetailsId));
                }
                if (filterModel.StartDate != null)
                {
                    reportQuery = reportQuery.Where(x => x.DateOfService != null && x.DateOfService >= filterModel.StartDate);
                }
                if (filterModel.EndDate != null)
                {
                    reportQuery = reportQuery.Where(x => x.DateOfService != null && x.DateOfService <= filterModel.EndDate);
                }
            }

            return await reportQuery.ToListAsync();
        }


        public async Task<List<SalesReportGroupModel>> ListSalesReportWithFilters2(QueryModel queryModel, SalesReportFilterModel filterModel, int orgId)
        {
            List<SqlParameter> paramsList = new List<SqlParameter>();
            paramsList.Add(new SqlParameter("@OrgId", orgId));
            if (filterModel.CompanyDetailsId != null && filterModel.CompanyDetailsId.Count > 0)
                paramsList.Add(new SqlParameter("@companyDetailsId", string.Join<int?>(',', filterModel.CompanyDetailsId)));
            if (filterModel.ProviderId != null && filterModel.ProviderId.Count > 0)
                paramsList.Add(new SqlParameter("@providerId", string.Join<long?>(',', filterModel.ProviderId)));

            if (filterModel.StartDate != null)
            {
                SqlParameter param = new SqlParameter("@StartDate", SqlDbType.DateTime);
                param.Value = filterModel.StartDate;
                paramsList.Add(param);
            }
            if (filterModel.EndDate != null)
            {
                SqlParameter param = new SqlParameter("@EndDate", SqlDbType.DateTime);
                param.Value = filterModel.EndDate;
                paramsList.Add(param);
            }

            var response = await ExecuteStoredProcedure("[Report].[ListSalesReportGroupedByEpisodes]", paramsList);
            return response;
        }
        public async Task<List<SalesReportGroupModel>> ExecuteStoredProcedure(string storedProcedureName, IEnumerable<SqlParameter> parameters)
        {
            List<SalesReportGroupModel> reports = null;
            var dbConnection = (_readOnlyDbContext.Database.GetDbConnection());
            dbConnection.Open();
            using (var cmd = dbConnection.CreateCommand())
            {
                cmd.CommandText = storedProcedureName;
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 1800;
                foreach (var parameter in parameters)
                {
                    cmd.Parameters.Add(parameter);
                }

                var reader = await cmd.ExecuteReaderAsync();
                reports = SqlDataToJson(reader);




            }
            return reports;
        }
        private List<SalesReportGroupModel> SqlDataToJson(DbDataReader dataReader)
        {
            var dataTable = new DataTable();
            List<SalesReportCompanyEpisodeView> salesReportList = new List<SalesReportCompanyEpisodeView>();
            dataTable.Load(dataReader);
            var dataTable2 = new DataTable();
            dataTable2.Load(dataReader);

            List<KeyValuePair<int, decimal>> companyTotalList = new();
            foreach (DataRow dr in dataTable2.Rows)
                companyTotalList.Add(new KeyValuePair<int, decimal>(Convert.ToInt32(dr["CompanyDetailsId"]), Convert.ToDecimal(dr["TotalCompanySalesIncome"])));


            salesReportList = (from rw in dataTable.AsEnumerable()
                               select new SalesReportCompanyEpisodeView
                               {
                                   EpisodeTypeId = rw["EpisodeTypeId"] == DBNull.Value ? null : Convert.ToInt16(rw["EpisodeTypeId"]),
                                   Income = rw["Fee"] == DBNull.Value ? 0 : (decimal?)rw["Fee"],
                                   Level1Id = rw["Level1Id"] == DBNull.Value ? null : Convert.ToInt16(rw["Level1Id"]),
                                   CompanyDetailsId = rw["CompanyDetailsId"] == DBNull.Value ? 0 : Convert.ToInt32(rw["CompanyDetailsId"]),
                                   CompanyName = rw["CompanyName"].ToString(),
                                   Level1 = rw["Level1"] == DBNull.Value ? null : rw["Level1"].ToString(),
                               }).ToList();

            List<SalesReportGroupModel> salesReportGroupModels = new();
            foreach (var kp in companyTotalList)
            {
                int companyId = kp.Key;
                decimal total = kp.Value;

                SalesReportGroupModel salesReportGroupModel = new();
                salesReportGroupModel.TotalFeeIncGst = total;
                salesReportGroupModel.CompanyDetailsId = companyId;
                salesReportGroupModel.CompanySalesReports = salesReportList.Where(x => x.CompanyDetailsId == companyId).ToList();
                salesReportGroupModel.CompanySalesReports?.ForEach(x =>
                {
                    x.PctIncome = (total == 0) ? 0 : Math.Round((decimal)x.Income / total * 100, 2);
                });
                salesReportGroupModels.Add(salesReportGroupModel);
            }


            return salesReportGroupModels;
        }

        public async Task<BusinessActivity> BusinessActivityCashDAL(QueryModel queryModel, BusinessActivityFilter filterModel, int orgId)
        {
            string storedProcedureName = null;
            List<SqlParameter> paramsList = new List<SqlParameter>();
            paramsList.Add(new SqlParameter("@OrgId", orgId));
            if (filterModel.CompanyDetailsId != null && filterModel.CompanyDetailsId.Count > 0)
                paramsList.Add(new SqlParameter("@companyDetailsId", string.Join<int?>(',', filterModel.CompanyDetailsId)));
            if (filterModel.ProviderId != null && filterModel.ProviderId.Count > 0)
                paramsList.Add(new SqlParameter("@providerId", string.Join<long?>(',', filterModel.ProviderId)));
            if (filterModel.year != null)
                paramsList.Add(new SqlParameter("@year", filterModel.year));



            switch (filterModel.BusinessActivityType)
            {

                case 458:


                    {
                        storedProcedureName = "[Report].[BusinessActivityReportCash]";
                        break;
                    }
                case 459:

                    {
                        storedProcedureName = "[Report].[BusinessActivityDeposits]";
                        break;
                    }
                case 460:

                    {
                        storedProcedureName = "[Report].[BusinessActivityReceipts]";
                        break;
                    }
                case 461:

                    {
                        storedProcedureName = "[Report].[BusinessActivityInvoiced]";
                        break;
                    }
                case 462:

                    {
                        storedProcedureName = "[Report].[BusinessActivityOutstanding]";
                        break;
                    }
                case 463:

                    {
                        storedProcedureName = "[Report].[BusinessActivityGst]";
                        break;
                    }

            }

            var response = await ExecuteStoredProcedureBA(storedProcedureName, paramsList);
            return response;
        }

        public async Task<BusinessActivity> ExecuteStoredProcedureBA(string storedProcedureName, IEnumerable<SqlParameter> parameters)
        {
            BusinessActivity reports = null;
            var dbConnection = (_readOnlyDbContext.Database.GetDbConnection());
            dbConnection.Open();
            using (var cmd = dbConnection.CreateCommand())
            {
                cmd.CommandText = storedProcedureName;
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 1800;
                foreach (var parameter in parameters)
                {
                    cmd.Parameters.Add(parameter);
                }

                var reader = await cmd.ExecuteReaderAsync();
                reports = SqlDataToJsonBA(reader);




            }
            return reports;
        }
        private BusinessActivity SqlDataToJsonBA(DbDataReader dataReader)
        {
            var dataTable = new DataTable();
            BusinessActivity cashObj = new BusinessActivity();
            dataTable.Load(dataReader);



            cashObj = (from rw in dataTable.AsEnumerable()
                       select new BusinessActivity
                       {
                           Q1Sum = rw["Q1Sum"] == DBNull.Value ? 0 : (decimal?)rw["Q1Sum"],
                           Q2Sum = rw["Q2Sum"] == DBNull.Value ? 0 : (decimal?)rw["Q2Sum"],
                           Q3Sum = rw["Q3Sum"] == DBNull.Value ? 0 : (decimal?)rw["Q3Sum"],
                           Q4Sum = rw["Q4Sum"] == DBNull.Value ? 0 : (decimal?)rw["Q4Sum"],
                           Total = rw["Total"] == DBNull.Value ? 0 : (decimal?)rw["Total"]

                       }).FirstOrDefault();

            return cashObj;
        }

        public async Task<List<MedicalReferralReport>> ListMedicalReferralReport(MedicalReferralReportFilterModel filterModel, int orgId)
        {
            List<SqlParameter> paramsList = new List<SqlParameter>();
            paramsList.Add(new SqlParameter("@OrgId", orgId));
            if (filterModel.CompanyDetailsId != null && filterModel.CompanyDetailsId.Count > 0)
                paramsList.Add(new SqlParameter("@companyDetailsId", string.Join<int?>(',', filterModel.CompanyDetailsId)));
            if (filterModel.ProviderId != null && filterModel.ProviderId.Count > 0)
                paramsList.Add(new SqlParameter("@providerId", string.Join<long?>(',', filterModel.ProviderId)));

            if (filterModel.StartDate != null)
            {
                SqlParameter param = new SqlParameter("@StartDate", SqlDbType.DateTime);
                param.Value = filterModel.StartDate;
                paramsList.Add(param);
            }
            if (filterModel.EndDate != null)
            {
                SqlParameter param = new SqlParameter("@EndDate", SqlDbType.DateTime);
                param.Value = filterModel.EndDate;
                paramsList.Add(param);
            }

            var response = await ExecuteStoredProcedure_MedicalReferralReport("[Report].[MedicalReferralReport]", paramsList);
            return response;
        }

        public async Task<List<MedicalReferralReport>> ExecuteStoredProcedure_MedicalReferralReport(string storedProcedureName, IEnumerable<SqlParameter> parameters)
        {
            List<MedicalReferralReport> reports = null;
            var dbConnection = (_readOnlyDbContext.Database.GetDbConnection());
            dbConnection.Open();
            using (var cmd = dbConnection.CreateCommand())
            {
                cmd.CommandText = storedProcedureName;
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 1800;
                foreach (var parameter in parameters)
                {
                    cmd.Parameters.Add(parameter);
                }

                var dataReader = await cmd.ExecuteReaderAsync();
                var dataTable = new DataTable();
                dataTable.Load(dataReader);

                reports = (from rw in dataTable.AsEnumerable()
                           select new MedicalReferralReport
                           {
                               ProviderTypeName = Convert.ToString(rw["ProviderTypeName"]),
                               NumberofReferrals = rw["NumberofReferrals"] == DBNull.Value ? 0 : Convert.ToInt32(rw["NumberofReferrals"])
                           }).ToList();
            }
            return reports;
        }

        public async Task<List<NonMedicalReferralReport>> ListNonMedicalReferralReport(NonMedicalReferralReportFilterModel filterModel, int orgId)
        {
            List<NonMedicalReferralReport> response = new List<NonMedicalReferralReport>();
            try
            {
                List<SqlParameter> paramsList = new List<SqlParameter>();
                paramsList.Add(new SqlParameter("@OrgId", orgId));
                //if (filterModel.CompanyDetailsId != null && filterModel.CompanyDetailsId.Count > 0)
                //    paramsList.Add(new SqlParameter("@companyDetailsId", string.Join<int?>(',', filterModel.CompanyDetailsId)));
                //if (filterModel.ProviderId != null && filterModel.ProviderId.Count > 0)
                //    paramsList.Add(new SqlParameter("@providerId", string.Join<long?>(',', filterModel.ProviderId)));

                if (filterModel.StartDate != null)
                {
                    SqlParameter param = new SqlParameter("@StartDate", SqlDbType.DateTime);
                    param.Value = filterModel.StartDate;
                    paramsList.Add(param);
                }
                if (filterModel.EndDate != null)
                {
                    SqlParameter param = new SqlParameter("@EndDate", SqlDbType.DateTime);
                    param.Value = filterModel.EndDate;
                    paramsList.Add(param);
                }

                response = await ExecuteStoredProcedure_NonMedicalReferralReport("[Report].[MedicalNonReferralReport]", paramsList);
                return response;
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return response;
        }

        public async Task<List<NonMedicalReferralReport>> ExecuteStoredProcedure_NonMedicalReferralReport(string storedProcedureName, IEnumerable<SqlParameter> parameters)
        {
            List<NonMedicalReferralReport> reports = null;
            var dbConnection = (_readOnlyDbContext.Database.GetDbConnection());
            dbConnection.Open();
            using (var cmd = dbConnection.CreateCommand())
            {
                cmd.CommandText = storedProcedureName;
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 1800;
                foreach (var parameter in parameters)
                {
                    cmd.Parameters.Add(parameter);
                }

                var dataReader = await cmd.ExecuteReaderAsync();
                var dataTable = new DataTable();
                dataTable.Load(dataReader);

                reports = (from rw in dataTable.AsEnumerable()
                           select new NonMedicalReferralReport
                           {
                               ProviderTypeName = Convert.ToString(rw["ProviderTypeName"]),
                               NumberofReferrals = rw["NumberofReferrals"] == DBNull.Value ? 0 : Convert.ToInt32(rw["NumberofReferrals"])
                           }).ToList();
            }
            return reports;
        }

        public async Task<long> AddEODReportNotes(EODReportNote eODReportNote)
        {
            await _updatableDBContext.AddAsync(eODReportNote);
            await _updatableDBContext.SaveChangesAsync();
            return (long)eODReportNote.Id;
        }

        public async Task UpdateEODReportNotes(EODReportNote eODReportNote)
        {
            _updatableDBContext.EODReportNotes.Update(eODReportNote);
            await _updatableDBContext.SaveChangesAsync();
            _updatableDBContext.Entry(eODReportNote).State = EntityState.Modified;
        }

        public async Task<EODReportNote> GetEODReportNotes(int orgId, DateTime paymentDate)
        {
            return await _readOnlyDbContext.EODReportNotes.FirstOrDefaultAsync(x => x.OrgId == orgId
                                                          && x.PaymentDate.Date == paymentDate.Date
                                                          && x.StatusId != (short)Status.Deleted);
        }

    }
}
