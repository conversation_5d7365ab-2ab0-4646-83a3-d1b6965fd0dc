﻿using Capstone2.Framework.Business.Common;
using Capstone2.Framework.RestApi;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.Report.Interfaces;
using Capstone2.RestServices.Report.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
//using static Microsoft.Azure.Amqp.Serialization.SerializableType;

namespace Capstone2.RestServices.Report.Services
{
    public class ReportBAL : IReportBAL
    {
        public IReportDAL _reportDAL;
        public readonly AppSettings _appSettings;
        private readonly ILogger<ReportBAL> _logger;

        public ReportBAL(IReportDAL reportDAL, IOptions<AppSettings> appSettings, ILogger<ReportBAL> logger)
        {
            _reportDAL = reportDAL;
            _appSettings = appSettings.Value;
            _logger = logger;

        }
        /// <summary>
        /// Method to get the total amount of EODReport
        /// </summary>
        /// <param name="paymentDate"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<EODReportTotal>> GetEODReportTotal(QueryModel queryModel, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<EODReportTotal> apiResponse = new();
            int orgId = baseHttpRequestContext.OrgId;
            List<int> permissionsIds = new();
            EODReportFilterModel filterModel = PrepareFilterParameters(queryModel.Filter);
            if (filterModel.Reconcile != null)
            {
                permissionsIds.Add(21);
            }
            else
            {
                permissionsIds.Add(20);
            }
            List<RolesPermissionsAssoc> permissions = await _reportDAL.GetPermissionsOnRoleId(baseHttpRequestContext.OrgId, baseHttpRequestContext.RoleId, permissionsIds);

            if (permissions is null || permissions.Count == 0)
            {
                apiResponse.StatusCode = StatusCodes.Status403Forbidden;
                apiResponse.Errors.Add("Not an Authorized User");
                return apiResponse;
            }
            foreach (var permission in permissions)
            {

                if (permission.IsAllowed == false)
                {
                    apiResponse.StatusCode = StatusCodes.Status403Forbidden;
                    apiResponse.Errors.Add("Not an Authorized User");
                    return apiResponse;
                }
            }
            EODReportTotal eodReportAmount = await _reportDAL.GetEODReportTotal(orgId, filterModel);

            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result = eodReportAmount;
            return apiResponse;
        }

        /// <summary>
        /// Method to get the total amount of Provider's income
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<ProviderIncomeReportTotal>> GetProviderIncomeTotal(QueryModel queryModel, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<ProviderIncomeReportTotal> apiResponse = new();
            ProviderIncomeReportTotal providerIncomeReportTotal = new ProviderIncomeReportTotal();
            int orgId = baseHttpRequestContext.OrgId;
            ProviderIncomeReportFilterModel filterModel = PrepareProviderIncomeReportFilterParameters(queryModel.Filter);
            List<ProviderIncomeReport> providerIncomeReportList = await _reportDAL.GetProviderIncomeTotal(orgId, filterModel);
            OrganisationView orgDetails = await FetchMasterCompanyDetails(baseHttpRequestContext);
            short timeZoneId = (orgDetails is null || orgDetails.TimeZoneMasterId <= 0) ? default(short) : orgDetails.TimeZoneMasterId;
            DateTime baseDate;
            if (timeZoneId > 0)
            {
                DateTime? CompanyDateTimeNow = GetDateFromOrganisation(orgDetails);
                if (CompanyDateTimeNow is not null && CompanyDateTimeNow > default(DateTime))
                {
                    baseDate = (DateTime)CompanyDateTimeNow;
                    var today = baseDate.Date;

                    var thisWeekStart = today.AddDays(-(int)today.DayOfWeek);
                    var thisWeekEnd = thisWeekStart.AddDays(7).AddSeconds(-1);
                    var thisMonthStart = today.AddDays(1 - today.Day);
                    var thisMonthEnd = thisMonthStart.AddMonths(1).AddSeconds(-1);

                    if (providerIncomeReportList != null && providerIncomeReportList.Count > 0)
                    {
                        providerIncomeReportTotal = new ProviderIncomeReportTotal
                        {
                            TodayIncome = providerIncomeReportList.Where(x => x.PaymentDate == today).Select(x => (decimal)x.ReceiptedAmount).Sum(),
                            WeekIncome = providerIncomeReportList.Where(x => x.PaymentDate >= thisWeekStart && x.PaymentDate <= thisWeekEnd).Select(x => (decimal)x.ReceiptedAmount).Sum(),
                            MonthIncome = providerIncomeReportList.Where(x => x.PaymentDate >= thisMonthStart && x.PaymentDate <= thisMonthEnd).Select(x => (decimal)x.ReceiptedAmount).Sum(),
                        };
                    }
                }
                else
                {
                    apiResponse.Errors.Add("Unable to fetch Company DateTime.");
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Result = null;
                    apiResponse.Message = "Failure";

                    return apiResponse;
                }
            }

            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result = providerIncomeReportTotal;
            return apiResponse;
        }
        private DateTime? GetDateFromOrganisation(OrganisationView orgDetails)
        {
            var CompanyDateTimeNow = DateTimeConversion.ConvertTimeFromUtc(DateTime.UtcNow, orgDetails.WindowsTimeZoneData);
            if (CompanyDateTimeNow is null)
            {
                CompanyDateTimeNow = DateTimeConversion.ConvertTimeFromUtc(DateTime.UtcNow, orgDetails.LinuxTimeZoneData);
            }

            return CompanyDateTimeNow;
        }
        private async Task<OrganisationView> FetchMasterCompanyDetails(BaseHttpRequestContext baseHttpRequestContext)
        {

            ApiResponse<OrganisationView> apiResponseCompany = new();
            string endpoint = string.Format("/company/Organisation/{0}", baseHttpRequestContext.OrgId);
            string companySeviceUrl = _appSettings.ApiUrls["CompanyServiceUrl"] + endpoint;
            RestClient restClientCompany = new RestClient(companySeviceUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            apiResponseCompany = await restClientCompany.GetAsync<ApiResponse<OrganisationView>>(companySeviceUrl);
            return (apiResponseCompany == null) ? null : apiResponseCompany.Result;

        }
        /// <summary>
        /// Method to list all the payments at EOD
        /// </summary>
        /// <param name="queryModel"></param>
        /// <param name="paymentDate"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<QueryResultList<Eodreport>>> ListPaymentTransactions(QueryModel queryModel, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<QueryResultList<Eodreport>> apiResponse = new();
            int orgId = baseHttpRequestContext.OrgId;
            EODReportFilterModel filterModel = PrepareFilterParameters(queryModel.Filter);
            List<int> permissionsIds = new();
            if (filterModel.Reconcile != null)
            {
                permissionsIds.Add(21);
            }
            else
            {
                permissionsIds.Add(20);
            }
            List<RolesPermissionsAssoc> permissions = await _reportDAL.GetPermissionsOnRoleId(baseHttpRequestContext.OrgId, baseHttpRequestContext.RoleId, permissionsIds);

            if (permissions is null || permissions.Count == 0)
            {
                apiResponse.StatusCode = StatusCodes.Status403Forbidden;
                apiResponse.Errors.Add("Not an Authorized User");
                return apiResponse;
            }
            foreach (var permission in permissions)
            {

                if (permission.IsAllowed == false)
                {
                    apiResponse.StatusCode = StatusCodes.Status403Forbidden;
                    apiResponse.Errors.Add("Not an Authorized User");
                    return apiResponse;
                }
            }
            QueryResultList<Eodreport> paymentQueryList = await _reportDAL.ListPaymentTransactions(orgId, queryModel, filterModel);
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result = paymentQueryList;
            return apiResponse;
        }

        /// <summary>
        /// Method to list all the payments grouped by entity and payment method type
        /// </summary>
        /// <param name="queryModel"></param>
        /// <param name="paymentDate"></param>
        /// <param name="baseHttpRequestContext"></param> 
        /// <returns></returns>
        public async Task<ApiResponse<dynamic>> ListGroupedPayments(QueryModel queryModel, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<dynamic> apiResponse = new();
            int orgId = baseHttpRequestContext.OrgId;
            EODReportFilterModel filterModel = PrepareFilterParameters(queryModel.Filter);
            QueryResultList<Eodreport> paymentQueryList = await _reportDAL.ListGroupedPayments(orgId, queryModel, filterModel);
            var records = paymentQueryList.ItemRecords;
            if (records != null)
            {
                var paymentRecords = (from r in records
                                      group r by (r.CompanyName, r.CompanyDetailsId, r.PaymentMethodType) into g
                                      select new ReconcileEODReport
                                      {
                                          CompanyName = g.Key.CompanyName,
                                          CompanyDetailsId = g.Key.CompanyDetailsId,
                                          PaymentMthodType = g.Key.PaymentMethodType,
                                          //Amount = g.Sum(r1 => (decimal)r1.Amount),
                                          CRAmount = g.Sum(cr => (decimal)cr.CRAmount),
                                          DRAmount = g.Sum(dr => (decimal)dr.DRAmount),
                                          Ids = g.Select(r1 => r1.PaymentDetailsId).ToList(),
                                          Surcharge = g.Sum(s => (decimal)s.Surcharge)
                                      });
                apiResponse.Result = paymentRecords.ToList();
            }
            else
                apiResponse.Result = new List<ReconcileEODReport>();

            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";

            return apiResponse;
        }

        /// <summary>
        /// Method to list all the provider income on a given date
        /// </summary>
        /// <param name="queryModel"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<dynamic>> ListProviderIncome(QueryModel queryModel, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<dynamic> apiResponse = new();
            int orgId = baseHttpRequestContext.OrgId;
            string timeZone = string.Empty;
            ProviderIncomeReportFilterModel filterModel = PrepareProviderIncomeReportFilterParameters(queryModel.Filter);

            OrganisationView orgDetails = await FetchMasterCompanyDetails(baseHttpRequestContext);
            bool isWindows = System.Runtime.InteropServices.RuntimeInformation.IsOSPlatform(OSPlatform.Windows);

            timeZone = (isWindows) ? EnumExtensions.GetDescription((WindowsTimeZone)(orgDetails.TimeZoneMasterId)) : EnumExtensions.GetDescription((LinuxTimeZone)(orgDetails.TimeZoneMasterId));
            DateTime StartDateUTC = (DateTime)DateTimeConversion.ConvertTimeToUtc((DateTime)filterModel.StartDate, timeZone);

            DateTime EndDateForUTC = filterModel.EndDate.Value.Date.AddDays(1).AddMinutes(-1);
            DateTime EndDateUTC = (DateTime)DateTimeConversion.ConvertTimeToUtc(EndDateForUTC, timeZone);

            QueryResultList<ProviderIncomeReport> paymentQueryList = await _reportDAL.ListProviderIncome(orgId, queryModel, filterModel, StartDateUTC, EndDateUTC);
            var records = paymentQueryList.ItemRecords;
            if (records != null)
            {
                List<ProviderIncomeTotal> providerIncomeTotalList = new();
                ProviderIncomeTotal invoiceTypeTotal = new ProviderIncomeTotal
                {

                    ProviderIncomeDetails = (from r in records
                                             where r.InvoiceTypeId == (short)InvoiceType.Invoice
                                             group r by (r.InvoiceDetailsId) into g
                                             select new ProviderIncomeInvoiceType
                                             {

                                                 PatientName = g.Select(r => r.PatientName).FirstOrDefault(),
                                                 InvoicedAmount = g.Select(r => (decimal)r.InvoicedAmount).FirstOrDefault(),
                                                 OutstandingAmount = g.Select(r => (decimal)r.OutstandingAmount).FirstOrDefault(),
                                                 ReceiptedAmount = g.Sum(r => (decimal)r.ReceiptedAmount),
                                             }).ToList(),
                    InvoiceType = "Invoice"
                };

                if (invoiceTypeTotal is not null)
                {
                    invoiceTypeTotal.TotalInvoiced = invoiceTypeTotal.ProviderIncomeDetails.Sum(r => (decimal)r.InvoicedAmount);
                    invoiceTypeTotal.TotalOutstanding = invoiceTypeTotal.ProviderIncomeDetails.Sum(r => (decimal)r.OutstandingAmount);
                    invoiceTypeTotal.TotalReceipted = invoiceTypeTotal.ProviderIncomeDetails.Sum(r => (decimal)r.ReceiptedAmount);
                }

                ProviderIncomeTotal depositTypeTotal = new ProviderIncomeTotal
                {

                    ProviderIncomeDetails = (from r in records
                                             where r.InvoiceTypeId == (short)InvoiceType.Estimate
                                             group r by (r.InvoiceDetailsId) into g
                                             select new ProviderIncomeInvoiceType
                                             {

                                                 PatientName = g.Select(r => r.PatientName).FirstOrDefault(),
                                                 InvoicedAmount = g.Select(r => (decimal)r.InvoicedAmount).FirstOrDefault(),
                                                 OutstandingAmount = g.Select(r => (decimal)r.OutstandingAmount).FirstOrDefault(),
                                                 ReceiptedAmount = g.Sum(r => (decimal)r.ReceiptedAmount),
                                             }).ToList(),
                    InvoiceType = "Deposit"
                };

                if (depositTypeTotal is not null)
                {
                    depositTypeTotal.TotalInvoiced = depositTypeTotal.ProviderIncomeDetails.Sum(r => (decimal)r.InvoicedAmount);
                    depositTypeTotal.TotalOutstanding = depositTypeTotal.ProviderIncomeDetails.Sum(r => (decimal)r.OutstandingAmount);
                    depositTypeTotal.TotalReceipted = depositTypeTotal.ProviderIncomeDetails.Sum(r => (decimal)r.ReceiptedAmount);

                }
                providerIncomeTotalList.Add(invoiceTypeTotal);
                providerIncomeTotalList.Add(depositTypeTotal);
                //var paymentRecords = (from r in records
                //                      group r by (r.InvoiceTypeId) into g
                //                      select new ProviderIncomeTotal
                //                      {

                //                          ProviderIncomeDetails = g.GroupBy(I).Select(r => r).ToList(),
                //                          TotalInvoiced = g.Sum(r => (decimal)r.InvoicedAmount),
                //                          TotalOutstanding = g.Sum(r => (decimal)r.OutstandingAmount),
                //                          TotalReceipted = g.Sum(r => (decimal)r.ReceiptedAmount),
                //                          InvoiceType = g.Select(r => r.InvoiceTypeId).FirstOrDefault() == (short)InvoiceType.Invoice ? "Invoice" : "Deposit"
                //                      });
                apiResponse.Result = providerIncomeTotalList;
            }
            else
                apiResponse.Result = new List<ProviderIncomeTotal>();

            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";

            return apiResponse;
        }

        private EODReportFilterModel PrepareFilterParameters(string filter)
        {
            if (!(string.IsNullOrEmpty(filter)))
            {
                return JsonConvert.DeserializeObject<EODReportFilterModel>(filter);
            }
            else
            {
                return null;
            }
        }
        private ProviderIncomeReportFilterModel PrepareProviderIncomeReportFilterParameters(string filter)
        {
            if (!(string.IsNullOrEmpty(filter)))
            {
                return JsonConvert.DeserializeObject<ProviderIncomeReportFilterModel>(filter);
            }
            else
            {
                return null;
            }
        }

        public async Task<ApiResponse<QueryResultList<RevenueReport>>> ListRevenueReport(QueryModel queryModel, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<QueryResultList<RevenueReport>> apiResponse = new();
            RevenueReportFilterModel filterModel = PrepareRevenueReportFilterParameters(queryModel.Filter);

            QueryResultList<RevenueReport> lstRevenueReport = await _reportDAL.ListRevenueReport(queryModel, filterModel, baseHttpRequestContext.OrgId);
            apiResponse.Result = lstRevenueReport;
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            return apiResponse;
        }

        private RevenueReportFilterModel PrepareRevenueReportFilterParameters(string filter)
        {
            if (!(string.IsNullOrEmpty(filter)))
            {
                return JsonConvert.DeserializeObject<RevenueReportFilterModel>(filter);
            }
            else
            {
                return null;
            }
        }
        private SalesReportFilterModel PrepareSalesReportFilterParameters(string filter)
        {
            if (!(string.IsNullOrEmpty(filter)))
            {
                return JsonConvert.DeserializeObject<SalesReportFilterModel>(filter);
            }
            else
            {
                return null;
            }
        }

        private BusinessActivityFilter PrepareBusinessActivityFilterParameters(string filter)
        {
            if (!(string.IsNullOrEmpty(filter)))
            {
                return JsonConvert.DeserializeObject<BusinessActivityFilter>(filter);
            }
            else
            {
                return null;
            }
        }
        public async Task<ApiResponse<RevenueReportGroupModel>> ListRevenueReportGroupByCompany(QueryModel queryModel, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<RevenueReportGroupModel> apiResponse = new();

            RevenueReportFilterModel filterModel = PrepareRevenueReportFilterParameters(queryModel.Filter);
            List<int> permissionsIds = new();

            if (filterModel.IsProviderScreen != null && filterModel.IsProviderScreen == true)
            {
                permissionsIds.Add(97);
            }
            else if (filterModel.IsProviderScreen != null && filterModel.IsProviderScreen == false)
            {
                permissionsIds.Add(61);
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status403Forbidden;
                apiResponse.Errors.Add("IsProviderScreen filter is not provided.Not an Authorized User");
                return apiResponse;
            }

            List<RolesPermissionsAssoc> permissions = await _reportDAL.GetPermissionsOnRoleId(baseHttpRequestContext.OrgId, baseHttpRequestContext.RoleId, permissionsIds);

            if (permissions is null || permissions.Count == 0)
            {
                apiResponse.StatusCode = StatusCodes.Status403Forbidden;
                apiResponse.Errors.Add("Not an Authorized User");
                return apiResponse;
            }
            foreach (var permission in permissions)
            {

                if (permission.IsAllowed == false)
                {
                    apiResponse.StatusCode = StatusCodes.Status403Forbidden;
                    apiResponse.Errors.Add("Not an Authorized User");
                    return apiResponse;
                }
            }

            List<RevenueReport> lstRevenueReport = await _reportDAL.ListRevenueReportWithFilters(queryModel, filterModel, baseHttpRequestContext.OrgId);

            //RevenueReportGroupModel revenueReportGroupModel = new();
            //lstRevenueReport?.GroupBy(x=>x.CompanyDetailsId)
            if (lstRevenueReport != null)
            {
                var groupByTypeTotAmt = (from report in lstRevenueReport
                                             //where invoice.TotalAmount != null && invoice.TotalAmount != 0
                                         group report by report.CompanyDetailsId into compGroup
                                         select new CompanyRevenueReport()
                                         {
                                             CompanyDetailsId = (int)compGroup.Key,
                                             Income = compGroup.Sum(s => (decimal)s.Fee),
                                             CompanyName = compGroup.Select(x => x.CompanyName).FirstOrDefault(),
                                             Gst = compGroup.Sum(s => (decimal)s.Gst)
                                         }).ToList();

                decimal totalFee = (groupByTypeTotAmt?.Sum(x => x.Income) is not null) ? (decimal)groupByTypeTotAmt?.Sum(x => x.Income) : 0;
                decimal gst = (groupByTypeTotAmt?.Sum(x => x.Gst) is not null) ? (decimal)groupByTypeTotAmt?.Sum(x => x.Gst) : 0;

                groupByTypeTotAmt?.ForEach(x =>
                {
                    x.PctIncome = Math.Round((decimal)x.Income / totalFee * 100, 2);
                });

                RevenueReportGroupModel revenueReportGroupModel = new()
                {
                    CompanyRevenueReports = groupByTypeTotAmt,
                    Gst = gst,
                    TotalFeeIncGst = totalFee
                };
                apiResponse.Result = revenueReportGroupModel;

            }
            else
            {
                apiResponse.Result = null;

            }
            apiResponse.Message = "Success";
            apiResponse.StatusCode = StatusCodes.Status200OK;
            return apiResponse;
        }


        public async Task<ApiResponse<List<SalesReportGroupModel>>> ListSalesReportGroupByCompanyEpisode(QueryModel queryModel, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<List<SalesReportGroupModel>> apiResponse = new();
            List<int> permissionsIds = new();

            SalesReportFilterModel filterModel = PrepareSalesReportFilterParameters(queryModel.Filter);
            if (filterModel.IsProviderScreen != null && filterModel.IsProviderScreen == true)
            {
                permissionsIds.Add(97);
            }
            else if (filterModel.IsProviderScreen != null && filterModel.IsProviderScreen == false)
            {
                permissionsIds.Add(61);
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status403Forbidden;
                apiResponse.Errors.Add("IsProviderScreen filter is not provided.Not an Authorized User");
                return apiResponse;
            }

            List<RolesPermissionsAssoc> permissions = await _reportDAL.GetPermissionsOnRoleId(baseHttpRequestContext.OrgId, baseHttpRequestContext.RoleId, permissionsIds);

            if (permissions is null || permissions.Count == 0)
            {
                apiResponse.StatusCode = StatusCodes.Status403Forbidden;
                apiResponse.Errors.Add("Not an Authorized User");
                return apiResponse;
            }
            foreach (var permission in permissions)
            {

                if (permission.IsAllowed == false)
                {
                    apiResponse.StatusCode = StatusCodes.Status403Forbidden;
                    apiResponse.Errors.Add("Not an Authorized User");
                    return apiResponse;
                }
            }

            List<SalesReportGroupModel> lstSalesReport = await _reportDAL.ListSalesReportWithFilters2(queryModel, filterModel, baseHttpRequestContext.OrgId);

            //RevenueReportGroupModel revenueReportGroupModel = new();
            //lstRevenueReport?.GroupBy(x=>x.CompanyDetailsId)
            if (lstSalesReport != null)
            {

                List<short> lvl1Ids = new();
                lstSalesReport.ForEach(x =>
                {
                    lvl1Ids.AddRange(x.CompanySalesReports.Where(x => x.EpisodeTypeId == (short)EpisodeTypes.MBS && x.Level1Id != null).Select(x => (short)x.Level1Id).ToList());
                });



                if (lvl1Ids != null && lvl1Ids.Count > 0)
                {
                    string filter = "{MbsLvl1Ids:[" + string.Join(",", lvl1Ids) + "]}";
                    string encodedFilter = Base64EncoderHelper.EncodeQueryModelFilterParam(filter);
                    string medicalScheduleApiUrl = _appSettings.ApiUrls["MedicalScheduleServiceUrl"] + "/MedicalSchedule/clinicalCategoryData?ps=" + lvl1Ids.Count + "&pn=" + 1 + "&st=" + "" + "&f=" + encodedFilter;
                    RestClient restClient = new RestClient(medicalScheduleApiUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
                    var medicalScheduleApiResponse = await restClient.GetAsync<ApiResponse<QueryResultList<ClinicalCategory>>>(medicalScheduleApiUrl);
                    List<ClinicalCategory> clinicalCategoriesList = new();
                    if (medicalScheduleApiResponse.StatusCode == StatusCodes.Status200OK && medicalScheduleApiResponse.Result is not null && medicalScheduleApiResponse.Result.CurrentCount > 0)
                    {
                        QueryResultList<ClinicalCategory> paginatedList = medicalScheduleApiResponse.Result;
                        clinicalCategoriesList = paginatedList.ItemRecords.ToList();

                        if (clinicalCategoriesList != null && clinicalCategoriesList.Count > 0)
                        {
                            lstSalesReport.ForEach(x =>
                            {
                                x.CompanySalesReports?.ForEach(report =>
                                {
                                    if (report.EpisodeTypeId == (short)EpisodeTypes.MBS)
                                        report.Level1 = $"(MBS) {clinicalCategoriesList.Where(x => x.Id == report.Level1Id).Select(a => a.Name).FirstOrDefault()}";
                                });
                            });
                        }



                    }


                }

                apiResponse.Result = lstSalesReport;
            }
            else
            {
                apiResponse.Result = null;

            }
            apiResponse.Message = "Success";
            apiResponse.StatusCode = StatusCodes.Status200OK;
            return apiResponse;
        }


        public async Task<ApiResponse<BusinessActivity>> BusinessActivityCashBAL(QueryModel queryModel, BaseHttpRequestContext baseHttpRequestContext)
        {

            ApiResponse<BusinessActivity> apiResponse = new();
            List<int> permissionsIds = new();
            BusinessActivityFilter filterModel = PrepareBusinessActivityFilterParameters(queryModel.Filter);

            if (filterModel.IsProviderScreen != null && filterModel.IsProviderScreen == true)
            {
                permissionsIds.Add(97);
            }
            else if (filterModel.IsProviderScreen != null && filterModel.IsProviderScreen == false)
            {
                permissionsIds.Add(61);
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status403Forbidden;
                apiResponse.Errors.Add("IsProviderScreen filter is not provided.Not an Authorized User");
                return apiResponse;
            }

            List<RolesPermissionsAssoc> permissions = await _reportDAL.GetPermissionsOnRoleId(baseHttpRequestContext.OrgId, baseHttpRequestContext.RoleId, permissionsIds);

            if (permissions is null || permissions.Count == 0)
            {
                apiResponse.StatusCode = StatusCodes.Status403Forbidden;
                apiResponse.Errors.Add("Not an Authorized User");
                return apiResponse;
            }
            foreach (var permission in permissions)
            {

                if (permission.IsAllowed == false)
                {
                    apiResponse.StatusCode = StatusCodes.Status403Forbidden;
                    apiResponse.Errors.Add("Not an Authorized User");
                    return apiResponse;
                }
            }




            BusinessActivity cashForYear = await _reportDAL.BusinessActivityCashDAL(queryModel, filterModel, baseHttpRequestContext.OrgId);


            //RevenueReportGroupModel revenueReportGroupModel = new();
            //lstRevenueReport?.GroupBy(x=>x.CompanyDetailsId)
            if (cashForYear != null)
            {
                if (cashForYear.Total <= 0)
                {
                    cashForYear.Total = cashForYear.Q1Sum + cashForYear.Q2Sum + cashForYear.Q3Sum + cashForYear.Q4Sum;
                }
                apiResponse.Result = cashForYear;
                apiResponse.Message = "Success";
                apiResponse.StatusCode = StatusCodes.Status200OK;
                return apiResponse;
            }
            else
            {

                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Errors.Add("Error while Fetching Data from DB");
                return apiResponse;
            }
        }
        public async Task<ApiResponse<List<MedicalReferralReport>>> ListMedicalReferralReport(QueryModel queryModel, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<List<MedicalReferralReport>> apiResponse = new();
            MedicalReferralReportFilterModel filterModel = null;
            if (!string.IsNullOrEmpty(queryModel.Filter))
            {
                filterModel = JsonConvert.DeserializeObject<MedicalReferralReportFilterModel>(queryModel.Filter);
            }

            List<MedicalReferralReport> lstMedicalReferralReport = await _reportDAL.ListMedicalReferralReport(filterModel, baseHttpRequestContext.OrgId);
            apiResponse.Result = lstMedicalReferralReport;
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            return apiResponse;
        }

        public async Task<ApiResponse<List<NonMedicalReferralReport>>> ListNonMedicalReferralReport(QueryModel queryModel, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<List<NonMedicalReferralReport>> apiResponse = new();
            NonMedicalReferralReportFilterModel filterModel = null;
            if (!string.IsNullOrEmpty(queryModel.Filter))
            {
                filterModel = JsonConvert.DeserializeObject<NonMedicalReferralReportFilterModel>(queryModel.Filter);
            }

            List<NonMedicalReferralReport> lstMedicalReferralReport = await _reportDAL.ListNonMedicalReferralReport(filterModel, baseHttpRequestContext.OrgId);
            apiResponse.Result = lstMedicalReferralReport;
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            return apiResponse;
        }

        public async Task<ApiResponse<long?>> AddEODReportNotes(EODReportNote eODReportNote, BaseHttpRequestContext baseHttpRequestContext)
        {
            eODReportNote.OrgId = baseHttpRequestContext.OrgId;
            eODReportNote.CreatedBy = baseHttpRequestContext.UserId;
            eODReportNote.CreatedDate = DateTime.UtcNow;
            eODReportNote.StatusId = (short)Status.Active;

            var id = await _reportDAL.AddEODReportNotes(eODReportNote);
            if (id > 0)
            {
                ApiResponse<long?> apiResponse = new()
                {
                    StatusCode = StatusCodes.Status200OK,
                    Message = "Success",
                    Result = id
                };
                return apiResponse;
            }
            else
            {
                ApiResponse<long?> apiResponse = new()
                {
                    StatusCode = StatusCodes.Status400BadRequest,
                    Message = "Failure",
                    Result = null
                };
                return apiResponse;
            }
        }

        public async Task<ApiResponse<string>> UpdateEODReportNotes(EODReportNote eodReportNote, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<string> apiResponse = new();
            var dbEODReportNote = await _reportDAL.GetEODReportNotes(baseHttpRequestContext.OrgId, eodReportNote.PaymentDate);
            if (dbEODReportNote != null && eodReportNote != null)
            {
                dbEODReportNote.Notes = eodReportNote.Notes;
                dbEODReportNote.ModifiedBy = baseHttpRequestContext.UserId;
                dbEODReportNote.ModifiedDate = DateTime.UtcNow;

                await _reportDAL.UpdateEODReportNotes(dbEODReportNote);

                apiResponse.Result = "Success";
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";
            }
            else
            {
                apiResponse.Result = null;
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failure";
            }

            return apiResponse;
        }

        public async Task<ApiResponse<EODReportNote>> GetEODReportNotes(string paymentDate, BaseHttpRequestContext baseHttpRequestContext)
        {
            if (string.IsNullOrWhiteSpace(paymentDate) || !DateTime.TryParse(paymentDate, out var parsedPaymentDate))
            {
                return new ApiResponse<EODReportNote>
                {
                    StatusCode = StatusCodes.Status400BadRequest,
                    Message = "Invalid or missing payment date",
                    Result = null
                };
            }

            var eodReportNote = await _reportDAL.GetEODReportNotes(baseHttpRequestContext.OrgId, parsedPaymentDate);

            return new ApiResponse<EODReportNote>
            {
                StatusCode = StatusCodes.Status200OK,
                Message = "Success",
                Result = eodReportNote
            };
        }

    }
}
