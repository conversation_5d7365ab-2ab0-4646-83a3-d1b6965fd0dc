﻿using Capstone2.RestServices.Report.Context;
using Capstone2.RestServices.Report.Interfaces;
using Capstone2.RestServices.Report.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Enum;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Report.Services
{
    public class ProcessingReportDAL : IProcessingReportDAL
    {
        public readonly ReadOnlyReportDBContext _readOnlyDbContext;
        public UpdatableReportDBContext _updatableDBContext;

        public ProcessingReportDAL(ReadOnlyReportDBContext readOnlyDbContext, UpdatableReportDBContext updatableDBContext)
        {
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
        }

        /// <summary>
        /// Method to list all the processing report based on filter
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="queryModel"></param>
        /// <param name="filterModel"></param>
        /// <returns></returns>
        public async Task<QueryResultList<ProcessingReport>> GetProcessingReportList(int orgId, QueryModel queryModel, ProcessingReportFilterModel filterModel)
        {
            IQueryable<ProcessingReport> processingReportQuery = from processingReport in _readOnlyDbContext.ProcessingReport
                                                                 where processingReport.OrgId == orgId
                                                                 && processingReport.StatusId == (short)Status.Active
                                                                 select new ProcessingReport
                                                                 {
                                                                     Id = processingReport.Id,
                                                                     OrgId = processingReport.OrgId,
                                                                     TransactionId = processingReport.TransactionId,
                                                                     ProcessingDate = processingReport.ProcessingDate,
                                                                     ClaimTypeId = processingReport.ClaimTypeId,
                                                                     ClaimType = processingReport.ClaimType,
                                                                     ClaimId = processingReport.ClaimId,
                                                                     ProviderId = processingReport.ProviderId,
                                                                     ProviderName = processingReport.ProviderName,
                                                                     ServiceProviderNumber = processingReport.ServiceProviderNumber,
                                                                     PayeeProviderName = processingReport.PayeeProviderName,
                                                                     PayeeProviderId = processingReport.PayeeProviderId,
                                                                     PayeeProviderNumber = processingReport.PayeeProviderNumber,
                                                                     BatchStatus = processingReport.BatchStatus,
                                                                     ProcessingStatus = processingReport.ProcessingStatus,
                                                                     ProcessingSubStatus = processingReport.ProcessingSubStatus,
                                                                     ProcessingStatusCode = processingReport.ProcessingStatusCode,
                                                                     ProcessedDate = processingReport.ProcessedDate,
                                                                     PaymentStatus = processingReport.PaymentStatus,
                                                                     PaymentStatusCode = processingReport.PaymentStatusCode,
                                                                     PaymentDate = processingReport.PaymentDate,
                                                                     PaymentTypeId = processingReport.PaymentTypeId,
                                                                     ErrorCode = processingReport.ErrorCode,
                                                                     ErrorText = processingReport.ErrorText,
                                                                     ClaimStatusId = processingReport.ClaimStatusId,
                                                                     StatusId = processingReport.StatusId,
                                                                     CreatedBy = processingReport.CreatedBy,
                                                                     CreatedDate = processingReport.CreatedDate,
                                                                     ModifiedDate = processingReport.ModifiedDate,
                                                                     ModifiedBy = processingReport.ModifiedBy
                                                                 };


            if (filterModel != null)
            {
                if (filterModel.StartDate != null && filterModel.EndDate != null)
                {
                    processingReportQuery = processingReportQuery.Where(x => x.ProcessingDate >= filterModel.StartDate && x.ProcessingDate <= filterModel.EndDate);
                }

                if (!string.IsNullOrWhiteSpace(filterModel.ProcessingStatus))
                {
                    processingReportQuery = processingReportQuery.Where(x => x.ProcessingStatusCode == Convert.ToInt16(filterModel.ProcessingStatus));
                }

                if (!string.IsNullOrWhiteSpace(filterModel.PaymentStatus))
                {
                    processingReportQuery = processingReportQuery.Where(x => x.PaymentStatusCode == Convert.ToInt16(filterModel.PaymentStatus));
                }

                if (!string.IsNullOrWhiteSpace(filterModel.ClaimTypeId))
                {
                    processingReportQuery = processingReportQuery.Where(x => x.ClaimTypeId == Convert.ToInt16(filterModel.ClaimTypeId));
                }

                if (!string.IsNullOrWhiteSpace(filterModel.InvoiceNumber))
                {
                    processingReportQuery = (from invoiceProcessing in _readOnlyDbContext.InvoiceMedicareAssocsProcessing
                                             join prQuery in processingReportQuery on invoiceProcessing.ProcessingReportId equals prQuery.Id
                                             where (invoiceProcessing.InvoiceSummaryRecordId!=null && invoiceProcessing.InvoiceSummaryRecordId.ToString().Contains(filterModel.InvoiceNumber))
                                             || (invoiceProcessing.InvoiceRecordId!=null && invoiceProcessing.InvoiceRecordId.Value.ToString().Contains(filterModel.InvoiceNumber))
                                             select prQuery);
                }
            }

            processingReportQuery = SortProcessingReport(processingReportQuery, queryModel.SortTerm, queryModel.SortOrder);
            QueryResultList<ProcessingReport> paginatedList = await PaginatedResultListForProcessingReport(processingReportQuery, queryModel);

            return paginatedList;
        }

        private IQueryable<ProcessingReport> SortProcessingReport(IQueryable<ProcessingReport> processingReportQuery, string sortTerm, string sortOrder)
        {
            sortTerm = (sortTerm is null) ? string.Empty : sortTerm;
            sortOrder = (sortOrder is null) ? string.Empty : sortOrder;
            switch (sortTerm.ToLower())
            {
                case "providername":
                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            processingReportQuery = processingReportQuery.OrderBy(x => x.ProviderName).ThenByDescending(x => x.CreatedDate);
                        }
                        else
                        {
                            processingReportQuery = processingReportQuery.OrderByDescending(x => x.ProviderName).ThenByDescending(x => x.CreatedDate);
                        }
                        break;
                    }
                case "providerid":
                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            processingReportQuery = processingReportQuery.OrderBy(x => x.ProviderId).ThenByDescending(x => x.CreatedDate);
                        }
                        else
                        {
                            processingReportQuery = processingReportQuery.OrderByDescending(x => x.ProviderId).ThenByDescending(x => x.CreatedDate);
                        }
                        break;
                    }
                default:
                    processingReportQuery = processingReportQuery.OrderByDescending(x => x.CreatedDate);

                    break;
            }
            return processingReportQuery;
        }


        private async Task<QueryResultList<ProcessingReport>> PaginatedResultListForProcessingReport(IQueryable<ProcessingReport> processingReportQuery, QueryModel queryModel)
        {
            QueryResultList<ProcessingReport> queryList = new QueryResultList<ProcessingReport>();
            List<ProcessingReport> paginatedList = null;
            if (queryModel.PageNumber > 0 && queryModel.PageSize > 0)
            {
                if (processingReportQuery.Any())
                {
                    paginatedList = await processingReportQuery.Skip((queryModel.PageNumber - 1) * queryModel.PageSize).Take(queryModel.PageSize).ToListAsync();
                }
            }

            if (paginatedList != null)
            {
                queryList.ItemRecords = paginatedList;
                queryList.PageNumber = queryModel.PageNumber;
                queryList.PageSize = queryModel.PageSize;
                queryList.CurrentCount = paginatedList.Count();
            }
            else
            {
                queryList.ItemRecords = null;
                queryList.CurrentCount = 0;
            }

            queryList.TotalCount = processingReportQuery.Count();
            queryList.PageNumber = queryModel.PageNumber;
            queryList.PageSize = queryModel.PageSize;

            return queryList;
        }

        public async Task<ProcessingReport> GetProcessingReportDetails(int orgId, string transactionId)
        {
            ProcessingReport processingReport = await (from procReport in _readOnlyDbContext.ProcessingReport.Include(x => x.InvoiceMedicareAssocsProcessings)
                                                       where procReport.OrgId == orgId
                                                       && procReport.TransactionId == transactionId
                                                       select new ProcessingReport
                                                       {
                                                           Id = procReport.Id,
                                                           OrgId = procReport.OrgId,
                                                           TransactionId = procReport.TransactionId,
                                                           ProcessingDate = procReport.ProcessingDate,
                                                           ClaimTypeId = procReport.ClaimTypeId,
                                                           ClaimType = procReport.ClaimType,
                                                           ClaimId = procReport.ClaimId,
                                                           ProviderId = procReport.ProviderId,
                                                           ProviderName = procReport.ProviderName,
                                                           ServiceProviderNumber = procReport.ServiceProviderNumber,
                                                           PayeeProviderName = procReport.PayeeProviderName,
                                                           PayeeProviderId=procReport.PayeeProviderId,
                                                           PayeeProviderNumber=procReport.PayeeProviderNumber,
                                                           BatchStatus = procReport.BatchStatus,
                                                           ProcessingStatus = procReport.ProcessingStatus,
                                                           ProcessingSubStatus = procReport.ProcessingSubStatus,
                                                           ProcessingStatusCode = procReport.ProcessingStatusCode,
                                                           ProcessedDate = procReport.ProcessedDate,
                                                           PaymentStatus = procReport.PaymentStatus,
                                                           PaymentStatusCode = procReport.PaymentStatusCode,
                                                           PaymentDate = procReport.PaymentDate,
                                                           PaymentTypeId = procReport.PaymentTypeId,
                                                           ErrorCode = procReport.ErrorCode,
                                                           ErrorText = procReport.ErrorText,
                                                           StatusId = procReport.StatusId,
                                                           CreatedBy = procReport.CreatedBy,
                                                           CreatedDate = procReport.CreatedDate,
                                                           ModifiedDate = procReport.ModifiedDate,
                                                           ModifiedBy = procReport.ModifiedBy,
                                                           InvoiceMedicareAssocsProcessings = procReport.InvoiceMedicareAssocsProcessings
                                                       }).FirstOrDefaultAsync();
            return processingReport;
        }

    }
}
