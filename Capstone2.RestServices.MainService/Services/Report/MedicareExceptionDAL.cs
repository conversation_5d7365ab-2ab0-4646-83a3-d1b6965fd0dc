﻿using Capstone2.RestServices.Report.Context;
using Capstone2.RestServices.Report.Interfaces;
using Capstone2.RestServices.Report.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Enum;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Report.Services
{
    public class MedicareExceptionDAL : IMedicareExceptionDAL
    {
        public readonly ReadOnlyReportDBContext _readOnlyDbContext;
        public UpdatableReportDBContext _updatableDBContext;

        public MedicareExceptionDAL(ReadOnlyReportDBContext readOnlyDbContext, UpdatableReportDBContext updatableDBContext)
        {
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
        }

        /// <summary>
        /// this method is used for soft delete records
        /// </summary>
        /// <param name="exceptionIds"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task DeleteMedicareExceptions(long exceptionId, long userId)
        {
            IQueryable<MedicareException> query = _readOnlyDbContext.MedicareExceptions.Where(x => x.Id == exceptionId && x.StatusId == (short)Status.Active);
            await query.ForEachAsync(x =>
              {
                  x.StatusId = (short)Status.Inactive;
                  x.ModifiedDate = DateTime.UtcNow;
                  x.ModifiedBy = userId;
              });

            _updatableDBContext.MedicareExceptions.UpdateRange(query);
            await _updatableDBContext.SaveChangesAsync();
        }

        /// <summary>
        /// Method to list medicareexceptions
        /// </summary>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<QueryResultList<MedicareException>> ListMedicareExceptions(int orgId, QueryModel queryModel, MedicareExceptionFilterModel filterModel)
        {
            IQueryable<MedicareException> query = _readOnlyDbContext.MedicareExceptions.Where(x => x.OrgId == orgId && x.StatusId == (short)Status.Active);
            if (filterModel != null)
            {
                if (filterModel.StartDate != null && filterModel.EndDate != null)
                {
                    query = query.Where(x => x.CreatedDate >= filterModel.StartDate && x.CreatedDate <= filterModel.EndDate);
                }

                if (!string.IsNullOrWhiteSpace(filterModel.InvoiceNumber))
                {
                    query = query.Where(x => (x.InvoiceSummaryRecordId != null && x.InvoiceSummaryRecordId.Value.ToString().Contains(filterModel.InvoiceNumber)) || (x.InvoiceRecordId != null && x.InvoiceRecordId.Value.ToString().Contains(filterModel.InvoiceNumber)));
                }

                if (filterModel.ClaimTypeId!=null && filterModel.ClaimTypeId.Count >0)
                {
                    query = query.Where(x => filterModel.ClaimTypeId.Contains(x.ClaimTypeId));
                }
            }
            query = SortTransactions(query, queryModel.SortTerm, queryModel.SortOrder);
            QueryResultList<MedicareException> paginatedList = await PaginatedResultListForMedicareException(query, queryModel);

            return paginatedList;
        }
        private async Task<QueryResultList<MedicareException>> PaginatedResultListForMedicareException(IQueryable<MedicareException> query, QueryModel queryModel)
        {
            QueryResultList<MedicareException> queryList = new QueryResultList<MedicareException>();
            List<MedicareException> paginatedList = null;
            if (queryModel.PageNumber > 0 && queryModel.PageSize > 0)
            {
                if (query.Any())
                {
                    paginatedList = await query.Skip((queryModel.PageNumber - 1) * queryModel.PageSize)
                 .Take(queryModel.PageSize).ToListAsync();
                }
            }

            if (paginatedList != null)
            {
                queryList.ItemRecords = paginatedList;
                queryList.PageNumber = queryModel.PageNumber;
                queryList.PageSize = queryModel.PageSize;
                queryList.CurrentCount = paginatedList.Count();
            }
            else
            {
                queryList.ItemRecords = null;
                queryList.CurrentCount = 0;
            }
            queryList.TotalCount = query.Count();
            queryList.PageNumber = queryModel.PageNumber;
            queryList.PageSize = queryModel.PageSize;

            return queryList;
        }

        private IQueryable<MedicareException> SortTransactions(IQueryable<MedicareException> query, string sortTerm, string sortOrder)
        {
            sortTerm = (sortTerm is null) ? string.Empty : sortTerm;
            sortOrder = (sortOrder is null) ? string.Empty : sortOrder;
            switch (sortTerm.ToLower())
            {

                case "invoicerecordid":

                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            query = query.OrderBy(x => x.InvoiceDetailsId).ThenByDescending(x => x.CreatedDate);
                        }
                        else
                        {

                            query = query.OrderByDescending(x => x.InvoiceDetailsId).ThenByDescending(x => x.CreatedDate);

                        }
                        break;
                    }
                case "createddate":

                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            query = query.OrderBy(x => x.CreatedDate);
                        }
                        else
                        {

                            query = query.OrderByDescending(x => x.CreatedDate); ;

                        }
                        break;
                    }

                default:
                    query = query.OrderByDescending(x => x.CreatedDate);

                    break;
            }
            return query;
        }

    }
}
