﻿using Capstone2.RestServices.PatientRegistration.Context;
using Capstone2.RestServices.PatientRegistration.Interfaces;
using Capstone2.RestServices.PatientRegistration.Models;
using Capstone2.Shared.Models.Common;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using Capstone2.Shared.Models.Enum;

namespace Capstone2.RestServices.Communication.Services
{

    public class PatientRegDAL : IPatientRegDAL
    {
        public UpdatablePatientRegistrationDBContext _updatableDBContext;
        public ReadOnlyPatientRegistrationDBContext _readOnlyDBContext;
        public PatientRegDAL(UpdatablePatientRegistrationDBContext updatableDBContext, ReadOnlyPatientRegistrationDBContext readOnlyDBContext)
        {
            _updatableDBContext = updatableDBContext;
            _readOnlyDBContext = readOnlyDBContext;
        }

        public async Task<long> AddUserFactor(Factor factor)
        {
            await _updatableDBContext.Factors.AddAsync(factor);
            int rows = await _updatableDBContext.SaveChangesAsync();
            if (rows > 0)
            {
                return rows;
            }
            return rows;
        }

        public async Task<EmailTemplate> GetEmailTemplate(string emailTemplateName, int orgId)
        {
            EmailTemplate template = await _readOnlyDBContext.EmailTemplates
                .Where(x => x.TemplateName == emailTemplateName && x.OrgId == orgId && x.Status == (int)Status.Active /*&& x.TemplateType == tempType*/).FirstAsync();
            return template;
        }

        public async Task<Factor> GetFactorValue(long prId)
        {
            return await _readOnlyDBContext.Factors.Where(x => x.PatientRecordId == prId && x.StatusId == (short)Status.Active).OrderByDescending(x => x.Id).FirstOrDefaultAsync();
        }

        public async Task<SmsTemplate> GetSmsTemplateForPatReg(string templateName, int orgId)
        {
            SmsTemplate template = await _readOnlyDBContext.SmsTemplates
                .Where(x => x.TemplateName == templateName && x.OrgId == orgId &&
                x.Status == (int)Status.Active).FirstAsync();
            return template;
        }

        public async Task<ClientSecreatView> GetClientSecreatAsync(string emailId)
        {
            
                var query = from UD in _readOnlyDBContext.UserDetails
                            where UD.LoginEmail == emailId
                            join AU in _readOnlyDBContext.Users on UD.Id equals AU.Id
                            join AUI in _readOnlyDBContext.UserIdentifiers on AU.Id equals AUI.UserId
                            where AUI.IdentifierType == (short)IdentifierType.Client_Id
                            join AUF in _readOnlyDBContext.UserFactors on AUI.Id equals AUF.UserIdentifierId
                            //where AUF.FactorValue == ((short)FactorType.Client_Secret).ToString()
                            select new ClientSecreatView
                            {
                                Id = UD.Id,
                                ClientId = AUI.IdentifierValue,
                                ClientSecret = AUF.FactorValue
                            };
                return await query.FirstOrDefaultAsync();

            
        }

        public async Task<PatientTokenAssoc> GetPatientToken(long recordId)
        {
            PatientTokenAssoc userToken = await _readOnlyDBContext.PatientTokenAssocs.OrderByDescending(x => x.Id).FirstOrDefaultAsync(x => x.PatientRecordId == recordId);
            return userToken;
        }

        public async Task<long> AddPatientDraftData(PatientDraftData inputDraftData)
        {

            await _updatableDBContext.PatientDraftData.AddAsync(inputDraftData);
            int rows = await _updatableDBContext.SaveChangesAsync();
            return rows > 0 ? inputDraftData.Id : 0;
        }

        public async Task<PatientDraftData> GetPatientDraftData(int orgId, long prid)
        {
            PatientDraftData patinentData = await _readOnlyDBContext.PatientDraftData
               .Where(x => x.PatientRecordId == prid && x.OrgId == orgId &&
               x.StatusId == (int)Status.Active).FirstOrDefaultAsync();

            return patinentData;


        }

        public async Task<int> UpdatePatientDraftData(PatientDraftData inputDraftData)
        {
            _updatableDBContext.PatientDraftData.Update(inputDraftData);
            return await _updatableDBContext.SaveChangesAsync();
        }

    }

}
