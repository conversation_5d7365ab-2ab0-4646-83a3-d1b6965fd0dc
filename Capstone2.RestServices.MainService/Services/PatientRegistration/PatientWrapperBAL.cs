﻿using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.PatientRegistration.Interfaces;
using Capstone2.RestServices.PatientRegistration.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Capstone2.RestServices.PatientRegistration.Services;
using Capstone2.Framework.RestApi;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Capstone2.Framework.Business.Common;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Communication;
using System.Text;
using Capstone2.Framework.RestApi.Utility;
using System.IdentityModel.Tokens.Jwt;
using Microsoft.IdentityModel.Tokens;
using System.Security.Claims;
using Microsoft.Identity.Client;
using Microsoft.Extensions.Logging;
using Capstone2.RestServices.Communication.Services;
using Capstone2.Shared.Models.Medicare;
using Microsoft.AspNetCore.Components.Forms;
using System.Linq;

namespace Capstone2.RestServices.PatientRegistration.Services
{
    public class PatientWrapperBAL : IPatientWrapperBAL
    {
        public IPatientWrapperDAL _patientWrapperDAL;
        public readonly AppSettings _appSettings;
        private IJwtSecurityTokenHelper _jwtSecurityTokenHelper;
        public IPatientRegBAL _patientRegBAL;
        public IPatientRegDAL _patientRegDAL;
        public PatientWrapperBAL(IPatientWrapperDAL patientWrapperDAL, IOptions<AppSettings> appSettings, IJwtSecurityTokenHelper jwtSecurityTokenHelper,IPatientRegBAL patientRegBAL,IPatientRegDAL patientRegDAL)
        {
            _patientRegBAL = patientRegBAL;
            _appSettings = appSettings.Value;
            _jwtSecurityTokenHelper = jwtSecurityTokenHelper;
            _patientRegDAL = patientRegDAL;
            _patientWrapperDAL = patientWrapperDAL;
        }      

        public async Task<ApiResponse<PatientDataWrapper>> GetPatientDataWarapper(long prid, PublicBaseHttpRequestContext publicbaseHttpRequestContext)
        {
            ApiResponse<PatientDataWrapper> apiResponse = new();
            if (publicbaseHttpRequestContext.tt.ToLower() != "acc")
            {
                apiResponse.StatusCode = 400;
                apiResponse.Errors.Add("Not a Valid Token, Need to provide Access token");
                apiResponse.Message = "failed";
                return apiResponse;
            }
            if (prid != publicbaseHttpRequestContext.PrId)
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "patient record id in query param does not match with token";
                apiResponse.Result = null;
                return apiResponse;

            }


            string token = await _patientRegBAL.GetSystemAdminLoginToken(publicbaseHttpRequestContext);
            PatientDetailInfo patientData = await _patientRegBAL.FetchPatientDetails(token, publicbaseHttpRequestContext.PrId);
            PatientDataWrapper masterData = new PatientDataWrapper();


            Task<InputPatientDetail> apiCall1 = GetPatientDetails(token,patientData.Id);         
            Task<AccountHolder> apiCall2 = GetAccountHolder(token,patientData.Id);       
            Task<HealthDetails> apiCall3 = GetHealthDetails(token,patientData.Id);
            Task<MedicalRecords> apiCall4 = GetMedicalRecords(token, patientData.Id);
            Task<List<InputReferralDetail>> apiCall5 = GetReferralDetails(token, patientData.Id);

            // Wait for all API calls to finish
            await Task.WhenAll(apiCall1, apiCall2, apiCall3,apiCall4,apiCall5);

            // Get the results
            masterData.PatientDetails = await apiCall1;
            masterData.AccountHolderDetails= await apiCall2;
            masterData.HealthDetails = await apiCall3;
            masterData.MedicalRecords = await apiCall4;
            masterData.ReferralDetails = await apiCall5;


            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Result = masterData;
            apiResponse.Message = "success";
            return apiResponse;


        }

        private async Task<InputPatientDetail> GetPatientDetails(string  token,long patientId)
        {
            ApiResponse<InputPatientDetail> apiResponsePatient = new();
            string endpoint = string.Format("/patient/patient_details/{0}", patientId);
            string patientSeviceUrl = _appSettings.ApiUrls["PatientServiceUrl"] + endpoint;
            RestClient restClientCompany = new RestClient(patientSeviceUrl, null, token, null);
            apiResponsePatient = await restClientCompany.GetAsync<ApiResponse<InputPatientDetail>>(patientSeviceUrl);
            return (apiResponsePatient == null) ? null : apiResponsePatient.Result;

        }

        private async Task<AccountHolder> GetAccountHolder(string token,long patientId)
        {
            ApiResponse<AccountHolder> apiResponsePatient = new();
            string endpoint = string.Format("/patient/{0}/account_holder", patientId);
            string patientSeviceUrl = _appSettings.ApiUrls["PatientServiceUrl"] + endpoint;
            RestClient restClientCompany = new RestClient(patientSeviceUrl, null, token, null);
            apiResponsePatient = await restClientCompany.GetAsync<ApiResponse<AccountHolder>>(patientSeviceUrl);
            return (apiResponsePatient == null) ? null : apiResponsePatient.Result;

        }

        private async Task<HealthDetails> GetHealthDetails(string token, long patientId)
        {
            ApiResponse<HealthDetails> apiResponsePatient = new();
            string endpoint = string.Format("/patient/{0}/health_details", patientId);
            string patientSeviceUrl = _appSettings.ApiUrls["PatientServiceUrl"] + endpoint;
            RestClient restClientCompany = new RestClient(patientSeviceUrl, null, token, null);
            apiResponsePatient = await restClientCompany.GetAsync<ApiResponse<HealthDetails>>(patientSeviceUrl);
            return (apiResponsePatient == null) ? null : apiResponsePatient.Result;

        }

        private async Task<MedicalRecords> GetMedicalRecords(string token, long patientId)
        {
            ApiResponse<MedicalRecords> apiResponsePatient = new();
            string endpoint = string.Format("/patient/{0}/medical_records", patientId);
            string patientSeviceUrl = _appSettings.ApiUrls["PatientServiceUrl"] + endpoint;
            RestClient restClientCompany = new RestClient(patientSeviceUrl, null, token, null);
            apiResponsePatient = await restClientCompany.GetAsync<ApiResponse<MedicalRecords>>(patientSeviceUrl);
            return (apiResponsePatient == null) ? null : apiResponsePatient.Result;

        }

        private async Task<List<InputReferralDetail>> GetReferralDetails(string token, long patientId)
        {
            ApiResponse<List<InputReferralDetail>> apiResponsePatient = new();
            string endpoint = string.Format("/patient/{0}/healthcare_team_patreg", patientId);
            string patientSeviceUrl = _appSettings.ApiUrls["PatientServiceUrl"] + endpoint;
            RestClient restClientCompany = new RestClient(patientSeviceUrl, null, token, null);
            apiResponsePatient = await restClientCompany.GetAsync<ApiResponse<List<InputReferralDetail>>>(patientSeviceUrl);
            return (apiResponsePatient == null) ? null : apiResponsePatient.Result;

        }

        public async Task<ApiResponse<string>> AddPatientData(long prid, PublicBaseHttpRequestContext publicbaseHttpRequestContext, InputPatientWrapper inputData)
        {
            ApiResponse<string> apiResponse = new();
            if (publicbaseHttpRequestContext.tt.ToLower() != "acc")
            {
                apiResponse.StatusCode = 400;
                apiResponse.Errors.Add("Not a Valid Token, Need to provide Access token");
                apiResponse.Message = "failed";
                return apiResponse;
            }
            if (prid != publicbaseHttpRequestContext.PrId)
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "patient record id in query param does not match with token";
                apiResponse.Result = null;
                return apiResponse;

            }
            string token = await _patientRegBAL.GetSystemAdminLoginToken(publicbaseHttpRequestContext);
            PatientDetailInfo patientData = await _patientRegBAL.FetchPatientDetails(token, publicbaseHttpRequestContext.PrId);
            long patientId = patientData.Id;
          

            if(inputData.PatientRegInputTypeId == (short)PatientRegInputType.PatientDetails)
            {

                apiResponse = await UpdatePatientDetails(token, patientId, inputData.PatientDetails);

            }
            else if(inputData.PatientRegInputTypeId == (short)PatientRegInputType.AccountHolder)
            {
                if(inputData.AccountHolderDetails.Id != null)
                {
                    apiResponse = await UpdateAccountHolder(token, patientId, inputData.AccountHolderDetails);
                }
                else
                {
                    apiResponse = await AddAccountHolder(token, patientId, inputData.AccountHolderDetails);
                }

            }
            else if (inputData.PatientRegInputTypeId == (short)PatientRegInputType.HealthDetails)
            {
                if (inputData.HealthDetails.Id != null)
                {
                    apiResponse = await UpdateHealthDetails(token, patientId, inputData.HealthDetails);
                }
                else
                {
                    apiResponse = await AddHealthDetails(token, patientId, inputData.HealthDetails);
                }

            }
            else if (inputData.PatientRegInputTypeId == (short)PatientRegInputType.MedicalRecords)
            {
                foreach(var medicalProcedure in inputData.MedicalProcedures)
                {
                    apiResponse =  await AddMedicalRecords(token,patientId, medicalProcedure);
                    if (apiResponse.StatusCode == 400)
                    {
                        apiResponse.Errors.Add("Failed For the object" + medicalProcedure);
                        return apiResponse;
                    }
                }
            }
            else if (inputData.PatientRegInputTypeId == (short)PatientRegInputType.RefferalDetails)
            {
                bool isUpdate = false;
                foreach(var referal in inputData.ReferralDetails)
                {

                    if (referal.Id != null && referal.Id > 0) {

                        isUpdate = true;
                    }
                }

                if (isUpdate == true)
                {
                    apiResponse = await UpdateReferralDetails(token, patientId, inputData.ReferralDetails);
                }
                else
                {
                    apiResponse = await AddReferralDetails(token, patientId, inputData.ReferralDetails);
                }

            }
            return apiResponse;

        }


        public async Task<ApiResponse<string>> UpdatePatientDetails(string token, long patientId,PatientDetail inputPatientDetail)
        {
            
            string endpoint = string.Format("/patient/patient_details/{0}", patientId);
            string updatePatientAPiUrl = _appSettings.ApiUrls["PatientServiceUrl"] + endpoint;
            RestClient patientDetailRestClient = new RestClient(updatePatientAPiUrl, null, token, null);
            var patientDetailResponse = await patientDetailRestClient.PutAsync<ApiResponse<string>>(updatePatientAPiUrl, inputPatientDetail);
            return patientDetailResponse;
        }

        public async Task<ApiResponse<string>> UpdateAccountHolder(string token, long patientId, AccountHolder inputAccountHolder)
        {
            string endpoint = string.Format("/patient/{0}/account_holder/{1}", patientId, inputAccountHolder.Id);
            string updatePatientAPiUrl = _appSettings.ApiUrls["PatientServiceUrl"] + endpoint;
            RestClient putAccountHolderRestClient = new RestClient(updatePatientAPiUrl, null, token, null);
            var putAccountHolderResponse = await putAccountHolderRestClient.PutAsync<ApiResponse<string>>(updatePatientAPiUrl, inputAccountHolder);
            return putAccountHolderResponse;
        }

        public async Task<ApiResponse<string>> UpdateHealthDetails(string token, long patientId, HealthDetails inputHealthDetails)
        {
            string endpoint = string.Format("/patient/{0}/health_details/{1}", patientId, inputHealthDetails.Id);
            string updatePatientAPiUrl = _appSettings.ApiUrls["PatientServiceUrl"] + endpoint;
            RestClient healthDetailsRestClient = new RestClient(updatePatientAPiUrl, null, token, null);
            var healthDetailsResponse = await healthDetailsRestClient.PutAsync<ApiResponse<string>>(updatePatientAPiUrl, inputHealthDetails);
            return healthDetailsResponse;
        }

        public async Task<ApiResponse<string>> AddAccountHolder(string token, long patientId, AccountHolder inputAccountHolder)
        {
            ApiResponse<string> addResponse = new();
            string endpoint = string.Format("/patient/{0}/account_holder", patientId);
            string addPatientAPiUrl = _appSettings.ApiUrls["PatientServiceUrl"] + endpoint;
            RestClient addAccountHolderRestClient = new RestClient(addPatientAPiUrl, null, token, null);
            inputAccountHolder.Id = 0;
            var addAccountHolderResponse = await addAccountHolderRestClient.PostAsync<ApiResponse<long?>>(addPatientAPiUrl, inputAccountHolder);
            if (addAccountHolderResponse.StatusCode == 200) {
                addResponse.StatusCode = StatusCodes.Status200OK;
                addResponse.Message = "sucess";
            }
            else
            {
                addResponse.StatusCode = addAccountHolderResponse.StatusCode;
                addResponse.Message = "Patient Account holder API Failed";
                addResponse.Errors = addAccountHolderResponse.Errors;
            }
            return addResponse;
        }

        public async Task<ApiResponse<string>> AddHealthDetails(string token, long patientId, HealthDetails inputHealthDetails)
        {
            ApiResponse<string> addResponse = new();
            string endpoint = string.Format("/patient/{0}/health_details", patientId);
            string addPatientAPiUrl = _appSettings.ApiUrls["PatientServiceUrl"] + endpoint;
            RestClient healthDetailsRestClient = new RestClient(addPatientAPiUrl, null, token, null);
            inputHealthDetails.Id = 0;
            var healthDetailsResponse = await healthDetailsRestClient.PostAsync<ApiResponse<long?>>(addPatientAPiUrl, inputHealthDetails);
            if (healthDetailsResponse.StatusCode == 200)
            {
                addResponse.StatusCode = StatusCodes.Status200OK;
                addResponse.Message = "sucess";
            }
            else
            {
                addResponse.StatusCode = healthDetailsResponse.StatusCode;
                addResponse.Message = "Patient Health Details API Failed";
                addResponse.Errors = healthDetailsResponse.Errors;
            }
            return addResponse;
        }

        public async Task<ApiResponse<string>> AddMedicalRecords(string token, long patientId, MedicalProcedures inputMedprocedure)
        {
            ApiResponse<string> addResponse = new();
            string endpoint = string.Format("/patient/{0}/medical_records", patientId);
            string addPatientAPiUrl = _appSettings.ApiUrls["PatientServiceUrl"] + endpoint;
            RestClient medicalRecordsRestClient = new RestClient(addPatientAPiUrl, null, token, null);
            inputMedprocedure.Id = 0;
            var medicalRecordsResponse = await medicalRecordsRestClient.PostAsync<ApiResponse<long?>>(addPatientAPiUrl, inputMedprocedure);
            if (medicalRecordsResponse.StatusCode == 200)
            {
                addResponse.StatusCode = StatusCodes.Status200OK;
                addResponse.Message = "sucess";
            }
            else
            {
                addResponse.StatusCode = medicalRecordsResponse.StatusCode;
                addResponse.Message = "Patient Medical Records API Failed";
                addResponse.Errors = medicalRecordsResponse.Errors;
            }
            return addResponse;
        }


        public async Task<ApiResponse<string>> AddReferralDetails(string token, long patientId, List<ReferralDetail> inputReferralDetails)
        {
            ApiResponse<string> addResponse = new();
            string endpoint = string.Format("/patient/{0}/healthcare_team_patreg", patientId);
            string addPatientAPiUrl = _appSettings.ApiUrls["PatientServiceUrl"] + endpoint;
            RestClient refferalRestClient = new RestClient(addPatientAPiUrl, null, token, null);
            
            var referralResponse = await refferalRestClient.PostAsync<ApiResponse<string>>(addPatientAPiUrl, inputReferralDetails);
            if (referralResponse.StatusCode == 200)
            {
                addResponse.StatusCode = StatusCodes.Status200OK;
                addResponse.Message = "sucess";
            }
            else
            {
                addResponse.StatusCode = referralResponse.StatusCode;
                addResponse.Message = "Referral details API Failed";
                addResponse.Errors = referralResponse.Errors;
            }
            return addResponse;
        }

        public async Task<ApiResponse<string>> UpdateReferralDetails(string token, long patientId, List<ReferralDetail> inputReferralDetails)
        {
            string endpoint = string.Format("/patient/{0}/healthcare_team_patreg", patientId);
            string updatePatientAPiUrl = _appSettings.ApiUrls["PatientServiceUrl"] + endpoint;
            RestClient referralRestClient = new RestClient(updatePatientAPiUrl, null, token, null);
            var referralResponse = await referralRestClient.PutAsync<ApiResponse<string>>(updatePatientAPiUrl, inputReferralDetails);
            return referralResponse;
        }


        public async Task<ApiResponse<string>> DeletePatientToken(long prid, PublicBaseHttpRequestContext publicbaseHttpRequestContext)
        {
            ApiResponse<string> apiResponse = new();
            int orgId = publicbaseHttpRequestContext.OrgId;

            if (publicbaseHttpRequestContext.tt.ToLower() != "acc")
            {
                apiResponse.StatusCode = 400;
                apiResponse.Errors.Add("Not a Valid Token, Need to provide Access token");
                apiResponse.Message = "failed";
                return apiResponse;
            }

            if (prid != publicbaseHttpRequestContext.PrId)
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "patient record id in query param does not match with token";
                apiResponse.Result = null;
                return apiResponse;

            }

            PatientTokenAssoc tokenAssoc = await _patientRegDAL.GetPatientToken(publicbaseHttpRequestContext.PrId);
            if (tokenAssoc == null)
            {
                apiResponse.StatusCode = 400;
                apiResponse.Errors.Add("Token in DB does not exists");
                apiResponse.Message = "failed";
                return apiResponse;
            }
           
            int rows  = await _patientWrapperDAL.DeletePatientToken(publicbaseHttpRequestContext.PrId);
            int rows1 = await _patientWrapperDAL.DeletePatientDraftData(publicbaseHttpRequestContext.PrId);

            if (rows > 0)
            {
                apiResponse.Result = "success";
                apiResponse.Message = "Success";
                apiResponse.StatusCode = StatusCodes.Status200OK;
                return apiResponse;
            }
            else
            {
                apiResponse.Errors.Add("Token cannot be deleted at this time.");
            }
            
            apiResponse.Message = "Failure";
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            return apiResponse;
        }

        public async Task<ApiResponse<FileDetailsOutputForId>> GetFileDataWarapper(long fileid, PublicBaseHttpRequestContext publicbaseHttpRequestContext)
        {
            ApiResponse<FileDetailsOutputForId> apiResponse = new();
            if (publicbaseHttpRequestContext.tt.ToLower() != "acc")
            {
                apiResponse.StatusCode = 400;
                apiResponse.Errors.Add("Not a Valid Token, Need to provide Access token");
                apiResponse.Message = "failed";
                return apiResponse;
            }
            string token = await _patientRegBAL.GetSystemAdminLoginToken(publicbaseHttpRequestContext);
            
            apiResponse = await GetFileDetails(token, fileid);
            if(apiResponse.Result != null && apiResponse.Result.FileDetail.FileRepository.FileModuleTypeId != (short)FileModuleType.PatientRegTemp)
            {
                apiResponse.StatusCode = StatusCodes.Status403Forbidden;
                apiResponse.Errors.Add("Not a Authorized User to access file");
                apiResponse.Message = "failed";
                apiResponse.Result = null;
                return apiResponse;


            }


            return apiResponse;
        }


        private async Task<ApiResponse<FileDetailsOutputForId>> GetFileDetails(string token, long fileId)
        {
            ApiResponse<FileDetailsOutputForId> apiResponsefile = new();
            string endpoint = string.Format("/file/file_details/{0}", fileId);
            string fileSeviceUrl = _appSettings.ApiUrls["FileServiceUrl"] + endpoint;
            RestClient restClientCompany = new RestClient(fileSeviceUrl, null, token, null);
            apiResponsefile = await restClientCompany.GetAsync<ApiResponse<FileDetailsOutputForId>>(fileSeviceUrl);
            return apiResponsefile;

        }

        public async Task<ApiResponse<long>> PostFileDataWarapper(PublicBaseHttpRequestContext publicbaseHttpRequestContext, FileUploadObject fileUpload)
        {
            ApiResponse<long> apiResponse = new();

            if (publicbaseHttpRequestContext.tt.ToLower() != "acc")
            {
                apiResponse.StatusCode = 400;
                apiResponse.Errors.Add("Not a Valid Token, Need to provide Access token");
                apiResponse.Message = "failed";
                return apiResponse;
            }

            if (fileUpload.FileModuleTypeId != (short)FileModuleType.PatientRegTemp)
            {

                apiResponse.StatusCode = StatusCodes.Status403Forbidden;
                apiResponse.Errors.Add("User does not have acccess");
                apiResponse.Message = "failed";
                return apiResponse;

            }
            string token = await _patientRegBAL.GetSystemAdminLoginToken(publicbaseHttpRequestContext);         
            apiResponse = await AddFileData(token, fileUpload);
            return apiResponse; 

        }

        public async Task<ApiResponse<long>> AddFileData(string token, FileUploadObject filedata)
        {
            string endpoint = string.Format("/file/fileupload");
            string addFileAPiUrl = _appSettings.ApiUrls["FileServiceUrl"] + endpoint;
            RestClient filePostRestClient = new RestClient(addFileAPiUrl, null, token, null);
            var filePostResponse = await filePostRestClient.PostAsync<ApiResponse<long>>(addFileAPiUrl, filedata);           
            return filePostResponse;
        }

        public async Task<ApiResponse<FileDetailsOutputForId>> DeleteFileDataWarapper(long fileid, PublicBaseHttpRequestContext publicbaseHttpRequestContext)
        {
            ApiResponse<FileDetailsOutputForId> apiResponse = new();
            if (publicbaseHttpRequestContext.tt.ToLower() != "acc")
            {
                apiResponse.StatusCode = 400;
                apiResponse.Errors.Add("Not a Valid Token, Need to provide Access token");
                apiResponse.Message = "failed";
                return apiResponse;
            }
            string token = await _patientRegBAL.GetSystemAdminLoginToken(publicbaseHttpRequestContext);
            apiResponse = await DeleteFileData(token, fileid);
            return apiResponse;


        }

        public async Task<ApiResponse<FileDetailsOutputForId>> DeleteFileData(string token, long fileId)
        {
            string endpoint = string.Format("/file/file_details/{0}", fileId);
            string addFileAPiUrl = _appSettings.ApiUrls["FileServiceUrl"] + endpoint;
            RestClient filePostRestClient = new RestClient(addFileAPiUrl, null, token, null);
            var filePostResponse = await filePostRestClient.DeleteAsync<ApiResponse<FileDetailsOutputForId>>(addFileAPiUrl, null);
            return filePostResponse;
        }

        public async Task<ApiResponse<long>> TransferFileDetails(PublicBaseHttpRequestContext publicbaseHttpRequestContext, string stringFileDetailsId)
        {
            ApiResponse<long> apiResponse = new();
            if (publicbaseHttpRequestContext.tt.ToLower() != "acc")
            {
                apiResponse.StatusCode = 400;
                apiResponse.Errors.Add("Not a Valid Token, Need to provide Access token");
                apiResponse.Message = "failed";
                return apiResponse;
            }
            string token = await _patientRegBAL.GetSystemAdminLoginToken(publicbaseHttpRequestContext);
            var listFileDetailsId = stringFileDetailsId.Split(',').ToList();
            List<long> listIds = listFileDetailsId.Select(s => long.Parse(s)).ToList();

            foreach ( var fileDetailsId in listIds)
            {
                apiResponse = await  TransferFileData(token, fileDetailsId);
                if(apiResponse.StatusCode == StatusCodes.Status400BadRequest || apiResponse.StatusCode == StatusCodes.Status500InternalServerError ) 
                
                {
                    apiResponse.Errors.Add("The transfer of files failed at the fileID "+ fileDetailsId);

                }

            }

            return apiResponse;

        }

        public async Task<ApiResponse<long>> TransferFileData(string token, long fileId)
        {
            string endpoint = string.Format("/file/filetransfer/{0}", fileId);
            string addFileAPiUrl = _appSettings.ApiUrls["FileServiceUrl"] + endpoint;
            RestClient fileGetRestClient = new RestClient(addFileAPiUrl, null, token, null);
            var filePostResponse = await fileGetRestClient.PostAsync<ApiResponse<long>>(addFileAPiUrl, null);
            return filePostResponse;
        }




    }
}
