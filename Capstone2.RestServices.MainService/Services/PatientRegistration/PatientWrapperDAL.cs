﻿using Capstone2.RestServices.PatientRegistration.Context;
using Capstone2.RestServices.PatientRegistration.Interfaces;
using Capstone2.RestServices.PatientRegistration.Models;
using Capstone2.Shared.Models.Common;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using Capstone2.Shared.Models.Enum;

namespace Capstone2.RestServices.Communication.Services
{

    public class PatientWrapperDAL : IPatientWrapperDAL
    {
        public UpdatablePatientRegistrationDBContext _updatableDBContext;
        public ReadOnlyPatientRegistrationDBContext _readOnlyDBContext;
        public PatientWrapperDAL(UpdatablePatientRegistrationDBContext updatableDBContext, ReadOnlyPatientRegistrationDBContext readOnlyDBContext)
        {
            _updatableDBContext = updatableDBContext;
            _readOnlyDBContext = readOnlyDBContext;
        }

        // delete main token and factors in the factors table
        public async Task<int> DeletePatientToken(long recordId)
        {
            PatientTokenAssoc userToken = await _updatableDBContext.PatientTokenAssocs.OrderByDescending(x => x.Id).FirstOrDefaultAsync(x => x.PatientRecordId == recordId);
            if (userToken != null)
            {
                 _updatableDBContext.PatientTokenAssocs.Remove(userToken);
                 var result =  await _updatableDBContext.SaveChangesAsync();

                 var factors = await _updatableDBContext.Factors.Where(x=> x.PatientRecordId == recordId).ToListAsync();
                if(factors != null && factors.Count > 0) 
                { 
                    _updatableDBContext.Factors.RemoveRange(factors);
                    var result1 = await _updatableDBContext.SaveChangesAsync();
                }


                return result;
            }
            return 0;
        }

        public async Task<int> DeletePatientDraftData(long recordId)
        {
            PatientDraftData patientdata = await _updatableDBContext.PatientDraftData.FirstOrDefaultAsync(x => x.PatientRecordId == recordId);
            if (patientdata != null)
            {
                _updatableDBContext.PatientDraftData.Remove(patientdata);
                var result = await _updatableDBContext.SaveChangesAsync();
                return result;
            }
            return 0;
        }

    }

}
