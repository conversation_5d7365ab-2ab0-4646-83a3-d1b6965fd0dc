﻿using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.PatientRegistration.Interfaces;
using Capstone2.RestServices.PatientRegistration.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Capstone2.RestServices.PatientRegistration.Services;
using Capstone2.Framework.RestApi;
using Capstone2.Shared.Models.Entities;
using Capstone2.Framework.Business.Common;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Communication;
using System.Text;
using Capstone2.Framework.RestApi.Utility;
using System.IdentityModel.Tokens.Jwt;
using Microsoft.IdentityModel.Tokens;
using System.Security.Claims;
using Microsoft.Identity.Client;
using Capstone2.RestServices.Communication.Services;

namespace Capstone2.RestServices.PatientRegistration.Services
{
    public class PatientRegBAL : IPatientRegBAL
    {
        public IPatientRegDAL _patientRegDAL;
        public readonly AppSettings _appSettings;
        private IJwtSecurityTokenHelper _jwtSecurityTokenHelper;
        public PatientRegBAL(IPatientRegDAL patientRegDAL, IOptions<AppSettings> appSettings, IJwtSecurityTokenHelper jwtSecurityTokenHelper)
        {
            _patientRegDAL = patientRegDAL;
            _appSettings = appSettings.Value;
            _jwtSecurityTokenHelper = jwtSecurityTokenHelper;
        }

        public async Task<ApiResponse<string>> GenerateOtpBAL(OtpObject otpObject, PublicBaseHttpRequestContext publicbaseHttpRequestContext)
        {
            ApiResponse<string> apiRes = new();
            PatientTokenAssoc tokenAssoc = await _patientRegDAL.GetPatientToken(publicbaseHttpRequestContext.PrId);
            if (tokenAssoc == null || tokenAssoc.PublicJwtToken != publicbaseHttpRequestContext.BearerToken)
            {
                apiRes.StatusCode = 400;
                apiRes.Errors.Add("Not a Valid Token, Token in DB does not match");
                apiRes.Message = "failed";
                return apiRes;
            }

            if (publicbaseHttpRequestContext.tt.ToLower() != "auth")
            {
                apiRes.StatusCode = 400;
                apiRes.Errors.Add("Not a Valid Token, Need to provide Authentication token");
                apiRes.Message = "failed";
                return apiRes;
            }


            string token = await GetSystemAdminLoginToken(publicbaseHttpRequestContext);
            PatientDetailInfo patientData = await FetchPatientDetails(token, publicbaseHttpRequestContext.PrId);
            patientData.RecordId = publicbaseHttpRequestContext.PrId;
            string otp = await ProcessOTPRequest(patientData);

            if (otpObject.factorType.ToLower() == "email")
            {
                EmailRequest emailRequest = await CreateNewEmailRequestMsg("TwoFactorAuthenticationForPatientRegistration", patientData, otp);
                if (!(emailRequest is null))
                {
                    emailRequest.ToEmailAddress = patientData.PersonalEmailAddress;
                    apiRes = await CreateEmailRequest(emailRequest, token);
                    if (apiRes.StatusCode == StatusCodes.Status200OK || apiRes.StatusCode == StatusCodes.Status202Accepted)
                    {
                        apiRes.StatusCode = StatusCodes.Status200OK;
                        apiRes.Message = "Success";
                        apiRes.Result = "Success";

                        /* if (!string.IsNullOrWhiteSpace(_appSettings.ApiUrls["DomainUrl"]) && ("capstonesystemdev.com.au".Equals(_appSettings.ApiUrls["DomainUrl"].ToLower()) || "capstonesystemuat.com.au".Equals(_appSettings.ApiUrls["DomainUrl"].ToLower())))
                             apiRes.Result = otp;*/

                        return apiRes;
                    }
                }
            }
            else if (otpObject.factorType.ToLower() == "mobile")
            {
                SmsRequest smsRequest = await CreateSMSForPatientReg("TwoFactorAuthenticationForPatientRegistration", patientData, otp);
                smsRequest.ToMobile = patientData.Mobile;
                if (!(smsRequest is null))
                {
                    apiRes = await CallSMSAPIAsync(smsRequest, token);
                    if (StatusCodes.Status200OK == apiRes.StatusCode)
                    {
                        apiRes.StatusCode = StatusCodes.Status200OK;
                        apiRes.Result = "Success";
                        apiRes.Message = "Success";
                        return apiRes;

                    }

                }

            }

            apiRes.StatusCode = 400;
            apiRes.Errors.Add("otp could not be sent");
            apiRes.Message = "failed";
            return apiRes;
        }

        public async Task<string> GetSystemAdminLoginToken(PublicBaseHttpRequestContext baseHttpRequestContext)
        {
            Dictionary<string, string> headerKeys = new Dictionary<string, string>();

            string anonymous_tokenURL = _appSettings.ApiUrls["AuthServiceUrl"] + "/auth/Anonymous_Token?orgCode=" + baseHttpRequestContext.OrgCode;
            RestClient anonymous_tokenRestClient = new RestClient(anonymous_tokenURL, headerKeys);
            var anonymous_token = await anonymous_tokenRestClient.GetAsync<ApiResponse<string>>(anonymous_tokenURL);


            /*string ClientSecretAndClientURL = _appSettings.ApiUrls["AuthServiceUrl"] + "/auth/getClientIdAndSecretBySystemEmail?systemAdminEmail=" + "<EMAIL>";
            RestClient ClientSecretRestClient = new RestClient(ClientSecretAndClientURL, null, anonymous_token.Result, null);
            var clientSecreatView = await ClientSecretRestClient.GetAsync<ApiResponse<ClientSecreatView>>(ClientSecretAndClientURL);
            if (clientSecreatView.StatusCode != 200)
            {

                return null;
            }*/

            string tokenURL = _appSettings.ApiUrls["AuthServiceUrl"] + "/auth/clientid/login";
            RestClient tokenRestClient = new RestClient(tokenURL, null, anonymous_token.Result, null);

            var clientData = await _patientRegDAL.GetClientSecreatAsync(_appSettings.PRAdminEmail);

            var requestBody = new
            {
                Type = "Client_Id",
                Identifier = clientData.ClientId,
                FactorValue = clientData.ClientSecret
            };
            var tokenResponse = await tokenRestClient.PostAsync<ApiResponse<TokenResponse>>(tokenURL, requestBody);
            if (tokenResponse != null && tokenResponse.StatusCode == 200 && !string.IsNullOrEmpty(tokenResponse.Result.Token))
            {
                return tokenResponse.Result.Token;
            }
            return null;
        }

        public async Task<PatientDetailInfo> FetchPatientDetails(string token, long recordId)
        {

            ApiResponse<PatientDetailInfo> apiResponsePatient = new();
            string endpoint = string.Format("/patient/patient_details/record_id/{0}", recordId);
            string patientSeviceUrl = _appSettings.ApiUrls["PatientServiceUrl"] + endpoint;
            RestClient restClientCompany = new RestClient(patientSeviceUrl, null, token, null);
            apiResponsePatient = await restClientCompany.GetAsync<ApiResponse<PatientDetailInfo>>(patientSeviceUrl);
            return (apiResponsePatient == null) ? null : apiResponsePatient.Result;

        }

        private async Task<string> ProcessOTPRequest(PatientDetailInfo patientData)
        {
            string OTP = GenerateOTP();
            Factor factor = new Factor();
            factor.otp = BcryptHashingHelper.GetHashedPassword(OTP);
            factor.PatientRecordId = patientData.RecordId;
            factor.ExpiryDate = DateTime.UtcNow.AddMinutes((int.Parse(_appSettings.PatientRegPinExpiryInMins)));
            factor.OrgId = patientData.OrgId;
            factor.StatusId = (short)Status.Active;
            await _patientRegDAL.AddUserFactor(factor);
            return OTP;

        }

        public string GenerateOTP()
        {
            //Random random = new Random();
            string otp = RandomNumberGenerator.RandomInteger().ToString();
            //string otp = random.Next(100000, 999999).ToString();
            return otp;
        }

        public async Task<EmailRequest> CreateNewEmailRequestMsg(string templateName, PatientDetailInfo patientData, string param1)
        {
            int orgId = patientData.OrgId;
            string patientName = patientData.FirstName + " " + patientData.SurName;
            if (orgId == default(int))
                return null;
            EmailTemplate template = await _patientRegDAL.GetEmailTemplate(templateName, orgId);
            if (!(template is null))
            {
                EmailRequest emailRequest = new EmailRequest();
                emailRequest.HtmlContent = string.Format(template.HtmlContent, patientName, param1);
                emailRequest.PlainTextContent = template.PlainTextContent;
                emailRequest.Subject = template.Subject;
                return emailRequest;
            }
            return null;
        }

        private async Task<SmsRequest> CreateSMSForPatientReg(string templateName, PatientDetailInfo patientData, string param1)
        {
            int orgId = patientData.OrgId;
            string patientName = patientData.FirstName + " " + patientData.SurName;
            if (orgId == default(int))
                return null;
            SmsTemplate template = await _patientRegDAL.GetSmsTemplateForPatReg(templateName, orgId);
            if (!(template is null))
            {
                SmsRequest smsRequest = new SmsRequest();
                smsRequest.RequestBody = string.Format(template.Body, param1);
                return smsRequest;
            }
            return null;
        }

        public async Task<ApiResponse<string>> CreateEmailRequest(EmailRequest emailRequest, string token)
        {
            ApiResponse<string> apiResponse = new ApiResponse<string>();
            //try
            //{

            // string interServiceToken = baseHttpRequestContext.InterServiceToken;
            string emailAPiUrl = _appSettings.ApiUrls["CommunicationServiceUrl"] + "/Email/SendEmail";
            RestClient restClient = new RestClient(emailAPiUrl, null, token, null);
            apiResponse = await restClient.PostAsync<ApiResponse<string>>(emailAPiUrl, emailRequest);
            return ProcessEmailResponse(apiResponse);
            //}
            //catch (Exception ex)
            //{
            //    apiResponse.Message = ex.Message;
            //    return apiResponse;
            //}

        }

        private ApiResponse<string> ProcessEmailResponse(ApiResponse<string> apiResponse)
        {
            var emailApiResponse = new ApiResponse<string>();
            if (apiResponse != null && StatusCodes.Status200OK == apiResponse.StatusCode && !string.IsNullOrWhiteSpace(apiResponse.Result))
                emailApiResponse.StatusCode = int.Parse(apiResponse.Result);
            else
                emailApiResponse.StatusCode = apiResponse.StatusCode;
            return emailApiResponse;
        }


        public async Task<ApiResponse<LoginResponse>> ValidateOtpBAL(VerifyObject otpObject, PublicBaseHttpRequestContext publicbaseHttpRequestContext)
        {
            bool isValid = false;

            
            ApiResponse<LoginResponse> apiResponse = new ApiResponse<LoginResponse>();
            if (publicbaseHttpRequestContext.tt.ToLower() != "auth")
            {
                apiResponse.StatusCode = 400;
                apiResponse.Errors.Add("Not a Valid Token, Need to provide Authentication token");
                apiResponse.Message = "failed";
                return apiResponse;
            }
            Factor factor = await _patientRegDAL.GetFactorValue(publicbaseHttpRequestContext.PrId);
            LoginResponse loginResponse = new();

            if (factor.ExpiryDate < DateTime.UtcNow)
            {
                isValid = false;

            }
            else
            {
                string decodedOTP = Base64EncoderHelper.DecodeBase64(otpObject.Value);
                isValid = BcryptHashingHelper.VerifyHashedPassword(decodedOTP, factor.otp);
            }
            PatientRegistrationRequest prRequest = new PatientRegistrationRequest();
            prRequest.FVT = otpObject.factorType;
            prRequest.RecordId = publicbaseHttpRequestContext.PrId;


            if (isValid)
            {
                //return New token for 
                var newAccessToken = GeneratePatinetRegistrationToken(prRequest, publicbaseHttpRequestContext.OrgId, publicbaseHttpRequestContext.OrgCode, Convert.ToInt32(_appSettings.PatientRegistrationTokenExpiresIn));
                loginResponse.Token = newAccessToken;
                loginResponse.IdleTimeOut = 20;
                apiResponse.Result = loginResponse;
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";

            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Errors.Add("Not a valid otp");
                apiResponse.Message = "Failed";
            }

            return apiResponse;

        }

        private string GeneratePatinetRegistrationToken(PatientRegistrationRequest patientRegRequest, int orgId, string orgCode, int patientRegTokenExpiry)
        {
            var jwtKey = _jwtSecurityTokenHelper.GetJWTExternalRSASecureKey().Result.ToString();
            var key = Encoding.ASCII.GetBytes(jwtKey);
            int expire_hour = Convert.ToInt32(patientRegTokenExpiry);
            var tokenHandeler = new JwtSecurityTokenHandler();
            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(new Claim[] {
                    new Claim("prid", patientRegRequest.RecordId.ToString()),
                    new Claim("oid", orgId.ToString()),
                    new Claim("ocode", orgCode),
                    new Claim("fvt",patientRegRequest.FVT),
                    new Claim("tt","acc"),
                }),
                Expires = DateTime.UtcNow.AddHours(expire_hour),
                Issuer = _appSettings.Issuer,
                Audience = _appSettings.Audience,
                SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
            };

            var token = tokenHandeler.CreateToken(tokenDescriptor);
            var FinalToken = tokenHandeler.WriteToken(token);
            return FinalToken;
        }

        /// <summary>
        /// Function to call SMS Service
        /// </summary>
        /// <param name="smsRequest"></param>
        /// <returns></returns>
        public async Task<ApiResponse<string>> CallSMSAPIAsync(SmsRequest smsRequest, string token)
        {
            ApiResponse<string> apiResponse = new ApiResponse<string>();
            string smsAPiUrl = _appSettings.ApiUrls["CommunicationServiceUrl"] + "/Sms/SendSms";
            // var token = baseHttpRequestContext.BearerToken;
            // string interServiceToken = baseHttpRequestContext.InterServiceToken;
            RestClient restClient = new RestClient(smsAPiUrl, null, token, null);
            var apiResponseFromAPI = await restClient.PostAsync<ApiResponse<long>>(smsAPiUrl, smsRequest);
            apiResponse.StatusCode = apiResponseFromAPI.StatusCode;
            apiResponse.Errors = apiResponseFromAPI.Errors;
            apiResponse.Result = apiResponseFromAPI.Result.ToString();
            apiResponse.Message = apiResponseFromAPI.Message;
            return apiResponse;
        }

        public async Task<ApiResponse<long?>> AddPatientDraftData(long prid, PublicBaseHttpRequestContext publicbaseHttpRequestContext, PatientDraftData inputDraftData)
        {
            ApiResponse<long?> apiResponse = new();
            var orgId = publicbaseHttpRequestContext.OrgId;
            // var loggedInUser = baseHttpRequestContext.UserId;

            if (prid != publicbaseHttpRequestContext.PrId)
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "patient record id in query param does not match prid in token";
                apiResponse.Result = null;
                return apiResponse;

            }

            if (publicbaseHttpRequestContext.tt.ToLower() != "acc")
            {
                apiResponse.StatusCode = 400;
                apiResponse.Errors.Add("Not a Valid Token, Need to provide Access token");
                apiResponse.Message = "failed";
                return apiResponse;
            }

            inputDraftData.OrgId = orgId;
            inputDraftData.StatusId = (short)Status.Active;
            var id = await _patientRegDAL.AddPatientDraftData(inputDraftData);
            if (id > 0)
            {
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";
                apiResponse.Result = id;
            }
            else
            {

                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failure";
                apiResponse.Result = null;

            }

            return apiResponse;

        }

        public async Task<ApiResponse<PatientDraftData>> GetPatientDraftData(PublicBaseHttpRequestContext publicbaseHttpRequestContext, long prid)
        {
            ApiResponse<PatientDraftData> apiResponse = new();
            var orgId = publicbaseHttpRequestContext.OrgId;

            if (prid != publicbaseHttpRequestContext.PrId)
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "patient record id in query param does not match with token";
                apiResponse.Result = null;
                return apiResponse;

            }
            if (publicbaseHttpRequestContext.tt.ToLower() != "acc")
            {
                apiResponse.StatusCode = 400;
                apiResponse.Errors.Add("Not a Valid Token, Need to provide Access token");
                apiResponse.Message = "failed";
                return apiResponse;
            }

            var patientDraftFromDB = await _patientRegDAL.GetPatientDraftData(orgId, prid);
            if (patientDraftFromDB is null)
            {
                apiResponse.Result = null;
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Patient Draft Data Does not exists";
                return apiResponse;
            }
            else
            {
                apiResponse.Result = patientDraftFromDB;
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";
                return apiResponse;
            }

        }

        public async Task<ApiResponse<long?>> EditPatientDraftData(PublicBaseHttpRequestContext publicbaseHttpRequestContext, long prid, PatientDraftData inputDraftData)
        {
            ApiResponse<long?> apiResponse = new();
            var orgId = publicbaseHttpRequestContext.OrgId;
            if (prid != publicbaseHttpRequestContext.PrId)
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "patient record id in query param does not match with token";
                apiResponse.Result = null;
                return apiResponse;

            }

            if (publicbaseHttpRequestContext.tt.ToLower() != "acc")
            {
                apiResponse.StatusCode = 400;
                apiResponse.Errors.Add("Not a Valid Token, Need to provide Access token");
                apiResponse.Message = "failed";
                return apiResponse;
            }

            var PatientDataFromDB = await _patientRegDAL.GetPatientDraftData(orgId, prid);

            inputDraftData.OrgId = orgId;
            inputDraftData.ModifiedDate = DateTime.UtcNow;
            inputDraftData.CreatedDate = PatientDataFromDB.CreatedDate;
            inputDraftData.Id = PatientDataFromDB.Id;
            inputDraftData.StatusId = PatientDataFromDB.StatusId;
            int id = await _patientRegDAL.UpdatePatientDraftData(inputDraftData);

            if (id > 0)
            {

                apiResponse.Result = id;
                apiResponse.Message = "Success";
                apiResponse.StatusCode = StatusCodes.Status200OK;
                return apiResponse;
            }
            else
            {
                apiResponse.Result = null;
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("The PatientDraftData could not be updated.");
                return apiResponse;

            }


        }

        public async Task<ApiResponse<MasterDataWrapper>> GetMasterDataWarapper(PublicBaseHttpRequestContext publicbaseHttpRequestContext)
        {
            string token = await GetSystemAdminLoginToken(publicbaseHttpRequestContext);
            MasterDataWrapper masterData = new MasterDataWrapper();
            ApiResponse<MasterDataWrapper> apiResponse = new ();

            if (publicbaseHttpRequestContext.tt.ToLower() != "acc")
            {
                apiResponse.StatusCode = 400;
                apiResponse.Errors.Add("Not a Valid Token, Need to provide Access token");
                apiResponse.Message = "failed";
                return apiResponse;
            }


            Task<List<CountryMaster>> apiCall1 = GetCountryMaster(token);         
            Task<List<StateMaster>> apiCall2 = GetStateMaster(token);       
            Task<List<NationalityMaster>> apiCall3 = GetNationalityMaster(token);        
            Task<List<LanguageMaster>> apiCall4 = GetLanguageMaster(token);         

            // Wait for all API calls to finish
            await Task.WhenAll(apiCall1, apiCall2, apiCall3, apiCall4);

            // Get the results
            masterData.Countries = await apiCall1;
            masterData.States= await apiCall2;
            masterData.Nationalities = await apiCall3;
            masterData.Languages = await apiCall4;

            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Result = masterData;
            apiResponse.Message = "success";
            return apiResponse;


        }

        private async Task<List<CountryMaster>> GetCountryMaster(string  token)
        {
            List<CountryMaster> listCountryMaster = new();

            string medicalContBandApiUrl = _appSettings.ApiUrls["MasterServiceUrl"] + "/master/countries";
            RestClient restClient = new RestClient(medicalContBandApiUrl, null, token, null);
            var httpResponse = await restClient.GetAsync<ApiResponse<List<CountryMaster>>>(medicalContBandApiUrl);
            if (httpResponse.StatusCode == StatusCodes.Status200OK && httpResponse.Result is not null)
            {
                listCountryMaster = httpResponse.Result;
            }

            return listCountryMaster;

        }

        private async Task<List<StateMaster>> GetStateMaster(string token)
        {
            List<StateMaster> listStateMaster = new();

            string medicalContBandApiUrl = _appSettings.ApiUrls["MasterServiceUrl"] + "/master/states?country_id=1";
            RestClient restClient = new RestClient(medicalContBandApiUrl, null, token, null);
            var httpResponse = await restClient.GetAsync<ApiResponse<List<StateMaster>>>(medicalContBandApiUrl);
            if (httpResponse.StatusCode == StatusCodes.Status200OK && httpResponse.Result is not null)
            {
                listStateMaster = httpResponse.Result;
            }

            return listStateMaster;

        }

        private async Task<List<LanguageMaster>> GetLanguageMaster(string token)
        {
            List<LanguageMaster> listLanguageMaster = new();

            string medicalContBandApiUrl = _appSettings.ApiUrls["MasterServiceUrl"] + "/master/languages";
            RestClient restClient = new RestClient(medicalContBandApiUrl, null, token, null);
            var httpResponse = await restClient.GetAsync<ApiResponse<List<LanguageMaster>>>(medicalContBandApiUrl);
            if (httpResponse.StatusCode == StatusCodes.Status200OK && httpResponse.Result is not null)
            {
                listLanguageMaster = httpResponse.Result;
            }

            return listLanguageMaster;

        }

        private async Task<List<NationalityMaster>> GetNationalityMaster(string token)
        {
            List<NationalityMaster> listNationalityMaster = new();

            string medicalContBandApiUrl = _appSettings.ApiUrls["MasterServiceUrl"] + "/master/nationalities";
            RestClient restClient = new RestClient(medicalContBandApiUrl, null, token, null);
            var httpResponse = await restClient.GetAsync<ApiResponse<List<NationalityMaster>>>(medicalContBandApiUrl);
            if (httpResponse.StatusCode == StatusCodes.Status200OK && httpResponse.Result is not null)
            {
                listNationalityMaster = httpResponse.Result;
            }

            return listNationalityMaster;

        }

        public async Task<ApiResponse<string>> GetAddressBAL(PublicBaseHttpRequestContext publicbaseHttpRequestContext, string st)
        {
            ApiResponse<string> apiResponse = new();

            if (publicbaseHttpRequestContext.tt.ToLower() != "acc")
            {
                apiResponse.StatusCode = 400;
                apiResponse.Errors.Add("Not a Valid Token, Need to provide Access token");
                apiResponse.Message = "failed";
                return apiResponse;
            }
            string token = await GetSystemAdminLoginToken(publicbaseHttpRequestContext);
            apiResponse = await GetAddressFinder(token, st, publicbaseHttpRequestContext);
            return apiResponse;

        }


        private async Task<ApiResponse<string>> GetAddressFinder(string token, string st,PublicBaseHttpRequestContext publicbaseHttpRequestContext)
        {
            // Create a dictionary
            Dictionary<string, string> customHeaders = new Dictionary<string, string>();

            // Add a key-value pair
            customHeaders.Add("referer", publicbaseHttpRequestContext.Referer);
            string addressFinderApiUrl = _appSettings.ApiUrls["MasterServiceUrl"] + "/master/address_finder?pn=1&ps=100&st=" + st;
            RestClient restClient = new RestClient(addressFinderApiUrl, null, token, null, customHeaders);
            var httpResponse = await restClient.GetAsync<ApiResponse<string>>(addressFinderApiUrl);
            return httpResponse;

        }

        public async Task<ApiResponse<string>> GetAddressByIdBAL(PublicBaseHttpRequestContext publicbaseHttpRequestContext, string addressId)
        {
            ApiResponse<string> apiResponse = new();

            if (publicbaseHttpRequestContext.tt.ToLower() != "acc")
            {
                apiResponse.StatusCode = 400;
                apiResponse.Errors.Add("Not a Valid Token, Need to provide Access token");
                apiResponse.Message = "failed";
                return apiResponse;
            }
            string token = await GetSystemAdminLoginToken(publicbaseHttpRequestContext);
            apiResponse = await GetAddressFinderbyId(token, addressId, publicbaseHttpRequestContext);
            return apiResponse;

        }

        private async Task<ApiResponse<string>> GetAddressFinderbyId(string token, string addressId, PublicBaseHttpRequestContext publicbaseHttpRequestContext)
        {
            // Create a dictionary
            Dictionary<string, string> customHeaders = new Dictionary<string, string>();

            // Add a key-value pair
            customHeaders.Add("referer", publicbaseHttpRequestContext.Referer);
            string endpoint = string.Format("/master/address_finder/{0}", addressId);
            string addressFinderApiUrl = _appSettings.ApiUrls["MasterServiceUrl"] + endpoint;

            RestClient restClient = new RestClient(addressFinderApiUrl, null, token, null, customHeaders);
            var httpResponse = await restClient.GetAsync<ApiResponse<string>>(addressFinderApiUrl);
            return httpResponse;

        }


        public async Task<ApiResponse<string>> tokenValidation(PublicBaseHttpRequestContext publicbaseHttpRequestContext)
        {
            ApiResponse<string> apiRes = new();
            PatientTokenAssoc tokenAssoc = await _patientRegDAL.GetPatientToken(publicbaseHttpRequestContext.PrId);
            if (tokenAssoc == null || tokenAssoc.PublicJwtToken != publicbaseHttpRequestContext.BearerToken)
            {
                apiRes.StatusCode = StatusCodes.Status400BadRequest;
                apiRes.Errors.Add("Not a Valid Token, Token in DB does not exists");
                apiRes.Message = "failed";
                return apiRes;
            }

            apiRes.StatusCode = StatusCodes.Status200OK;
            apiRes.Message = "Success";
            return apiRes;
        }



}
}
