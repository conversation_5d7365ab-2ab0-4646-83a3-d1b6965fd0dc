﻿using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.Patient.Interfaces;
using Capstone2.RestServices.Patient.Models;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Patient.Services
{
    public class MedicalRecordsBAL : IMedicalRecordsBAL
    {
        public readonly IMedicalRecordsDAL _medicalRecordsDAL;

        public MedicalRecordsBAL(IMedicalRecordsDAL medicalRecordsDAL)
        {
            _medicalRecordsDAL = medicalRecordsDAL;
        }

        /// <summary>
        /// Get MedicalRecordsDataby Patient ID
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<MedicalRecords>> GetMedicalRecords(BaseHttpRequestContext baseHttpRequestContext, long patientId)
        {
            var orgId = baseHttpRequestContext.OrgId;

            var medicalProcedures = await _medicalRecordsDAL.GetMedicalProcedures(orgId, patientId);

            var medicalHistory = await _medicalRecordsDAL.GetMedicalHistory(orgId, patientId);

            return new ApiResponse<MedicalRecords>
            {
                StatusCode = StatusCodes.Status200OK,
                Message = "Success",
                Result = new MedicalRecords
                {
                    MedicalProcedures = medicalProcedures,
                    MedicalHistory = medicalHistory
                }
            };
        }

        /// <summary>
        /// Method to save a entry in MedicalRecordsData
        /// </summary>
        /// <param name="MedicalRecordsPayload"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> AddMedicalRecord(BaseHttpRequestContext baseHttpRequestContext, long patientId, MedicalProcedures inputMedicalProcedures)
        {
            var orgId = baseHttpRequestContext.OrgId;
            var userId = baseHttpRequestContext.UserId;

            inputMedicalProcedures.PatientDetailsId = patientId;
            inputMedicalProcedures.IsActive = true;
            inputMedicalProcedures.OrgId = orgId;
            inputMedicalProcedures.CreatedBy = userId;
            inputMedicalProcedures.CreatedDate = DateTime.UtcNow;
            inputMedicalProcedures.StatusId = (short)Status.Active;

            DateTime.SpecifyKind(inputMedicalProcedures.CreatedDate, DateTimeKind.Utc);
            var id = await _medicalRecordsDAL.AddMedicalRecord(inputMedicalProcedures);
            if (id > 0)
            {
                ApiResponse<long?> apiResponse = new()
                {
                    StatusCode = StatusCodes.Status200OK,
                    Message = "Success",
                    Result = id
                };
                return apiResponse;
            }
            else
            {
                ApiResponse<long?> apiResponse = new()
                {
                    StatusCode = StatusCodes.Status400BadRequest,
                    Message = "Failure",
                    Result = null
                };
                return apiResponse;
            }
        }

        /// <summary>
        /// Edit MedicalRecords
        /// </summary>
        /// <param name="id"></param>
        /// <param name="inputMedicalRecords"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<string>> EditMedicalRecord(BaseHttpRequestContext baseHttpRequestContext, long patientId, long recordId, MedicalProcedures inputMedicalProcedures)
        {
            ApiResponse<string> apiResponse = new();
            var orgId = baseHttpRequestContext.OrgId;
            var userId = baseHttpRequestContext.UserId;

            List<MedicalProcedures> procedures = new();

            var xxistingMedicalProcedures = await _medicalRecordsDAL.GetExistingMedicalProceduresById(recordId);
            xxistingMedicalProcedures.ModifiedBy = userId;
            xxistingMedicalProcedures.ModifiedDate = DateTime.UtcNow;
            xxistingMedicalProcedures.StatusId = (short)Status.Inactive;
            procedures.Add(xxistingMedicalProcedures);

            inputMedicalProcedures.PatientDetailsId = patientId;
            inputMedicalProcedures.IsActive = xxistingMedicalProcedures.IsActive;
            inputMedicalProcedures.OrgId = orgId;
            inputMedicalProcedures.CreatedBy = userId;
            inputMedicalProcedures.CreatedDate = DateTime.UtcNow;
            inputMedicalProcedures.StatusId = (short)Status.Active;
            procedures.Add(inputMedicalProcedures);

            await _medicalRecordsDAL.UpdateMedicalRecord(procedures);

            apiResponse.Result = "Success";
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            return apiResponse;
        }

        /// <summary>
        /// Delete MedicalRecords
        /// </summary>
        /// <param name="id"></param>
        /// <param name="inputMedicalRecords"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<string>> DeleteMedicalRecord(BaseHttpRequestContext baseHttpRequestContext, long patientId, long recordId)
        {
            ApiResponse<string> apiResponse = new();
            var orgId = baseHttpRequestContext.OrgId;
            var userId = baseHttpRequestContext.UserId;

            List<MedicalProcedures> procedures = new();

            var xxistingMedicalProcedures = await _medicalRecordsDAL.GetExistingMedicalProceduresById(recordId);
            xxistingMedicalProcedures.ModifiedBy = userId;
            xxistingMedicalProcedures.ModifiedDate = DateTime.UtcNow;
            xxistingMedicalProcedures.StatusId = (short)Status.Deleted;
            procedures.Add(xxistingMedicalProcedures);

            await _medicalRecordsDAL.UpdateMedicalRecord(procedures);

            apiResponse.Result = "Success";
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            return apiResponse;
        }

        /// <summary>
        /// Update Verify Status
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="patientId"></param>
        /// <param name="inpoutVerificationStatus"></param>
        /// <returns></returns>
        public async Task<ApiResponse<string>> VerifyMedicalRecord(BaseHttpRequestContext baseHttpRequestContext, long patientId, VerificationStatus inpoutVerificationStatus)
        {
            ApiResponse<string> apiResponse = new();
            var orgId = baseHttpRequestContext.OrgId;
            var userId = baseHttpRequestContext.UserId;

            List<MedicalHistory> procedures = new();

            var xxistingMedicalHistory = await _medicalRecordsDAL.GetExistingMedicalHistory(orgId, patientId, inpoutVerificationStatus.Type);
            if (xxistingMedicalHistory != null)
            {
                xxistingMedicalHistory.ModifiedBy = userId;
                xxistingMedicalHistory.ModifiedDate = DateTime.UtcNow;
                xxistingMedicalHistory.StatusId = (short)Status.Inactive;
                procedures.Add(xxistingMedicalHistory);
            }

            if (inpoutVerificationStatus.IsVerified)
            {
                var newMedicalHistory = new MedicalHistory();
                newMedicalHistory.Type = inpoutVerificationStatus.Type;
                newMedicalHistory.StatusId = (short)Status.Active;
                newMedicalHistory.PatientDetailsId = patientId;
                newMedicalHistory.OrgId = orgId;
                newMedicalHistory.CreatedBy = userId;
                newMedicalHistory.CreatedDate = DateTime.UtcNow;
                procedures.Add(newMedicalHistory);
            }

            await _medicalRecordsDAL.VerifyMedicalRecord(procedures);

            apiResponse.Result = "Success";
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            return apiResponse;
        }

        /// <summary>
        /// Validate Medical Procedure by Name
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="patientId"></param>
        /// <param name="procedureName"></param>
        /// <returns></returns>
        public async Task<ApiResponse<bool>> ValidateMedicalProcedureName(BaseHttpRequestContext baseHttpRequestContext, long patientId, short type, long record_id, string search_term)
        {
            var orgId = baseHttpRequestContext.OrgId;

            var xxistingMedicalHistory = await _medicalRecordsDAL.FetchMedicalProcedureByName(patientId, orgId, type, search_term);

            if (record_id > 0)
            {
                xxistingMedicalHistory = xxistingMedicalHistory.FindAll(x => x.Id != record_id);
            }

            return new ApiResponse<bool>
            {
                Result = xxistingMedicalHistory.Count > 0,
                StatusCode = StatusCodes.Status200OK,
                Message = "Success"
            };
        }

        
        public async Task<ApiResponse<string>> UpdateProcedureStatus(BaseHttpRequestContext baseHttpRequestContext, long patientId, long recordId, ProcedureStatusUpdateRequest procedureStatusUpdateRequest)
        {
            ApiResponse<string> apiResponse = new();
            var orgId = baseHttpRequestContext.OrgId;
            var userId = baseHttpRequestContext.UserId;

            var xxistingMedicalProcedures = await _medicalRecordsDAL.GetExistingMedicalProceduresById(recordId);
            xxistingMedicalProcedures.IsActive = procedureStatusUpdateRequest.IsAcive;
            xxistingMedicalProcedures.ModifiedBy = userId;
            xxistingMedicalProcedures.ModifiedDate = DateTime.UtcNow;

            await _medicalRecordsDAL.UpdateProcedureStatus(xxistingMedicalProcedures);

            apiResponse.Result = "Success";
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            return apiResponse;
        }
    }
}
