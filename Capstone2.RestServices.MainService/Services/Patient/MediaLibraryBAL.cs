﻿using AutoMapper;
using Microsoft.Extensions.Configuration;
using Capstone2.Framework.RestApi;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.Patient.Interfaces;
using Capstone2.RestServices.Patient.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Transactions;
using System.Web;
using Capstone2.Framework.RestApi.Utility;

namespace Capstone2.RestServices.Patient.Services
{
    public class MediaLibraryBAL : IMediaLibraryBAL
    {
        private readonly IMediaLibraryDAL _mediaLibraryDAL;
        private readonly ICommDAL _commDAL;
        private readonly IActivityLogDAL _activityDAL;
        private readonly ILetterDAL _letterDAL;
        public readonly AppSettings _appSettings;
        public IMapper _mapper;
        private readonly IConfiguration _configuration;
        private IASBMessageSenderHelper _asbMessageSenderHelper;

        public MediaLibraryBAL(IOptions<AppSettings> appSettings, IMediaLibraryDAL mediaLibraryDAL, ICommDAL commDAL, IMapper mapper,IActivityLogDAL activityDAL,ILetterDAL letterDAL, IConfiguration configuration, IASBMessageSenderHelper asbMessageSenderHelper)
        {
            _appSettings = appSettings.Value;
            _mediaLibraryDAL = mediaLibraryDAL;
            _commDAL = commDAL;
            _mapper = mapper;
            _activityDAL = activityDAL;
            _letterDAL = letterDAL;
            _configuration = configuration;
            _asbMessageSenderHelper = asbMessageSenderHelper;
        }

        /// <summary>
        /// Method to add a new media in patient media library
        /// </summary>
        /// <param name="media"></param>
        /// <param name="patientId"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> AddMedia(PatientMediaAssoc media, long patientId, BaseHttpRequestContext baseHttpRequestContext)
        {
            media.OrgId = baseHttpRequestContext.OrgId;
            media.PatientDetailsId = patientId;
            media.StatusId = (short)Status.Active;
            media.CreatedBy = baseHttpRequestContext.UserId;
            long? id = await _mediaLibraryDAL.AddMedia(media);
            ApiResponse<long?> apiResponse = new();
            if (id > 0)
            {
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";
                apiResponse.Result = id;
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Errors.Add("Media cannot be added at this time.");
            }
            return apiResponse;
        }

        //private CommunicationNote AddActiveLogEntry(PatientMediaAssoc media, long patientId, BaseHttpRequestContext baseHttpRequestContext, short ops, PatientMediaAssoc oldMedia, Guid? bulkGroupId, int bulkActivityCount)
        //{
        //    CommunicationNote commNote = new();
        //    commNote.OrgId = baseHttpRequestContext.OrgId;
        //    commNote.CreatedDate = DateTime.UtcNow;
        //    commNote.ModifiedDate = DateTime.UtcNow;
        //    commNote.ModifiedBy = baseHttpRequestContext.UserId;
        //    commNote.PatientDetailsId = patientId;
        //    commNote.CommGroupTypeId = (short)CommGroupType.Media_Library;
        //    commNote.CommTypeId = (short)CommType.Media_Library;
        //    if (ops == (short)ActivityLogOps.File_Upload)
        //    {
        //        commNote.FileDetailsId = media.FileDetailsId;
        //        string fileName = media.MediaName;
        //        string operationName = "File Uploaded.";
        //        commNote.Notes = operationName + "\n File Name:" + fileName;
        //        commNote.MediaLibrarySubTypeId = (short)MediaLibrarySubType.File_Upload;
        //        commNote.GroupId = media.GroupId;
        //        commNote.IsParent = false;

        //    }
        //    else if (ops == (short)ActivityLogOps.File_Delete)
        //    {
        //        commNote.FileDetailsId = null;
        //        string fileName = media.MediaName;
        //        string deleteReason = string.Empty;
        //        if (!string.IsNullOrWhiteSpace(media.DeleteReason))
        //            deleteReason = (media.DeleteReason.LastIndexOf('.') == media.DeleteReason.Length - 1) ? media.DeleteReason : media.DeleteReason + '.';

        //        string operationName = "File Deleted.Delete Reason:" + deleteReason;
        //        commNote.Notes = operationName + "\r\nFile Name:" + fileName;
        //        if (bulkGroupId is not null)
        //        {
        //            commNote.IsParent = false;
        //            commNote.GroupId = bulkGroupId;
        //            commNote.MediaLibrarySubTypeId = (short)MediaLibrarySubType.File_Update;

        //        }
        //        else
        //        {
        //            commNote.MediaLibrarySubTypeId = (short)MediaLibrarySubType.Media_Update;
        //        }

        //    }
        //    else if (ops == (short)ActivityLogOps.File_Rename)
        //    {
        //        commNote.FileDetailsId = media.FileDetailsId;
        //        string fileName = media.MediaName;
        //        string operationName = "File Renamed from " + oldMedia.MediaName + " to " + media.MediaName;
        //        commNote.Notes = operationName;
        //        if (bulkGroupId is not null)
        //        {
        //            commNote.IsParent = false;
        //            commNote.GroupId = bulkGroupId;
        //            commNote.MediaLibrarySubTypeId = (short)MediaLibrarySubType.File_Update;

        //        }
        //        else
        //        {
        //            commNote.MediaLibrarySubTypeId = (short)MediaLibrarySubType.Media_Update;
        //        }

        //    }
        //    else if (ops == (short)ActivityLogOps.File_Tags_Update)
        //    {
        //        commNote.FileDetailsId = media.FileDetailsId;
        //        string fileName = media.MediaName;
        //        string operationName = "Media Tags Updated.";
        //        commNote.Notes = operationName + "\n File Name: " + fileName;
        //        if (bulkGroupId is not null)
        //        {
        //            commNote.IsParent = false;
        //            commNote.GroupId = bulkGroupId;
        //            commNote.MediaLibrarySubTypeId = (short)MediaLibrarySubType.File_Update;
        //        }
        //        else
        //        {
        //            commNote.MediaLibrarySubTypeId = (short)MediaLibrarySubType.Media_Update;
        //        }
        //    }
        //    else if (ops == (short)ActivityLogOps.Bulk_Delete || ops == (short)ActivityLogOps.Bulk_Tags_Update)
        //    {
        //        string operationName = string.Empty;
        //        if (ops == (short)ActivityLogOps.Bulk_Delete)
        //            operationName = "Number of files Deleted:" + bulkActivityCount;
        //        else
        //            operationName = "Number of files Updated:" + bulkActivityCount;
        //        commNote.Notes = operationName;
        //        commNote.MediaLibrarySubTypeId = (short)MediaLibrarySubType.Bulk_Media_Update;
        //        commNote.GroupId = bulkGroupId;
        //    }
        //    return commNote;
        //}
        /// <summary>
        /// Method to create an ActivityLog object
        /// </summary>
        /// <param name="activityLogDB"></param>
        /// <param name="activityLogOp"></param>
        /// <param name="lstVariableJSON"></param>
        /// <param name="id"></param>
        /// <param name="patientDetailsId"></param>
        /// <param name="appointmentStatus"></param>
        /// <returns></returns>
        public ActivityLog CreateActivityLogEntry(int activityLogOp,  long? patientDetailsId, PatientMediaAssoc media, PatientMediaAssoc oldMedia, int? episodesId, Guid? bulkGroupId, int bulkActivityCount, BaseHttpRequestContext baseHttpRequestContext)
        {
            ActivityLog activityLog = new();
            activityLog.ActivityLogTypeId = (short)ActivityLogType.Media_Library;
            activityLog.PatientDetailsId = (long)patientDetailsId;
            activityLog.EntityId = (media is not null)?media.Id:null;
            activityLog.OrgId = baseHttpRequestContext.OrgId;
            activityLog.CreatedDate = DateTime.UtcNow;
            activityLog.ModifiedDate = DateTime.UtcNow;
            activityLog.ModifiedBy = baseHttpRequestContext.UserId;
            activityLog.EpisodesId = (media is not null) ? media.EpisodesId:null;
            if (media is not null && media.EpisodesId is not null)
            {
                activityLog.EpisodesId = media.EpisodesId;
            }
            if (media is null)
            {
                activityLog.EpisodesId = episodesId;
            }
            if (activityLogOp == (short)ActivityLogOps.File_Delete && bulkGroupId is null)
            {

                string fileName = media.MediaName;
                string deleteReason = string.Empty;
                if (!string.IsNullOrWhiteSpace(media.DeleteReason))
                    deleteReason = (media.DeleteReason.LastIndexOf('.') == media.DeleteReason.Length - 1) ? media.DeleteReason : media.DeleteReason + '.';

                string operationName = "File Deleted.Delete Reason:" + deleteReason;
                activityLog.Description = operationName + "File Name:" + fileName;

                activityLog.Title = EnumExtensions.GetDescription(MediaLibrarySubType.Media_Update);

            }
            else if (activityLogOp == (short)ActivityLogOps.File_Delete && bulkGroupId is not null)
            {



            }
            else if (activityLogOp == (short)ActivityLogOps.File_Rename)
            {
                activityLog.FileDetailsId = media.FileDetailsId;
                activityLog.EntityId = media.Id;
                string fileName = media.MediaName;
                activityLog.Description = "File Renamed from " + oldMedia.MediaName + " to " + media.MediaName;
                activityLog.Title = EnumExtensions.GetDescription(MediaLibrarySubType.Media_Update);

            }
            else if (activityLogOp == (short)ActivityLogOps.File_Tags_Update)
            {
                activityLog.FileDetailsId = media.FileDetailsId;
                activityLog.EntityId = media.Id;
                string fileName = media.MediaName;
                string operationName = "Media Tags Updated.";
                activityLog.Description = operationName + "File Name: " + fileName;
                activityLog.Title = EnumExtensions.GetDescription(MediaLibrarySubType.Media_Update);
            }
            else if (activityLogOp == (short)ActivityLogOps.Episode_Update)
            {
                activityLog.FileDetailsId = media.FileDetailsId;
                activityLog.EntityId = media.Id;
                string fileName = media.MediaName;
                string operationName = "Media Episode Updated.";
                activityLog.Description = operationName + "File Name: " + fileName;
                activityLog.Title = EnumExtensions.GetDescription(MediaLibrarySubType.Media_Update);
            }
            else if (activityLogOp == (short)ActivityLogOps.Bulk_Delete || activityLogOp == (short)ActivityLogOps.Bulk_Tags_Update)
            {
                string operationName = string.Empty;
                if (activityLogOp == (short)ActivityLogOps.Bulk_Delete)
                    operationName = "Number of files Deleted:" + bulkActivityCount;
                else
                    operationName = "Number of files Updated:" + bulkActivityCount;
                activityLog.Description = operationName;
                activityLog.Title = EnumExtensions.GetDescription(MediaLibrarySubType.Bulk_Media_Update);
                activityLog.ActivityLogSubTypeId =(short?)MediaLibrarySubType.Bulk_Media_Update;
            }
            return activityLog;
        }
        /// <summary>
        /// Add Activity log child Entries
        /// </summary>
        /// <param name="activityLogOp"></param>
        /// <param name="lstVariableJSON"></param>
        /// <param name="id"></param>
        /// <param name="patientDetailsId"></param>
        /// <param name="appointmentStatus"></param>
        /// <param name="notes"></param>
        /// <returns></returns>
        private ActivityLogChildEntry CreateActivityLogChildEntry(int activityLogOp, List<VariableJSON> lstVariableJSON,  PatientMediaAssoc media, PatientMediaAssoc oldMedia, Guid? bulkGroupId, int bulkActivityCount,BaseHttpRequestContext baseHttpRequestContext)
        {
            ActivityLogChildEntry activityLogChild = new();
            activityLogChild.OrgId = baseHttpRequestContext.OrgId;
            activityLogChild.CreatedDate = DateTime.UtcNow;
            activityLogChild.ModifiedDate = DateTime.UtcNow;
            activityLogChild.ModifiedBy = baseHttpRequestContext.UserId;
            activityLogChild.EntityId = (media.Id <= 0) ? null : media.Id;
            if ((int)ActivityLogOps.File_Upload == activityLogOp)
            {
                activityLogChild.FileDetailsId =  media.FileDetailsId;
                string fileName = media.MediaName;
                activityLogChild.Description = "File Uploaded. File Name:"+fileName;
                activityLogChild.Title = EnumExtensions.GetDescription(MediaLibrarySubType.File_Upload);
            }
            else if (activityLogOp == (short)ActivityLogOps.File_Delete)
            {
                activityLogChild.FileDetailsId = null;
                string fileName = media.MediaName;
                string deleteReason = string.Empty;
                if (!string.IsNullOrWhiteSpace(media.DeleteReason))
                    deleteReason = (media.DeleteReason.LastIndexOf('.') == media.DeleteReason.Length - 1) ? media.DeleteReason : media.DeleteReason + '.';

                string operationName = "File Deleted.Delete Reason:" + deleteReason;
                activityLogChild.Description  = operationName + "File Name:" + fileName;
                activityLogChild.EntityId = (media.Id <=0)?null:media.Id;
                activityLogChild.Title = EnumExtensions.GetDescription(MediaLibrarySubType.File_Update);


            }
            else if (activityLogOp == (short)ActivityLogOps.File_Rename)
            {
                activityLogChild.FileDetailsId = media.FileDetailsId;
                activityLogChild.EntityId = media.Id;
                string fileName = media.MediaName;
                activityLogChild.Description = "File Renamed from " + oldMedia.MediaName + " to " + media.MediaName;
                if (bulkGroupId is not null)
                {
                    activityLogChild.Title = EnumExtensions.GetDescription(MediaLibrarySubType.File_Update);

                }
                else
                {
                    activityLogChild.Title = EnumExtensions.GetDescription(MediaLibrarySubType.Media_Update);
                }

            }
            else if (activityLogOp == (short)ActivityLogOps.File_Tags_Update)
            {
                activityLogChild.EntityId = media.Id;
                activityLogChild.FileDetailsId = media.FileDetailsId;
                string fileName = media.MediaName;
                string operationName = "Media Tags Updated.";
                activityLogChild.Description = operationName + "File Name: " + fileName;
                if (bulkGroupId is not null)
                {
                    activityLogChild.Title = EnumExtensions.GetDescription(MediaLibrarySubType.File_Update);//(short)MediaLibrarySubType.File_Update;
                }
                else
                {
                    activityLogChild.Title = EnumExtensions.GetDescription(MediaLibrarySubType.Media_Update);
                }
            }
            else if (activityLogOp == (short)ActivityLogOps.Episode_Update)
            {
                activityLogChild.FileDetailsId = media.FileDetailsId;
                activityLogChild.EntityId = media.Id;
                string fileName = media.MediaName;
                string operationName = "Media Episode Updated.";
                activityLogChild.Description = operationName + "File Name: " + fileName;
                if (bulkGroupId is not null)
                {
                    activityLogChild.Title = EnumExtensions.GetDescription(MediaLibrarySubType.File_Update);//(short)MediaLibrarySubType.File_Update;
                }
                else
                {
                    activityLogChild.Title = EnumExtensions.GetDescription(MediaLibrarySubType.Media_Update);
                }
            }
            else if (activityLogOp == (short)ActivityLogOps.Bulk_Delete || activityLogOp == (short)ActivityLogOps.Bulk_Tags_Update)
            {
                activityLogChild.EntityId = media.Id;
                string operationName = string.Empty;
                if (activityLogOp == (short)ActivityLogOps.Bulk_Delete)
                    activityLogChild.Description = "Number of files Deleted:" + bulkActivityCount;
                else
                    activityLogChild.Description = "Number of files Updated:" + bulkActivityCount;

                activityLogChild.Title = EnumExtensions.GetDescription(MediaLibrarySubType.Media_Update);


            }
            //else if((int)ActivityLogOps.Update == activityLogOp)
            //{

            //        activityLogChild.VariableJson = (variableJSON is null) ? null : JsonConvert.SerializeObject(variableJSON);
            //        activityLogChild.ActivityStatusId = appointmentStatus;
            //        activityLog.ActivityLogChildEntries.Add(activityLogChild);

            //}
            return activityLogChild;
        }
        /// <summary>
        /// Method to create a new tag
        /// </summary>
        /// <param name="tag"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> AddTag(Tag tag, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<long?> apiResponse = new();
            tag.OrgId = baseHttpRequestContext.OrgId;
            tag.StatusId = (short)Status.Active;
            tag.CreatedBy = baseHttpRequestContext.UserId;

            long id = await _mediaLibraryDAL.AddTag(tag);
            if (id > 0)
            {
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Result = id;
                apiResponse.Message = "Success";
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = null;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("Tags cannot be added at this time.Please try again later.");
            }
            return apiResponse;
        }

        /// <summary>
        /// Method to fetch the media for a patient to display in media library
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="queryModel"></param>
        /// <param name="patient_id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<QueryResultList<ListPatientMediaAssoc>>> ListMediaLibrary(BaseHttpRequestContext baseHttpRequestContext, QueryModel queryModel, long patient_id)
        {
            ApiResponse<QueryResultList<ListPatientMediaAssoc>> apiResponse = new();         
            MediaLibraryFilterModel filterModel = PrepareFilterParameters(queryModel.Filter);

            QueryResultList<ListPatientMediaAssoc> queryList = await _mediaLibraryDAL.ListMediaLibrary(baseHttpRequestContext.OrgId, queryModel, filterModel, patient_id);
            if (queryList.ItemRecords is not null && queryList.ItemRecords.Any())
            {
                queryList.ItemRecords = await FetchFileDetailsForMediaAsync(queryList.ItemRecords.ToList(), baseHttpRequestContext);
            }

            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result = queryList;
            return apiResponse;
        }
        private MediaLibraryFilterModel PrepareFilterParameters(string filter)
        {
            if (!(string.IsNullOrEmpty(filter)))
            {
                MediaLibraryFilterModel mediaLibraryFilter = JsonConvert.DeserializeObject<MediaLibraryFilterModel>(filter);
                return mediaLibraryFilter;
            }
            else
            {
                return null;
            }
        }

        private async Task<List<ListPatientMediaAssoc>> FetchFileDetailsForMediaAsync(List<ListPatientMediaAssoc> paginatedList, BaseHttpRequestContext baseHttpRequestContext)
        {
            List<long> lstFileDetailsId = paginatedList.Select(x => x.FileDetailsId).ToList();
            List<FileDetailsOutputForId> fileResponse = new();
            if (lstFileDetailsId.Count > 0)
            {
                string stringFileDetailsId = string.Join(",", lstFileDetailsId);
                var token = baseHttpRequestContext.BearerToken;
                string interServiceToken = baseHttpRequestContext.InterServiceToken;
                string fileAPiUrl = _appSettings.ApiUrls["FileServiceUrl"] + "/file/file_details_list?" + "stringFileDetailsId" + "=" + HttpUtility.UrlEncode(stringFileDetailsId);
                RestClient restClient = new RestClient(fileAPiUrl, null, token, interServiceToken);
                var fileApiResponse = await restClient.GetAsync<ApiResponse<List<FileDetailsOutputForId>>>(fileAPiUrl, null);
                if (fileApiResponse.StatusCode == StatusCodes.Status200OK)
                {
                    fileResponse = fileApiResponse.Result;
                }
                if (fileResponse.Count > 0)
                {
                    paginatedList.ForEach(x =>
                    {
                        FileDetailsOutputForId fileDetailsOutputForId = fileResponse.Find(f => f.FileDetail.Id == x.FileDetailsId);
                        x.FileDetailsOutput = fileDetailsOutputForId;
                    });
                }
            }
            return paginatedList;
        }


        /// <summary>
        /// Method to retrieve Tags
        /// </summary>
        /// <param name="searchTerm"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<List<Tag>>> ListTags(string searchTerm, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<List<Tag>> apiResponse = new();            
            apiResponse.Result = await _mediaLibraryDAL.ListTags(searchTerm, baseHttpRequestContext.OrgId);
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            return apiResponse;
        }

        /// <summary>
        /// Method to add media collection to media library
        /// </summary>
        /// <param name="mediaCollection"></param>
        /// <param name="patientId"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<string>> AddPatientMedias(InputPatientMediaAssoc mediaCollection, long patientId, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<string> apiResponse = new ApiResponse<string>();
            List<CommunicationNote> lstCommNotes = new();
            bool isConsultNote = false;
            List<ActivityLogChildEntry> lstActivtyLogChild = new();

            if (mediaCollection is not null && mediaCollection.PatientMediaAssocs is not null && mediaCollection.PatientMediaAssocs.Count > 0)
            {
                mediaCollection.PatientMediaAssocs.ToList().ForEach(media =>
                {
                    media.OrgId = baseHttpRequestContext.OrgId;
                    media.PatientDetailsId = patientId;
                    media.StatusId = (short)Status.Active;
                    media.CreatedBy = baseHttpRequestContext.UserId;
                    if (media.MediaLibrarySubTypeId is null || !media.MediaLibrarySubTypeId.HasValue)
                        media.MediaLibrarySubTypeId = (short)MediaLibrarySubType.File_Upload;
                    else if(media.MediaLibrarySubTypeId == (short)MediaLibrarySubType.Consult_Note_Template && isConsultNote == false){
                        isConsultNote = true;

                    }
                    if (media.PatientMediaTagsAssocs is not null)
                    {
                        media.PatientMediaTagsAssocs.ToList().ForEach(mediaTag =>
                        {
                            mediaTag.OrgId = baseHttpRequestContext.OrgId;
                            mediaTag.StatusId = (short)Status.Active;
                            mediaTag.CreatedBy = baseHttpRequestContext.UserId;
                            mediaTag.CreatedDate = DateTime.UtcNow;
                        });
                    }
                });

                using (var transaction = new TransactionScope(TransactionScopeOption.Required, TransactionScopeAsyncFlowOption.Enabled))
                {
                    short ops;

                    int rows = await _mediaLibraryDAL.AddMediaCollection(mediaCollection);
                    if (rows > 0 )
                    {
                        if(isConsultNote == false) { 

                            foreach (PatientMediaAssoc media in mediaCollection.PatientMediaAssocs)
                            //mediaCollection.PatientMediaAssocs.ToList().ForEach(async media =>
                            {
                                ops = (media.MediaLibrarySubTypeId is null || media.MediaLibrarySubTypeId == (short)MediaLibrarySubType.File_Upload) ? (short)ActivityLogOps.File_Upload : (short)ActivityLogOps.Letter_Upload;
                                //CommunicationNote commNote = AddActiveLogEntry(media, patientId, baseHttpRequestContext, ops, null);
                                //lstCommNotes.Add(commNote);
                                if (mediaCollection.ActivityLogId is not null)
                                {
                                    ActivityLogChildEntry childLogEntry = CreateActivityLogChildEntry(ops, null, media, null, null, 0, baseHttpRequestContext);
                                    childLogEntry.ActivityLogId = (long)mediaCollection.ActivityLogId;
                                    lstActivtyLogChild.Add(childLogEntry);
                                }


                            };
                            //await _commDAL.AddCommNotesRange(lstCommNotes);

                            if(lstActivtyLogChild is not null && lstActivtyLogChild.Count > 0)
                            {
                                await _activityDAL.AddActivtyLogChildEntryRange(lstActivtyLogChild);
                            }
                        }


                        apiResponse.StatusCode = StatusCodes.Status200OK;
                        apiResponse.Result = "Success";
                        apiResponse.Message = "Success";
                        transaction.Complete();
                        return apiResponse;
                    }
                    else
                    {
                        transaction.Dispose();
                    }
                }
            }

            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            apiResponse.Errors.Add("Media cannot be added at this time.");
            return apiResponse;
        }

        //private CommunicationNote AddActiveLogEntry(PatientMediaAssoc media, long patientId, BaseHttpRequestContext baseHttpRequestContext, short ops, PatientMediaAssoc oldMedia)
        //{
        //    return AddActiveLogEntry(media, patientId, baseHttpRequestContext, ops, oldMedia, null, 0);
        //}

        /// <summary>
        /// Method to soft delete a media in media library
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="patientId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<string>> DeleteMedia(BaseHttpRequestContext baseHttpRequestContext, long patientId, long id, DeleteObject deleteObject)
        {
            ApiResponse<string> apiResponse = new ApiResponse<string>();
            if (deleteObject is null || string.IsNullOrWhiteSpace(deleteObject.DeleteReason))
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Errors.Add("Delete Reason is mandatory.");
                apiResponse.Message = "Failure";
                return apiResponse;
            }
            PatientMediaAssoc media = await _mediaLibraryDAL.GetMediaAssocs(id, patientId, baseHttpRequestContext.OrgId);
            if (media is not null)
            {
                using (var transaction = new TransactionScope(TransactionScopeOption.Required, TransactionScopeAsyncFlowOption.Enabled))
                {
                    if (media.PatientMediaTagsAssocs is not null && media.PatientMediaTagsAssocs.Any())
                    {
                        media.PatientMediaTagsAssocs.ToList().ForEach(a =>
                        {
                            a.ModifiedBy = baseHttpRequestContext.UserId;
                            a.ModifiedDate = DateTime.UtcNow;
                            a.StatusId = (short)Status.Deleted;
                            a.OrgId = baseHttpRequestContext.OrgId;
                            a.PatientMediaAssocId = id;
                        });
                    }
                    if (deleteObject is not null)
                        media.DeleteReason = deleteObject.DeleteReason;
                    media.PatientDetailsId = patientId;
                    media.ModifiedBy = baseHttpRequestContext.UserId;
                    media.ModifiedDate = DateTime.UtcNow;
                    media.StatusId = (short)Status.Deleted;
                    media.OrgId = baseHttpRequestContext.OrgId;
                    if (media.FileDetailsId > 0)
                    {
                        //Making a call to file service to soft delete a file
                        var token = baseHttpRequestContext.BearerToken;
                        string interServiceToken = baseHttpRequestContext.InterServiceToken;
                        string fileAPiUrl = _appSettings.ApiUrls["FileServiceUrl"] + "/file/file_details/" + media.FileDetailsId;
                        RestClient restClient = new RestClient(fileAPiUrl, null, token, interServiceToken);
                        var fileApiResponse = await restClient.DeleteAsync<ApiResponse<FileDetailsOutputForId>>(fileAPiUrl, null);
                        if (fileApiResponse.StatusCode == StatusCodes.Status200OK)
                        {
                            int rows = await _mediaLibraryDAL.UpdateMediaAssoc(media);
                            if (rows > 0)
                            {
                                //CommunicationNote commNote = AddActiveLogEntry(media, patientId, baseHttpRequestContext, (short)ActivityLogOps.File_Delete, null);
                                //await _commDAL.AddCommNotesDAL(commNote);
                                transaction.Complete();
                                apiResponse.StatusCode = StatusCodes.Status200OK;
                                apiResponse.Result = "Success";
                                apiResponse.Message = "Success";
                                return apiResponse;
                            }
                        }

                    }
                    transaction.Dispose();
                }

            }
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            apiResponse.Errors.Add("Media cannot be deleted at this time.");
            apiResponse.Message = "Failure";
            return apiResponse;
        }
        public async Task<ApiResponse<string>> DeleteMedia(BaseHttpRequestContext baseHttpRequestContext, long patientId, List<long> lstIds, DeleteObject deleteObject)
        {
            ApiResponse<string> apiResponse = new ApiResponse<string>();
            InputPatientMediaAssoc inputPatientMediaAssoc = new();
            short ops = (short)ActivityLogOps.File_Delete;
            Guid groupId = Guid.NewGuid();
            List<CommunicationNote> lstCommunicationNotes = new();
            List<ActivityLogChildEntry> lstActivityLogChildEntries = new();
            ActivityLog activityLog = new();
            List<long> listFileDetailsId = new();
            //lstIds.ForEachAsync( async mediaId => 
            //  {
            //      PatientMediaAssoc media = await _mediaLibraryDAL.GetMediaAssocsSync(mediaId, patientId, baseHttpRequestContext.OrgId);
            //      inputPatientMediaAssoc.PatientMediaAssocs.Add(media);
            //  });
            foreach(long mediaId in lstIds)
            {
                PatientMediaAssoc media = await _mediaLibraryDAL.GetMediaAssocsSync(mediaId, patientId, baseHttpRequestContext.OrgId);
                inputPatientMediaAssoc.PatientMediaAssocs.Add(media);
            }
            using (var transaction = new TransactionScope(TransactionScopeOption.Required, TransactionScopeAsyncFlowOption.Enabled))
            {
                if (inputPatientMediaAssoc.PatientMediaAssocs is not null && inputPatientMediaAssoc.PatientMediaAssocs.Count > 0)
                {
                    inputPatientMediaAssoc.PatientMediaAssocs.ToList().ForEach(media =>
                    {
                        if (media is not null)
                        {
                            if (media.PatientMediaTagsAssocs is not null && media.PatientMediaTagsAssocs.Any())
                            {
                                media.PatientMediaTagsAssocs.ToList().ForEach(a =>
                                {
                                    a.ModifiedBy = baseHttpRequestContext.UserId;
                                    a.ModifiedDate = DateTime.UtcNow;
                                    a.StatusId = (short)Status.Deleted;
                                    a.OrgId = baseHttpRequestContext.OrgId;
                                });
                            }
                            if (deleteObject is not null)
                                media.DeleteReason = deleteObject.DeleteReason;
                            media.PatientDetailsId = patientId;
                            media.ModifiedBy = baseHttpRequestContext.UserId;
                            media.ModifiedDate = DateTime.UtcNow;
                            media.StatusId = (short)Status.Deleted;
                            media.OrgId = baseHttpRequestContext.OrgId;
                            listFileDetailsId.Add(media.FileDetailsId);

                            inputPatientMediaAssoc.PatientMediaAssocs.Add(media);

                            if (deleteObject.IsBulkActivity is not null && deleteObject.IsBulkActivity == true)
                            {
                                //CommunicationNote commNote = AddActiveLogEntry(media, patientId, baseHttpRequestContext, ops, null, groupId, 0);
                                //lstCommunicationNotes.Add(commNote);
                                ActivityLogChildEntry childLogEntry = CreateActivityLogChildEntry(ops, null, media, null, groupId, 0, baseHttpRequestContext);
                                lstActivityLogChildEntries.Add(childLogEntry);

                            }
                            else
                            {
                                //CommunicationNote commNote = AddActiveLogEntry(media, patientId, baseHttpRequestContext, (short)ActivityLogOps.File_Delete, null);
                                //lstCommunicationNotes.Add(commNote);
                                activityLog = CreateActivityLogEntry((int)ActivityLogOps.File_Delete,  patientId,  media, null, inputPatientMediaAssoc.EpisodesId, null, 0, baseHttpRequestContext);
                            }

                        }
                    });
                }
                if (deleteObject.IsBulkActivity is not null && deleteObject.IsBulkActivity == true)
                {
                    //CommunicationNote commNote = AddActiveLogEntry(null, patientId, baseHttpRequestContext, (short)ActivityLogOps.Bulk_Delete, null, groupId, inputPatientMediaAssoc.PatientMediaAssocs.Count);
                    //lstCommunicationNotes.Add(commNote);
                    activityLog = CreateActivityLogEntry((short)ActivityLogOps.Bulk_Delete, patientId, null, null, inputPatientMediaAssoc.EpisodesId, groupId, inputPatientMediaAssoc.PatientMediaAssocs.Count, baseHttpRequestContext);

                    activityLog.ActivityLogChildEntries = lstActivityLogChildEntries;
                }
                await _mediaLibraryDAL.UpdateRangeMediaAssoc(inputPatientMediaAssoc);
                // await _commDAL.AddCommNotesRange(lstCommunicationNotes);
                await _activityDAL.AddActivityLog(activityLog);

                string stringFileDetailsId = string.Join(",", listFileDetailsId);
                var token = baseHttpRequestContext.BearerToken;
                string interServiceToken = baseHttpRequestContext.InterServiceToken;
                string fileAPiUrl = _appSettings.ApiUrls["FileServiceUrl"] + "/file/file_details_list?stringFileDetailsId" + "=" + HttpUtility.UrlEncode(stringFileDetailsId);
                RestClient restClient = new RestClient(fileAPiUrl, null, token, interServiceToken);
                var fileApiResponse = await restClient.DeleteAsync<ApiResponse<string>>(fileAPiUrl, null);

                transaction.Complete();
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Result = "Success";
                apiResponse.Message = "Success";
                return apiResponse;
            }

            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            apiResponse.Errors.Add("Media cannot be deleted at this time.");
            apiResponse.Message = "Failure";
            return apiResponse;
        }

        /// <summary>
        /// Method to soft delete a media in media library
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="filedetailsId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<string>> DeleteMediaFromFileId(int orgId, long userId, long patientId, long filedetailsId)
        {
            ApiResponse<string> apiResponse = new ApiResponse<string>();
            PatientMediaAssoc media = await _mediaLibraryDAL.GetMediaAssocsFromFileDetailsId(filedetailsId, patientId, orgId);
            if (media is not null)
            {
                if (media.PatientMediaTagsAssocs is not null && media.PatientMediaTagsAssocs.Any())
                {
                    media.PatientMediaTagsAssocs.ToList().ForEach(a =>
                    {
                        a.ModifiedBy = userId;
                        a.ModifiedDate = DateTime.UtcNow;
                        a.StatusId = (short)Status.Deleted;
                        a.OrgId = orgId;
                        a.PatientMediaAssocId = media.Id;
                    });
                }
                media.PatientDetailsId = patientId;
                media.ModifiedBy = userId;
                media.ModifiedDate = DateTime.UtcNow;
                media.StatusId = (short)Status.Deleted;
                media.OrgId = orgId;

                await _mediaLibraryDAL.UpdateMediaLibrary(media);

            }
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Result = "Success";
            apiResponse.Message = "Success";
            return apiResponse;
        }

        private List<PatientMediaTagsAssoc> AddUpdateMediaTags(ICollection<PatientMediaTagsAssoc> mediaTagsCollection, ICollection<PatientMediaTagsAssoc> dbMediaTagsCollection, BaseHttpRequestContext baseHttpRequestContext, long patientId, long mediaId)
        {
            ApiResponse<string> apiResponse = new ApiResponse<string>();
            int orgId = baseHttpRequestContext.OrgId;
            long loggedInUser = baseHttpRequestContext.UserId;
            List<PatientMediaTagsAssoc> lstAddMediaTags = new List<PatientMediaTagsAssoc>();
            List<PatientMediaTagsAssoc> lstUpdateMediaTags = new List<PatientMediaTagsAssoc>();
            List<PatientMediaTagsAssoc> lstInputMediaTags = new();
            if (mediaTagsCollection is not null && mediaTagsCollection is not null && mediaTagsCollection.Any())
            {
                lstInputMediaTags = mediaTagsCollection.ToList();
            }

            lstInputMediaTags.ForEach(mediaTagAssoc =>
            {
                if (mediaTagAssoc.Id > 0)
                {
                    var existingobj = dbMediaTagsCollection.FirstOrDefault(x => x.Id == mediaTagAssoc.Id);
                    dbMediaTagsCollection.Remove(existingobj);
                }
                else
                {
                    PatientMediaTagsAssoc mediaTagAssocDB = dbMediaTagsCollection.Where(x => x.TagsId == mediaTagAssoc.TagsId).FirstOrDefault();
                    if (mediaTagAssocDB is null)
                    {
                        PatientMediaTagsAssoc newMediaTagAssoc = new();
                        newMediaTagAssoc.OrgId = orgId;
                        newMediaTagAssoc.CreatedBy = loggedInUser;
                        newMediaTagAssoc.StatusId = (short)Status.Active;
                        newMediaTagAssoc.PatientMediaAssocId = mediaId;
                        newMediaTagAssoc.CreatedDate = DateTime.UtcNow;
                        newMediaTagAssoc.TagsId = mediaTagAssoc.TagsId;
                        lstAddMediaTags.Add(newMediaTagAssoc);
                    }
                    else
                    {
                        if (mediaTagAssocDB.StatusId == (short)Status.Deleted)
                        {
                            mediaTagAssocDB.StatusId = (short)Status.Active;
                            mediaTagAssocDB.ModifiedDate = DateTime.UtcNow;
                            mediaTagAssocDB.ModifiedBy = loggedInUser;
                            lstUpdateMediaTags.Add(mediaTagAssocDB);

                        }
                        dbMediaTagsCollection.Remove(mediaTagAssocDB);

                    }
                }
            });

            foreach (var mediaDB in dbMediaTagsCollection.Where(x => x.StatusId == (short)Status.Active))
            {
                mediaDB.StatusId = (short)Status.Deleted;
                mediaDB.ModifiedDate = DateTime.UtcNow;
                mediaDB.ModifiedBy = loggedInUser;
                lstUpdateMediaTags.Add(mediaDB);
            }
            List<PatientMediaTagsAssoc> patientMediaTagsAssocs = lstAddMediaTags.Concat(lstUpdateMediaTags).ToList();
            return patientMediaTagsAssocs;
        }


        /// <summary>
        /// Method to fetch media from id
        /// </summary>
        /// <param name="id"></param>
        /// <param name="patient_id"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<InputPatientMedia>> GetPatientMedia(long id, long patientId, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<InputPatientMedia> apiResponse = new();
            InputPatientMedia media = await _mediaLibraryDAL.GetPatientMedia(id, patientId, baseHttpRequestContext.OrgId);
            if (media is null)
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = media;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("The media doesnot exist.");

            }
            else
            {
                if(media?.FileDetailsId >default(long))
                {
                    long fileId = (long)media?.FileDetailsId;
                    var token = baseHttpRequestContext.BearerToken;
                    string interServiceToken = baseHttpRequestContext.InterServiceToken;
                    string fileAPiUrl = _appSettings.ApiUrls["FileServiceUrl"] + "/file/file_details/" + fileId;
                    RestClient restClient = new RestClient(fileAPiUrl, null, token, interServiceToken);
                    var fileApiResponse = await restClient.GetAsync<ApiResponse<FileDetailsOutputForId>>(fileAPiUrl, null);
                    if (fileApiResponse.StatusCode == StatusCodes.Status200OK)
                    {
                        media.FileDetailsOutput = fileApiResponse.Result;
                    }
                    else
                    {
                        apiResponse.StatusCode = fileApiResponse.StatusCode;
                        apiResponse.Errors.Add("The media cannot be retrieved.");
                        apiResponse.Message = fileApiResponse.Message;
                        return apiResponse;
                    }
                }

                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Result = media;
                apiResponse.Message = "Success";
            }

            return apiResponse;
        }

        /// <summary>
        /// method to update media
        /// </summary>
        /// <param name="inputPatientMedia"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="patientId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<PatientMediaAssoc>> EditPatientMedia(PatientMediaAssoc inputPatientMedia, BaseHttpRequestContext baseHttpRequestContext, long patientId, long id)
        {
            ApiResponse<PatientMediaAssoc> apiResponse = new();

            PatientMediaAssoc mediaDB = await _mediaLibraryDAL.GetMediaAssocs(id, patientId, baseHttpRequestContext.OrgId);
            if (mediaDB is null)
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = null;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("The media doesnot exist.");
                return apiResponse;
            }
            inputPatientMedia.OrgId = baseHttpRequestContext.OrgId;
            inputPatientMedia.ModifiedBy = baseHttpRequestContext.UserId;
            inputPatientMedia.ModifiedDate = DateTime.UtcNow;
            inputPatientMedia.PatientDetailsId = patientId;
            inputPatientMedia.StatusId = (short)Status.Active;
            inputPatientMedia.Id = id;
            inputPatientMedia.MediaLibrarySubTypeId = mediaDB.MediaLibrarySubTypeId;
            using (var transaction = new TransactionScope(TransactionScopeOption.Required, TransactionScopeAsyncFlowOption.Enabled))
            {
                List<PatientMediaTagsAssoc> patinetMediaTagAssocs = AddUpdateMediaTags(inputPatientMedia.PatientMediaTagsAssocs, mediaDB.PatientMediaTagsAssocs, baseHttpRequestContext, patientId, id);
                inputPatientMedia.PatientMediaTagsAssocs = patinetMediaTagAssocs;
                if (patinetMediaTagAssocs is not null && patinetMediaTagAssocs.Count > 0)
                {
                    //CommunicationNote commNote = AddActiveLogEntry(inputPatientMedia, patientId, baseHttpRequestContext, (short)ActivityLogOps.File_Tags_Update, null);
                    //await _commDAL.AddCommNotesDAL(commNote);
                }
                int rows = await _mediaLibraryDAL.UpdateMediaAssoc(inputPatientMedia);
                bool callFileApi = false;
                if (rows > 0 && !inputPatientMedia.MediaName.Equals(mediaDB.MediaName))
                {   //adding an entry in activity log for filerename
                    //CommunicationNote commNote = AddActiveLogEntry(inputPatientMedia, patientId, baseHttpRequestContext, (short)ActivityLogOps.File_Rename, mediaDB);
                    //await _commDAL.AddCommNotesDAL(commNote);
                    callFileApi = true;
                }
                transaction.Complete();
                transaction.Dispose();

                if (callFileApi)
                {
                    FileDetails fileDetail = await _commDAL.GetFileDetails(baseHttpRequestContext.OrgId, inputPatientMedia.FileDetailsId);
                    fileDetail.ModifiedDate = DateTime.UtcNow;
                    fileDetail.CustomFileName = inputPatientMedia.MediaName;
                    fileDetail.ModifiedBy = baseHttpRequestContext.UserId;
                    string fileAPiUrl = _appSettings.ApiUrls["FileServiceUrl"] + "/file/file_details/" + inputPatientMedia.FileDetailsId;

                    RestClient restClient = new RestClient(fileAPiUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
                    await restClient.PutAsync<ApiResponse<FileDetailsOutputForId>>(fileAPiUrl, fileDetail);

                }
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Result = inputPatientMedia;
                apiResponse.Message = "Success";
                return apiResponse;
            }

        }

        public async Task<ApiResponse<string>> EditPatientMediaTags(BaseHttpRequestContext baseHttpRequestContext, long patientId, PatientMediaTagsUpdate patientMediaTagsUpdate)
        {
            ApiResponse<string> apiResponse = new ApiResponse<string>();
            int orgId = baseHttpRequestContext.OrgId;
            long loggedInUser = baseHttpRequestContext.UserId;
            List<long> lstIds = patientMediaTagsUpdate.FileDetailsIds
                                        .Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                                        .Select(long.Parse)
                                        .ToList();
            List<PatientMediaAssoc> lstPatientMediaAssoc = await _mediaLibraryDAL.FetchPatientMediasByFileDetailsId(lstIds, baseHttpRequestContext.OrgId, patientId);

            if (lstPatientMediaAssoc is not null && lstPatientMediaAssoc.Any())
            {
                List<PatientMediaTagsAssoc> lstAddMediaTags = new();
                List<long> tagsList = patientMediaTagsUpdate.Tags
                                        .Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                                        .Select(long.Parse)
                                        .ToList();

                if (tagsList.Any())
                {
                    foreach (var mediaAssoc in lstPatientMediaAssoc)
                    {
                        foreach (var tagId in tagsList)
                        {
                            lstAddMediaTags.Add(new PatientMediaTagsAssoc
                            {
                                OrgId = orgId,
                                PatientMediaAssocId = mediaAssoc.Id,
                                TagsId = tagId,
                                StatusId = (short)Status.Active,
                                CreatedBy = loggedInUser,
                                CreatedDate = DateTime.UtcNow
                            });
                        }
                    }
                }

                // Assuming UpdatePatientMediaTags method has been adjusted to accept and use mediaCollection
                await _mediaLibraryDAL.UpdatePatientMediaTags(lstPatientMediaAssoc, loggedInUser, lstAddMediaTags);

                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = null;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("Medias cannot be fetched at this time.");
            }

            return apiResponse;
        }

        public async Task<ApiResponse<string>> DeleteMediaColl(BaseHttpRequestContext baseHttpRequestContext, long patientId, DeleteObject deleteObject)
        {
            ApiResponse<string> apiResponse = new ApiResponse<string>();
           
            if (deleteObject is null || string.IsNullOrWhiteSpace(deleteObject.DeleteReason))
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Errors.Add("Delete Reason is mandatory.");
                apiResponse.Message = "Failure";

            }
            if (deleteObject.IsBulkActivity == true)
            {
                RolesPermissionsAssoc permission = await _mediaLibraryDAL.GetPermissionOnRoleId(baseHttpRequestContext.OrgId, baseHttpRequestContext.RoleId, (short)ApiPermission.Bulk_Patient_Media);

                if (permission.IsAllowed == false)
                {
                    apiResponse.StatusCode = StatusCodes.Status403Forbidden;
                    apiResponse.Errors.Add("Not an Authorized User");
                    apiResponse.Result = null;
                    return apiResponse;
                }

            }
            if (!string.IsNullOrWhiteSpace(deleteObject.Ids))
            {
                List<long> lstIds = deleteObject.Ids.Split(',').ToList().Select(s => long.Parse(s)).ToList();
                if (lstIds.Count > 0)
                {
                    apiResponse = await DeleteMedia(baseHttpRequestContext, patientId, lstIds, deleteObject);
                    return apiResponse;

                }
            }
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            apiResponse.Errors.Add("Medias cannot be deleted at this time.");
            apiResponse.Message = "Failure";
            return apiResponse;
        }

        public async Task<ApiResponse<string>> EditPatientMediaColl(InputPatientMediaAssoc inputPatientMediaAssocs, BaseHttpRequestContext baseHttpRequestContext, long patientId)
        {
            ApiResponse<string> apiResponse = new ApiResponse<string>();

            if (inputPatientMediaAssocs.IsBulkActivity == true)
            {
                RolesPermissionsAssoc permission = await _mediaLibraryDAL.GetPermissionOnRoleId(baseHttpRequestContext.OrgId, baseHttpRequestContext.RoleId, (short)ApiPermission.Bulk_Patient_Media);

                if (permission.IsAllowed == false)
                {
                    apiResponse.StatusCode = StatusCodes.Status403Forbidden;
                    apiResponse.Errors.Add("Not an Authorized User");
                    apiResponse.Result = null;
                    return apiResponse;
                }
               
            }

            InputPatientMediaAssoc dbPatientMediaAssocColl = new();
            if (inputPatientMediaAssocs is not null && inputPatientMediaAssocs.PatientMediaAssocs is not null && inputPatientMediaAssocs.PatientMediaAssocs.Count > 0)
            {
                Guid? groupId = null;
                List<CommunicationNote> lstCommNote = new();
                List<ActivityLog> lstActivityLog = new();
                List<ActivityLogChildEntry> lstActivityLogChildEntries = new();
                ActivityLog activityLog = new();
                if (inputPatientMediaAssocs.IsBulkActivity == true)
                    groupId = Guid.NewGuid();
                using (var transaction = new TransactionScope(TransactionScopeOption.Required, TransactionScopeAsyncFlowOption.Enabled))
                {
                    if (inputPatientMediaAssocs is not null && inputPatientMediaAssocs.PatientMediaAssocs.Count > 0)
                    {
                        foreach(PatientMediaAssoc inputPatientMedia in inputPatientMediaAssocs.PatientMediaAssocs)
                        //inputPatientMediaAssocs.PatientMediaAssocs.ToList().ForEach(async inputPatientMedia =>
                        {
                            if (inputPatientMedia is not null)
                            {
                                CommunicationNote commNote = null;

                                PatientMediaAssoc mediaDB = await _mediaLibraryDAL.GetMediaAssocsSync(inputPatientMedia.Id, patientId, baseHttpRequestContext.OrgId);

                                inputPatientMedia.OrgId = baseHttpRequestContext.OrgId;
                                inputPatientMedia.ModifiedBy = baseHttpRequestContext.UserId;
                                inputPatientMedia.ModifiedDate = DateTime.UtcNow;
                                inputPatientMedia.PatientDetailsId = patientId;
                                inputPatientMedia.StatusId = (short)Status.Active;
                                inputPatientMedia.MediaLibrarySubTypeId = mediaDB.MediaLibrarySubTypeId;
                                //if (inputPatientMedia.EpisodesId is null && mediaDB.EpisodesId is not null)
                                //{
                                //    inputPatientMedia.EpisodesId = mediaDB.EpisodesId;
                                //}
                                List<PatientMediaTagsAssoc> patinetMediaTagAssocs = null;
                                if (inputPatientMediaAssocs.IsBulkActivity is not null && inputPatientMediaAssocs.IsBulkActivity == true)
                                {
                                    patinetMediaTagAssocs = AddMediaTags(inputPatientMedia.PatientMediaTagsAssocs, mediaDB.PatientMediaTagsAssocs, baseHttpRequestContext, patientId, inputPatientMedia.Id);

                                }
                                else
                                    patinetMediaTagAssocs = AddUpdateMediaTags(inputPatientMedia.PatientMediaTagsAssocs, mediaDB.PatientMediaTagsAssocs, baseHttpRequestContext, patientId, inputPatientMedia.Id);
                                inputPatientMedia.PatientMediaTagsAssocs = patinetMediaTagAssocs;

                                if (patinetMediaTagAssocs is not null && patinetMediaTagAssocs.Count > 0)
                                {
                                    if (inputPatientMediaAssocs.IsBulkActivity is not null && inputPatientMediaAssocs.IsBulkActivity == true)
                                    {
                                        //commNote = AddActiveLogEntry(inputPatientMedia, patientId, baseHttpRequestContext, (short)ActivityLogOps.File_Tags_Update, null, groupId, 0);
                                        //lstCommNote.Add(commNote);
                                        ActivityLogChildEntry childLogEntry = CreateActivityLogChildEntry((short)ActivityLogOps.File_Tags_Update, null, inputPatientMedia, null, groupId, 0, baseHttpRequestContext);
                                        lstActivityLogChildEntries.Add(childLogEntry);
                                    }
                                    else
                                    {
                                        //commNote = AddActiveLogEntry(inputPatientMedia, patientId, baseHttpRequestContext, (short)ActivityLogOps.File_Tags_Update, null);
                                        //lstCommNote.Add(commNote);
                                        activityLog = CreateActivityLogEntry((int)ActivityLogOps.File_Tags_Update, patientId, inputPatientMedia, null, inputPatientMediaAssocs.EpisodesId, null, 0, baseHttpRequestContext);
                                        lstActivityLog.Add(activityLog);
                                    }
                                }

                                if (inputPatientMediaAssocs.IsBulkActivity is not null && inputPatientMediaAssocs.IsBulkActivity == true && inputPatientMediaAssocs.IsEpisodeUpdate == true)
                                {
                                    //commNote = AddActiveLogEntry(inputPatientMedia, patientId, baseHttpRequestContext, (short)ActivityLogOps.File_Rename, mediaDB, groupId, 0);
                                    //lstCommNote.Add(commNote);
                                    ActivityLogChildEntry childLogEntry = CreateActivityLogChildEntry((short)ActivityLogOps.Episode_Update, null, inputPatientMedia, null, groupId, 0, baseHttpRequestContext);
                                    lstActivityLogChildEntries.Add(childLogEntry);
                                }
                                else
                                {
                                    if (inputPatientMedia.EpisodesId != mediaDB.EpisodesId)
                                    {
                                        activityLog = CreateActivityLogEntry((int)ActivityLogOps.Episode_Update, patientId, inputPatientMedia, null, inputPatientMediaAssocs.EpisodesId, null, 0, baseHttpRequestContext);
                                        lstActivityLog.Add(activityLog);
                                    }
                                }
                                bool callFileApi = false;
                                if (!inputPatientMedia.MediaName.Equals(mediaDB.MediaName))
                                {   //adding an entry in activity log for filerename
                                    if (inputPatientMediaAssocs.IsBulkActivity is not null && inputPatientMediaAssocs.IsBulkActivity == true)
                                    {
                                        //commNote = AddActiveLogEntry(inputPatientMedia, patientId, baseHttpRequestContext, (short)ActivityLogOps.File_Rename, mediaDB, groupId, 0);
                                        //lstCommNote.Add(commNote);
                                        ActivityLogChildEntry childLogEntry = CreateActivityLogChildEntry((short)ActivityLogOps.File_Tags_Update, null, inputPatientMedia, null, groupId, 0, baseHttpRequestContext);
                                        lstActivityLogChildEntries.Add(childLogEntry);
                                    }
                                    else
                                    {
                                        //commNote = AddActiveLogEntry(inputPatientMedia, patientId, baseHttpRequestContext, (short)ActivityLogOps.File_Rename, mediaDB);
                                        //lstCommNote.Add(commNote);
                                        activityLog = CreateActivityLogEntry((int)ActivityLogOps.File_Rename, patientId, inputPatientMedia, mediaDB, inputPatientMediaAssocs.EpisodesId, null, 0, baseHttpRequestContext);
                                        lstActivityLog.Add(activityLog);
                                    }

                                    callFileApi = true;
                                }

                                if (callFileApi)
                                {
                                    FileDetails fileDetail = await _commDAL.GetFileDetails(baseHttpRequestContext.OrgId, inputPatientMedia.FileDetailsId);
                                    fileDetail.ModifiedDate = DateTime.UtcNow;
                                    fileDetail.CustomFileName = inputPatientMedia.MediaName;
                                    fileDetail.ModifiedBy = baseHttpRequestContext.UserId;
                                    string fileAPiUrl = _appSettings.ApiUrls["FileServiceUrl"] + "/file/file_details/" + inputPatientMedia.FileDetailsId;
                                    RestClient restClient = new RestClient(fileAPiUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
                                    var fileApiResponse = await restClient.PutAsync<ApiResponse<FileDetailsOutputForId>>(fileAPiUrl, fileDetail);

                                }
                            }
                        };
                    }
                    if (inputPatientMediaAssocs.IsBulkActivity is not null && inputPatientMediaAssocs.IsBulkActivity == true && lstActivityLogChildEntries.Count > 0)
                    {
                        //CommunicationNote commNote = AddActiveLogEntry(null, patientId, baseHttpRequestContext, (short)ActivityLogOps.Bulk_Tags_Update, null, groupId, inputPatientMediaAssocs.PatientMediaAssocs.Count);
                        //lstCommNote.Add(commNote);
                        activityLog = CreateActivityLogEntry((short)ActivityLogOps.Bulk_Tags_Update, patientId, null, null, inputPatientMediaAssocs.EpisodesId, groupId, inputPatientMediaAssocs.PatientMediaAssocs.Count, baseHttpRequestContext);

                        activityLog.ActivityLogChildEntries = lstActivityLogChildEntries;
                        lstActivityLog.Add(activityLog);
                    }
                    //await _commDAL.AddCommNotesRange(lstCommNote);
                    await _mediaLibraryDAL.UpdateRangeMediaAssoc(inputPatientMediaAssocs);
                    if (lstActivityLog.Count > 0)
                    {
                        await _activityDAL.AddActivtyLogRange(lstActivityLog);
                    }
                    transaction.Complete();
                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    apiResponse.Result = "Success";
                    apiResponse.Message = "Success";
                    return apiResponse;
                }
            }
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            apiResponse.Errors.Add("Medias cannot be updated at this time.");
            apiResponse.Message = "Failure";
            return apiResponse;
        }

        private List<PatientMediaTagsAssoc> AddMediaTags(ICollection<PatientMediaTagsAssoc> mediaTagsCollection, ICollection<PatientMediaTagsAssoc> dbMediaTagsCollection, BaseHttpRequestContext baseHttpRequestContext, long patientId, long mediaId)
        {
            ApiResponse<string> apiResponse = new ApiResponse<string>();
            int orgId = baseHttpRequestContext.OrgId;
            long loggedInUser = baseHttpRequestContext.UserId;
            List<PatientMediaTagsAssoc> lstAddMediaTags = new List<PatientMediaTagsAssoc>();
            List<PatientMediaTagsAssoc> lstInputMediaTags = new();
            if (mediaTagsCollection is not null && mediaTagsCollection is not null && mediaTagsCollection.Any())
            {
                lstInputMediaTags = mediaTagsCollection.ToList();
            }
            lstInputMediaTags.ForEach(mediaTagAssoc =>
            {
                if (mediaTagAssoc.Id > 0)
                {
                    var existingobj = dbMediaTagsCollection.FirstOrDefault(x => x.Id == mediaTagAssoc.Id);
                    dbMediaTagsCollection.Remove(existingobj);
                }
                else
                {
                    PatientMediaTagsAssoc mediaTagAssocDB = dbMediaTagsCollection.Where(x => x.TagsId == mediaTagAssoc.TagsId).FirstOrDefault();
                    if (mediaTagAssocDB is null)
                    {
                        PatientMediaTagsAssoc newMediaTagAssoc = new();
                        newMediaTagAssoc.OrgId = orgId;
                        newMediaTagAssoc.CreatedBy = loggedInUser;
                        newMediaTagAssoc.StatusId = (short)Status.Active;
                        newMediaTagAssoc.PatientMediaAssocId = mediaId;
                        newMediaTagAssoc.CreatedDate = DateTime.UtcNow;
                        newMediaTagAssoc.TagsId = mediaTagAssoc.TagsId;
                        lstAddMediaTags.Add(newMediaTagAssoc);
                    }
                    else
                    {
                        if (mediaTagAssocDB.StatusId == (short)Status.Deleted)
                        {
                            mediaTagAssocDB.StatusId = (short)Status.Active;
                            mediaTagAssocDB.ModifiedDate = DateTime.UtcNow;
                            mediaTagAssocDB.ModifiedBy = loggedInUser;
                            lstAddMediaTags.Add(mediaTagAssocDB);

                        }
                        dbMediaTagsCollection.Remove(mediaTagAssocDB);

                    }
                }
            });
            return lstAddMediaTags;
        }

        /// <summary>
        /// Method to fetch multiple medias
        /// </summary>
        /// <param name="patient_id"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task<ApiResponse<List<ListPatientMediaAssoc>>> GetMultiplePatientMedias(long patient_id, BaseHttpRequestContext baseHttpRequestContext, string Ids)
        {
            ApiResponse<List<ListPatientMediaAssoc>> apiResponse = new();
            List<long> lstIds = Ids.Split(',').ToList().Select(s => long.Parse(s)).ToList();
            if (lstIds is not null && lstIds.Any())
            {
                List<ListPatientMediaAssoc> listPatientMediaAssocsOutput = new();
                List<PatientMediaAssoc> lstPatientMediaAssoc = await _mediaLibraryDAL.FetchPatientMedias(lstIds, baseHttpRequestContext.OrgId, patient_id);
                if (lstPatientMediaAssoc is not null && lstPatientMediaAssoc.Any())
                {
                    lstPatientMediaAssoc.ForEach(patientmedia =>
                    {
                        ListPatientMediaAssoc patientMediaAssocList = _mapper.Map<PatientMediaAssoc, ListPatientMediaAssoc>(patientmedia);
                        listPatientMediaAssocsOutput.Add(patientMediaAssocList);
                    });
                    listPatientMediaAssocsOutput = await FetchFileDetailsForMediaAsync(listPatientMediaAssocsOutput, baseHttpRequestContext);
                }

                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Result = listPatientMediaAssocsOutput;
                apiResponse.Message = "Success";
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = null;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("Medias cannot be fetched at this time.");

            }
            return apiResponse;
        }

        /// <summary>
        /// GetMedia By passing FileDetailsId
        /// </summary>
        /// <param name="id"></param>
        /// <param name="patientId"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<InputPatientMedia>> GetPatientMediaByFileDetailsId(long id, long patientId, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<InputPatientMedia> apiResponse = new();
            var media = await _mediaLibraryDAL.GetPatientMediaByFiledetailsId(id, patientId, baseHttpRequestContext.OrgId);
            if (media is null)
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = media;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("The media doesnot exist.");
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Result = media;
                apiResponse.Message = "Success";
            }

            return apiResponse;
        }

        /// <summary>
        /// Method to check Permission for given API
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="permissionsIds"></param>
        /// <returns></returns>
        public async Task<bool> CheckPermission(BaseHttpRequestContext baseHttpRequestContext, List<short> permissionsIds)
        {
            List<RolesPermissionsAssoc> permissions = await _mediaLibraryDAL.GetPermissionsOnRoleId(baseHttpRequestContext.OrgId, baseHttpRequestContext.RoleId, permissionsIds);
            bool isAnyAllowed = permissions.Any(x => x.IsAllowed == true);
            return isAnyAllowed;

        }

        /// <summary>
        /// Method to check Permission along with bulk permission for given API
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="permissionsIds"></param>
        /// <returns></returns>
        public async Task<bool> CheckPermissionWithBulk(BaseHttpRequestContext baseHttpRequestContext, List<short> permissionsIds)
        {
            
            List<RolesPermissionsAssoc> permissions = await _mediaLibraryDAL.GetPermissionsOnRoleId(baseHttpRequestContext.OrgId, baseHttpRequestContext.RoleId, permissionsIds);
            RolesPermissionsAssoc bulkPerm = permissions.FirstOrDefault(p => p.PermissionsId == (short)ApiPermission.Bulk_Patient_Media);
            if(bulkPerm != null)
            {
                if(bulkPerm.IsAllowed == false)
                {
                    return false;
                }
                else
                {
                    permissions = permissions.Where(p => p.PermissionsId != (short)ApiPermission.Bulk_Patient_Media).ToList();

                }
            }

            bool isAnyAllowed = permissions.Any(x => x.IsAllowed == true);
            return isAnyAllowed;

        }

        public async Task<ApiResponse<Email>> SendBulkMediaEmail(long patient_id, Email email, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<Email> apiResponse = new();

            if (email is not null && !string.IsNullOrWhiteSpace(email.AttachmentsFileIds))
            {

                email.PatientDetailsId = patient_id;
                email.OrgId = baseHttpRequestContext.OrgId;
                //email.PatientEmailTypeId = (short)PatientEmailType.Bulk_Media;
                email.CreatedBy = baseHttpRequestContext.UserId;
                email.CreatedDate = DateTime.UtcNow;
                email.EmailStatusId = (short)EmailStatus.InQueue;
                email.StatusId = (short)Status.Active;
                int rows = await _letterDAL.AddPatientEmail(email);
                if (rows > 0)
                {
                    apiResponse.Result = email;
                    apiResponse.StatusCode = StatusCodes.Status200OK;

                    await StorePatientEmailMessage(baseHttpRequestContext.OrgCode, email.Id);

                    return apiResponse;
                }
            }
            apiResponse.Result = null;
            apiResponse.Message = "Failure";
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            apiResponse.Errors.Add("Email cannot be sent at this time.");
            return apiResponse;
        }


        private async Task StorePatientEmailMessage(string orgCode, long emailId, long? activityLogChildId = null)
        {
            EmailRequestDataModel patientRequestDataModel = new EmailRequestDataModel
            {
                OrgCode = orgCode,
                PropertyType = (int)EODRequestDataType.Patient,
                PropertyId = emailId,
                ActicityLogChildId = activityLogChildId


            };
            Dictionary<string, string> userPros = new Dictionary<string, string>
                        {
                            { "RequestType","Patient"},
                            { "to",_configuration["AzureAD:ASBSubNamePatientEmail"]}
                        };
            byte[] jsonContentByteArr = Encoding.GetEncoding("iso-8859-1").GetBytes(JsonConvert.SerializeObject(patientRequestDataModel));
            await _asbMessageSenderHelper.MessageSenderTopic(jsonContentByteArr, userPros, _configuration["AzureAD:AzSBConnStringPatient"], _configuration["AzureAD:ASBTopicPatient"]);
        }

    }
}
