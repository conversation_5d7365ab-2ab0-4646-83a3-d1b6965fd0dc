﻿using Capstone2.RestServices.Patient.Context;
using Capstone2.RestServices.Patient.Interfaces;
using Capstone2.RestServices.Patient.Models;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Patient.Services
{
    public class HealthDetailsDAL : IHealthDetailsDAL
    {
        public readonly ReadOnlyPatientDBContext _readOnlyDbContext;
        public readonly UpdatablePatientDBContext _updatableDBContext;

        public HealthDetailsDAL(ReadOnlyPatientDBContext readOnlyDbContext, UpdatablePatientDBContext updatableDBContext)
        {
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
        }

        /// <summary>
        /// Method to add HealthDetailsData to HealthDetailsData table
        /// </summary>
        /// <param name="healthDetails"></param>
        /// <returns></returns>
        public async Task<long> AddHealthDetails(HealthDetails healthDetails)
        {
            _updatableDBContext.HealthDetails.Add(healthDetails);
            return await _updatableDBContext.SaveChangesAsync();
        }

        public async Task<HealthDetails> FetchHealthDetails(int orgId, long? patientId)
        {
            var accountHolder = await (from HD in _readOnlyDbContext.HealthDetails
                                       where HD.PatientDetailsId == patientId && HD.OrgId == orgId
                                       && HD.StatusId == (short)Status.Active
                                       select new HealthDetails
                                       {
                                           Id = HD.Id,
                                           OrgId = HD.OrgId,
                                           PatientDetailsId = HD.PatientDetailsId,
                                           Weight = HD.Weight,
                                           Height = HD.Height,
                                           Bmi = HD.Bmi,
                                           IsSmoker = HD.IsSmoker,
                                           NumOfCigarates = HD.NumOfCigarates,
                                           IsAlcohol = HD.IsAlcohol,
                                           NumOfDrinks = HD.NumOfDrinks,
                                           IsPregnant = HD.IsPregnant,
                                           PregnantDetails = HD.PregnantDetails,
                                           IsDiabetes = HD.IsDiabetes,
                                           DiabetesType = HD.DiabetesType,
                                           IsBleeding = HD.IsBleeding,
                                           BleedingDetails = HD.BleedingDetails,
                                           IsBreathingIssues = HD.IsBreathingIssues,
                                           BreathingIssuesDetails = HD.BreathingIssuesDetails,
                                           IsHeartProblems = HD.IsHeartProblems,
                                           HeartProblemsDetails = HD.HeartProblemsDetails,
                                           IsCovidImmunised = HD.IsCovidImmunised,
                                           CovidImmunisedDate = HD.CovidImmunisedDate,
                                           Comments = HD.Comments,

                                           StatusId = HD.StatusId,
                                           CreatedDate = HD.CreatedDate,
                                           CreatedBy = HD.CreatedBy,
                                           ModifiedBy = HD.ModifiedBy,
                                           ModifiedDate = HD.ModifiedDate
                                       }).SingleOrDefaultAsync();
            return accountHolder;
        }

        /// <summary>
        /// Method to fetch the HealthDetails details for a patient from db
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<HealthDetails> GetHealthDetails(int orgId, long patientId)
        {
            return await _readOnlyDbContext.HealthDetails.FirstOrDefaultAsync(x => x.PatientDetailsId == patientId && x.OrgId == orgId && x.StatusId == (short)Status.Active);
        }

        /// <summary>
        /// Update HealthDetails
        /// </summary>
        /// <param name="healthDetails"></param>
        /// <returns></returns>
        public async Task<int> UpdateHealthDetails(HealthDetails healthDetails)
        {
            _updatableDBContext.HealthDetails.Update(healthDetails);
            return await _updatableDBContext.SaveChangesAsync();
        }
    }
}
