﻿using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.Patient.Interfaces;
using Capstone2.RestServices.Patient.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Transactions;

namespace Capstone2.RestServices.Patient.Services
{
    public class SmsBAL : ISmsBAL
    {
        public readonly ISmsDAL _smsDAL;

        public SmsBAL(ISmsDAL smsDAL)
        {
            _smsDAL = smsDAL;
        }

        public async Task<ApiResponse<QueryResultList<SmsHistoryView>>> ListSmsHistoryBAL( BaseHttpRequestContext baseHttpRequestContext, QueryModel queryModel)
        {
            ApiResponse<QueryResultList<SmsHistoryView>> apiResponse = new();

            SmsFilter filterModel = PrepareFilterParameters(queryModel.Filter);
            if (filterModel is null)
            {
                apiResponse.Errors.Add("Filter is needed to get the list of Sms History");
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failure";
                return apiResponse;
            }

            QueryResultList<SmsHistoryView> queryList = await _smsDAL.ListSmsHistoryDAL(baseHttpRequestContext.OrgId, queryModel, filterModel);

            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result = queryList;



            return apiResponse;
        }

        /// <summary>
        /// method to generate the filter parameters from the request
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        private SmsFilter PrepareFilterParameters(string filter)
        {
            if (!(String.IsNullOrEmpty(filter)))
            {
                return JsonConvert.DeserializeObject<SmsFilter>(filter);
            }
            else
            {
                return null;
            }
        }

        public async Task<long> AddToSmsHistory(long? appointmentId, long patientId, long smsId, short smsSentTypeId, BaseHttpRequestContext baseHttpRequestContext)
        {
            SmsHistory smsHistory = new();
            smsHistory.AppointmentDetailsId = appointmentId;
            smsHistory.PatientDetailsId = patientId;
            smsHistory.SmsRequestsId = smsId;
            smsHistory.OrgId = baseHttpRequestContext.OrgId;
            smsHistory.SmsSentTypeId = smsSentTypeId;
            if(smsSentTypeId == (short)SmsSentType.User)
            {
                smsHistory.SentById = baseHttpRequestContext.UserId;
            }
            else if(smsSentTypeId == (short)SmsSentType.Patient)
            {

                smsHistory.SentById = patientId;
            }

            long smsAdded = await _smsDAL.AddSmsHistory(smsHistory);
            return smsAdded;

        }
    }
}
