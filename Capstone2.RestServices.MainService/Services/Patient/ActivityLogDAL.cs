﻿using Capstone2.RestServices.Patient.Context;
using Capstone2.RestServices.Patient.Interfaces;
using Capstone2.RestServices.Patient.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Patient.Services
{
    public class ActivityLogDAL :IActivityLogDAL
    {
        public readonly ReadOnlyPatientDBContext _readOnlyDbContext;
        public readonly UpdatablePatientDBContext _updatableDBContext;

        public ActivityLogDAL(ReadOnlyPatientDBContext readOnlyDbContext, UpdatablePatientDBContext updatableDBContext)
        {
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
        }
        /// <summary>
        /// Adding new ActivityLog enry to database
        /// </summary>
        /// <param name="activityLog"></param>
        /// <returns></returns>
        public async Task<long> AddActivityLog(ActivityLog activityLog)
        {
            await _updatableDBContext.ActivityLogs.AddAsync(activityLog);
            int rows = await _updatableDBContext.SaveChangesAsync();
            return rows > 0 ? activityLog.Id : 0;
        }
        /// <summary>
        /// Method to get Activitylog obj from db
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ActivityLog> GetActivityLog(int orgId, long id)
        {
            return await _readOnlyDbContext.ActivityLogs.AsNoTracking().FirstOrDefaultAsync(p => p.Id == id && p.OrgId == orgId);
        }

        /// <summary>
        /// Method to get Activitylog obj from db
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ActivityLogChildEntry> GetActivityLogChild(int orgId, long id)
        {
            return await _readOnlyDbContext.ActivityLogChildEntries.AsNoTracking().FirstOrDefaultAsync(p => p.ActivityLogId == id && p.OrgId == orgId && p.ActivityStatusId == (short)Status.Active);
        }

        public async Task<ActivityLog> GetActivityLogWithSubTypeId(long patientId, int orgId, long? entityId, short activityLogTypeId, short? activityLogSubTypeId)
        {
            return await _readOnlyDbContext.ActivityLogs.Where(log => log.PatientDetailsId == patientId && log.OrgId == orgId && log.EntityId == entityId && log.ActivityLogTypeId == activityLogTypeId && log.ActivityLogSubTypeId == activityLogSubTypeId).FirstOrDefaultAsync();
        }
        /// <summary>
        /// Method to get Activity logs based on patientid
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="queryModel"></param>
        /// <param name="filterModel"></param>
        /// <returns></returns>
        public async Task<QueryResultList<ActivityLogList>> ListActivityLogs(int orgId,long patient_id, QueryModel queryModel, ActivityLogFilter filterModel)
        {
            var query = from A in _readOnlyDbContext.ActivityLogs
                        where A.OrgId == orgId && A.PatientDetailsId == patient_id
                        join UD in _readOnlyDbContext.UserDetails on A.ModifiedBy equals UD.Id into JUD
                        from UD in JUD.DefaultIfEmpty()
                        select new ActivityLogList
                        {
                            Id = A.Id,
                            OrgId = A.OrgId,
                            EpisodesId = A.EpisodesId,
                            ActivityLogTypeId = A.ActivityLogTypeId,
                            ActivityLogSubTypeId = A.ActivityLogSubTypeId,
                            ActivityStatusId = A.ActivityStatusId,
                            PatientDetailsId=A.PatientDetailsId,
                            ModifiedBy = A.ModifiedBy,
                            ModifiedDate = A.ModifiedDate,
                            Modifier = new UserDetails
                            {
                                Id = UD.Id,
                                OrgId = UD.OrgId,
                                FirstName = UD.FirstName,
                                SurName = UD.SurName,
                                TitleId = UD.TitleId
                            },
                            CreatedDate = A.CreatedDate,
                            EntityId = A.EntityId,
                            Description = A.Description,
                            FileDetailsId = A.FileDetailsId,
                            Title = A.Title,
                        };

            if (filterModel is not null)
            {
                if (filterModel.ActivityLogTypeId is not null && filterModel.ActivityLogTypeId.Any())
                {
                    query = query.Where(x => filterModel.ActivityLogTypeId.Contains(x.ActivityLogTypeId));

                }

                if (filterModel.EpisodesId is not null && filterModel.EpisodesId.Any())
                {
                    query = query.Where(x => filterModel.EpisodesId.Contains(x.EpisodesId));

                }
                if (filterModel.EntityId is not null && filterModel.EntityId.Any())
                {
                    query = query.Where(x => filterModel.EntityId.Contains(x.EntityId));

                }
            }
            if (!string.IsNullOrWhiteSpace(queryModel.SortOrder) && !string.IsNullOrWhiteSpace(queryModel.SortTerm))
            {
                query = SortActivityLogs(query, queryModel.SortOrder, queryModel.SortTerm);

            }
            List<ActivityLogList> paginatedList = await CreatePaginateList(query, queryModel);
            QueryResultList<ActivityLogList> queryList = new QueryResultList<ActivityLogList>();
            if (paginatedList != null)
            {

                queryList.ItemRecords = paginatedList;
                queryList.PageNumber = queryModel.PageNumber;
                queryList.PageSize = queryModel.PageSize;
                queryList.CurrentCount = paginatedList.Count();

            }
            else
            {
                queryList.ItemRecords = null;
                queryList.CurrentCount = 0;

            }
            queryList.TotalCount = query.Count();
            queryList.PageNumber = queryModel.PageNumber;
            queryList.PageSize = queryModel.PageSize;
            return queryList;
        }
        private IQueryable<ActivityLogList> SortActivityLogs(IQueryable<ActivityLogList> query, string sortOrder, string sortTerm)
        {
            switch (sortTerm.ToLower())
            {


                case "ModifiedDate":

                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            query = query.OrderBy(x => x.ModifiedDate);
                        }
                        else
                        {

                            query = query.OrderByDescending(x => x.ModifiedDate);

                        }
                        break;
                    }
              
                default:
                    {
                        query = query.OrderByDescending(x => x.ModifiedDate);
                        break;
                    }

            }
            return query;

        }

        private async Task<List<ActivityLogList>> CreatePaginateList(IQueryable<ActivityLogList> activityLogQuery, QueryModel queryModel)
        {
            if (queryModel.PageNumber > 0 && queryModel.PageSize > 0)
            {
                if (activityLogQuery.Any())
                {

                    List<ActivityLogList> paginatedList = await activityLogQuery.Skip((queryModel.PageNumber - 1) * queryModel.PageSize)
                 .Take(queryModel.PageSize).ToListAsync();

                    return paginatedList;

                }

            }
            return null;
        }


        /// <summary>
        /// Method to update Activitylog obj in db
        /// </summary>
        /// <param name="activityLog"></param>
        /// <returns></returns>
        public async Task<int> UpdateActivityLog(Models.ActivityLog activityLog)
        {
            _updatableDBContext.ActivityLogs.Update(activityLog);
            return await _updatableDBContext.SaveChangesAsync();
        }

        public async Task<ActivityLog> FetchActivtyLogFromEntityId(long patientId, int orgId, long? entityId, short activityLogTypeId)
        {
            return await _readOnlyDbContext.ActivityLogs.Where(log => log.PatientDetailsId == patientId && log.OrgId == orgId && log.EntityId == entityId && log.ActivityLogTypeId == activityLogTypeId).FirstOrDefaultAsync();
        }
        /// <summary>
        /// Method to fetch appoints based on appointmentIds
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="list"></param>
        /// <returns></returns>
        public async Task<List<AppointmentDetailInfo>> FetchAppointmentsFromIds(int orgId, List<long?> lstAppointmentIds)
        {

            var query = from A in _readOnlyDbContext.AppointmentDetails
                        join AP in _readOnlyDbContext.AppointmentTypes on A.AppointmentTypesId equals AP.Id into JAT
                        from AP in JAT.DefaultIfEmpty()
                        join UD in _readOnlyDbContext.UserDetails on A.UserDetailsId equals UD.Id into JUD
                        from UD in JUD.DefaultIfEmpty()
                        where A.OrgId == orgId && lstAppointmentIds.Contains(A.Id)
                        select new AppointmentDetailInfo
                        {

                            Id = A.Id,
                            UserDetailsId = A.UserDetailsId,
                            OrgId = A.OrgId,
                            StartTime = A.StartTime,
                            EndTime = A.EndTime,
                            PatientDetailsId = A.PatientDetailsId,
                            DateOfAppointment = A.DateOfAppointment,
                            AppointmentCategoryId = A.AppointmentCategoryId,
                            AppointmentStatusId = A.AppointmentStatusId,
                            AppointmentTypesId = A.AppointmentTypesId,
                            Type = (AP == null) ? null : AP.Type,
                            AppointmentCateogry = EnumExtensions.GetDescription((AppointmentType)A.AppointmentCategoryId),
                            UserDetails = new UserDetails
                            {
                                Id = UD.Id,
                                OrgId = UD.OrgId,
                                FirstName = UD.FirstName,
                                SurName = UD.SurName,
                                TitleId = UD.TitleId,
                                Title = (UD != null && UD.TitleId == null && UD.TitleId > 0) ? null : EnumExtensions.GetDescription((TitleType)UD.TitleId),
                            },
                        };
            return await query.ToListAsync();
        }


       public async Task<QueryResultList<ActivityLogChildList>> ListActivityLogChildDAL(int orgId, QueryModel queryModel, long activitylog_id)
        {
            var query = from A in _readOnlyDbContext.ActivityLogChildEntries
                        where A.OrgId == orgId && A.ActivityLogId == activitylog_id
                        join UD in _readOnlyDbContext.UserDetails on A.ModifiedBy equals UD.Id into JUD
                        from UD in JUD.DefaultIfEmpty()
                        select new ActivityLogChildList
                        {
                            Id = A.Id,
                            OrgId = A.OrgId,
                            ActivityLogId = A.ActivityLogId,
                            SubTypeId = A.SubTypeId,
                            EntityId = A.EntityId,
                             VariableJson = A.VariableJson,
                             ActivityStatusId = A.ActivityStatusId,
                             EmailStatusId = A.EmailStatusId,
                             EmailStatus = (A.EmailStatusId != null ) ? EnumExtensions.GetDescription((EmailStatus)A.EmailStatusId) : null,
                            ActivityStatus = (A.SubTypeId != null && A.SubTypeId == (short)MediaLibrarySubType.Letter && A.ActivityStatusId!=null && A.ActivityStatusId >0) ? EnumExtensions.GetDescription((LetterStatus)A.ActivityStatusId):null,
                             ModifiedBy = A.ModifiedBy,
                             ModifiedDate = A.ModifiedDate,
                             Modifier = new UserDetails
                             {
                                 Id = UD.Id,
                                 OrgId = UD.OrgId,
                                 FirstName = UD.FirstName,
                                 SurName = UD.SurName,
                                 TitleId = UD.TitleId
                             },
                             CreatedDate = A.CreatedDate,
                             Description = A.Description,
                             FileDetailsId = A.FileDetailsId,
                             Title = A.Title,
                         };

             /* if (filterModel is not null)
             {
                 if (filterModel.ActivityLogTypeId.Any())
                 {
                     query = query.Where(x => filterModel.ActivityLogTypeId.Contains(x.ActivityLogTypeId));

                 }
             }*/
             if (!string.IsNullOrWhiteSpace(queryModel.SortOrder) && !string.IsNullOrWhiteSpace(queryModel.SortTerm))
             {
                 query = SortActivityLogChild(query, queryModel.SortOrder, queryModel.SortTerm);

             }
             List<ActivityLogChildList> paginatedList = await CreatePaginateChildLogList(query, queryModel);
             QueryResultList<ActivityLogChildList> queryList = new QueryResultList<ActivityLogChildList>();
             if (paginatedList != null)
             {

                 queryList.ItemRecords = paginatedList;
                 queryList.PageNumber = queryModel.PageNumber;
                 queryList.PageSize = queryModel.PageSize;
                 queryList.CurrentCount = paginatedList.Count();

             }
             else
             {
                 queryList.ItemRecords = null;
                 queryList.CurrentCount = 0;

             }
             queryList.TotalCount = query.Count();
             queryList.PageNumber = queryModel.PageNumber;
             queryList.PageSize = queryModel.PageSize;
             return queryList;
         }


         private IQueryable<ActivityLogChildList> SortActivityLogChild(IQueryable<ActivityLogChildList> query, string sortOrder, string sortTerm)
         {
             switch (sortTerm.ToLower())
             {


                 case "ModifiedDate":

                     {
                         if ("asc".Equals(sortOrder.ToLower()))
                         {
                             query = query.OrderBy(x => x.ModifiedDate);
                         }
                         else
                         {

                             query = query.OrderByDescending(x => x.ModifiedDate);

                         }
                         break;
                     }

                 default:
                     {
                         query = query.OrderByDescending(x => x.ModifiedDate);
                         break;
                     }

             }
             return query;

         }

        private async Task<List<ActivityLogChildList>> CreatePaginateChildLogList(IQueryable<ActivityLogChildList> activityLogQuery, QueryModel queryModel)
        {
            if (queryModel.PageNumber > 0 && queryModel.PageSize > 0)
            {
                if (activityLogQuery.Any())
                {

                    List<ActivityLogChildList> paginatedList = await activityLogQuery.Skip((queryModel.PageNumber - 1) * queryModel.PageSize)
                 .Take(queryModel.PageSize).ToListAsync();

                    return paginatedList;

                }

            }
            return null;
        }
        /// <summary>
        /// Method to insert a list of Activity log child entries to database
        /// </summary>
        /// <param name="lstActivtyLogChild"></param>
        /// <returns></returns>
        public async Task AddActivtyLogChildEntryRange(List<ActivityLogChildEntry> lstActivtyLogChild)
        {
            _updatableDBContext.ActivityLogChildEntries.AddRange(lstActivtyLogChild);
            await _updatableDBContext.SaveChangesAsync();
        }

        public async Task AddActivtyLogRange(List<ActivityLog> lstActivtyLog)
        {
            _updatableDBContext.ActivityLogs.AddRange(lstActivtyLog);
            await _updatableDBContext.SaveChangesAsync();
        }
        public async Task<int> AddActivtyLogChildEntry(ActivityLogChildEntry activityLogChildEntry)
        {
            await _updatableDBContext.ActivityLogChildEntries.AddAsync(activityLogChildEntry);
            return await _updatableDBContext.SaveChangesAsync();
        }

        /// <summary>
        /// Method to fetch a permission
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="rid"></param>
        public async Task<RolesPermissionsAssoc> GetPermissionOnRoleId(int orgId, int rid, short pid)
        {
            var permissionobject = _readOnlyDbContext.RolesPermissionsAssocs.AsNoTracking().FirstOrDefault(x => x.RolesId == rid && x.StatusId == (short)Status.Active && x.OrgId == orgId && x.PermissionsId == pid);
            return permissionobject;
        }

        /// <summary>
        /// Method to fetch a Activity log for given letter
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="rid"></param>


        public async Task<ActivityLog> GetActivityLogForPatientActions(long LetterId, int orgId)
        {
            return await _readOnlyDbContext.ActivityLogs.Include(e => e.ActivityLogChildEntries).Where(log => log.OrgId == orgId && log.EntityId == LetterId && log.ActivityLogTypeId == (short)ActivityLogType.Media_Library && log.ActivityLogSubTypeId == (short)MediaLibrarySubType.Patient_Letter).FirstOrDefaultAsync();
        }

        /// Method to get Activity logs based on patientid and activityLogId
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="patient_id"></param>
        /// <param name="activityLogId"></param>
        /// <returns></returns>
        public async Task<ActivityLogList> GetActivityLog(int orgId, long patient_id,long activityLog_id)
        {
            var activityLog = await  (from A in _readOnlyDbContext.ActivityLogs
                             where A.OrgId == orgId && A.PatientDetailsId == patient_id && A.Id == activityLog_id
                             join UD in _readOnlyDbContext.UserDetails on A.ModifiedBy equals UD.Id into JUD
                             from UD in JUD.DefaultIfEmpty()
                             select new ActivityLogList
                             {
                                 Id = A.Id,
                                 OrgId = A.OrgId,
                                 EpisodesId = A.EpisodesId,
                                 ActivityLogTypeId = A.ActivityLogTypeId,
                                 ActivityLogSubTypeId = A.ActivityLogSubTypeId,
                                 ActivityStatusId = A.ActivityStatusId,
                                 PatientDetailsId = A.PatientDetailsId,
                                 ModifiedBy = A.ModifiedBy,
                                 ModifiedDate = A.ModifiedDate,
                                 Modifier = new UserDetails
                                 {
                                     Id = UD.Id,
                                     OrgId = UD.OrgId,
                                     FirstName = UD.FirstName,
                                     SurName = UD.SurName,
                                     TitleId = UD.TitleId
                                 },
                                 CreatedDate = A.CreatedDate,
                                 EntityId = A.EntityId,
                                 Description = A.Description,
                                 FileDetailsId = A.FileDetailsId,
                                 Title = A.Title,
                             }).FirstOrDefaultAsync();

            return activityLog;
        }

        /// <summary>
        /// Method to update Child Activitylog obj in db
        /// </summary>
        /// <param name="activityLog"></param>
        /// <returns></returns>
        public async Task<int> UpdateActivityLogChild(Models.ActivityLogChildEntry childActivityLog)
        {
            _updatableDBContext.ActivityLogChildEntries.Update(childActivityLog);
            return await _updatableDBContext.SaveChangesAsync();
        }

        /// <summary>
        /// Method to update Child Activitylog obj in db
        /// </summary>
        /// <param name="activityLog"></param>
        /// <returns></returns>
        public async Task<int> UpdateActivityLogChildByFileDetailsId(Models.ActivityLogChildEntry updatedChildActivityLog, int statusId, int filedetailsid)
        {
            var childActivityLogDb = await _readOnlyDbContext.ActivityLogChildEntries
            .SingleOrDefaultAsync(log => log.FileDetailsId == filedetailsid);

            //if (childActivityLogDb != null)
            //{
            //    childActivityLogDb.ActivityStatusId = (short)statusId;
            //    _updatableDBContext.ActivityLogChildEntries.Update(childActivityLogDb);
            //    return await _updatableDBContext.SaveChangesAsync();
            //}

            if (childActivityLogDb != null)
            {
                _updatableDBContext.ActivityLogChildEntries.Remove(childActivityLogDb);
                return await _updatableDBContext.SaveChangesAsync();
            }

            return 0;
        }
    }
}
