﻿using Capstone2.RestServices.Patient.Context;
using Capstone2.RestServices.Patient.Interfaces;
using Capstone2.RestServices.Patient.Models;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Patient.Services
{
    public class ReferralDAL : IReferralDAL
    {
        public readonly ReadOnlyPatientDBContext _readOnlyDbContext;
        public readonly UpdatablePatientDBContext _updatableDBContext;
        public ReferralDAL(ReadOnlyPatientDBContext readOnlyDbContext, UpdatablePatientDBContext updatableDBContext)
        {
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
        }

        public async Task<List<InputReferralDetail>> FetchReferrals(int orgId, long? patientId,short IsDefault=2,List<long> referralDetailsId =null)
        {
            List<SqlParameter> paramsList = new List<SqlParameter>();
            paramsList.Add(new SqlParameter("@OrgId", orgId));
            paramsList.Add(new SqlParameter("@patientDetailsId", patientId));
            paramsList.Add(new SqlParameter("@referralStatus", (short)Status.Active));
            //0,1 corresponding to values in the isdefault column any other value the isdefault column will be removed from the filter
            paramsList.Add(new SqlParameter("@isDefault", IsDefault));
            if (referralDetailsId is not null && referralDetailsId.Count>0)
                paramsList.Add(new SqlParameter("@referralDetailsId", string.Join<long>(",", referralDetailsId)));

            var response = await ExecuteStoredProcedure("[Patient].[FetchReferralDetails]", paramsList);
            return response;
        }
        public async Task<List<InputReferralDetail>> ExecuteStoredProcedure(string storedProcedureName, IEnumerable<SqlParameter> parameters)
        {
            List<InputReferralDetail> referrals = null;
            var dbConnection = (_readOnlyDbContext.Database.GetDbConnection());
            dbConnection.Open();
            using (var cmd = dbConnection.CreateCommand())
            {
                cmd.CommandText = storedProcedureName;
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 1800;
                foreach (var parameter in parameters)
                {
                    cmd.Parameters.Add(parameter);
                }
                var reader = await cmd.ExecuteReaderAsync();
                referrals = SqlDataToJson(reader);
            }
            return referrals;
        }
        private List<InputReferralDetail> SqlDataToJson(DbDataReader dataReader)
        {
            var dataTable = new DataTable();
            List<InputReferralDetail> refList = new List<InputReferralDetail>();
            dataTable.Load(dataReader);
           
            refList = (from rw in dataTable.AsEnumerable()
                        select new InputReferralDetail
                        {
                            Id = rw["Id"] == DBNull.Value ? null : Convert.ToInt64(rw["Id"]),
                            OrgId = Convert.ToInt32(rw["OrgId"]),
                            PatientDetailsId = rw["PatientDetailsId"] == DBNull.Value ? null : Convert.ToInt64(rw["PatientDetailsId"]),
                            IssueDate = rw["IssueDate"] == DBNull.Value ? null : Convert.ToDateTime(rw["IssueDate"]),
                            ActiveDate = rw["ActiveDate"] == DBNull.Value ? null : Convert.ToDateTime(rw["ActiveDate"]),
                            ExpiryDate = rw["ExpiryDate"] == DBNull.Value ? null : Convert.ToDateTime(rw["ExpiryDate"]),
                            ReferralTypeId = (short)(rw["ReferralTypeId"] == DBNull.Value ? 0 : Convert.ToInt16(rw["ReferralTypeId"])),
                            AssignedProviderInfo = rw["AssignedProvider"] == DBNull.Value ? null : JsonConvert.DeserializeObject<UserDetail>(Convert.ToString(rw["AssignedProvider"])),
                            ReferringProviderCompanyAssocsId = rw["UserCompanyAssocsId"] == DBNull.Value ? null : Convert.ToInt64(rw["UserCompanyAssocsId"]),
                            ReferringProviderInfo = rw["ReferringProvider"] == DBNull.Value ? null : JsonConvert.DeserializeObject<ProviderInfo>(Convert.ToString(rw["ReferringProvider"])),
                            FileDetailsId = rw["FileDetailsId"] == DBNull.Value ? null : Convert.ToInt64(rw["FileDetailsId"]),
                            AssignedProviderId = rw["UserDetailsId"] == DBNull.Value ? null : Convert.ToInt64(rw["UserDetailsId"]),
                            IsDefault = rw["IsDefault"] == DBNull.Value ? null : Convert.ToBoolean(rw["IsDefault"])
                        }).ToList();
           

            return refList;
        }

        /// <summary>
        /// Method to fetch the referral details for a patient from db
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ICollection<ReferralDetail>> GetReferrals(int orgId, long id)
        {
            var referralList = await _readOnlyDbContext.ReferralDetails.AsNoTracking().Where(x => x.PatientDetailsId == id && x.OrgId == orgId && x.StatusId == (short)Status.Active).ToListAsync();
            return referralList;
        }

        public async Task<long> AddReferrals(List<ReferralDetail> referralDetails)
        {
            _updatableDBContext.ReferralDetails.AddRange(referralDetails);
            return await _updatableDBContext.SaveChangesAsync();
        }

        public async Task<long> UpdateReferrals(List<ReferralDetail> referralDetails)
        {
            _updatableDBContext.ReferralDetails.UpdateRange(referralDetails);
            return await _updatableDBContext.SaveChangesAsync();
        }
    }
}
