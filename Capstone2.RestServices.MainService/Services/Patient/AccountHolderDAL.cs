﻿using Capstone2.RestServices.Patient.Context;
using Capstone2.RestServices.Patient.Interfaces;
using Capstone2.RestServices.Patient.Models;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Patient.Services
{
    public class AccountHolderDAL : IAccountHolderDAL
    {
        public readonly ReadOnlyPatientDBContext _readOnlyDbContext;
        public readonly UpdatablePatientDBContext _updatableDBContext;
        public AccountHolderDAL(ReadOnlyPatientDBContext readOnlyDbContext, UpdatablePatientDBContext updatableDBContext)
        {
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
        }

        /// <summary>
        /// Method to add AccountHolderData to AccountHolderData table
        /// </summary>
        /// <param name="AccountHolderData"></param>
        /// <returns></returns>
        public async Task<long> AddAccountHolderDataAsync(AccountHolder AccountHolderData)
        {
            _updatableDBContext.AccountHolder.Add(AccountHolderData);
            return await _updatableDBContext.SaveChangesAsync();
        }

        public async Task<AccountHolder> FetchAccountHolderDetails(int orgId, long patientId)
        {
            var accountHolder = await (from AH in _readOnlyDbContext.AccountHolder
                                       where AH.PatientDetailsId == patientId && AH.OrgId == orgId && AH.StatusId == (short)Status.Active
                                       select new AccountHolder
                                       {
                                           Id = AH.Id,
                                           OrgId = AH.OrgId,
                                           PatientDetailsId = AH.PatientDetailsId,
                                           DefaultAccountHolder = AH.DefaultAccountHolder,
                                           DefaultBillingSchedule = AH.DefaultBillingSchedule,
                                           IsMedicare = AH.IsMedicare,
                                           IsHealthFund = AH.IsHealthFund,
                                           IsParentGuardian = AH.IsParentGuardian,
                                           IsDva = AH.IsDva,
                                           IsAdf = AH.IsAdf,
                                           IsPension = AH.IsPension,
                                           IsHealthCareCard = AH.IsHealthCareCard,
                                           IsOther = AH.IsOther,
                                           OtherType = AH.OtherType,
                                           IsWorkersComp = AH.IsWorkersComp,
                                           StatusId = AH.StatusId,
                                           CreatedDate = AH.CreatedDate,
                                           CreatedBy = AH.CreatedBy,
                                           ModifiedBy = AH.ModifiedBy,
                                           ModifiedDate = AH.ModifiedDate,

                                           AccountHolderAddresses = (List<AccountHolderAddresses>)(ICollection<AccountHolderAddresses>)
                                                (from AA in _readOnlyDbContext.AccountHolderAddresses
                                                 where AA.OrgId == orgId && AA.AccountHolderId == AH.Id && AA.StatusId == (short)Status.Active
                                                 select new AccountHolderAddresses
                                                 {
                                                     Id = AA.Id,
                                                     OrgId = AA.OrgId,
                                                     AccountHolderId = AA.AccountHolderId,
                                                     AccountHolderType = AA.AccountHolderType,
                                                     AddressType = AA.AddressType,
                                                     AddressLine1 = AA.AddressLine1,
                                                     AddressLine2 = AA.AddressLine2,
                                                     Suburb = AA.Suburb,
                                                     StateId = AA.StateId,
                                                     CountryId = AA.CountryId,
                                                     PostCode = AA.PostCode,
                                                     StatusId = AA.StatusId,
                                                     CreatedDate = AA.CreatedDate,
                                                     CreatedBy = AA.CreatedBy,
                                                     ModifiedBy = AA.ModifiedBy,
                                                     ModifiedDate = AA.ModifiedDate
                                                 }),

                                           AccountHolderAssocs = (List<AccountHolderAssocs>)(ICollection<AccountHolderAssocs>)
                                                (from AA in _readOnlyDbContext.AccountHolderAssocs
                                                 where AA.OrgId == orgId && AA.AccountHolderId == AH.Id && AA.StatusId == (short)Status.Active
                                                 select new AccountHolderAssocs
                                                 {
                                                     Id = AA.Id,
                                                     OrgId = AA.OrgId,
                                                     AccountHolderId = AA.AccountHolderId,
                                                     AccountHolderType = AA.AccountHolderType,
                                                     AccountNumber = AA.AccountNumber,
                                                     AccountSubNumber = AA.AccountSubNumber,
                                                     ColourId = AA.ColourId,
                                                     CompanyDetailsId = AA.CompanyDetailsId,
                                                     ExpiryDate = AA.ExpiryDate,
                                                     VerificationStatus = AA.VerificationStatus,
                                                     VerificationMessage = AA.VerificationMessage,
                                                     VerifiedBy = AA.VerifiedBy,
                                                     VerifiedDate = AA.VerifiedDate,
                                                     StatusId = AA.StatusId,
                                                     CreatedDate = AA.CreatedDate,
                                                     CreatedBy = AA.CreatedBy,
                                                     ModifiedBy = AA.ModifiedBy,
                                                     ModifiedDate = AA.ModifiedDate,
                                                     AcceptedDisabilityCode = AA.AcceptedDisabilityCode,
                                                     AcceptedDisabilityInd = AA.AcceptedDisabilityInd
                                                 }),

                                           AccountHolderHealthfundAssocs = (AccountHolderHealthfundAssocs)
                                                (from HF in _readOnlyDbContext.AccountHolderHealthfundAssocs
                                                 where HF.OrgId == orgId && HF.AccountHolderId == AH.Id && HF.StatusId == (short)Status.Active
                                                 select new AccountHolderHealthfundAssocs
                                                 {
                                                     Id = HF.Id,
                                                     OrgId = HF.OrgId,
                                                     AccountHolderId = HF.AccountHolderId,
                                                     AccountHolderType = HF.AccountHolderType,
                                                     Name = HF.Name,
                                                     MembershipNumber = HF.MembershipNumber,
                                                     FundId = HF.FundId,
                                                     LevelOfCover = HF.LevelOfCover,
                                                     PolicyOwnerName = HF.PolicyOwnerName,
                                                     Relationship = HF.Relationship,
                                                     MemberAddress = HF.MemberAddress,
                                                     VerificationStatus = HF.VerificationStatus,
                                                     VerificationMessage = HF.VerificationMessage,
                                                     VerifiedBy = HF.VerifiedBy,
                                                     VerifiedDate = HF.VerifiedDate,
                                                     StatusId = HF.StatusId,
                                                     CreatedDate = HF.CreatedDate,
                                                     CreatedBy = HF.CreatedBy,
                                                     ModifiedBy = HF.ModifiedBy,
                                                     ModifiedDate = HF.ModifiedDate
                                                 }).FirstOrDefault(),

                                           AccountHolderParentGuardianAssocs = (AccountHolderParentGuardianAssocs)
                                                (from HF in _readOnlyDbContext.AccountHolderParentGuardianAssocs
                                                 where HF.OrgId == orgId && HF.AccountHolderId == AH.Id && HF.StatusId == (short)Status.Active
                                                 select new AccountHolderParentGuardianAssocs
                                                 {
                                                     Id = HF.Id,
                                                     OrgId = HF.OrgId,
                                                     AccountHolderId = HF.AccountHolderId,
                                                     AccountHolderType = HF.AccountHolderType,
                                                     FirstName = HF.FirstName,
                                                     LastName = HF.LastName,
                                                     DateOfBirth = HF.DateOfBirth,
                                                     MedicareCardNumber = HF.MedicareCardNumber,
                                                     MedicareCardIrn = HF.MedicareCardIrn,
                                                     MedicareCardExpiry = HF.MedicareCardExpiry,
                                                     Notes = HF.Notes,
                                                     VerificationStatus = HF.VerificationStatus,
                                                     VerificationMessage = HF.VerificationMessage,
                                                     VerifiedBy = HF.VerifiedBy,
                                                     VerifiedDate = HF.VerifiedDate,
                                                     StatusId = HF.StatusId,
                                                     CreatedDate = HF.CreatedDate,
                                                     CreatedBy = HF.CreatedBy,
                                                     ModifiedBy = HF.ModifiedBy,
                                                     ModifiedDate = HF.ModifiedDate
                                                 }).FirstOrDefault(),

                                           AccountHolderWorkersCompAssocs = (AccountHolderWorkersCompAssocs)
                                                (from HF in _readOnlyDbContext.AccountHolderWorkersCompAssocs
                                                 where HF.OrgId == orgId && HF.AccountHolderId == AH.Id && HF.StatusId == (short)Status.Active
                                                 select new AccountHolderWorkersCompAssocs
                                                 {
                                                     Id = HF.Id,
                                                     OrgId = HF.OrgId,
                                                     AccountHolderId = HF.AccountHolderId,
                                                     AccountHolderType = HF.AccountHolderType,
                                                     CompanyDetailsId = HF.CompanyDetailsId,
                                                     ClaimManager = HF.ClaimManager,
                                                     ClaimManagerWorkPhone = HF.ClaimManagerWorkPhone,
                                                     ClaimManagerMobilePhone = HF.ClaimManagerMobilePhone,
                                                     ClaimManagerEmail = HF.ClaimManagerEmail,
                                                     ClaimNumber = HF.ClaimNumber,
                                                     EmployerCompanyName = HF.EmployerCompanyName,
                                                     EmployeeRepresentativeName = HF.EmployeeRepresentativeName,
                                                     EmployeeRepresentativeWorkPhone = HF.EmployeeRepresentativeWorkPhone,
                                                     EmployeeRepresentativeMobilePhone = HF.EmployeeRepresentativeMobilePhone,
                                                     EmployeeRepresentativeEmail = HF.EmployeeRepresentativeEmail,
                                                     EmployeeRole = HF.EmployeeRole,
                                                     EmployerInjury = HF.EmployerInjury,
                                                     EmployerInjuryDate = HF.EmployerInjuryDate,
                                                     EmployeeAffectedBody = HF.EmployeeAffectedBody,
                                                     EmployeeInjuryPlace = HF.EmployeeInjuryPlace,
                                                     EmployeeIncidentSummary = HF.EmployeeIncidentSummary,
                                                     StatusId = HF.StatusId,
                                                     CreatedDate = HF.CreatedDate,
                                                     CreatedBy = HF.CreatedBy,
                                                     ModifiedBy = HF.ModifiedBy,
                                                     ModifiedDate = HF.ModifiedDate
                                                 }).FirstOrDefault()

                                       }).FirstOrDefaultAsync();
            return accountHolder;
        }

        /// <summary>
        /// Method to fetch the AccountHolder details for a patient from db
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<AccountHolder> GetAccountHolder(int orgId, long accountHolderId)
        {
            return await _readOnlyDbContext.AccountHolder.FirstOrDefaultAsync(x => x.Id == accountHolderId && x.OrgId == orgId);
        }

        /// <summary>
        /// GetAccountHolderAssocs
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="accountHolderId"></param>
        /// <returns></returns>
        public async Task<List<AccountHolderAssocs>> GetAccountHolderAssocs(int orgId, long accountHolderId)
        {
            return await _readOnlyDbContext.AccountHolderAssocs
                            .Where(s => s.AccountHolderId == accountHolderId && s.OrgId == orgId && s.StatusId == (short)Status.Active)
                            .AsNoTracking().ToListAsync();
        }

        /// <summary>
        /// AccountHolderAddress
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="accountHolderId"></param>
        /// <returns></returns>
        public async Task<List<AccountHolderAddresses>> GetAccountHolderAddress(int orgId, long accountHolderId)
        {
            return await _readOnlyDbContext.AccountHolderAddresses
                            .Where(s => s.AccountHolderId == accountHolderId && s.OrgId == orgId && s.StatusId == (short)Status.Active)
                            .AsNoTracking().ToListAsync();
        }

        /// <summary>
        /// Update AccountHolder
        /// </summary>
        /// <param name="AccountHolder"></param>
        /// <returns></returns>
        public async Task<int> UpdateAccountHolderData(AccountHolder AccountHolderData)
        {
            _updatableDBContext.AccountHolder.Update(AccountHolderData);
            return await _updatableDBContext.SaveChangesAsync();
        }

        public async Task<ClaimantPatientDetailInfo> GetAccountHolderInfo(int orgId, long patientId)
        {
            return await (from P in _readOnlyDbContext.PatientDetails
                          where P.Id == patientId && P.OrgId == orgId
                          join AH in _readOnlyDbContext.AccountHolder on P.Id equals AH.PatientDetailsId into JAH
                          from AH in JAH.DefaultIfEmpty()
                              //join AD in _readOnlyDbContext.Addresses on P.Id equals AD.PatientDetailsId into JAD
                              //from AD in JAD.DefaultIfEmpty()
                              //join AHA in _readOnlyDbContext.AccountHolderAssocs on AH.Id equals AHA.AccountHolderId into JAHA
                              //from AHA in JAHA.DefaultIfEmpty()
                          select new ClaimantPatientDetailInfo
                          {
                              Id = (long)P.Id,
                              SurName = P.SurName,
                              FirstName = P.FirstName,
                              DateofBirth = P.DateofBirth,
                              GenderId = P.GenderId,
                              AccountHolderId = (AH == null) ? null : AH.Id,
                              Mobile = P.Mobile,
                              HomeContact = P.HomeContact,
                              WorkContact = P.WorkContact,
                              AlsoKnownAs = P.OtherName,
                              PatientAddress = (P == null) ? null :
                               (from ADP in _readOnlyDbContext.Addresses
                                where ADP.AddressType == (short)AddressType.Physical && ADP.PatientDetailsId == P.Id && ADP.StatusId == (short)Status.Active
                                select new PatientAddress
                                {
                                    Id = ADP.Id,
                                    PatientDetailsId = ADP.PatientDetailsId,
                                    AddressLine1 = ADP.AddressLine1,
                                    AddressLine2 = ADP.AddressLine2,
                                    Suburb = ADP.Suburb,
                                    CountryId = ADP.CountryId,
                                    PostCode = ADP.PostCode,
                                    StateId = ADP.StateId
                                }).FirstOrDefault(),
                              AccountHolderAssocInfos = (AH == null) ? null : (ICollection<AccountHolderAssocInfo>)
                                         (from AHA in _readOnlyDbContext.AccountHolderAssocs
                                          where AH.Id == AHA.AccountHolderId && AHA.StatusId == (short)Status.Active

                                          select new AccountHolderAssocInfo
                                          {
                                              Id = AHA.Id,
                                              AccountHolderId = AHA.AccountHolderId,
                                              AccountNumber = AHA.AccountNumber,
                                              AccountSubNumber = AHA.AccountSubNumber,
                                              AccountHolderType = AHA.AccountHolderType,
                                              ColourId = AHA.ColourId,
                                              AcceptedDisabilityCode = AHA.AcceptedDisabilityCode,
                                              AcceptedDisabilityInd = AHA.AcceptedDisabilityInd
                                          }),
                              AccountHolderHealthfundAssocs = (AH == null) ? null : (ICollection<AccountHolderHealthfundAssoc>)
                                         (from AHH in _readOnlyDbContext.AccountHolderHealthfundAssocs
                                          where AH.Id == AHH.AccountHolderId && AHH.StatusId == (short)Status.Active

                                          select new AccountHolderHealthfundAssoc
                                          {
                                              Id = AHH.Id,
                                              AccountHolderId = AHH.AccountHolderId,
                                              Name = AHH.Name,
                                              MembershipNumber = AHH.MembershipNumber,
                                              AccountHolderType = AHH.AccountHolderType,
                                              FundId = AHH.FundId,
                                              LevelOfCover = AHH.LevelOfCover
                                          }),
                              AccountHolderAddress = (AH == null || AH.IsParentGuardian == false) ? null :
                                        (from AHAD in _readOnlyDbContext.AccountHolderAddresses
                                         where AH.Id == AHAD.AccountHolderId && AHAD.AccountHolderType == (short)AccountHolderTypes.ParentOrGuardian && AHAD.StatusId == (short)Status.Active
                                         select new AccountHolderAddressesInfo
                                         {
                                             Id = AHAD.Id,
                                             AddressLine1 = AHAD.AddressLine1,
                                             AddressLine2 = AHAD.AddressLine2,
                                             Suburb = AHAD.Suburb,
                                             PostCode = AHAD.PostCode,
                                             AccountHolderType = AHAD.AccountHolderType,
                                         }).FirstOrDefault(),
                              AccountHolderParentGuardianAssocInfo = (AH == null || AH.IsParentGuardian == false) ? null :
                                         (from AHP in _readOnlyDbContext.AccountHolderParentGuardianAssocs
                                          where AH.Id == AHP.AccountHolderId && AHP.StatusId == (short)Status.Active && AHP.AccountHolderType == (short)AccountHolderTypes.ParentOrGuardian

                                          select new AccountHolderParentGuardianAssocInfo
                                          {
                                              Id = AHP.Id,
                                              AccountHolderId = AHP.AccountHolderId,
                                              MedicareCardIrn = AHP.MedicareCardIrn,
                                              MedicareCardNumber = AHP.MedicareCardNumber,
                                              DateOfBirth = AHP.DateOfBirth,
                                              FirstName = AHP.FirstName,
                                              LastName = AHP.LastName
                                          }).FirstOrDefault()


                          }).FirstOrDefaultAsync();


        }

        public async Task<PatientViewForClaims> PatientViewForClaims(int orgId, long patientId)
        {
            return await (from P in _readOnlyDbContext.PatientDetails
                          where P.Id == patientId && P.OrgId == orgId
                          join AH in _readOnlyDbContext.AccountHolder on P.Id equals AH.PatientDetailsId into JAH
                          from AH in JAH.DefaultIfEmpty()

                          select new PatientViewForClaims
                          {
                              Id = (long)P.Id,
                              SurName = P.SurName,
                              FirstName = P.FirstName,
                              DateofBirth = P.DateofBirth,
                              Mobile = P.Mobile,
                              HomeContact = P.HomeContact,
                              WorkContact = P.WorkContact,
                              RecordId = P.RecordId,
                              PersonalEmailAddress = P.PersonalEmailAddress,
                              AccountHolderHealthfundAssocs = (AH == null) ? null : (ICollection<AccountHolderHealthfundAssoc>)
                                         (from AHH in _readOnlyDbContext.AccountHolderHealthfundAssocs
                                          where AH.Id == AHH.AccountHolderId && AHH.StatusId == (short)Status.Active

                                          select new AccountHolderHealthfundAssoc
                                          {
                                              Id = AHH.Id,
                                              AccountHolderId = AHH.AccountHolderId,
                                              Name = AHH.Name,
                                              MembershipNumber = AHH.MembershipNumber,
                                              AccountHolderType = AHH.AccountHolderType,
                                              FundId = AHH.FundId,
                                              LevelOfCover = AHH.LevelOfCover,
                                              MemberAddress = AHH.MemberAddress,
                                              Relationship = AHH.Relationship,
                                              HealthFundLevelOfCover = (AHH.LevelOfCover != null && AHH.LevelOfCover > default(short)) ? EnumExtensions.GetDescription((HealthFundLevelOfCover)AHH.LevelOfCover) : null,
                                              PolicyOwnerName = AHH.PolicyOwnerName
                                          })

                          }).FirstOrDefaultAsync();


        }

        public async Task<int> UpdateIHI(long patient_id, long userId, string ihiNumber)
        {
            var patientDetail = await _updatableDBContext.PatientDetails.FindAsync(patient_id);
            if (patientDetail == null)
            {
                throw new Exception("Patient not found");
            }
            patientDetail.IHINumber = ihiNumber;
            patientDetail.ModifiedBy = userId;
            patientDetail.ModifiedDate = DateTime.UtcNow;

            return await _updatableDBContext.SaveChangesAsync();
        }
    }
}
