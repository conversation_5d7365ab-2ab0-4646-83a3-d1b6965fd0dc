﻿using AutoMapper;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.Patient.Interfaces;
using Capstone2.RestServices.Patient.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;


namespace Capstone2.RestServices.Patient.Services
{
    public class ActivityLogBAL : IActivityLogBAL
    {
        public readonly IActivityLogDAL _activityLogDAL;
        public IMapper _mapper;
        public readonly AppSettings _appSettings;
        private readonly ILogger<ActivityLogBAL> _logger;
        public ActivityLogBAL(IActivityLogDAL activityLogDAL, IMapper mapper, IOptions<AppSettings> appSettings, ILogger<ActivityLogBAL> logger)
        {
            _activityLogDAL = activityLogDAL;
            _mapper = mapper;
            _appSettings = appSettings.Value;
           _logger = logger;        

    }
    /// <summary>
    /// Method to add new Activity log entry
    /// </summary>
    /// <param name="activityLog"></param>
    /// <param name="baseHttpRequestContext"></param>
    /// <param name="patient_id"></param>
    /// <returns></returns>
    public async Task<ApiResponse<long?>> AddActivityLog(ActivityLog activityLog, BaseHttpRequestContext baseHttpRequestContext, long patient_id)
        {
            ApiResponse<long?> apiResponse = new();

            var orgId = baseHttpRequestContext.OrgId;
            var loggedInUser = baseHttpRequestContext.UserId;

            activityLog.PatientDetailsId = patient_id;
            activityLog.OrgId = orgId;
            activityLog.CreatedDate = DateTime.UtcNow;
            activityLog.ModifiedDate = DateTime.UtcNow;
            activityLog.ModifiedBy = loggedInUser;
            if (activityLog.ActivityLogChildEntries is not null && activityLog.ActivityLogChildEntries.Count > 0)
            {
                activityLog.ActivityLogChildEntries.ToList().ForEach(log =>
                {
                    log.OrgId = orgId;
                    log.CreatedDate = DateTime.UtcNow;
                    log.ModifiedDate = DateTime.UtcNow;
                    log.ModifiedBy = loggedInUser;
                }                 

                );
            }

            var id = await _activityLogDAL.AddActivityLog(activityLog);
            if (id > 0)
            {

                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";
                apiResponse.Result = id;
                return apiResponse;
            }
            else
            {

                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failure";
                apiResponse.Result = null;
                apiResponse.Errors.Add("Activity Log could not be created.");
                return apiResponse;
            }

        }
        /// <summary>
        /// Upating activity log
        /// </summary>
        /// <param name="activityLog"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="patient_id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> UpdateActivityLog(ActivityLog activityLog, BaseHttpRequestContext baseHttpRequestContext, long patient_id,long id)
        {
            ApiResponse<long?> apiResponse = new();

            int orgId = baseHttpRequestContext.OrgId;
            var loggedInUser = baseHttpRequestContext.UserId;

            ActivityLog dbActivityLog = await _activityLogDAL.GetActivityLog(orgId,id);
            if(dbActivityLog is not null)
            {
                dbActivityLog.ActivityStatusId = activityLog.ActivityStatusId;
                dbActivityLog.FileDetailsId = activityLog.FileDetailsId;
                dbActivityLog.ModifiedDate = DateTime.UtcNow;
                dbActivityLog.ModifiedBy = loggedInUser;
                dbActivityLog.EpisodesId = activityLog.EpisodesId;
                if (activityLog.ActivityLogTypeId == (short)ActivityLogType.Media_Library)
                    dbActivityLog.PatientDetailsId = activityLog.PatientDetailsId;
                if (!string.IsNullOrWhiteSpace(activityLog.Description))
                    dbActivityLog.Description = activityLog.Description;
                if (activityLog.ActivityLogChildEntries is not null && activityLog.ActivityLogChildEntries.Count > 0)
                {
                    activityLog.ActivityLogChildEntries.ToList().ForEach(log =>
                    {
                        log.OrgId = orgId;
                        log.CreatedDate = DateTime.UtcNow;
                        log.ModifiedDate = DateTime.UtcNow;
                        log.ModifiedBy = loggedInUser;
                    });
                    dbActivityLog.ActivityLogChildEntries = activityLog.ActivityLogChildEntries;
                }

                int rows = await _activityLogDAL.UpdateActivityLog(dbActivityLog);
                if(rows > 0)
                {
                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    apiResponse.Message = "Success";
                    apiResponse.Result = id;
                    return apiResponse;
                }
            }      
            

            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            apiResponse.Message = "Failure";
            apiResponse.Errors.Add("Activity Log could not be found.");
            return apiResponse;
            
            
        }

        public async Task<ApiResponse<long?>> UpdateActivityLogChild(ActivityLogChildEntryInfo activityLog, BaseHttpRequestContext baseHttpRequestContext, int statusId, int filedetailsid)
        {
            ApiResponse<long?> apiResponse = new();

            int orgId = baseHttpRequestContext.OrgId;
            var loggedInUser = baseHttpRequestContext.UserId;

            ActivityLogChildEntry dbActivityLogChild = await _activityLogDAL.GetActivityLogChild(orgId, activityLog.ActivityLogId);

            if (dbActivityLogChild is not null)
            {
                int rows = await _activityLogDAL.UpdateActivityLogChildByFileDetailsId(dbActivityLogChild, statusId, filedetailsid);
                if (rows > 0)
                {
                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    apiResponse.Message = "Success";
                    apiResponse.Result = dbActivityLogChild.ActivityLogId;
                    return apiResponse;
                }
            }

            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            apiResponse.Message = "Failure";
            apiResponse.Errors.Add("Activity Log Child could not be found.");
            return apiResponse;

        }

            /// <summary>
            /// Method to retireve list of Activity logs for a patient
            /// </summary>
            /// <param name="baseHttpRequestContext"></param>
            /// <param name="queryModel"></param>
            /// <param name="patient_id"></param>
            /// <returns></returns>
            public async Task<ApiResponse<QueryResultList<ActivityLogList>>> ListActivityLogs(BaseHttpRequestContext baseHttpRequestContext, QueryModel queryModel, long patient_id)
        {
            ApiResponse<QueryResultList<ActivityLogList>> apiResponse = new();
            ActivityLogFilter filterModel = PrepareFilterParameters(queryModel.Filter);

            if (filterModel is not null && filterModel.ActivityLogTypeId is not null && filterModel.ActivityLogTypeId.Contains((short)ActivityLogType.Media_Library))
            {
                var perm = await  _activityLogDAL.GetPermissionOnRoleId(baseHttpRequestContext.OrgId, baseHttpRequestContext.RoleId, (short)ApiPermission.Activitylog_Filter_Media);
                if(perm.IsAllowed == false)
                {
                    apiResponse.StatusCode = StatusCodes.Status403Forbidden;
                    apiResponse.Errors.Add("Not an Authorized User");
                    apiResponse.Result = null;
                    return apiResponse;
                }
            }
          
            QueryResultList<ActivityLogList> queryList = await _activityLogDAL.ListActivityLogs(baseHttpRequestContext.OrgId, patient_id,queryModel, filterModel);
            if(queryList.ItemRecords is not null && queryList.ItemRecords.Count > 0)
            {
                List<ActivityLogList> lstActivtyLogs = queryList.ItemRecords.ToList();
                //Checking for Appointment Activty logs to popualte AppointmentDetails
                List<KeyValuePair<long?, long>> lstAppLogs =  lstActivtyLogs.Where(log => log.ActivityLogTypeId == (short)ActivityLogType.Appointments && log.EntityId is not null).Select(x => new KeyValuePair<long?, long>( x.EntityId, x.Id)).ToList();
                if(lstAppLogs is not null && lstAppLogs.Count > 0)
                {
                    List<AppointmentDetailInfo> lstAppointments = await _activityLogDAL.FetchAppointmentsFromIds(baseHttpRequestContext.OrgId, lstAppLogs.Select(kvp => kvp.Key).ToList());
                    if(lstAppointments is not null && lstAppointments.Count > 0)
                    {
                       
                        lstActivtyLogs.Where(log => lstAppLogs.Select(kv => kv.Value).ToList().Contains(log.Id)).ToList()
                            .ForEach(activityLog=>
                            {
                                activityLog.AppointmentDetails = lstAppointments.Find(x => x.Id == activityLog.EntityId);
                                activityLog.ActivityStatus = (activityLog.ActivityStatusId is null) ? null : EnumExtensions.GetDescription((AppointmentStatus)activityLog.ActivityStatusId);
                            });
                    }
                    
                }
                List<ActivityLogList> lstLetterLogs = lstActivtyLogs.Where(log => log.ActivityLogTypeId == (short)ActivityLogType.Media_Library && log.ActivityLogSubTypeId == (short)MediaLibrarySubType.Patient_Letter && log.EntityId is not null).ToList();
                if (lstLetterLogs is not null && lstLetterLogs.Count > 0)
                {
                    lstLetterLogs.ForEach(activityLog =>
                    {
                        activityLog.ActivityStatus = (activityLog.ActivityStatusId is null) ? null : EnumExtensions.GetDescription((LetterStatus)activityLog.ActivityStatusId);
                    });

                }
            }
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result = queryList;



            return apiResponse;
        }

        private ActivityLogFilter PrepareFilterParameters(string filter)
        {
            if (!(string.IsNullOrEmpty(filter)))
            {
                return JsonConvert.DeserializeObject<ActivityLogFilter>(filter);
            }
            else
            {
                return null;
            }
        }
        /// <summary>
        /// Method to insert a new Activity log child entry
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="activityLog"></param>
        /// <param name="patientId"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> AddActivityLogChild(BaseHttpRequestContext baseHttpRequestContext, ActivityLogChildEntryInfo activityLogChild, long patientId)
        {
            ApiResponse<long?> apiResponse = new();

            int orgId = baseHttpRequestContext.OrgId;
            var loggedInUser = baseHttpRequestContext.UserId;


            if (!(activityLogChild is null || activityLogChild.ParentEntityId is null ))
            {
                ActivityLog dbActivityLog = await _activityLogDAL.FetchActivtyLogFromEntityId(patientId, orgId, activityLogChild.ParentEntityId, activityLogChild.ParentActivityLogTypeId);
                if (dbActivityLog is not null)
                {
                    dbActivityLog.ActivityStatusId = activityLogChild.ActivityStatusId;
                    dbActivityLog.Description = activityLogChild.ParentDescription;
                    dbActivityLog.FileDetailsId = activityLogChild.FileDetailsId;
                    dbActivityLog.ModifiedDate = DateTime.UtcNow;
                    dbActivityLog.ModifiedBy = loggedInUser;

                    ActivityLogChildEntry inputActivtyLogChild = _mapper.Map<ActivityLogChildEntryInfo, ActivityLogChildEntry>(activityLogChild);

                    if (inputActivtyLogChild is not null )
                    {

                        inputActivtyLogChild.OrgId = orgId;
                        inputActivtyLogChild.CreatedDate = DateTime.UtcNow;
                        inputActivtyLogChild.ModifiedDate = DateTime.UtcNow;
                        inputActivtyLogChild.ModifiedBy = loggedInUser;
                        dbActivityLog.ActivityLogChildEntries.Add(inputActivtyLogChild);

                    }
                    int rows = await _activityLogDAL.UpdateActivityLog(dbActivityLog);
                    if (rows > 0)
                    {
                        apiResponse.StatusCode = StatusCodes.Status200OK;
                        apiResponse.Message = "Success";
                        apiResponse.Result = dbActivityLog.Id;
                        return apiResponse;
                    }
                    
                }
            }
            
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            apiResponse.Message = "Failure";
            apiResponse.Errors.Add("Activity Log cannot be added with the data provided.");
            return apiResponse;
        }

        /// <summary>
        /// Method to retireve list of Activity logs Child Entries for a patient
        /// </summary>
        public async Task<ApiResponse<QueryResultList<ActivityLogChildList>>> ListActivityLogChildBAL(BaseHttpRequestContext baseHttpRequestContext, QueryModel queryModel, long activitylog_id)
        {
            ApiResponse<QueryResultList<ActivityLogChildList>> apiResponse = new();

            // ActivityLogFilter filterModel = PrepareFilterParameters(queryModel.Filter);

           QueryResultList<ActivityLogChildList> queryList = await _activityLogDAL.ListActivityLogChildDAL(baseHttpRequestContext.OrgId, queryModel, activitylog_id);

            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result = queryList;
            return apiResponse;

        }


        /// <summary>
        /// Method to retireve Activity log for a patient
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="id"></param>
        /// <param name="patient_id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<ActivityLogList>> GetActivityLogById(BaseHttpRequestContext baseHttpRequestContext,long patient_id,long id)
        {
            ApiResponse<ActivityLogList> apiResponse = new();

            ActivityLogList activityLog = await _activityLogDAL.GetActivityLog(baseHttpRequestContext.OrgId, patient_id,id);

            if (activityLog is null)
            {
                apiResponse.Result = null;
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("The Activity Log does not exist.");
                return apiResponse;
            }

            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result = activityLog;
            return apiResponse;
        }





    }
}
