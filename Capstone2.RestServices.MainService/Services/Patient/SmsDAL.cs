﻿using Capstone2.RestServices.Patient.Context;
using Capstone2.RestServices.Patient.Interfaces;
using Capstone2.RestServices.Patient.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Patient.Services
{
    public class SmsDAL : ISmsDAL
    {
        public readonly ReadOnlyPatientDBContext _readOnlyDbContext;
        public readonly UpdatablePatientDBContext _updatableDBContext;
        public SmsDAL(ReadOnlyPatientDBContext readOnlyDbContext, UpdatablePatientDBContext updatableDBContext)
        {
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
        }

        public async Task<QueryResultList<SmsHistoryView>> ListSmsHistoryDAL(int orgId, QueryModel queryModel, SmsFilter filterModel)
        {

            var query = from SH in _readOnlyDbContext.SmsHistory
                        where SH.OrgId == orgId //&& A.StatusId == (short)Status.Active
                        join SR in _readOnlyDbContext.SmsRequests on SH.SmsRequestsId equals SR.Id into JAT
                        from SR in JAT.DefaultIfEmpty()                           
                        select new SmsHistoryView
                        {
                            Id = SH.Id,
                            OrgId = SH.OrgId,
                            PatientDetailsId = SH.PatientDetailsId,
                            SmsRequestsId = SH.SmsRequestsId,
                            AppointmentDetailsId = SH.AppointmentDetailsId,
                            SmsSentTypeId = SH.SmsSentTypeId,
                            SentById = SH.SentById,
                            CreatedDate = SH.CreatedDate,
                            CreatedBy = SH.CreatedBy,
                            SmsRequest = new SmsRequestInfo
                            {
                                Id = SR.Id,
                                RequestBody = (SH.SmsSentTypeId == (short)SmsSentType.Patient) ? SR.Reply : SR.RequestBody,
                                ResonseStatus = SR.ResonseStatus
                            },


                            PatientDetails = (SH.SmsSentTypeId == (short)SmsSentType.Patient) ? (from patient in _readOnlyDbContext.PatientDetails
                                                                                                 where SH.SentById == patient.Id
                                                                                                 select new PatientDetailInfo
                                                                                                 {
                                                                                                     Id = (long)patient.Id,
                                                                                                     FirstName = patient == null ? string.Empty : patient.FirstName,
                                                                                                     SurName = patient == null ? string.Empty : patient.SurName,
                                                                                                     PreferredName = patient == null ? string.Empty : patient.PreferredName

                                                                                                 }).FirstOrDefault() : null,
                            UserDetails = (SH.SmsSentTypeId == (short)SmsSentType.User) ? (from user in _readOnlyDbContext.UserDetails
                                                                                           where SH.SentById == user.Id
                                                                                           select new UserDetailInfo
                                                                                           {
                                                                                               Id = (long)user.Id,
                                                                                               FirstName = user.FirstName,
                                                                                               SurName = user.SurName,

                                                                                           }).FirstOrDefault() : null,
                            AppointmentDetails = (SH.AppointmentDetailsId == null) ? null: (from AP in _readOnlyDbContext.AppointmentDetails
                                                                                           where SH.AppointmentDetailsId == AP.Id
                                                                                           join AT in _readOnlyDbContext.AppointmentTypes on AP.AppointmentTypesId equals AT.Id into JAP
                                                                                           from AT in JAP.DefaultIfEmpty()
                                                                                           select new AppointmentInfo
                                                                                           {
                                                                                               Id = AP.Id,
                                                                                               AppointmentTypeCategoryId = AT.Category,
                                                                                               AppointmentTypeName = AT.Type,
                                                                                               AppointmentTypesColour = AT.Colour,
                                                                                               AppointmentTypesId = AT.Id

                                                                                           }).FirstOrDefault()
                            
                        };

            if (filterModel is not null)
            {
                if (filterModel.PatientDetailsId != null)
                {
                    query = query.Where(x =>  filterModel.PatientDetailsId.Contains(x.PatientDetailsId));

                }
                
            }


            if (!string.IsNullOrWhiteSpace(queryModel.SortOrder) && !string.IsNullOrWhiteSpace(queryModel.SortTerm))
            {
                query = SortSms(query, queryModel.SortOrder, queryModel.SortTerm);

            }
            List<SmsHistoryView> paginatedList = await CreatePaginateList(query, queryModel);
            QueryResultList<SmsHistoryView> queryList = new QueryResultList<SmsHistoryView>();
            if (paginatedList != null)
            {

                queryList.ItemRecords = paginatedList;
                queryList.PageNumber = queryModel.PageNumber;
                queryList.PageSize = queryModel.PageSize;
                queryList.CurrentCount = paginatedList.Count();

            }
            else
            {
                queryList.ItemRecords = null;
                queryList.CurrentCount = 0;

            }
            queryList.TotalCount = query.Count();
            queryList.PageNumber = queryModel.PageNumber;
            queryList.PageSize = queryModel.PageSize;
            return queryList;




        }

        private IQueryable<SmsHistoryView> SortSms(IQueryable<SmsHistoryView> query, string sortOrder, string sortTerm)
        {
            switch (sortTerm.ToLower())
            {
                case "createddate":

                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            query = query.OrderBy(x => x.CreatedDate);
                        }
                        else
                        {

                            query = query.OrderByDescending(x => x.CreatedDate);

                        }
                        break;
                    }
               

                default:
                    {
                        query = query.OrderByDescending(x => x.CreatedDate);
                        break;
                    }

            }
            return query;

        }

        private async Task<List<SmsHistoryView>> CreatePaginateList(IQueryable<SmsHistoryView> appointmentQuery, QueryModel queryModel)
        {
            if (queryModel.PageNumber > 0 && queryModel.PageSize > 0)
            {
                if (appointmentQuery.Any())
                {

                    List<SmsHistoryView> paginatedList = await appointmentQuery.Skip((queryModel.PageNumber - 1) * queryModel.PageSize)
                 .Take(queryModel.PageSize).ToListAsync();

                    return paginatedList;

                }

            }
            return null;
        }

        public async Task<long> AddSmsHistory(SmsHistory smsHistory)
        {
            await _updatableDBContext.SmsHistory.AddAsync(smsHistory);
            int rows = await _updatableDBContext.SaveChangesAsync();
            return rows > 0 ? smsHistory.Id : 0;
        }


    }
}
