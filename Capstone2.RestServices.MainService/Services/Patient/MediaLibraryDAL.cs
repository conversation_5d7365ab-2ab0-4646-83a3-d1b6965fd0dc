﻿using AutoMapper;
using Capstone2.RestServices.Patient.Context;
using Capstone2.RestServices.Patient.Interfaces;
using Capstone2.RestServices.Patient.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Patient.Services
{
    public class MediaLibraryDAL : IMediaLibraryDAL
    {
        public readonly ReadOnlyPatientDBContext _readOnlyDbContext;
        public UpdatablePatientDBContext _updatableDBContext;
        public IMapper _mapper;
        public MediaLibraryDAL(ReadOnlyPatientDBContext readOnlyDbContext, UpdatablePatientDBContext updatableDBContext, IMapper mapper)
        {
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
            _mapper = mapper;
        }

        /// <summary>
        /// Method to add new media to Media Library of a patient
        /// </summary>
        /// <param name="media"></param>
        /// <returns></returns>
        public async Task<long?> AddMedia(PatientMediaAssoc media)
        {
            await _updatableDBContext.PatientMediaAssocs.AddAsync(media);
            await _updatableDBContext.SaveChangesAsync();
            return media.Id;
        }

        /// <summary>
        /// Method tosave collection of medias to media library
        /// </summary>
        /// <param name="mediaCollection"></param>
        /// <returns></returns>
        public async Task<int> AddMediaCollection(InputPatientMediaAssoc mediaCollection)
        {
            _updatableDBContext.PatientMediaAssocs.AddRange(mediaCollection.PatientMediaAssocs);
            return await _updatableDBContext.SaveChangesAsync();
        }

        public async Task AddMediasToMediaLibrary(InputPatientMediaAssoc mediaCollection)
        {
            _updatableDBContext.PatientMediaAssocs.AddRange(mediaCollection.PatientMediaAssocs);
            await _updatableDBContext.SaveChangesAsync();
        }

        /// <summary>
        /// Method to create a new Tag
        /// </summary>
        /// <param name="tag"></param>
        /// <returns></returns>
        public async Task<long> AddTag(Tag tag)
        {
            _updatableDBContext.Tags.Add(tag);
            await _updatableDBContext.SaveChangesAsync();
            return tag.Id;
        }

        /// <summary>
        /// Method to retrieve the media based on the id together with the tag associations
        /// </summary>
        /// <param name="id"></param>
        /// <param name="patientId"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<PatientMediaAssoc> GetMediaAssocs(long id, long patientId, int orgId)
        {
            PatientMediaAssoc media = await _readOnlyDbContext.PatientMediaAssocs.Include(m => m.PatientMediaTagsAssocs).Where(m => m.OrgId == orgId && m.Id == id).SingleOrDefaultAsync();
            return media;
        }
        public async Task<PatientMediaAssoc> GetMediaAssocsSync(long id, long patientId, int orgId)
        {
            PatientMediaAssoc media = await _readOnlyDbContext.PatientMediaAssocs.AsNoTrackingWithIdentityResolution().Include(m => m.PatientMediaTagsAssocs).Where(m => m.OrgId == orgId && m.Id == id).SingleOrDefaultAsync();
            return media;
        }

        /// <summary>
        /// Method to fetch list of Tags
        /// </summary>
        /// <param name="searchTerm"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<List<Tag>> ListTags(string searchTerm, int orgId)
        {
            IQueryable<Tag> lstTags = _readOnlyDbContext.Tags.Where(x => x.OrgId == orgId && x.StatusId == (short)Status.Active).OrderBy(x => x.Name);
            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                lstTags = lstTags.Where(x => x.Name.StartsWith(searchTerm));
            }
            return await lstTags.ToListAsync();
        }

        /// <summary>
        /// Method to update Media Library
        /// </summary>
        /// <param name="media"></param>
        public async Task<int> UpdateMediaAssoc(PatientMediaAssoc media)
        {
            _updatableDBContext.PatientMediaAssocs.Update(media);
            int rows = await _updatableDBContext.SaveChangesAsync();
            _updatableDBContext.Entry(media).State = EntityState.Modified;
            return rows;
        }

        /// <summary>
        /// Method to update Media Library in bulk
        /// </summary>
        /// <param name="media"></param>
        public async Task<int> UpdateRangeMediaAssoc(InputPatientMediaAssoc mediaCollection)
        {
            _updatableDBContext.PatientMediaAssocs.UpdateRange(mediaCollection.PatientMediaAssocs);
            return await _updatableDBContext.SaveChangesAsync();
        }
        public async Task UpdateMediaLibrary(PatientMediaAssoc media)
        {
            _updatableDBContext.PatientMediaAssocs.Update(media);
            await _updatableDBContext.SaveChangesAsync();
        }
        public async Task<List<PatientMediaTagsAssoc>> GetMediaTagsAssocs(int orgId, long mediaId)
        {
            return await _readOnlyDbContext.PatientMediaTagsAssocs.AsNoTracking().Where(x => x.OrgId == orgId && x.PatientMediaAssocId == mediaId).ToListAsync();
        }

        public async Task<int> AddMediaTags(List<PatientMediaTagsAssoc> lstAddMediaTags)
        {
            _updatableDBContext.PatientMediaTagsAssocs.AddRange(lstAddMediaTags);
            return await _updatableDBContext.SaveChangesAsync();
        }

        public async Task<PatientMediaAssoc> GetMediaAssocsFromFileDetailsId(long filedetailsId, long patientId, int orgId)
        {
            PatientMediaAssoc media = await _updatableDBContext.PatientMediaAssocs.AsNoTracking().Include(m => m.PatientMediaTagsAssocs).Where(m => m.OrgId == orgId && m.FileDetailsId == filedetailsId).FirstOrDefaultAsync();
            return media;
        }
        public async Task<QueryResultList<ListPatientMediaAssoc>> ListMediaLibrary(int orgId, QueryModel queryModel, MediaLibraryFilterModel mediafilterModel, long patient_id)
        {
            List<SqlParameter> paramsList = new List<SqlParameter>();
            var limit = queryModel.PageSize;
            var offset = (queryModel.PageNumber - 1) * queryModel.PageSize;

            paramsList.Add(new SqlParameter("@orgId", orgId));
            paramsList.Add(new SqlParameter("@offset", offset));
            paramsList.Add(new SqlParameter("@limit", limit));
            paramsList.Add(new SqlParameter("@patientId", patient_id));

            if (!(string.IsNullOrEmpty(queryModel.SearchTerm)))
            {
                paramsList.Add(new SqlParameter("@searchTerm", queryModel.SearchTerm));
            }
            if (!(mediafilterModel is null))
            {
                if (mediafilterModel.MediaTypeId != null && mediafilterModel.MediaTypeId.Any())
                {
                    paramsList.Add(new SqlParameter("@mediaTypeId", string.Join<short>(",", mediafilterModel.MediaTypeId)));
                }
                if (mediafilterModel.EpisodesId != null && mediafilterModel.EpisodesId.Any())
                {
                    paramsList.Add(new SqlParameter("@episodesId", string.Join<int?>(",", mediafilterModel.EpisodesId)));
                }
                if (mediafilterModel.StartDate != null && mediafilterModel.StartDate.Any())
                {
                    paramsList.Add(new SqlParameter("@startDate", mediafilterModel.StartDate[0]));
                }
                if (mediafilterModel.EndDate != null && mediafilterModel.EndDate.Any())
                {
                    paramsList.Add(new SqlParameter("@endDate", mediafilterModel.EndDate[0]));
                }
            }

            var response = await ExecuteStoredProcedure("[Patient].[ListMediaLibrary]", paramsList);
            response.PageNumber = queryModel.PageNumber;
            response.PageSize = queryModel.PageSize;
            return response;
        }

        public async Task<QueryResultList<ListPatientMediaAssoc>> ExecuteStoredProcedure(string storedProcedureName, IEnumerable<SqlParameter> parameters)
        {
            var dbConnection = (_readOnlyDbContext.Database.GetDbConnection());
            await dbConnection.OpenAsync().ConfigureAwait(false);
            using (var cmd = dbConnection.CreateCommand())
            {
                cmd.CommandText = storedProcedureName;
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 1800;
                foreach (var parameter in parameters)
                {
                    cmd.Parameters.Add(parameter);
                }
                var reader = await cmd.ExecuteReaderAsync();
                return SqlDataToJson(reader);
            }
        }

        private QueryResultList<ListPatientMediaAssoc> SqlDataToJson(DbDataReader dataReader)
        {
            var dataTable = new DataTable();
            List<ListPatientMediaAssoc> mediaList = new List<ListPatientMediaAssoc>();
            dataTable.Load(dataReader);
            try
            {
                mediaList = (from rw in dataTable.AsEnumerable()
                             select new ListPatientMediaAssoc
                             {
                                 Id = rw["Id"] == DBNull.Value ? null : Convert.ToInt64(rw["Id"]),
                                 GroupId = rw["GroupId"] == DBNull.Value ? null : (Guid)rw["GroupId"],
                                 OrgId = Convert.ToInt32(rw["OrgId"]),
                                 PatientDetailsId = Convert.ToInt64(rw["PatientDetailsId"]),
                                 MediaName = (string)(rw["MediaName"] == DBNull.Value ? null : rw["MediaName"]),
                                 CreatedDate = Convert.ToDateTime(rw["CreatedDate"]),
                                 ModifiedDate = rw["ModifiedDate"] == DBNull.Value ? null : Convert.ToDateTime(rw["ModifiedDate"]),
                                 EpisodesId = (short)(rw["EpisodesId"] == DBNull.Value ? 0 : Convert.ToInt32(rw["EpisodesId"])),
                                 MediaTypeId = (short)(rw["MediaTypeId"] == DBNull.Value ? 0 : Convert.ToInt16(rw["MediaTypeId"])),
                                 Creator = rw["Creator"] == DBNull.Value ? null : JsonConvert.DeserializeObject<UserDetailInfo>(Convert.ToString(rw["Creator"])),
                                 FileDetailsId = Convert.ToInt64(rw["FileDetailsId"]),
                                 CreatedBy = rw["CreatedBy"] == DBNull.Value ? null : Convert.ToInt64(rw["CreatedBy"]),
                                 ModifiedBy = rw["ModifiedBy"] == DBNull.Value ? null : Convert.ToInt64(rw["ModifiedBy"]),
                                 MediaLibrarySubTypeId = rw["MediaLibrarySubTypeId"] == DBNull.Value ? null : Convert.ToInt16(rw["MediaLibrarySubTypeId"]),
                                 LetterId = (rw["LetterId"] == DBNull.Value ? (long?)null : Convert.ToInt64(rw["LetterId"]))

                             }).ToList();
            }
            catch (Exception E)
            {
            }
            var dataTable2 = new DataTable();
            dataTable2.Load(dataReader);
            var Total = int.Parse(JsonConvert.SerializeObject(dataTable2.Rows[0][0]));
            var respose = new QueryResultList<ListPatientMediaAssoc>()
            {
                ItemRecords = mediaList,
                CurrentCount = mediaList.Count(),
                TotalCount = Total,
                PageNumber = 0,
                PageSize = 0
            };
            return respose;
        }

        public async Task<List<PatientMediaAssoc>> FetchMediaLibraryFromGroupId(long patientId, Guid groupId, int orgId)
        {
            return await _readOnlyDbContext.PatientMediaAssocs.Where(x => x.OrgId == orgId && x.PatientDetailsId == patientId && x.GroupId == groupId).ToListAsync();
        }

        public async Task UpdateMediaTags(List<PatientMediaTagsAssoc> dbMediaTagsCollection)
        {
            _updatableDBContext.PatientMediaTagsAssocs.UpdateRange(dbMediaTagsCollection);
            await _updatableDBContext.SaveChangesAsync();
        }

        /// <summary>
        /// Method to fetch all details of a media with the associated tags
        /// </summary>
        /// <param name="id"></param>
        /// <param name="patientId"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<InputPatientMedia> GetPatientMedia(long id, long patientId, int orgId)
        {
            InputPatientMedia media = await (from PMA in _readOnlyDbContext.PatientMediaAssocs
                                             where PMA.OrgId == orgId && PMA.PatientDetailsId == patientId && PMA.Id == id
                                             from UDC in _readOnlyDbContext.UserDetails.Where(u => u.OrgId == orgId && u.Id == PMA.CreatedBy)
                                             from UDM in _readOnlyDbContext.UserDetails.Where(u => u.OrgId == orgId && u.Id == PMA.ModifiedBy).DefaultIfEmpty()
                                             select new InputPatientMedia
                                             {
                                                 Id = PMA.Id,
                                                 OrgId = PMA.OrgId,
                                                 PatientDetailsId = PMA.PatientDetailsId,
                                                 MediaName = PMA.MediaName,
                                                 EpisodesId = PMA.EpisodesId,
                                                 MediaTypeId = PMA.MediaTypeId,
                                                 CreatedDate = PMA.CreatedDate,
                                                 CreatedBy = PMA.CreatedBy,
                                                 ModifiedDate = PMA.ModifiedDate,
                                                 ModifiedBy = PMA.ModifiedBy,
                                                 FileDetailsId = PMA.FileDetailsId,
                                                 GroupId = PMA.GroupId,
                                                 DeleteReason = PMA.DeleteReason,
                                                 StatusId = PMA.StatusId,
                                                 MediaLibrarySubTypeId = PMA.MediaLibrarySubTypeId,
                                                 Creator = _mapper.Map<UserDetail, UserDetailInfo>(UDC),
                                                 Modifier = _mapper.Map<UserDetail, UserDetailInfo>(UDM),
                                                 InputPatientMediaTags =
                                                 (from PMT in _readOnlyDbContext.PatientMediaTagsAssocs
                                                  where PMT.OrgId == orgId && PMT.PatientMediaAssocId == id && PMT.StatusId == (short)Status.Active
                                                  join TG in _readOnlyDbContext.Tags on PMT.TagsId equals TG.Id
                                                  select new InputPatientMediaTag
                                                  {
                                                      Id = PMT.Id,
                                                      TagsId = PMT.TagsId,
                                                      OrgId = PMT.OrgId,
                                                      TagName = TG.Name,
                                                      StatusId = PMT.StatusId,
                                                      CreatedBy = PMT.CreatedBy,
                                                      CreatedDate = PMT.CreatedDate,
                                                      ModifiedDate = PMT.ModifiedDate,
                                                      ModifiedBy = PMT.ModifiedBy
                                                  }).ToList()


                                             }).FirstOrDefaultAsync();
            return media;
        }

        /// <summary>
        /// Method to letter template based on id
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<LetterTemplateView> GetLetterTemplateDAL(int orgId, long id)
        {
            var letterTemplate = await (from LT in _readOnlyDbContext.LetterTemplates.Where(x => x.Id == id && x.OrgId == orgId)
                                        from CD in _readOnlyDbContext.CompanyDetails.Where(u => u.OrgId == orgId && u.Id == LT.CompanyDetailsId).DefaultIfEmpty()
                                        select new LetterTemplateView()
                                        {
                                            Id = LT.Id,
                                            OrgId = LT.OrgId,
                                            Name = LT.Name,
                                            Text = LT.Text,
                                            HeaderStyleTypeId = LT.HeaderStyleTypeId,
                                            FooterStyleTypeId = LT.FooterStyleTypeId,
                                            FileDetailsId = LT.FileDetailsId,
                                            CompanyDetailsId = LT.CompanyDetailsId,
                                            CompanyDetails = (LT.CompanyDetailsId == null) ? null : new LetterCompanyDetails
                                            {
                                                Id = CD.Id,
                                                Name = CD.Name
                                            },
                                            DeleteReason = LT.DeleteReason,
                                            StatusId = LT.StatusId,
                                            CreatedDate = LT.CreatedDate,
                                            CreatedBy = LT.CreatedBy,
                                            ModifiedBy = LT.ModifiedBy,
                                            ModifiedDate = LT.ModifiedDate,
                                            TemplateTypeId = LT.TemplateTypeId
                                        }).FirstOrDefaultAsync();

            return letterTemplate;
        }

        /// <summary>
        /// Method for fetching multiple mediAS
        /// </summary>
        /// <param name="lstIds"></param>
        /// <param name="orgId"></param>
        /// <param name="patient_id"></param>
        public async Task<List<PatientMediaAssoc>> FetchPatientMedias(List<long> lstIds, int orgId, long patient_id)
        {
            return await _readOnlyDbContext.PatientMediaAssocs.Where(media => media.OrgId == orgId && media.PatientDetailsId == patient_id && lstIds.Contains(media.Id)).ToListAsync();
        }

        /// <summary>
        /// Method for fetching multiple mediAS based on FileDetailsId
        /// </summary>
        /// <param name="lstIds"></param>
        /// <param name="orgId"></param>
        /// <param name="patient_id"></param>
        public async Task<List<PatientMediaAssoc>> FetchPatientMediasByFileDetailsId(List<long> lstIds, int orgId, long patient_id)
        {
            return await _readOnlyDbContext.PatientMediaAssocs.Where(media => media.OrgId == orgId && media.PatientDetailsId == patient_id && lstIds.Contains(media.FileDetailsId)).ToListAsync();
        }

        /// <summary>
        /// GetMedia By passing FileDetailsId
        /// </summary>
        /// <param name="id"></param>
        /// <param name="patientId"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<InputPatientMedia> GetPatientMediaByFiledetailsId(long id, long patientId, int orgId)
        {
            InputPatientMedia media = await (from PMA in _readOnlyDbContext.PatientMediaAssocs
                                             where PMA.OrgId == orgId && PMA.PatientDetailsId == patientId && PMA.FileDetailsId == id
                                             select new InputPatientMedia
                                             {
                                                 Id = PMA.Id,
                                                 OrgId = PMA.OrgId,
                                                 PatientDetailsId = PMA.PatientDetailsId,
                                                 MediaName = PMA.MediaName,
                                                 EpisodesId = PMA.EpisodesId,
                                                 MediaTypeId = PMA.MediaTypeId,
                                                 CreatedDate = PMA.CreatedDate,
                                                 CreatedBy = PMA.CreatedBy,
                                                 ModifiedDate = PMA.ModifiedDate,
                                                 ModifiedBy = PMA.ModifiedBy,
                                                 FileDetailsId = PMA.FileDetailsId,
                                                 GroupId = PMA.GroupId,
                                                 DeleteReason = PMA.DeleteReason,
                                                 StatusId = PMA.StatusId,
                                                 MediaLibrarySubTypeId = PMA.MediaLibrarySubTypeId
                                             }).FirstOrDefaultAsync();
            return media;
        }

        /// <summary>
        /// Method to letter template based on id
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<LetterTemplateView> GetLetterTemplateBySubTypeDAL(int orgId, long subTypeId)
        {
            var letterTemplate = await (from LT in _readOnlyDbContext.LetterTemplates.Where(x => x.TemplateSubTypeID == subTypeId && x.OrgId == orgId && x.StatusId == (short)Status.Active)
                                        select new LetterTemplateView()
                                        {
                                            Id = LT.Id,
                                            OrgId = LT.OrgId,
                                            Name = LT.Name,
                                            Text = LT.Text,
                                            HeaderStyleTypeId = LT.HeaderStyleTypeId,
                                            FooterStyleTypeId = LT.FooterStyleTypeId,
                                            FileDetailsId = LT.FileDetailsId,
                                            CompanyDetailsId = LT.CompanyDetailsId,
                                            DeleteReason = LT.DeleteReason,
                                            StatusId = LT.StatusId,
                                            CreatedDate = LT.CreatedDate,
                                            CreatedBy = LT.CreatedBy,
                                            ModifiedBy = LT.ModifiedBy,
                                            ModifiedDate = LT.ModifiedDate
                                        }).FirstOrDefaultAsync();

            return letterTemplate;
        }

        /// <summary>
        /// Method to fetch a permission
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="rid"></param>
        public async Task<List<RolesPermissionsAssoc>> GetPermissionsOnRoleId(int orgId, int rid, List<short> pids)
        {
            List<RolesPermissionsAssoc> permissionsList = await  _readOnlyDbContext.RolesPermissionsAssocs.Where(x => x.RolesId == rid && x.StatusId == (short)Status.Active && x.OrgId == orgId && pids.Contains((short)x.PermissionsId)).ToListAsync();
            return permissionsList;
        }

        /// <summary>
        /// Method to fetch a permission
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="rid"></param>
        public async Task<RolesPermissionsAssoc> GetPermissionOnRoleId(int orgId, int rid, short pid)
        {
            var permissionobject = await _readOnlyDbContext.RolesPermissionsAssocs.FirstOrDefaultAsync(x => x.RolesId == rid && x.StatusId == (short)Status.Active && x.OrgId == orgId && x.PermissionsId == pid);
            return permissionobject;
        }

        public async Task UpdatePatientMediaTags(List<PatientMediaAssoc> mediaCollection, long loggedInUser, List<PatientMediaTagsAssoc> lstAddMediaTags)
        {
            // Extract the IDs from mediaCollection
            var mediaAssocIds = mediaCollection.Select(m => m.Id).ToList();

            // Find matching records in PatientMediaTagsAssocs
            var patientMediaTagsAssocs = await _readOnlyDbContext.PatientMediaTagsAssocs
                .Where(p => mediaAssocIds.Contains(p.PatientMediaAssocId))
                .ToListAsync();

            if (patientMediaTagsAssocs.Any())
            {
                foreach (var record in patientMediaTagsAssocs)
                {
                    record.StatusId = (short)Status.Deleted;
                    record.ModifiedDate = DateTime.UtcNow;
                    record.ModifiedBy = loggedInUser;
                }
                _updatableDBContext.PatientMediaTagsAssocs.UpdateRange(patientMediaTagsAssocs);
            }

            if (lstAddMediaTags.Any())
            {
                _updatableDBContext.PatientMediaTagsAssocs.AddRange(lstAddMediaTags);
            }

            await _updatableDBContext.SaveChangesAsync();
        }


    }
}

