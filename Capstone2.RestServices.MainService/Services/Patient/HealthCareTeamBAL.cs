﻿using AutoMapper;
using Capstone2.Framework.Business.Common;
using Capstone2.Framework.RestApi;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.Patient.Interfaces;
using Capstone2.RestServices.Patient.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Patient.Services
{
    public class HealthCareTeamBAL : IHealthCareTeamBAL
    {
        public readonly ICommDAL _commDAL;
        public readonly IReferralDAL _referralDAL;
        public readonly IMediaLibraryDAL _mediaLibraryDAL;
        public readonly IMediaLibraryBAL _mediaLibraryBAL;
        public readonly IHealthCareTeamDAL _healthCareTeamDAL;
        public readonly IActivityLogDAL _activityDAL;
        public readonly AppSettings _appSettings;
        private readonly ILogger<HealthCareTeamBAL> _logger;

        public IMapper _mapper;

        public HealthCareTeamBAL(
            ICommDAL commDAL,
            IReferralDAL referralDAL, 
            IHealthCareTeamDAL healthCareTeamDAL, 
            IMediaLibraryDAL mediaLibraryDAL, 
            IMediaLibraryBAL mediaLibraryBAL, 
            IActivityLogDAL activityDAL,
            IOptions<AppSettings> appSettings, IMapper mapper)
        {
            _commDAL = commDAL;
            _referralDAL = referralDAL;
            _healthCareTeamDAL = healthCareTeamDAL;
            _mediaLibraryDAL = mediaLibraryDAL;
            _mediaLibraryBAL = mediaLibraryBAL;
            _activityDAL = activityDAL;
            _appSettings = appSettings.Value;
            _mapper = mapper;

        }

        /// <summary>
        /// Method to save a entry in HealthCareTeamData
        /// </summary>
        /// <param name="HealthCareTeamPayload"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<string>> AddHealthCareData(BaseHttpRequestContext baseHttpRequestContext, long patientId, HealthCareTeamDetails inputHealthCareData, bool isOnlyRefferal)
        {
            List<ActivityLog> lstActivityLog = new();
            var orgId = baseHttpRequestContext.OrgId;
            var userId = baseHttpRequestContext.UserId;

            // HealthCareTeams
            if( isOnlyRefferal == false ) { 
                foreach (var healthCareTeam in inputHealthCareData.HealthCareTeams)
                {
                    healthCareTeam.PatientDetailsId = patientId;
                    healthCareTeam.OrgId = orgId;
                    healthCareTeam.CreatedDate = DateTime.UtcNow;
                    healthCareTeam.StatusId = (short)Status.Active;
                }
                DateTime.SpecifyKind(DateTime.UtcNow, DateTimeKind.Utc);
                await _healthCareTeamDAL.AddHealthCareTeamDataAsync(inputHealthCareData.HealthCareTeams);
            }

            // ReferralDetails
            foreach (var referralDetail in inputHealthCareData.ReferralDetails)
            {
                referralDetail.PatientDetailsId = patientId;
                referralDetail.OrgId = orgId;
                referralDetail.CreatedDate = DateTime.UtcNow;
                referralDetail.StatusId = (short)Status.Active;
                referralDetail.IsDefault = (referralDetail.IsDefault is null) ? false : referralDetail.IsDefault;
            }
            DateTime.SpecifyKind(DateTime.UtcNow, DateTimeKind.Utc);
            await _referralDAL.AddReferrals(inputHealthCareData.ReferralDetails);

            // Add Referral Files
            List<PatientMediaAssoc> mediaLst = new();
            foreach (var referral in inputHealthCareData.ReferralDetails)
            {
                if (referral.FileDetailsId != null)
                {
                    var fileDetails = await _commDAL.GetFileDetails(referral.OrgId, (long)referral.FileDetailsId);
                    PatientMediaAssoc patientMedia = AddToMediaLibrary(fileDetails, patientId, orgId, userId);
                    mediaLst.Add(patientMedia);
                    ActivityLog activityLog = CreateActivityLogEntry((int)ActivityLogOps.File_Upload, patientId, null, fileDetails, orgId, userId);
                    lstActivityLog.Add(activityLog);
                }
            }
            if (mediaLst != null && mediaLst.Count > 0)
            {
                InputPatientMediaAssoc mediaColl = new();
                mediaColl.PatientMediaAssocs = mediaLst;
                await _mediaLibraryDAL.AddMediaCollection(mediaColl);
            }
            if (lstActivityLog is not null && lstActivityLog.Count > 0)
            {
                await _activityDAL.AddActivtyLogRange(lstActivityLog);
            }

            // Adding to Referral Source

            if (isOnlyRefferal == false)
            {
                await InsertReferralDetails(baseHttpRequestContext, patientId, inputHealthCareData.ReferralDetailsPatient);
            }

            return new ApiResponse<string>
            {
                StatusCode = StatusCodes.Status200OK,
                Message = "Success",
                Result = "Success"
            };
        }

        private PatientMediaAssoc AddToMediaLibrary(FileDetails fileDetails, long patientid, int orgId, long userId)
        {
            PatientMediaAssoc media = new();
            media.OrgId = orgId;
            media.PatientDetailsId = patientid;
            media.CreatedBy = userId;
            media.FileDetailsId = fileDetails.Id;
            media.MediaName = (string.IsNullOrWhiteSpace(fileDetails.CustomFileName)) ? fileDetails.FileName : fileDetails.CustomFileName;
            media.MediaTypeId = (short)MediaType.PDF;
            media.StatusId = (short)Status.Active;
            media.GroupId = Guid.NewGuid();
            media.MediaLibrarySubTypeId = (short)MediaLibrarySubType.Refferal_Letter;
            return media;
        }

        private ActivityLog CreateActivityLogEntry(int activityLogOp, long? patientDetailsId, PatientMediaAssoc media, FileDetails fileDetails, int orgId, long userId)
        {
            ActivityLog activityLog = new();
            activityLog.ActivityLogTypeId = (short)ActivityLogType.Media_Library;
            activityLog.PatientDetailsId = (long)patientDetailsId;
            activityLog.EntityId = (media is not null && media.Id > 0) ? media.Id : null;
            activityLog.OrgId = orgId;
            activityLog.CreatedDate = DateTime.UtcNow;
            activityLog.ModifiedDate = DateTime.UtcNow;
            activityLog.ModifiedBy = userId;
            if ((int)ActivityLogOps.File_Upload == activityLogOp)
            {
                activityLog.Title = EnumExtensions.GetDescription(MediaLibrarySubType.Refferal_Letter);
                activityLog.FileDetailsId = fileDetails.Id;
                string fileName = (fileDetails.CustomFileName != null) ? fileDetails.CustomFileName : fileDetails.FileName;
                string operationName = "File Uploaded.";
                activityLog.Description = operationName + " File Name:" + fileName;
            }
            if ((int)ActivityLogOps.File_Delete == activityLogOp)
            {
                activityLog.Title = EnumExtensions.GetDescription(MediaLibrarySubType.Refferal_Letter);
                activityLog.FileDetailsId = null;
                string fileName = (fileDetails.CustomFileName != null) ? fileDetails.CustomFileName : fileDetails.FileName;
                string operationName = "File Deleted.";
                activityLog.Description = operationName + " File Name:" + fileName;
            }
            return activityLog;
        }

        /// <summary>
        /// Get HealthCareTeamDataby ID
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<InputHealthCareTeamDetails>> GetHealthCareData(BaseHttpRequestContext baseHttpRequestContext, long patientId, bool isOnlyRefferal)
        {
            InputHealthCareTeamDetails apiResposne = new();
            var orgId = baseHttpRequestContext.OrgId;

            if (isOnlyRefferal == false)
            {
                // Healthcare Team
                apiResposne.InputHealthCareTeams = await FormatHealthCareTeamDetails(orgId, patientId);
            }

            // Referal List
            apiResposne = await FormatReferralDetails(apiResposne, orgId, patientId, baseHttpRequestContext);

            return new ApiResponse<InputHealthCareTeamDetails>
            {
                StatusCode = StatusCodes.Status200OK,
                Message = "Success",
                Result = apiResposne
            };
        }

        private async Task<List<InputHealthCareTeam>> FormatHealthCareTeamDetails(int orgId, long patientId)
        {
            var inputHealthCareTeams = await _healthCareTeamDAL.FetchHealthCareTeamDetails(orgId, patientId);
            return inputHealthCareTeams != null ? inputHealthCareTeams.ToList() : null;
        }

        private async Task<InputHealthCareTeamDetails> FormatReferralDetails(InputHealthCareTeamDetails inputPatientDetail, int orgId, long patientId, BaseHttpRequestContext baseHttpRequestContext)
        {
            List<InputReferralDetail> lstReferrals = await _referralDAL.FetchReferrals(orgId, patientId);
            OrganisationView orgDetails = await FetchMasterCompanyDetails(baseHttpRequestContext);
            short timeZoneId = (orgDetails is null || orgDetails.TimeZoneMasterId <= 0) ? default(short) : orgDetails.TimeZoneMasterId;
            DateTime? currDateTime = new();
            if (timeZoneId > 0)
            {
                currDateTime = GetDateFromOrganisation(orgDetails);

            }
            foreach (var referral in lstReferrals)
            {
                if (referral.FileDetailsId != null)
                {
                    long fileId = (long)referral.FileDetailsId;
                    var token = baseHttpRequestContext.BearerToken;
                    string interServiceToken = baseHttpRequestContext.InterServiceToken;
                    string fileAPiUrl = _appSettings.ApiUrls["FileServiceUrl"] + "/file/file_details/" + fileId;
                    RestClient restClient = new RestClient(fileAPiUrl, null, token, interServiceToken);
                    var fileApiResponse = await restClient.GetAsync<ApiResponse<FileDetailsOutputForId>>(fileAPiUrl, null);
                    if (fileApiResponse.StatusCode == StatusCodes.Status200OK)
                    {
                        referral.FileDetailsOutput = fileApiResponse.Result;
                    }
                }

                if (referral.ReferringProviderInfo is not null)
                    referral.ReferringProviderInfo.ProviderType = (referral.ReferringProviderInfo.ProviderTypeId == null) ? null : EnumExtensions.GetDescription((ProviderType)(referral.ReferringProviderInfo.ProviderTypeId));
                referral.ReferralType = (referral.ReferralTypeId == null || referral.ReferralTypeId <= 0) ? null : EnumExtensions.GetDescription((ReferralType)(referral.ReferralTypeId));
            }
            if (lstReferrals.Any())
            {
               
                List<InputReferralDetail> active = lstReferrals.Where(x => x.ExpiryDate is null || x.ExpiryDate >= currDateTime.GetValueOrDefault().Date).ToList();
                List<InputReferralDetail> inActive = lstReferrals.Where(x => x.ExpiryDate < currDateTime.GetValueOrDefault().Date).ToList();
                inputPatientDetail.ActiveReferralDetails = active;
                inputPatientDetail.InActiveReferralDetails = inActive;
                if(inputPatientDetail.InActiveReferralDetails is not null && inputPatientDetail.InActiveReferralDetails.Count > 0)
                {
                    inputPatientDetail.InActiveReferralDetails.ForEach(x => x.IsDefault = false);
                }
            }

            return inputPatientDetail;
        }
        private DateTime? GetDateFromOrganisation(OrganisationView orgDetails)
        {
            bool isWindows = System.Runtime.InteropServices.RuntimeInformation
                                                 .IsOSPlatform(OSPlatform.Windows);
            string timeZone = (isWindows) ? orgDetails.WindowsTimeZoneData : orgDetails.LinuxTimeZoneData;

            var CompanyDateTimeNow = DateTimeConversion.ConvertTimeFromUtc(DateTime.UtcNow, timeZone);
            //if (CompanyDateTimeNow is null)
            //{
            //    CompanyDateTimeNow = DateTimeConversion.ConvertTimeFromUtc(DateTime.UtcNow, orgDetails.LinuxTimeZoneData);
            //}

            return CompanyDateTimeNow;
        }
        private async Task<OrganisationView> FetchMasterCompanyDetails(BaseHttpRequestContext baseHttpRequestContext)
        {

            ApiResponse<OrganisationView> apiResponseCompany = new();
            string endpoint = string.Format("/company/Organisation/{0}", baseHttpRequestContext.OrgId);
            string companySeviceUrl = _appSettings.ApiUrls["CompanyServiceUrl"] + endpoint;
            RestClient restClientCompany = new RestClient(companySeviceUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            apiResponseCompany = await restClientCompany.GetAsync<ApiResponse<OrganisationView>>(companySeviceUrl);
            return (apiResponseCompany == null || apiResponseCompany.Result == null) ? null : apiResponseCompany.Result;

        }
        /// <summary>
        /// Edit Appointment Type
        /// </summary>
        /// <param name="id"></param>
        /// <param name="inputHealthCareTeam"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<string>> EditHealthCareData(BaseHttpRequestContext baseHttpRequestContext, long patientId, HealthCareTeamDetails inputHealthCareData, bool isOnlyRefferal)
        {
            var orgId = baseHttpRequestContext.OrgId;
            var userId = baseHttpRequestContext.UserId;

            if (isOnlyRefferal == false)
            {
                // Healthcare Team
                var existingHealthCareData = await _healthCareTeamDAL.GetHealthCareTeams(orgId, patientId);
                var healthCareTeamLst = EditHealthCareTeam(existingHealthCareData, inputHealthCareData.HealthCareTeams, orgId, userId);
                await _healthCareTeamDAL.UpdateHealthCareTeamData(healthCareTeamLst);
            }

            // Referals
            var removeReferralList = await _referralDAL.GetReferrals(orgId, patientId);
            var referralList = await EditReferrals(removeReferralList, inputHealthCareData.ReferralDetails, orgId, userId, patientId);
            await _referralDAL.UpdateReferrals(referralList);

            return new ApiResponse<string>
            {
                StatusCode = StatusCodes.Status200OK,
                Message = "Success",
                Result = "Success"
            };
        }

        private List<HealthCareTeam> EditHealthCareTeam(ICollection<HealthCareTeam> removeHealthCareTeamList, ICollection<HealthCareTeam> inputHealthCareTeamList, int orgId, long userId)
        {
            List<HealthCareTeam> addHCTeam = new();
            if (inputHealthCareTeamList == null)
            {
                return addHCTeam;
            }
            foreach (var healthCareTeam in inputHealthCareTeamList)
            {
                if (healthCareTeam.Id > 0)
                {
                    var existingobj = removeHealthCareTeamList.FirstOrDefault(x => x.Id == healthCareTeam.Id);
                    var result = removeHealthCareTeamList.Remove(existingobj);
                }
                else
                {
                    healthCareTeam.OrgId = orgId;
                    healthCareTeam.StatusId = (short)Status.Active;
                    addHCTeam.Add(healthCareTeam);
                }
            }

            foreach (var healthCareTeam in removeHealthCareTeamList)
            {
                healthCareTeam.StatusId = (short)Status.Deleted;
                healthCareTeam.ModifiedDate = DateTime.UtcNow;
                healthCareTeam.ModifiedBy = userId;
            }
            addHCTeam = addHCTeam.Concat(removeHealthCareTeamList).ToList();
            return addHCTeam;
        }

        /// <summary>
        /// Method to update/delete the referral details collection
        /// </summary>
        /// <param name="removeReferralList"></param>
        /// <param name="inputReferralList"></param>
        /// <returns></returns>
        private async Task<List<ReferralDetail>> EditReferrals(ICollection<ReferralDetail> removeReferralList, ICollection<ReferralDetail> inputReferralList, int orgId, long userId, long patientId)
        {
            List<ReferralDetail> addRefList = new();
            List<PatientMediaAssoc> mediaListAdd = new();
            List<ActivityLog> lstActivtyLog = new();
            List<long?> fileListDel = new();
            ActivityLog activityLog;

            if (inputReferralList == null)
            {
                return addRefList;
            }
            foreach (ReferralDetail referral in inputReferralList)
            {
                if (referral.Id > 0)
                {
                    var existingobj = removeReferralList.FirstOrDefault(x => x.Id == referral.Id);

                    if (existingobj.FileDetailsId == null && referral.FileDetailsId != null)
                    {
                        var fileDetailsAdd = await _commDAL.GetFileDetails(referral.OrgId, (long)referral.FileDetailsId);
                        PatientMediaAssoc patientMedia = AddToMediaLibrary(fileDetailsAdd, referral.PatientDetailsId, orgId, userId);
                        mediaListAdd.Add(patientMedia);
                        //await AddCommNotesForRefferals(fileDetailsAdd, referral.PatientDetailsId, (short)ActivityLogOps.File_Upload, userId);
                        activityLog = CreateActivityLogEntry((short)ActivityLogOps.File_Upload, patientId, patientMedia, fileDetailsAdd, orgId, userId);
                        lstActivtyLog.Add(activityLog);
                    }
                    else if (existingobj.FileDetailsId != null && referral.FileDetailsId == null)
                    {
                        var fileDetailsremove = await _commDAL.GetFileDetails(existingobj.OrgId, (long)existingobj.FileDetailsId);
                        fileListDel.Add(existingobj.FileDetailsId);
                        // _mediaLibraryBAL.DeleteMediaFromFileId(orgId, userId, referral.PatientDetailsId,(long)existingobj.FileDetailsId);
                        //await AddCommNotesForRefferals(fileDetailsremove, existingobj.PatientDetailsId, (short)ActivityLogOps.File_Delete, userId);
                        activityLog = CreateActivityLogEntry((short)ActivityLogOps.File_Delete, patientId, null, fileDetailsremove, orgId, userId);
                        lstActivtyLog.Add(activityLog);
                    }
                    else if (existingobj.FileDetailsId != referral.FileDetailsId)
                    {
                        var fileDetailsremove = await _commDAL.GetFileDetails(existingobj.OrgId, (long)existingobj.FileDetailsId);
                        //await AddCommNotesForRefferals(fileDetailsremove, existingobj.PatientDetailsId, (short)ActivityLogOps.File_Delete, userId);
                        //_mediaLibraryBAL.DeleteMediaFromFileId(orgId, userId, referral.PatientDetailsId, (long)existingobj.FileDetailsId);
                        fileListDel.Add(existingobj.FileDetailsId);
                        activityLog = CreateActivityLogEntry((short)ActivityLogOps.File_Delete, patientId, null, fileDetailsremove, orgId, userId);
                        lstActivtyLog.Add(activityLog);
                        var fileDetailsAdd = await _commDAL.GetFileDetails(referral.OrgId, (long)referral.FileDetailsId);
                        //await AddCommNotesForRefferals(fileDetailsAdd, referral.PatientDetailsId, (short)ActivityLogOps.File_Upload, userId);
                        PatientMediaAssoc patientMedia = AddToMediaLibrary(fileDetailsAdd, referral.PatientDetailsId, orgId, userId);
                        activityLog = CreateActivityLogEntry((short)ActivityLogOps.File_Upload, patientId, patientMedia, fileDetailsAdd, orgId, userId);
                        lstActivtyLog.Add(activityLog);

                        mediaListAdd.Add(patientMedia);
                    }

                    var result = removeReferralList.Remove(existingobj);
                }
                else
                {
                    referral.CreatedDate = DateTime.UtcNow;
                    if (referral.FileDetailsId != null)
                    {
                        var fileDetails = await _commDAL.GetFileDetails(referral.OrgId, (long)referral.FileDetailsId);
                        PatientMediaAssoc patientMedia = AddToMediaLibrary(fileDetails, referral.PatientDetailsId, orgId, userId);
                        mediaListAdd.Add(patientMedia);
                        activityLog = CreateActivityLogEntry((short)ActivityLogOps.File_Upload, patientId, patientMedia, fileDetails, orgId, userId);
                        lstActivtyLog.Add(activityLog);
                    }
                }
                referral.OrgId = orgId;
                referral.StatusId = (short)Status.Active;
                referral.ModifiedDate = DateTime.UtcNow;
                referral.IsDefault = (referral.IsDefault is null) ? false : referral.IsDefault;
                addRefList.Add(referral);
            }
            if (removeReferralList != null && removeReferralList.Count > 0)
            {
                removeReferralList.ToList().ForEach(x => { x.StatusId = (short)Status.Deleted; x.ModifiedDate = DateTime.UtcNow; x.IsDefault = false; });
                addRefList = addRefList.Concat(removeReferralList).ToList();

                foreach (var item in removeReferralList)
                {
                    if (item.FileDetailsId is not null)
                    {
                        var fileDetails = await _commDAL.GetFileDetails(item.OrgId, (long)item.FileDetailsId);
                        //await AddCommNotesForRefferals(fileDetails, item.PatientDetailsId, (short)ActivityLogOps.File_Delete, userId);
                        activityLog = CreateActivityLogEntry((short)ActivityLogOps.File_Delete, patientId, null, fileDetails, orgId, userId);
                        lstActivtyLog.Add(activityLog);
                        fileListDel.Add(item.FileDetailsId);
                    }
                }
            }
            if (mediaListAdd != null && mediaListAdd.Count > 0)
            {
                InputPatientMediaAssoc mediaColl = new();
                mediaColl.PatientMediaAssocs = mediaListAdd;
                await _mediaLibraryDAL.AddMediasToMediaLibrary(mediaColl);
            }
            if (fileListDel != null && fileListDel.Count > 0)
            {
                foreach (var item in fileListDel)
                {
                    await _mediaLibraryBAL.DeleteMediaFromFileId(orgId, userId, patientId, (long)item);
                }
            }
            if (lstActivtyLog is not null && lstActivtyLog.Count > 0)
            {
                await _activityDAL.AddActivtyLogRange(lstActivtyLog);
            }
            return addRefList;
        }
        /// <summary>
        /// Method fetch referraldetails based on id
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="patient_id"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async  Task<ApiResponse<List<ReferralDetailInfo>>> GetReferralDetails(BaseHttpRequestContext baseHttpRequestContext, QueryModel queryModel)
        {
            var orgId = baseHttpRequestContext.OrgId;
            List<ReferralDetailInfo> lstReferrals = null; 
            ReferralFilterModel filterModel = PrepareFilterParameters(queryModel.Filter);
            if(filterModel is not null && filterModel.ReferralDetailsId.Count > 0)
            {
                
                List<InputReferralDetail> listReferralDB = await _referralDAL.FetchReferrals(orgId, null, 2,filterModel.ReferralDetailsId);
                if (listReferralDB is not null && listReferralDB.Count > 0)
                {
                    // InputReferralDetail referralDB = listReferralDB.FirstOrDefault();
                    lstReferrals = _mapper.Map<List<InputReferralDetail>, List<ReferralDetailInfo>>(listReferralDB);
                }

            }
          
            return new ApiResponse<List<ReferralDetailInfo>>
            {
                StatusCode = StatusCodes.Status200OK,
                Message = "Success",
                Result = lstReferrals
            };
        }
        private ReferralFilterModel PrepareFilterParameters(string filter)
        {
            if (!(String.IsNullOrEmpty(filter)))
            {
                return JsonConvert.DeserializeObject<ReferralFilterModel>(filter);
            }
            else
            {
                return null;
            }
        }

        public async Task<ApiResponse<List<ReferralDetailsPatient>>> GetReferralDetailsPatient(int orgId, long patientId)
        {
            var response = new ApiResponse<List<ReferralDetailsPatient>>();
            var referrals = await _healthCareTeamDAL.GetReferralDetailsPatient(orgId,patientId);
            response.StatusCode = StatusCodes.Status200OK;
            response.Result = _mapper.Map<List<ReferralDetailsPatient>>(referrals);
            response.Message = "Success";
            return response;
        }

        public async Task<ApiResponse<string>> InsertReferralDetails(BaseHttpRequestContext baseHttpRequestContext, long patientId, List<ReferralDetailsPatient> referralDetailsPatient)
        {
            try
            {
                foreach (var item in referralDetailsPatient) { 
                    item.OrgID = baseHttpRequestContext.OrgId;
                    item.PatientDetailsId = patientId;
                    item.CreatedDate = DateTime.UtcNow;
                }
                var insertedReferral = await _healthCareTeamDAL.InsertReferralDetails(patientId, referralDetailsPatient);
                if(insertedReferral > 0)
                {
                    return new ApiResponse<string>
                    {
                        StatusCode = StatusCodes.Status200OK,
                        Message = "Added Successfully",
                        Result = "Success"
                    };
                }
                else
                {
                    return new ApiResponse<string>
                    {
                        StatusCode = StatusCodes.Status400BadRequest,
                        Message = "Failure",
                        Result = "Failure"
                    };
                }
            }
            catch (Exception ex) {
                return new ApiResponse<string>
                {
                    StatusCode = StatusCodes.Status400BadRequest,
                    Message = ex.Message,
                    Result = "Failure"
                };
            }
            
        }

        public async Task<ApiResponse<string>> EditReferralDetails(BaseHttpRequestContext baseHttpRequestContext, long patientId, List<ReferralDetailsPatient> referralDetailsPatient)
        {
            try
            {
                foreach (var item in referralDetailsPatient) { item.OrgID = baseHttpRequestContext.OrgId; }
                //referralDetailsPatient.OrgID = baseHttpRequestContext.OrgId;
                var insertedReferral = await _healthCareTeamDAL.EditReferralDetails(patientId, referralDetailsPatient);
                if (insertedReferral > 0)
                {
                    return new ApiResponse<string>
                    {
                        StatusCode = StatusCodes.Status200OK,
                        Message = "Added Successfully",
                        Result = "Success"
                    };
                }
                else
                {
                    return new ApiResponse<string>
                    {
                        StatusCode = StatusCodes.Status400BadRequest,
                        Message = "Failure",
                        Result = "Failure"
                    };
                }
            }
            catch (Exception ex)
            {
                return new ApiResponse<string>
                {
                    StatusCode = StatusCodes.Status400BadRequest,
                    Message = ex.Message,
                    Result = "Failure"
                };
            }
        }

        public async Task<ApiResponse<List<ReferralSourceModel>>> GetAllReferralSource(int orgId)
        {
            var response = new ApiResponse<List<ReferralSourceModel>>();
            var referrals = await _healthCareTeamDAL.GetAllReferralSource(orgId);
            response.StatusCode = StatusCodes.Status200OK;
            response.Result = _mapper.Map<List<ReferralSourceModel>>(referrals);
            response.Message = "Success";
            return response;
        }

        public async Task<ApiResponse<List<ReferralSourceDetails>>> GetAllReferralSourceSubType(int orgId)
        {
            var response = new ApiResponse<List<ReferralSourceDetails>>();
            var referrals = await _healthCareTeamDAL.GetAllReferralSourceSubType(orgId);
            response.StatusCode = StatusCodes.Status200OK;
            response.Result = _mapper.Map<List<ReferralSourceDetails>>(referrals);
            response.Message = "Success";
            return response;
        }

    }
}
