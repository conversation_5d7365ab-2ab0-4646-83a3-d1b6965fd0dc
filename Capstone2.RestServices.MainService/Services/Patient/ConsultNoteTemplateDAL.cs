﻿using Capstone2.RestServices.Patient.Context;
using Capstone2.RestServices.Patient.Interfaces;
using Capstone2.RestServices.Patient.Models;
//using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Patient.Services
{
    public class ConsultNoteTemplateDAL : IConsultNoteTemplateDAL
    {
        public readonly ReadOnlyPatientDBContext _readOnlyDbContext;
        public readonly UpdatablePatientDBContext _updatableDBContext;
        public ConsultNoteTemplateDAL(ReadOnlyPatientDBContext readOnlyDbContext, UpdatablePatientDBContext updatableDBContext)
        {
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
        }

        /// <summary>
        /// Method to add ConsultNoteTemplateData to ConsultNoteTemplateData table
        /// </summary>
        /// <param name="ConsultNoteTemplateData"></param>
        /// <returns></returns>
        public async Task<long> AddConsultNoteTemplateDataAsync(ConsultNotes consultNoteTemplateData)
        {
            await _updatableDBContext.ConsultNotes.AddAsync(consultNoteTemplateData);
            int rows = await _updatableDBContext.SaveChangesAsync();
            return rows > 0 ? consultNoteTemplateData.Id : 0;
        }

        /// <summary>
        /// Method to ConsultNoteTemplateIds based on Communication id
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ConsultNoteTemplateIds> GetConsultNoteTemplateIds(int orgId, long communicationId)
        {
            var consultNoteTemplateData = await (from CN in _readOnlyDbContext.ConsultNotes.Where(x => x.CommunicationNotesId == communicationId && x.OrgId == orgId)
                                                 select new ConsultNoteTemplateIds
                                                 {
                                                     ConsultNoteId = CN.Id,
                                                     TemplateId = CN.ConsultNoteTemplateId
                                                 }).FirstOrDefaultAsync();
            return consultNoteTemplateData;
        }

        /// <summary>
        /// Method to ConsultNoteTemplateData based on id
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ConsultNotes> GetConsultNoteTemplateData(int orgId, long id)
        {
            var consultNoteTemplateData = await (from CN in _readOnlyDbContext.ConsultNotes.Where(x => x.Id == id && x.OrgId == orgId)
                                                 select new ConsultNotes
                                                 {
                                                     Id = CN.Id,
                                                     OrgId = CN.OrgId,
                                                     PatientDetailsId = CN.PatientDetailsId,
                                                     EpisodesId = CN.EpisodesId,
                                                     CommunicationNotesId = CN.CommunicationNotesId,
                                                     ConsultNoteTemplateId = CN.ConsultNoteTemplateId,
                                                     StatusId = CN.StatusId,
                                                     CreatedBy = CN.CreatedBy,
                                                     CreatedDate = CN.CreatedDate,
                                                     ModifiedBy = CN.ModifiedBy,
                                                     ModifiedDate = CN.ModifiedDate,
                                                     ConsultNotesControls = (List<ConsultNotesControls>)(ICollection<ConsultNotesControls>)(
                                                        from CT in _readOnlyDbContext.ConsultNotesControls.Include(x=>x.ConsultNoteMediaAssocs.Where(x=>x.StatusId==(short)Status.Active))
                                                        where CT.OrgId == orgId && CT.ConsultNotesId == id
                                                        select new ConsultNotesControls
                                                        {
                                                            Id = CT.Id,
                                                            OrgId = CT.OrgId,
                                                            ConsultNotesId = CT.ConsultNotesId,
                                                            ConsultNoteTemplateControlsId = CT.ConsultNoteTemplateControlsId,
                                                            Value = CT.Value,
                                                            StatusId = CT.StatusId,
                                                            CreatedBy = CT.CreatedBy,
                                                            CreatedDate = CT.CreatedDate,
                                                            ModifiedBy = CT.ModifiedBy,
                                                            ModifiedDate = CT.ModifiedDate,
                                                            ConsultNoteMediaAssocs = (CT==null ||CT.ConsultNoteMediaAssocs==null || CT.ConsultNoteMediaAssocs.Count==0)?null: (ICollection<ConsultNoteMediaAssoc>)CT.ConsultNoteMediaAssocs.Where(x=>x.StatusId==(short)Status.Active)
                                                        })
                                                 }).FirstOrDefaultAsync();
            return consultNoteTemplateData;
        }

        /// <summary>
        /// Method to ConsultNoteTemplate based on id
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ConsultNotes> GetConsultNoteTemplateDataSync(int orgId, long id)
        {
            return await _readOnlyDbContext.ConsultNotes.Where(s => s.Id == id && s.OrgId == orgId).AsNoTracking().FirstOrDefaultAsync();
        }

        /// <summary>
        /// GetConsultNoteTemplateControls Sync
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="consultNoteTemplateId"></param>
        /// <returns></returns>
        public async Task<List<ConsultNotesControls>> GetConsultNotesControlsSync(int orgId, long consultNoteTemplateId)
        {
            return await _readOnlyDbContext.ConsultNotesControls.Where(s => s.ConsultNotesId == consultNoteTemplateId && s.OrgId == orgId).Include(x=>x.ConsultNoteMediaAssocs.Where(x=>x.StatusId==(short)Status.Active)).AsNoTracking().ToListAsync();
        }

        /// <summary>
        /// Update ConsultNoteTemplate
        /// </summary>
        /// <param name="ConsultNoteTemplate"></param>
        /// <returns></returns>
        public async Task<int> UpdateConsultNoteTemplateData(ConsultNotes consultNoteTemplateData)
        {
            _updatableDBContext.ConsultNotes.Update(consultNoteTemplateData);
            return await _updatableDBContext.SaveChangesAsync();
        }
    }
}
