﻿using AutoMapper;
using Capstone2.Framework.Business.Common;
using Capstone2.Framework.RestApi;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.Framework.RestApi.Utility;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.Patient.Interfaces;
using Capstone2.RestServices.Patient.Models;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Entities.Hub;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Transactions;

namespace Capstone2.RestServices.Patient.Services
{
    public class LetterBAL : ILetterBAL
    {
        public readonly AppSettings _appSettings;
        public readonly ILetterDAL _letterDAL;
        public readonly ICommDAL _commDAL;
        public readonly IPatientDAL _patientDAL;
        public readonly IActivityLogDAL _activityLogDAL;
        private readonly ILogger<LetterBAL> _logger;
        private IASBMessageSenderHelper _asbMessageSenderHelper;
        private readonly IConfiguration _configuration;
        public readonly IMediaLibraryDAL _mediaLibraryDAL;
        public readonly ISmsBAL _smsBAL;
        public IMapper _mapper;

        public LetterBAL(IOptions<AppSettings> appSettings, ILetterDAL letterDAL, ICommDAL commDAL, IMediaLibraryDAL mediaLibraryDAL, IMapper mapper, IActivityLogDAL activityLogDAL, IPatientDAL patientDAL, ILogger<LetterBAL> logger, IConfiguration configuration, IASBMessageSenderHelper asbMessageSenderHelper, ISmsBAL smsBAL)
        {
            _appSettings = appSettings.Value;
            _letterDAL = letterDAL;
            _commDAL = commDAL;
            _mediaLibraryDAL = mediaLibraryDAL;
            _patientDAL = patientDAL;
            _activityLogDAL = activityLogDAL;
            _logger = logger;
            this._asbMessageSenderHelper = asbMessageSenderHelper;
            this._configuration = configuration;
            _mapper = mapper;
            _smsBAL = smsBAL;
        }

        /// <summary>
        /// Method to Add Letter
        /// </summary>
        /// <param name="patient_id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> AddLetterBAL(long patient_id, Letter inputLetter, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<long?> apiResponse = new();
            using (var transaction = new TransactionScope(TransactionScopeOption.Required, TransactionScopeAsyncFlowOption.Enabled))
            {
                var patientData = await _patientDAL.GetPatientDetails(baseHttpRequestContext.OrgId, patient_id);
                var orgDetails = await _letterDAL.GetOrganistaionDAL(baseHttpRequestContext.OrgId);
                var Date = GetDateFromOrganisation(orgDetails);
                inputLetter.Name = patientData.RecordId.ToString() + "_" + inputLetter.Name + "_" + Date;
                inputLetter.OrgId = baseHttpRequestContext.OrgId;
                inputLetter.CreatedBy = baseHttpRequestContext.UserId;
                inputLetter.PatientDetailsId = patient_id;
                inputLetter.StatusId = (short)Status.Active;
                inputLetter.CreatedDate = DateTime.UtcNow;
                var numberOfVersions = 0;
                if (inputLetter.VersionNo != 1)
                {
                    var listOfLetters = await _letterDAL.GetLetterAllVersionDAL(inputLetter.LetterId, inputLetter.PatientDetailsId);
                    numberOfVersions = listOfLetters.Count;
                    inputLetter.VersionNo = (short?)(numberOfVersions + 1);
                    inputLetter.Name = listOfLetters[0].Name + "_V" + inputLetter.VersionNo;
                }

                if (inputLetter.LetterStatusId == (short)LetterStatus.Draft && inputLetter.IsCopyLetter && inputLetter.LetterId.HasValue)
                {
                    Letter letterFromDb = await _letterDAL.GetLetterDAL(patient_id, baseHttpRequestContext.OrgId, inputLetter.LetterId.Value);
                    if (letterFromDb is not null)
                    {
                        inputLetter.SFDTFileDetailsId = letterFromDb.SFDTFileDetailsId;
                        inputLetter.FileDetailsId = letterFromDb.SFDTFileDetailsId;
                        inputLetter.AssignedTo = letterFromDb.AssignedTo;
                        inputLetter.AddressorEntityId = letterFromDb.AddressorEntityId;
                        inputLetter.AddresseeTypeId = letterFromDb.AddresseeTypeId;
                        inputLetter.AddresseeEntityId = letterFromDb.AddresseeEntityId;
                        inputLetter.AppointmentDetailsId = letterFromDb.AppointmentDetailsId;
                    }
                }

                long letter_id = await _letterDAL.AddLetterDAL(inputLetter);

                if (inputLetter.LetterStatusId == (short)LetterStatus.Approved)
                {
                    if (inputLetter.FileDetailsId.HasValue)
                    {
                        //downoloadFile
                        var token = baseHttpRequestContext.BearerToken;
                        string interServiceToken = baseHttpRequestContext.InterServiceToken;
                        string DownloadfileAPiUrl = _appSettings.ApiUrls["FileServiceUrl"] + "/file/filedownload?Id=" + inputLetter.FileDetailsId;
                        RestClient downlodRestClient = new RestClient(DownloadfileAPiUrl, null, token, interServiceToken);
                        var downloadedfile = await downlodRestClient.GetAsync<ApiResponse<string>>(DownloadfileAPiUrl);

                        if (downloadedfile.StatusCode != StatusCodes.Status200OK)
                        {
                            apiResponse.StatusCode = downloadedfile.StatusCode;
                            apiResponse.Errors = downloadedfile.Errors;
                            apiResponse.Message = downloadedfile.Message;
                            return apiResponse;
                        }

                        //ConvertContentToPDF
                        string syncFusionServiceURL = _appSettings.ApiUrls["SyncFusionServiceUrl"] + "/syncfusion/ConvertDocToPDF";
                        RestClient syncFusionClient = new RestClient(syncFusionServiceURL, null, token, interServiceToken);
                        var fileUploadResponse = await syncFusionClient.PostAsync<ApiResponse<FileUploadObject>>(syncFusionServiceURL, downloadedfile.Result);

                        if (fileUploadResponse.StatusCode != StatusCodes.Status200OK)
                        {
                            apiResponse.StatusCode = fileUploadResponse.StatusCode;
                            apiResponse.Errors = fileUploadResponse.Errors;
                            apiResponse.Message = fileUploadResponse.Message;
                            return apiResponse;
                        }

                        //File Upload Started
                        var fileUpload = fileUploadResponse.Result;
                        fileUpload.CustomFileName = inputLetter.Name + ".pdf";
                        string fileAPiUrl = _appSettings.ApiUrls["FileServiceUrl"] + "/file/fileupload";
                        RestClient restClient = new RestClient(fileAPiUrl, null, token, interServiceToken);
                        var fileApiResponse = await restClient.PostAsync<ApiResponse<long>>(fileAPiUrl, fileUpload);

                        if (fileApiResponse.StatusCode == StatusCodes.Status200OK)
                        {
                            inputLetter.FileDetailsId = fileApiResponse.Result;
                            PatientMediaAssoc media = new();
                            media.OrgId = baseHttpRequestContext.OrgId;
                            media.PatientDetailsId = patient_id;
                            media.StatusId = (short)Status.Active;
                            media.CreatedBy = baseHttpRequestContext.UserId;
                            media.FileDetailsId = fileApiResponse.Result;
                            media.MediaName = fileUpload.CustomFileName;
                            media.MediaLibrarySubTypeId = (short)MediaLibrarySubType.Letter;
                            media.MediaTypeId = (short)MediaType.PDF;
                            media.LetterId = letter_id;
                            Guid g = Guid.NewGuid();
                            media.GroupId = g;
                            long? mediaId = await _mediaLibraryDAL.AddMedia(media);
                        }
                        else
                        {
                            apiResponse.StatusCode = fileApiResponse.StatusCode;
                            apiResponse.Errors = fileApiResponse.Errors;
                            apiResponse.Message = fileApiResponse.Message;
                            return apiResponse;
                        }
                    }
                }

                ActivityLog parentEntry = CreateActivityLogEntry((int)ActivityLogOps.Parent_Letter_Add, patient_id, inputLetter, baseHttpRequestContext);
                ActivityLogChildEntry childEntry = await CreateActivityLogChildEntry((short)ActivityLogOps.Letter_Add, patient_id, inputLetter, baseHttpRequestContext);
                if (inputLetter.FileDetailsId is not null)
                {
                    parentEntry.FileDetailsId = inputLetter.FileDetailsId;
                    childEntry.FileDetailsId = inputLetter.FileDetailsId;
                }
                if (letter_id > 0)
                {
                    if (parentEntry is not null)
                    {
                        parentEntry.ActivityLogChildEntries.Add(childEntry);
                    }
                    await _activityLogDAL.AddActivityLog(parentEntry);
                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    apiResponse.Message = "Success";
                    apiResponse.Result = letter_id;
                    transaction.Complete();
                    return apiResponse;
                }

                transaction.Dispose();

            }
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            apiResponse.Errors.Add("Letter cannot be created at this time.");

            return apiResponse;
        }

        public async Task<ApiResponse<string>> EditLetterBAL(long patient_id, long id, EmailLetter emailLetter, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<string> apiResponse = new();
            ApiResponse<long> pdfFileResponse = new();
            var inputLetter = emailLetter.Letter;
            var inputEmailView = emailLetter.Email;
            Email emailDB = null;
            ActivityLog parentEntryFromDB = null;
            ActivityLogChildEntry childEntryForEmail = null;
            inputLetter.OrgId = baseHttpRequestContext.OrgId;
            inputLetter.ModifiedBy = baseHttpRequestContext.UserId;
            inputLetter.ModifiedDate = DateTime.UtcNow;
            LetterOutput letterFromDb = await _letterDAL.GetLetterOutDAL(patient_id, baseHttpRequestContext.OrgId, id);

            if (letterFromDb is null)
            {
                apiResponse.Errors.Add("Letter cannot be edited");
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failure";
                return apiResponse;
            }

            using (var transaction = new TransactionScope(TransactionScopeOption.Required, TransactionScopeAsyncFlowOption.Enabled))
            {
                if (inputLetter.LetterStatusId == (short)LetterStatus.Approved || inputEmailView is not null)
                {
                    if (inputLetter.FileDetailsId.HasValue)
                    {
                        pdfFileResponse = await ConvertToPdf(inputLetter, baseHttpRequestContext);

                        if (pdfFileResponse.StatusCode == StatusCodes.Status200OK)
                        {
                            if (inputLetter.LetterStatusId == (short)LetterStatus.Approved)
                            {
                                inputLetter.FileDetailsId = pdfFileResponse.Result;
                                PatientMediaAssoc media = new();
                                media.OrgId = baseHttpRequestContext.OrgId;
                                media.PatientDetailsId = patient_id;
                                media.StatusId = (short)Status.Active;
                                media.CreatedBy = baseHttpRequestContext.UserId;
                                media.FileDetailsId = pdfFileResponse.Result;
                                media.MediaName = inputLetter.Name + ".pdf";
                                media.MediaLibrarySubTypeId = (short)MediaLibrarySubType.Letter;
                                media.MediaTypeId = (short)MediaType.PDF;
                                media.LetterId = inputLetter.Id;
                                Guid g = Guid.NewGuid();
                                media.GroupId = g;
                                long? mediaId = await _mediaLibraryDAL.AddMedia(media);
                            }

                        }
                        else
                        {
                            apiResponse.StatusCode = pdfFileResponse.StatusCode;
                            apiResponse.Errors = pdfFileResponse.Errors;
                            apiResponse.Message = pdfFileResponse.Message;
                            return apiResponse;
                        }
                    }
                }

                inputLetter.CreatedBy = letterFromDb.CreatedBy;
                inputLetter.Name = letterFromDb.Name;
                inputLetter.LetterTemplatesId = letterFromDb.LetterTemplatesId;
                inputLetter.CreatedDate = letterFromDb.CreatedDate;
                inputLetter.PatientDetailsId = patient_id;
                inputLetter.StatusId = letterFromDb.StatusId;
                inputLetter.Id = letterFromDb.Id;
                inputLetter.VersionNo = letterFromDb.VersionNo;
                inputLetter.LetterId = letterFromDb.LetterId;
                inputLetter.AppointmentDetailsId = letterFromDb.AppointmentDetailsId;

                int row = await _letterDAL.EditLetterDAL(inputLetter);
                ActivityLogChildEntry childEntry = await CreateActivityLogChildEntry((short)ActivityLogOps.Letter_Update, patient_id, inputLetter, baseHttpRequestContext);

                parentEntryFromDB = await _activityLogDAL.GetActivityLogWithSubTypeId(patient_id, childEntry.OrgId, inputLetter.Id, (short)ActivityLogType.Media_Library, (short?)MediaLibrarySubType.Patient_Letter);
                if (inputLetter.FileDetailsId is not null)
                {
                    parentEntryFromDB.FileDetailsId = inputLetter.FileDetailsId;
                    childEntry.FileDetailsId = inputLetter.FileDetailsId;

                }
                parentEntryFromDB.ActivityStatusId = inputLetter.LetterStatusId;
                if (childEntry.ActivityStatusId == (short)LetterStatus.Approved)
                {
                    parentEntryFromDB.Description = "Letter Name:" + inputLetter.Name + ".pdf";
                }
                else
                {
                    parentEntryFromDB.Description = "Letter Name:" + inputLetter.Name;
                }
                parentEntryFromDB.ModifiedDate = DateTime.UtcNow;
                parentEntryFromDB.ModifiedBy = baseHttpRequestContext.UserId;
                if (row > 0 && (inputLetter.LetterStatusId != letterFromDb.LetterStatusId))
                {
                    parentEntryFromDB.ActivityLogChildEntries.Add(childEntry);
                }

                if (emailLetter.Email is not null)
                {
                    Email email = _mapper.Map<EmailView, Email>(emailLetter.Email);
                    // To make Entry into Email Table So that function can pick the email to send
                    if (pdfFileResponse.Result.ToString() != "0")
                    {
                        email.AttachmentsFileIds = string.IsNullOrEmpty(email.AttachmentsFileIds)
                            ? pdfFileResponse.Result.ToString()
                            : $"{pdfFileResponse.Result.ToString()},{email.AttachmentsFileIds}";
                    }

                    emailDB = await AddPatientEmailBAL(patient_id, email, baseHttpRequestContext);

                    // To make the Entry of the Email in Activity log child table 
                    childEntryForEmail = await CreateActivityLogChildEntry((short)ActivityLogOps.Letter_Email, patient_id, inputLetter, baseHttpRequestContext, emailLetter.Email);
                    parentEntryFromDB.ActivityLogChildEntries.Add(childEntryForEmail);

                }

                await _activityLogDAL.UpdateActivityLog(parentEntryFromDB);
                transaction.Complete();
                transaction.Dispose();
            }
            if (emailDB is not null)
            {
                StorePatientEmailMessage(baseHttpRequestContext.OrgCode, emailDB.Id, childEntryForEmail.Id);
            }

            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result = "Success";
            return apiResponse;

        }

        /// <summary>
        /// Method to fetch a letter based on Id
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<LetterOutput>> GetLetterBAL(long patient_id, BaseHttpRequestContext baseHttpRequestContext, long id)
        {
            int orgId = baseHttpRequestContext.OrgId;
            ApiResponse<LetterOutput> apiResposne = new();
            LetterOutput letterFromDB = await _letterDAL.GetLetterOutDAL(patient_id, orgId, id);
            letterFromDB = await GetImageSasToken(letterFromDB, baseHttpRequestContext);

            if (letterFromDB is null)
            {
                apiResposne.Result = null;
                apiResposne.StatusCode = StatusCodes.Status400BadRequest;
                apiResposne.Message = "Failure";
                apiResposne.Errors.Add("The Letter doesnot exist.");
                return apiResposne;
            }

            letterFromDB.CreatedDate = DateTime.SpecifyKind(letterFromDB.CreatedDate, DateTimeKind.Utc);
            letterFromDB.ModifiedDate = (letterFromDB.ModifiedDate == null) ? null : DateTime.SpecifyKind((DateTime)letterFromDB.ModifiedDate, DateTimeKind.Utc);

            apiResposne.Result = letterFromDB;
            apiResposne.StatusCode = StatusCodes.Status200OK;
            apiResposne.Message = "Success";
            return apiResposne;
        }

        /// <summary>
        /// Method to delete a letter
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> DeleteLetterBAL(long patient_id, long id, BaseHttpRequestContext baseHttpRequestContext, DeleteObject deleteObject)
        {
            int orgId = baseHttpRequestContext.OrgId;
            long loggedInUser = baseHttpRequestContext.UserId;
            ApiResponse<long?> apiResponse = new();

            if (string.IsNullOrEmpty(deleteObject.DeleteReason) || deleteObject.DeleteReason == " ")
            {
                apiResponse.Errors.Add("Can not Delete the Letter without Delete Reason");
                apiResponse.Message = "Failure";
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                return apiResponse;
            }

            Letter letter = await _letterDAL.GetLetterDAL(patient_id, baseHttpRequestContext.OrgId, id);
            if (letter is not null && letter.LetterStatusId != (short)LetterStatus.Deleted && letter.LetterStatusId != (short)LetterStatus.Approved)
            {
                letter.StatusId = (short)Status.Deleted;
                letter.LetterStatusId = (short)LetterStatus.Deleted;
                letter.ModifiedBy = loggedInUser;
                letter.ModifiedDate = DateTime.UtcNow;
                letter.DeleteReason = deleteObject.DeleteReason;
                using (var transaction = new TransactionScope(TransactionScopeOption.Required, TransactionScopeAsyncFlowOption.Enabled))
                {

                    int rows = await _letterDAL.EditLetterDAL(letter);

                    if (rows > 0)
                    {
                        ActivityLogChildEntry childEntry = await CreateActivityLogChildEntry((short)ActivityLogOps.Letter_Delete, patient_id, letter, baseHttpRequestContext);
                        ActivityLog parentEntryFromDB = await _activityLogDAL.GetActivityLogWithSubTypeId(patient_id, childEntry.OrgId, letter.Id, (short)ActivityLogType.Media_Library, (short?)MediaLibrarySubType.Patient_Letter);
                        parentEntryFromDB.ActivityStatusId = letter.LetterStatusId;
                        parentEntryFromDB.ModifiedDate = DateTime.UtcNow;
                        parentEntryFromDB.ModifiedBy = baseHttpRequestContext.UserId;
                        parentEntryFromDB.ActivityLogChildEntries.Add(childEntry);
                        await _activityLogDAL.UpdateActivityLog(parentEntryFromDB);
                        apiResponse.Result = id;
                        apiResponse.Message = "Success";
                        apiResponse.StatusCode = StatusCodes.Status200OK;
                        transaction.Complete();
                        return apiResponse;
                    }
                    apiResponse.Errors.Add("letter cannot be deleted at this time.");
                    transaction.Dispose();
                }
            }
            apiResponse.Errors.Add("Letter cannot be deleted");
            apiResponse.Message = "Failure";
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            return apiResponse;

        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="patient_id"></param>
        /// <param name="id"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="inputLetter"></param>
        /// <returns></returns>
        public async Task<ApiResponse<string>> DownloadFinanceLetter(long patient_id, long id, EmailLetterView emailLetterView, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<string> apiResponse = new();
            List<short> lstEmailTypes = new()
            {
                (short)PatientEmailType.Parent_Estimate,
                (short)PatientEmailType.Medicare_Statement,
                (short)PatientEmailType.Eclipse_Statement

            };
            long? fileDetailsId = null;
            Letter inputLetter = null;
            if (emailLetterView.Letter is not null)
                inputLetter = _mapper.Map<LetterView, Letter>(emailLetterView.Letter);
            if (inputLetter is not null)
            {
                inputLetter.OrgId = baseHttpRequestContext.OrgId;
                inputLetter.ModifiedBy = baseHttpRequestContext.UserId;
                inputLetter.ModifiedDate = DateTime.UtcNow;
                Letter letterFromDb = inputLetter;
                long emailId = default(long);
                using (var transaction = new TransactionScope(TransactionScopeOption.Required, TransactionScopeAsyncFlowOption.Enabled))
                {
                    if (inputLetter.FileDetailsId.HasValue)
                    {
                        //downoloadFile
                        var token = baseHttpRequestContext.BearerToken;
                        string interServiceToken = baseHttpRequestContext.InterServiceToken;
                        string DownloadfileAPiUrl = _appSettings.ApiUrls["FileServiceUrl"] + "/file/filedownload?Id=" + inputLetter.FileDetailsId;
                        RestClient downlodRestClient = new RestClient(DownloadfileAPiUrl, null, token, interServiceToken);
                        var downloadedfile = await downlodRestClient.GetAsync<ApiResponse<string>>(DownloadfileAPiUrl);

                        if (downloadedfile.StatusCode != StatusCodes.Status200OK)
                        {
                            apiResponse.StatusCode = downloadedfile.StatusCode;
                            apiResponse.Errors = downloadedfile.Errors;
                            apiResponse.Message = downloadedfile.Message;
                            apiResponse.Result = "File Download failed";
                            return apiResponse;
                        }

                        //ConvertContentToPDF
                        string syncFusionServiceURL = _appSettings.ApiUrls["SyncFusionServiceUrl"] + "/syncfusion/ConvertDocToPDF";
                        RestClient syncFusionClient = new RestClient(syncFusionServiceURL, null, token, interServiceToken);
                        var fileUploadResponse = await syncFusionClient.PostAsync<ApiResponse<FileUploadObject>>(syncFusionServiceURL, downloadedfile.Result);

                        if (fileUploadResponse is null || fileUploadResponse.StatusCode != StatusCodes.Status200OK)
                        {
                            apiResponse.StatusCode = fileUploadResponse.StatusCode;
                            apiResponse.Errors = fileUploadResponse.Errors;
                            apiResponse.Message = fileUploadResponse.Message;
                            apiResponse.Result = "File Conversion failed.";
                            return apiResponse;
                        }

                        //File Upload Started
                        var fileUpload = fileUploadResponse.Result;
                        fileUpload.CustomFileName = inputLetter.Name;
                        string fileAPiUrl = _appSettings.ApiUrls["FileServiceUrl"] + "/file/fileupload";
                        RestClient restClient = new RestClient(fileAPiUrl, null, token, interServiceToken);
                        var fileApiResponse = await restClient.PostAsync<ApiResponse<long>>(fileAPiUrl, fileUpload);

                        if (fileApiResponse.StatusCode == StatusCodes.Status200OK)
                        {
                            fileDetailsId = fileApiResponse.Result; ;

                            if (lstEmailTypes.Contains(emailLetterView.PatientEmailTypeId))
                            {
                                inputLetter.FileDetailsId = fileApiResponse.Result;
                                PatientMediaAssoc media = new();
                                media.OrgId = baseHttpRequestContext.OrgId;
                                media.PatientDetailsId = patient_id;
                                media.StatusId = (short)Status.Active;
                                media.CreatedBy = baseHttpRequestContext.UserId;
                                media.FileDetailsId = fileApiResponse.Result;
                                media.MediaName = fileUpload.CustomFileName;
                                media.MediaLibrarySubTypeId = (short)MediaLibrarySubType.Letter;
                                media.MediaTypeId = (short)MediaType.PDF;
                                Guid g = Guid.NewGuid();
                                media.GroupId = g;
                                long? mediaId = await _mediaLibraryDAL.AddMedia(media);
                                ActivityLog parentEntry = CreateActivityLogEntry((int)ActivityLogOps.File_Upload, patient_id, media, baseHttpRequestContext);
                                ActivityLogChildEntry childEntry = CreateActivityLogChildEntry((short)ActivityLogOps.File_Upload, patient_id, media, baseHttpRequestContext);
                                if (inputLetter.FileDetailsId is not null)
                                {
                                    childEntry.FileDetailsId = inputLetter.FileDetailsId;

                                }
                                if (parentEntry is not null)
                                {
                                    parentEntry.ActivityLogChildEntries.Add(childEntry);
                                }
                                await _activityLogDAL.AddActivityLog(parentEntry);
                            }


                            if (emailLetterView.Email is not null)
                            {

                                emailLetterView.Email.PatientDetailsId = patient_id;
                                emailLetterView.Email.OrgId = baseHttpRequestContext.OrgId;
                                emailLetterView.Email.PatientEmailTypeId = emailLetterView.PatientEmailTypeId;
                                emailLetterView.Email.AttachmentsFileIds = (fileApiResponse.Result > default(long)) ? fileApiResponse.Result.ToString() : string.Empty;
                                emailLetterView.Email.CreatedBy = baseHttpRequestContext.UserId;
                                emailLetterView.Email.CreatedDate = DateTime.UtcNow;
                                emailLetterView.Email.EmailStatusId = (short)EmailStatus.InQueue;
                                emailLetterView.Email.StatusId = (short)Status.Active;
                                int rows = await _letterDAL.AddPatientEmail(emailLetterView.Email);
                                emailId = (rows > 0) ? emailLetterView.Email.Id : 0;

                            }
                        }
                        else
                        {
                            apiResponse.StatusCode = fileApiResponse.StatusCode;
                            apiResponse.Errors = fileApiResponse.Errors;
                            apiResponse.Message = fileApiResponse.Message;
                            apiResponse.Result = "FileUpload Failed";
                            return apiResponse;
                        }
                    }
                    transaction.Complete();
                    transaction.Dispose();

                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    apiResponse.Message = "Success";
                    apiResponse.Result = "Success";


                    if (emailId > default(long))
                    {
                        await StorePatientEmailMessage(baseHttpRequestContext.OrgCode, emailId);
                    }
                    if (emailLetterView.PatientEmailTypeId == (short)PatientEmailType.Eclipse_Statement || emailLetterView.PatientEmailTypeId == (short)PatientEmailType.Medicare_Statement)
                    {
                        if (emailLetterView.InvoiceMedicareAssocId != null)
                            await UpdateInvoiceMedicareAssoc(fileDetailsId, emailLetterView.InvoiceMedicareAssocId, baseHttpRequestContext);
                    }

                    return apiResponse;
                }

            }
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            apiResponse.Errors.Add("Letter object is mandatory");
            apiResponse.Message = "Failure";
            apiResponse.Result = "FileUpload Failed";
            return apiResponse;
        }

        private async Task UpdateInvoiceMedicareAssoc(long? fileDetailsId, long? invoiceMedicareAssocId, BaseHttpRequestContext baseHttpRequestContext)
        {
            string invoiceServiceUrl = $"{_appSettings.ApiUrls["InvoiceServiceUrl"]}/invoice/invoice_medicareassoc/patch/{invoiceMedicareAssocId}";
            InvoiceMedicareAssocUpdateView updateView = new()
            {
                FileDetailsId = fileDetailsId
            };
            RestClient restClient = new RestClient(invoiceServiceUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            var invoiceApiResponse = await restClient.PutAsync<ApiResponse<string>>(invoiceServiceUrl, updateView);
        }

        private async Task StorePatientEmailMessage(string orgCode, long emailId, long? activityLogChildId = null)
        {
            EmailRequestDataModel patientRequestDataModel = new EmailRequestDataModel
            {
                OrgCode = orgCode,
                PropertyType = (int)EODRequestDataType.Patient,
                PropertyId = emailId,
                ActicityLogChildId = activityLogChildId


            };
            Dictionary<string, string> userPros = new Dictionary<string, string>
                        {
                            { "RequestType","Patient"},
                            { "to",_configuration["AzureAD:ASBSubNamePatientEmail"]}
                        };
            byte[] jsonContentByteArr = Encoding.GetEncoding("iso-8859-1").GetBytes(JsonConvert.SerializeObject(patientRequestDataModel));
            await _asbMessageSenderHelper.MessageSenderTopic(jsonContentByteArr, userPros, _configuration["AzureAD:AzSBConnStringPatient"], _configuration["AzureAD:ASBTopicPatient"]);
        }

        private async Task StorePatientEHealthMessage(string orgCode, long propertyId, short propertyType, long? activityLogChildId = null)
        {
            EmailRequestDataModel patientRequestDataModel = new EmailRequestDataModel
            {
                OrgCode = orgCode,
                PropertyType = propertyType,
                PropertyId = propertyId,
                ActicityLogChildId = activityLogChildId

            };
            Dictionary<string, string> userPros = new Dictionary<string, string>
                        {
                            { "RequestType","Patient"},
                            { "to",_configuration["AzureAD:ASBSubNameEhealth"]}
                        };
            byte[] jsonContentByteArr = Encoding.GetEncoding("iso-8859-1").GetBytes(JsonConvert.SerializeObject(patientRequestDataModel));
            await _asbMessageSenderHelper.MessageSenderTopic(jsonContentByteArr, userPros, _configuration["AzureAD:AzSBConnStringPatient"], _configuration["AzureAD:ASBTopicPatient"]);
        }
        public async Task<ApiResponse<string>> GetPaymentDepositORInvoiceReceipt(long patient_id, long invoiceDetailsId)
        {
            ApiResponse<string> apiResponse = new ApiResponse<string>();



            return apiResponse;
        }
        /// <summary>
        /// GetImage Sas token
        /// </summary>
        /// <param name="letterTemplate"></param>
        /// <returns></returns>
        private async Task<LetterOutput> GetImageSasToken(LetterOutput letter, BaseHttpRequestContext baseHttpRequestContext)
        {
            //this pattern will fetch all the urls present in letter.
            string pattern = "https:([^\"]+)";
            Regex rgx = new Regex(pattern, RegexOptions.IgnoreCase);
            MatchCollection urlMatches = rgx.Matches(letter.Text);
            var fileFormats = Enum.GetNames(typeof(EnumImageFileType)).ToList();
            foreach (var url in urlMatches.ToList())
            {
                Uri uri = new Uri(url.Value);
                string filename = uri.Segments.Last();

                //GetSasToken for file
                if (!string.IsNullOrEmpty(uri.ToString()) && fileFormats.Contains(uri.ToString()))
                {
                    FileDetails fileDetails = await _commDAL.GetFileDetailsByFileName(baseHttpRequestContext.OrgId, filename.ToString());
                    if (fileDetails != null)
                    {
                        string fileSasToken = await GetFileSASToken(fileDetails.Id, baseHttpRequestContext);

                        if (!string.IsNullOrEmpty(fileSasToken))
                            letter.Text = letter.Text.Replace(url.Value.ToString(), fileSasToken);
                    }
                }
            }
            return letter;
        }

        //private CommunicationNote AddActiveLogEntry(Letter inputLetter, long patient_id, BaseHttpRequestContext baseHttpRequestContext, short ops, Letter oldLetter)
        //{
        //    CommunicationNote commNote = new();
        //    commNote.OrgId = baseHttpRequestContext.OrgId;
        //    commNote.CreatedDate = DateTime.UtcNow;
        //    commNote.ModifiedDate = DateTime.UtcNow;
        //    commNote.ModifiedBy = baseHttpRequestContext.UserId;
        //    commNote.PatientDetailsId = patient_id;
        //    commNote.CommGroupTypeId = (short)CommGroupType.Media_Library;
        //    commNote.CommTypeId = (short)CommType.Media_Library;
        //    commNote.LetterId = inputLetter.Id;
        //    commNote.LetterStatusId = inputLetter.LetterStatusId;
        //    commNote.MediaLibrarySubTypeId = (short)MediaLibrarySubType.Letter;
        //    commNote.IsParent = false;
        //    if (inputLetter.FileDetailsId is not null)
        //    {
        //        commNote.FileDetailsId = inputLetter.FileDetailsId;
        //    }

        //    if (ops == (short)ActivityLogOps.Letter_Add)
        //    {
        //        string letterName = inputLetter.Name;
        //        string operationName = "Letter Added.";
        //        if (inputLetter.VersionNo > 1)
        //        {
        //            commNote.Notes = operationName + "\r\nLetter Name:" + letterName;
        //        }
        //        else
        //        {
        //            commNote.Notes = operationName + "\r\nLetter Name:" + letterName;
        //        }

        //    }
        //    if (ops == (short)ActivityLogOps.Parent_Letter_Add)
        //    {
        //        string letterName = inputLetter.Name;
        //        if (inputLetter.VersionNo > 1)
        //        {
        //            commNote.Notes = "Letter Name:" + letterName;
        //        }
        //        else
        //        {
        //            commNote.Notes = "Letter Name:" + letterName;
        //        }
        //        commNote.Notes = "Letter Name:" + letterName;
        //        commNote.MediaLibrarySubTypeId = (short)MediaLibrarySubType.Patient_Letter;
        //        commNote.IsParent = true;

        //    }
        //    else if (ops == (short)ActivityLogOps.Letter_Update)
        //    {
        //        string letterName = inputLetter.Name;
        //        string operationName = "Letter Updated.";
        //        if (commNote.LetterStatusId == (short)LetterStatus.Approved)
        //        {
        //            commNote.Notes = operationName + "\r\nLetter Name:" + letterName + ".pdf";
        //        }
        //        else
        //        {
        //            commNote.Notes = operationName + "\r\nLetter Name:" + letterName;
        //        }
        //    }
        //    else if (ops == (short)ActivityLogOps.Letter_Delete)
        //    {
        //        string letterName = inputLetter.Name;
        //        string deleteReason = string.Empty;
        //        if (!string.IsNullOrWhiteSpace(inputLetter.DeleteReason))
        //            deleteReason = (inputLetter.DeleteReason.LastIndexOf('.') == inputLetter.DeleteReason.Length - 1) ? inputLetter.DeleteReason : inputLetter.DeleteReason + '.';

        //        string operationName = "Letter Deleted. Delete Reason:" + deleteReason;
        //        commNote.Notes = operationName + "\r\nLetter Name:" + letterName;
        //    }
        //    return commNote;
        //}

        /// <summary>
        /// Method to create an ActivityLog object
        /// </summary>
        /// <param name="activityLogOp"></param>
        /// <param name="inputLetter"></param>
        /// <param name="patientDetailsId"></param>
        /// <returns></returns>
        private ActivityLog CreateActivityLogEntry(int activityLogOp, long? patientDetailsId, Letter inputLetter, BaseHttpRequestContext baseHttpRequestContext)
        {
            ActivityLog activityLog = new();
            activityLog.ActivityLogTypeId = (short)ActivityLogType.Media_Library;
            activityLog.ActivityStatusId = inputLetter.LetterStatusId;
            activityLog.PatientDetailsId = (long)patientDetailsId;
            activityLog.EntityId = (inputLetter is not null) ? inputLetter.Id : null;
            activityLog.OrgId = baseHttpRequestContext.OrgId;
            activityLog.CreatedDate = DateTime.UtcNow;
            activityLog.ModifiedDate = DateTime.UtcNow;
            activityLog.ModifiedBy = baseHttpRequestContext.UserId;
            activityLog.ActivityLogSubTypeId = (short?)MediaLibrarySubType.Patient_Letter;
            if ((int)ActivityLogOps.Parent_Letter_Add == activityLogOp)
            {
                string letterName = inputLetter.Name;
                activityLog.Description = "Letter Name:" + letterName;
                activityLog.Title = EnumExtensions.GetDescription(MediaLibrarySubType.Patient_Letter);

            }
            return activityLog;
        }

        /// <summary>
        /// Method to create an ActivityLog object
        /// </summary>
        /// <param name="activityLogOp"></param>
        /// <param name="inputLetter"></param>
        /// <param name="patientDetailsId"></param>
        /// <returns></returns>
        private ActivityLog CreateActivityLogEntry(int activityLogOp, long? patientDetailsId, PatientMediaAssoc patientMediaAssoc, BaseHttpRequestContext baseHttpRequestContext)
        {
            ActivityLog activityLog = new();
            activityLog.ActivityLogTypeId = (short)ActivityLogType.Media_Library;
            activityLog.PatientDetailsId = (long)patientDetailsId;
            //activityLog.EntityId = (patientMediaAssoc is not null) ? patientMediaAssoc.Id : null;
            activityLog.OrgId = baseHttpRequestContext.OrgId;
            activityLog.CreatedDate = DateTime.UtcNow;
            activityLog.ModifiedDate = DateTime.UtcNow;
            activityLog.ModifiedBy = baseHttpRequestContext.UserId;
            activityLog.ActivityLogSubTypeId = (short?)MediaLibrarySubType.Media_Upload;
            if ((int)ActivityLogOps.File_Upload == activityLogOp)
            {
                string fileName = patientMediaAssoc.MediaName;
                activityLog.Description = "Number of files Uploaded:1";
                activityLog.Title = EnumExtensions.GetDescription(MediaLibrarySubType.Media_Upload);

            }
            return activityLog;
        }
        /// <summary>
        /// Add Activity log child Entries
        /// </summary>
        /// <param name="activityLogOp"></param>
        /// <param name="lstVariableJSON"></param>
        /// <param name="inputLetter"></param>
        /// <param name="patientDetailsId"></param>
        /// <returns></returns>
        private async Task<ActivityLogChildEntry> CreateActivityLogChildEntry(int activityLogOp, long? patientDetailsId, Letter inputLetter, BaseHttpRequestContext baseHttpRequestContext, EmailView emailView = null)
        {
            ActivityLogChildEntry activityLogChild = new();
            activityLogChild.OrgId = baseHttpRequestContext.OrgId;
            activityLogChild.CreatedDate = DateTime.UtcNow;
            activityLogChild.ModifiedDate = DateTime.UtcNow;
            activityLogChild.ModifiedBy = baseHttpRequestContext.UserId;
            activityLogChild.ActivityStatusId = inputLetter.LetterStatusId;
            activityLogChild.EntityId = (inputLetter.Id <= 0) ? null : inputLetter.Id;
            activityLogChild.Title = EnumExtensions.GetDescription(MediaLibrarySubType.Letter);
            activityLogChild.SubTypeId = (short?)MediaLibrarySubType.Letter;

            if ((int)ActivityLogOps.Letter_Add == activityLogOp)
            {
                activityLogChild.Description = await GetActivityChildMessage(inputLetter, "Letter Created.", baseHttpRequestContext);
            }
            else if (activityLogOp == (short)ActivityLogOps.Letter_Update)
            {
                activityLogChild.Description = await GetActivityChildMessage(inputLetter, "Letter Updated.", baseHttpRequestContext);
            }
            else if (activityLogOp == (short)ActivityLogOps.Letter_Delete)
            {
                string letterName = inputLetter.Name;
                string deleteReason = string.Empty;
                if (!string.IsNullOrWhiteSpace(inputLetter.DeleteReason))
                    deleteReason = (inputLetter.DeleteReason.LastIndexOf('.') == inputLetter.DeleteReason.Length - 1) ? inputLetter.DeleteReason : inputLetter.DeleteReason + '.';

                string operationName = "Letter Deleted. Delete Reason:" + deleteReason;
                activityLogChild.Description = operationName + "\r\nLetter Name:" + letterName;
            }
            else if (activityLogOp == (short)ActivityLogOps.Letter_Email)
            {
                activityLogChild.Description = "Letter Emailed";
                List<VariableJSON> emailVariableJSON = new();
                VariableJSON variableJson = new();
                variableJson.Operation = Enum.GetName(ActivityLogOps.Letter_Email);
                variableJson.Column = "Addressee(To): ";
                variableJson.OldValue = null;
                variableJson.NewValue = emailView.ToActivityString;
                emailVariableJSON.Add(variableJson);
                if (emailView.CcAddresses is not null)
                {
                    VariableJSON ccJson = new();
                    ccJson.Operation = Enum.GetName(ActivityLogOps.Letter_Email);
                    ccJson.Column = "(CC): ";
                    ccJson.OldValue = null;
                    ccJson.NewValue = emailView.CcActivityString;
                    emailVariableJSON.Add(ccJson);
                }

                activityLogChild.VariableJson = (emailVariableJSON is null || emailVariableJSON.Count == 0) ? null : JsonConvert.SerializeObject(emailVariableJSON);
                activityLogChild.EmailStatusId = (short)EmailStatus.InQueue;

            }
            else if (activityLogOp == (short)ActivityLogOps.Letter_Print)
            {
                activityLogChild.Description = "Letter Printed";

            }
            else if (activityLogOp == (short)ActivityLogOps.Letter_Download)
            {
                activityLogChild.Description = "Letter Downloaded";

            }
            else if (activityLogOp == (short)ActivityLogOps.Letter_EHealth)
            {
                List<VariableJSON> emailVariableJSON = new();
                VariableJSON variableJson = new();
                variableJson.Operation = Enum.GetName(ActivityLogOps.Letter_EHealth);
                variableJson.Column = "Addressee(To): ";
                variableJson.OldValue = null;
                variableJson.NewValue = emailView.ToActivityString;
                emailVariableJSON.Add(variableJson);
                if (!string.IsNullOrWhiteSpace(emailView.CcActivityString))
                {
                    VariableJSON ccJson = new();
                    ccJson.Operation = Enum.GetName(ActivityLogOps.Letter_EHealth);
                    ccJson.Column = "(CC): ";
                    ccJson.OldValue = null;
                    ccJson.NewValue = emailView.CcActivityString;
                    emailVariableJSON.Add(ccJson);
                }
                activityLogChild.VariableJson = (emailVariableJSON is null || emailVariableJSON.Count == 0) ? null : JsonConvert.SerializeObject(emailVariableJSON);
                //activityLogChild.EmailStatusId = (short)EmailStatus.InQueue;
            }
            return activityLogChild;
        }
        /// <summary>
        /// Add Activity log child Entries
        /// </summary>
        /// <param name="activityLogOp"></param>
        /// <param name="lstVariableJSON"></param>
        /// <param name="inputLetter"></param>
        /// <param name="patientDetailsId"></param>
        /// <returns></returns>
        private ActivityLogChildEntry CreateActivityLogChildEntry(int activityLogOp, long? patientDetailsId, PatientMediaAssoc patientMediaAssoc, BaseHttpRequestContext baseHttpRequestContext)
        {
            ActivityLogChildEntry activityLogChild = new();
            activityLogChild.OrgId = baseHttpRequestContext.OrgId;
            activityLogChild.CreatedDate = DateTime.UtcNow;
            activityLogChild.ModifiedDate = DateTime.UtcNow;
            activityLogChild.ModifiedBy = baseHttpRequestContext.UserId;
            activityLogChild.EntityId = (patientMediaAssoc.Id <= 0) ? null : patientMediaAssoc.Id;

            if ((int)ActivityLogOps.File_Upload == activityLogOp)
            {
                activityLogChild.FileDetailsId = patientMediaAssoc.FileDetailsId;
                string fileName = patientMediaAssoc.MediaName;
                activityLogChild.Description = "File Uploaded. File Name:" + fileName;
                activityLogChild.Title = EnumExtensions.GetDescription(MediaLibrarySubType.File_Upload);
            }
            return activityLogChild;
        }
        private async Task<string> GetActivityChildMessage(Letter inputLetter, string operationName, BaseHttpRequestContext baseHttpRequestContext)
        {
            operationName = $"{operationName}\n<b>{EnumExtensions.GetDescription((LetterStatus)inputLetter.LetterStatusId)}</b>";
            if (inputLetter.AssignedTo != null)
            {
                operationName = operationName + "\n<i>Assigned to [" + await FetchUserName((long)inputLetter.AssignedTo, baseHttpRequestContext) + "]</i>";
            }
            operationName = operationName + "\nStatus: <i>" + EnumExtensions.GetDescription((LetterStatus)inputLetter.LetterStatusId) + "</i>";
            return operationName;
        }

        private async Task<string> FetchUserName(long userId, BaseHttpRequestContext baseHttpRequestContext)
        {
            var userSeviceUrl = _appSettings.ApiUrls["UserServiceUrl"] + "/user/user_detailinfo?Ids=" + userId.ToString();
            var restClientUser = new RestClient(userSeviceUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            var apiResponseUsers = await restClientUser.GetAsync<ApiResponse<List<UserInfoView>>>(userSeviceUrl);
            if (apiResponseUsers.StatusCode == StatusCodes.Status200OK && apiResponseUsers.Result is not null)
            {
                return $"{apiResponseUsers.Result.FirstOrDefault()?.FirstName} {apiResponseUsers.Result.FirstOrDefault()?.SurName}";
            }
            return "";
        }

        private string GetDateFromOrganisation(OrganisationView orgDetails)
        {
            var CompnayDateTimeNow = DateTimeConversion.ConvertTimeFromUtc(DateTime.UtcNow, orgDetails.WindowsTimeZoneData);
            var CompnayDateNow = CompnayDateTimeNow is not null ? ((DateTime)CompnayDateTimeNow).ToString("ddMMyyyy") : null;
            if (string.IsNullOrEmpty(CompnayDateNow))
            {
                CompnayDateTimeNow = DateTimeConversion.ConvertTimeFromUtc(DateTime.UtcNow, orgDetails.LinuxTimeZoneData);
                CompnayDateNow = CompnayDateTimeNow is not null ? ((DateTime)CompnayDateTimeNow).ToString("ddMMyyyy") : null;
            }

            return CompnayDateNow;
        }

        /// <summary>
        /// Get SAS token for file.
        /// </summary>
        /// <param name="fileId"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        private async Task<string> GetFileSASToken(long fileId, BaseHttpRequestContext baseHttpRequestContext)
        {
            string fileSAStoken = "";
            var token = baseHttpRequestContext.BearerToken;
            string interServiceToken = baseHttpRequestContext.InterServiceToken;
            string fileAPiUrl = _appSettings.ApiUrls["FileServiceUrl"] + "/file/file_details/" + fileId;
            RestClient restClient = new RestClient(fileAPiUrl, null, token, interServiceToken);
            var fileApiResponse = await restClient.GetAsync<ApiResponse<FileDetailsOutputForId>>(fileAPiUrl, null);
            if (fileApiResponse != null && fileApiResponse.Result.SasTokens != null)
                fileSAStoken = fileApiResponse.Result.SasTokens.FileToken.ToString();

            return fileSAStoken;
        }

        //** LETTER ACTIONS  **//

        /// <summary>
        /// AddLetterActions
        /// </summary>
        /// <param name="letterId"></param>
        /// <param name="letterActions"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> AddLetterActions(long letterId, LetterActions letterActions, BaseHttpRequestContext baseHttpRequestContext)
        {
            var orgId = baseHttpRequestContext.OrgId;
            var loggedInUser = baseHttpRequestContext.UserId;
            ApiResponse<long?> apiResponse = new();
            letterActions.LetterId = letterId;
            letterActions.OrgId = orgId;
            letterActions.CreatedDate = DateTime.UtcNow;
            letterActions.CreatedBy = loggedInUser;
            letterActions.StatusId = (short)Status.Active;

            //prepare variable json to add the values 
            List<VariableJSON> lstVariableJSON = await VariableJSONForAdd(letterActions, orgId);
            // Get ActivityLog for given LetterId
            // Get ActivityLog for given LetterId
            ActivityLog ParentActivity = await _activityLogDAL.GetActivityLogForPatientActions(letterActions.LetterId, orgId);
            //Putting an entry in ActivtyLogChildEntry table
            ActivityLogChildEntry lastChildActivity = ParentActivity.ActivityLogChildEntries.OrderByDescending(i => i.ModifiedDate).FirstOrDefault();
            lastChildActivity.ModifiedDate = DateTime.UtcNow;
            lastChildActivity.ModifiedBy = baseHttpRequestContext.UserId;
            lastChildActivity.VariableJson = (lstVariableJSON is null || lstVariableJSON.Count == 0) ? null : JsonConvert.SerializeObject(lstVariableJSON);

            using (var transaction = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
            {

                var id = await _letterDAL.AddLetterActions(letterActions);
                if (id == 0)
                {
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Message = "Add Letter Actions failed";
                    apiResponse.Result = 0;
                    return apiResponse;
                }

                await _activityLogDAL.UpdateActivityLogChild(lastChildActivity);

                apiResponse.Result = id;
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";

                transaction.Complete();
            }

            return apiResponse;
        }

        /// <summary>
        /// GetLetterActions
        /// </summary>
        /// <param name="letterId"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<LetterActions>> GetLetterActions(long letterId, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<LetterActions> apiResposne = new();
            var dbResponse = await _letterDAL.GetLetterActions(letterId, baseHttpRequestContext.OrgId);
            apiResposne.Result = dbResponse;
            apiResposne.StatusCode = StatusCodes.Status200OK;
            apiResposne.Message = "Success";
            return apiResposne;
        }

        /// <summary>
        /// EditLetterActions
        /// </summary>
        /// <param name="letterId"></param>
        /// <param name="letterActions"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<string>> EditLetterActions(long letterId, LetterActions letterActions, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<string> apiResponse = new();
            var orgId = baseHttpRequestContext.OrgId;
            var userId = baseHttpRequestContext.UserId;
            var dbResponse = await _letterDAL.GetLetterActions(letterId, orgId);

            letterActions.Id = dbResponse.Id;
            letterActions.LetterId = letterId;
            letterActions.OrgId = orgId;
            letterActions.ModifiedDate = DateTime.UtcNow;
            letterActions.ModifiedBy = userId;
            letterActions.CreatedBy = dbResponse.CreatedBy;
            letterActions.CreatedDate = dbResponse.CreatedDate;

            //prepare variable json to add the values 
            List<VariableJSON> newVariableJSON = await GenerateVariableJSON(dbResponse, letterActions, orgId);
            // Get ActivityLog for given LetterId
            ActivityLog ParentActivity = await _activityLogDAL.GetActivityLogForPatientActions(letterActions.LetterId, orgId);
            //Putting an entry in ActivtyLogChildEntry table
            ActivityLogChildEntry lastChildActivity = ParentActivity.ActivityLogChildEntries.Where(c => c.EmailStatusId == null).OrderByDescending(i => i.ModifiedDate).FirstOrDefault();

            if (lastChildActivity.VariableJson is null)
            {
                lastChildActivity.VariableJson = (newVariableJSON is null || newVariableJSON.Count == 0) ? null : JsonConvert.SerializeObject(newVariableJSON);
            }
            else if (lastChildActivity.VariableJson is not null && newVariableJSON is not null && newVariableJSON.Count > 0)
            {
                List<VariableJSON> lastVariableJson = JsonConvert.DeserializeObject<List<VariableJSON>>(lastChildActivity.VariableJson);
                lastVariableJson = lastVariableJson.Concat(newVariableJSON).ToList();
                lastChildActivity.VariableJson = JsonConvert.SerializeObject(lastVariableJson);
            }


            using (var transaction = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
            {

                int rows = await _letterDAL.UpdateLetterActions(letterActions);
                if (rows == 0)
                {
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Message = "Update Letter Actions failed";
                    apiResponse.Result = "Failed";
                    return apiResponse;
                }
                if (newVariableJSON is not null && newVariableJSON.Count > 0)
                {
                    lastChildActivity.ModifiedDate = DateTime.UtcNow;
                    lastChildActivity.ModifiedBy = baseHttpRequestContext.UserId;
                    await _activityLogDAL.UpdateActivityLogChild(lastChildActivity);
                }



                transaction.Complete();
            }

            apiResponse.Result = "Success";
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            return apiResponse;
        }

        private async Task<List<VariableJSON>> GenerateVariableJSON(object OldObject, object newObject, int orgId)
        {
            var props = OldObject.GetType().GetProperties();
            List<VariableJSON> lstVariableJSON = new();
            List<long> ChangedUserIds = new();
            List<UserDetailInfo> lstUsers = new();

            //Get all the username for userIds which are changed 
            foreach (PropertyInfo i in props)
            {
                if (checkAuditCoumns(i.Name))
                {
                    var oldVal = i.GetValue(OldObject, null);
                    var newVal = i.GetValue(newObject, null);
                    bool isUpdated = false;
                    if (oldVal is null && newVal is not null || oldVal is not null && newVal is null)
                    {
                        isUpdated = true;
                    }
                    else if (oldVal is not null && newVal is not null && !oldVal.Equals(newVal))
                    {
                        isUpdated = true;

                    }

                    if (isUpdated)
                    {
                        if (oldVal is not null)
                        {
                            ChangedUserIds.Add((long)oldVal);
                        }
                        if (newVal is not null)
                        {
                            ChangedUserIds.Add((long)newVal);
                        }
                    }
                }
            }
            //Get all userId details from DB 
            if (ChangedUserIds is not null)
            {
                lstUsers = await _letterDAL.FetchUserDetailsFromIds(ChangedUserIds, orgId);
            }

            //Prepare Variable Json for changes in letterActions Table

            foreach (PropertyInfo i in props)
            {
                if (checkAuditCoumns(i.Name))
                {
                    var oldVal = i.GetValue(OldObject, null);
                    var newVal = i.GetValue(newObject, null);
                    bool isUpdated = false;
                    if (oldVal is null && newVal is not null || oldVal is not null && newVal is null)
                    {
                        isUpdated = true;
                    }
                    else if (oldVal is not null && newVal is not null && !oldVal.Equals(newVal))
                    {
                        isUpdated = true;

                    }

                    if (isUpdated)
                    {

                        VariableJSON variableJson = new();
                        variableJson.Operation = Enum.GetName(ActivityLogOps.Update);
                        switch (i.Name.ToLower())
                        {
                            case "draftuserid":
                                {
                                    variableJson.Column = "Drafted User";
                                    if (oldVal is not null)
                                    {
                                        UserDetailInfo user = lstUsers.Where(i => i.Id == (long)oldVal).FirstOrDefault();
                                        string title = (user.TitleId is null) ? string.Empty : EnumExtensions.GetDescription((TitleType)user.TitleId);
                                        variableJson.OldValue = title + " " + user.FirstName + " " + user.SurName;
                                    }
                                    if (newVal is not null)
                                    {
                                        UserDetailInfo user = lstUsers.Where(i => i.Id == (long)newVal).FirstOrDefault();
                                        string title = (user.TitleId is null) ? string.Empty : EnumExtensions.GetDescription((TitleType)user.TitleId);
                                        variableJson.NewValue = title + " " + user.FirstName + " " + user.SurName;
                                    }
                                    break;
                                }
                            case "dictationuserid":
                                {
                                    variableJson.Column = "Dictation User";
                                    if (oldVal is not null)
                                    {
                                        UserDetailInfo user = lstUsers.Where(i => i.Id == (long)oldVal).FirstOrDefault();
                                        string title = (user.TitleId is null) ? string.Empty : EnumExtensions.GetDescription((TitleType)user.TitleId);
                                        variableJson.OldValue = title + " " + user.FirstName + " " + user.SurName;
                                    }
                                    if (newVal is not null)
                                    {
                                        UserDetailInfo user = lstUsers.Where(i => i.Id == (long)newVal).FirstOrDefault();
                                        string title = (user.TitleId is null) ? string.Empty : EnumExtensions.GetDescription((TitleType)user.TitleId);
                                        variableJson.NewValue = title + " " + user.FirstName + " " + user.SurName;
                                    }
                                    break;
                                }
                            case "inreviewuserid":
                                {
                                    variableJson.Column = "InReview User";
                                    if (oldVal is not null)
                                    {
                                        UserDetailInfo user = lstUsers.Where(i => i.Id == (long)oldVal).FirstOrDefault();
                                        string title = (user.TitleId is null) ? string.Empty : EnumExtensions.GetDescription((TitleType)user.TitleId);
                                        variableJson.OldValue = title + " " + user.FirstName + " " + user.SurName;
                                    }
                                    if (newVal is not null)
                                    {
                                        UserDetailInfo user = lstUsers.Where(i => i.Id == (long)newVal).FirstOrDefault();
                                        string title = (user.TitleId is null) ? string.Empty : EnumExtensions.GetDescription((TitleType)user.TitleId);
                                        variableJson.NewValue = title + " " + user.FirstName + " " + user.SurName;
                                    }
                                    break;
                                }
                            case "pendingapprovaluserid":
                                {
                                    variableJson.Column = "Pending Approval User";
                                    if (oldVal is not null)
                                    {
                                        UserDetailInfo user = lstUsers.Where(i => i.Id == (long)oldVal).FirstOrDefault();
                                        string title = (user.TitleId is null) ? string.Empty : EnumExtensions.GetDescription((TitleType)user.TitleId);
                                        variableJson.OldValue = title + " " + user.FirstName + " " + user.SurName;
                                    }
                                    if (newVal is not null)
                                    {
                                        UserDetailInfo user = lstUsers.Where(i => i.Id == (long)newVal).FirstOrDefault();
                                        string title = (user.TitleId is null) ? string.Empty : EnumExtensions.GetDescription((TitleType)user.TitleId);
                                        variableJson.NewValue = title + " " + user.FirstName + " " + user.SurName;
                                    }
                                    break;
                                }
                            case "approveduserid":
                                {
                                    variableJson.Column = "Approved User";
                                    if (oldVal is not null)
                                    {
                                        UserDetailInfo user = lstUsers.Where(i => i.Id == (long)oldVal).FirstOrDefault();
                                        string title = (user.TitleId is null) ? string.Empty : EnumExtensions.GetDescription((TitleType)user.TitleId);
                                        variableJson.OldValue = title + " " + user.FirstName + " " + user.SurName;
                                    }
                                    if (newVal is not null)
                                    {
                                        UserDetailInfo user = lstUsers.Where(i => i.Id == (long)newVal).FirstOrDefault();
                                        string title = (user.TitleId is null) ? string.Empty : EnumExtensions.GetDescription((TitleType)user.TitleId);
                                        variableJson.NewValue = title + " " + user.FirstName + " " + user.SurName;
                                    }

                                    break;
                                }
                            case "sentuserid":
                                {
                                    variableJson.Column = "Sent User";
                                    if (oldVal is not null)
                                    {
                                        UserDetailInfo user = lstUsers.Where(i => i.Id == (long)oldVal).FirstOrDefault();
                                        string title = (user.TitleId is null) ? string.Empty : EnumExtensions.GetDescription((TitleType)user.TitleId);
                                        variableJson.OldValue = title + " " + user.FirstName + " " + user.SurName;
                                    }
                                    if (newVal is not null)
                                    {
                                        UserDetailInfo user = lstUsers.Where(i => i.Id == (long)newVal).FirstOrDefault();
                                        string title = (user.TitleId is null) ? string.Empty : EnumExtensions.GetDescription((TitleType)user.TitleId);
                                        variableJson.NewValue = title + " " + user.FirstName + " " + user.SurName;
                                    }
                                    break;
                                }
                            case "patientrecieveduserid":
                                {
                                    variableJson.Column = "Patient Received User";
                                    if (oldVal is not null)
                                    {
                                        UserDetailInfo user = lstUsers.Where(i => i.Id == (long)oldVal).FirstOrDefault();
                                        string title = (user.TitleId is null) ? string.Empty : EnumExtensions.GetDescription((TitleType)user.TitleId);
                                        variableJson.OldValue = title + " " + user.FirstName + " " + user.SurName;
                                    }
                                    if (newVal is not null)
                                    {
                                        UserDetailInfo user = lstUsers.Where(i => i.Id == (long)newVal).FirstOrDefault();
                                        string title = (user.TitleId is null) ? string.Empty : EnumExtensions.GetDescription((TitleType)user.TitleId);
                                        variableJson.NewValue = title + " " + user.FirstName + " " + user.SurName;
                                    }
                                    break;
                                }
                            default:
                                {
                                    variableJson.NewValue = newVal;
                                    variableJson.OldValue = oldVal;
                                    variableJson.Column = i.Name;
                                    break;
                                }

                        }

                        lstVariableJSON.Add(variableJson);
                    }

                }
            }

            return lstVariableJSON;
        }


        private bool checkAuditCoumns(string colName)
        {
            if (colName.ToLower().Equals("draftuserid") ||
                colName.ToLower().Equals("dictationuserid") ||
                colName.ToLower().Equals("inreviewuserid") ||
                colName.ToLower().Equals("pendingapprovaluserid") ||
                colName.ToLower().Equals("approveduserid") ||
                colName.ToLower().Equals("sentuserid") ||
                colName.ToLower().Equals("patientrecieveduserid")
                )
                return true;

            return false;
        }

        /* private bool CheckAuditUserIdCoumns(string colName)
         {
             if (colName.ToLower().Equals("draftuserid") ||
                 colName.ToLower().Equals("dictationuserid") ||
                 colName.ToLower().Equals("inreviewuserid") ||
                 colName.ToLower().Equals("pendingapprovaluserid") ||
                 colName.ToLower().Equals("approveduserid") ||
                 colName.ToLower().Equals("sentuserid") ||
                 colName.ToLower().Equals("patientrecieveduserid")
                 )
                 return true;

             return false;
         }*/

        //** PATIENT ACTIONS - LETTER DETAILS  **//

        /// <summary>
        /// GetPatientActionsLetterDetails
        /// </summary>
        /// <param name="id"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<PatientActionsLetterDetails>> GetPatientActionsLetterDetails(long id, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<PatientActionsLetterDetails> apiResposne = new();
            var letterFromDB = await _letterDAL.GetPatientActionsLetterDetails(id, baseHttpRequestContext.OrgId);
            apiResposne.Result = letterFromDB;
            apiResposne.StatusCode = StatusCodes.Status200OK;
            apiResposne.Message = "Success";
            return apiResposne;
        }

        /* /// <summary>
         /// Add Activity log child Entries
         /// </summary>
         /// <param name="activityLogOp"></param>
         /// <param name="lstVariableJSON"></param>
         /// <param name="inputLetter"></param>
         /// <param name="patientDetailsId"></param>
         /// <returns></returns>
         private async Task<ActivityLogChildEntry> CreateALChildForPatientAction(List<VariableJSON> lstVariableJSON, LetterActions letterActions, BaseHttpRequestContext baseHttpRequestContext)
         {
             ActivityLogChildEntry activityLogChild = new();
             activityLogChild.OrgId = baseHttpRequestContext.OrgId;
             activityLogChild.CreatedDate = DateTime.UtcNow;
             activityLogChild.ModifiedDate = DateTime.UtcNow;
             activityLogChild.ModifiedBy = baseHttpRequestContext.UserId;
             activityLogChild.VariableJson = (lstVariableJSON is null || lstVariableJSON.Count == 0) ? null : JsonConvert.SerializeObject(lstVariableJSON);
             //  activityLogChild.ActivityStatusId = inputLetter.LetterStatusId;
             activityLogChild.EntityId = letterActions.LetterId;
             activityLogChild.Title = EnumExtensions.GetDescription(MediaLibrarySubType.Letter);
             activityLogChild.SubTypeId = (short?)MediaLibrarySubType.Letter;
             activityLogChild.Description = "Letter Actions Updated";          
             return activityLogChild;
         }*/

        private async Task<List<VariableJSON>> VariableJSONForAdd(object newObject, int orgId)
        {
            var props = newObject.GetType().GetProperties();
            List<VariableJSON> lstVariableJSON = new();
            List<long> ChangedUserIds = new();
            List<UserDetailInfo> lstUsers = new();

            //Get all the username for userIds which are changed 
            foreach (PropertyInfo i in props)
            {
                if (checkAuditCoumns(i.Name))
                {
                    var oldVal = i.GetValue(newObject, null);
                    if (oldVal is not null)
                    {
                        ChangedUserIds.Add((long)oldVal);
                    }
                }
            }
            //Get all userId details from DB 
            if (ChangedUserIds is not null)
            {
                lstUsers = await _letterDAL.FetchUserDetailsFromIds(ChangedUserIds, orgId);
            }

            //Prepare Variable Json for changes in letterActions Table

            foreach (PropertyInfo i in props)
            {
                if (checkAuditCoumns(i.Name))
                {
                    var newVal = i.GetValue(newObject, null);
                    if (newVal is not null)
                    {
                        VariableJSON variableJson = new();
                        variableJson.Operation = Enum.GetName(ActivityLogOps.Update);
                        switch (i.Name.ToLower())
                        {
                            case "draftuserid":
                                {
                                    variableJson.Column = "Drafted User";
                                    UserDetailInfo user = lstUsers.Where(i => i.Id == (long)newVal).FirstOrDefault();
                                    string title = (user.TitleId is null) ? string.Empty : EnumExtensions.GetDescription((TitleType)user.TitleId);
                                    variableJson.NewValue = title + " " + user.FirstName + " " + user.SurName;
                                    break;
                                }
                            case "dictationuserid":
                                {
                                    variableJson.Column = "Dictation User";
                                    UserDetailInfo user = lstUsers.Where(i => i.Id == (long)newVal).FirstOrDefault();
                                    string title = (user.TitleId is null) ? string.Empty : EnumExtensions.GetDescription((TitleType)user.TitleId);
                                    variableJson.NewValue = title + " " + user.FirstName + " " + user.SurName;
                                    break;
                                }
                            case "inreviewuserid":
                                {
                                    variableJson.Column = "InReview User";
                                    UserDetailInfo user = lstUsers.Where(i => i.Id == (long)newVal).FirstOrDefault();
                                    string title = (user.TitleId is null) ? string.Empty : EnumExtensions.GetDescription((TitleType)user.TitleId);
                                    variableJson.NewValue = title + " " + user.FirstName + " " + user.SurName;
                                    break;
                                }
                            case "pendingapprovaluserid":
                                {
                                    variableJson.Column = "Pending Approval User";
                                    UserDetailInfo user = lstUsers.Where(i => i.Id == (long)newVal).FirstOrDefault();
                                    string title = (user.TitleId is null) ? string.Empty : EnumExtensions.GetDescription((TitleType)user.TitleId);
                                    variableJson.NewValue = title + " " + user.FirstName + " " + user.SurName;
                                    break;
                                }
                            case "approveduserid":
                                {
                                    variableJson.Column = "Approved User";
                                    UserDetailInfo user = lstUsers.Where(i => i.Id == (long)newVal).FirstOrDefault();
                                    string title = (user.TitleId is null) ? string.Empty : EnumExtensions.GetDescription((TitleType)user.TitleId);
                                    variableJson.NewValue = title + " " + user.FirstName + " " + user.SurName;
                                    break;
                                }
                            case "sentuserid":
                                {
                                    variableJson.Column = "Sent User";
                                    UserDetailInfo user = lstUsers.Where(i => i.Id == (long)newVal).FirstOrDefault();
                                    string title = (user.TitleId is null) ? string.Empty : EnumExtensions.GetDescription((TitleType)user.TitleId);
                                    variableJson.NewValue = title + " " + user.FirstName + " " + user.SurName;
                                    break;
                                }
                            case "patientrecieveduserid":
                                {
                                    variableJson.Column = "Patient Received User";
                                    UserDetailInfo user = lstUsers.Where(i => i.Id == (long)newVal).FirstOrDefault();
                                    string title = (user.TitleId is null) ? string.Empty : EnumExtensions.GetDescription((TitleType)user.TitleId);
                                    variableJson.NewValue = title + " " + user.FirstName + " " + user.SurName;
                                    break;
                                }
                            default:
                                {
                                    variableJson.NewValue = newVal;
                                    variableJson.Column = i.Name;
                                    break;
                                }

                        }

                        lstVariableJSON.Add(variableJson);
                    }

                }
            }

            return lstVariableJSON;
        }
        /// <summary>
        /// MEthod to send patient email
        /// </summary>
        /// <param name="patient_id"></param>
        /// <param name="email"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<Email>> SendPatientEmail(long patient_id, EmailView emailview, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<Email> apiResponse = new();
            LetterActivityChild letterOpsData = new();
            letterOpsData.LetterOpsTypeId = (short?)LetterOpsType.Email;
            long? childActivityLogId = null;

            Email email = _mapper.Map<EmailView, Email>(emailview);
            var emailDB = await AddPatientEmailBAL(patient_id, email, baseHttpRequestContext);

            if (emailDB is not null)
            {
                if (emailDB.Id > default(long))
                {
                    if (emailview.LetterId is not null)
                    {
                        var childLogResponse = await AddLetterActivityChild(patient_id, (long)emailview.LetterId, letterOpsData, baseHttpRequestContext, emailview);
                        childActivityLogId = childLogResponse.Result;
                    }
                    await StorePatientEmailMessage(baseHttpRequestContext.OrgCode, emailDB.Id, childActivityLogId);
                }

                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Result = email;
                apiResponse.Message = "Success";
                return apiResponse;
            }

            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            apiResponse.Result = null;
            apiResponse.Message = "Failure";
            apiResponse.Errors.Add("Email cannot be added to queue.Please try again later.");
            return apiResponse;
        }

        public async Task<Email> AddPatientEmailBAL(long patient_id, Email email, BaseHttpRequestContext baseHttpRequestContext)
        {
            if (email is not null)
            {

                email.PatientDetailsId = patient_id;
                email.OrgId = baseHttpRequestContext.OrgId;

                email.CreatedBy = baseHttpRequestContext.UserId;
                email.CreatedDate = DateTime.UtcNow;
                email.EmailStatusId = (short)EmailStatus.InQueue;
                email.StatusId = (short)Status.Active;
                int rows = await _letterDAL.AddPatientEmail(email);
                if (rows > 0)
                {
                    return email;
                }
            }

            return null;
        }

        public async Task<ApiResponse<long>> ConvertToPdf(Letter inputLetter, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<long> apiResponse = new();
            //downoloadFile
            var token = baseHttpRequestContext.BearerToken;
            string interServiceToken = baseHttpRequestContext.InterServiceToken;
            string DownloadfileAPiUrl = _appSettings.ApiUrls["FileServiceUrl"] + "/file/filedownload?Id=" + inputLetter.FileDetailsId;
            RestClient downlodRestClient = new RestClient(DownloadfileAPiUrl, null, token, interServiceToken);
            var downloadedfile = await downlodRestClient.GetAsync<ApiResponse<string>>(DownloadfileAPiUrl);

            if (downloadedfile.StatusCode != StatusCodes.Status200OK)
            {
                apiResponse.StatusCode = downloadedfile.StatusCode;
                apiResponse.Errors = downloadedfile.Errors;
                apiResponse.Message = downloadedfile.Message;
                return apiResponse;
            }

            //ConvertContentToPDF
            string syncFusionServiceURL = _appSettings.ApiUrls["SyncFusionServiceUrl"] + "/syncfusion/ConvertDocToPDF";
            RestClient syncFusionClient = new RestClient(syncFusionServiceURL, null, token, interServiceToken);
            var fileUploadResponse = await syncFusionClient.PostAsync<ApiResponse<FileUploadObject>>(syncFusionServiceURL, downloadedfile.Result);

            if (fileUploadResponse.StatusCode != StatusCodes.Status200OK)
            {
                apiResponse.StatusCode = fileUploadResponse.StatusCode;
                apiResponse.Errors = fileUploadResponse.Errors;
                apiResponse.Message = fileUploadResponse.Message;
                return apiResponse;
            }

            //File Upload Started
            var fileUpload = fileUploadResponse.Result;
            fileUpload.CustomFileName = inputLetter.Name + ".pdf";
            string fileAPiUrl = _appSettings.ApiUrls["FileServiceUrl"] + "/file/fileupload";
            RestClient restClient = new RestClient(fileAPiUrl, null, token, interServiceToken);
            var fileApiResponse = await restClient.PostAsync<ApiResponse<long>>(fileAPiUrl, fileUpload);

            return fileApiResponse;
        }

        public async Task<ApiResponse<long>> AddLetterActivityChild(long patient_id, long id, LetterActivityChild letterActivity, BaseHttpRequestContext baseHttpRequestContext, EmailView emailView)
        {
            ApiResponse<long> apiResponse = new();
            short activityLogOps = 0;
            LetterOutput letterFromDb = await _letterDAL.GetLetterOutDAL(patient_id, baseHttpRequestContext.OrgId, id);
            if (letterFromDb is null)
            {
                apiResponse.Errors.Add("Letter Not Found");
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failure";
                return apiResponse;
            }
            Letter inputLetter = _mapper.Map<LetterOutput, Letter>(letterFromDb);
            if (letterActivity.LetterOpsTypeId == (short)LetterOpsType.Email)
            {
                activityLogOps = (short)ActivityLogOps.Letter_Email;
            }
            else if (letterActivity.LetterOpsTypeId == (short)LetterOpsType.Print)
            {
                activityLogOps = (short)ActivityLogOps.Letter_Print;

            }
            else if (letterActivity.LetterOpsTypeId == (short)LetterOpsType.Download)
            {
                activityLogOps = (short)ActivityLogOps.Letter_Download;
            }

            var parentEntryFromDB = await _activityLogDAL.GetActivityLogWithSubTypeId(patient_id, baseHttpRequestContext.OrgId, inputLetter.Id, (short)ActivityLogType.Media_Library, (short?)MediaLibrarySubType.Patient_Letter);
            parentEntryFromDB.ModifiedDate = DateTime.UtcNow;
            parentEntryFromDB.ModifiedBy = baseHttpRequestContext.UserId;

            ActivityLogChildEntry childEntry = await CreateActivityLogChildEntry(activityLogOps, patient_id, inputLetter, baseHttpRequestContext, emailView);
            parentEntryFromDB.ActivityLogChildEntries.Add(childEntry);
            int row = await _activityLogDAL.UpdateActivityLog(parentEntryFromDB);
            if (row > 0)
            {
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";
                apiResponse.Result = childEntry.Id;
                return apiResponse;

            }
            else
            {
                apiResponse.Errors.Add("Activty Log child entry can not be completed");
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failure";
                return apiResponse;
            }

        }

        public async Task<ApiResponse<string>> SendPatientSms(long patient_id, SmsInput inputSms, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<string> mainApiResponse = new ApiResponse<string>();
            var orgDetails = await _letterDAL.GetOrganistaionDAL(baseHttpRequestContext.OrgId);

            if (orgDetails != null && orgDetails.AdhocSmsSubscription == false)
            {
                mainApiResponse.Errors.Add("Sms cannot be sent.Sms Service is not part of subscription. ");
                mainApiResponse.StatusCode = StatusCodes.Status400BadRequest;
                mainApiResponse.Message = "Failure";
                return mainApiResponse;
            }



            Shared.Models.Communication.SmsRequest smsRequest = new Shared.Models.Communication.SmsRequest
            {
                RequestBody = inputSms.RequestBody,
                ToMobile = inputSms.ToMobile,
                SmsTypeId = (inputSms.AppointmentDetailsId == null) ? (short)SmsType.AdhocSmsWithOutAppointment : (short)SmsType.AdhocSmsWithAppointment

            };

            //ActivityLog activityLog = await AddActivityLogEntryForAdhocSms(patient_id, inputSms, baseHttpRequestContext,(short)EmailStatus.InQueue);

            // if ( activityLog.Id > default(long))
            // {
            ApiResponse<string> apiResponse = new ApiResponse<string>();
            string smsAPiUrl = _appSettings.ApiUrls["CommunicationServiceUrl"] + "/Sms/SendSmsWithReply";
            var token = baseHttpRequestContext.BearerToken;
            string interServiceToken = baseHttpRequestContext.InterServiceToken;
            RestClient restClient = new RestClient(smsAPiUrl, null, token, interServiceToken);
            var apiResponseFromAPI = await restClient.PostAsync<ApiResponse<long>>(smsAPiUrl, smsRequest);
            apiResponse.StatusCode = apiResponseFromAPI.StatusCode;
            apiResponse.Errors = apiResponseFromAPI.Errors;
            apiResponse.Result = apiResponseFromAPI.Result.ToString();
            apiResponse.Message = apiResponseFromAPI.Message;

            var SmsHistoryAdd = await _smsBAL.AddToSmsHistory(inputSms.AppointmentDetailsId, patient_id, apiResponseFromAPI.Result, (short)SmsSentType.User, baseHttpRequestContext);


            if (apiResponse != null && apiResponse.StatusCode == StatusCodes.Status200OK)
            {

                if (inputSms.AppointmentDetailsId is not null && inputSms.AppointmentDetailsId > default(long))
                    await AddActivityLogEntryForAdhocSms(patient_id, (long)inputSms.AppointmentDetailsId, inputSms, baseHttpRequestContext, (short)EmailStatus.Sent);

                mainApiResponse.StatusCode = StatusCodes.Status200OK;
                mainApiResponse.Message = "Success";
                mainApiResponse.Result = apiResponse.Result;
                return mainApiResponse;
            }
            else
            {

                //  await UpdateActivityLogEntryForAdhocSms(activityLog, baseHttpRequestContext, (short)EmailStatus.Failed);


            }
            // }


            mainApiResponse.Errors.Add("Sms cannot be sent.");
            mainApiResponse.StatusCode = StatusCodes.Status400BadRequest;
            mainApiResponse.Message = "Failure";
            return mainApiResponse;



        }

        private async Task UpdateActivityLogEntryForAdhocSms(ActivityLog activityLog, BaseHttpRequestContext baseHttpRequestContext, short smsStatus)
        {
            activityLog.ActivityStatusId = smsStatus;
            activityLog.ModifiedDate = DateTime.UtcNow;
            activityLog.ModifiedBy = baseHttpRequestContext.UserId;
            await _activityLogDAL.UpdateActivityLog(activityLog);

        }

        private async Task UpdateActivityLogEntryForAdhocSms(long activityLogId, BaseHttpRequestContext baseHttpRequestContext, short smsStatus)
        {
            ActivityLog dbActivityLog = await _activityLogDAL.GetActivityLog(baseHttpRequestContext.OrgId, activityLogId);
            dbActivityLog.ActivityStatusId = smsStatus;
            dbActivityLog.ModifiedDate = DateTime.UtcNow;
            dbActivityLog.ModifiedBy = baseHttpRequestContext.UserId;

            await _activityLogDAL.UpdateActivityLog(dbActivityLog);
        }

        private async Task AddActivityLogEntryForAdhocSms(long patient_id, long appointentDetailsId, SmsInput inputSms, BaseHttpRequestContext baseHttpRequestContext, short smsStatus = (short)EmailStatus.InQueue)
        {
            //CommunicationNote commNote = new CommunicationNote()
            //{
            //    PatientDetailsId = patient_id,
            //    Notes = inputSms.RequestBody,
            //    VersionNo = 1,
            //    CommGroupTypeId = (short)CommGroupType.Patient_Adhoc_SMS,
            //    CommTypeId = (short)CommType.Phone,
            //    OrgId = baseHttpRequestContext.OrgId,
            //    //FileDetailsId = (long.TryParse(emailRequest.AttachmentsFileIds, out result)) ? result : null,
            //    CreatedDate = DateTime.UtcNow,
            //    ModifiedBy = baseHttpRequestContext.UserId,
            //    ModifiedDate = DateTime.UtcNow
            //};
            //ActivityLog activityLog = new()
            //{
            //    PatientDetailsId = commNote.PatientDetailsId,
            //    CreatedDate = DateTime.UtcNow,
            //    ModifiedBy = commNote.ModifiedBy,
            //    ModifiedDate = DateTime.UtcNow,
            //    OrgId = commNote.OrgId,
            //    Description = commNote.Notes,
            //    ActivityLogTypeId = (short)ActivityLogType.Phone,
            //    Title = ActivityLogType.Phone.ToString(),
            //    ActivityStatusId = smsStatus,
            //    EntityId = commNote.Id
            //};
            //using (var transaction = new TransactionScope(TransactionScopeOption.Required, TransactionScopeAsyncFlowOption.Enabled))
            //{
            //    int rows = await _commDAL.AddCommNotes(commNote);

            //    if (rows > 0)
            //    {

            //        activityLog.EntityId = commNote.Id;
            //        long activityLogId = await _activityLogDAL.AddActivityLog(activityLog);
            //        if (activityLogId > default(long))
            //        {
            //            transaction.Complete();
            //            return activityLog;
            //        }
            //    }
            //}

            //return null;//            default(long);

            ActivityLog activityLog = await _activityLogDAL.FetchActivtyLogFromEntityId(patient_id, baseHttpRequestContext.OrgId, appointentDetailsId, (short)ActivityLogType.Appointments);

            if (activityLog is not null)
            {
                ActivityLogChildEntry activityLogChildEntry = new()
                {
                    OrgId = baseHttpRequestContext.OrgId,
                    ActivityLogId = activityLog.Id,
                    Description = "SMS Sent " + "<br/>" + inputSms.RequestBody,
                    CreatedDate = DateTime.UtcNow,
                    ModifiedBy = baseHttpRequestContext.UserId,
                    ModifiedDate = DateTime.UtcNow,
                    ActivityStatusId = activityLog.ActivityStatusId
                };
                await _activityLogDAL.AddActivtyLogChildEntry(activityLogChildEntry);

            }



        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="patient_id"></param>
        /// <param name="inputMessageRequestBody"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<EHealthMessage>> InitiateEHealthAcitivty(long patient_id, InputMessageRequestBody inputMessageRequestBody, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<EHealthMessage> apiResponse = new();
            if (inputMessageRequestBody is not null && inputMessageRequestBody.Providers is not null)
            {
                Letter letterFromDb = await _letterDAL.GetLetterDAL(patient_id, baseHttpRequestContext.OrgId, (long)inputMessageRequestBody.LetterId);
                if (letterFromDb is null || letterFromDb.FileDetailsId is null)
                {
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Result = null;
                    apiResponse.Message = "Failure";
                    apiResponse.Errors.Add("Letter attachment is mandatory.");
                    return apiResponse;
                }
                if (letterFromDb is not null && letterFromDb.LetterStatusId == (short)LetterStatus.Approved)
                {
                    InputOBResult obResultLetter = new();
                    obResultLetter.FileDetailsId = letterFromDb.FileDetailsId;
                    inputMessageRequestBody.OBResults.Add(obResultLetter);
                }
                else
                {
                    ApiResponse<long> pdfFileResponse = new();

                    pdfFileResponse = await ConvertToPdf(letterFromDb, baseHttpRequestContext);
                    if (pdfFileResponse.StatusCode == StatusCodes.Status200OK)
                    {

                        InputOBResult obResultLetter = new();
                        obResultLetter.FileDetailsId = pdfFileResponse.Result;
                        inputMessageRequestBody.OBResults.Add(obResultLetter);

                    }
                    else
                    {
                        apiResponse.StatusCode = pdfFileResponse.StatusCode;
                        apiResponse.Errors = pdfFileResponse.Errors;
                        apiResponse.Message = pdfFileResponse.Message;
                        return apiResponse;
                    }
                }
                Provider toProvider = inputMessageRequestBody.Providers.Where(x => x.DoctorRole == "To").FirstOrDefault();
                Provider fromProvider = inputMessageRequestBody.Providers.Where(x => x.DoctorRole == "From").FirstOrDefault();
                List<Provider> ccProviders = inputMessageRequestBody.Providers.Where(x => x.DoctorRole == "CC").ToList();
                string ccActivityString = string.Empty;
                string toActivityString = string.Empty;
                if (toProvider is null)
                {
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Result = null;
                    apiResponse.Message = "Failure";
                    apiResponse.Errors.Add("Addresee is mandatory.");
                    return apiResponse;
                }
                else if (fromProvider is null)
                {
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Result = null;
                    apiResponse.Message = "Failure";
                    apiResponse.Errors.Add("Addresssor is mandatory.");
                    return apiResponse;
                }
                else
                {
                    toActivityString = toProvider.GivenName + " " + toProvider.LastName /*+ "<" + toProvider.Email + ">"*/;
                }
                if (ccProviders is not null && ccProviders.Count > 0)
                {
                    ccProviders.ToList().ForEach(x =>
                    {
                        if (ccActivityString != "") ccActivityString = ccActivityString + ",";
                        ccActivityString = ccActivityString + x.GivenName + " " + x.LastName /*+ "<" + x.Email + ">"*/;

                    });
                }

                string attachmentFileIds = string.Empty;
                if (inputMessageRequestBody.OBResults.Any())
                {
                    List<long> lstFileIds = inputMessageRequestBody.OBResults.Where(x => x.FileDetailsId != null).Select(x => (long)x.FileDetailsId).ToList();
                    attachmentFileIds = string.Join(',', lstFileIds);
                    if (lstFileIds is null || lstFileIds.Count == 0)
                    {
                        apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                        apiResponse.Result = null;
                        apiResponse.Message = "Failure";
                        apiResponse.Errors.Add("Letter attachment is mandatory.");
                        return apiResponse;
                    }
                }
                EHealthMessage eHealthMessage = new()
                {
                    OrgId = baseHttpRequestContext.OrgId,
                    SecureMessagingTypeId = (short)SecureMessagingType.MO,
                    CreatedBy = baseHttpRequestContext.UserId,
                    CreatedDate = DateTime.UtcNow,
                    MessageStatusId = (short)EHealthMessagingStatus.InQueue,
                    PatientDetailsId = patient_id,
                    StatusId = (short)Status.Active,
                    RequestBody = JsonConvert.SerializeObject(inputMessageRequestBody),
                    AttachmentsFileIds = attachmentFileIds,
                    OrderNumber = await _letterDAL.GetNextVal("[Patient].[SeqOrderNumber]")

                };

                ActivityLog activityLogDB = await _activityLogDAL.GetActivityLogWithSubTypeId(patient_id, baseHttpRequestContext.OrgId, inputMessageRequestBody.LetterId, (short)ActivityLogType.Media_Library, (short?)MediaLibrarySubType.Patient_Letter);
                if (activityLogDB is not null)
                {

                    EmailView email = new()
                    {
                        CcActivityString = ccActivityString,
                        ToActivityString = toActivityString
                    };
                    ActivityLogChildEntry childEntryForEHealth = await CreateActivityLogChildEntry((short)ActivityLogOps.Letter_EHealth, patient_id, letterFromDb, baseHttpRequestContext, email);
                    childEntryForEHealth.ActivityLogId = activityLogDB.Id;
                    childEntryForEHealth.EmailStatusId = (short)EmailStatus.InQueue;
                    if (eHealthMessage.SecureMessagingTypeId == (short)SecureMessagingType.MO)
                        childEntryForEHealth.Description = "Letter supplied to Medical Objects(Ref#:";
                    else
                        childEntryForEHealth.Description = "Letter sent";

                    int rows = 0;
                    int activitylogChildRows = 0;
                    using (var transaction = new TransactionScope(TransactionScopeOption.Required, TransactionScopeAsyncFlowOption.Enabled))
                    {

                        if (childEntryForEHealth is not null)
                        {
                            childEntryForEHealth.Description += eHealthMessage.OrderNumber + ").";
                            //childEntryForEHealth.EntityId = eHealthMessage.Id;
                            activitylogChildRows = await _activityLogDAL.AddActivtyLogChildEntry(childEntryForEHealth);
                            if (activitylogChildRows > 0)
                            {
                                eHealthMessage.ActivityLogChildId = childEntryForEHealth.Id;
                                rows = await _letterDAL.AddEHealthMessage(eHealthMessage);

                            }
                        }
                        transaction.Complete();
                    }
                    if (rows > 0 && activitylogChildRows > 0)
                    {
                        await StorePatientEHealthMessage(baseHttpRequestContext.OrgCode, eHealthMessage.Id, 17, childEntryForEHealth.Id);
                        apiResponse.StatusCode = StatusCodes.Status200OK;
                        apiResponse.Result = eHealthMessage;
                        apiResponse.Message = "Success";
                        return apiResponse;
                    }
                }


            }

            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            apiResponse.Result = null;
            apiResponse.Message = "Failure";
            apiResponse.Errors.Add("EHealth Acitivty failed.Please try again later.");


            return apiResponse;
        }
    }

}
