﻿using Capstone2.RestServices.Patient.Context;
using Capstone2.RestServices.Patient.Interfaces;
using Capstone2.RestServices.Patient.Models;
using Capstone2.Shared.Models.Enum;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Patient.Services
{
    public class MedicalRecordsDAL : IMedicalRecordsDAL
    {
        public readonly ReadOnlyPatientDBContext _readOnlyDbContext;
        public readonly UpdatablePatientDBContext _updatableDBContext;
        public MedicalRecordsDAL(ReadOnlyPatientDBContext readOnlyDbContext, UpdatablePatientDBContext updatableDBContext)
        {
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
        }

        /// <summary>
        /// get MedicalProcedures
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="patientId"></param>
        /// <returns></returns>
        public async Task<List<MedicalProcedures>> GetMedicalProcedures(int orgId, long patientId)
        {
            var MedicalRecords = await (from MP in _readOnlyDbContext.MedicalProcedures
                                        where MP.PatientDetailsId == patientId && MP.OrgId == orgId && MP.StatusId == (short)Status.Active
                                        select new MedicalProcedures
                                        {
                                           Id = MP.Id,
                                           OrgId = MP.OrgId,
                                           PatientDetailsId = MP.PatientDetailsId,
                                           Type = MP.Type,
                                           Procedure = MP.Procedure,
                                           Date = MP.Date,
                                           IsActive = MP.IsActive,
                                           StatusId = MP.StatusId,
                                           CreatedDate = MP.CreatedDate,
                                           CreatedBy = MP.CreatedBy,
                                           ModifiedBy = MP.ModifiedBy,
                                           ModifiedDate = MP.ModifiedDate
                                        })
                                        .OrderByDescending(x => x.IsActive)
                                        .ThenByDescending(x => x.Date)
                                        .ToListAsync();
            return MedicalRecords;
        }

        /// <summary>
        /// get MedicalHistory
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="patientId"></param>
        /// <returns></returns>
        public async Task<List<MedicalHistoryView>> GetMedicalHistory(int orgId, long patientId)
        {
            var MedicalRecords = await (from MP in _readOnlyDbContext.MedicalHistory
                                        join UD in _readOnlyDbContext.UserDetails on MP.CreatedBy equals UD.Id
                                        where MP.PatientDetailsId == patientId && MP.OrgId == orgId && MP.StatusId == (short)Status.Active
                                        select new MedicalHistoryView
                                        {
                                            Id = MP.Id,
                                            OrgId = MP.OrgId,
                                            PatientDetailsId = MP.PatientDetailsId,
                                            Type = MP.Type,
                                            StatusId = MP.StatusId,
                                            CreatedDate = MP.CreatedDate,
                                            CreatedBy = MP.CreatedBy,
                                            ModifiedBy = MP.ModifiedBy,
                                            ModifiedDate = MP.ModifiedDate,
                                            CreatedByFirstName = UD.FirstName,
                                            CreatedBySurName = UD.SurName
                                        }).ToListAsync();
            return MedicalRecords;
        }

        /// <summary>
        /// Method to add MedicalRecordsData to MedicalRecordsData table
        /// </summary>
        /// <param name="inputMedicalProcedures"></param>
        /// <returns></returns>
        public async Task<long?> AddMedicalRecord(MedicalProcedures inputMedicalProcedures)
        {
            _updatableDBContext.MedicalProcedures.Add(inputMedicalProcedures);
            return await _updatableDBContext.SaveChangesAsync();
        }

        /// <summary>
        /// Method to Update MedicalRecordsData to MedicalRecordsData table
        /// </summary>
        /// <param name="inputMedicalProcedures"></param>
        /// <returns></returns>
        public async Task<long?> UpdateMedicalRecord(List<MedicalProcedures> inputMedicalProcedures)
        {
            _updatableDBContext.MedicalProcedures.UpdateRange(inputMedicalProcedures);
            return await _updatableDBContext.SaveChangesAsync();
        }

        /// <summary>
        /// get procedure based on ID
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<MedicalProcedures> GetExistingMedicalProceduresById(long id)
        {
            return await _readOnlyDbContext.MedicalProcedures.FirstOrDefaultAsync(s => s.Id == id);
        }

        /// <summary>
        /// Get Existing Medical History
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="patientId"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        public async Task<MedicalHistory> GetExistingMedicalHistory(int orgId, long patientId, short type)
        {
            return await _readOnlyDbContext.MedicalHistory.FirstOrDefaultAsync(s => 
                            s.PatientDetailsId == patientId && s.OrgId == orgId && 
                            s.Type == type && s.StatusId == (short)Status.Active);
        }

        /// <summary>
        /// Method to Update Verification Status to MedicalHistory table
        /// </summary>
        /// <param name="inputMedicalHistory"></param>
        /// <returns></returns>
        public async Task<long?> VerifyMedicalRecord(List<MedicalHistory> inputMedicalHistory)
        {
            _updatableDBContext.MedicalHistory.UpdateRange(inputMedicalHistory);
            return await _updatableDBContext.SaveChangesAsync();
        }

        /// <summary>
        /// Method to Retrive existing procedure based on Name
        /// </summary>
        /// <param name="patientId"></param>
        /// <param name="orgId"></param>
        /// <param name="inputValidationRequest"></param>
        /// <returns></returns>
        public async Task<List<MedicalProcedures>> FetchMedicalProcedureByName(long patientId, int orgId, short type, string search_term)
        {
            return await _readOnlyDbContext.MedicalProcedures.Where(s => 
                            s.PatientDetailsId == patientId && s.OrgId == orgId && s.StatusId == (short)Status.Active &&
                            s.Type == type && s.Procedure == search_term
                          ).ToListAsync();
        }

        /// <summary>
        /// Update Procedure Status
        /// </summary>
        /// <param name="inputMedicalProcedure"></param>
        /// <returns></returns>
        public async Task<long?> UpdateProcedureStatus(MedicalProcedures inputMedicalProcedure)
        {
            _updatableDBContext.MedicalProcedures.Update(inputMedicalProcedure);
            return await _updatableDBContext.SaveChangesAsync();
        }
    }
}
