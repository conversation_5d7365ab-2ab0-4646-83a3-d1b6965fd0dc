﻿using Capstone2.Framework.Business.Common;
using Capstone2.RestServices.Patient.Context;
using Capstone2.RestServices.Patient.Interfaces;
using Capstone2.RestServices.Patient.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Reflection;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Patient.Services
{
    public class PatientDAL : IPatientDAL
    {
        public readonly ReadOnlyPatientDBContext _readOnlyDbContext;
        public readonly UpdatablePatientDBContext _updatableDBContext;
        private IDistributedCacheHelper _redisCache;

        public PatientDAL(ReadOnlyPatientDBContext readOnlyDbContext, UpdatablePatientDBContext updatableDBContext, IDistributedCacheHelper cache)
        {
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
            _redisCache = cache;
        }

        /// <summary>
        /// Adding the new addresses for a patient
        /// </summary>
        /// <param name="addAddressList"></param>
        public async Task AddAddressList(ICollection<Address> addAddressList)
        {
            _updatableDBContext.Addresses.AddRange(addAddressList);
            await _updatableDBContext.SaveChangesAsync();
        }

        /// <summary>
        /// method to delete an address for a patient
        /// </summary>
        /// <param name="removeAddressList"></param>
        public async Task UpdateAddressList(ICollection<Address> removeAddressList)
        {
            _updatableDBContext.Addresses.UpdateRange(removeAddressList);
            await _updatableDBContext.SaveChangesAsync();
        }

        /// <summary>
        /// method to add a patient to database
        /// </summary>
        /// <param name="patientDetail"></param>
        /// <returns></returns>
        public async Task<long> AddPatientDetails(PatientDetail patientDetail)
        {
            await _updatableDBContext.PatientDetails.AddAsync(patientDetail);
            await _updatableDBContext.SaveChangesAsync();
            return (long)patientDetail.Id;
        }

        /// <summary>
        /// Method to update the patient record in PatientDetails table
        /// </summary>
        /// <param name="inputPatientDetail"></param>
        public async Task EditPatientDetails(PatientDetail inputPatientDetail)
        {
            _updatableDBContext.PatientDetails.Update(inputPatientDetail);
            await _updatableDBContext.SaveChangesAsync();
            _updatableDBContext.Entry(inputPatientDetail).State = EntityState.Modified;
        }

        /// <summary>
        /// Method to get a patent from database based on id and orgId
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<PatientDetail> GetPatientDetails(int orgId, long id)
        {
            var patientDetail = await _readOnlyDbContext.PatientDetails.AsNoTracking()
                                        .FirstOrDefaultAsync(p => p.Id == id && p.OrgId == orgId);
            return patientDetail;
        }

        public async Task<QueryResultList<PatientSearch>> ListPatient(int orgId, string orgCode, QueryModel queryModel, PatientFilterModel filterModel)
        {
            QueryResultList<PatientSearch> paginatedList = new QueryResultList<PatientSearch>();

            //POC for cache code
            string patientOrgKey = "Tbl_Patient_" + orgCode;

            List<PatientSearch> patientQuery = await _redisCache.GetFromCache<List<PatientSearch>>($"{patientOrgKey}");

            if (patientQuery == null)
            {
                patientQuery = await _readOnlyDbContext.PatientDetails.Where(x => x.OrgId == orgId).Select(x => new PatientSearch { Id = x.Id, FirstName = x.FirstName, SurName = x.SurName, Mobile = x.Mobile, HomeContact = x.HomeContact, WorkContact = x.WorkContact, RecordId = x.RecordId, PersonalEmailAddress = x.PersonalEmailAddress, DateofBirth = x.DateofBirth, StatusId = x.StatusId, PreferredName = x.PreferredName }).OrderByDescending(x => x.Id).AsNoTracking().ToListAsync();
                await _redisCache.SetIntoCache(patientQuery, $"{patientOrgKey}");
            }

            //Line to uncomment for patient search
            // List<PatientDetail> patientQuery = await _readOnlyDbContext.PatientDetails.Where(x => x.OrgId == orgId ).Select(x => new PatientDetail { Id = x.Id, FirstName = x.FirstName, SurName = x.SurName, Mobile = x.Mobile, HomeContact = x.HomeContact, WorkContact = x.WorkContact, RecordId = x.RecordId, PersonalEmailAddress = x.PersonalEmailAddress, DateofBirth = x.DateofBirth, StatusId = x.StatusId }).OrderByDescending(x => x.Id).ToListAsync();

            //IQueryable<PatientDetail> patientQuery =  _readOnlyDbContext.PatientDetails.Where(x => x.OrgId == orgId).Select(x => new PatientDetail { Id = x.Id, FirstName = x.FirstName, SurName = x.SurName, Mobile = x.Mobile, HomeContact = x.HomeContact, WorkContact = x.WorkContact, RecordId = x.RecordId, PersonalEmailAddress = x.PersonalEmailAddress, DateofBirth = x.DateofBirth, StatusId = x.StatusId }).OrderByDescending(x => x.Id);

            if (patientQuery != null)
            {
                if (!(String.IsNullOrWhiteSpace(queryModel.SearchTerm)))
                {
                    patientQuery = SearchPatients(patientQuery, queryModel.SearchTerm);
                    //patientQuery = patientQuery.Where(p => p.RecordId.ToString().Contains(queryModel.SearchTerm));
                }

                if (patientQuery != null)
                {
                    //  patientQuery = await _readOnlyDbContext.PatientDetails.Where(p => patientQuery.Select(x => x.Id).Contains(p.Id)).ToListAsync();
                    if (filterModel is not null)
                    {
                        if (filterModel.StatusId is not null && filterModel.StatusId.Any())
                        {
                            patientQuery = patientQuery.Where(patient => filterModel.StatusId.Contains(patient.StatusId)).ToList();
                        }
                    }
                    paginatedList = PaginatedResultListAsync(patientQuery, queryModel);
                }
            }

            return paginatedList;
        }

        private QueryResultList<PatientSearch> PaginatedResultListAsync(List<PatientSearch> patientQuery, QueryModel queryModel)
        {
            QueryResultList<PatientSearch> queryList = new QueryResultList<PatientSearch>();
            List<PatientSearch> paginatedList = null;
            if (queryModel.PageNumber > 0 && queryModel.PageSize > 0)
            {
                if (patientQuery.Any())
                {
                    paginatedList = patientQuery.OrderByDescending(x => x.Id).Skip((queryModel.PageNumber - 1) * queryModel.PageSize)
                 .Take(queryModel.PageSize).ToList();
                }
            }

            if (paginatedList != null)
            {
                queryList.ItemRecords = paginatedList;
                queryList.PageNumber = queryModel.PageNumber;
                queryList.PageSize = queryModel.PageSize;
                queryList.CurrentCount = paginatedList.Count();
            }
            else
            {
                queryList.ItemRecords = null;
                queryList.CurrentCount = 0;
            }
            queryList.TotalCount = patientQuery != null ? patientQuery.Count() : 0;
            queryList.PageNumber = queryModel.PageNumber;
            queryList.PageSize = queryModel.PageSize;
            patientQuery?.Clear();
            return queryList;
        }

        /// <summary>
        /// Search based on ID,contact no.s ,fistname and surname
        /// </summary>
        /// <param name="patientQuery"></param>
        /// <param name="searchTerm"></param>
        /// <returns></returns>
        private List<PatientSearch> SearchPatients(List<PatientSearch> patientQuery, string searchTerm)
        {
            string filterString = string.Empty;
            bool istwoWordSearch = Regex.IsMatch(searchTerm, @"\s");

            if (!istwoWordSearch)
            {
                CultureInfo provider = CultureInfo.InvariantCulture;
                DateTime dateTimeSearch; // 1/1/0001 12:00:00 AM 

                bool isDate = DateTime.TryParseExact(searchTerm, "dd/MM/yyyy", provider, DateTimeStyles.None, out dateTimeSearch);
                if (isDate)
                {
                    patientQuery = patientQuery.Where(x => x.DateofBirth == dateTimeSearch).ToList();
                }
                else
                {
                    patientQuery = patientQuery.Where(x => (IsNullCheck(x.FirstName).StartsWith(searchTerm, StringComparison.OrdinalIgnoreCase)
                || IsNullCheck(x.SurName).StartsWith(searchTerm, StringComparison.OrdinalIgnoreCase)
                || IsNullCheck(x.PreferredName).StartsWith(searchTerm, StringComparison.OrdinalIgnoreCase)
                || IsNullCheck(x.Mobile).Equals(searchTerm)
                || IsNullCheck(x.HomeContact).Equals(searchTerm)
                || IsNullCheck(x.WorkContact).Equals(searchTerm)
                || IsNullCheck(x.PersonalEmailAddress).Equals(searchTerm)
                || x.RecordId.ToString().Equals(searchTerm))).ToList();
                }
            }
            else
            {
                string[] search = searchTerm.Split(" ");
                patientQuery = patientQuery.Where(x => (IsNullCheck(x.FirstName).StartsWith(search[0], StringComparison.OrdinalIgnoreCase) && IsNullCheck(x.SurName).StartsWith(search[1], StringComparison.OrdinalIgnoreCase)) || (IsNullCheck(x.SurName).StartsWith(search[0], StringComparison.OrdinalIgnoreCase) && IsNullCheck(x.FirstName).StartsWith(search[1], StringComparison.OrdinalIgnoreCase))).ToList();

            }
            return patientQuery;
        }
        private string IsNullCheck(string valStr)
        {
            if (string.IsNullOrWhiteSpace(valStr))
                valStr = "";
            return valStr;
        }

        public async Task<ICollection<Address>> GetPatientAddresses(int orgId, long id)
        {
            var Addresses = await _readOnlyDbContext.Addresses.AsNoTracking().Where(x => x.PatientDetailsId == id && x.OrgId == orgId && x.StatusId == (short)Status.Active).ToListAsync();
            return Addresses;
        }

        /// <summary>
        /// Fetching the referraldetails with provider names etc
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ProviderInfo> FetchReferralProviderDetails(int orgId, long id)
        {
            var providerInfo = await (from UC in _readOnlyDbContext.UserCompanyAssocs
                                      join CD in _readOnlyDbContext.CompanyDetails on UC.CompanyId equals CD.Id
                                      join UD in _readOnlyDbContext.UserDetails on UC.UserDetailsId equals UD.Id
                                      where UC.OrgId == orgId && CD.OrgId == orgId && UD.OrgId == orgId
                                      && UC.Id == id && UC.CompanyId == CD.Id && UC.UserDetailsId == UD.Id
                                      select new ProviderInfo
                                      {
                                          Id = UC.Id,
                                          OrgId = UC.OrgId,
                                          UserDetailsId = UC.UserDetailsId,
                                          FirstName = UD.FirstName,
                                          SurName = UD.SurName,
                                          ProviderTypeId = UD.ProviderTypeId,
                                          Specialisation = UD.Specialisation,
                                          CompanyId = UC.CompanyId,
                                          CompanyName = CD.Name,
                                          ProviderNumber = UC.ProviderNumber,
                                          UserTypeId = UD.UserTypeId,
                                          CompanyTypeId = UC.CompanyTypeId
                                      }).SingleOrDefaultAsync();
            return providerInfo;
        }

        public async Task<List<UserDetail>> GetUserDetailsFromUserId(int orgId, long id)
        {
            var result = await _readOnlyDbContext.UserDetails.Where(x => x.Id == id && x.OrgId == orgId).ToListAsync();
            return result;
        }

        public async Task<CompanyDetail> GetCompanyData(int id, int orgId)
        {
            return await _readOnlyDbContext.CompanyDetails.Include(e => e.CompanyAddresses.Where(e => e.StatusId == (short)Status.Active)).Where(C1 => C1.OrgId == orgId && C1.Id == id).FirstOrDefaultAsync();
        }

        public async Task<UserDetail> GetLoggedInUser(long UserId, int orgId)
        {
            return await _readOnlyDbContext.UserDetails.Include(e => e.UserAddresses.Where(e => e.StatusId == (short)Status.Active)).Include(e => e.UserCompanyAssocs.Where(e => e.StatusId == (short)Status.Active)).Where(C1 => C1.OrgId == orgId && C1.Id == UserId).FirstOrDefaultAsync();
        }

        public async Task<PatientDetail> GetPatientDetailsForPartialUpdate(int orgId, long id)
        {
            var patientDetail = await _readOnlyDbContext.PatientDetails.Where(a => a.StatusId == (short)PatientStatus.Active && a.Id == id && a.OrgId == orgId).FirstOrDefaultAsync();
            return patientDetail;
        }

        public async Task<int> PartialUpdatePatient(IDictionary nameValuePairProperties, long? loggedInUser, PatientDetail dbPatient)
        {
            _updatableDBContext.PatientDetails.Attach(dbPatient);
            var currentValues = _updatableDBContext.Entry(dbPatient).CurrentValues;
            // var nameValuePairProperties = appointmentUpdateView.ToDictionary(a => a.PropertyName, a => a.PropertyValue);
            //foreach (var keyValue in nameValuePairProperties)
            //{
            //    var value = keyValue.Value;

            //    currentValues[keyValue.Key] = Convert.ChangeType(value, currentValues[keyValue.Key].GetType(), CultureInfo.InvariantCulture);
            //}
            var type = dbPatient.GetType();
            PropertyInfo[] p = type.GetProperties();
            foreach (string key in nameValuePairProperties.Keys)
            {
                if (p.ToList().Where(x => x.Name == key).Any())

                {
                    var currenttype = p.ToList().Find(x => x.Name == key).GetType();
                    var value = nameValuePairProperties[key];
                    //currentValues[key] = key.GetType();
                    if (key == "DateofBirth")
                    {
                        currentValues[key] = (DateTime?)value; //Convert.ChangeType(value, DateTime , CultureInfo.InvariantCulture);
                    }
                    else
                    {
                        currentValues[key] = (string?)value;
                    }

                }

            }
            if (_updatableDBContext.Entry(dbPatient).State == EntityState.Modified)
            {
                dbPatient.ModifiedDate = DateTime.UtcNow;
                dbPatient.ModifiedBy = loggedInUser;
            }
            return await _updatableDBContext.SaveChangesAsync();


        }

        /// <summary>
        /// Check for exact Duplicate patient
        /// </summary>
        /// <param name="patientdetails"></param>
        /// <returns></returns>
        public async Task<List<PatientDetail>> CheckExactDuplicate(PatientVerificationRequest patientdetails)
        {
            return await _readOnlyDbContext.PatientDetails.Where(a =>
                  a.StatusId == (short)PatientStatus.Active &&
                  a.FirstName == patientdetails.FirstName &&
                  a.SurName == patientdetails.SurName &&
                  a.DateofBirth == patientdetails.DateofBirth &&
                  (
                      (a.Mobile == patientdetails.Mobile) ||
                      (a.HomeContact == patientdetails.HomeContact) ||
                      (a.PersonalEmailAddress == patientdetails.PersonalEmailAddress)
                  )
            ).ToListAsync();
        }

        /// <summary>
        /// Check for Duplicate
        /// </summary>
        /// <param name="patientdetails"></param>
        /// <returns></returns>
        public async Task<List<PatientDetail>> CheckDuplicate(PatientVerificationRequest patientdetails)
        {
            return await _readOnlyDbContext.PatientDetails.Where(a =>
                a.StatusId == (short)PatientStatus.Active &&
                a.DateofBirth != null && a.DateofBirth == patientdetails.DateofBirth &&
                (
                    (a.Mobile == patientdetails.Mobile) ||
                    (a.HomeContact == patientdetails.HomeContact)
                )
            ).ToListAsync();
        }

        /// <summary>
        /// GetProviderDetailsByProviderID
        /// </summary>
        /// <param name="providerNumber"></param>
        /// <param name="companyID"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<UserDetail> GetProviderDetailsByProviderID(string providerNumber, int companyID, long orgId)
        {
            var providerInfo = await (from UC in _readOnlyDbContext.UserCompanyAssocs
                                      join CD in _readOnlyDbContext.CompanyDetails on UC.CompanyId equals CD.Id
                                      join UD in _readOnlyDbContext.UserDetails on UC.UserDetailsId equals UD.Id
                                      where UC.OrgId == orgId && CD.OrgId == orgId && UD.OrgId == orgId
                                      && UC.ProviderNumber == providerNumber
                                      && UC.CompanyId == companyID
                                      && UC.UserDetailsId == UD.Id
                                      select UD).FirstOrDefaultAsync();
            return providerInfo;

        }

        public async Task<AddresseeProvider> GetAddresseeProvider(long userCompanyAssocId, int orgId)
        {
            var queryList = await (from UCA in _readOnlyDbContext.UserCompanyAssocs
                                   where UCA.OrgId == orgId && UCA.StatusId == (short)Status.Active && UCA.Id == userCompanyAssocId
                                   select new AddresseeProvider
                                   {

                                       CompanyData = (_readOnlyDbContext.CompanyDetails.Include(e => e.CompanyAddresses.Where(e => e.StatusId == (short)Status.Active)).Where(C1 => C1.OrgId == orgId && C1.Id == UCA.CompanyId).FirstOrDefault()),
                                       ProviderData = (_readOnlyDbContext.UserDetails.Where(C1 => C1.OrgId == orgId && C1.Id == UCA.UserDetailsId).FirstOrDefault())
                                   }).FirstOrDefaultAsync();



            return queryList;
        }

        public async Task<PatientInfo> GetPatientDetailsFromRecordId(int orgId, long recordId)
        {
            return await _readOnlyDbContext.PatientDetails.Where(x => x.RecordId == recordId && x.OrgId == orgId)
                 .Select(x => new PatientInfo
                 {
                     Id = (x.Id == null) ? 0 : (long)x.Id,
                     RecordId = x.RecordId,
                     FirstName = x.FirstName,
                     SurName = x.SurName,
                     Mobile = x.Mobile,
                     OrgId = x.OrgId,
                     PersonalEmailAddress = x.PersonalEmailAddress,
                 }).FirstOrDefaultAsync();
        }

        public async Task<ICollection<HIServiceDetail>> GetHIServiceDetails(int orgId, long id)
        {
            var hiServiceDetails = await _readOnlyDbContext.HIServiceDetails.AsNoTracking().Where(x => x.PatientDetailsId == id && x.OrgId == orgId && x.HIServiceTypeId == (short)HIServiceType.IHI).ToListAsync();
            return hiServiceDetails;
        }

        public async Task<List<PatientInfo>> GetPatientDetailsByIHINumber(int orgId, string ihiNumber)
        {
            return await _readOnlyDbContext.PatientDetails.Where(x => x.IHINumber == ihiNumber && x.OrgId == orgId && x.StatusId == (short)PatientStatus.Active)
                 .Select(x => new PatientInfo
                 {
                     Id = (x.Id == null) ? 0 : (long)x.Id,
                     OrgId = x.OrgId,
                     FirstName=x.FirstName,           
                     SurName=x.SurName,
                 }).ToListAsync();
        }

        public async Task<long?> AddHIServiceDetail(HIServiceDetail inputHIServiceDetail)
        {
            _updatableDBContext.HIServiceDetails.Add(inputHIServiceDetail);
            return await _updatableDBContext.SaveChangesAsync();
        }

        public async Task<long?> UpdateHIServiceDetail(List<HIServiceDetail> inputHIServiceDetails)
        {
            _updatableDBContext.HIServiceDetails.UpdateRange(inputHIServiceDetails);
            return await _updatableDBContext.SaveChangesAsync();
        }

        public async Task<HIServiceDetail> GetExistingHIServiceDetailById(long id)
        {
            return await _readOnlyDbContext.HIServiceDetails.FirstOrDefaultAsync(s => s.Id == id);
        }

        public async Task<PatientDraftData> GetPatientDraftData(int orgId, long prid)
        {
            PatientDraftData patinentData = await _readOnlyDbContext.PatientDraftData
               .Where(x => x.PatientRecordId == prid && x.OrgId == orgId &&
               x.StatusId == (int)Status.Active).FirstOrDefaultAsync();

            return patinentData;


        }
    }
}
