﻿using AutoMapper;
using Capstone2.RestServices.Patient.Interfaces;
using Capstone2.RestServices.Patient.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Patient.Services
{
    public class CommBAL : ICommBAL
    {
        public readonly ICommDAL _commDAL;
        public IMapper _mapper;
        private readonly IActivityLogDAL _activityDAL;

        public CommBAL(ICommDAL commDAL, IMapper mapper, IActivityLogDAL activityDAL)
        {
            _commDAL = commDAL;
            _mapper = mapper;
            _activityDAL = activityDAL;

        }

        /// <summary>
        /// Method to Add Communication notes details
        /// </summary>
        /// <param name="inputPatientDetail"></param>
        /// <param name="orgId"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task<ApiResponse<string>> AddCommNoteBAL(CommunicationNote inputCommDetail, int orgId, long userId, long patient_id)
        {
            ApiResponse<string> apiResponse = new();
            if (inputCommDetail.PatientDetailsId != patient_id)
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Errors.Add("PatientId in Body and Path parameter are different");
                return apiResponse;
            }
            ActivityLog activityLog = new();
            if (inputCommDetail.VersionNo > 1 && inputCommDetail.CommTypeId != (short)CommType.Consult_Note_Template)
            {
                var MostRescentComm = await _commDAL.GetLastCommNotesDAL(inputCommDetail);

                var timedifference = DateTime.UtcNow - MostRescentComm.CreatedDate;
                var maxtime = TimeSpan.FromHours(48);
                int timeResult = TimeSpan.Compare(timedifference, maxtime);
                if (timeResult == -1)
                {
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Errors.Add("The Pervious Version can be still Edited. No need to add the New Version");
                    return apiResponse;

                }
                short activitylogTypeId = FetchActivityLogTypeIdfromCommNoteTypeId(MostRescentComm.CommTypeId);
                activityLog = await _activityDAL.FetchActivtyLogFromEntityId(patient_id, orgId, MostRescentComm.Id, activitylogTypeId);
                if ((ActivityLogType)activitylogTypeId == ActivityLogType.Consult_Note_Template)
                {
                    activityLog.Title = EnumExtensions.GetDescription((ActivityLogType)activitylogTypeId);
                }
                else
                {
                    activityLog.Title = EnumExtensions.GetDescription((ActivityLogType)activitylogTypeId) + " v" + inputCommDetail.VersionNo;
                }
            }
            else
            {
                activityLog = CreateActivityLogEntry(patient_id, inputCommDetail, orgId, userId);

            }
            inputCommDetail.OrgId = orgId;
            inputCommDetail.ModifiedDate = DateTime.UtcNow;
            inputCommDetail.ModifiedBy = userId;
            int rows = await _commDAL.AddCommNotesDAL(inputCommDetail);
            if (rows > 0)
            {
                activityLog.EntityId = inputCommDetail.Id;
                if (inputCommDetail.VersionNo > 1 && inputCommDetail.CommTypeId != (short)CommType.Consult_Note_Template)
                {
                    activityLog.ModifiedBy = userId;
                    activityLog.ModifiedDate = DateTime.UtcNow;
                    if (inputCommDetail.Notes is not null)
                    {
                        //if (inputCommDetail.Notes.Length <= 100)
                        //{
                        activityLog.Description = inputCommDetail.Notes;
                        //}
                        //else
                        //{
                        //    activityLog.Description = inputCommDetail.Notes.Substring(0, 100);
                        //}
                    }
                    await _activityDAL.UpdateActivityLog(activityLog);
                }
                else
                {
                    await _activityDAL.AddActivityLog(activityLog);
                }
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";
                apiResponse.Result = Convert.ToString(inputCommDetail.Id);
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Errors.Add("Communication Note cannot be created at this time.");
            }

            return apiResponse;
        }

        private short FetchActivityLogTypeIdfromCommNoteTypeId(short commTypeId)
        {
            short activityLogTypeId=0;
            switch (commTypeId)
            {
                case (short)CommType.Quick_Consult_Note:
                    activityLogTypeId = (short)ActivityLogType.Quick_Consult_Note;
                    break;
                case (short)CommType.Phone:
                    activityLogTypeId = (short)ActivityLogType.Phone;
                    break;
                case (short)CommType.Email:
                    activityLogTypeId = (short)ActivityLogType.Email;                   
                    break;
                case (short)CommType.Consult_Note_Template:
                    activityLogTypeId = (short)ActivityLogType.Consult_Note_Template;
                    break;
                case (short)CommType.Pathology_Request:
                    activityLogTypeId = (short)ActivityLogType.Pathology_Request;
                    break;
            }
            return activityLogTypeId;
        }

        /// <summary>
        /// Method to fetch a Communication Notes based on Id
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<CommunicationNote>> GetCommNote(int orgId, long id, long patient_id)
        {
            ApiResponse<CommunicationNote> apiResponse = new();
            CommunicationNote commNote = await _commDAL.GetCommNoteDAL(orgId, id);
            if (commNote.PatientDetailsId != patient_id)
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Errors.Add("PatientId in communication note  and Path parameter are different");
                return apiResponse;
            }

            apiResponse.Result = commNote;
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            return apiResponse;
        }

        /// <summary>
        /// Method to get list of communication notes
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        public async Task<ApiResponse<QueryResultList<ListCommunicationNote>>> ListCommNotes(int orgId, QueryModel queryModel, long patient_id)
        {
            ApiResponse<QueryResultList<ListCommunicationNote>> apiResponse = new();
            CommNotesFilterModel filterModel = PrepareFilterParameters(queryModel.Filter);

            QueryResultList<ListCommunicationNote> commList = await _commDAL.ListCommNotesDAL(orgId, queryModel, filterModel, patient_id);

            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result = commList;
            return apiResponse;
        }

        /// <summary>
        /// method to generate the filter parameters from the request
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        private CommNotesFilterModel PrepareFilterParameters(string filter)
        {
            if (!(String.IsNullOrEmpty(filter)))
            {
                return JsonConvert.DeserializeObject<CommNotesFilterModel>(filter);
            }
            else
            {
                return null;
            }
        }


        public async Task<ApiResponse<string>> EditCommNoteBAL(CommunicationNote inputCommDetail, int orgId, long modifierId, long id, long patient_id)
        {
            ApiResponse<string> apiResponse = new();
            inputCommDetail.OrgId = orgId;
            inputCommDetail.ModifiedDate = DateTime.UtcNow;
            inputCommDetail.ModifiedBy = modifierId;
            CommunicationNote commFromDb = await _commDAL.GetCommNoteDAL(orgId, id);
            if (commFromDb == null)
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Errors.Add("Communication Notes for given id is not found");
                return apiResponse;
            }
            var timedifference = DateTime.UtcNow - commFromDb.CreatedDate;
            var maxtime = TimeSpan.FromHours(48);
            int timeResult = TimeSpan.Compare(timedifference, maxtime);
            if (timeResult == 1 && commFromDb.CommTypeId != (short)CommType.Consult_Note_Template && commFromDb.CommTypeId != (short)CommType.Pathology_Request)
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Errors.Add("More then 48 hours from Creation.The communication note can not be edited");
                return apiResponse;

            }
            int rows = await _commDAL.EditCommNotesDAL(inputCommDetail);
            if(rows > 0)
            {
                short activitylogTypeId = FetchActivityLogTypeIdfromCommNoteTypeId(inputCommDetail.CommTypeId);
                ActivityLog activityLog = await _activityDAL.FetchActivtyLogFromEntityId(patient_id, orgId, inputCommDetail.Id, activitylogTypeId);
                if (activityLog.ActivityLogTypeId == (short)ActivityLogType.Consult_Note_Template && inputCommDetail.FileDetailsId is not null)
                {
                    activityLog.ActivityStatusId = (short)ConsultNoteStatus.Locked;
                    activityLog.FileDetailsId = inputCommDetail.FileDetailsId;
                }
                activityLog.ModifiedBy = modifierId;
                activityLog.ModifiedDate = DateTime.UtcNow;
                activityLog.EpisodesId = inputCommDetail.EpisodesId;

                if (inputCommDetail.Notes is not null)
                {
                    //if (inputCommDetail.Notes.Length <= 100)
                    //{
                        activityLog.Description = inputCommDetail.Notes;
                    //}
                    //else
                    //{
                    //    activityLog.Description = inputCommDetail.Notes.Substring(0, 100);
                    //}
                }
                await _activityDAL.UpdateActivityLog(activityLog);
            }
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result = "Success";
            return apiResponse;
        }

        /// <summary>
        /// Method to fetch a All versions of Communication Notes based on Id
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<List<CommunicationNote>>> GetCommNoteAllVersion(int orgId, long id, long patient_id)
        {
            ApiResponse<List<CommunicationNote>> apiResponse = new();
            List<CommunicationNote> commNote = await _commDAL.GetCommNoteAllVersionDAL(orgId, id);
            if (commNote[0].PatientDetailsId != patient_id)
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Errors.Add("PatientId in communication note  and Path parameter are different");
                return apiResponse;
            }

            apiResponse.Result = commNote;
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            return apiResponse;
        }
        /// <summary>
        /// Method to create an ActivityLog object
        /// </summary>
        /// <param name="activityLogDB"></param>
        /// <param name="activityLogOp"></param>
        /// <param name="lstVariableJSON"></param>
        /// <param name="id"></param>
        /// <param name="patientDetailsId"></param>
        /// <param name="appointmentStatus"></param>
        /// <returns></returns>
        public ActivityLog CreateActivityLogEntry(long? patientDetailsId, CommunicationNote commNote, int orgId, long userId)
        {
            ActivityLog activityLog = new();
            activityLog.PatientDetailsId = (long)patientDetailsId;
            activityLog.OrgId = orgId;
            activityLog.EpisodesId = (commNote is not null) ? commNote.EpisodesId: null;
            if (commNote.Notes is not null)
            {
                //if(commNote.Notes.Length <= 100)
                //{
                    activityLog.Description = commNote.Notes;
                //}
                //else
                //{
                //    activityLog.Description = commNote.Notes.Substring(0, 100);
                //}
            }
            activityLog.CreatedDate = DateTime.UtcNow;
            activityLog.ModifiedDate = DateTime.UtcNow;
            activityLog.ModifiedBy = userId;
       
            activityLog.ActivityLogTypeId = FetchActivityLogTypeIdfromCommNoteTypeId(commNote.CommTypeId);
            if(activityLog.ActivityLogTypeId == (short)ActivityLogType.Consult_Note_Template && commNote.FileDetailsId is null){

                activityLog.ActivityStatusId = (short)ConsultNoteStatus.Unlocked;
            }
            else if(activityLog.ActivityLogTypeId == (short)ActivityLogType.Consult_Note_Template && commNote.FileDetailsId is not null)
            {
                activityLog.ActivityStatusId = (short)ConsultNoteStatus.Locked;
                activityLog.FileDetailsId = commNote.FileDetailsId;
            }

            activityLog.Title = EnumExtensions.GetDescription((ActivityLogType)activityLog.ActivityLogTypeId)+" v" + commNote.VersionNo;

            return activityLog;
        }
      
    }
}
