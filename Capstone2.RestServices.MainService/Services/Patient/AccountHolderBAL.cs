﻿using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.Patient.Interfaces;
using Capstone2.RestServices.Patient.Models;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Transactions;

namespace Capstone2.RestServices.Patient.Services
{
    public class AccountHolderBAL : IAccountHolderBAL
    {
        public readonly IAccountHolderDAL _accountHolderDAL;

        public AccountHolderBAL(IAccountHolderDAL accountHolderDAL)
        {
            _accountHolderDAL = accountHolderDAL;
        }

        /// <summary>
        /// Method to save a entry in AccountHolderData
        /// </summary>
        /// <param name="AccountHolderPayload"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> AddAccountHolderData(BaseHttpRequestContext baseHttpRequestContext, long patientId, AccountHolder inputAccountHolderData)
        {
            var orgId = baseHttpRequestContext.OrgId;
            var userId = baseHttpRequestContext.UserId;

            inputAccountHolderData.PatientDetailsId = patientId;
            inputAccountHolderData.OrgId = orgId;
            inputAccountHolderData.CreatedBy = userId;
            inputAccountHolderData.CreatedDate = DateTime.UtcNow;
            inputAccountHolderData.StatusId = (short)Status.Active;

            // Address
            if (inputAccountHolderData?.AccountHolderAddresses?.Any() ?? false)
            {
                inputAccountHolderData.AccountHolderAddresses = MapAccountHolderAddress(inputAccountHolderData, orgId, userId);
                inputAccountHolderData.AccountHolderAddresses.ToList().ForEach(a => { a.OrgId = orgId; a.CreatedBy = userId; a.CreatedDate = DateTime.UtcNow; a.StatusId = (short)Status.Active; });
            }

            // AccountHolderAssocs
            if (inputAccountHolderData?.AccountHolderAssocs?.Any() ?? false)
            {
                inputAccountHolderData.AccountHolderAssocs = MapAccountHolderAssocs(inputAccountHolderData, orgId, userId);
                inputAccountHolderData.AccountHolderAssocs.ToList().ForEach(a => { a.OrgId = orgId; a.CreatedBy = userId; a.CreatedDate = DateTime.UtcNow; a.StatusId = (short)Status.Active; });
            }

            // AccountHolderHealthfundAssocs
            if (inputAccountHolderData.AccountHolderHealthfundAssocs != null)
            {
                if (inputAccountHolderData.IsHealthFund ?? false)
                {
                    inputAccountHolderData.AccountHolderHealthfundAssocs.OrgId = orgId;
                    inputAccountHolderData.AccountHolderHealthfundAssocs.CreatedBy = userId;
                    inputAccountHolderData.AccountHolderHealthfundAssocs.CreatedDate = DateTime.UtcNow;
                    inputAccountHolderData.AccountHolderHealthfundAssocs.StatusId = (short)Status.Active;
                }
                else { inputAccountHolderData.AccountHolderHealthfundAssocs = null; }
            }

            // AccountHolderParentGuardianAssocs
            if (inputAccountHolderData.AccountHolderParentGuardianAssocs != null)
            {
                if (inputAccountHolderData.IsParentGuardian ?? false)
                {
                    inputAccountHolderData.AccountHolderParentGuardianAssocs.OrgId = orgId;
                    inputAccountHolderData.AccountHolderParentGuardianAssocs.CreatedBy = userId;
                    inputAccountHolderData.AccountHolderParentGuardianAssocs.CreatedDate = DateTime.UtcNow;
                    inputAccountHolderData.AccountHolderParentGuardianAssocs.StatusId = (short)Status.Active;
                }
                else { inputAccountHolderData.AccountHolderParentGuardianAssocs = null; }
            }

            // AccountHolderWorkersCompAssocs
            if (inputAccountHolderData.AccountHolderWorkersCompAssocs != null)
            {
                if (inputAccountHolderData.IsWorkersComp ?? false)
                {
                    inputAccountHolderData.AccountHolderWorkersCompAssocs.OrgId = orgId;
                    inputAccountHolderData.AccountHolderWorkersCompAssocs.CreatedBy = userId;
                    inputAccountHolderData.AccountHolderWorkersCompAssocs.CreatedDate = DateTime.UtcNow;
                    inputAccountHolderData.AccountHolderWorkersCompAssocs.StatusId = (short)Status.Active;
                }
                else { inputAccountHolderData.AccountHolderWorkersCompAssocs = null; }
            }

            DateTime.SpecifyKind(inputAccountHolderData.CreatedDate, DateTimeKind.Utc);
            var id = await _accountHolderDAL.AddAccountHolderDataAsync(inputAccountHolderData);
            if (id > 0)
            {
                ApiResponse<long?> apiResponse = new()
                {
                    StatusCode = StatusCodes.Status200OK,
                    Message = "Success",
                    Result = id
                };
                return apiResponse;
            }
            else
            {
                ApiResponse<long?> apiResponse = new()
                {
                    StatusCode = StatusCodes.Status400BadRequest,
                    Message = "Failure",
                    Result = null
                };
                return apiResponse;
            }
        }

        /// <summary>
        /// Get AccountHolderDataby ID
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<AccountHolder>> GetAccountHolderData(BaseHttpRequestContext baseHttpRequestContext, long patientId)
        {
            ApiResponse<AccountHolder> apiResposne = new();
            var orgId = baseHttpRequestContext.OrgId;

            var accountHolderFromDB = await _accountHolderDAL.FetchAccountHolderDetails(orgId, patientId);

            return new ApiResponse<AccountHolder>
            {
                StatusCode = StatusCodes.Status200OK,
                Message = "Success",
                Result = accountHolderFromDB
            };
        }

        /// <summary>
        /// Edit AccountHolder
        /// </summary>
        /// <param name="id"></param>
        /// <param name="inputAccountHolder"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<string>> EditAccountHolderData(BaseHttpRequestContext baseHttpRequestContext, long patientId, long accountHolderId, AccountHolder inputAccountHolderData)
        {
            ApiResponse<string> apiResponse = new();
            var orgId = baseHttpRequestContext.OrgId;
            var userId = baseHttpRequestContext.UserId;

            var appointmentFromDb = await _accountHolderDAL.GetAccountHolder(orgId, accountHolderId);

            inputAccountHolderData.Id = accountHolderId;
            inputAccountHolderData.PatientDetailsId = patientId;
            inputAccountHolderData.OrgId = orgId;
            inputAccountHolderData.ModifiedDate = DateTime.UtcNow;
            inputAccountHolderData.ModifiedBy = userId;
            inputAccountHolderData.CreatedBy = appointmentFromDb.CreatedBy;
            inputAccountHolderData.CreatedDate = appointmentFromDb.CreatedDate;

            // AccountHolderAddress
            if (inputAccountHolderData.AccountHolderAddresses.Any())
            {
                inputAccountHolderData.AccountHolderAddresses = MapAccountHolderAddress(inputAccountHolderData, orgId, userId);
                inputAccountHolderData.AccountHolderAddresses.ToList().ForEach(a => { a.OrgId = orgId; a.AccountHolderId = appointmentFromDb.Id; });
            }

            // AccountHolderAssocs
            if (inputAccountHolderData.AccountHolderAssocs.Any())
            {
                inputAccountHolderData.AccountHolderAssocs = MapAccountHolderAssocs(inputAccountHolderData, orgId, userId);
                inputAccountHolderData.AccountHolderAssocs.ToList().ForEach(a => { a.OrgId = orgId; a.AccountHolderId = appointmentFromDb.Id; });
            }

            // AccountHolderHealthfundAssocs
            if (inputAccountHolderData.AccountHolderHealthfundAssocs != null)
            {
                inputAccountHolderData.AccountHolderHealthfundAssocs.AccountHolderId = appointmentFromDb.Id;
                inputAccountHolderData.AccountHolderHealthfundAssocs.OrgId = orgId;
            }

            // AccountHolderParentGuardianAssocs
            if (inputAccountHolderData.AccountHolderParentGuardianAssocs != null)
            {
                inputAccountHolderData.AccountHolderParentGuardianAssocs.AccountHolderId = appointmentFromDb.Id;
                inputAccountHolderData.AccountHolderParentGuardianAssocs.OrgId = orgId;
            }

            // AccountHolderWorkersCompAssocs
            if (inputAccountHolderData.AccountHolderWorkersCompAssocs != null)
            {
                inputAccountHolderData.AccountHolderWorkersCompAssocs.AccountHolderId = appointmentFromDb.Id;
                inputAccountHolderData.AccountHolderWorkersCompAssocs.OrgId = orgId;
            }

            using (var transaction = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
            {
                inputAccountHolderData.AccountHolderAssocs = await EditAccountHolderAssocs(inputAccountHolderData.AccountHolderAssocs, accountHolderId, orgId, userId);

                inputAccountHolderData.AccountHolderAddresses = await EditAccountHolderAddress(inputAccountHolderData.AccountHolderAddresses, accountHolderId, orgId, userId);

                inputAccountHolderData.AccountHolderHealthfundAssocs = EditAccountHolderHealthfundAssocs(inputAccountHolderData, userId);

                inputAccountHolderData.AccountHolderParentGuardianAssocs = EditAccountHolderParentGuardianAssocs(inputAccountHolderData, userId);

                inputAccountHolderData.AccountHolderWorkersCompAssocs = EditAccountHolderWorkersCompAssocs(inputAccountHolderData, userId);

                await _accountHolderDAL.UpdateAccountHolderData(inputAccountHolderData);

                apiResponse.Result = "Success";
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";

                transaction.Complete();
            }

            return apiResponse;
        }

        private ICollection<AccountHolderAssocs> MapAccountHolderAssocs(AccountHolder inputAccountHolderData, int orgId, long userId)
        {
            if (inputAccountHolderData?.AccountHolderAssocs?.Any() ?? false)
            {
                foreach (var accountHolder in inputAccountHolderData.AccountHolderAssocs)
                {
                    switch (accountHolder.AccountHolderType)
                    {
                        case 200: if (!(inputAccountHolderData.IsMedicare ?? false)) { inputAccountHolderData.AccountHolderAssocs.Remove(accountHolder); } break;
                        case 203: if (!(inputAccountHolderData.IsDva ?? false)) { inputAccountHolderData.AccountHolderAssocs.Remove(accountHolder); } break;
                        case 204: if (!(inputAccountHolderData.IsAdf ?? false)) { inputAccountHolderData.AccountHolderAssocs.Remove(accountHolder); } break;
                        case 205: if (!(inputAccountHolderData.IsPension ?? false)) { inputAccountHolderData.AccountHolderAssocs.Remove(accountHolder); } break;
                        case 206: if (!(inputAccountHolderData.IsHealthCareCard ?? false)) { inputAccountHolderData.AccountHolderAssocs.Remove(accountHolder); } break;
                        case 207: if (!(inputAccountHolderData.IsOther ?? false)) { inputAccountHolderData.AccountHolderAssocs.Remove(accountHolder); } break;
                        default: break;
                    }
                }
                return inputAccountHolderData.AccountHolderAssocs;
            }
            return null;
        }

        private ICollection<AccountHolderAddresses> MapAccountHolderAddress(AccountHolder inputAccountHolderData, int orgId, long userId)
        {
            if (inputAccountHolderData?.AccountHolderAddresses?.Any() ?? false)
            {
                foreach (var accountHolder in inputAccountHolderData.AccountHolderAddresses)
                {
                    switch (accountHolder.AccountHolderType)
                    {
                        case 201: if (!(inputAccountHolderData.IsHealthFund ?? false)) { inputAccountHolderData.AccountHolderAddresses.Remove(accountHolder); } break;
                        case 202: if (!(inputAccountHolderData.IsParentGuardian ?? false)) { inputAccountHolderData.AccountHolderAddresses.Remove(accountHolder); } break;
                        case 203: if (!(inputAccountHolderData.IsDva ?? false)) { inputAccountHolderData.AccountHolderAddresses.Remove(accountHolder); } break;
                        case 204: if (!(inputAccountHolderData.IsAdf ?? false)) { inputAccountHolderData.AccountHolderAddresses.Remove(accountHolder); } break;
                        case 205: if (!(inputAccountHolderData.IsPension ?? false)) { inputAccountHolderData.AccountHolderAddresses.Remove(accountHolder); } break;
                        case 206: if (!(inputAccountHolderData.IsHealthCareCard ?? false)) { inputAccountHolderData.AccountHolderAddresses.Remove(accountHolder); } break;
                        case 207: if (!(inputAccountHolderData.IsOther ?? false)) { inputAccountHolderData.AccountHolderAddresses.Remove(accountHolder); } break;
                        case 208: if (!(inputAccountHolderData.IsWorkersComp ?? false)) { inputAccountHolderData.AccountHolderAddresses.Remove(accountHolder); } break;
                        default: break;
                    }
                }
                return inputAccountHolderData.AccountHolderAddresses;
            }
            return null;
        }

        private async Task<List<AccountHolderAssocs>> EditAccountHolderAssocs(ICollection<AccountHolderAssocs> inputControlList, long id, int orgId, long userId)
        {
            List<AccountHolderAssocs> addControlList = new();

            var removeUserList = await _accountHolderDAL.GetAccountHolderAssocs(orgId, id);

            foreach (var control in inputControlList)
            {
                if (control.Id > 0)
                {
                    var existingobj = removeUserList.FirstOrDefault(x => x.Id == control.Id);
                    removeUserList.Remove(existingobj);
                    control.ModifiedDate = DateTime.UtcNow;
                    control.ModifiedBy = userId;
                    control.StatusId = (short)Status.Active;
                    addControlList.Add(control);
                }
                else
                {
                    var userAssocDB = removeUserList.Where(x => x.AccountHolderType == control.AccountHolderType).FirstOrDefault();
                    if (userAssocDB is null)
                    {
                        control.CreatedDate = DateTime.UtcNow;
                        control.CreatedBy = userId;
                        control.StatusId = (short)Status.Active;
                        addControlList.Add(control);
                    }
                    else
                    {
                        if (userAssocDB.StatusId == (short)Status.Deleted)
                        {
                            userAssocDB.StatusId = (short)Status.Active;
                            userAssocDB.ModifiedDate = DateTime.UtcNow;
                            userAssocDB.ModifiedBy = userId;
                            addControlList.Add(userAssocDB);
                        }
                        removeUserList.Remove(userAssocDB);
                    }
                }
            }

            foreach (var userAssoc in removeUserList.Where(x => x.StatusId == (short)Status.Active))
            {
                userAssoc.StatusId = (short)Status.Deleted;
                userAssoc.ModifiedDate = DateTime.UtcNow;
                userAssoc.ModifiedBy = userId;
            }

            addControlList = addControlList.Concat(removeUserList).ToList();
            return addControlList;
        }

        private async Task<List<AccountHolderAddresses>> EditAccountHolderAddress(ICollection<AccountHolderAddresses> inputControlList, long id, int orgId, long userId)
        {
            List<AccountHolderAddresses> addControlList = new();

            var removeUserList = await _accountHolderDAL.GetAccountHolderAddress(orgId, id);

            foreach (var control in inputControlList)
            {
                if (control.Id > 0)
                {
                    var existingobj = removeUserList.FirstOrDefault(x => x.Id == control.Id);
                    removeUserList.Remove(existingobj);
                    control.ModifiedDate = DateTime.UtcNow;
                    control.ModifiedBy = userId;
                    control.StatusId = (short)Status.Active;
                    addControlList.Add(control);
                }
                else
                {
                    var userAssocDB = removeUserList.Where(x => x.AccountHolderType == control.AccountHolderType).FirstOrDefault();
                    if (userAssocDB is null)
                    {
                        control.CreatedDate = DateTime.UtcNow;
                        control.CreatedBy = userId;
                        control.StatusId = (short)Status.Active;
                        addControlList.Add(control);
                    }
                    else
                    {
                        if (userAssocDB.StatusId == (short)Status.Deleted)
                        {
                            userAssocDB.StatusId = (short)Status.Active;
                            userAssocDB.ModifiedDate = DateTime.UtcNow;
                            userAssocDB.ModifiedBy = userId;
                            addControlList.Add(userAssocDB);
                        }
                        removeUserList.Remove(userAssocDB);
                    }
                }
            }

            foreach (var userAssoc in removeUserList.Where(x => x.StatusId == (short)Status.Active))
            {
                userAssoc.StatusId = (short)Status.Deleted;
                userAssoc.ModifiedDate = DateTime.UtcNow;
                userAssoc.ModifiedBy = userId;
            }

            addControlList = addControlList.Concat(removeUserList).ToList();
            return addControlList;
        }

        private AccountHolderHealthfundAssocs EditAccountHolderHealthfundAssocs(AccountHolder accountHolder, long userId)
        {
            if (accountHolder.AccountHolderHealthfundAssocs == null) { return null; }

            if (accountHolder.AccountHolderHealthfundAssocs.Id > 0)
            {
                accountHolder.AccountHolderHealthfundAssocs.ModifiedDate = DateTime.UtcNow;
                accountHolder.AccountHolderHealthfundAssocs.ModifiedBy = userId;
            }
            else
            {
                accountHolder.AccountHolderHealthfundAssocs.CreatedDate = DateTime.UtcNow;
                accountHolder.AccountHolderHealthfundAssocs.CreatedBy = userId;
            }
            accountHolder.AccountHolderHealthfundAssocs.StatusId = (accountHolder.IsHealthFund ?? false) ? (short)Status.Active : (short)Status.Deleted;
            return accountHolder.AccountHolderHealthfundAssocs;
        }

        private AccountHolderParentGuardianAssocs EditAccountHolderParentGuardianAssocs(AccountHolder accountHolder, long userId)
        {
            if (accountHolder.AccountHolderParentGuardianAssocs == null) { return null; }

            if (accountHolder.AccountHolderParentGuardianAssocs.Id > 0)
            {
                accountHolder.AccountHolderParentGuardianAssocs.ModifiedDate = DateTime.UtcNow;
                accountHolder.AccountHolderParentGuardianAssocs.ModifiedBy = userId;
            }
            else
            {
                accountHolder.AccountHolderParentGuardianAssocs.CreatedDate = DateTime.UtcNow;
                accountHolder.AccountHolderParentGuardianAssocs.CreatedBy = userId;
            }
            accountHolder.AccountHolderParentGuardianAssocs.StatusId = (accountHolder.IsParentGuardian ?? false) ? (short)Status.Active : (short)Status.Deleted;
            return accountHolder.AccountHolderParentGuardianAssocs;
        }

        private AccountHolderWorkersCompAssocs EditAccountHolderWorkersCompAssocs(AccountHolder accountHolder, long userId)
        {
            if (accountHolder.AccountHolderWorkersCompAssocs == null) { return null; }

            if (accountHolder.AccountHolderWorkersCompAssocs.Id > 0)
            {
                accountHolder.AccountHolderWorkersCompAssocs.ModifiedDate = DateTime.UtcNow;
                accountHolder.AccountHolderWorkersCompAssocs.ModifiedBy = userId;
            }
            else
            {
                accountHolder.AccountHolderWorkersCompAssocs.CreatedDate = DateTime.UtcNow;
                accountHolder.AccountHolderWorkersCompAssocs.CreatedBy = userId;
            }
            accountHolder.AccountHolderWorkersCompAssocs.StatusId = (accountHolder.IsWorkersComp ?? false) ? (short)Status.Active : (short)Status.Deleted;
            return accountHolder.AccountHolderWorkersCompAssocs;
        }

        public async Task<ApiResponse<ClaimantPatientDetailInfo>> GetAccountHolderInfo(BaseHttpRequestContext baseHttpRequestContext, long patient_id)
        {
            ApiResponse<ClaimantPatientDetailInfo> apiResposne = new();
            var orgId = baseHttpRequestContext.OrgId;

            ClaimantPatientDetailInfo accountHolderFromDB = await _accountHolderDAL.GetAccountHolderInfo(orgId, patient_id);

            return new ApiResponse<ClaimantPatientDetailInfo>
            {
                StatusCode = StatusCodes.Status200OK,
                Message = "Success",
                Result = accountHolderFromDB
            };
        }

        public async Task<ApiResponse<string>> UpdateIHI(BaseHttpRequestContext baseHttpRequestContext, long patientId, Dictionary<string, object> request)
        {
            try
            {
                if (!request.ContainsKey("ihiNumber"))
                {
                    throw new Exception("IHI Number is Required to update IHI number");
                }

                await _accountHolderDAL.UpdateIHI(patientId, baseHttpRequestContext.UserId, request["ihiNumber"].ToString());
                return new ApiResponse<string>
                {
                    StatusCode = StatusCodes.Status200OK,
                    Message = "Success",
                    Result = null
                };
            }
            catch (Exception ex)
            {
                return new ApiResponse<string>
                {
                    StatusCode = StatusCodes.Status400BadRequest,
                    Message = "Error",
                    Result = null,
                    Errors = new List<string> { ex.Message }
                };
            }
        }
    }
}
