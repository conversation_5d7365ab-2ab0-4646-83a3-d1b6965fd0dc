﻿using AutoMapper;
using Capstone2.Framework.Business.Common;
using Capstone2.Framework.RestApi;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.Framework.RestApi.Utility;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.Patient.Interfaces;
using Capstone2.RestServices.Patient.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Globalization;
using System.Linq;
using System.Reflection;
using System.Runtime.InteropServices;
using System.ServiceModel.Channels;
using System.Text;
using System.Threading.Tasks;
using System.Transactions;
using static Capstone2.RestServices.Patient.Models.PatientForLetter;

namespace Capstone2.RestServices.Patient.Services
{
    public class PatientBAL : IPatientBAL
    {
        public readonly AppSettings _appSettings;
        public readonly IPatientDAL _patientDAL;
        public readonly ICommDAL _commDAL;
        public readonly IReferralDAL _referralDAL;
        public readonly IMediaLibraryDAL _mediaLibraryDAL;
        public readonly IMedicalRecordsDAL _medicalRecordsDAL;
        public readonly IMediaLibraryBAL _mediaLibraryBAL;
        public readonly IHealthCareTeamDAL _healthCareTeamDAL;
        private readonly IActivityLogDAL _activityDAL;
        public readonly IAccountHolderDAL _accountHolderDAL;
        private readonly ILogger<PatientBAL> _logger;
        private IASBMessageSenderHelper _asbMessageSenderHelper;
        private readonly IConfiguration _configuration;
        public readonly IHealthDetailsDAL _healthDetailsDAL;
        private IDistributedCacheHelper _redisCache;

        public IMapper _mapper;

        public PatientBAL(IOptions<AppSettings> appSettings, IPatientDAL patientDAL, ICommDAL commDAL, IMapper mapper, IReferralDAL referralDAL, IHealthCareTeamDAL healthCareTeamDAL, IMediaLibraryDAL mediaLibraryDAL, IMediaLibraryBAL mediaLibraryBAL, IActivityLogDAL activityDAL, IAccountHolderDAL accountHolderDAL, ILogger<PatientBAL> logger, IConfiguration configuration, IASBMessageSenderHelper asbMessageSenderHelper, IMedicalRecordsDAL medicalRecordsDAL, IHealthDetailsDAL healthDetailsDAL, IDistributedCacheHelper redisCache)
        {
            _appSettings = appSettings.Value;
            _patientDAL = patientDAL;
            _mapper = mapper;
            _referralDAL = referralDAL;
            _healthCareTeamDAL = healthCareTeamDAL;
            _commDAL = commDAL;
            _mediaLibraryDAL = mediaLibraryDAL;
            _mediaLibraryBAL = mediaLibraryBAL;
            _activityDAL = activityDAL;
            _accountHolderDAL = accountHolderDAL;
            _logger = logger;
            this._asbMessageSenderHelper = asbMessageSenderHelper;
            this._configuration = configuration;
            _medicalRecordsDAL = medicalRecordsDAL;
            _healthDetailsDAL = healthDetailsDAL;
            _redisCache = redisCache;
        }

        /// <summary>
        /// Method to Add patient details
        /// </summary>
        /// <param name="inputPatientDetail"></param>
        /// <param name="orgId"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long>> AddPatientDetailsBAL(PatientDetail patientDetail, BaseHttpRequestContext baseHttpRequestContext)
        {
            int orgId = baseHttpRequestContext.OrgId;
            long userId = baseHttpRequestContext.UserId;
            ApiResponse<long> apiResponse = new();
            List<ActivityLog> lstActivityLog = new();

            if (patientDetail != null && patientDetail.Addresses.Any())
            {
                patientDetail.Addresses.ToList().ForEach(a => { a.OrgId = orgId; a.ModifiedBy = userId; a.StatusId = (short)Status.Active; });
            }

            patientDetail.OrgId = orgId;
            patientDetail.ModifiedBy = userId;

            long patientid = await _patientDAL.AddPatientDetails(patientDetail);
            if (patientid > 0)
            {
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";
                apiResponse.Result = patientid;

                await StorePatientSearchMessage(baseHttpRequestContext.OrgCode);
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Patient cannot be created at this time.");
            }

            return apiResponse;
        }

        public async Task<ApiResponse<string>> EditPatientDetails(long id, PatientDetail inputPatientDetail, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<string> apiResponse = new();
            inputPatientDetail.OrgId = baseHttpRequestContext.OrgId;
            inputPatientDetail.ModifiedDate = DateTime.UtcNow;
            inputPatientDetail.ModifiedBy = baseHttpRequestContext.UserId;

            PatientDetail patientFromDb = await _patientDAL.GetPatientDetails(baseHttpRequestContext.OrgId, id);
            bool cacheUpdate = false;
            if (inputPatientDetail.StatusId != patientFromDb.StatusId)
            {
                var perm = await _activityDAL.GetPermissionOnRoleId(baseHttpRequestContext.OrgId, baseHttpRequestContext.RoleId, (short)ApiPermission.Change_Status_Patient);
                if (perm.IsAllowed == false)
                {
                    apiResponse.StatusCode = StatusCodes.Status403Forbidden;
                    apiResponse.Errors.Add("Not an Authorized User");
                    apiResponse.Result = null;
                    return apiResponse;
                }
            }

            if (inputPatientDetail != null && inputPatientDetail.Addresses.Any())
            {
                inputPatientDetail.Addresses.ToList().ForEach(a => { a.OrgId = baseHttpRequestContext.OrgId; a.ModifiedBy = baseHttpRequestContext.UserId; a.PatientDetailsId = id; });
            }

            var removeAddressList = await _patientDAL.GetPatientAddresses(baseHttpRequestContext.OrgId, id);
            var inputAddressList = inputPatientDetail.Addresses;

            inputPatientDetail.Addresses = null;

            bool isASBChange = false; string fullName = string.Empty;
            bool ispatientUpdate = false;
            List<Address> addrList = EditAddresses(removeAddressList, inputAddressList);

            inputPatientDetail.Addresses = addrList;
            fullName = $"{inputPatientDetail.FirstName} {inputPatientDetail.SurName}";
            if (!fullName.Equals($"{patientFromDb.FirstName} {patientFromDb.SurName}", StringComparison.OrdinalIgnoreCase))
            {
                ispatientUpdate = true;
                isASBChange = true;
                cacheUpdate = true;
            }
            if (!ispatientUpdate && (inputPatientDetail.DateofBirth != null && patientFromDb.DateofBirth != null && inputPatientDetail.DateofBirth != patientFromDb.DateofBirth) || (inputPatientDetail.DateofBirth is null && patientFromDb.DateofBirth != null) || (patientFromDb.DateofBirth is null && inputPatientDetail.DateofBirth != null))
            {
                ispatientUpdate = true;
            }
            if (!ispatientUpdate && (inputPatientDetail.GenderId != null && patientFromDb.GenderId != null && inputPatientDetail.GenderId != patientFromDb.GenderId) || (inputPatientDetail.GenderId is null && patientFromDb.GenderId != null) || (patientFromDb.GenderId is null && inputPatientDetail.GenderId != null))
            {
                ispatientUpdate = true;
            }
            if (!ispatientUpdate && addrList.Count > 0)
            {
                ispatientUpdate = true;
            }
            using (var transaction = new TransactionScope(TransactionScopeOption.Required, TransactionScopeAsyncFlowOption.Enabled))
            {

                //to add for patient serach fields - for cache clearing

                await _patientDAL.EditPatientDetails(inputPatientDetail);

                apiResponse.Result = "Success";
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";

                transaction.Complete();
            }

            await StorePatientSearchMessage(baseHttpRequestContext.OrgCode);
            //TODO:Send message to Service Bus and name to be update in EOD report
            if (isASBChange)
            {
                await StorePatientDetailMessage(orgCode: baseHttpRequestContext.OrgCode, patientId: id, fullName: fullName);

            }
            if (ispatientUpdate) //for hcp summary
                await StorePatientDetailMessageForPropertyType((int)EODRequestDataType.Patient_Update, orgCode: baseHttpRequestContext.OrgCode, patientId: id, null);

            await StorePatientSearchMessage(baseHttpRequestContext.OrgCode);

            return apiResponse;
        }

        private async Task StorePatientDetailMessage(string orgCode, long patientId, string fullName)
        {
            PaymentRequestDataModel patientRequestDataModel = new PaymentRequestDataModel
            {
                OrgName = orgCode,
                PropertyType = (int)EODRequestDataType.Patient,
                PropertyId = new long[] { patientId },
                PropertyValue = fullName
            };
            Dictionary<string, string> userPros = new Dictionary<string, string>
                        {
                            { "RequestType","Patient"},
                            { "to",_configuration["AzureAD:ASBSubNamePatientEOD"]}
                        };
            byte[] jsonContentByteArr = Encoding.GetEncoding("iso-8859-1").GetBytes(JsonConvert.SerializeObject(patientRequestDataModel));
            await _asbMessageSenderHelper.MessageSenderTopic(jsonContentByteArr, userPros, _configuration["AzureAD:AzSBConnStringPatient"], _configuration["AzureAD:ASBTopicPatient"]);
        }
        private async Task StorePatientDetailMessageForPropertyType(int propertyType, string orgCode, long patientId, string value = null)
        {
            PaymentRequestDataModel patientRequestDataModel = new PaymentRequestDataModel
            {
                OrgName = orgCode,
                PropertyType = propertyType,
                PropertyId = new long[] { patientId },
                PropertyValue = value
            };
            Dictionary<string, string> userPros = new Dictionary<string, string>
                        {
                            { "RequestType","Patient"},
                            { "to",_configuration["AzureAD:ASBSubNamePatientEOD"]}
                        };
            byte[] jsonContentByteArr = Encoding.GetEncoding("iso-8859-1").GetBytes(JsonConvert.SerializeObject(patientRequestDataModel));
            await _asbMessageSenderHelper.MessageSenderTopic(jsonContentByteArr, userPros, _configuration["AzureAD:AzSBConnStringPatient"], _configuration["AzureAD:ASBTopicPatient"]);
        }

        private List<Address> EditAddresses(ICollection<Address> removeAddressList, ICollection<Address> inputAddressList)
        {
            List<Address> addAddressList = new();
            foreach (var address in inputAddressList)
            {
                if (address.Id > 0)
                {
                    var existingobj = removeAddressList.FirstOrDefault(x => x.Id == address.Id);
                    var result = removeAddressList.Remove(existingobj);
                }
                else
                {
                    var AddressTodb = new Address();
                    AddressTodb = address;
                    AddressTodb.StatusId = (short)Status.Active;
                    addAddressList.Add(AddressTodb);
                }
            }

            foreach (var address in removeAddressList)
            {
                address.StatusId = (short)Status.Deleted;
                address.ModifiedDate = DateTime.UtcNow;
            }
            addAddressList = addAddressList.Concat(removeAddressList).ToList();
            return addAddressList;
        }

        /// <summary>
        /// Method to fetch a patient based on Id
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<InputPatientDetail>> GetPatientDetails(BaseHttpRequestContext baseHttpRequestContext, long id)
        {
            int orgId = baseHttpRequestContext.OrgId;
            ApiResponse<InputPatientDetail> apiResposne = new();
            PatientDetail patient = await _patientDAL.GetPatientDetails(orgId, id);
            patient.Addresses = await _patientDAL.GetPatientAddresses(orgId, id);
            patient.HIServiceDetails = await _patientDAL.GetHIServiceDetails(orgId, id);

            InputPatientDetail inputPatientDetail = _mapper.Map<PatientDetail, InputPatientDetail>(patient);

            if (patient?.HIServiceDetails?.Count > 0)
            {
                var hiserviceDetail = patient.HIServiceDetails.LastOrDefault(x => x.PatientDetailsId == patient.Id);
                //.Where(x => x.IHINumber == patient.IHINumber)
                //.OrderByDescending(x => x.ModifiedDate ?? x.CreatedDate)
                //.FirstOrDefault();
                if (hiserviceDetail != null)
                {
                    inputPatientDetail.IHISearchTypeId = hiserviceDetail.HISearchTypeId;
                    inputPatientDetail.IHINumberRecordId = hiserviceDetail.Id;
                    inputPatientDetail.IHIRecordStatusId = hiserviceDetail.HIRecordStatusId;
                    inputPatientDetail.IHIStatusId = hiserviceDetail.HIStatusId;
                    inputPatientDetail.HIRecordSource = hiserviceDetail.HIRecordSource;
                    inputPatientDetail.IHIReason = hiserviceDetail.Reason;
                    inputPatientDetail.IHIVerificationMessage = hiserviceDetail.VerificationMessage;
                    inputPatientDetail.IHINumberVerifiedDate = hiserviceDetail.ModifiedDate ?? hiserviceDetail.CreatedDate;
                }
            }

            //inputPatientDetail = await FormatReferralDetails(inputPatientDetail, orgId, baseHttpRequestContext);
            //inputPatientDetail = await FormatHealthCareTeamDetails(inputPatientDetail, orgId, baseHttpRequestContext);

            apiResposne.Result = inputPatientDetail;
            apiResposne.StatusCode = StatusCodes.Status200OK;
            apiResposne.Message = "Success";
            return apiResposne;
        }

        /// <summary>
        /// Method to display list of patients for global search
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        public async Task<ApiResponse<QueryResultList<PatientSearch>>> ListPatient(BaseHttpRequestContext baseHttpRequestContext, QueryModel queryModel)
        {
            ApiResponse<QueryResultList<PatientSearch>> apiResponse = new();
            //using (var transaction = new TransactionScope(TransactionScopeOption.Required, TransactionScopeAsyncFlowOption.Enabled))
            //{
            PatientFilterModel filterModel = PrepareFilterParameters(queryModel.Filter);
            QueryResultList<PatientSearch> patientQuery = await _patientDAL.ListPatient(baseHttpRequestContext.OrgId, baseHttpRequestContext.OrgCode, queryModel, filterModel);
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result = patientQuery;

            //    transaction.Complete();
            //}
            return apiResponse;
        }
        private PatientFilterModel PrepareFilterParameters(string filter)
        {
            if (!(string.IsNullOrEmpty(filter)))
            {
                PatientFilterModel patientFilter = JsonConvert.DeserializeObject<PatientFilterModel>(filter);
                return patientFilter;
            }
            else
            {
                return null;
            }
        }

        public async Task<ApiResponse<LetterTemplateView>> GetLetterTemplateBAL(long patient_id, long letter_template_id, BaseHttpRequestContext baseHttpRequestContext)
        {
            int orgId = baseHttpRequestContext.OrgId;
            ApiResponse<LetterTemplateView> apiResposne = new();
            LetterTemplateView letterFromDB = await _mediaLibraryDAL.GetLetterTemplateDAL(orgId, letter_template_id);
            if (letterFromDB is null)
            {
                apiResposne.Result = null;
                apiResposne.StatusCode = StatusCodes.Status400BadRequest;
                apiResposne.Message = "Failure";
                apiResposne.Errors.Add("The Letter Template doesnot exist.");
                return apiResposne;
            }

            if (letterFromDB is not null && letterFromDB.FileDetailsId is not null)
            {
                letterFromDB = await FormatLetterTemplate(letterFromDB, baseHttpRequestContext);
            }
            if (letterFromDB is not null)
            {

                var finalText = await ConvertoPatientDataText(letterFromDB, patient_id, orgId, baseHttpRequestContext);
                letterFromDB.Text = finalText;
                letterFromDB.CreatedDate = DateTime.SpecifyKind(letterFromDB.CreatedDate, DateTimeKind.Utc);
                letterFromDB.ModifiedDate = (letterFromDB.ModifiedDate == null) ? null : DateTime.SpecifyKind((DateTime)letterFromDB.ModifiedDate, DateTimeKind.Utc);

                apiResposne.Result = letterFromDB;
                apiResposne.StatusCode = StatusCodes.Status200OK;
                apiResposne.Message = "Success";
                return apiResposne;
            }
            else
            {
                apiResposne.Result = null;
                apiResposne.StatusCode = StatusCodes.Status500InternalServerError;
                apiResposne.Message = "Data for given letter templateId is not found";
                return apiResposne;
            }

        }

        /// <summary>
        /// Method to fetch the Filedetailsoutput for the 
        /// </summary>
        /// <param name="inputSnippetUserAssocs"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        private async Task<LetterTemplateView> FormatLetterTemplate(LetterTemplateView inputLetterTemplate, BaseHttpRequestContext baseHttpRequestContext)
        {
            long fileId = (long)inputLetterTemplate.FileDetailsId;
            var token = baseHttpRequestContext.BearerToken;
            string interServiceToken = baseHttpRequestContext.InterServiceToken;
            string fileAPiUrl = _appSettings.ApiUrls["FileServiceUrl"] + "/file/file_details/" + fileId;
            RestClient restClient = new RestClient(fileAPiUrl, null, token, interServiceToken);
            var fileApiResponse = await restClient.GetAsync<ApiResponse<FileDetailsOutputForId>>(fileAPiUrl, null);
            if (fileApiResponse.StatusCode == StatusCodes.Status200OK)
            {
                inputLetterTemplate.FileDetailsOutput = fileApiResponse.Result;
            }

            return inputLetterTemplate;
        }

        /// <summary>
        /// Method to replace the Data Fields with Actual patient Data 
        /// </summary>
        /// <param name="Text"></param>
        /// <returns></returns>
        private async Task<string> ConvertoPatientDataText(LetterTemplateView letterFromDB, long patient_id, int orgId, BaseHttpRequestContext baseHttpRequestContext)
        {
            var patientDataFromDB = await GetPatientDetails(baseHttpRequestContext, patient_id);
            var data1 = patientDataFromDB.Result;
            var actualData = data1;
            var Text = letterFromDB.Text;
            CompanyDetail icFromDB = new CompanyDetail();
            if (letterFromDB.CompanyDetailsId.HasValue)
            {
                icFromDB = await _patientDAL.GetCompanyData(letterFromDB.CompanyDetailsId.Value, orgId);
            }

            if (Text.Contains("##PD_"))
            {
                PatientForLetter patientData = new();
                patientData["##PD_PatientId##"] = actualData.Id.ToString();
                //patientData["##PD_Title##"] = actualData.TitleId is not null ? Enum.GetName(typeof(TitleType), actualData.TitleId) : null;
                patientData["##PD_Title##"] = actualData.TitleId is not null ? EnumExtensions.GetDescription((TitleType)actualData.TitleId) : null;
                patientData["##PD_FirstName##"] = actualData.FirstName;
                patientData["##PD_Surname##"] = actualData.SurName;
                patientData["##PD_Salutation##"] = actualData.Salutation;
                patientData["##PD_Date_of_Birth##"] = actualData.DateofBirth is not null ? ((DateTime)actualData.DateofBirth).ToString("dd/MM/yyyy") : null;
                patientData["##PD_Occupation##"] = actualData.Occupation;
                patientData["##PD_Guardian_Name##"] = actualData.GuardianName;
                patientData["##PD_Guardian_Work_Number##"] = actualData.GuardianWorkContact;
                patientData["##PD_Guardian_Home_Number##"] = actualData.GuardianHomeContact;
                patientData["##PD_Guardian_Mobile_Number##"] = actualData.GuardianMobileContact;
                patientData["##PD_Patient_Work_Number##"] = actualData.WorkContact;
                patientData["##PD_Patient_Home_Number##"] = actualData.HomeContact;
                patientData["##PD_Patient_Mobile_Number##"] = actualData.Mobile;
                patientData["##PD_Physical_Address##"] = GetAddressforPatient(actualData.Addresses.ToList(), Convert.ToInt16(AddressType.Physical));
                patientData["##PD_Postal_Address##"] = GetAddressforPatient(actualData.Addresses.ToList(), Convert.ToInt16(AddressType.Postal));
                patientData["##PD_RecordID##"] = actualData.RecordId.ToString();

                foreach (KeyValuePair<string, string> DataField in patientData)
                {
                    if (Text.Contains(DataField.Key) && (!string.IsNullOrEmpty(DataField.Value)))
                    {
                        Text = Text.Replace(DataField.Key, DataField.Value);
                    }
                    else if (Text.Contains(DataField.Key) && letterFromDB.TemplateTypeId == (short)TemplateType.SMS_Template && string.IsNullOrWhiteSpace(DataField.Value))
                    {
                        Text = Text.Replace(DataField.Key, string.Empty);
                    }
                }
            }

            if (Text.Contains("##ActiveReferral_"))
            {
                //var referalFromDB = await _referralDAL.FetchReferrals(orgId, patient_id);

                var activeReferralDetails = await ActiveReferralDetails(orgId, patient_id, baseHttpRequestContext);

                if (activeReferralDetails.Count > 0)
                {
                    var ActiveRefferalFromDB = activeReferralDetails.Where(x => x.IsDefault == true).FirstOrDefault();
                    ActiveRefferalLetter activeRefferal = new();
                    if (ActiveRefferalFromDB.ReferringProviderInfo is not null)
                    {
                        activeRefferal["##ActiveReferral_Referring_Doctor##"] = ActiveRefferalFromDB.ReferringProviderInfo is not null ? ActiveRefferalFromDB.ReferringProviderInfo.FirstName + " " + ActiveRefferalFromDB.ReferringProviderInfo.SurName : null;
                        activeRefferal["##ActiveReferral_Specialization##"] = ActiveRefferalFromDB.ReferringProviderInfo.Specialisation is not null ? ActiveRefferalFromDB.ReferringProviderInfo.Specialisation : null;
                        activeRefferal["##ActiveReferral_Provider_Location##"] = ActiveRefferalFromDB.ReferringProviderInfo.CompanyName is not null ? ActiveRefferalFromDB.ReferringProviderInfo.CompanyName : null;
                        activeRefferal["##ActiveReferral_Provider_Type##"] = ActiveRefferalFromDB.ReferringProviderInfo.ProviderType is not null ? ActiveRefferalFromDB.ReferringProviderInfo.ProviderType : null;
                    }
                    activeRefferal["##ActiveReferral_Provider_Assigned_To##"] = ActiveRefferalFromDB.AssignedProviderInfo is not null ? ActiveRefferalFromDB.AssignedProviderInfo.FirstName + " " + ActiveRefferalFromDB.AssignedProviderInfo.SurName : null;
                    activeRefferal["##ActiveReferral_Issue_Date##"] = ActiveRefferalFromDB.IssueDate is not null ? ((DateTime)ActiveRefferalFromDB.IssueDate).ToString("dd/MM/yyyy") : null;
                    activeRefferal["##ActiveReferral_Active_Date##"] = ActiveRefferalFromDB.ActiveDate is not null ? ((DateTime)ActiveRefferalFromDB.ActiveDate).ToString("dd/MM/yyyy") : null;
                    activeRefferal["##ActiveReferral_Expiry_Date##"] = ActiveRefferalFromDB.ExpiryDate is not null ? ((DateTime)ActiveRefferalFromDB.ExpiryDate).ToString("dd/MM/yyyy") : null;

                    foreach (KeyValuePair<string, string> DataField in activeRefferal)
                    {
                        if (Text.Contains(DataField.Key) && (!string.IsNullOrEmpty(DataField.Value)))
                        {
                            Text = Text.Replace(DataField.Key, DataField.Value);
                        }
                    }
                }
            }

            if (Text.Contains("##Healthcareteam"))
            {
                var healthCareFromDB = await _healthCareTeamDAL.FetchHealthCareTeamDetails(orgId, patient_id);

                if (healthCareFromDB.Count > 0)
                {
                    //var healthCareFromDB = healthCareTeams;
                    var healthCareString = "";
                    healthCareFromDB.ToList().ForEach(healthCare =>
                    {
                        if (healthCare.ProviderCompanyAssocsInfo is not null)
                        {

                            healthCareString +=
                            healthCare.ProviderCompanyAssocsInfo.FirstName + " " + healthCare.ProviderCompanyAssocsInfo.SurName +
                            ", " + healthCare.ProviderCompanyAssocsInfo.CompanyName + ", " + healthCare.ProviderCompanyAssocsInfo.ProviderType
                            + ", " + healthCare.ProviderCompanyAssocsInfo.Specialisation;
                        }
                    });

                    Text = Text.Replace("##Healthcareteam##", healthCareString);
                }
            }
            if (Text.Contains("##IC_"))
            {
                ICLetter iCompanyLetter = new();
                iCompanyLetter["##IC_Internal_Company_Name##"] = icFromDB.Name;
                iCompanyLetter["##IC_Internal_Company_Email##"] = icFromDB.Email;
                iCompanyLetter["##IC_ACN##"] = icFromDB.Acn;
                iCompanyLetter["##IC_ABN##"] = icFromDB.Abn;
                iCompanyLetter["##IC_Office_Address##"] = GetAddressforComapny(icFromDB.CompanyAddresses.ToList(), Convert.ToInt16(AddressType.Physical));
                iCompanyLetter["##IC_Other_Address##"] = GetAddressforComapny(icFromDB.CompanyAddresses.ToList(), Convert.ToInt16(AddressType.Other));
                iCompanyLetter["##IC_HPIO_Location##"] = icFromDB.Hpiolocation;
                iCompanyLetter["##IC_HPIO_Number##"] = icFromDB.Hpionumber.ToString();
                iCompanyLetter["##IC_Bank_Details##"] = icFromDB.BankDetails;
                iCompanyLetter["##IC_Account_Name##"] = icFromDB.AccountName;
                iCompanyLetter["##IC_BSB##"] = icFromDB.Bsb == null ? "" : icFromDB.Bsb.ToString();
                iCompanyLetter["##IC_Account_Number##"] = icFromDB.AccountNo;
                iCompanyLetter["##IC_Work_Number##"] = icFromDB.WorkContact;
                iCompanyLetter["##IC_Fax_Number##"] = icFromDB.FaxNumber;
                iCompanyLetter["##IC_Mobile_Number##"] = icFromDB.Mobile;

                if (icFromDB.LogoFileDetailsId != null && icFromDB.LogoFileDetailsId > 0)
                {
                    string logoSasToken = await GetLogoforCompany(icFromDB.LogoFileDetailsId, baseHttpRequestContext);
                    if (!string.IsNullOrEmpty(logoSasToken))
                        iCompanyLetter["##IC_Company_Image##"] = $"<img src='{logoSasToken}'/>";

                }
                else
                    iCompanyLetter["##IC_Company_Image##"] = null;
                foreach (KeyValuePair<string, string> DataField in iCompanyLetter)
                {
                    if (Text.Contains(DataField.Key) && (!string.IsNullOrEmpty(DataField.Value)))
                    {
                        Text = Text.Replace(DataField.Key, DataField.Value);
                    }

                }
            }

            if (Text.Contains("##IP_"))
            {
                var ipFromDB = await _patientDAL.GetLoggedInUser(baseHttpRequestContext.UserId, orgId);
                IPLetter iUserLetter = new();

                if (ipFromDB.UserTypeId == (short)UserType.Internal_Provider)
                {
                    //iUserLetter["##IP_Title##"] = ipFromDB.TitleId is not null ? Enum.GetName(typeof(TitleType), ipFromDB.TitleId) : null; ;
                    iUserLetter["##IP_Title##"] = ipFromDB.TitleId is not null ? EnumExtensions.GetDescription((TitleType)ipFromDB.TitleId) : null; ;
                    iUserLetter["##IP_FirstName##"] = ipFromDB.FirstName;
                    iUserLetter["##IP_Surname##"] = ipFromDB.SurName;
                    iUserLetter["##IP_Salutation##"] = ipFromDB.Salutation;
                    iUserLetter["##IP_Qualification##"] = ipFromDB.Qualification;
                    iUserLetter["##IP_Specialisation##"] = ipFromDB.Specialisation;
                    iUserLetter["##IP_Date_of_Birth##"] = ipFromDB.DateofBirth is not null ? ((DateTime)ipFromDB.DateofBirth).ToString("dd/MM/yyyy") : null;
                    iUserLetter["##IP_Home_Address##"] = GetAddressforUser(ipFromDB.UserAddresses.ToList(), Convert.ToInt16(AddressType.Home));
                    iUserLetter["##IP_Other_Address##"] = GetAddressforUser(ipFromDB.UserAddresses.ToList(), Convert.ToInt16(AddressType.Other));
                    iUserLetter["##IP_Personal_Email##"] = ipFromDB.PersonalEmailAddress;
                    iUserLetter["##IP_APHRA_Number##"] = ipFromDB.AphraNumber;
                    iUserLetter["##IP_Provider_Type##"] = ipFromDB.ProviderTypeId is not null ? EnumExtensions.GetDescription((ProviderType)ipFromDB.ProviderTypeId) : null;
                    iUserLetter["##IP_HPI-I_Number##"] = ipFromDB.HPIINumber;
                    iUserLetter["##IP_Prescriber_Number##"] = ipFromDB.PrescriberNumber;
                    iUserLetter["##IP_Internal_Entity_Provider_Number##"] = ipFromDB.UserCompanyAssocs.Where(e => e.CompanyId == icFromDB.Id).FirstOrDefault() is not null ?
                                                                            ipFromDB.UserCompanyAssocs.Where(e => e.CompanyId == icFromDB.Id).FirstOrDefault().ProviderNumber : null;
                    iUserLetter["##IP_Internal_Entity_Provider_Location##"] = ipFromDB.UserCompanyAssocs.Where(e => e.CompanyId == icFromDB.Id).FirstOrDefault() is not null ?
                                                                               icFromDB.Name : null;
                    if (ipFromDB.IsInternalEntityBank == false)
                    {
                        iUserLetter["##IP_Account_Name##"] = ipFromDB.AccountName;
                        iUserLetter["##IP_BSB##"] = ipFromDB.BSB;
                        iUserLetter["##IP_Account_Number##"] = ipFromDB.AccountNo;
                        iUserLetter["##IP_Bank_Details##"] = ipFromDB.BankDetails;
                    }
                    else
                    {
                        iUserLetter["##IP_Account_Name##"] = icFromDB.AccountName;
                        iUserLetter["##IP_BSB##"] = icFromDB.Bsb.ToString();
                        iUserLetter["##IP_Account_Number##"] = icFromDB.AccountNo;
                        iUserLetter["##IP_Bank_Details##"] = icFromDB.BankDetails;
                    }

                    foreach (KeyValuePair<string, string> DataField in iUserLetter)
                    {
                        if (Text.Contains(DataField.Key) && (!string.IsNullOrEmpty(DataField.Value)))
                        {
                            Text = Text.Replace(DataField.Key, DataField.Value);
                        }
                    }
                }
            }
            if (Text.Contains("##D_Today##"))
            {
                var sydneyDateTimeNow = DateTimeConversion.ConvertTimeFromUtc(DateTime.UtcNow, "AUS Eastern Standard Time");
                var sydneyDateNow = sydneyDateTimeNow is not null ? ((DateTime)sydneyDateTimeNow).ToString("dd/MM/yyyy") : null;
                if (string.IsNullOrEmpty(sydneyDateNow))
                {
                    sydneyDateTimeNow = DateTimeConversion.ConvertTimeFromUtc(DateTime.UtcNow, "Australia/Sydney");
                    sydneyDateNow = sydneyDateTimeNow is not null ? ((DateTime)sydneyDateTimeNow).ToString("dd/MM/yyyy") : null;
                }
                Text = Text.Replace("##D_Today##", sydneyDateNow);
            }

            return Text;
        }

        private async Task<PatientTagsModel> GetPatientDataTags(LetterTemplateView letterFromDB, PatientLetterTemplateFilterModel patientLetterTemplateFilterModel, int orgId, BaseHttpRequestContext baseHttpRequestContext, string letterTemplateText = null)
        {
            var patient_id = patientLetterTemplateFilterModel.PatientId;
            var patientDataFromDB = await GetPatientDetails(baseHttpRequestContext, patient_id);
            var data1 = patientDataFromDB.Result;
            var actualData = data1;
            PatientTagsModel patientTagsModel = new PatientTagsModel();

            CompanyDetail icFromDB = new CompanyDetail();
            if (patientLetterTemplateFilterModel.TemplateTypeId != (short)TemplateTypesEnum.Label_Template)
            {
                var companyDetailsId = patientLetterTemplateFilterModel.TemplateTypeId == (short)TemplateTypesEnum.Finance_Template ? patientLetterTemplateFilterModel.CompanyDetailsId.Value : letterFromDB.CompanyDetailsId.Value;

                if (companyDetailsId > 0)
                {
                    icFromDB = await _patientDAL.GetCompanyData(companyDetailsId, orgId);
                }
            }

            PatientForLetter patientData = new();
            patientData["##PD_MedicareCardNumber##"] = null;
            patientData["##PD_PatientId##"] = actualData.RecordId.ToString();
            //patientData["##PD_Title##"] = actualData.TitleId is not null ? Enum.GetName(typeof(TitleType), actualData.TitleId) : null;
            patientData["##PD_Title##"] = actualData.TitleId is not null ? EnumExtensions.GetDescription((TitleType)actualData.TitleId) : null;
            patientData["##PD_FirstName##"] = actualData.FirstName;
            patientData["##PD_Surname##"] = actualData.SurName;
            patientData["##PD_Salutation##"] = actualData.Salutation;
            patientData["##PD_Date_of_Birth##"] = actualData.DateofBirth is not null ? ((DateTime)actualData.DateofBirth).ToString("dd/MM/yyyy") : null;
            patientData["##PD_Occupation##"] = actualData.Occupation;
            patientData["##PD_Guardian_Name##"] = actualData.GuardianName;
            patientData["##PD_Guardian_Work_Number##"] = actualData.GuardianWorkContact;
            patientData["##PD_Guardian_Home_Number##"] = actualData.GuardianHomeContact;
            patientData["##PD_Guardian_Mobile_Number##"] = actualData.GuardianMobileContact;
            patientData["##PD_Patient_Work_Number##"] = actualData.WorkContact;
            patientData["##PD_Patient_Home_Number##"] = actualData.HomeContact;
            patientData["##PD_Patient_Mobile_Number##"] = actualData.Mobile;
            patientData["##PD_Gender##"] = actualData.GenderId is not null ? (Enum.GetName(typeof(Gender), actualData.GenderId)).Substring(0, 1) : null;
            patientData["##PD_Age##"] = actualData.DateofBirth is not null ? GetAgeforPatient((DateTime)actualData.DateofBirth) : null;



            //patientData["##PD_Physical_Address##"] = GetAddressforPatient(actualData.Addresses.ToList(), Convert.ToInt16(AddressType.Physical));
            var phyAddress = actualData.Addresses.ToList().FirstOrDefault(p => p.AddressType == Convert.ToInt16(AddressType.Physical) && p.StatusId == (short)Status.Active);
            if (phyAddress is not null)
            {
                patientData["##PD_Physical_AddressLine1##"] = string.IsNullOrWhiteSpace(phyAddress.AddressLine1) ? string.Empty : phyAddress.AddressLine1;
                patientData["##PD_Physical_AddressLine2##"] = string.IsNullOrWhiteSpace(phyAddress.AddressLine2) ? string.Empty : phyAddress.AddressLine2;
                patientData["##PD_Physical_Suburb##"] = string.IsNullOrWhiteSpace(phyAddress.Suburb) ? string.Empty : phyAddress.Suburb;
                patientData["##PD_Physical_State##"] = Enum.GetName(typeof(State), phyAddress.StateId);
                patientData["##PD_Physical_Postcode##"] = phyAddress.PostCode;
            }
            else
            {
                patientData["##PD_Physical_AddressLine1##"] = null;
                patientData["##PD_Physical_AddressLine2##"] = null;
                patientData["##PD_Physical_Suburb##"] = null;
                patientData["##PD_Physical_State##"] = null;
                patientData["##PD_Physical_Postcode##"] = null;

            }

            // patientData["##PD_Postal_Address##"] = GetAddressforPatient(actualData.Addresses.ToList(), Convert.ToInt16(AddressType.Postal));
            var postAddress = actualData.Addresses.ToList().FirstOrDefault(p => p.AddressType == Convert.ToInt16(AddressType.Postal) && p.StatusId == (short)Status.Active);
            if (postAddress is not null)
            {
                patientData["##PD_Postal_AddressLine1##"] = string.IsNullOrWhiteSpace(postAddress.AddressLine1) ? string.Empty : postAddress.AddressLine1;
                patientData["##PD_Postal_AddressLine2##"] = string.IsNullOrWhiteSpace(postAddress.AddressLine2) ? string.Empty : postAddress.AddressLine2;
                patientData["##PD_Postal_Suburb##"] = string.IsNullOrWhiteSpace(postAddress.Suburb) ? string.Empty : postAddress.Suburb;
                patientData["##PD_Postal_State##"] = Enum.GetName(typeof(State), postAddress.StateId);
                patientData["##PD_Postal_Postcode##"] = postAddress.PostCode;
            }
            else
            {
                patientData["##PD_Postal_AddressLine1##"] = null;
                patientData["##PD_Postal_AddressLine2##"] = null;
                patientData["##PD_Postal_Suburb##"] = null;
                patientData["##PD_Postal_State##"] = null;
                patientData["##PD_Postal_Postcode##"] = null;

            }

            var homeAddress = actualData.Addresses.ToList().FirstOrDefault(p => p.AddressType == Convert.ToInt16(AddressType.Home) && p.StatusId == (short)Status.Active);
            if (homeAddress is not null)
            {
                patientData["##PD_Home_AddressLine1##"] = string.IsNullOrWhiteSpace(homeAddress.AddressLine1) ? string.Empty : homeAddress.AddressLine1;
                patientData["##PD_Home_AddressLine2##"] = string.IsNullOrWhiteSpace(homeAddress.AddressLine2) ? string.Empty : homeAddress.AddressLine2;
                patientData["##PD_Home_Suburb##"] = string.IsNullOrWhiteSpace(homeAddress.Suburb) ? string.Empty : homeAddress.Suburb;
                patientData["##PD_Home_State##"] = Enum.GetName(typeof(State), homeAddress.StateId);
                patientData["##PD_Home_Postcode##"] = homeAddress.PostCode;
            }
            else
            {
                patientData["##PD_Home_AddressLine1##"] = null;
                patientData["##PD_Home_AddressLine2##"] = null;
                patientData["##PD_Home_Suburb##"] = null;
                patientData["##PD_Home_State##"] = null;
                patientData["##PD_Home_Postcode##"] = null;
            }

            var officeAddress = actualData.Addresses.ToList().FirstOrDefault(p => p.AddressType == Convert.ToInt16(AddressType.Office) && p.StatusId == (short)Status.Active);
            if (officeAddress is not null)
            {
                patientData["##PD_Office_AddressLine1##"] = string.IsNullOrWhiteSpace(officeAddress.AddressLine1) ? string.Empty : officeAddress.AddressLine1;
                patientData["##PD_Office_AddressLine2##"] = string.IsNullOrWhiteSpace(officeAddress.AddressLine2) ? string.Empty : officeAddress.AddressLine2;
                patientData["##PD_Office_Suburb##"] = string.IsNullOrWhiteSpace(officeAddress.Suburb) ? string.Empty : officeAddress.Suburb;
                patientData["##PD_Office_State##"] = Enum.GetName(typeof(State), officeAddress.StateId);
                patientData["##PD_Office_Postcode##"] = officeAddress.PostCode;
            }
            else
            {
                patientData["##PD_Office_AddressLine1##"] = null;
                patientData["##PD_Office_AddressLine2##"] = null;
                patientData["##PD_Office_Suburb##"] = null;
                patientData["##PD_Office_State##"] = null;
                patientData["##PD_Office_Postcode##"] = null;

            }

            var otherAddress = actualData.Addresses.ToList().FirstOrDefault(p => p.AddressType == Convert.ToInt16(AddressType.Other) && p.StatusId == (short)Status.Active);
            if (otherAddress is not null)
            {
                patientData["##PD_Other_AddressLine1##"] = string.IsNullOrWhiteSpace(otherAddress.AddressLine1) ? string.Empty : otherAddress.AddressLine1;
                patientData["##PD_Other_AddressLine2##"] = string.IsNullOrWhiteSpace(otherAddress.AddressLine2) ? string.Empty : otherAddress.AddressLine2;
                patientData["##PD_Other_Suburb##"] = string.IsNullOrWhiteSpace(otherAddress.Suburb) ? string.Empty : otherAddress.Suburb;
                patientData["##PD_Other_State##"] = Enum.GetName(typeof(State), otherAddress.StateId);
                patientData["##PD_Other_Postcode##"] = otherAddress.PostCode;
            }
            else
            {
                patientData["##PD_Other_AddressLine1##"] = null;
                patientData["##PD_Other_AddressLine2##"] = null;
                patientData["##PD_Other_Suburb##"] = null;
                patientData["##PD_Other_State##"] = null;
                patientData["##PD_Other_Postcode##"] = null;
            }
            patientData["##PD_RecordID##"] = actualData.RecordId.ToString();

            var accountHolderFromDB = await _accountHolderDAL.FetchAccountHolderDetails(orgId, patient_id);
            if (accountHolderFromDB is not null && accountHolderFromDB.AccountHolderAssocs is not null && accountHolderFromDB.AccountHolderAssocs.Count > 0)
            {
                var medicaredata = accountHolderFromDB.AccountHolderAssocs.ToList().FirstOrDefault(p => p.AccountHolderType == Convert.ToInt16(AccountHolderTypes.Medicare));
                if (medicaredata is not null)
                {
                    patientData["##AH_Medicare_Number##"] = medicaredata.AccountNumber;
                    patientData["##AH_IRN##"] = medicaredata.AccountSubNumber;
                    patientData["##AH_Medicare_Expiry_Date##"] = medicaredata.ExpiryDate is not null ? ((DateTime)medicaredata.ExpiryDate).ToString("dd/MM/yyyy") : null;

                }
                else
                {
                    patientData["##AH_Medicare_Number##"] = null;
                    patientData["##AH_IRN##"] = null;
                    patientData["##AH_Medicare_Expiry_Date##"] = null;


                }
                var dvadata = accountHolderFromDB.AccountHolderAssocs.ToList().FirstOrDefault(p => p.AccountHolderType == Convert.ToInt16(AccountHolderTypes.DVA));
                if (dvadata is not null)
                {
                    patientData["##AH_DVA_Number##"] = dvadata.AccountNumber;
                    patientData["##AH_DVA_Expiry_Date##"] = dvadata.ExpiryDate is not null ? ((DateTime)dvadata.ExpiryDate).ToString("dd/MM/yyyy") : null;

                }
                else
                {
                    patientData["##AH_DVA_Number##"] = null;
                    patientData["##AH_DVA_Expiry_Date##"] = null;

                }
            }
            else
            {
                patientData["##AH_Medicare_Number##"] = null;
                patientData["##AH_IRN##"] = null;
                patientData["##AH_Medicare_Expiry_Date##"] = null;
                patientData["##AH_DVA_Number##"] = null;
                patientData["##AH_DVA_Expiry_Date##"] = null;
            }
            if (accountHolderFromDB is not null && accountHolderFromDB.AccountHolderHealthfundAssocs is not null)
            {
                var healthFund = accountHolderFromDB.AccountHolderHealthfundAssocs;

                patientData["##AH_Health_Fund_Name##"] = healthFund.Name;
                patientData["##AH_MF_ID##"] = healthFund.FundId;
                patientData["##AH_Membership_Number##"] = healthFund.MembershipNumber;

            }
            else
            {
                patientData["##AH_Health_Fund_Name##"] = null;
                patientData["##AH_MF_ID##"] = null;
                patientData["##AH_Membership_Number##"] = null;
            }



            patientTagsModel.Data = new List<KeyValuePair<string, string>>();
            patientTagsModel.Data.AddRange(patientData);
            var activeReferralDetails = await ActiveReferralDetails(orgId, patient_id, baseHttpRequestContext);
            ActiveRefferalLetter activeRefferal = new();

            if (activeReferralDetails != null && activeReferralDetails.Count > 0)
            {
                var ActiveRefferalFromDB = activeReferralDetails.Where(x => x.IsDefault == true).FirstOrDefault();
                if (ActiveRefferalFromDB?.ReferringProviderInfo is not null)
                {
                    activeRefferal["##ActiveReferral_Referring_Doctor##"] = ActiveRefferalFromDB.ReferringProviderInfo is not null ? ActiveRefferalFromDB.ReferringProviderInfo.FirstName + " " + ActiveRefferalFromDB.ReferringProviderInfo.SurName : null;
                    activeRefferal["##ActiveReferral_Specialization##"] = ActiveRefferalFromDB.ReferringProviderInfo.Specialisation is not null ? ActiveRefferalFromDB.ReferringProviderInfo.Specialisation : null;
                    activeRefferal["##ActiveReferral_Provider_Location##"] = ActiveRefferalFromDB.ReferringProviderInfo.CompanyName is not null ? ActiveRefferalFromDB.ReferringProviderInfo.CompanyName : null;
                    activeRefferal["##ActiveReferral_Provider_Type##"] = ActiveRefferalFromDB.ReferringProviderInfo.ProviderType is not null ? ActiveRefferalFromDB.ReferringProviderInfo.ProviderType : null;
                    activeRefferal["##ARP_Salutation##"] = ActiveRefferalFromDB.ReferringProviderInfo.Salutation;
                    activeRefferal["##ARP_Title##"] = ActiveRefferalFromDB.ReferringProviderInfo.TitleId is not null ? EnumExtensions.GetDescription((TitleType)ActiveRefferalFromDB.ReferringProviderInfo.TitleId) : null;

                    if (ActiveRefferalFromDB.ReferringProviderInfo.CompanyId > 0)
                    {
                        // Fetch Active Refferal providers Company Addresses
                        var aRPcompanyData = await _patientDAL.GetCompanyData(ActiveRefferalFromDB.ReferringProviderInfo.CompanyId, orgId);
                        var aRPphyAddress = aRPcompanyData.CompanyAddresses.ToList().FirstOrDefault(p => p.AddressType == Convert.ToInt16(AddressType.Physical) && p.StatusId == (short)Status.Active);
                        if (aRPphyAddress is not null)
                        {
                            activeRefferal["##ARP_Physical_AddressLine1##"] = string.IsNullOrWhiteSpace(aRPphyAddress.AddressLine1) ? string.Empty : aRPphyAddress.AddressLine1;
                            activeRefferal["##ARP_Physical_AddressLine2##"] = string.IsNullOrWhiteSpace(aRPphyAddress.AddressLine2) ? string.Empty : aRPphyAddress.AddressLine2;
                            activeRefferal["##ARP_Physical_Suburb##"] = string.IsNullOrWhiteSpace(aRPphyAddress.Suburb) ? string.Empty : aRPphyAddress.Suburb;
                            activeRefferal["##ARP_Physical_State##"] = Enum.GetName(typeof(State), aRPphyAddress.StateId);
                            activeRefferal["##ARP_Physical_Postcode##"] = aRPphyAddress.PostCode;
                        }
                        else
                        {
                            activeRefferal["##ARP_Physical_AddressLine1##"] = null;
                            activeRefferal["##ARP_Physical_AddressLine2##"] = null;
                            activeRefferal["##ARP_Physical_Suburb##"] = null;
                            activeRefferal["##ARP_Physical_State##"] = null;
                            activeRefferal["##ARP_Physical_Postcode##"] = null;

                        }
                        var aRPpostAddress = aRPcompanyData.CompanyAddresses.ToList().FirstOrDefault(p => p.AddressType == Convert.ToInt16(AddressType.Postal) && p.StatusId == (short)Status.Active);
                        if (aRPpostAddress is not null)
                        {
                            activeRefferal["##ARP_Postal_AddressLine1##"] = string.IsNullOrWhiteSpace(aRPpostAddress.AddressLine1) ? string.Empty : aRPpostAddress.AddressLine1;
                            activeRefferal["##ARP_Postal_AddressLine2##"] = string.IsNullOrWhiteSpace(aRPpostAddress.AddressLine2) ? string.Empty : aRPpostAddress.AddressLine2;
                            activeRefferal["##ARP_Postal_Suburb##"] = string.IsNullOrWhiteSpace(aRPpostAddress.Suburb) ? string.Empty : aRPpostAddress.Suburb;
                            activeRefferal["##ARP_Postal_State##"] = Enum.GetName(typeof(State), aRPpostAddress.StateId);
                            activeRefferal["##ARP_Postal_Postcode##"] = aRPpostAddress.PostCode;
                        }
                        else
                        {
                            activeRefferal["##ARP_Postal_AddressLine1##"] = null;
                            activeRefferal["##ARP_Postal_AddressLine2##"] = null;
                            activeRefferal["##ARP_Postal_Suburb##"] = null;
                            activeRefferal["##ARP_Postal_State##"] = null;
                            activeRefferal["##ARP_Postal_Postcode##"] = null;

                        }
                        var aRPotherAddress = aRPcompanyData.CompanyAddresses.ToList().FirstOrDefault(p => p.AddressType == Convert.ToInt16(AddressType.Other) && p.StatusId == (short)Status.Active);
                        if (aRPotherAddress is not null)
                        {
                            activeRefferal["##ARP_Other_AddressLine1##"] = string.IsNullOrWhiteSpace(aRPotherAddress.AddressLine1) ? string.Empty : aRPotherAddress.AddressLine1;
                            activeRefferal["##ARP_Other_AddressLine2##"] = string.IsNullOrWhiteSpace(aRPotherAddress.AddressLine2) ? string.Empty : aRPotherAddress.AddressLine2;
                            activeRefferal["##ARP_Other_Suburb##"] = string.IsNullOrWhiteSpace(aRPotherAddress.Suburb) ? string.Empty : aRPotherAddress.Suburb;
                            activeRefferal["##ARP_Other_State##"] = Enum.GetName(typeof(State), aRPotherAddress.StateId);
                            activeRefferal["##ARP_Other_Postcode##"] = string.IsNullOrWhiteSpace(aRPotherAddress.PostCode) ? string.Empty : aRPotherAddress.PostCode;
                        }
                        else
                        {
                            activeRefferal["##ARP_Other_AddressLine1##"] = null;
                            activeRefferal["##ARP_Other_AddressLine2##"] = null;
                            activeRefferal["##ARP_Other_Suburb##"] = null;
                            activeRefferal["##ARP_Other_State##"] = null;
                            activeRefferal["##ARP_Other_Postcode##"] = null;
                        }
                    }
                }
                else activeRefferal = GernateEmptyDataForActiveReferralProviderTags(activeRefferal);

                activeRefferal["##ActiveReferral_Provider_Assigned_To##"] = ActiveRefferalFromDB?.AssignedProviderInfo is not null ? ActiveRefferalFromDB.AssignedProviderInfo.FirstName + " " + ActiveRefferalFromDB.AssignedProviderInfo.SurName : null;
                activeRefferal["##ActiveReferral_Issue_Date##"] = ActiveRefferalFromDB?.IssueDate is not null ? ((DateTime)ActiveRefferalFromDB.IssueDate).ToString("dd/MM/yyyy") : null;
                activeRefferal["##ActiveReferral_Active_Date##"] = ActiveRefferalFromDB?.ActiveDate is not null ? ((DateTime)ActiveRefferalFromDB.ActiveDate).ToString("dd/MM/yyyy") : null;
                activeRefferal["##ActiveReferral_Expiry_Date##"] = ActiveRefferalFromDB?.ExpiryDate is not null ? ((DateTime)ActiveRefferalFromDB.ExpiryDate).ToString("dd/MM/yyyy") : null;


            }
            else
            {
                activeRefferal["##ActiveReferral_Provider_Assigned_To##"] = null;
                activeRefferal["##ActiveReferral_Issue_Date##"] = null;
                activeRefferal["##ActiveReferral_Active_Date##"] = null;
                activeRefferal["##ActiveReferral_Expiry_Date##"] = null;
                activeRefferal = GernateEmptyDataForActiveReferralProviderTags(activeRefferal);
            }
            patientTagsModel.Data.AddRange(activeRefferal);

            var healthCareFromDB = await _healthCareTeamDAL.FetchHealthCareTeamDetails(orgId, patient_id);

            if (healthCareFromDB.Count > 0)
            {
                //var healthCareFromDB = healthCareTeams;
                var healthCareString = "";
                healthCareFromDB.ToList().ForEach(healthCare =>
                {
                    if (healthCare.ProviderCompanyAssocsInfo is not null)
                    {

                        healthCareString +=
                        healthCare.ProviderCompanyAssocsInfo.FirstName + " " + healthCare.ProviderCompanyAssocsInfo.SurName +
                        ", " + healthCare.ProviderCompanyAssocsInfo.CompanyName + ", ";
                    }
                    if (healthCare.ProviderCompanyPostalAddress is not null)
                    {

                        healthCareString +=
                        (string.IsNullOrWhiteSpace(healthCare.ProviderCompanyPostalAddress.AddressLine1) ? string.Empty : (healthCare.ProviderCompanyPostalAddress.AddressLine1 + ", ")) +
                        (string.IsNullOrWhiteSpace(healthCare.ProviderCompanyPostalAddress.AddressLine2) ? string.Empty : (healthCare.ProviderCompanyPostalAddress.AddressLine2 + ", ")) +
                        (string.IsNullOrWhiteSpace(healthCare.ProviderCompanyPostalAddress.Suburb) ? string.Empty : (healthCare.ProviderCompanyPostalAddress.Suburb + ", ")) +
                        Enum.GetName(typeof(State), healthCare.ProviderCompanyPostalAddress.StateId) + " " + healthCare.ProviderCompanyPostalAddress.PostCode + ";";
                    }
                    if (healthCare.ProviderLocation is not null)
                    {
                        healthCareString +=
                        healthCare.ProviderLocation + ",";
                    }
                    if (healthCare.ProviderLocationPostalAddress is not null)
                    {
                        healthCareString +=
                        (string.IsNullOrWhiteSpace(healthCare.ProviderLocationPostalAddress.AddressLine1) ? string.Empty : (healthCare.ProviderLocationPostalAddress.AddressLine1 + ", ")) +
                        (string.IsNullOrWhiteSpace(healthCare.ProviderLocationPostalAddress.AddressLine2) ? string.Empty : (healthCare.ProviderLocationPostalAddress.AddressLine2 + ", ")) +
                        (string.IsNullOrWhiteSpace(healthCare.ProviderLocationPostalAddress.Suburb) ? string.Empty : (healthCare.ProviderLocationPostalAddress.Suburb + ", ")) +
                        Enum.GetName(typeof(State), healthCare.ProviderLocationPostalAddress.StateId) + " " + healthCare.ProviderLocationPostalAddress.PostCode + ";";

                    }
                    healthCareString += "##NewLine##";

                });

                KeyValuePair<string, string> healthCareData = new KeyValuePair<string, string>("##Healthcareteam##", healthCareString);
                patientTagsModel.Data.Add(healthCareData);
            }
            else
            {
                KeyValuePair<string, string> healthCareDataEmpty = new KeyValuePair<string, string>("##Healthcareteam##", string.Empty);
                patientTagsModel.Data.Add(healthCareDataEmpty);
            }

            ICLetter iCompanyLetter = new();
            if (icFromDB is not null)
            {
                iCompanyLetter["##IC_Internal_Company_Name##"] = icFromDB.Name;
                iCompanyLetter["##IC_Internal_Company_Email##"] = icFromDB.Email;
                iCompanyLetter["##IC_ACN##"] = icFromDB.Acn;
                iCompanyLetter["##IC_ABN##"] = icFromDB.Abn;
                iCompanyLetter["##IC_HPIO_Location##"] = icFromDB.Hpiolocation;
                iCompanyLetter["##IC_HPIO_Number##"] = icFromDB.Hpionumber.ToString();
                iCompanyLetter["##IC_Bank_Details##"] = icFromDB.BankDetails;
                iCompanyLetter["##IC_Account_Name##"] = icFromDB.AccountName;
                iCompanyLetter["##IC_BSB##"] = icFromDB.Bsb;
                iCompanyLetter["##IC_Account_Number##"] = icFromDB.AccountNo;
                iCompanyLetter["##IC_Work_Number##"] = icFromDB.WorkContact;
                iCompanyLetter["##IC_Fax_Number##"] = icFromDB.FaxNumber;
                iCompanyLetter["##IC_Mobile_Number##"] = icFromDB.Mobile;
                iCompanyLetter["##IC_Facility_ID##"] = icFromDB.FacilityId;

                // Fetch Internal Company Addresses
                var icpostAddress = icFromDB.CompanyAddresses.ToList().FirstOrDefault(p => p.AddressType == Convert.ToInt16(AddressType.Postal) && p.StatusId == (short)Status.Active);
                if (icpostAddress is not null)
                {
                    iCompanyLetter["##IC_Postal_AddressLine1##"] = string.IsNullOrWhiteSpace(icpostAddress.AddressLine1) ? string.Empty : icpostAddress.AddressLine1;
                    iCompanyLetter["##IC_Postal_AddressLine2##"] = string.IsNullOrWhiteSpace(icpostAddress.AddressLine2) ? string.Empty : icpostAddress.AddressLine2;
                    iCompanyLetter["##IC_Postal_Suburb##"] = string.IsNullOrWhiteSpace(icpostAddress.Suburb) ? string.Empty : icpostAddress.Suburb;
                    iCompanyLetter["##IC_Postal_State##"] = Enum.GetName(typeof(State), icpostAddress.StateId);
                    iCompanyLetter["##IC_Postal_Postcode##"] = icpostAddress.PostCode;
                }
                else
                {
                    iCompanyLetter["##IC_Postal_AddressLine1##"] = null;
                    iCompanyLetter["##IC_Postal_AddressLine2##"] = null;
                    iCompanyLetter["##IC_Postal_Suburb##"] = null;
                    iCompanyLetter["##IC_Postal_State##"] = null;
                    iCompanyLetter["##IC_Postal_Postcode##"] = null;

                }
                var icOtherAddress = icFromDB.CompanyAddresses.ToList().FirstOrDefault(p => p.AddressType == Convert.ToInt16(AddressType.Other) && p.StatusId == (short)Status.Active);
                if (icOtherAddress is not null)
                {
                    iCompanyLetter["##IC_Other_AddressLine1##"] = string.IsNullOrWhiteSpace(icOtherAddress.AddressLine1) ? string.Empty : icOtherAddress.AddressLine1;
                    iCompanyLetter["##IC_Other_AddressLine2##"] = string.IsNullOrWhiteSpace(icOtherAddress.AddressLine2) ? string.Empty : icOtherAddress.AddressLine2;
                    iCompanyLetter["##IC_Other_Suburb##"] = string.IsNullOrWhiteSpace(icOtherAddress.Suburb) ? string.Empty : icOtherAddress.Suburb;
                    iCompanyLetter["##IC_Other_State##"] = Enum.GetName(typeof(State), icOtherAddress.StateId);
                    iCompanyLetter["##IC_Other_Postcode##"] = icOtherAddress.PostCode;
                }
                else
                {
                    iCompanyLetter["##IC_Other_AddressLine1##"] = null;
                    iCompanyLetter["##IC_Other_AddressLine2##"] = null;
                    iCompanyLetter["##IC_Other_Suburb##"] = null;
                    iCompanyLetter["##IC_Other_State##"] = null;
                    iCompanyLetter["##IC_Other_Postcode##"] = null;
                }


                if (icFromDB.LogoFileDetailsId != null && icFromDB.LogoFileDetailsId > 0)
                {
                    string logoSasToken = await GetLogoforCompany(icFromDB.LogoFileDetailsId, baseHttpRequestContext);
                    if (!string.IsNullOrEmpty(logoSasToken))
                        iCompanyLetter["##IC_Company_Image##"] = $"<img src='{logoSasToken}'/>";
                }
                else
                    iCompanyLetter["##IC_Company_Image##"] = null;
            }


            patientTagsModel.Data.AddRange(iCompanyLetter);
            var ipFromDB = await _patientDAL.GetLoggedInUser(baseHttpRequestContext.UserId, orgId);
            IPLetter iUserLetter = new();
            if (ipFromDB is not null && ipFromDB.UserTypeId == (short)UserType.Internal_Provider)
            {
                iUserLetter["##IP_Title##"] = ipFromDB.TitleId is not null ? EnumExtensions.GetDescription((TitleType)ipFromDB.TitleId) : null; ;
                iUserLetter["##IP_FirstName##"] = ipFromDB.FirstName;
                iUserLetter["##IP_Surname##"] = ipFromDB.SurName;
                iUserLetter["##IP_Salutation##"] = ipFromDB.Salutation;
                iUserLetter["##IP_Qualification##"] = ipFromDB.Qualification;
                iUserLetter["##IP_Specialisation##"] = ipFromDB.Specialisation;
                iUserLetter["##IP_Date_of_Birth##"] = ipFromDB.DateofBirth is not null ? ((DateTime)ipFromDB.DateofBirth).ToString("dd/MM/yyyy") : null;
                iUserLetter["##IP_APHRA_Number##"] = ipFromDB.AphraNumber;
                iUserLetter["##IP_Provider_Type##"] = ipFromDB.ProviderTypeId is not null ? EnumExtensions.GetDescription((ProviderType)ipFromDB.ProviderTypeId) : null;
                iUserLetter["##IP_HPI-I_Number##"] = ipFromDB.HPIINumber;
                iUserLetter["##IP_Prescriber_Number##"] = ipFromDB.PrescriberNumber;
                iUserLetter["##IP_Internal_Entity_Provider_Number##"] = ipFromDB.UserCompanyAssocs.Where(e => e.CompanyId == icFromDB.Id).FirstOrDefault() is not null ?
                                                                        ipFromDB.UserCompanyAssocs.Where(e => e.CompanyId == icFromDB.Id).FirstOrDefault().ProviderNumber : null;
                iUserLetter["##IP_Internal_Entity_Provider_Location##"] = ipFromDB.UserCompanyAssocs.Where(e => e.CompanyId == icFromDB.Id).FirstOrDefault() is not null ?
                                                                            icFromDB.Name : null;
                if (ipFromDB.IsInternalEntityBank == false)
                {
                    iUserLetter["##IP_Account_Name##"] = ipFromDB.AccountName;
                    iUserLetter["##IP_BSB##"] = ipFromDB.BSB;
                    iUserLetter["##IP_Account_Number##"] = ipFromDB.AccountNo;
                    iUserLetter["##IP_Bank_Details##"] = ipFromDB.BankDetails;
                }
                else
                {
                    iUserLetter["##IP_Account_Name##"] = (icFromDB is null) ? null : icFromDB.AccountName;
                    iUserLetter["##IP_BSB##"] = (icFromDB is null || string.IsNullOrWhiteSpace(icFromDB.Bsb)) ? null : icFromDB.Bsb.ToString();
                    iUserLetter["##IP_Account_Number##"] = (icFromDB is null) ? null : icFromDB.AccountNo;
                    iUserLetter["##IP_Bank_Details##"] = (icFromDB is null) ? null : icFromDB.BankDetails;
                }

                patientTagsModel.Data.AddRange(iUserLetter);

            }
            //For AddressorData
            if (patientLetterTemplateFilterModel.AddressorEntityId.HasValue && patientLetterTemplateFilterModel.AddressorEntityId.Value > 0)
            {
                ADLetter adUserLetter = new();
                var adFromDB = await _patientDAL.GetLoggedInUser(patientLetterTemplateFilterModel.AddressorEntityId.Value, orgId);
                adUserLetter["##AD_Title##"] = adFromDB.TitleId is not null ? EnumExtensions.GetDescription((TitleType)adFromDB.TitleId) : null; ;
                adUserLetter["##AD_FirstName##"] = adFromDB.FirstName;
                adUserLetter["##AD_Surname##"] = adFromDB.SurName;
                adUserLetter["##AD_Salutation##"] = adFromDB.Salutation;
                adUserLetter["##AD_Qualification##"] = adFromDB.Qualification;
                adUserLetter["##AD_Specialisation##"] = adFromDB.Specialisation;
                adUserLetter["##AD_Date_of_Birth##"] = adFromDB.DateofBirth is not null ? ((DateTime)adFromDB.DateofBirth).ToString("dd/MM/yyyy") : null;
                adUserLetter["##AD_APHRA_Number##"] = adFromDB.AphraNumber;
                adUserLetter["##AD_Provider_Type##"] = adFromDB.ProviderTypeId is not null ? EnumExtensions.GetDescription((ProviderType)adFromDB.ProviderTypeId) : null;
                adUserLetter["##AD_HPI-I_Number##"] = adFromDB.HPIINumber;
                adUserLetter["##AD_Prescriber_Number##"] = adFromDB.PrescriberNumber;
                adUserLetter["##AD_Internal_Entity_Provider_Number##"] = adFromDB.UserCompanyAssocs.Where(e => e.CompanyId == icFromDB.Id).FirstOrDefault() is not null ?
                                                                        adFromDB.UserCompanyAssocs.Where(e => e.CompanyId == icFromDB.Id).FirstOrDefault().ProviderNumber : null;
                adUserLetter["##AD_Internal_Entity_Provider_Location##"] = adFromDB.UserCompanyAssocs.Where(e => e.CompanyId == icFromDB.Id).FirstOrDefault() is not null ?
                                                                            icFromDB.Name : null;
                if (ipFromDB.IsInternalEntityBank == false)
                {
                    adUserLetter["##AD_Account_Name##"] = adFromDB.AccountName;
                    adUserLetter["##AD_BSB##"] = adFromDB.BSB;
                    adUserLetter["##AD_Account_Number##"] = adFromDB.AccountNo;
                    adUserLetter["##AD_Bank_Details##"] = adFromDB.BankDetails;
                }
                else
                {
                    adUserLetter["##AD_Account_Name##"] = (icFromDB is null) ? null : icFromDB.AccountName;
                    adUserLetter["##AD_BSB##"] = (icFromDB is null || string.IsNullOrWhiteSpace(icFromDB.Bsb)) ? null : icFromDB.Bsb.ToString();
                    adUserLetter["##AD_Account_Number##"] = (icFromDB is null) ? null : icFromDB.AccountNo;
                    adUserLetter["##AD_Bank_Details##"] = (icFromDB is null) ? null : icFromDB.BankDetails;
                }

                patientTagsModel.Data.AddRange(adUserLetter);


            }
            APVLetter apvLetter = new();

            //For Addressee Provider Data
            if (patientLetterTemplateFilterModel.AddresseeProviderId.HasValue && patientLetterTemplateFilterModel.AddresseeProviderId.Value > 0)
            {
                AddresseeProvider apvFromDB = await _patientDAL.GetAddresseeProvider(patientLetterTemplateFilterModel.AddresseeProviderId.Value, orgId);
                UserDetail apvProviderData = apvFromDB.ProviderData;

                apvLetter["##APV_Title##"] = apvProviderData.TitleId is not null ? EnumExtensions.GetDescription((TitleType)apvProviderData.TitleId) : null; ;
                apvLetter["##APV_FirstName##"] = apvProviderData.FirstName;
                apvLetter["##APV_Surname##"] = apvProviderData.SurName;
                apvLetter["##APV_Salutation##"] = apvProviderData.Salutation;
                apvLetter["##APV_Specialisation##"] = apvProviderData.Specialisation;

                // External Company Addresses
                CompanyDetail apvCompanyData = apvFromDB.CompanyData;
                var apvpostAddress = apvCompanyData.CompanyAddresses.ToList().FirstOrDefault(p => p.AddressType == Convert.ToInt16(AddressType.Postal) && p.StatusId == (short)Status.Active);
                if (apvpostAddress is not null)
                {
                    apvLetter["##APV_Postal_AddressLine1##"] = string.IsNullOrWhiteSpace(apvpostAddress.AddressLine1) ? string.Empty : apvpostAddress.AddressLine1;
                    apvLetter["##APV_Postal_AddressLine2##"] = string.IsNullOrWhiteSpace(apvpostAddress.AddressLine2) ? string.Empty : apvpostAddress.AddressLine2;
                    apvLetter["##APV_Postal_Suburb##"] = string.IsNullOrWhiteSpace(apvpostAddress.Suburb) ? string.Empty : apvpostAddress.Suburb;
                    apvLetter["##APV_Postal_State##"] = Enum.GetName(typeof(State), apvpostAddress.StateId);
                    apvLetter["##APV_Postal_Postcode##"] = apvpostAddress.PostCode;
                }
                else
                {
                    apvLetter["##APV_Postal_AddressLine1##"] = null;
                    apvLetter["##APV_Postal_AddressLine2##"] = null;
                    apvLetter["##APV_Postal_Suburb##"] = null;
                    apvLetter["##APV_Postal_State##"] = null;
                    apvLetter["##APV_Postal_Postcode##"] = null;

                }
                var apvOtherAddress = apvCompanyData.CompanyAddresses.ToList().FirstOrDefault(p => p.AddressType == Convert.ToInt16(AddressType.Other) && p.StatusId == (short)Status.Active);
                if (apvOtherAddress is not null)
                {
                    apvLetter["##APV_Other_AddressLine1##"] = string.IsNullOrWhiteSpace(apvOtherAddress.AddressLine1) ? string.Empty : apvOtherAddress.AddressLine1;
                    apvLetter["##APV_Other_AddressLine2##"] = string.IsNullOrWhiteSpace(apvOtherAddress.AddressLine2) ? string.Empty : apvOtherAddress.AddressLine2;
                    apvLetter["##APV_Other_Suburb##"] = string.IsNullOrWhiteSpace(apvOtherAddress.Suburb) ? string.Empty : apvOtherAddress.Suburb;
                    apvLetter["##APV_Other_State##"] = Enum.GetName(typeof(State), apvOtherAddress.StateId);
                    apvLetter["##APV_Other_Postcode##"] = apvOtherAddress.PostCode;
                }
                else
                {
                    apvLetter["##APV_Other_AddressLine1##"] = null;
                    apvLetter["##APV_Other_AddressLine2##"] = null;
                    apvLetter["##APV_Other_Suburb##"] = null;
                    apvLetter["##APV_Other_State##"] = null;
                    apvLetter["##APV_Other_Postcode##"] = null;
                }
                ECLetter ecLetter = await GetExtCompanyTags(apvFromDB.CompanyData.Id, orgId);
                if (ecLetter is not null)
                {
                    patientTagsModel.Data.AddRange(ecLetter);
                }
                else
                {
                    ecLetter = GetExtCompanyTagsEmptyData();
                    patientTagsModel.Data.AddRange(ecLetter);
                }
            }
            else
            {
                apvLetter["##APV_Other_AddressLine1##"] = null;
                apvLetter["##APV_Other_AddressLine2##"] = null;
                apvLetter["##APV_Other_Suburb##"] = null;
                apvLetter["##APV_Other_State##"] = null;
                apvLetter["##APV_Other_Postcode##"] = null;
                apvLetter["##APV_Postal_AddressLine1##"] = null;
                apvLetter["##APV_Postal_AddressLine2##"] = null;
                apvLetter["##APV_Postal_Suburb##"] = null;
                apvLetter["##APV_Postal_State##"] = null;
                apvLetter["##APV_Postal_Postcode##"] = null;
                apvLetter["##APV_Title##"] = null; ;
                apvLetter["##APV_FirstName##"] = null;
                apvLetter["##APV_Surname##"] = null;
                apvLetter["##APV_Salutation##"] = null;
                apvLetter["##APV_Specialisation##"] = null;

            }
            patientTagsModel.Data.AddRange(apvLetter);
            if (patientLetterTemplateFilterModel.AddresseeCompanyId.HasValue && patientLetterTemplateFilterModel.AddresseeCompanyId.Value > 0)
            {
                ECLetter ecLetter = await GetExtCompanyTags((int)patientLetterTemplateFilterModel.AddresseeCompanyId, orgId);
                if (ecLetter is not null)
                {
                    patientTagsModel.Data.AddRange(ecLetter);
                }
            }
            else
            {
                ECLetter ecLetter = GetExtCompanyTagsEmptyData();
                patientTagsModel.Data.AddRange(ecLetter);

            }
            if (patientLetterTemplateFilterModel.AppointmentDetailsId is null)
            {


                APLetter appointmentData = new();
                appointmentData["##AP_Appointment_Type##"] = null;
                appointmentData["##AP_Appointment_Date##"] = null;
                appointmentData["##AP_Appointment_Time##"] = null;
                appointmentData["##AP_Appointment_Provider##"] = null; ;
                appointmentData["##AP_Appointment_Entity##"] = null;
                appointmentData["##AP_Admission_Date##"] = null;
                appointmentData["##AP_Admission_Time##"] = null;
                appointmentData["##AP_Appointment_Details##"] = null;
                appointmentData["##AP_Anaesthetic_Type##"] = null;
                appointmentData["##AP_Anaesthetist##"] = null;
                appointmentData["##AP_Assistant##"] = null;
                appointmentData["##AP_Hospital##"] = null;
                appointmentData["##AP_Facility_ID##"] = null;
                appointmentData["##AP_MBS_Item##"] = null;
                appointmentData["##AP_Appointment_Location##"] = null;
                patientTagsModel.Data.AddRange(appointmentData);

            }

            /*
            // Commented because from frontend directly calling the appointment API to fetch the Appoinmtment Tags
            //For AppointmentData
            if (patientLetterTemplateFilterModel.AppointmentDetailsId.HasValue && patientLetterTemplateFilterModel.AppointmentDetailsId.Value > 0)
            {
                AppointmentTagsForLetter apptFromDB = await GetAppointmentData(orgId,patientLetterTemplateFilterModel.AppointmentDetailsId.Value,baseHttpRequestContext);
                if(apptFromDB is not null)
                {
                    patientTagsModel.Data.AddRange(apptFromDB.Data);
                }


            }*/

            //For MedicalRecordsData

            MRLetter medicalRecLetter = new();
            var mrFromDB = await _medicalRecordsDAL.GetMedicalProcedures(orgId, patient_id);
            var healthData = await _healthDetailsDAL.FetchHealthDetails(orgId, patient_id);

            if (mrFromDB is not null && mrFromDB.Count > 0)
            {
                List<MedicalProcedures> alleryList = mrFromDB.Where(m => m.Type == (short)MedicalHistoryTypes.Allergies).ToList();
                medicalRecLetter["##PMD_Allergies##"] = GetMedicalProcedureStrings(alleryList);
                List<MedicalProcedures> mHistoryList = mrFromDB.Where(m => m.Type == (short)MedicalHistoryTypes.Past_medical_history).ToList();
                medicalRecLetter["##PMD_Medical_History##"] = GetMedicalProcedureStrings(mHistoryList);
                List<MedicalProcedures> medicationList = mrFromDB.Where(m => m.Type == (short)MedicalHistoryTypes.Medications).ToList();
                medicalRecLetter["##PMD_Medications##"] = GetMedicalProcedureStrings(medicationList);
                List<MedicalProcedures> malertList = mrFromDB.Where(m => m.Type == (short)MedicalHistoryTypes.Medical_alerts && m.IsActive == true).ToList();
                medicalRecLetter["##PMD_Alerts##"] = GetMedicalProcedureStrings(malertList);
                if (healthData is not null)
                {
                    medicalRecLetter["##PMD_BMI##"] = (healthData.Bmi is null) ? null : healthData.Bmi.ToString();
                }
                else
                {
                    medicalRecLetter["##PMD_BMI##"] = null;
                }


            }
            else
            {
                medicalRecLetter["##PMD_BMI##"] = null;
                medicalRecLetter["##PMD_Alerts##"] = null;
                medicalRecLetter["##PMD_Allergies##"] = null;
                medicalRecLetter["##PMD_Medical_History##"] = null;
                medicalRecLetter["##PMD_Medications##"] = null;
            }
            patientTagsModel.Data.AddRange(medicalRecLetter);

            //For DateTime 
            var sydneyDateTimeNow = DateTimeConversion.ConvertTimeFromUtc(DateTime.UtcNow, "AUS Eastern Standard Time");
            var sydneyDateNow = sydneyDateTimeNow is not null ? ((DateTime)sydneyDateTimeNow).ToString("dd/MM/yyyy") : null;
            if (string.IsNullOrEmpty(sydneyDateNow))
            {
                sydneyDateTimeNow = DateTimeConversion.ConvertTimeFromUtc(DateTime.UtcNow, "Australia/Sydney");
                sydneyDateNow = sydneyDateTimeNow is not null ? ((DateTime)sydneyDateTimeNow).ToString("dd/MM/yyyy") : null;
            }
            KeyValuePair<string, string> D_Today = new KeyValuePair<string, string>("##D_Today##", sydneyDateNow);
            patientTagsModel.Data.Add(D_Today);

            if (patientLetterTemplateFilterModel.InvoiceDetailsId.HasValue && patientLetterTemplateFilterModel.InvoiceDetailsId.Value > 0)
            {
                //InterServiceCalltoGetFinanceDetails

                if (patientLetterTemplateFilterModel.FinanceSummaryTable.Value)
                {


                    var financeTagResponse = await GetFinanceTemplateTagData(baseHttpRequestContext, patientLetterTemplateFilterModel.InvoiceDetailsId.Value);
                    if (financeTagResponse.StatusCode == StatusCodes.Status200OK)
                    {
                        var financeTagData = financeTagResponse.Result;
                        FinanceDetailsForLetter financeDetails = new();
                        financeDetails["##AccountHolder##"] = financeTagData.AccountHolder;
                        financeDetails["##EstimateNumber##"] = financeTagData.EstimateNumber.ToString();
                        financeDetails["##EstimateDate##"] = financeTagData.EstimateDate.HasValue ? financeTagData.EstimateDate.ToString() : "To be Confirmed";
                        financeDetails["##AppointmentDate##"] = financeTagData.AppointmentDate.HasValue ? financeTagData.AppointmentDate.Value.ToString() : "To be Confirmed";
                        financeDetails["##AdmissionDate##"] = financeTagData.AdmissionDate.HasValue ? financeTagData.AdmissionDate.Value.ToString() : "To be Confirmed";
                        financeDetails["##ProcedureDetails##"] = financeTagData.ProcedureDetails;
                        financeDetails["##ServiceProvider##"] = financeTagData.ServiceProvider;

                        var summaryTotalRow = new InvoiceSummaryTagData()
                        {
                            Description = "Total",
                            FeeValue = financeTagData.invoiceSummaryTagDatas.Sum(x => x.FeeValue),
                            MedicareValue = financeTagData.invoiceSummaryTagDatas.Sum(x => x.MedicareValue),
                            HealthFund = financeTagData.invoiceSummaryTagDatas.Sum(x => x.HealthFund),
                            OopValue = financeTagData.invoiceSummaryTagDatas.Sum(x => x.OopValue),
                            BalanceDue = financeTagData.invoiceSummaryTagDatas.Sum(x => x.BalanceDue)
                        };
                        DataTable dt = new DataTable();

                        dt.Columns.AddRange(new DataColumn[6] { new DataColumn("Description", typeof(string)),
                            new DataColumn("Fee (Inc GST)", typeof(string)),
                            new DataColumn("Medicare",typeof(string)),
                            new DataColumn("Health Fund",typeof(string)),
                            new DataColumn("OOP",typeof(string)),
                            new DataColumn("Balance Due",typeof(string))
                            });

                        foreach (var data in financeTagData.invoiceSummaryTagDatas)
                        {
                            dt.Rows.Add(data.Description,
                                string.Format(new CultureInfo("en-AU", false), "{0:C}", data.FeeValue),
                                string.Format(new CultureInfo("en-AU", false), "{0:C}", data.MedicareValue),
                                string.Format(new CultureInfo("en-AU", false), "{0:C}", data.HealthFund),
                                string.Format(new CultureInfo("en-AU", false), "{0:C}", data.OopValue),
                                string.Format(new CultureInfo("en-AU", false), "{0:C}", data.BalanceDue));
                        }

                        StringBuilder sb = new StringBuilder();
                        sb.Append("<table cellpadding='3' cellspacing='0' style='font-size: 9pt;font-family:Calibri'>");
                        sb.Append("<tr>");
                        foreach (DataColumn column in dt.Columns)
                        {
                            if (column.ColumnName == "Description")
                                sb.Append("<th style='background-color: #E5E6E6;color:#35404A;text-align: left;padding-left:5px;margin-top:5px;margin-bottom:5px;'>" + column.ColumnName + "</th>");
                            else
                            {
                                if (column.ColumnName == "Balance Due")
                                    sb.Append("<th style='background-color: #E5E6E6;color:#35404A;text-align: right;padding-right:5px;margin-top:5px;margin-bottom:5px;'>" + column.ColumnName + "</th>");

                                else
                                    sb.Append("<th style='background-color: #E5E6E6;color:#35404A;text-align: right;margin-top:5px;margin-bottom:5px;'>" + column.ColumnName + "</th>");

                            }

                        }
                        sb.Append("</tr>");

                        //Adding DataRow.
                        int i = 0;

                        foreach (DataRow row in dt.Rows)
                        {
                            sb.Append("<tr>");
                            string bkgcolor = string.Empty;
                            if (i % 2 == 0)
                            {
                                bkgcolor = "#FFFFFF";
                            }
                            else
                                bkgcolor = "#F5F6F8";
                            foreach (DataColumn column in dt.Columns)
                            {


                                if (column.ColumnName != "Description")
                                {
                                    if (column.ColumnName == "Balance Due")
                                        sb.Append($"<td style='text-align: right;padding-right:5px;width:100px;color:35404A;background-color:{bkgcolor};'>" + row[column.ColumnName].ToString() + "</td>");
                                    else
                                        sb.Append($"<td style='text-align: right;width:100px;color:35404A;background-color:{bkgcolor};'>" + row[column.ColumnName].ToString() + "</td>");

                                }
                                else
                                    sb.Append($"<td style='text-align: left;padding-left:5px;width:100px;color:35404A;background-color:{bkgcolor};'>" + row[column.ColumnName].ToString() + "</td>");
                            }
                            sb.Append("</tr>");
                            i++;
                        }

                        // Add Footer
                        sb.Append("<tfoot><tr style='outline: 1px solid #000'>");
                        sb.Append("<td style='text-align: right; font-weight:bold;background-color: #E5E6E6;color:35404A;margin-top:2px;margin-bottom:2px;'>" + summaryTotalRow.Description + "</td>");
                        sb.Append("<td style='text-align: right;font-weight:bold; background-color: #E5E6E6;color:35404A;margin-top:2px;margin-bottom:2px;'>" + string.Format(new CultureInfo("en-AU", false), "{0:C}", summaryTotalRow.FeeValue) + "</td>");
                        sb.Append("<td style='text-align: right; font-weight:bold;background-color: #E5E6E6;color:35404A;margin-top:2px;margin-bottom:2px;'>" + string.Format(new CultureInfo("en-AU", false), "{0:C}", summaryTotalRow.MedicareValue) + "</td>");
                        sb.Append("<td style='text-align: right; font-weight:bold;background-color: #E5E6E6;color:35404A;margin-top:2px;margin-bottom:2px;'>" + string.Format(new CultureInfo("en-AU", false), "{0:C}", summaryTotalRow.HealthFund) + "</td>");
                        sb.Append("<td style='text-align: right; font-weight:bold;background-color: #E5E6E6;color:35404A;margin-top:2px;margin-bottom:2px;'>" + string.Format(new CultureInfo("en-AU", false), "{0:C}", summaryTotalRow.OopValue) + "</td>");
                        sb.Append("<td style='text-align: right; font-weight:bold;background-color: #E5E6E6;color:35404A;margin-top:2px;margin-bottom:2px;'>" + string.Format(new CultureInfo("en-AU", false), "{0:C}", summaryTotalRow.BalanceDue) + "</td>");
                        sb.Append("</tr></tfoot>");


                        //Table end.
                        sb.Append("</table>");
                        string tableHtmlText = sb.ToString();
                        var sfdtResult = await GetSFDTTextData(tableHtmlText, baseHttpRequestContext);
                        if (sfdtResult.StatusCode == StatusCodes.Status200OK)
                        {
                            financeDetails["##FinanceSummaryTable##"] = sfdtResult.Result.ToString();
                        }

                        if (financeDetails.Any())
                        {
                            patientTagsModel.Data.AddRange(financeDetails);
                        }

                    }


                }

                if (patientLetterTemplateFilterModel.FinanceDetailedTable.Value)
                {
                    var financeTagResponse = await GetFinanceDetailedTemplateTagData(baseHttpRequestContext, patientLetterTemplateFilterModel.InvoiceDetailsId.Value);

                    if (financeTagResponse.StatusCode == StatusCodes.Status200OK)
                    {
                        var financeTagData = financeTagResponse.Result.InvoiceTagsDetail;
                        StringBuilder sb = new StringBuilder();
                        FinanceDetailsForLetter financeDetails = new();

                        DataTable dt = new DataTable();
                        List<string> leftColumns = new()
                        {
                            "Description",
                            "Item No",
                            "Qty"
                        };
                        dt.Columns.AddRange(new DataColumn[7] {
                                    new DataColumn("Item No", typeof(string)),
                                    new DataColumn("Description", typeof(string)),
                                    new DataColumn("Qty", typeof(string)),
                                    new DataColumn("Fee (Inc GST)", typeof(string)),
                                    new DataColumn("Medicare",typeof(string)),
                                    new DataColumn("Health Fund",typeof(string)),
                                    new DataColumn("OOP",typeof(string)),
                                    });

                        sb.Append("<table cellspacing='0' cellpadding='3' style='font-size: 9pt;width:100%;font-family:Calibri'>");
                        sb.Append("<tr>");
                        foreach (DataColumn column in dt.Columns)
                        {
                            if (leftColumns.Contains(column.ColumnName))
                            {
                                if (column.ColumnName == "Qty")
                                    sb.Append("<th style='background-color: #E5E6E6;color:#35404A;width:4%;text-align: left;margin-top:5px;margin-bottom:5px;'>" + column.ColumnName + "</th>");
                                else if (column.ColumnName == "Description")
                                {
                                    sb.Append("<th style='background-color: #E5E6E6;color:#35404A;width:25%;text-align: left;margin-top:5px;margin-bottom:5px;'>" + column.ColumnName + "</th>");

                                }
                                else
                                {
                                    sb.Append("<th style='background-color: #E5E6E6;color:#35404A;padding-left:5px;width:9%;text-align: left;margin-top:5px;margin-bottom:5px;'>" + column.ColumnName + "</th>");

                                }
                            }
                            else
                            {
                                if (column.ColumnName == "OOP")
                                    sb.Append("<th style='background-color: #E5E6E6;color:#35404A;padding-right:5px;width:15.5%;text-align: right;margin-top:5px;margin-bottom:5px;'>" + column.ColumnName + "</th>");
                                else
                                    sb.Append("<th style='background-color: #E5E6E6;color:#35404A;width:15.5%;text-align: right;margin-top:5px;margin-bottom:5px;'>" + column.ColumnName + "</th>");

                            }
                        }
                        sb.Append("</tr>");

                        financeDetails["##AccountHolder##"] = financeTagData[0].AccountHolder;
                        financeDetails["##EstimateNumber##"] = financeTagData[0].EstimateNumber.ToString();
                        financeDetails["##EstimateDate##"] = financeTagData[0].EstimateDate.HasValue ? financeTagData[0].EstimateDate.Value.ToString() : "To be Confirmed";
                        financeDetails["##AppointmentDate##"] = financeTagData[0].AppointmentDate.HasValue ? financeTagData[0].AppointmentDate.Value.ToString() : "To be Confirmed";
                        financeDetails["##AdmissionDate##"] = financeTagData[0].AdmissionDate.HasValue ? financeTagData[0].AdmissionDate.Value.ToString() : "To be Confirmed";
                        financeDetails["##ProcedureDetails##"] = financeTagData[0].ProcedureDetails;
                        financeDetails["##ServiceProvider##"] = financeTagData[0].ServiceProvider;

                        foreach (var data in financeTagData)
                        {
                            dt.Rows.Clear();

                            var summaryTotalRow = new InvoiceSummaryTagData()
                            {
                                Description = "Total",
                                FeeValue = data.invoiceSummaryTagDatas.Sum(x => x.FeeValue),
                                MedicareValue = data.invoiceSummaryTagDatas.Sum(x => x.MedicareValue),
                                HealthFund = data.invoiceSummaryTagDatas.Sum(x => x.HealthFund),
                                OopValue = data.invoiceSummaryTagDatas.Sum(x => x.OopValue),
                            };

                            foreach (var row in data.invoiceSummaryTagDatas)
                            {
                                dt.Rows.Add(
                                    row.ItemNumner.ToString(),
                                    row.Description,
                                    row.Quantity.ToString(),
                                    string.Format(new CultureInfo("en-AU", false), "{0:C}", row.FeeValue),
                                    string.Format(new CultureInfo("en-AU", false), "{0:C}", row.MedicareValue),
                                    string.Format(new CultureInfo("en-AU", false), "{0:C}", row.HealthFund),
                                    string.Format(new CultureInfo("en-AU", false), "{0:C}", row.OopValue));
                            }

                            // Add Top Provider details Row
                            sb.Append($"<tr><td colspan='7' style='text-align: left; background-color: #D3D3D3;font-weight:bold;'>" + data.ServiceProvider + "</td></tr>");

                            //Adding DataRow.
                            int i = 0;

                            foreach (DataRow row in dt.Rows)
                            {
                                sb.Append("<tr>");
                                string bkgcolor = string.Empty;
                                if (i % 2 == 0)
                                {
                                    bkgcolor = "#FFFFFF";
                                }
                                else
                                    bkgcolor = "#F5F6F8";
                                foreach (DataColumn column in dt.Columns)
                                {
                                    if (leftColumns.Contains(column.ColumnName))
                                    {
                                        if (column.ColumnName == "Qty")
                                            sb.Append($"<td style='text-align: left;width:4%;color:35404A;background-color:{bkgcolor};'>" + row[column.ColumnName].ToString() + "</td>");
                                        else if (column.ColumnName == "Description")
                                        {
                                            sb.Append($"<td style='text-align: left;width:25%;color:35404A;background-color:{bkgcolor};'>" + row[column.ColumnName].ToString() + "</td>");

                                        }
                                        else
                                        {
                                            if (column.ColumnName == "Item No" && row[column.ColumnName].ToString() == "0")
                                            {
                                                sb.Append($"<td style='text-align: left;padding-left:5px;width:9%;color:35404A;background-color:{bkgcolor};'>" + string.Empty + "</td>");

                                            }
                                            else
                                                sb.Append($"<td style='text-align: left;padding-left:5px;width:9%;color:35404A;background-color:{bkgcolor};'>" + row[column.ColumnName].ToString() + "</td>");

                                        }
                                    }
                                    else
                                    {
                                        if (column.ColumnName == "OOP")
                                            sb.Append($"<td style='text-align: right;padding-right:5px;width:15.5%;color:35404A;background-color:{bkgcolor};'>" + row[column.ColumnName].ToString() + "</td>");
                                        else
                                            sb.Append($"<td style='text-align: right;width:15.5%;color:35404A;background-color:{bkgcolor};'>" + row[column.ColumnName].ToString() + "</td>");

                                    }
                                }
                                sb.Append("</tr>");
                                i++;
                            }
                            // Add Footer
                            sb.Append("<tr>");
                            sb.Append("<td colspan='3' style='text-align: right; background-color: #E5E6E6;color:35404A;margin-top:2px;margin-bottom:2px;font-weight:bold;'>" + summaryTotalRow.Description + "</td>");
                            sb.Append("<td style='text-align: right; background-color: #E5E6E6;color:35404A;margin-top:2px;margin-bottom:2px;font-weight:bold;'>" + string.Format(new CultureInfo("en-AU", false), "{0:C}", summaryTotalRow.FeeValue) + "</td>");
                            sb.Append("<td style='text-align: right; background-color: #E5E6E6;color:35404A;margin-top:2px;margin-bottom:2px;font-weight:bold;'>" + string.Format(new CultureInfo("en-AU", false), "{0:C}", summaryTotalRow.MedicareValue) + "</td>");
                            sb.Append("<td style='text-align: right; background-color: #E5E6E6;color:35404A;margin-top:2px;margin-bottom:2px;font-weight:bold;'>" + string.Format(new CultureInfo("en-AU", false), "{0:C}", summaryTotalRow.HealthFund) + "</td>");
                            sb.Append("<td style='text-align: right; background-color: #E5E6E6;color:35404A;margin-top:2px;margin-bottom:2px;font-weight:bold;'>" + string.Format(new CultureInfo("en-AU", false), "{0:C}", summaryTotalRow.OopValue) + "</td>");
                            sb.Append("</tr>");

                            //Table end.
                        }

                        sb.Append("<tr style='outline: 1px solid #000'>");
                        sb.Append("<td colspan='3' style='text-align:right;  background-color: #E5E6E6;margin-top:2px;margin-bottom:2px;font-weight:bold;'>" + "Grand Total" + "</td>");
                        sb.Append("<td style='text-align: right;background-color: #E5E6E6;margin-top:2px;margin-bottom:2px;font-weight:bold;'>" + string.Format(new CultureInfo("en-AU", false), "{0:C}", financeTagData.Sum(x => x.invoiceSummaryTagDatas.Sum(s => s.FeeValue))) + "</td>");
                        sb.Append("<td style='text-align: right;background-color: #E5E6E6;margin-top:2px;margin-bottom:2px;font-weight:bold;'>" + string.Format(new CultureInfo("en-AU", false), "{0:C}", financeTagData.Sum(x => x.invoiceSummaryTagDatas.Sum(s => s.MedicareValue))) + "</td>");
                        sb.Append("<td style='text-align: right;background-color: #E5E6E6;margin-top:2px;margin-bottom:2px;font-weight:bold;'>" + string.Format(new CultureInfo("en-AU", false), "{0:C}", financeTagData.Sum(x => x.invoiceSummaryTagDatas.Sum(s => s.HealthFund))) + "</td>");
                        sb.Append("<td style='text-align: right;background-color: #E5E6E6;margin-top:2px;margin-bottom:2px;font-weight:bold;'>" + string.Format(new CultureInfo("en-AU", false), "{0:C}", financeTagData.Sum(x => x.invoiceSummaryTagDatas.Sum(s => s.OopValue))) + "</td>");

                        sb.Append("</tr></table>");
                        var sfdtResult = await GetSFDTTextData(sb.ToString(), baseHttpRequestContext);

                        if (sfdtResult.StatusCode == StatusCodes.Status200OK)
                        {
                            financeDetails["##FinanceDetailedSummary##"] = sfdtResult.Result.ToString();
                        }

                        if (financeDetails.Any())
                        {
                            patientTagsModel.Data.AddRange(financeDetails);
                        }
                    }
                }
            }
            return patientTagsModel;
        }

        private ActiveRefferalLetter GernateEmptyDataForActiveReferralProviderTags(ActiveRefferalLetter activeRefferal)
        {
            activeRefferal["##ARP_Physical_AddressLine1##"] = null;
            activeRefferal["##ARP_Physical_AddressLine2##"] = null;
            activeRefferal["##ARP_Physical_Suburb##"] = null;
            activeRefferal["##ARP_Physical_State##"] = null;
            activeRefferal["##ARP_Physical_Postcode##"] = null;
            activeRefferal["##ARP_Postal_AddressLine1##"] = null;
            activeRefferal["##ARP_Postal_AddressLine2##"] = null;
            activeRefferal["##ARP_Postal_Suburb##"] = null;
            activeRefferal["##ARP_Postal_State##"] = null;
            activeRefferal["##ARP_Postal_Postcode##"] = null;
            activeRefferal["##ARP_Other_AddressLine1##"] = null;
            activeRefferal["##ARP_Other_AddressLine2##"] = null;
            activeRefferal["##ARP_Other_Suburb##"] = null;
            activeRefferal["##ARP_Other_State##"] = null;
            activeRefferal["##ARP_Other_Postcode##"] = null;
            activeRefferal["##ActiveReferral_Referring_Doctor##"] = null;
            activeRefferal["##ActiveReferral_Specialization##"] = null;
            activeRefferal["##ActiveReferral_Provider_Location##"] = null;
            activeRefferal["##ActiveReferral_Provider_Type##"] = null;
            activeRefferal["##ARP_Salutation##"] = null;
            activeRefferal["##ARP_Title##"] = null;
            return activeRefferal;
        }

        private string GetMedicalProcedureStrings(List<MedicalProcedures> medicalPList)
        {
            if (medicalPList is not null && medicalPList.Count > 0)
            {
                var mpString = "";
                medicalPList.ForEach(allergy =>
                {
                    mpString +=
                        allergy?.Procedure + "-" + ((allergy.Date is null) ? "" : ((DateTime)allergy.Date).ToString("dd/MM/yyyy")) + "##NewLine##";

                });
                mpString = mpString.Remove(mpString.LastIndexOf("##NewLine##"));//REMOVING THE LAST ", "
                return mpString;
            }
            return null;
        }

        private async Task<List<InputReferralDetail>> ActiveReferralDetails(int orgId, long patientId, BaseHttpRequestContext baseHttpRequestContext)
        {
            List<InputReferralDetail> lstReferrals = await _referralDAL.FetchReferrals(orgId, patientId);

            foreach (var referral in lstReferrals)
            {
                //Image of the Reffereing doctor is not needed in the Templates. Testing if Needed in any other place 
                // Removing this to avoid the Interservice call to file service which is not needed in this scenario 

                /*if (referral.FileDetailsId != null)   
                {
                    long fileId = (long)referral.FileDetailsId;
                    var token = baseHttpRequestContext.BearerToken;
                    string interServiceToken = baseHttpRequestContext.InterServiceToken;
                    string fileAPiUrl = _appSettings.ApiUrls["FileServiceUrl"] + "/file/file_details/" + fileId;
                    RestClient restClient = new RestClient(fileAPiUrl, null, token, interServiceToken);
                    var fileApiResponse = await restClient.GetAsync<ApiResponse<FileDetailsOutputForId>>(fileAPiUrl, null);
                    if (fileApiResponse.StatusCode == StatusCodes.Status200OK)
                    {
                        referral.FileDetailsOutput = fileApiResponse.Result;
                    }
                }*/

                if (referral.ReferringProviderInfo is not null)
                    referral.ReferringProviderInfo.ProviderType = (referral.ReferringProviderInfo.ProviderTypeId == null) ? null : EnumExtensions.GetDescription((ProviderType)(referral.ReferringProviderInfo.ProviderTypeId));
                referral.ReferralType = (referral.ReferralTypeId == null || referral.ReferralTypeId <= 0) ? null : EnumExtensions.GetDescription((ReferralType)(referral.ReferralTypeId));
            }
            if (lstReferrals.Any())
            {
                OrganisationView orgDetails = await FetchMasterCompanyDetails(baseHttpRequestContext);
                DateTime? currDateTime = GetDateFromOrganisation(orgDetails);
                return lstReferrals.Where(x => x.ExpiryDate is null || x.ExpiryDate >= currDateTime.GetValueOrDefault().Date).ToList();
            }

            return null;
        }

        private async Task<AppointmentTagsForLetter> GetAppointmentData(int orgId, long appointmentId, BaseHttpRequestContext baseHttpRequestContext)
        {

            AppointmentTagsForLetter appTagsOutPut = new();
            var token = baseHttpRequestContext.BearerToken;
            string interServiceToken = baseHttpRequestContext.InterServiceToken;
            string appointAPiUrl = _appSettings.ApiUrls["AppointmentServiceUrl"] + "/appointment/appointment_details_for_letter/" + appointmentId;
            RestClient restClient = new RestClient(appointAPiUrl, null, token, interServiceToken);
            var fileApiResponse = await restClient.GetAsync<ApiResponse<AppointmentTagsForLetter>>(appointAPiUrl, null);
            if (fileApiResponse.StatusCode == StatusCodes.Status200OK)
            {
                appTagsOutPut = fileApiResponse.Result;
                return appTagsOutPut;
            }

            return null;
        }

        private string GetAddressforPatient(List<Address> addressList, short addressType)
        {
            var pAddress = addressList.FirstOrDefault(p => p.AddressType == addressType && p.StatusId == (short)Status.Active);
            if (pAddress != null)
                return (string.IsNullOrWhiteSpace(pAddress.AddressLine1) ? string.Empty : pAddress.AddressLine1 + ",") + (string.IsNullOrWhiteSpace(pAddress.AddressLine2) ? string.Empty : pAddress.AddressLine2 + ",") + (string.IsNullOrWhiteSpace(pAddress.Suburb) ? string.Empty : pAddress.Suburb + ",")
                        + Enum.GetName(typeof(State), pAddress.StateId) + ", " + Enum.GetName(typeof(Country), pAddress.CountryId) + ", " + pAddress.PostCode;


            //  pAddress.AddressLine1 + " " + pAddress.AddressLine2 + " " + pAddress.Suburb + ", " + Enum.GetName(typeof(State), pAddress.StateId) + ", " + Enum.GetName(typeof(Country), pAddress.CountryId) + ", " + pAddress.PostCode;
            else
                return null;
        }

        private string GetAddressforComapny(List<CompanyAddress> addressList, short addressType)
        {
            var pAddress = addressList.FirstOrDefault(p => p.AddressType == addressType);
            if (pAddress != null)
                return (string.IsNullOrWhiteSpace(pAddress.AddressLine1) ? string.Empty : pAddress.AddressLine1 + ",") + (string.IsNullOrWhiteSpace(pAddress.AddressLine2) ? string.Empty : pAddress.AddressLine2 + ",") + (string.IsNullOrWhiteSpace(pAddress.Suburb) ? string.Empty : pAddress.Suburb + ",")
                        + Enum.GetName(typeof(State), pAddress.StateId) + ", " + Enum.GetName(typeof(Country), pAddress.CountryId) + ", " + pAddress.PostCode;

            else
                return null;
        }
        private string GetAddressforUser(List<UserAddress> addressList, short addressType)
        {
            var pAddress = addressList.FirstOrDefault(p => p.AddressType == addressType);
            if (pAddress != null)
                return (string.IsNullOrWhiteSpace(pAddress.AddressLine1) ? string.Empty : pAddress.AddressLine1 + ",") + (string.IsNullOrWhiteSpace(pAddress.AddressLine2) ? string.Empty : pAddress.AddressLine2 + ",") + (string.IsNullOrWhiteSpace(pAddress.Suburb) ? string.Empty : pAddress.Suburb + ",")
                        + Enum.GetName(typeof(State), pAddress.StateId) + ", " + Enum.GetName(typeof(Country), pAddress.CountryId) + ", " + pAddress.PostCode;

            else
                return null;
        }


        /// <summary>
        /// Method to do partial update
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="id"></param>
        /// <param name="patientUpdateView"></param>
        /// <returns></returns>

        public async Task<ApiResponse<string>> PartialUpdatePatient(BaseHttpRequestContext baseHttpRequestContext, long id, PatientUpdateView patientUpdateView)
        {
            ApiResponse<string> apiResponse = new();
            bool isASBChange = false;
            string fullName = string.Empty;
            bool isPatientUpdate = false;
            if (patientUpdateView is not null)
            {

                PatientDetail dbPatient = await _patientDAL.GetPatientDetailsForPartialUpdate(baseHttpRequestContext.OrgId, id);
                if (dbPatient is not null)
                {
                    Dictionary<string, object> propertyDictionary = (Dictionary<string, object>)GetFilledProperties(patientUpdateView);
                    if (propertyDictionary is not null)
                    {
                        if (propertyDictionary.ContainsKey("FirstName") || propertyDictionary.ContainsKey("SurName"))
                        {
                            fullName = (patientUpdateView is null || string.IsNullOrWhiteSpace(patientUpdateView.FirstName)) ? dbPatient.FirstName : patientUpdateView.FirstName;
                            fullName = fullName + " " + ((patientUpdateView is null || string.IsNullOrWhiteSpace(patientUpdateView.SurName)) ? dbPatient.SurName : patientUpdateView.SurName);
                            //fullName = $"{inputPatientDetail.FirstName} {inputPatientDetail.SurName}";
                            if (!fullName.Equals($"{dbPatient.FirstName} {dbPatient.SurName}", StringComparison.OrdinalIgnoreCase))
                                isASBChange = true;
                        }
                        if (propertyDictionary.ContainsKey("DateofBirth") || propertyDictionary.ContainsKey("SurName") || propertyDictionary.ContainsKey("FirstName"))
                        {
                            isPatientUpdate = true;
                        }
                    }


                    int rows = await _patientDAL.PartialUpdatePatient(propertyDictionary, baseHttpRequestContext.UserId, dbPatient);
                    if (rows > 0)
                    {
                        await StorePatientSearchMessage(baseHttpRequestContext.OrgCode);

                        if (isASBChange && !string.IsNullOrWhiteSpace(fullName))
                        {
                            await StorePatientDetailMessage(orgCode: baseHttpRequestContext.OrgCode, patientId: id, fullName: fullName);
                        }
                        if (isPatientUpdate)
                        {
                            await StorePatientDetailMessageForPropertyType((int)EODRequestDataType.Patient_Update, orgCode: baseHttpRequestContext.OrgCode, patientId: id);
                        }
                    }
                    apiResponse.Result = "Successfully updated";
                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    return apiResponse;
                }
                else
                {
                    apiResponse.Errors.Add("Patient not found.");

                }
            }
            apiResponse.Errors.Add("patient cannot be updated.");
            apiResponse.Message = "Failure";
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            return apiResponse;
        }

        private IDictionary GetFilledProperties(PatientUpdateView patientUpdateView)
        {
            Dictionary<string, object> propertyDictionary = new();
            foreach (PropertyInfo property in patientUpdateView.GetType().GetProperties())
            {
                var value = property.GetValue(patientUpdateView);
                if (value is not null)
                {
                    propertyDictionary.Add(property.Name, property.GetValue(patientUpdateView));
                }
            }
            return propertyDictionary;
        }

        /// Get Organisation Logo URL by OrgId
        /// </summary>
        /// <param name="orgnisationId"></param>
        /// <returns>OrganisationLogo</returns>
        public async Task<string> GetLogoforCompany(long? fileId, BaseHttpRequestContext baseHttpRequestContext)
        {
            string fileSasToken = "";
            var token = baseHttpRequestContext.BearerToken;
            string interServiceToken = baseHttpRequestContext.InterServiceToken;
            string fileAPiUrl = _appSettings.ApiUrls["FileServiceUrl"] + "/file/file_details/" + fileId;
            RestClient restClient = new RestClient(fileAPiUrl, null, token, interServiceToken);
            var fileApiResponse = await restClient.GetAsync<ApiResponse<FileDetailsOutputForId>>(fileAPiUrl, null);
            if (fileApiResponse.Result != null && fileApiResponse.Result.SasTokens != null)
            {
                fileSasToken = fileApiResponse.Result.SasTokens.FileToken.ToString();
            }
            return fileSasToken;
        }

        public async Task<ApiResponse<PatientVerificationResponse>> CheckDuplicate(PatientVerificationRequest patientdetails)
        {
            ApiResponse<PatientVerificationResponse> apiResponse = new();
            PatientVerificationResponse response = new();

            if (patientdetails is not null)
            {
                var dbPatient = await _patientDAL.CheckExactDuplicate(patientdetails);

                if (patientdetails.PatientId > 0)
                {
                    dbPatient = dbPatient.FindAll(x => x.Id != patientdetails.PatientId);
                }

                if (dbPatient != null && dbPatient.Count > 0)
                {
                    response.StatusId = 2; // exact duplicate found
                    response.NumberOfRecords = dbPatient.Count();
                    response.MatchingRecords = dbPatient.Select(x => new PatientVerificationBody
                    {
                        PatientId = x.Id,
                        FirstName = x.FirstName,
                        SurName = x.SurName,
                        DateofBirth = x.DateofBirth,
                        Mobile = x.Mobile,
                        HomeContact = x.HomeContact,
                        PersonalEmailAddress = x.PersonalEmailAddress
                    }).ToList();

                    apiResponse.Result = response;
                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    return apiResponse;
                }

                var mathingPatient = await _patientDAL.CheckDuplicate(patientdetails);

                if (patientdetails.PatientId > 0)
                {
                    mathingPatient = mathingPatient.FindAll(x => x.Id != patientdetails.PatientId);
                }

                if (mathingPatient != null && mathingPatient.Count > 0)
                {
                    response.StatusId = 1; // exact duplicate found
                    response.NumberOfRecords = mathingPatient.Count;
                    response.MatchingRecords = mathingPatient.Select(x => new PatientVerificationBody
                    {
                        PatientId = x.Id,
                        FirstName = x.FirstName,
                        SurName = x.SurName,
                        DateofBirth = x.DateofBirth,
                        Mobile = x.Mobile,
                        HomeContact = x.HomeContact,
                        PersonalEmailAddress = x.PersonalEmailAddress
                    }).ToList();

                    apiResponse.Result = response;
                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    return apiResponse;
                }

                response.StatusId = 0; // exact duplicate found
                response.NumberOfRecords = mathingPatient.Count;
                apiResponse.Result = response;
                apiResponse.StatusCode = StatusCodes.Status200OK;
                return apiResponse;
            }

            apiResponse.Errors.Add("patient cannot be null.");
            apiResponse.Message = "Failure";
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            return apiResponse;
        }

        /// <summary>
        /// Method to fetch a patient based on Id
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<PatientDetailInfo>> GetPatientDetailsById(BaseHttpRequestContext baseHttpRequestContext, long id)
        {
            int orgId = baseHttpRequestContext.OrgId;
            ApiResponse<PatientDetailInfo> apiResposne = new();
            var patient = await _patientDAL.GetPatientDetails(orgId, id);

            PatientDetailInfo patientDetailInfo = _mapper.Map<PatientDetail, PatientDetailInfo>(patient);
            apiResposne.Result = patientDetailInfo;
            apiResposne.StatusCode = StatusCodes.Status200OK;
            apiResposne.Message = "Success";
            return apiResposne;
        }

        public async Task<ApiResponse<PatientTagsModel>> GetLetterTemplateData(PatientLetterTemplateFilterModel patientLetterTemplateFilterModel, BaseHttpRequestContext baseHttpRequestContext)
        {
            int orgId = baseHttpRequestContext.OrgId;
            ApiResponse<PatientTagsModel> apiResposne = new();
            LetterTemplateView letterFromDB = await _mediaLibraryDAL.GetLetterTemplateDAL(orgId, patientLetterTemplateFilterModel.LetterTemplateId);
            if (letterFromDB is null)
            {
                apiResposne.Result = null;
                apiResposne.StatusCode = StatusCodes.Status400BadRequest;
                apiResposne.Message = "Failure";
                apiResposne.Errors.Add("The Letter Template doesnot exist.");
                return apiResposne;
            }

            apiResposne.Result = await GetPatientDataTags(letterFromDB, patientLetterTemplateFilterModel, orgId, baseHttpRequestContext);
            apiResposne.StatusCode = StatusCodes.Status200OK;
            apiResposne.Message = "Success";
            return apiResposne;
        }
        /// <summary>
        /// Method to fetch Referral and Account hold info for patients( for claims)
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="patient_id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<ClaimantPatientDetailInfo>> GetReferralAndAccountHolderInfo(BaseHttpRequestContext baseHttpRequestContext, long patient_id)
        {
            ApiResponse<ClaimantPatientDetailInfo> apiResposne = new();
            var orgId = baseHttpRequestContext.OrgId;

            ClaimantPatientDetailInfo patientFromDB = await _accountHolderDAL.GetAccountHolderInfo(orgId, patient_id);
            //to fetch default referral
            List<InputReferralDetail> listReferralDB = await _referralDAL.FetchReferrals(orgId, patient_id, 1);

            if (listReferralDB is not null && listReferralDB.Count > 0)
            {
                InputReferralDetail referralDB = listReferralDB.FirstOrDefault();
                patientFromDB.ReferralDetail = _mapper.Map<InputReferralDetail, ReferralDetailInfo>(referralDB);
            }
            return new ApiResponse<ClaimantPatientDetailInfo>
            {
                StatusCode = StatusCodes.Status200OK,
                Message = "Success",
                Result = patientFromDB
            };
        }

        /// <summary>
        /// Method to fetch Referral and Account hold info for patients( for claims)
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="patient_id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<InvoiceTemplateTagsData>> GetFinanceTemplateTagData(BaseHttpRequestContext baseHttpRequestContext, long invoiceId)
        {
            ApiResponse<InvoiceTemplateTagsData> apiResponse = new ApiResponse<InvoiceTemplateTagsData>();
            string invoiceAPIUrl = _appSettings.ApiUrls["InvoiceServiceUrl"] + "/invoice/invoice_summaries/getInvoiceSummaryTags/" + invoiceId;
            RestClient restClient = new RestClient(invoiceAPIUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            apiResponse = await restClient.GetAsync<ApiResponse<InvoiceTemplateTagsData>>(invoiceAPIUrl, null);
            return apiResponse;
        }

        /// <summary>
        /// Method to fetch Referral and Account hold info for patients( for claims)
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="patient_id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<InvoiceTagsDetails>> GetFinanceDetailedTemplateTagData(BaseHttpRequestContext baseHttpRequestContext, long invoiceId)
        {
            ApiResponse<InvoiceTagsDetails> apiResponse = new ApiResponse<InvoiceTagsDetails>();
            string invoiceAPIUrl = _appSettings.ApiUrls["InvoiceServiceUrl"] + "/invoice/invoice_summaries/getInvoiceDetailedSummaryTags/" + invoiceId;
            RestClient restClient = new RestClient(invoiceAPIUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            apiResponse = await restClient.GetAsync<ApiResponse<InvoiceTagsDetails>>(invoiceAPIUrl, null);
            return apiResponse;
        }

        // <summary>
        /// Method to fetch Referral and Account hold info for patients( for claims)
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="patient_id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<DepositInvoiceLetterTemplate>> GetPaymentDepositORInvoiceReceipt(long patient_id, long invoiceDetailsId, BaseHttpRequestContext baseHttpRequestContext)
        {

            ApiResponse<DepositInvoiceLetterTemplate> apiResposne = new ApiResponse<DepositInvoiceLetterTemplate>();
            PatientDetail patientDetail = await _patientDAL.GetPatientDetails(baseHttpRequestContext.OrgId, patient_id);
            if (patientDetail == null && patientDetail.Id == 0)
            {
                apiResposne.Result = null;
                apiResposne.StatusCode = StatusCodes.Status400BadRequest;
                apiResposne.Message = "Failure";
                apiResposne.Errors.Add("Patient doesnot exist.");
                return apiResposne;
            }

            patientDetail.Addresses = await _patientDAL.GetPatientAddresses(baseHttpRequestContext.OrgId, patient_id);
            DepositInvoiceLetterTemplate depositInvoiceLetterTemplate = new DepositInvoiceLetterTemplate();
            depositInvoiceLetterTemplate.Letter = new LetterTemplateView();
            LetterTemplateView letterFromDB = await _mediaLibraryDAL.GetLetterTemplateBySubTypeDAL(baseHttpRequestContext.OrgId, (short)TemplateSubTypesEnum.DepositTemplate);
            if (letterFromDB is null)
            {
                apiResposne.Result = null;
                apiResposne.StatusCode = StatusCodes.Status400BadRequest;
                apiResposne.Message = "Failure";
                apiResposne.Errors.Add("The Letter Template doesnot exist.");
                return apiResposne;
            }
            depositInvoiceLetterTemplate.Letter = letterFromDB;
            PatientTagsModel patientTagsModel = new PatientTagsModel();
            PatientForLetter patientData = new();
            patientData["##PD_PatientId##"] = patientDetail.Id.ToString();
            patientData["##PD_Title##"] = patientDetail.TitleId is not null ? EnumExtensions.GetDescription((TitleType)patientDetail.TitleId) : null;
            patientData["##PD_FirstName##"] = patientDetail.FirstName;
            patientData["##PD_Surname##"] = patientDetail.SurName;
            patientData["##PD_Salutation##"] = patientDetail.Salutation;
            patientData["##PD_Date_of_Birth##"] = patientDetail.DateofBirth is not null ? ((DateTime)patientDetail.DateofBirth).ToString("dd/MM/yyyy") : null;
            patientData["##PD_Occupation##"] = patientDetail.Occupation;
            patientData["##PD_Guardian_Name##"] = patientDetail.GuardianName;
            patientData["##PD_Guardian_Work_Number##"] = patientDetail.GuardianWorkContact;
            patientData["##PD_Guardian_Home_Number##"] = patientDetail.GuardianHomeContact;
            patientData["##PD_Guardian_Mobile_Number##"] = patientDetail.GuardianMobileContact;
            patientData["##PD_Patient_Work_Number##"] = patientDetail.WorkContact;
            patientData["##PD_Patient_Home_Number##"] = patientDetail.HomeContact;
            patientData["##PD_Patient_Mobile_Number##"] = patientDetail.Mobile;
            //patientData["##PD_Physical_Address##"] = GetAddressforPatient(patientDetail.Addresses.ToList(), Convert.ToInt16(AddressType.Physical));
            //patientData["##PD_Postal_Address##"] = GetAddressforPatient(patientDetail.Addresses.ToList(), Convert.ToInt16(AddressType.Postal));
            patientData["##PD_RecordID##"] = patientDetail.RecordId.ToString();





            var phyAddress = patientDetail.Addresses?.ToList().FirstOrDefault(p => p.AddressType == Convert.ToInt16(AddressType.Physical) && p.StatusId == (short)Status.Active);
            if (phyAddress is not null)
            {
                patientData["##PD_Physical_AddressLine1##"] = string.IsNullOrWhiteSpace(phyAddress.AddressLine1) ? string.Empty : phyAddress.AddressLine1;
                patientData["##PD_Physical_AddressLine2##"] = string.IsNullOrWhiteSpace(phyAddress.AddressLine2) ? string.Empty : phyAddress.AddressLine2;
                patientData["##PD_Physical_Suburb##"] = string.IsNullOrWhiteSpace(phyAddress.Suburb) ? string.Empty : phyAddress.Suburb;
                patientData["##PD_Physical_State##"] = Enum.GetName(typeof(State), phyAddress.StateId);
                patientData["##PD_Physical_Postcode##"] = phyAddress.PostCode;
            }
            else
            {
                patientData["##PD_Physical_AddressLine1##"] = null;
                patientData["##PD_Physical_AddressLine2##"] = null;
                patientData["##PD_Physical_Suburb##"] = null;
                patientData["##PD_Physical_State##"] = null;
                patientData["##PD_Physical_Postcode##"] = null;

            }

            // patientData["##PD_Postal_Address##"] = GetAddressforPatient(actualData.Addresses.ToList(), Convert.ToInt16(AddressType.Postal));
            var postAddress = patientDetail.Addresses?.ToList().FirstOrDefault(p => p.AddressType == Convert.ToInt16(AddressType.Postal) && p.StatusId == (short)Status.Active);
            if (postAddress is not null)
            {
                patientData["##PD_Postal_AddressLine1##"] = string.IsNullOrWhiteSpace(postAddress.AddressLine1) ? string.Empty : postAddress.AddressLine1;
                patientData["##PD_Postal_AddressLine2##"] = string.IsNullOrWhiteSpace(postAddress.AddressLine2) ? string.Empty : postAddress.AddressLine2;
                patientData["##PD_Postal_Suburb##"] = string.IsNullOrWhiteSpace(postAddress.Suburb) ? string.Empty : postAddress.Suburb;
                patientData["##PD_Postal_State##"] = Enum.GetName(typeof(State), postAddress.StateId);
                patientData["##PD_Postal_Postcode##"] = postAddress.PostCode;
            }
            else
            {
                patientData["##PD_Postal_AddressLine1##"] = null;
                patientData["##PD_Postal_AddressLine2##"] = null;
                patientData["##PD_Postal_Suburb##"] = null;
                patientData["##PD_Postal_State##"] = null;
                patientData["##PD_Postal_Postcode##"] = null;

            }

            var homeAddress = patientDetail.Addresses?.ToList().FirstOrDefault(p => p.AddressType == Convert.ToInt16(AddressType.Home) && p.StatusId == (short)Status.Active);
            if (homeAddress is not null)
            {
                patientData["##PD_Home_AddressLine1##"] = string.IsNullOrWhiteSpace(homeAddress.AddressLine1) ? string.Empty : homeAddress.AddressLine1;
                patientData["##PD_Home_AddressLine2##"] = string.IsNullOrWhiteSpace(homeAddress.AddressLine2) ? string.Empty : homeAddress.AddressLine2;
                patientData["##PD_Home_Suburb##"] = string.IsNullOrWhiteSpace(homeAddress.Suburb) ? string.Empty : homeAddress.Suburb;
                patientData["##PD_Home_State##"] = Enum.GetName(typeof(State), homeAddress.StateId);
                patientData["##PD_Home_Postcode##"] = homeAddress.PostCode;
            }
            else
            {
                patientData["##PD_Home_AddressLine1##"] = null;
                patientData["##PD_Home_AddressLine2##"] = null;
                patientData["##PD_Home_Suburb##"] = null;
                patientData["##PD_Home_State##"] = null;
                patientData["##PD_Home_Postcode##"] = null;
            }

            var officeAddress = patientDetail.Addresses?.ToList().FirstOrDefault(p => p.AddressType == Convert.ToInt16(AddressType.Office) && p.StatusId == (short)Status.Active);
            if (officeAddress is not null)
            {
                patientData["##PD_Office_AddressLine1##"] = string.IsNullOrWhiteSpace(officeAddress.AddressLine1) ? string.Empty : officeAddress.AddressLine1;
                patientData["##PD_Office_AddressLine2##"] = string.IsNullOrWhiteSpace(officeAddress.AddressLine2) ? string.Empty : officeAddress.AddressLine2;
                patientData["##PD_Office_Suburb##"] = string.IsNullOrWhiteSpace(officeAddress.Suburb) ? string.Empty : officeAddress.Suburb;
                patientData["##PD_Office_State##"] = Enum.GetName(typeof(State), officeAddress.StateId);
                patientData["##PD_Office_Postcode##"] = officeAddress.PostCode;
            }
            else
            {
                patientData["##PD_Office_AddressLine1##"] = null;
                patientData["##PD_Office_AddressLine2##"] = null;
                patientData["##PD_Office_Suburb##"] = null;
                patientData["##PD_Office_State##"] = null;
                patientData["##PD_Office_Postcode##"] = null;

            }

            var otherAddress = patientDetail.Addresses?.ToList().FirstOrDefault(p => p.AddressType == Convert.ToInt16(AddressType.Other) && p.StatusId == (short)Status.Active);
            if (otherAddress is not null)
            {
                patientData["##PD_Other_AddressLine1##"] = string.IsNullOrWhiteSpace(otherAddress.AddressLine1) ? string.Empty : otherAddress.AddressLine1;
                patientData["##PD_Other_AddressLine2##"] = string.IsNullOrWhiteSpace(otherAddress.AddressLine2) ? string.Empty : otherAddress.AddressLine2;
                patientData["##PD_Other_Suburb##"] = string.IsNullOrWhiteSpace(otherAddress.Suburb) ? string.Empty : otherAddress.Suburb;
                patientData["##PD_Other_State##"] = Enum.GetName(typeof(State), otherAddress.StateId);
                patientData["##PD_Other_Postcode##"] = otherAddress.PostCode;
            }
            else
            {
                patientData["##PD_Other_AddressLine1##"] = null;
                patientData["##PD_Other_AddressLine2##"] = null;
                patientData["##PD_Other_Suburb##"] = null;
                patientData["##PD_Other_State##"] = null;
                patientData["##PD_Other_Postcode##"] = null;
            }


            var accountHolderFromDB = await _accountHolderDAL.FetchAccountHolderDetails(baseHttpRequestContext.OrgId, patient_id);
            if (accountHolderFromDB is not null && accountHolderFromDB.AccountHolderAssocs is not null && accountHolderFromDB.AccountHolderAssocs.Count > 0)
            {
                var medicaredata = accountHolderFromDB.AccountHolderAssocs.ToList().FirstOrDefault(p => p.AccountHolderType == Convert.ToInt16(AccountHolderTypes.Medicare));
                if (medicaredata is not null)
                {
                    patientData["##AH_Medicare_Number##"] = medicaredata.AccountNumber;
                    patientData["##AH_IRN##"] = medicaredata.AccountSubNumber;
                    patientData["##AH_Medicare_Expiry_Date##"] = medicaredata.ExpiryDate is not null ? ((DateTime)medicaredata.ExpiryDate).ToString("dd/MM/yyyy") : null;

                }
                else
                {
                    patientData["##AH_Medicare_Number##"] = null;
                    patientData["##AH_IRN##"] = null;
                    patientData["##AH_Medicare_Expiry_Date##"] = null;


                }
                var dvadata = accountHolderFromDB.AccountHolderAssocs.ToList().FirstOrDefault(p => p.AccountHolderType == Convert.ToInt16(AccountHolderTypes.DVA));
                if (dvadata is not null)
                {
                    patientData["##AH_DVA_Number##"] = dvadata.AccountNumber;
                    patientData["##AH_DVA_Expiry_Date##"] = dvadata.ExpiryDate is not null ? ((DateTime)dvadata.ExpiryDate).ToString("dd/MM/yyyy") : null;

                }
                else
                {
                    patientData["##AH_DVA_Number##"] = null;
                    patientData["##AH_DVA_Expiry_Date##"] = null;

                }
            }
            if (accountHolderFromDB is not null && accountHolderFromDB.AccountHolderHealthfundAssocs is not null)
            {
                var healthFund = accountHolderFromDB.AccountHolderHealthfundAssocs;

                patientData["##AH_Health_Fund_Name##"] = healthFund.Name;
                patientData["##AH_MF_ID##"] = healthFund.FundId;
                patientData["##AH_Membership_Number##"] = healthFund.MembershipNumber;

            }
            else
            {
                patientData["##AH_Health_Fund_Name##"] = null;
                patientData["##AH_MF_ID##"] = null;
                patientData["##AH_Membership_Number##"] = null;
            }
            OrganisationView orgDetails = await FetchMasterCompanyDetails(baseHttpRequestContext);
            DateTime? currDateTime = GetDateFromOrganisation(orgDetails);
            bool isWindows = System.Runtime.InteropServices.RuntimeInformation
                                                  .IsOSPlatform(OSPlatform.Windows);

            short TimeZoneMasterId = (orgDetails is not null) ? orgDetails.TimeZoneMasterId : default(short);
            string timeZone;
            if (TimeZoneMasterId > default(short))
            {
                timeZone = (isWindows) ? EnumExtensions.GetDescription((WindowsTimeZone)(TimeZoneMasterId)) : EnumExtensions.GetDescription((LinuxTimeZone)(TimeZoneMasterId));
            }
            else { timeZone = null; }
            string currDateTimeStr = (currDateTime is not null) ? ((DateTime)currDateTime).ToString("dd/MM/yyy") : null;




            patientTagsModel.Data = new List<KeyValuePair<string, string>>();
            patientTagsModel.Data.AddRange(patientData);
            patientTagsModel.Data.Add(new KeyValuePair<string, string>("##D_Today##", currDateTimeStr));

            var depositTagsData = await GetInvoiceDepositTagsDetails(invoiceDetailsId, baseHttpRequestContext);

            if (depositTagsData.StatusCode == StatusCodes.Status200OK)
            {
                if (depositTagsData.Result.CompanyDetailsId.HasValue && depositTagsData.Result.CompanyDetailsId.Value > 0)
                {
                    var icFromDB = await _patientDAL.GetCompanyData((Int32)depositTagsData.Result.CompanyDetailsId.Value, baseHttpRequestContext.OrgId);

                    ICLetter iCompanyLetter = new();
                    iCompanyLetter["##IC_Internal_Company_Name##"] = icFromDB.Name;
                    iCompanyLetter["##IC_Internal_Company_Email##"] = icFromDB.Email;
                    iCompanyLetter["##IC_ACN##"] = icFromDB.Acn;
                    iCompanyLetter["##IC_ABN##"] = icFromDB.Abn;
                    //  iCompanyLetter["##IC_Office_Address##"] = GetAddressforComapny(icFromDB.CompanyAddresses.ToList(), Convert.ToInt16(AddressType.Physical));
                    // iCompanyLetter["##IC_Other_Address##"] = GetAddressforComapny(icFromDB.CompanyAddresses.ToList(), Convert.ToInt16(AddressType.Other));
                    iCompanyLetter["##IC_HPIO_Location##"] = icFromDB.Hpiolocation;
                    iCompanyLetter["##IC_HPIO_Number##"] = icFromDB.Hpionumber.ToString();
                    iCompanyLetter["##IC_Bank_Details##"] = icFromDB.BankDetails;
                    iCompanyLetter["##IC_Account_Name##"] = icFromDB.AccountName;
                    iCompanyLetter["##IC_BSB##"] = icFromDB.Bsb;
                    iCompanyLetter["##IC_Account_Number##"] = icFromDB.AccountNo;
                    iCompanyLetter["##IC_Work_Number##"] = icFromDB.WorkContact;
                    iCompanyLetter["##IC_Fax_Number##"] = icFromDB.FaxNumber;
                    iCompanyLetter["##IC_Mobile_Number##"] = icFromDB.Mobile;
                    iCompanyLetter["##HowToPay_AccountName##"] = icFromDB.AccountName;
                    iCompanyLetter["##HowToPay_AccountNumber##"] = icFromDB.AccountNo;
                    iCompanyLetter["##HowToPay_BSB##"] = icFromDB.Bsb;
                    iCompanyLetter["##IC_Facility_ID##"] = icFromDB.FacilityId;

                    // Fetch Internal Company Addresses
                    var icpostAddress = icFromDB.CompanyAddresses.ToList().FirstOrDefault(p => p.AddressType == Convert.ToInt16(AddressType.Postal) && p.StatusId == (short)Status.Active);
                    if (icpostAddress is not null)
                    {
                        iCompanyLetter["##IC_Postal_AddressLine1##"] = string.IsNullOrWhiteSpace(icpostAddress.AddressLine1) ? string.Empty : icpostAddress.AddressLine1;
                        iCompanyLetter["##IC_Postal_AddressLine2##"] = string.IsNullOrWhiteSpace(icpostAddress.AddressLine2) ? string.Empty : icpostAddress.AddressLine2;
                        iCompanyLetter["##IC_Postal_Suburb##"] = string.IsNullOrWhiteSpace(icpostAddress.Suburb) ? string.Empty : icpostAddress.Suburb;
                        iCompanyLetter["##IC_Postal_State##"] = Enum.GetName(typeof(State), icpostAddress.StateId);
                        iCompanyLetter["##IC_Postal_Postcode##"] = icpostAddress.PostCode;
                    }
                    else
                    {
                        iCompanyLetter["##IC_Postal_AddressLine1##"] = null;
                        iCompanyLetter["##IC_Postal_AddressLine2##"] = null;
                        iCompanyLetter["##IC_Postal_Suburb##"] = null;
                        iCompanyLetter["##IC_Postal_State##"] = null;
                        iCompanyLetter["##IC_Postal_Postcode##"] = null;

                    }
                    var icOtherAddress = icFromDB.CompanyAddresses.ToList().FirstOrDefault(p => p.AddressType == Convert.ToInt16(AddressType.Other) && p.StatusId == (short)Status.Active);
                    if (icOtherAddress is not null)
                    {
                        iCompanyLetter["##IC_Other_AddressLine1##"] = string.IsNullOrWhiteSpace(icOtherAddress.AddressLine1) ? string.Empty : icOtherAddress.AddressLine1;
                        iCompanyLetter["##IC_Other_AddressLine2##"] = string.IsNullOrWhiteSpace(icOtherAddress.AddressLine2) ? string.Empty : icOtherAddress.AddressLine2;
                        iCompanyLetter["##IC_Other_Suburb##"] = string.IsNullOrWhiteSpace(icOtherAddress.Suburb) ? string.Empty : icOtherAddress.Suburb;
                        iCompanyLetter["##IC_Other_State##"] = Enum.GetName(typeof(State), icOtherAddress.StateId);
                        iCompanyLetter["##IC_Other_Postcode##"] = icOtherAddress.PostCode;
                    }
                    else
                    {
                        iCompanyLetter["##IC_Other_AddressLine1##"] = null;
                        iCompanyLetter["##IC_Other_AddressLine2##"] = null;
                        iCompanyLetter["##IC_Other_Suburb##"] = null;
                        iCompanyLetter["##IC_Other_State##"] = null;
                        iCompanyLetter["##IC_Other_Postcode##"] = null;
                    }

                    if (icFromDB.LogoFileDetailsId != null && icFromDB.LogoFileDetailsId > 0)
                    {
                        string logoSasToken = await GetLogoforCompany(icFromDB.LogoFileDetailsId, baseHttpRequestContext);
                        if (!string.IsNullOrEmpty(logoSasToken))
                            iCompanyLetter["##IC_Company_Image##"] = $"<img src='{logoSasToken}'/>";

                    }
                    else
                        iCompanyLetter["##IC_Company_Image##"] = null;
                    patientTagsModel.Data.AddRange(iCompanyLetter);
                }

                FinanceDetailsForLetter financeData = new();
                var totalRefund = depositTagsData.Result.TotalRefund;
                if (depositTagsData.Result.InPatientCompanyDetailsId.HasValue && depositTagsData.Result.InPatientCompanyDetailsId.Value > 0)
                {
                    var inPatientCompanyDB = await _patientDAL.GetCompanyData((Int32)depositTagsData.Result.InPatientCompanyDetailsId.Value, baseHttpRequestContext.OrgId);
                    financeData["##InvoiceFacilityName##"] = inPatientCompanyDB.Name;
                    financeData["##InvoiceFacilityId##"] = inPatientCompanyDB.FacilityId;
                }
                else
                {
                    financeData["##InvoiceFacilityName##"] = null;
                    financeData["##InvoiceFacilityId##"] = null;
                }

                if (totalRefund != null && totalRefund != 0)
                    financeData["##TotalRefund##"] = string.Format(new CultureInfo("en-AU", false), "{0:C}", totalRefund);
                else
                    financeData["##TotalRefund##"] = null;
                financeData["##BalanceOutstanding##"] = string.Format(new CultureInfo("en-AU", false), "{0:C}", depositTagsData.Result.BalanceOutstanding);
                financeData["##EstimateNumber##"] = depositTagsData.Result.EstimateNumber.ToString();
                financeData["##ReceiptedAmount##"] = string.Format(new CultureInfo("en-AU", false), "{0:C}", depositTagsData.Result.ReceiptedAmount);
                var totalAmountPaid = depositTagsData.Result.PaymentDetailsTagsDatas.Sum(x => x.AmountPaid);
                var totalEstimate = depositTagsData.Result.TotalEstimate;//depositTagsData.Result.BalanceOutstanding + totalAmountPaid;
                financeData["##TotalAmountPaid##"] = string.Format(new CultureInfo("en-AU", false), "{0:C}", totalAmountPaid);
                financeData["##TotalEstimate##"] = string.Format(new CultureInfo("en-AU", false), "{0:C}", totalEstimate);
                financeData["##AP_Appointment_Provider##"] = depositTagsData.Result.AppointmentServiceProvider;
                financeData["##AP_Appointment_Entity##"] = depositTagsData.Result.AppointmentInternalEntity;
                financeData["##AP_Appointment_Date##"] = depositTagsData.Result.AppointmentDate.HasValue ? depositTagsData.Result.AppointmentDate.Value.ToString("dd/MM/yyyy") : "";
                financeData["##AP_Appointment_Time##"] = depositTagsData.Result.AppointmentDate.HasValue ? depositTagsData.Result.AppointmentDate.Value.ToString("hh:mm") : "";
                var paymentData = depositTagsData.Result.PaymentDetailsTagsDatas;
                DataTable dt = new DataTable();
                dt.Columns.AddRange(new DataColumn[3] { new DataColumn("Date Paid", typeof(string)),
                            new DataColumn("Payment Type", typeof(string)),
                            new DataColumn("Amount Paid",typeof(string)),
                            });

                foreach (var data in paymentData)
                {
                    dt.Rows.Add(data.DatePaid.ToString("dd/MM/yyyy"),
                        data.PaymentType,
                        string.Format(new CultureInfo("en-AU", false), "{0:C}", data.AmountPaid));
                }

                StringBuilder sb = new StringBuilder();
                sb.Append("<table cellpadding='5' cellspacing='0' style='width:60%;font-size: 9pt;font-family:Calibri'>");
                sb.Append("<tr>");
                foreach (DataColumn column in dt.Columns)
                {
                    if (column.ColumnName == "Amount Paid")
                        sb.Append("<th style='background-color: #E5E6E6;color:#35404A;text-align: right;margin-top:5px;margin-bottom:5px;'>" + column.ColumnName + "</th>");
                    else
                        sb.Append("<th style='background-color: #E5E6E6;color:#35404A;text-align: left;margin-top:5px;margin-bottom:5px;'>" + column.ColumnName + "</th>");

                }
                sb.Append("</tr>");

                //Adding DataRow.
                int i = 0;
                string bkgcolor = string.Empty;
                foreach (DataRow row in dt.Rows)
                {
                    if (i % 2 == 0)
                    {
                        bkgcolor = "#FFFFFF";
                    }
                    else
                        bkgcolor = "#F5F6F8";
                    sb.Append("<tr>");
                    foreach (DataColumn column in dt.Columns)
                    {
                        if (column.ColumnName == "Amount Paid")
                            sb.Append($"<td style='text-align: right;color:35404A;background-color:{bkgcolor};'>" + row[column.ColumnName].ToString() + "</td>");
                        else
                            sb.Append($"<td style='text-align: left;color:35404A;background-color:{bkgcolor};'>" + row[column.ColumnName].ToString() + "</td>");

                    }
                    sb.Append("</tr>");
                    i++;
                }

                //Table end.
                sb.Append("</table>");
                string tableHtmlText = sb.ToString();
                var sfdtResult = await GetSFDTTextData(tableHtmlText, baseHttpRequestContext);
                if (sfdtResult.StatusCode == StatusCodes.Status200OK)
                {
                    financeData["##DepositReceiptTable##"] = sfdtResult.Result.ToString();
                }

                if (financeData.Any())
                {
                    patientTagsModel.Data.AddRange(financeData);
                }

                depositInvoiceLetterTemplate.PatientTagsModel = patientTagsModel;
                apiResposne.Result = depositInvoiceLetterTemplate;
                apiResposne.StatusCode = StatusCodes.Status200OK;
                apiResposne.Message = "Success";
                return apiResposne;
            }

            apiResposne.Result = depositInvoiceLetterTemplate;
            apiResposne.StatusCode = StatusCodes.Status400BadRequest;
            apiResposne.Message = "Failure";
            apiResposne.Errors.Add("Patient invoice doesnot exist.");
            return apiResposne;

        }

        /// <summary>
        /// GetLodgmentAndStatementReceipt 
        /// </summary>
        /// <param name="patient_id"></param>
        /// <param name="invoiceDetailsId"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="transactionId"></param>
        /// <returns></returns>
        public async Task<ApiResponse<DepositInvoiceLetterTemplate>> GetLodgmentAndStatementReceipt(long patient_id, long invoiceDetailsId, string transactionId, BaseHttpRequestContext baseHttpRequestContext)
        {
            bool isWindows = System.Runtime.InteropServices.RuntimeInformation.IsOSPlatform(OSPlatform.Windows);

            ApiResponse<DepositInvoiceLetterTemplate> apiResposne = new ApiResponse<DepositInvoiceLetterTemplate>();
            DepositInvoiceLetterTemplate depositInvoiceLetterTemplate = new DepositInvoiceLetterTemplate();
            depositInvoiceLetterTemplate.Letter = new LetterTemplateView();

            PatientTagsModel patientTagsModel = new PatientTagsModel();
            var invoiceResponse = await GetInvoicDetails(invoiceDetailsId, baseHttpRequestContext);
            if (invoiceResponse == null || invoiceResponse.Result == null)
            {
                apiResposne.Result = null;
                apiResposne.StatusCode = StatusCodes.Status400BadRequest;
                apiResposne.Message = "Failure";
                apiResposne.Errors.Add("GetLodgmentAndStatementTagData --> GetInvoicDetails fetch failed");
                return apiResposne;
            }

            var invoiceDetails = invoiceResponse.Result;

            //Get InvoiceDeposit Tags Data
            var response = await GetLodgmentAndStatementTagData(invoiceDetailsId, transactionId, baseHttpRequestContext);

            if (response == null || response.StatusCode != StatusCodes.Status200OK)
            {
                apiResposne.Result = null;
                apiResposne.StatusCode = StatusCodes.Status400BadRequest;
                apiResposne.Message = "Failure";
                apiResposne.Errors.Add("GetLodgmentAndStatementTagData fetch failed");
                return apiResposne;
            }

            //Get MBS ItemsData
            List<long> mbsEstimateNumbers = new List<long>();
            mbsEstimateNumbers.AddRange(response.Result.paymentDetails.Select(a => a.ItemNumber));
            var mbsItemsDetails = await FetchMBSItemDetails(mbsEstimateNumbers, baseHttpRequestContext);
            string locationID = "";
            string serviceLocation = "";
            string hospitalName = "";
            CompanyDetail companyDetails = new CompanyDetail();
            if (invoiceDetails.CompanyDetailsId.HasValue)
            {
                companyDetails = await _patientDAL.GetCompanyData(invoiceDetails.CompanyDetailsId.Value, baseHttpRequestContext.OrgId);

                if (companyDetails != null)
                {
                    hospitalName = companyDetails.Name;
                    locationID = companyDetails.MinorId;
                    serviceLocation = companyDetails.Name
                        + " " + GetAddressforComapny(companyDetails.CompanyAddresses.ToList(), Convert.ToInt16(AddressType.Physical))
                        + " Contact Number: " + companyDetails.WorkContact;
                }
            }
            long templateSubType = 0;
            if (response.Result.TransactionStatus == "MEDICARE_PENDED")
                templateSubType = (short)TemplateSubTypesEnum.LodgementTemplate;
            else if (response.Result.TransactionStatus == "MEDICARE_ASSESSED")
                templateSubType = (short)TemplateSubTypesEnum.StatementOfClaimTemplate;

            if (templateSubType != 0)
            {
                LetterTemplateView letterFromDB = await _mediaLibraryDAL.GetLetterTemplateBySubTypeDAL(baseHttpRequestContext.OrgId, templateSubType);
                if (letterFromDB is null)
                {
                    apiResposne.Result = null;
                    apiResposne.StatusCode = StatusCodes.Status400BadRequest;
                    apiResposne.Message = "Failure";
                    apiResposne.Errors.Add("The Letter Template doesnot exist.");
                    return apiResposne;
                }
                depositInvoiceLetterTemplate.Letter = letterFromDB;
                var templateData = response.Result;
                FinanceDetailsForLetter financeData = new();


                PatientForLetter patientData = new();
                patientData["##PD_FirstName##"] = (templateData.PatientDetails is not null && !string.IsNullOrWhiteSpace(templateData.PatientDetails.FirstName)) ? templateData.PatientDetails.FirstName : string.Empty;
                patientData["##PD_Surname##"] = (templateData.PatientDetails is not null && !string.IsNullOrWhiteSpace(templateData.PatientDetails.Surname)) ? templateData.PatientDetails.Surname : string.Empty;
                patientData["##PD_Date_of_Birth##"] = (templateData.PatientDetails is not null && !string.IsNullOrWhiteSpace(templateData.PatientDetails.DateOfBirth)) ? templateData.PatientDetails.DateOfBirth : string.Empty;
                patientTagsModel.Data = new List<KeyValuePair<string, string>>();
                patientTagsModel.Data.AddRange(patientData);
                if (templateData.ClaimantDetails.MedicareCardNo == templateData.PatientDetails.MedicareCardNo && templateData.ClaimantDetails.IRN == templateData.PatientDetails.IRN)
                {
                    financeData["##CD_FirstName##"] = "";
                    financeData["##CD_Surname##"] = "";
                    financeData["##CD_DateofBirth##"] = "";
                    financeData["##CD_MedicareCardNumber##"] = "";
                    financeData["##CD_IRN##"] = "";
                    financeData["##ACRF##"] = templateData.ACRF;
                    financeData["##CD_Address##"] = (response is null || response.Result is null || response.Result.ClaimantDetails is null || response.Result.ClaimantDetails.Address is null) ? string.Empty : response.Result.ClaimantDetails.Address;
                    financeData["##CD_TelephoneNo##"] = (response is null || response.Result is null || response.Result.ClaimantDetails is null || response.Result.ClaimantDetails.TelephoneNumber is null) ? string.Empty : response.Result.ClaimantDetails.TelephoneNumber;
                }
                else
                {
                    financeData["##CD_FirstName##"] = templateData.ClaimantDetails.FirstName;
                    financeData["##CD_Surname##"] = templateData.ClaimantDetails.Surname;
                    financeData["##CD_DateofBirth##"] = templateData.ClaimantDetails.DateOfBirth;
                    financeData["##CD_MedicareCardNumber##"] = templateData.ClaimantDetails.MedicareCardNo;
                    financeData["##CD_IRN##"] = templateData.ClaimantDetails.IRN;
                    financeData["##ACRF##"] = templateData.ACRF;
                    financeData["##CD_Address##"] = (response is null || response.Result is null || response.Result.ClaimantDetails is null || response.Result.ClaimantDetails.Address is null) ? string.Empty : response.Result.ClaimantDetails.Address;
                    financeData["##CD_TelephoneNo##"] = (response is null || response.Result is null || response.Result.ClaimantDetails is null || response.Result.ClaimantDetails.TelephoneNumber is null) ? string.Empty : response.Result.ClaimantDetails.TelephoneNumber; ;
                }
                if (!string.IsNullOrWhiteSpace(templateData.AccountNumber))
                {
                    StringBuilder sbEft = new StringBuilder();
                    sbEft.Append("<p style='font-size: 8pt;font-family:Arial;'><b>EFT Payment</b><br/>");
                    sbEft.Append("I authorise the payment of my benefits to be paid directly into the following bank or financial institution account:</p>");

                    DataTable eftTable = new DataTable();
                    eftTable.Columns.AddRange(new DataColumn[2] { new DataColumn("Name", typeof(string)),
                            new DataColumn("Value", typeof(string))
                            });
                    eftTable.Rows.Add(new object[] { "BSB Number:", templateData.BSBNumber });
                    eftTable.Rows.Add(new object[] { "Account Number:", templateData.AccountNumber });
                    eftTable.Rows.Add(new object[] { "Account Name:", templateData.AccountName });

                    sbEft.Append("<table style='font-size: 8pt;font-family:Arial;border-spacing: 0 1em;'>");

                    //Adding DataRow.
                    foreach (DataRow row in eftTable.Rows)
                    {
                        sbEft.Append("<tr>");
                        foreach (DataColumn column in eftTable.Columns)
                        {
                            sbEft.Append("<td style='text-align: left;width:20%;'>" + row[column.ColumnName].ToString() + "</td>");
                        }
                        sbEft.Append("</tr>");
                    }

                    //Table end.
                    sbEft.Append("</table>");

                    var sfdtResultEft = await GetSFDTTextData(sbEft.ToString(), baseHttpRequestContext);

                    if (sfdtResultEft.StatusCode == StatusCodes.Status200OK)
                    {
                        financeData["##PCI_Temp_EFT##"] = sfdtResultEft.Result.ToString();
                    }
                }
                else
                {
                    financeData["##PCI_Temp_EFT##"] = string.Empty;
                }

                if (invoiceDetails.ProviderDetails != null)
                {
                    financeData["##ServiceProviderName##"] = invoiceDetails.ProviderDetails.FirstName + " " + invoiceDetails.ProviderDetails.SurName;
                    financeData["##ServiceProviderNumber##"] = templateData.ServiceProviderNumber;
                }
                // TO DO: Paye provider is not applicable
                if (!string.IsNullOrWhiteSpace(templateData.PayeeProviderNumber))
                {
                    financeData["##PayeeProviderNo##"] = templateData.PayeeProviderNumber;
                    financeData["##PayeeProvider##"] = (invoiceDetails.PayeeProviderDetails != null) ? invoiceDetails.PayeeProviderDetails?.FirstName + " " + invoiceDetails.PayeeProviderDetails?.SurName : string.Empty;
                }
                else
                {
                    financeData["##PayeeProviderNo##"] = "";// templateData.PayeeProviderNumber;
                    financeData["##PayeeProvider##"] = ""; // invoiceDetails.ProviderDetails.FirstName + " " + invoiceDetails.ProviderDetails.SurName;

                }

                //Get Referral Period

                financeData["##ReferringProviderName##"] = "";
                financeData["##ReferringProviderNo##"] = "";
                financeData["##ReferralDate##"] = "";
                financeData["##ReferralPeriod##"] = "";
                string referralPeriod = templateData.PeriodOfReferral != null ? templateData.PeriodOfReferral : "";
                if (templateData.ReferralOverrideCode != null && templateData.ReferralOverrideCode != "")
                {
                    if (templateData.ReferralOverrideCode.ToLower() == "e")
                    {
                        financeData["##ReferralPeriod##"] = "Emergency Referral";
                    }
                    else if (templateData.ReferralOverrideCode.ToLower() == "l")
                    {
                        financeData["##ReferralPeriod##"] = "Lost Referral";
                    }
                    else if (templateData.ReferralOverrideCode.ToLower() == "h")
                    {
                        financeData["##ReferralPeriod##"] = "Referral Within " + hospitalName;
                    }
                }
                else
                {
                    if (referralPeriod.ToLower() == "s")
                    {
                        if (invoiceDetails.CompanyDetailsId.HasValue)
                        {
                            var providerUserDetails = await _patientDAL.GetProviderDetailsByProviderID(templateData.RequestingReferringProviderNumber, invoiceDetails.CompanyDetailsId.Value, baseHttpRequestContext.OrgId);
                            if (providerUserDetails != null)
                            {
                                if (providerUserDetails.ProviderTypeId == (short)ProviderType.General_Practitioner)
                                {
                                    financeData["##ReferralPeriod##"] = "12 Months";
                                }
                                if (providerUserDetails.ProviderTypeId == (short)ProviderType.Specialist_and_consultant_physician)
                                {
                                    financeData["##ReferralPeriod##"] = "3 Month";
                                }
                            }
                        }
                    }
                    else
                    {
                        financeData["##ReferralPeriod##"] = templateData.PeriodOfReferral;
                    }

                    if (invoiceDetails.AppointmentDetails != null && invoiceDetails.AppointmentDetails.ReferralDetail != null && templateData.RequestingReferringProviderNumber != "")
                    {
                        financeData["##ReferringProviderName##"] = invoiceDetails.AppointmentDetails.ReferralDetail.ReferringProviderInfo.FirstName + " " + invoiceDetails.AppointmentDetails.ReferralDetail.ReferringProviderInfo.SurName;
                        financeData["##ReferringProviderNo##"] = templateData.RequestingReferringProviderNumber;
                        financeData["##ReferralDate##"] = templateData.DateOfRequestReferral == default(DateTime) ? "" : templateData.DateOfRequestReferral.ToString("dd/MM/yyyy");
                    }
                }

                financeData["##PD_IRN##"] = templateData.PatientDetails.IRN;
                financeData["##FacilityID##"] = templateData.FacilityID;
                financeData["##AccountName##"] = templateData.AccountName;
                financeData["##AccountNumber##"] = templateData.AccountNumber;
                financeData["##BSBNumber##"] = templateData.BSBNumber;
                financeData["##IsAccountFullyPaid##"] = (templateData.IsAccountFullyPaid == "Y") ? "YES" : "NO";
                financeData["##PD_MedicareCardNumber##"] = templateData.PatientDetails.MedicareCardNo;
                financeData["##LocationId##"] = locationID;
                financeData["##ServiceLocation##"] = locationID + " " + serviceLocation;
                financeData["##ChargeTotal##"] = templateData.paymentDetails != null ? Decimal.ToInt32(templateData.paymentDetails.ToList().Sum(s => s.Charges)).ToString() : "";
                if (string.IsNullOrEmpty(templateData.ClaimReference) || string.IsNullOrWhiteSpace(templateData.ClaimReference))
                {
                    financeData["##ClaimReference##"] = "";
                }
                else
                {

                    string timeZone;
                    List<CompanyDetailInfo> lstCompany = await GetCompanyByIds(new List<int> { (int)invoiceDetails.CompanyDetailsId }, baseHttpRequestContext);
                    short? timeZoneId = (lstCompany is not null && lstCompany.Count > 0) ? lstCompany.FirstOrDefault().TimeZoneId : null;
                    if (timeZoneId is not null)
                    {
                        timeZone = (isWindows) ? EnumExtensions.GetDescription((WindowsTimeZone)(timeZoneId)) : EnumExtensions.GetDescription((LinuxTimeZone)(timeZoneId));
                    }
                    else
                    {
                        OrganisationView orgDetails = await FetchMasterCompanyDetails(baseHttpRequestContext);
                        timeZone = (isWindows) ? orgDetails.WindowsTimeZoneData : orgDetails.LinuxTimeZoneData;
                    }

                    DateTime eventDate = (DateTime)DateTimeConversion.ConvertTimeFromUtc(DateTime.ParseExact(templateData.ClaimReference, "dd/MM/yyyy h:mm tt", System.Globalization.CultureInfo.InvariantCulture), timeZone);
                    financeData["##ClaimReference##"] = "[" + locationID + "][" + eventDate.ToString("dd/MM/yyyy h:mm tt") + "]";

                }

                DataTable dt = new DataTable();
                dt.Columns.AddRange(new DataColumn[5] { new DataColumn("Date of Service", typeof(string)),
                            new DataColumn("Item No", typeof(string)),
                            new DataColumn("Description Of Service",typeof(string)),
                            new DataColumn("Charge",typeof(string)),
                            new DataColumn("Patient Contribution",typeof(string))
                            });
                List<MedicareErrors> medicareErrors = new();

                if (templateData.TransactionStatus == "MEDICARE_ASSESSED")
                {
                    if (templateData.paymentDetails is not null && templateData.paymentDetails.Count > 0)
                    {
                        List<string> lstRsnCodes = templateData.paymentDetails.Where(x => x.RSNCode != null && x.RSNCode != "").Select(x => x.RSNCode).ToList();
                        if (lstRsnCodes != null && lstRsnCodes.Count > 0)
                        {
                            medicareErrors = await FetchMedicareReasonCodes(lstRsnCodes, baseHttpRequestContext);
                        }
                    }
                    dt.Columns.AddRange(new DataColumn[2]
                                {
                            new DataColumn("RSN Code", typeof(string)),
                            new DataColumn("Benefit", typeof(string))
                                });
                }

                foreach (var data in templateData.paymentDetails)
                {
                    string description = (mbsItemsDetails != null && mbsItemsDetails.Any(c => c.ItemNum == data.ItemNumber)) ? mbsItemsDetails.Where(c => c.ItemNum == data.ItemNumber).FirstOrDefault().Description : string.Empty;
                    if (description != null && description.Length > 30)
                    {
                        description = description.Substring(0, 30) + "...";
                    }
                    else
                        description = string.Empty;
                    string patientContrib = string.Empty;
                    if ((templateData.IsAccountFullyPaid == "Y" || templateData.IsAccountFullyPaid == "YES") && data.Charges > 0)
                    {
                        patientContrib = string.Format(new CultureInfo("en-AU", false), "{0:C}", data.Charges / 100);
                    }
                    else if ((templateData.IsAccountFullyPaid == "N" || templateData.IsAccountFullyPaid == "NO") && data.PatientContribution > 0)
                    {
                        patientContrib = string.Format(new CultureInfo("en-AU", false), "{0:C}", data.PatientContribution / 100);
                    }
                    else
                    {
                        patientContrib = string.Format(new CultureInfo("en-AU", false), "{0:C}", 0);
                    }
                    if (!string.IsNullOrWhiteSpace(data.Description))
                    {
                        description = data.Description + " - " + description;
                    }
                    if (!string.IsNullOrWhiteSpace(data.FieldQuantity))
                    {
                        description = $"FQty-{data.FieldQuantity} {description}";
                    }
                    if (templateData.TransactionStatus == "MEDICARE_ASSESSED")
                    {
                        if (!string.IsNullOrWhiteSpace(data.RSNCode) && medicareErrors != null && medicareErrors.Count > 0)
                        {
                            string RsnDesc = medicareErrors.Where(x => x.ErrorCode == data.RSNCode).Select(x => x.ErrorDescription).FirstOrDefault();
                            if (!string.IsNullOrWhiteSpace(RsnDesc))
                            {
                                data.RSNCode = data.RSNCode + ":" + RsnDesc;
                            }
                        }
                        dt.Rows.Add(data.DateOfService.ToString("dd/MM/yyyy HH:mm"),
                        (data.HospitalInd != null && data.HospitalInd.ToLower() == "y") ? data.ItemNumber + "*" : data.ItemNumber,
                        description,
                         string.Format(new CultureInfo("en-AU", false), "{0:C}", data.Charges > 0 ? data.Charges / 100 : 0),
                         patientContrib,
                        //(data.PatientContribution <=default(decimal))? string.Format(new CultureInfo("en-AU", false), "{0:C}", data.Charges > 0 ? data.Charges / 100 : 0) : string.Format(new CultureInfo("en-AU", false), "{0:C}", data.PatientContribution > 0 ? data.PatientContribution / 100 : 0),
                        data.RSNCode,
                        string.IsNullOrWhiteSpace(data.Benefit) ? string.Empty : string.Format(new CultureInfo("en-AU", false), "{0:C}", decimal.Parse(data.Benefit) > 0 ? decimal.Parse(data.Benefit) / 100 : 0)

                        );
                    }
                    if (templateData.TransactionStatus == "MEDICARE_PENDED")
                    {

                        dt.Rows.Add(data.DateOfService.ToString("dd/MM/yyyy HH:mm"),
                        (data.HospitalInd != null && data.HospitalInd.ToLower() == "y") ? data.ItemNumber + "*" : data.ItemNumber,
                        description,
                        //(mbsItemsDetails != null && mbsItemsDetails.Any(c => c.ItemNum == data.ItemNumber)) ? data.Description + " - " + ((string.IsNullOrWhiteSpace(mbsItemsDetails.Where(c => c.ItemNum == data.ItemNumber).FirstOrDefault().Description)) ? string.Empty : (mbsItemsDetails.Where(c => c.ItemNum == data.ItemNumber).FirstOrDefault().Description.Length > 40) ? mbsItemsDetails.Where(c => c.ItemNum == data.ItemNumber).FirstOrDefault().Description.Substring(0, 40) : mbsItemsDetails.Where(c => c.ItemNum == data.ItemNumber).FirstOrDefault().Description) : data.Description,
                         string.Format(new CultureInfo("en-AU", false), "{0:C}", data.Charges > 0 ? data.Charges / 100 : 0),
                         patientContrib);
                        //(data.PatientContribution <= default(decimal)) ? (data.Charges>default(decimal) ? string.Format(new CultureInfo("en-AU", false), "{0:C}", data.Charges > 0 ? data.Charges / 100 : 0) :string.Empty) : string.Format(new CultureInfo("en-AU", false), "{0:C}", data.PatientContribution > 0 ? data.PatientContribution / 100 : 0));
                    }
                }

                StringBuilder sb = new StringBuilder();
                sb.Append("<table cellpadding='5' cellspacing='0' style='font-size: 9pt;font-family:Arial'>");
                sb.Append("<tr>");

                foreach (DataColumn column in dt.Columns)
                {
                    sb.Append("<th style='text-align: left;width:100%'>" + column.ColumnName + "</th>");
                }
                sb.Append("</tr>");

                //Adding DataRow.
                foreach (DataRow row in dt.Rows)
                {
                    sb.Append("<tr>");
                    foreach (DataColumn column in dt.Columns)
                    {
                        sb.Append("<td style='text-align: left;width:100%;'>" + row[column.ColumnName].ToString() + "</td>");
                    }
                    sb.Append("</tr>");
                }

                //Table end.
                sb.Append("</table>");

                var sfdtResult = await GetSFDTTextData(sb.ToString(), baseHttpRequestContext);
                if (sfdtResult.StatusCode == StatusCodes.Status200OK)
                {
                    if (templateData.TransactionStatus == "MEDICARE_PENDED")
                    {
                        financeData["##LodgementAdviceTable##"] = sfdtResult.Result.ToString();
                    }
                    else
                    {
                        financeData["##StatementOfAdviceTable##"] = sfdtResult.Result.ToString();
                    }
                }

                if (financeData.Any())
                {
                    patientTagsModel.Data.AddRange(financeData);
                }

                depositInvoiceLetterTemplate.PatientTagsModel = patientTagsModel;
                apiResposne.Result = depositInvoiceLetterTemplate;
                apiResposne.StatusCode = StatusCodes.Status200OK;
                apiResposne.Message = "Success";
                return apiResposne;
            }

            apiResposne.Result = null;
            apiResposne.StatusCode = StatusCodes.Status400BadRequest;
            apiResposne.Message = "Transaction Status not valid";
            apiResposne.Errors.Add("Transaction Status not valid");
            return apiResposne;
        }
        private async Task<List<MedicareErrors>> FetchMedicareReasonCodes(List<string> lstRsnCodes, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<List<MedicareErrors>> apiResponse = new();
            string filter = "{ErrorCodes:[" + string.Join(",", lstRsnCodes) + "]}";
            string encodedFilter = Base64EncoderHelper.EncodeQueryModelFilterParam(filter);
            string masterApiUrl = _appSettings.ApiUrls["MasterServiceUrl"] + "/master/medicare_errors/" + (short)ErrorType.Medicare_Errors + "?pn=1&ps=100&soo=Asc&f=" + encodedFilter;
            RestClient restClient = new RestClient(masterApiUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            apiResponse = await restClient.GetAsync<ApiResponse<List<MedicareErrors>>>(masterApiUrl);
            if (apiResponse != null && apiResponse.StatusCode == StatusCodes.Status200OK && apiResponse.Result is not null && apiResponse.Result.Count > 0)
            {
                return apiResponse.Result;
            }
            return null;
        }
        public async Task<ApiResponse<DepositInvoiceLetterTemplate>> GetInvoiceCustomBilling(long patient_id, long invoiceDetailsId, BaseHttpRequestContext baseHttpRequestContext)
        {

            ApiResponse<DepositInvoiceLetterTemplate> apiResposne = new ApiResponse<DepositInvoiceLetterTemplate>();
            PatientDetail patientDetail = await _patientDAL.GetPatientDetails(baseHttpRequestContext.OrgId, patient_id);
            if (patientDetail == null && patientDetail.Id == 0)
            {
                apiResposne.Result = null;
                apiResposne.StatusCode = StatusCodes.Status400BadRequest;
                apiResposne.Message = "Failure";
                apiResposne.Errors.Add("Patient doesnot exist.");
                return apiResposne;
            }

            patientDetail.Addresses = await _patientDAL.GetPatientAddresses(baseHttpRequestContext.OrgId, patient_id);



            DepositInvoiceLetterTemplate depositInvoiceLetterTemplate = new DepositInvoiceLetterTemplate();

            PatientTagsModel patientTagsModel = new PatientTagsModel();
            OrganisationView orgDetails = await FetchMasterCompanyDetails(baseHttpRequestContext);
            DateTime? currDateTime = GetDateFromOrganisation(orgDetails);
            bool isWindows = System.Runtime.InteropServices.RuntimeInformation
                                                  .IsOSPlatform(OSPlatform.Windows);
            short TimeZoneMasterId = (orgDetails is not null) ? orgDetails.TimeZoneMasterId : default(short);
            string timeZone;
            if (TimeZoneMasterId > default(short))
            {
                timeZone = (isWindows) ? EnumExtensions.GetDescription((WindowsTimeZone)(TimeZoneMasterId)) : EnumExtensions.GetDescription((LinuxTimeZone)(TimeZoneMasterId));
            }
            else { timeZone = null; }
            string currDateTimeStr = (currDateTime is not null) ? ((DateTime)currDateTime).ToString("dd/MM/yyy") : null;
            PatientForLetter patientData = new();
            patientData["##PD_PatientId##"] = patientDetail.Id.ToString();
            patientData["##PD_Title##"] = patientDetail.TitleId is not null ? EnumExtensions.GetDescription((TitleType)patientDetail.TitleId) : null;
            patientData["##PD_FirstName##"] = patientDetail.FirstName;
            patientData["##PD_Surname##"] = patientDetail.SurName;
            patientData["##PD_Salutation##"] = patientDetail.Salutation;
            patientData["##PD_Date_of_Birth##"] = patientDetail.DateofBirth is not null ? ((DateTime)patientDetail.DateofBirth).ToString("dd/MM/yyyy") : null;
            patientData["##PD_Occupation##"] = patientDetail.Occupation;
            patientData["##PD_Guardian_Name##"] = patientDetail.GuardianName;
            patientData["##PD_Guardian_Work_Number##"] = patientDetail.GuardianWorkContact;
            patientData["##PD_Guardian_Home_Number##"] = patientDetail.GuardianHomeContact;
            patientData["##PD_Guardian_Mobile_Number##"] = patientDetail.GuardianMobileContact;
            patientData["##PD_Patient_Work_Number##"] = patientDetail.WorkContact;
            patientData["##PD_Patient_Home_Number##"] = patientDetail.HomeContact;
            patientData["##PD_Patient_Mobile_Number##"] = patientDetail.Mobile;
            //patientData["##PD_Physical_Address##"] = GetAddressforPatient(patientDetail.Addresses.ToList(), Convert.ToInt16(AddressType.Physical));
            //patientData["##PD_Postal_Address##"] = GetAddressforPatient(patientDetail.Addresses.ToList(), Convert.ToInt16(AddressType.Postal));
            patientData["##PD_RecordID##"] = patientDetail.RecordId.ToString();
            var phyAddress = patientDetail.Addresses?.ToList().FirstOrDefault(p => p.AddressType == Convert.ToInt16(AddressType.Physical) && p.StatusId == (short)Status.Active);
            if (phyAddress is not null)
            {
                patientData["##PD_Physical_AddressLine1##"] = string.IsNullOrWhiteSpace(phyAddress.AddressLine1) ? string.Empty : phyAddress.AddressLine1;
                patientData["##PD_Physical_AddressLine2##"] = string.IsNullOrWhiteSpace(phyAddress.AddressLine2) ? string.Empty : phyAddress.AddressLine2;
                patientData["##PD_Physical_Suburb##"] = string.IsNullOrWhiteSpace(phyAddress.Suburb) ? string.Empty : phyAddress.Suburb;
                patientData["##PD_Physical_State##"] = Enum.GetName(typeof(State), phyAddress.StateId);
                patientData["##PD_Physical_Postcode##"] = phyAddress.PostCode;
            }
            else
            {
                patientData["##PD_Physical_AddressLine1##"] = null;
                patientData["##PD_Physical_AddressLine2##"] = null;
                patientData["##PD_Physical_Suburb##"] = null;
                patientData["##PD_Physical_State##"] = null;
                patientData["##PD_Physical_Postcode##"] = null;

            }

            // patientData["##PD_Postal_Address##"] = GetAddressforPatient(actualData.Addresses.ToList(), Convert.ToInt16(AddressType.Postal));
            var postAddress = patientDetail.Addresses?.ToList().FirstOrDefault(p => p.AddressType == Convert.ToInt16(AddressType.Postal) && p.StatusId == (short)Status.Active);
            if (postAddress is not null)
            {
                patientData["##PD_Postal_AddressLine1##"] = string.IsNullOrWhiteSpace(postAddress.AddressLine1) ? string.Empty : postAddress.AddressLine1;
                patientData["##PD_Postal_AddressLine2##"] = string.IsNullOrWhiteSpace(postAddress.AddressLine2) ? string.Empty : postAddress.AddressLine2;
                patientData["##PD_Postal_Suburb##"] = string.IsNullOrWhiteSpace(postAddress.Suburb) ? string.Empty : postAddress.Suburb;
                patientData["##PD_Postal_State##"] = Enum.GetName(typeof(State), postAddress.StateId);
                patientData["##PD_Postal_Postcode##"] = postAddress.PostCode;
            }
            else
            {
                patientData["##PD_Postal_AddressLine1##"] = null;
                patientData["##PD_Postal_AddressLine2##"] = null;
                patientData["##PD_Postal_Suburb##"] = null;
                patientData["##PD_Postal_State##"] = null;
                patientData["##PD_Postal_Postcode##"] = null;

            }

            var homeAddress = patientDetail.Addresses?.ToList().FirstOrDefault(p => p.AddressType == Convert.ToInt16(AddressType.Home) && p.StatusId == (short)Status.Active);
            if (homeAddress is not null)
            {
                patientData["##PD_Home_AddressLine1##"] = string.IsNullOrWhiteSpace(homeAddress.AddressLine1) ? string.Empty : homeAddress.AddressLine1;
                patientData["##PD_Home_AddressLine2##"] = string.IsNullOrWhiteSpace(homeAddress.AddressLine2) ? string.Empty : homeAddress.AddressLine2;
                patientData["##PD_Home_Suburb##"] = string.IsNullOrWhiteSpace(homeAddress.Suburb) ? string.Empty : homeAddress.Suburb;
                patientData["##PD_Home_State##"] = Enum.GetName(typeof(State), homeAddress.StateId);
                patientData["##PD_Home_Postcode##"] = homeAddress.PostCode;
            }
            else
            {
                patientData["##PD_Home_AddressLine1##"] = null;
                patientData["##PD_Home_AddressLine2##"] = null;
                patientData["##PD_Home_Suburb##"] = null;
                patientData["##PD_Home_State##"] = null;
                patientData["##PD_Home_Postcode##"] = null;
            }

            var officeAddress = patientDetail.Addresses?.ToList().FirstOrDefault(p => p.AddressType == Convert.ToInt16(AddressType.Office) && p.StatusId == (short)Status.Active);
            if (officeAddress is not null)
            {
                patientData["##PD_Office_AddressLine1##"] = string.IsNullOrWhiteSpace(officeAddress.AddressLine1) ? string.Empty : officeAddress.AddressLine1;
                patientData["##PD_Office_AddressLine2##"] = string.IsNullOrWhiteSpace(officeAddress.AddressLine2) ? string.Empty : officeAddress.AddressLine2;
                patientData["##PD_Office_Suburb##"] = string.IsNullOrWhiteSpace(officeAddress.Suburb) ? string.Empty : officeAddress.Suburb;
                patientData["##PD_Office_State##"] = Enum.GetName(typeof(State), officeAddress.StateId);
                patientData["##PD_Office_Postcode##"] = officeAddress.PostCode;
            }
            else
            {
                patientData["##PD_Office_AddressLine1##"] = null;
                patientData["##PD_Office_AddressLine2##"] = null;
                patientData["##PD_Office_Suburb##"] = null;
                patientData["##PD_Office_State##"] = null;
                patientData["##PD_Office_Postcode##"] = null;

            }

            var otherAddress = patientDetail.Addresses?.ToList().FirstOrDefault(p => p.AddressType == Convert.ToInt16(AddressType.Other) && p.StatusId == (short)Status.Active);
            if (otherAddress is not null)
            {
                patientData["##PD_Other_AddressLine1##"] = string.IsNullOrWhiteSpace(otherAddress.AddressLine1) ? string.Empty : otherAddress.AddressLine1;
                patientData["##PD_Other_AddressLine2##"] = string.IsNullOrWhiteSpace(otherAddress.AddressLine2) ? string.Empty : otherAddress.AddressLine2;
                patientData["##PD_Other_Suburb##"] = string.IsNullOrWhiteSpace(otherAddress.Suburb) ? string.Empty : otherAddress.Suburb;
                patientData["##PD_Other_State##"] = Enum.GetName(typeof(State), otherAddress.StateId);
                patientData["##PD_Other_Postcode##"] = otherAddress.PostCode;
            }
            else
            {
                patientData["##PD_Other_AddressLine1##"] = null;
                patientData["##PD_Other_AddressLine2##"] = null;
                patientData["##PD_Other_Suburb##"] = null;
                patientData["##PD_Other_State##"] = null;
                patientData["##PD_Other_Postcode##"] = null;
            }
            var accountHolderFromDB = await _accountHolderDAL.FetchAccountHolderDetails(baseHttpRequestContext.OrgId, patient_id);
            if (accountHolderFromDB is not null && accountHolderFromDB.AccountHolderAssocs is not null && accountHolderFromDB.AccountHolderAssocs.Count > 0)
            {
                var medicaredata = accountHolderFromDB.AccountHolderAssocs.ToList().FirstOrDefault(p => p.AccountHolderType == Convert.ToInt16(AccountHolderTypes.Medicare));
                if (medicaredata is not null)
                {
                    patientData["##AH_Medicare_Number##"] = medicaredata.AccountNumber;
                    patientData["##AH_IRN##"] = medicaredata.AccountSubNumber;
                    patientData["##AH_Medicare_Expiry_Date##"] = medicaredata.ExpiryDate is not null ? ((DateTime)medicaredata.ExpiryDate).ToString("dd/MM/yyyy") : null;

                }
                else
                {
                    patientData["##AH_Medicare_Number##"] = null;
                    patientData["##AH_IRN##"] = null;
                    patientData["##AH_Medicare_Expiry_Date##"] = null;


                }
                var dvadata = accountHolderFromDB.AccountHolderAssocs.ToList().FirstOrDefault(p => p.AccountHolderType == Convert.ToInt16(AccountHolderTypes.DVA));
                if (dvadata is not null)
                {
                    patientData["##AH_DVA_Number##"] = dvadata.AccountNumber;
                    patientData["##AH_DVA_Expiry_Date##"] = dvadata.ExpiryDate is not null ? ((DateTime)dvadata.ExpiryDate).ToString("dd/MM/yyyy") : null;

                }
                else
                {
                    patientData["##AH_DVA_Number##"] = null;
                    patientData["##AH_DVA_Expiry_Date##"] = null;

                }
            }
            else
            {
                patientData["##AH_Medicare_Number##"] = null;
                patientData["##AH_IRN##"] = null;
                patientData["##AH_Medicare_Expiry_Date##"] = null;
                patientData["##AH_DVA_Number##"] = null;
                patientData["##AH_DVA_Expiry_Date##"] = null;

            }
            if (accountHolderFromDB is not null && accountHolderFromDB.AccountHolderHealthfundAssocs is not null)
            {
                var healthFund = accountHolderFromDB.AccountHolderHealthfundAssocs;

                patientData["##AH_Health_Fund_Name##"] = healthFund.Name;
                patientData["##AH_MF_ID##"] = healthFund.FundId;
                patientData["##AH_Membership_Number##"] = healthFund.MembershipNumber;

            }
            else
            {
                patientData["##AH_Health_Fund_Name##"] = null;
                patientData["##AH_MF_ID##"] = null;
                patientData["##AH_Membership_Number##"] = null;
            }
            patientTagsModel.Data = new List<KeyValuePair<string, string>>();
            patientTagsModel.Data.AddRange(patientData);
            patientTagsModel.Data.Add(new KeyValuePair<string, string>("##D_Today##", currDateTimeStr));

            //Get InvoiceDeposit Tags Data
            var depositTagsData = await GeInvoiceCustomBillingTagData(invoiceDetailsId, baseHttpRequestContext);

            if (depositTagsData.StatusCode == StatusCodes.Status200OK)
            {
                FinanceDetailsForLetter financeData = new();
                financeData["##TemplateTitle##"] = depositTagsData.Result.TotalEstimate == 0 ? "Invoice" : "Tax Invoice Receipt";
                if (!string.IsNullOrWhiteSpace(depositTagsData.Result.HealthFundParticipantId))
                {
                    patientData["##AH_Health_Fund_Name##"] = depositTagsData.Result.HealthFundParticipantId;

                }
                if (!string.IsNullOrWhiteSpace(depositTagsData.Result.HealthFundMembershipNo))
                {
                    patientData["##AH_Membership_Number##"] = depositTagsData.Result.HealthFundMembershipNo;

                }
                if (!string.IsNullOrWhiteSpace(patientData["##AH_Health_Fund_Name##"]))
                {
                    ApiResponse<MedicalContractorInfo> apiResponseMC = await FetchHealthFundAddress(patientData["##AH_Health_Fund_Name##"], baseHttpRequestContext);
                    if (apiResponseMC is not null && apiResponseMC.Result != null && apiResponseMC.Result != null)
                    {
                        financeData["##MC_Name##"] = (string.IsNullOrWhiteSpace(apiResponseMC.Result.Name)) ? string.Empty : apiResponseMC.Result.Name;
                        if (apiResponseMC.Result.MedicalContractorAddressInfo != null)
                        {
                            MedicalContractorAddressInfo addressInfo = apiResponseMC.Result.MedicalContractorAddressInfo;
                            financeData["##MC_Postal_AddressLine1##"] = addressInfo.AddressLine1;
                            financeData["##MC_Postal_AddressLine2##"] = addressInfo.AddressLine2;
                            financeData["##MC_Postal_Suburb##"] = addressInfo.Suburb;
                            financeData["##MC_Postal_Postcode##"] = addressInfo.PostCode;
                            financeData["##MC_Postal_State##"] = Enum.GetName(typeof(State), addressInfo.StateId);
                        }
                        else
                        {
                            financeData["##MC_Postal_AddressLine1##"] = string.Empty;
                            financeData["##MC_Postal_AddressLine2##"] = string.Empty;
                            financeData["##MC_Postal_Suburb##"] = string.Empty;
                            financeData["##MC_Postal_Postcode##"] = string.Empty;
                            financeData["##MC_Postal_State##"] = string.Empty;
                        }

                    }
                    else
                    {
                        financeData["##MC_Postal_AddressLine1##"] = string.Empty;
                        financeData["##MC_Postal_AddressLine2##"] = string.Empty;
                        financeData["##MC_Postal_Suburb##"] = string.Empty;
                        financeData["##MC_Postal_Postcode##"] = string.Empty;
                        financeData["##MC_Postal_State##"] = string.Empty;
                        financeData["##MC_Name##"] = string.Empty;
                    }
                }
                else
                {
                    financeData["##MC_Postal_AddressLine1##"] = string.Empty;
                    financeData["##MC_Postal_AddressLine2##"] = string.Empty;
                    financeData["##MC_Postal_Suburb##"] = string.Empty;
                    financeData["##MC_Postal_Postcode##"] = string.Empty;
                    financeData["##MC_Postal_State##"] = string.Empty;
                    financeData["##MC_Name##"] = string.Empty;
                }
                depositInvoiceLetterTemplate.Letter = new LetterTemplateView();
                LetterTemplateView letterFromDB = await _mediaLibraryDAL.GetLetterTemplateBySubTypeDAL(baseHttpRequestContext.OrgId, (short)TemplateSubTypesEnum.InvoiceTemplate);
                if (letterFromDB is null)
                {
                    apiResposne.Result = null;
                    apiResposne.StatusCode = StatusCodes.Status400BadRequest;
                    apiResposne.Message = "Failure";
                    apiResposne.Errors.Add("The Letter Template doesnot exist.");
                    return apiResposne;
                }
                depositInvoiceLetterTemplate.Letter = letterFromDB;

                if (depositTagsData.Result.CompanyDetailsId.HasValue && depositTagsData.Result.CompanyDetailsId.Value > 0)
                {
                    var icFromDB = await _patientDAL.GetCompanyData((Int32)depositTagsData.Result.CompanyDetailsId.Value, baseHttpRequestContext.OrgId);

                    ICLetter iCompanyLetter = new();
                    iCompanyLetter["##IC_Internal_Company_Name##"] = icFromDB.Name;
                    iCompanyLetter["##IC_Internal_Company_Email##"] = icFromDB.Email;
                    iCompanyLetter["##IC_ACN##"] = icFromDB.Acn;
                    iCompanyLetter["##IC_ABN##"] = icFromDB.Abn;
                    // iCompanyLetter["##IC_Office_Address##"] = GetAddressforComapny(icFromDB.CompanyAddresses.ToList(), Convert.ToInt16(AddressType.Physical));
                    // iCompanyLetter["##IC_Other_Address##"] = GetAddressforComapny(icFromDB.CompanyAddresses.ToList(), Convert.ToInt16(AddressType.Other));
                    iCompanyLetter["##IC_HPIO_Location##"] = icFromDB.Hpiolocation;
                    iCompanyLetter["##IC_HPIO_Number##"] = icFromDB.Hpionumber.ToString();
                    iCompanyLetter["##IC_Bank_Details##"] = icFromDB.BankDetails;
                    iCompanyLetter["##IC_Account_Name##"] = icFromDB.AccountName;
                    iCompanyLetter["##IC_BSB##"] = icFromDB.Bsb;
                    iCompanyLetter["##IC_Account_Number##"] = icFromDB.AccountNo;
                    iCompanyLetter["##IC_Work_Number##"] = icFromDB.WorkContact;
                    iCompanyLetter["##IC_Fax_Number##"] = icFromDB.FaxNumber;
                    iCompanyLetter["##IC_Mobile_Number##"] = icFromDB.Mobile;
                    financeData["##HowToPay_AccountName##"] = icFromDB.AccountName;
                    financeData["##HowToPay_AccountNumber##"] = icFromDB.AccountNo;
                    financeData["##HowToPay_BSB##"] = icFromDB.Bsb;
                    iCompanyLetter["##IC_Facility_ID##"] = icFromDB.FacilityId;

                    // Fetch Internal Company Addresses
                    var icpostAddress = icFromDB.CompanyAddresses.ToList().FirstOrDefault(p => p.AddressType == Convert.ToInt16(AddressType.Postal) && p.StatusId == (short)Status.Active);
                    if (icpostAddress is not null)
                    {
                        iCompanyLetter["##IC_Postal_AddressLine1##"] = string.IsNullOrWhiteSpace(icpostAddress.AddressLine1) ? string.Empty : icpostAddress.AddressLine1;
                        iCompanyLetter["##IC_Postal_AddressLine2##"] = string.IsNullOrWhiteSpace(icpostAddress.AddressLine2) ? string.Empty : icpostAddress.AddressLine2;
                        iCompanyLetter["##IC_Postal_Suburb##"] = string.IsNullOrWhiteSpace(icpostAddress.Suburb) ? string.Empty : icpostAddress.Suburb;
                        iCompanyLetter["##IC_Postal_State##"] = Enum.GetName(typeof(State), icpostAddress.StateId);
                        iCompanyLetter["##IC_Postal_Postcode##"] = icpostAddress.PostCode;
                    }
                    else
                    {
                        iCompanyLetter["##IC_Postal_AddressLine1##"] = null;
                        iCompanyLetter["##IC_Postal_AddressLine2##"] = null;
                        iCompanyLetter["##IC_Postal_Suburb##"] = null;
                        iCompanyLetter["##IC_Postal_State##"] = null;
                        iCompanyLetter["##IC_Postal_Postcode##"] = null;

                    }
                    var icOtherAddress = icFromDB.CompanyAddresses.ToList().FirstOrDefault(p => p.AddressType == Convert.ToInt16(AddressType.Other) && p.StatusId == (short)Status.Active);
                    if (icOtherAddress is not null)
                    {
                        iCompanyLetter["##IC_Other_AddressLine1##"] = string.IsNullOrWhiteSpace(icOtherAddress.AddressLine1) ? string.Empty : icOtherAddress.AddressLine1;
                        iCompanyLetter["##IC_Other_AddressLine2##"] = string.IsNullOrWhiteSpace(icOtherAddress.AddressLine2) ? string.Empty : icOtherAddress.AddressLine2;
                        iCompanyLetter["##IC_Other_Suburb##"] = string.IsNullOrWhiteSpace(icOtherAddress.Suburb) ? string.Empty : icOtherAddress.Suburb;
                        iCompanyLetter["##IC_Other_State##"] = Enum.GetName(typeof(State), icOtherAddress.StateId);
                        iCompanyLetter["##IC_Other_Postcode##"] = icOtherAddress.PostCode;
                    }
                    else
                    {
                        iCompanyLetter["##IC_Other_AddressLine1##"] = null;
                        iCompanyLetter["##IC_Other_AddressLine2##"] = null;
                        iCompanyLetter["##IC_Other_Suburb##"] = null;
                        iCompanyLetter["##IC_Other_State##"] = null;
                        iCompanyLetter["##IC_Other_Postcode##"] = null;
                    }
                    if (icFromDB.LogoFileDetailsId != null && icFromDB.LogoFileDetailsId > 0)
                    {
                        string logoSasToken = await GetLogoforCompany(icFromDB.LogoFileDetailsId, baseHttpRequestContext);

                        if (!string.IsNullOrEmpty(logoSasToken))
                            iCompanyLetter["##IC_Company_Image##"] = $"<img src='{logoSasToken}'/>";
                    }
                    else
                        iCompanyLetter["##IC_Company_Image##"] = null;
                    patientTagsModel.Data.AddRange(iCompanyLetter);
                }
                if (depositTagsData.Result.InPatientCompanyDetailsId.HasValue && depositTagsData.Result.InPatientCompanyDetailsId.Value > 0)
                {
                    var inPatientCompanyDB = await _patientDAL.GetCompanyData((Int32)depositTagsData.Result.InPatientCompanyDetailsId.Value, baseHttpRequestContext.OrgId);
                    financeData["##InvoiceFacilityName##"] = inPatientCompanyDB.Name;
                    financeData["##InvoiceFacilityId##"] = inPatientCompanyDB.FacilityId;
                }
                else
                {
                    financeData["##InvoiceFacilityName##"] = null;
                    financeData["##InvoiceFacilityId##"] = null;
                }
                DateTime? invDate = depositTagsData.Result.EstimateDate.HasValue ? depositTagsData.Result.EstimateDate : null;
                if (invDate is not null)
                {
                    DateTime? invDateTz = DateTimeConversion.ConvertTimeFromUtc((DateTime)invDate, timeZone);
                    financeData["##InvoiceDate##"] = invDateTz is not null ? ((DateTime)invDateTz).ToString("dd/MM/yyyy") : null;

                }
                else
                {
                    financeData["##InvoiceDate##"] = depositTagsData.Result.EstimateDate.HasValue ? depositTagsData.Result.EstimateDate.Value.ToString("dd/MM/yyyy") : null;

                }
                financeData["##BalanceOutstanding##"] = string.Format(new CultureInfo("en-AU", false), "{0:C}", depositTagsData.Result.BalanceOutstanding);
                financeData["##InvoiceNumber##"] = depositTagsData.Result.EstimateNumber.ToString();
                //financeData["##InvoiceDate##"] = depositTagsData.Result.EstimateDate.HasValue ? depositTagsData.Result.EstimateDate.Value.ToString("dd/MM/yyyy") : null;
                financeData["##ReceiptedAmount##"] = string.Format(new CultureInfo("en-AU", false), "{0:C}", depositTagsData.Result.ReceiptedAmount);
                var totalAmountPaid = depositTagsData.Result.TotalEstimate;
                var totalEstimate = depositTagsData.Result.BalanceOutstanding + totalAmountPaid;
                var totalRefund = depositTagsData.Result.TotalRefund;
                financeData["##TotalAmountPaid##"] = string.Format(new CultureInfo("en-AU", false), "{0:C}", totalAmountPaid);
                financeData["##TotalEstimate##"] = string.Format(new CultureInfo("en-AU", false), "{0:C}", totalEstimate);
                if (totalRefund != null && totalRefund != 0)
                    financeData["##TotalRefund##"] = string.Format(new CultureInfo("en-AU", false), "{0:C}", totalRefund);
                else
                    financeData["##TotalRefund##"] = null;
                financeData["##AP_Appointment_Provider##"] = depositTagsData.Result.AppointmentServiceProvider;
                financeData["##AP_Appointment_Entity##"] = depositTagsData.Result.AppointmentInternalEntity;
                financeData["##AP_Appointment_Date##"] = depositTagsData.Result.AppointmentDate.HasValue ? depositTagsData.Result.AppointmentDate.Value.ToString("dd/MM/yyyy") : "";
                financeData["##AP_Appointment_Time##"] = depositTagsData.Result.AppointmentDate.HasValue ? depositTagsData.Result.AppointmentDate.Value.ToString("hh:mm") : "";

                financeData["##ReferringProviderName##"] = depositTagsData.Result.ReferralProviderName;
                financeData["##ReferringProviderNo##"] = depositTagsData.Result.ReferringProviderNumber;
                financeData["##ReferralDate##"] = depositTagsData.Result.ReferralDate.HasValue ? depositTagsData.Result.ReferralDate.Value.ToString("dd/MM/yyyy") : "";
                financeData["##ReferralPeriod##"] = depositTagsData.Result.ReferralPeriod;

                financeData["##ReferralAddress##"] = "";
                if (depositTagsData.Result.ReferringProviderCompanyId > 0)
                {
                    var referralCompany = await _patientDAL.GetCompanyData(depositTagsData.Result.ReferringProviderCompanyId, baseHttpRequestContext.OrgId);
                    financeData["##ReferralAddress##"] = GetAddressforComapny(referralCompany.CompanyAddresses.ToList(), Convert.ToInt16(AddressType.Physical));
                }

                financeData["##ServiceProviderName##"] = "";
                financeData["##ServiceProviderNumber##"] = "";

                if (depositTagsData.Result.ExcessAmount != null && depositTagsData.Result.ExcessAmount > 0)
                {

                    financeData["##InvoiceExcessAmount##"] = string.Format(new CultureInfo("en-AU", false), "{0:C}", depositTagsData.Result.ExcessAmount);
                    financeData["##InvoiceExcessAmountPaid##"] = string.Format(new CultureInfo("en-AU", false), "{0:C}", (depositTagsData.Result.ExcessAmount - depositTagsData.Result.ExcessOwing));

                }
                else
                {
                    financeData["##InvoiceExcessAmount##"] = string.Format(new CultureInfo("en-AU", false), "{0:C}", 0);
                    financeData["##InvoiceExcessAmountPaid##"] = string.Format(new CultureInfo("en-AU", false), "{0:C}", 0);

                }

                if (depositTagsData.Result.IsInPatient == true)
                {
                    financeData["##InPatient_or_OutPatient##"] = "*In Patient";

                }
                else
                {
                    financeData["##InPatient_or_OutPatient##"] = "*Out Patient";
                }
                if (depositTagsData.Result.UserDetailsId.HasValue)
                {
                    var providerDetails = await _patientDAL.GetLoggedInUser(depositTagsData.Result.UserDetailsId.Value, baseHttpRequestContext.OrgId);
                    if (providerDetails != null)
                    {
                        var defaultCompany = providerDetails.UserCompanyAssocs.Where(x => x.IsDefault == true).FirstOrDefault();
                        if (providerDetails.IsInternalEntityBank.HasValue && providerDetails.IsInternalEntityBank.Value)
                        {
                            if (defaultCompany != null)
                            {
                                var defaultCompanyDetails = await _patientDAL.GetCompanyData(defaultCompany.CompanyId, baseHttpRequestContext.OrgId);
                                financeData["##HowToPay_AccountName##"] = defaultCompanyDetails.AccountName;
                                financeData["##HowToPay_AccountNumber##"] = defaultCompanyDetails.AccountNo;
                                financeData["##HowToPay_BSB##"] = defaultCompanyDetails.Bsb;
                            }
                        }
                        else
                        {
                            financeData["##HowToPay_AccountName##"] = providerDetails.AccountName;
                            financeData["##HowToPay_AccountNumber##"] = providerDetails.AccountNo;
                            financeData["##HowToPay_BSB##"] = providerDetails.BSB;
                        }

                        financeData["##ServiceProviderName##"] = depositTagsData.Result.ServiceProvider;
                        financeData["##ServiceProviderNumber##"] = defaultCompany.ProviderNumber;
                    }
                }

                financeData["##PD_MedicareCardNumber##"] = "";
                if (depositTagsData.Result.AccountHolderType > 0)
                {
                    var accountHolders = await _accountHolderDAL.GetAccountHolderInfo(baseHttpRequestContext.OrgId, patient_id);
                    if (accountHolders != null && accountHolders.AccountHolderAssocInfos != null)
                    {
                        if (depositTagsData.Result.AccountHolderType == (short)AccountHolderTypes.Patient)
                        {
                            var dvaHolderAsscos = accountHolders.AccountHolderAssocInfos.Where(x => x.AccountHolderType == (short)AccountHolderTypes.DVA).FirstOrDefault();
                            var medicareHolderAsscos = accountHolders.AccountHolderAssocInfos.Where(x => x.AccountHolderType == (short)AccountHolderTypes.Medicare).FirstOrDefault();

                            financeData["##PD_MedicareCardNumber##"] = (medicareHolderAsscos != null ? medicareHolderAsscos.AccountNumber + "(" + medicareHolderAsscos.AccountSubNumber + ")" : "")
                                + (dvaHolderAsscos != null ? "/" + dvaHolderAsscos.AccountNumber : "");
                        }
                        else
                        {
                            var holderAsscos = accountHolders.AccountHolderAssocInfos.Where(x => x.AccountHolderType == depositTagsData.Result.AccountHolderType).FirstOrDefault();
                            if (holderAsscos != null)
                            {
                                financeData["##PD_MedicareCardNumber##"] = holderAsscos.AccountHolderType == (short)AccountHolderTypes.Medicare ? holderAsscos.AccountNumber + "(" + holderAsscos.AccountSubNumber + ")" : holderAsscos.AccountNumber;
                            }
                        }
                    }
                }

                var paymentData = depositTagsData.Result.CustomBillingItemDetails;
                DataTable dt = new DataTable();
                dt.Columns.AddRange(new DataColumn[7] { new DataColumn("Date of Service", typeof(string)),
                            new DataColumn("Item No", typeof(string)),
                            new DataColumn("Description",typeof(string)),
                            new DataColumn("GST",typeof(string)),
                            new DataColumn("Fee",typeof(string)),
                            new DataColumn("Total",typeof(string)),
                            new DataColumn("Notes",typeof(string)),
                            });
                List<string> leftColumns = new()
                {
                    "Description",
                    "Item No",
                    "Notes",
                    "Date of Service"
                };
                foreach (var data in paymentData)
                {
                    dt.Rows.Add(data.DateOfService == default(DateTime) ? "" : data.DateOfService.ToString("dd/MM/yyyy"),
                        data.ItemNumber,
                        data.Description,
                        string.Format(new CultureInfo("en-AU", false), "{0:C}", data.GST),
                        string.Format(new CultureInfo("en-AU", false), "{0:C}", data.Fee),
                        string.Format(new CultureInfo("en-AU", false), "{0:C}", data.GST + data.Fee),
                        string.Format(new CultureInfo("en-AU", false), "{0:C}", data.Note)
                        );
                }

                StringBuilder sb = new StringBuilder();
                sb.Append("<table cellpadding='3' cellspacing='0' style='width:100%;font-size: 9pt;font-family:Calibri'>");
                sb.Append("<tr>");
                foreach (DataColumn column in dt.Columns)
                {

                    if (leftColumns.Contains(column.ColumnName))
                    {
                        if (column.ColumnName != "Notes")
                        {
                            if (column.ColumnName == "Date of Service")
                                sb.Append("<th style='padding-left:5px;background-color: #E5E6E6;color:#35404A;width:15%;text-align: left;margin-top:5px;margin-bottom:5px;'>" + column.ColumnName + "</th>");
                            else if (column.ColumnName == "Description")
                            {
                                sb.Append("<th style='background-color: #E5E6E6;color:#35404A;width:19%;text-align: left;margin-top:5px;margin-bottom:5px;'>" + column.ColumnName + "</th>");

                            }
                            else
                            {

                                sb.Append("<th style='background-color: #E5E6E6;color:#35404A;width:15%;text-align: left;margin-top:5px;margin-bottom:5px;'>" + column.ColumnName + "</th>");

                            }
                        }

                    }
                    else
                    {
                        if (column.ColumnName == "Total")
                            sb.Append("<th style='padding-right:5px;background-color: #E5E6E6;color:#35404A;width:17%;text-align: right;margin-top:5px;margin-bottom:5px;'>" + column.ColumnName + "</th>");
                        else
                            sb.Append("<th style='background-color: #E5E6E6;color:#35404A;width:17%;text-align: right;margin-top:5px;margin-bottom:5px;'>" + column.ColumnName + "</th>");

                    }
                }
                sb.Append("</tr>");

                //Adding DataRow.
                foreach (DataRow row in dt.Rows)
                {
                    sb.Append("<tr>");
                    foreach (DataColumn column in dt.Columns)
                    {
                        if (leftColumns.Contains(column.ColumnName))
                        {
                            if (column.ColumnName != "Notes")
                            {
                                if (column.ColumnName == "Date of Service")
                                    sb.Append($"<td style='padding-left:5px;text-align: left;width:15%;color:35404A;background-color:#F5F6F8;'>" + row[column.ColumnName].ToString() + "</td>");
                                else if (column.ColumnName == "Description")
                                {
                                    sb.Append($"<td style='text-align: left;width:19%;color:35404A;background-color:#F5F6F8;'>" + row[column.ColumnName].ToString() + "</td>");

                                }
                                else
                                {
                                    if (column.ColumnName == "Item No" && row[column.ColumnName].ToString() == "0")
                                    {
                                        sb.Append($"<td style='text-align: left;width:15%;color:35404A;background-color:#F5F6F8;'>" + string.Empty + "</td>");

                                    }
                                    else
                                    {

                                        sb.Append($"<td style='text-align: left;width:15%;color:35404A;background-color:#F5F6F8;'>" + row[column.ColumnName].ToString() + "</td>");

                                    }
                                }
                            }

                        }
                        else
                        {
                            if (column.ColumnName == "Total")
                                sb.Append($"<td style='padding-right:5px;text-align: right;width:17%;color:35404A;background-color:#F5F6F8;'>" + row[column.ColumnName].ToString() + "</td>");
                            else
                                sb.Append($"<td style='text-align: right;width:17%;color:35404A;background-color:#F5F6F8;'>" + row[column.ColumnName].ToString() + "</td>");

                        }

                    }
                    sb.Append("</tr>");
                    sb.Append("<tr><td colspan='5' style='text-align: left;color:35404A;width:100%;background-color:##FFFFFF;font-style: italic;'>" + row["Notes"].ToString() + "</td></tr>");

                }

                sb.Append(" <tr> <td colspan='3' style='width:20%;text-align: right; background-color: #E5E6E6;color:35404A;margin-top:2px;margin-bottom:2px;font-weight:bold;'>Total</td>" +
                    "<td style='text-align: right; background-color: #E5E6E6;color:35404A;width:17%;margin-top:2px;margin-bottom:2px;font-weight:bold;'> " + string.Format(new CultureInfo("en-AU", false), "{0:C}", paymentData.Sum(x => x.GST)) + "</td>" +
                    "<td style='text-align: right; background-color: #E5E6E6;color:35404A;width:17%;margin-top:2px;margin-bottom:2px;font-weight:bold;'>" + string.Format(new CultureInfo("en-AU", false), "{0:C}", paymentData.Sum(x => x.Fee)) + " </td>" +
                    "<td style='text-align: right; background-color: #E5E6E6;color:35404A;width:17;%margin-top:2px;margin-bottom:2px;font-weight:bold;'>" + string.Format(new CultureInfo("en-AU", false), "{0:C}", paymentData.Sum(x => x.Fee) + paymentData.Sum(x => x.GST)) + "</td></tr>");
                //Table end.
                sb.Append("</table>");
                string tableHtmlText = sb.ToString();
                var sfdtResult = await GetSFDTTextData(tableHtmlText, baseHttpRequestContext);

                if (sfdtResult.StatusCode == StatusCodes.Status200OK)
                {
                    financeData["##InvoiceReceiptTable##"] = sfdtResult.Result.ToString();
                }

                if (financeData.Any())
                {
                    patientTagsModel.Data.AddRange(financeData);
                }

                depositInvoiceLetterTemplate.PatientTagsModel = patientTagsModel;
                apiResposne.Result = depositInvoiceLetterTemplate;
                apiResposne.StatusCode = StatusCodes.Status200OK;
                apiResposne.Message = "Success";
                return apiResposne;
            }

            apiResposne.Result = depositInvoiceLetterTemplate;
            apiResposne.StatusCode = StatusCodes.Status400BadRequest;
            apiResposne.Message = "Failure";
            apiResposne.Errors.Add("Patient invoice doesnot exist.");
            return apiResposne;
        }

        private async Task<ApiResponse<MedicalContractorInfo>> FetchHealthFundAddress(string participantId, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<MedicalContractorInfo> apiResponse = new();


            string utilityAPIUrl = _appSettings.ApiUrls["UtilityServiceUrl"] + "/utility/medical_contractor/medical_contractor_address?strParticipantId=" + participantId;
            RestClient restClient = new RestClient(utilityAPIUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            apiResponse = await restClient.GetAsync<ApiResponse<MedicalContractorInfo>>(utilityAPIUrl, null);
            return apiResponse;
        }

        public async Task<ApiResponse<DepositInvoiceLetterTemplate>> GetOECTemplate(long patient_id, long invoiceDetailsId, string transactionId, BaseHttpRequestContext baseHttpRequestContext)
        {

            ApiResponse<DepositInvoiceLetterTemplate> apiResposne = new ApiResponse<DepositInvoiceLetterTemplate>();
            PatientDetail patientDetail = await _patientDAL.GetPatientDetails(baseHttpRequestContext.OrgId, patient_id);
            if (patientDetail == null && patientDetail.Id == 0)
            {
                apiResposne.Result = null;
                apiResposne.StatusCode = StatusCodes.Status400BadRequest;
                apiResposne.Message = "Failure";
                apiResposne.Errors.Add("Patient doesnot exist.");
                return apiResposne;
            }

            patientDetail.Addresses = await _patientDAL.GetPatientAddresses(baseHttpRequestContext.OrgId, patient_id);
            DepositInvoiceLetterTemplate depositInvoiceLetterTemplate = new DepositInvoiceLetterTemplate();

            PatientTagsModel patientTagsModel = new PatientTagsModel();
            PatientForLetter patientData = new();
            patientData["##PD_PatientId##"] = patientDetail.Id.ToString();
            patientData["##PD_Title##"] = patientDetail.TitleId is not null ? EnumExtensions.GetDescription((TitleType)patientDetail.TitleId) : null;
            patientData["##PD_FirstName##"] = patientDetail.FirstName;
            patientData["##PD_Surname##"] = patientDetail.SurName;
            patientData["##PD_Salutation##"] = patientDetail.Salutation;
            patientData["##PD_Date_of_Birth##"] = patientDetail.DateofBirth is not null ? ((DateTime)patientDetail.DateofBirth).ToString("dd/MM/yyyy") : null;
            patientData["##PD_Occupation##"] = patientDetail.Occupation;
            patientData["##PD_Guardian_Name##"] = patientDetail.GuardianName;
            patientData["##PD_Guardian_Work_Number##"] = patientDetail.GuardianWorkContact;
            patientData["##PD_Guardian_Home_Number##"] = patientDetail.GuardianHomeContact;
            patientData["##PD_Guardian_Mobile_Number##"] = patientDetail.GuardianMobileContact;
            patientData["##PD_Patient_Work_Number##"] = patientDetail.WorkContact;
            patientData["##PD_Patient_Home_Number##"] = patientDetail.HomeContact;
            patientData["##PD_Patient_Mobile_Number##"] = patientDetail.Mobile;
            patientData["##PD_Physical_Address##"] = GetAddressforPatient(patientDetail.Addresses.ToList(), Convert.ToInt16(AddressType.Physical));
            patientData["##PD_Postal_Address##"] = GetAddressforPatient(patientDetail.Addresses.ToList(), Convert.ToInt16(AddressType.Postal));
            patientData["##PD_RecordID##"] = patientDetail.RecordId.ToString();

            patientTagsModel.Data = new List<KeyValuePair<string, string>>();
            patientTagsModel.Data.AddRange(patientData);

            //Get InvoiceDeposit Tags Data
            var invoiceDetails = await GetInvoicDetails(invoiceDetailsId, baseHttpRequestContext);

            var templateData = await GetOECTemplateTags(invoiceDetailsId, transactionId, baseHttpRequestContext);
            if (templateData.StatusCode == StatusCodes.Status200OK)
            {
                FinanceDetailsForLetter financeData = new();

                depositInvoiceLetterTemplate.Letter = new LetterTemplateView();
                LetterTemplateView letterFromDB = await _mediaLibraryDAL.GetLetterTemplateBySubTypeDAL(baseHttpRequestContext.OrgId, (short)TemplateSubTypesEnum.OECTemplate);
                if (letterFromDB is null)
                {
                    apiResposne.Result = null;
                    apiResposne.StatusCode = StatusCodes.Status400BadRequest;
                    apiResposne.Message = "Failure";
                    apiResposne.Errors.Add("The Letter Template doesnot exist.");
                    return apiResposne;
                }
                depositInvoiceLetterTemplate.Letter = letterFromDB;

                if (invoiceDetails.Result.CompanyDetailsId.HasValue && invoiceDetails.Result.CompanyDetailsId.Value > 0)
                {
                    var icFromDB = await _patientDAL.GetCompanyData((Int32)invoiceDetails.Result.CompanyDetailsId.Value, baseHttpRequestContext.OrgId);

                    ICLetter iCompanyLetter = new();
                    iCompanyLetter["##IC_Internal_Company_Name##"] = icFromDB.Name;
                    iCompanyLetter["##IC_Internal_Company_Email##"] = icFromDB.Email;
                    iCompanyLetter["##IC_ACN##"] = icFromDB.Acn;
                    iCompanyLetter["##IC_ABN##"] = icFromDB.Abn;
                    iCompanyLetter["##IC_Office_Address##"] = GetAddressforComapny(icFromDB.CompanyAddresses.ToList(), Convert.ToInt16(AddressType.Physical));
                    iCompanyLetter["##IC_Other_Address##"] = GetAddressforComapny(icFromDB.CompanyAddresses.ToList(), Convert.ToInt16(AddressType.Other));
                    iCompanyLetter["##IC_HPIO_Location##"] = icFromDB.Hpiolocation;
                    iCompanyLetter["##IC_HPIO_Number##"] = icFromDB.Hpionumber.ToString();
                    iCompanyLetter["##IC_Bank_Details##"] = icFromDB.BankDetails;
                    iCompanyLetter["##IC_Account_Name##"] = icFromDB.AccountName;
                    iCompanyLetter["##IC_BSB##"] = icFromDB.Bsb;
                    iCompanyLetter["##IC_Account_Number##"] = icFromDB.AccountNo;
                    iCompanyLetter["##IC_Work_Number##"] = icFromDB.WorkContact;
                    iCompanyLetter["##IC_Fax_Number##"] = icFromDB.FaxNumber;
                    iCompanyLetter["##IC_Mobile_Number##"] = icFromDB.Mobile;
                    financeData["##HowToPay_AccountName##"] = icFromDB.AccountName;
                    financeData["##HowToPay_AccountNumber##"] = icFromDB.AccountNo;
                    financeData["##HowToPay_BSB##"] = icFromDB.Bsb;
                    financeData["##LocationId##"] = icFromDB.MinorId;

                    if (icFromDB.LogoFileDetailsId != null && icFromDB.LogoFileDetailsId > 0)
                    {
                        string logoSasToken = await GetLogoforCompany(icFromDB.LogoFileDetailsId, baseHttpRequestContext);

                        if (!string.IsNullOrEmpty(logoSasToken))
                            iCompanyLetter["##IC_Company_Image##"] = $"<img src='{logoSasToken}'/>";
                    }
                    else
                        iCompanyLetter["##IC_Company_Image##"] = null;
                    patientTagsModel.Data.AddRange(iCompanyLetter);
                }


                financeData["##FundExplanationText##"] = templateData.Result.FundExplanationText;
                financeData["##FundReferenceId##"] = templateData.Result.FundReferenceID;
                financeData["##TableDescription##"] = templateData.Result.TableDescription;
                financeData["##ExclusionDescription##"] = templateData.Result.ExclusionDescription;
                financeData["##TableScale##"] = templateData.Result.TableScale;
                financeData["##FinancialStatus##"] = templateData.Result.FinancialStatus;
                financeData["##ExcessAmount##"] = templateData.Result.ExcessAmount == 0 ? "" : string.Format(new CultureInfo("en-AU", false), "{0:C}", templateData.Result.ExcessAmount / 100);
                financeData["##ExcessBonusAmount##"] = templateData.Result.ExcessBonusAmount == 0 ? "" : string.Format(new CultureInfo("en-AU", false), "{0:C}", templateData.Result.ExcessBonusAmount / 100);
                financeData["##ExcessAmountDescription##"] = templateData.Result.ExcessAmountDescription;
                financeData["##BenifitLimitation##"] = templateData.Result.BenefitLimitations;
                financeData["##CoPaymentDaysRemaining##"] = templateData.Result.CoPaymentDayszRemaining;
                financeData["##CoPaymentAmount##"] = templateData.Result.CoPaymentAmount == 0 ? "" : string.Format(new CultureInfo("en-AU", false), "{0:C}", templateData.Result.CoPaymentAmount / 100);
                financeData["##PD_IRN##"] = templateData.Result.PatientIRN;
                financeData["##PD_MedicareCardNumber##"] = templateData.Result.PatientMedicareNumber;
                financeData["##AccountReferenceId##"] = templateData.Result.AccountReferenceId;
                financeData["##Outcome##"] = "";

                var paymentData = templateData.Result.OecPaymentDetails;
                if (paymentData != null)
                {


                    DataTable dt = new DataTable();
                    dt.Columns.AddRange(new DataColumn[9] { new DataColumn("Date of Service", typeof(string)),
                                new DataColumn("Item Number", typeof(string)),
                                new DataColumn("Charge Amount",typeof(string)),
                                new DataColumn("Schedule Fee",typeof(string)),
                                new DataColumn("Medicare Benifit Amount",typeof(string)),
                                new DataColumn("Fund Benefit Amount",typeof(string)),
                                new DataColumn("Medicare Explanation Code",typeof(string)),
                                new DataColumn("Fund Explanation Code",typeof(string)),
                                new DataColumn("Service Fund Assessment Code",typeof(string)),
                                });

                    foreach (var data in paymentData)
                    {
                        dt.Rows.Add(data.DateOfService == default(DateTime) ? "" : data.DateOfService.ToString("dd/MM/yyyy"),
                            data.ItemNumber,
                            string.Format(new CultureInfo("en-AU", false), "{0:C}", long.Parse(data.ChargeAmount) > 0 ? long.Parse(data.ChargeAmount) / 100 : 0),
                            string.Format(new CultureInfo("en-AU", false), "{0:C}", data.ScheduleFee > 0 ? data.ScheduleFee / 100 : 0),
                            string.Format(new CultureInfo("en-AU", false), "{0:C}", data.MedicareBenefitAmount > 0 ? data.MedicareBenefitAmount / 100 : 0),
                            string.Format(new CultureInfo("en-AU", false), "{0:C}", data.FundBenefitAmount > 0 ? data.FundBenefitAmount / 100 : 0),
                            data.MedicalExplanationCode,
                            data.FundExplanationCode,
                            data.ServiceFundAssessmentCode
                            );
                    }

                    StringBuilder sb = new StringBuilder();
                    sb.Append("<table cellpadding='1' cellspacing='0' style='font-size: 7pt;font-family:Arial'>");
                    sb.Append("<tr>");
                    foreach (DataColumn column in dt.Columns)
                    {
                        if (column.ColumnName != "Date of Service")
                        {
                            sb.Append("<th style='text-align: left'>" + column.ColumnName + "</th>");
                        }
                        else
                        {
                            sb.Append("<th style='text-align: left; width:100px'>" + column.ColumnName + "</th>");
                        }
                    }
                    sb.Append("</tr>");

                    //Adding DataRow.
                    foreach (DataRow row in dt.Rows)
                    {
                        sb.Append("<tr>");
                        foreach (DataColumn column in dt.Columns)
                        {
                            sb.Append("<td style='text-align: left'>" + row[column.ColumnName].ToString() + "</td>");
                        }
                        sb.Append("</tr>");
                    }

                    //Table end.
                    sb.Append("</table>");


                    string tableHtmlText = sb.ToString();
                    var sfdtResult = await GetSFDTTextData(tableHtmlText, baseHttpRequestContext);

                    if (sfdtResult.StatusCode == StatusCodes.Status200OK)
                    {
                        financeData["##OECTemplateTable##"] = sfdtResult.Result.ToString();
                    }
                    else
                    {
                        financeData["##OECTemplateTable##"] = "";
                    }

                }

                else
                {
                    financeData["##OECTemplateTable##"] = "";
                }

                if (financeData.Any())
                {
                    patientTagsModel.Data.AddRange(financeData);
                }

                depositInvoiceLetterTemplate.PatientTagsModel = patientTagsModel;
                apiResposne.Result = depositInvoiceLetterTemplate;
                apiResposne.StatusCode = StatusCodes.Status200OK;
                apiResposne.Message = "Success";
                return apiResposne;
            }

            apiResposne.Result = depositInvoiceLetterTemplate;
            apiResposne.StatusCode = StatusCodes.Status400BadRequest;
            apiResposne.Message = "Failure";
            apiResposne.Errors.Add("Patient OEC Template data doesnot exist.");
            return apiResposne;
        }

        /// <summary>
        /// GetLodgmentAndStatementTagData 
        /// </summary>
        /// <param name="invoiceDetailsId"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        private async Task<ApiResponse<InvoiceDepositTagsDetails>> GeInvoiceCustomBillingTagData(long invoiceDetailsId, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<InvoiceDepositTagsDetails> apiResponse = new ApiResponse<InvoiceDepositTagsDetails>();
            string invoiceAPIUrl = _appSettings.ApiUrls["InvoiceServiceUrl"] + "/invoice/invoice_Details/GeInvoiceCustomBillingTagData/" + invoiceDetailsId;
            RestClient restClient = new RestClient(invoiceAPIUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            apiResponse = await restClient.GetAsync<ApiResponse<InvoiceDepositTagsDetails>>(invoiceAPIUrl, null);
            return apiResponse;
        }


        /// <summary>
        /// Fetch MBS Details
        /// </summary>
        /// <param name="listItemIds"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        private async Task<List<ListMbsData>> FetchMBSItemDetails(List<long> listItemIds, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<QueryResultList<ListMbsData>> apiResponseItems = new();
            string filter = "{ListMbsItems:[" + string.Join(",", listItemIds) + "]}";
            string encodedFilter = Base64EncoderHelper.EncodeQueryModelFilterParam(filter);
            string medicalScheduleApiUrl = _appSettings.ApiUrls["MedicalScheduleServiceUrl"] + "/MedicalSchedule/mbs?pn=1&ps=100&soo=Asc&f=" + encodedFilter;
            RestClient restClient = new RestClient(medicalScheduleApiUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            var medicalScheduleMbsApiResponse = await restClient.GetAsync<ApiResponse<QueryResultList<ListMbsData>>>(medicalScheduleApiUrl);
            if (medicalScheduleMbsApiResponse.StatusCode == StatusCodes.Status200OK && medicalScheduleMbsApiResponse.Result is not null && medicalScheduleMbsApiResponse.Result.CurrentCount > 0)
            {
                return medicalScheduleMbsApiResponse.Result.ItemRecords.ToList();
            }
            return null;
        }

        /// <summary>
        /// GetInvoiceDepositTagsDetails 
        /// </summary>
        /// <param name="invoiceDetailsId"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        private async Task<ApiResponse<InvoiceDepositTagsDetails>> GetInvoiceDepositTagsDetails(long invoiceDetailsId, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<InvoiceDepositTagsDetails> apiResponse = new ApiResponse<InvoiceDepositTagsDetails>();
            string invoiceAPIUrl = _appSettings.ApiUrls["InvoiceServiceUrl"] + "/invoice/invoice_Details/GetInvoiceDepositTagsDetails/" + invoiceDetailsId;
            RestClient restClient = new RestClient(invoiceAPIUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            apiResponse = await restClient.GetAsync<ApiResponse<InvoiceDepositTagsDetails>>(invoiceAPIUrl, null);
            return apiResponse;

        }

        /// <summary>
        /// GetInvoiceDepositTagsDetails 
        /// </summary>
        /// <param name="invoiceDetailsId"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        private async Task<ApiResponse<InvoiceDetailView>> GetInvoicDetails(long invoiceDetailsId, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<InvoiceDetailView> apiResponse = new ApiResponse<InvoiceDetailView>();
            string invoiceAPIUrl = _appSettings.ApiUrls["InvoiceServiceUrl"] + "/invoice/invoice_summaries/0/invoice_details/" + invoiceDetailsId;
            RestClient restClient = new RestClient(invoiceAPIUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            apiResponse = await restClient.GetAsync<ApiResponse<InvoiceDetailView>>(invoiceAPIUrl, null);
            return apiResponse;

        }

        /// <summary>
        /// GetSFDTTextData
        /// </summary>
        /// <param name="htmlText"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        private async Task<ApiResponse<JObject>> GetSFDTTextData(string htmlText, BaseHttpRequestContext baseHttpRequestContext)
        {
            //getSFDT Text 
            var token = baseHttpRequestContext.BearerToken;
            string interServiceToken = baseHttpRequestContext.InterServiceToken;
            string synFusionAPiUrl = _appSettings.ApiUrls["SyncFusionServiceUrl"] + "/syncfusion/ConvertHTMLTOSFDT";
            RestClient restClient = new RestClient(synFusionAPiUrl, null, token, interServiceToken);
            return await restClient.PostAsync<ApiResponse<JObject>>(synFusionAPiUrl, htmlText);
        }
        private DateTime? GetDateFromOrganisation(OrganisationView orgDetails)
        {
            bool isWindows = System.Runtime.InteropServices.RuntimeInformation
                                                 .IsOSPlatform(OSPlatform.Windows);
            string timeZone = (isWindows) ? orgDetails.WindowsTimeZoneData : orgDetails.LinuxTimeZoneData;

            var CompanyDateTimeNow = DateTimeConversion.ConvertTimeFromUtc(DateTime.UtcNow, timeZone);
            //if (CompanyDateTimeNow is null)
            //{
            //    CompanyDateTimeNow = DateTimeConversion.ConvertTimeFromUtc(DateTime.UtcNow, orgDetails.LinuxTimeZoneData);
            //}

            return CompanyDateTimeNow;
        }
        private async Task<OrganisationView> FetchMasterCompanyDetails(BaseHttpRequestContext baseHttpRequestContext)
        {

            ApiResponse<OrganisationView> apiResponseCompany = new();
            string endpoint = string.Format("/company/Organisation/{0}", baseHttpRequestContext.OrgId);
            string companySeviceUrl = _appSettings.ApiUrls["CompanyServiceUrl"] + endpoint;
            RestClient restClientCompany = new RestClient(companySeviceUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            apiResponseCompany = await restClientCompany.GetAsync<ApiResponse<OrganisationView>>(companySeviceUrl);
            return (apiResponseCompany == null || apiResponseCompany.Result == null) ? null : apiResponseCompany.Result;

        }

        /// <summary>
        /// GetLodgmentAndStatementTagData 
        /// </summary>
        /// <param name="invoiceDetailsId"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        private async Task<ApiResponse<LodgmentAndStatementTemplateTags>> GetLodgmentAndStatementTagData(long invoiceDetailsId, string transactionID, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<LodgmentAndStatementTemplateTags> apiResponse = new ApiResponse<LodgmentAndStatementTemplateTags>();
            string medicarePaymentAPIUrl = _appSettings.ApiUrls["MedicareServiceUrl"] + string.Format("/medicare/GetLodgmentAndStatementTags/{0}/{1}", invoiceDetailsId, transactionID);
            RestClient restClient = new RestClient(medicarePaymentAPIUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            return await restClient.GetAsync<ApiResponse<LodgmentAndStatementTemplateTags>>(medicarePaymentAPIUrl, null);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="lstCompanyIds"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<List<CompanyDetailInfo>> GetCompanyByIds(List<int> lstCompanyIds, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<List<CompanyDetailInfo>> apiResponseCompany = new();
            int ps = lstCompanyIds.Count();
            string filter = "{CompanyDetailId:[" + string.Join(",", lstCompanyIds) + "]}";
            string endpoint = "/company/company_details_info?pn = 1 & ps =" + ps;
            string encodedFilter = Base64EncoderHelper.EncodeQueryModelFilterParam(filter);
            string companySeviceUrl = _appSettings.ApiUrls["CompanyServiceUrl"] + endpoint + "&f=" + encodedFilter;
            RestClient restClientCompany = new RestClient(companySeviceUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            apiResponseCompany = await restClientCompany.GetAsync<ApiResponse<List<CompanyDetailInfo>>>(companySeviceUrl);

            return (apiResponseCompany == null || apiResponseCompany.Result == null) ? null : apiResponseCompany.Result;
        }

        /// <summary>
        /// GetOCETemplateData 
        /// </summary>
        /// <param name="invoiceDetailsId"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        private async Task<ApiResponse<OECTemplateTags>> GetOECTemplateTags(long invoiceDetailsId, string transactionId, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<OECTemplateTags> apiResponse = new ApiResponse<OECTemplateTags>();
            string medicarePaymentAPIUrl = _appSettings.ApiUrls["MedicareServiceUrl"] + string.Format("/medicare/GetOECTemplateTagsData/{0}/{1}", invoiceDetailsId, transactionId);
            RestClient restClient = new RestClient(medicarePaymentAPIUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            return await restClient.GetAsync<ApiResponse<OECTemplateTags>>(medicarePaymentAPIUrl, null);
        }


        private string GetAgeforPatient(DateTime dateOfBirth)
        {
            DateTime today = DateTime.Today;
            int age = today.Year - dateOfBirth.Year;

            if (dateOfBirth > today.AddYears(-age))
            {
                age--;
            }

            return age.ToString();
        }

        /// <summary>
        /// GetExternalCompanyTags 
        /// </summary>
        /// <param name="CompanyId"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        private async Task<ECLetter> GetExtCompanyTags(int companyId, int orgId)
        {

            ECLetter ecLetter = new();
            var ecFromDB = await _patientDAL.GetCompanyData(companyId, orgId);
            if (ecFromDB is not null)
            {
                if (ecFromDB.CompanyTypeId == (short)CompanyType.InternalCompany)
                {
                    return null;
                }
                ecLetter["##EC_External_Company_Name##"] = ecFromDB.Name;
                ecLetter["##EC_External_Company_Email##"] = ecFromDB.Email;
                ecLetter["##EC_ABN##"] = ecFromDB.Abn;
                ecLetter["##EC_ACN##"] = ecFromDB.Acn;
                ecLetter["##EC_HPIO_Number##"] = ecFromDB.Hpionumber.ToString();
                ecLetter["##EC_Work_Number##"] = ecFromDB.WorkContact;
                ecLetter["##EC_Fax_Number##"] = ecFromDB.FaxNumber;
                ecLetter["##EC_Mobile_Number##"] = ecFromDB.Mobile;
                ecLetter["##EC_Facility_ID##"] = ecFromDB.FacilityId;

                // External Company Addresses
                var ecpostAddress = ecFromDB.CompanyAddresses.ToList().FirstOrDefault(p => p.AddressType == Convert.ToInt16(AddressType.Postal) && p.StatusId == (short)Status.Active);
                if (ecpostAddress is not null)
                {
                    ecLetter["##EC_Postal_AddressLine1##"] = string.IsNullOrWhiteSpace(ecpostAddress.AddressLine1) ? string.Empty : ecpostAddress.AddressLine1;
                    ecLetter["##EC_Postal_AddressLine2##"] = string.IsNullOrWhiteSpace(ecpostAddress.AddressLine2) ? string.Empty : ecpostAddress.AddressLine2;
                    ecLetter["##EC_Postal_Suburb##"] = string.IsNullOrWhiteSpace(ecpostAddress.Suburb) ? string.Empty : ecpostAddress.Suburb;
                    ecLetter["##EC_Postal_State##"] = Enum.GetName(typeof(State), ecpostAddress.StateId);
                    ecLetter["##EC_Postal_Postcode##"] = ecpostAddress.PostCode;
                }
                else
                {
                    ecLetter["##EC_Postal_AddressLine1##"] = null;
                    ecLetter["##EC_Postal_AddressLine2##"] = null;
                    ecLetter["##EC_Postal_Suburb##"] = null;
                    ecLetter["##EC_Postal_State##"] = null;
                    ecLetter["##EC_Postal_Postcode##"] = null;

                }
                var ecOtherAddress = ecFromDB.CompanyAddresses.ToList().FirstOrDefault(p => p.AddressType == Convert.ToInt16(AddressType.Other) && p.StatusId == (short)Status.Active);
                if (ecOtherAddress is not null)
                {
                    ecLetter["##EC_Other_AddressLine1##"] = string.IsNullOrWhiteSpace(ecOtherAddress.AddressLine1) ? string.Empty : ecOtherAddress.AddressLine1;
                    ecLetter["##EC_Other_AddressLine2##"] = string.IsNullOrWhiteSpace(ecOtherAddress.AddressLine2) ? string.Empty : ecOtherAddress.AddressLine2;
                    ecLetter["##EC_Other_Suburb##"] = string.IsNullOrWhiteSpace(ecOtherAddress.Suburb) ? string.Empty : ecOtherAddress.Suburb;
                    ecLetter["##EC_Other_State##"] = Enum.GetName(typeof(State), ecOtherAddress.StateId);
                    ecLetter["##EC_Other_Postcode##"] = ecOtherAddress.PostCode;
                }
                else
                {
                    ecLetter["##EC_Other_AddressLine1##"] = null;
                    ecLetter["##EC_Other_AddressLine2##"] = null;
                    ecLetter["##EC_Other_Suburb##"] = null;
                    ecLetter["##EC_Other_State##"] = null;
                    ecLetter["##EC_Other_Postcode##"] = null;
                }
            }
            return ecLetter;
        }
        private ECLetter GetExtCompanyTagsEmptyData()
        {
            ECLetter ecLetter = new();
            ecLetter["##EC_External_Company_Name##"] = null;
            ecLetter["##EC_External_Company_Email##"] = null;
            ecLetter["##EC_ABN##"] = null;
            ecLetter["##EC_ACN##"] = null;
            ecLetter["##EC_HPIO_Number##"] = null;
            ecLetter["##EC_Work_Number##"] = null;
            ecLetter["##EC_Fax_Number##"] = null;
            ecLetter["##EC_Mobile_Number##"] = null;
            ecLetter["##EC_Facility_ID##"] = null;
            ecLetter["##EC_Other_AddressLine1##"] = null;
            ecLetter["##EC_Other_AddressLine2##"] = null;
            ecLetter["##EC_Other_Suburb##"] = null;
            ecLetter["##EC_Other_State##"] = null;
            ecLetter["##EC_Other_Postcode##"] = null;
            ecLetter["##EC_Postal_AddressLine1##"] = null;
            ecLetter["##EC_Postal_AddressLine2##"] = null;
            ecLetter["##EC_Postal_Suburb##"] = null;
            ecLetter["##EC_Postal_State##"] = null;
            ecLetter["##EC_Postal_Postcode##"] = null;

            return ecLetter;
        }


        private async Task StorePatientSearchMessage(string orgCode)
        {
            EmailRequestDataModel patientRequestDataModel = new EmailRequestDataModel
            {
                OrgCode = orgCode,
                PropertyType = (int)EODRequestDataType.Patient_Search,
                PropertyId = 0,
                ActicityLogChildId = 0


            };
            Dictionary<string, string> userPros = new Dictionary<string, string>
                        {
                            { "RequestType","Patient"},
                            { "to",_configuration["AzureAD:ASBSubNamePatientSearch"]}
                        };
            byte[] jsonContentByteArr = Encoding.GetEncoding("iso-8859-1").GetBytes(JsonConvert.SerializeObject(patientRequestDataModel));
            await _asbMessageSenderHelper.MessageSenderTopic(jsonContentByteArr, userPros, _configuration["AzureAD:AzSBConnStringPatient"], _configuration["AzureAD:ASBTopicPatient"]);
        }

        public async Task<ApiResponse<PatientViewForClaims>> GetPatientDetailsForHCPPatientConsent(BaseHttpRequestContext baseHttpRequestContext, long id)
        {
            ApiResponse<PatientViewForClaims> apiResposne = new();
            var orgId = baseHttpRequestContext.OrgId;

            PatientViewForClaims patientFromDB = await _accountHolderDAL.PatientViewForClaims(orgId, id);
            //to fetch default referral

            return new ApiResponse<PatientViewForClaims>
            {
                StatusCode = StatusCodes.Status200OK,
                Message = "Success",
                Result = patientFromDB
            };
        }

        public async Task<ApiResponse<PatientInfo>> GetPatientDetailsFromRecordId(BaseHttpRequestContext baseHttpRequestContext, long recordId)
        {
            int orgId = baseHttpRequestContext.OrgId;
            ApiResponse<PatientInfo> apiResposne = new();
            PatientInfo patient = await _patientDAL.GetPatientDetailsFromRecordId(orgId, recordId);

            apiResposne.Result = patient;
            apiResposne.StatusCode = StatusCodes.Status200OK;
            apiResposne.Message = "Success";
            return apiResposne;
        }
        public async Task<ApiResponse<List<HIServiceDetailLogs>>> GetHIServiceDetails(BaseHttpRequestContext baseHttpRequestContext, long id)
        {
            ApiResponse<List<HIServiceDetailLogs>> apiResponse = new();
            ICollection<HIServiceDetail> ihiServiceDetails = await _patientDAL.GetHIServiceDetails(baseHttpRequestContext.OrgId, id);
            List<HIServiceDetailLogs> ihiServiceDetailList = _mapper.Map<List<HIServiceDetailLogs>>(ihiServiceDetails);
            apiResponse.Result = ihiServiceDetailList;
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            return apiResponse;
        }

        public async Task<ApiResponse<long?>> AddHIServiceDetail(BaseHttpRequestContext baseHttpRequestContext, long patientId, HIServiceDetail inputHIServiceDetail)
        {
            var orgId = baseHttpRequestContext.OrgId;
            var userId = baseHttpRequestContext.UserId;

            if (inputHIServiceDetail.HIStatusId == default(int) && inputHIServiceDetail.HIRecordStatusId == default(int) && string.IsNullOrWhiteSpace(inputHIServiceDetail.IHINumber))
            {
                inputHIServiceDetail.HIStatusId = inputHIServiceDetail.Reason.Contains(HIServiceStatusType.Retired.ToString(), StringComparison.OrdinalIgnoreCase)
                    ? (short)HIServiceStatusType.Retired
                    : (short)HIServiceStatusType.Expired;
            }

            if (inputHIServiceDetail.HIRecordSource == default(int))
                inputHIServiceDetail.HIRecordSource = (short)HIRecordSourceType.Manual;

            // Check for existing IHI number
            var isIHINumberExist = await _patientDAL.GetPatientDetailsByIHINumber(orgId, inputHIServiceDetail.IHINumber);
            if (isIHINumberExist?.Any(x => x.Id != patientId) == true)
            {
                var existingPatient = isIHINumberExist.First(x => x.Id != patientId);
                var validationMsg = $"IHINumber ({inputHIServiceDetail.IHINumber}) is already associated with another patient - {existingPatient.SurName}, {existingPatient.FirstName} || ID: {existingPatient.Id}.";

                inputHIServiceDetail.HIStatusId = 0;
                inputHIServiceDetail.HIRecordStatusId = 0;
                inputHIServiceDetail.VerificationMessage = $"Verification failed for the given IHI number";
                inputHIServiceDetail.Reason = validationMsg;
                inputHIServiceDetail.PatientDetailsId = patientId;
                inputHIServiceDetail.OrgId = orgId;
                inputHIServiceDetail.HIServiceTypeId = (short)HIServiceType.IHI;
                inputHIServiceDetail.CreatedDate = DateTime.UtcNow;
                inputHIServiceDetail.CreatedBy = userId;

                DateTime.SpecifyKind(inputHIServiceDetail.CreatedDate, DateTimeKind.Utc);
                await _patientDAL.AddHIServiceDetail(inputHIServiceDetail);

                return new ApiResponse<long?>
                {
                    StatusCode = StatusCodes.Status200OK,
                    Message = "Failure",
                    Result = null,
                    Errors = new List<string> { validationMsg, inputHIServiceDetail.VerificationMessage }
                };
            }

            inputHIServiceDetail.PatientDetailsId = patientId;
            inputHIServiceDetail.OrgId = orgId;
            inputHIServiceDetail.HIServiceTypeId = (short)HIServiceType.IHI;
            inputHIServiceDetail.CreatedDate = DateTime.UtcNow;
            inputHIServiceDetail.CreatedBy = userId;

            DateTime.SpecifyKind(inputHIServiceDetail.CreatedDate, DateTimeKind.Utc);
            var id = await _patientDAL.AddHIServiceDetail(inputHIServiceDetail);
            return new ApiResponse<long?>
            {
                StatusCode = id > 0 ? StatusCodes.Status200OK : StatusCodes.Status400BadRequest,
                Message = id > 0 ? "Success" : "Failure",
                Result = id > 0 ? id : null
            };
        }

        public async Task<ApiResponse<string>> EditHIServiceDetail(BaseHttpRequestContext baseHttpRequestContext, long patientId, long hiservicedetailId, HIServiceDetail inputHIServiceDetail)
        {
            ApiResponse<string> apiResponse = new();
            var orgId = baseHttpRequestContext.OrgId;
            var userId = baseHttpRequestContext.UserId;

            // Check for existing IHI number
            var isIHINumberExist = await _patientDAL.GetPatientDetailsByIHINumber(orgId, inputHIServiceDetail.IHINumber);
            if (isIHINumberExist?.Any(x => x.Id != patientId) == true)
            {
                var existingPatient = isIHINumberExist.First(x => x.Id != patientId);
                var validationMsg = $"IHINumber ({inputHIServiceDetail.IHINumber}) is already associated with another patient - {existingPatient.SurName}, {existingPatient.FirstName} || ID: {existingPatient.Id}.";

                return new ApiResponse<string>
                {
                    StatusCode = StatusCodes.Status200OK,
                    Message = "Failure",
                    Result = null,
                    Errors = new List<string> { validationMsg }
                };
            }

            List<HIServiceDetail> ihiDetails = new();

            var existingihiDetails = await _patientDAL.GetExistingHIServiceDetailById(hiservicedetailId);
            existingihiDetails.ModifiedBy = userId;
            existingihiDetails.ModifiedDate = DateTime.UtcNow;
            if (existingihiDetails.HIRecordStatusId == (short)HIServiceStatusType.Verified)
                existingihiDetails.HIRecordStatusId = (short)HIServiceStatusType.UnVerified;
            if (existingihiDetails.HIStatusId == (short)HIServiceStatusType.Active)
                existingihiDetails.HIStatusId = (short)HIServiceStatusType.Deactivated;
            ihiDetails.Add(existingihiDetails);

            if (inputHIServiceDetail.HIRecordSource == default(int))
                inputHIServiceDetail.HIRecordSource = (short)HIRecordSourceType.Manual;

            inputHIServiceDetail.PatientDetailsId = patientId;
            inputHIServiceDetail.OrgId = orgId;
            inputHIServiceDetail.HIServiceTypeId = (short)HIServiceType.IHI;
            inputHIServiceDetail.CreatedBy = inputHIServiceDetail.ModifiedBy = userId;
            inputHIServiceDetail.CreatedDate = DateTime.UtcNow;
            inputHIServiceDetail.ModifiedDate = DateTime.UtcNow;
            ihiDetails.Add(inputHIServiceDetail);

            await _patientDAL.UpdateHIServiceDetail(ihiDetails);

            apiResponse.Result = "Success";
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            return apiResponse;
        }

        public async Task<ApiResponse<PatientDraftData>> GetPatientDraftData(BaseHttpRequestContext baseHttpRequestContext, long prid)
        {
            ApiResponse<PatientDraftData> apiResponse = new();
            var orgId = baseHttpRequestContext.OrgId;


            var patientDraftFromDB = await _patientDAL.GetPatientDraftData(orgId, prid);
            if (patientDraftFromDB is null)
            {
                apiResponse.Result = null;
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Patient Draft Data Does not exists";
                return apiResponse;
            }
            else
            {
                apiResponse.Result = patientDraftFromDB;
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";
                return apiResponse;
            }
        }
    }
}
