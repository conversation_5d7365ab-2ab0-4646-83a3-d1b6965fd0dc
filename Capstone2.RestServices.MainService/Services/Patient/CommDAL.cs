﻿using Capstone2.RestServices.Patient.Context;
using Capstone2.RestServices.Patient.Interfaces;
using Capstone2.RestServices.Patient.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Patient.Services
{
    public class CommDAL : ICommDAL
    {
        public readonly ReadOnlyPatientDBContext _readOnlyDbContext;
        public readonly UpdatablePatientDBContext _updatableDBContext;
        public CommDAL(ReadOnlyPatientDBContext readOnlyDbContext, UpdatablePatientDBContext updatableDBContext)
        {
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
        }

        /// <summary>
        /// method to add a Communication note to database
        /// </summary>
        /// <param name="patientDetail"></param>
        /// <returns></returns>
        public async Task<int> AddCommNotesDAL(CommunicationNote commNote)
        {
            await _updatableDBContext.CommunicationNotes.AddAsync(commNote);
            return await _updatableDBContext.SaveChangesAsync();
        }

        public async Task<int> AddCommNotes(CommunicationNote commNote)
        {
            _updatableDBContext.CommunicationNotes.Add(commNote);
            return await _updatableDBContext.SaveChangesAsync();
        }

        /// <summary>
        /// Method to get a Communication Note from database based on id and orgId
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<CommunicationNote> GetCommNoteDAL(int orgId, long id)
        {
            return await _readOnlyDbContext.CommunicationNotes.FirstOrDefaultAsync(p => p.Id == id && p.OrgId == orgId);
        }

        /// <summary>
        /// Method to get a Parent Communication Note from database based on id and orgId
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="patientDetailsId"></param>
        /// <param name="letterId"></param>
        /// <returns></returns>
        public async Task<CommunicationNote> GetParentCommNoteDAL(int orgId, long patientDetailsId, long? letterId)
        {
            return await _readOnlyDbContext.CommunicationNotes.FirstOrDefaultAsync(p => p.PatientDetailsId == patientDetailsId && p.LetterId == letterId && p.OrgId == orgId && p.IsParent == true);
        }

        public async Task<QueryResultList<ListCommunicationNote>> ListCommNotesDAL(int orgId, QueryModel queryModel, CommNotesFilterModel commfilterModel, long patient_id)
        {
            List<SqlParameter> paramsList = new List<SqlParameter>();
            var limit = queryModel.PageSize;
            var offset = (queryModel.PageNumber - 1) * queryModel.PageSize;
            paramsList.Add(new SqlParameter("@OrgId", orgId));
            paramsList.Add(new SqlParameter("@offset", offset));
            paramsList.Add(new SqlParameter("@limit", limit));
            paramsList.Add(new SqlParameter("@patient_id", patient_id));

            if (!(commfilterModel is null))
            {
                if (commfilterModel.StatusId != null && commfilterModel.StatusId.Any())
                {
                    paramsList.Add(new SqlParameter("@StatusId", string.Join<short>(",", commfilterModel.StatusId)));
                }
                if (commfilterModel.CommTypeId != null && commfilterModel.CommTypeId.Any())
                {
                    paramsList.Add(new SqlParameter("@CommTypeId", string.Join<short>(",", commfilterModel.CommTypeId)));

                }
                if (commfilterModel.CommGroupTypeId != null && commfilterModel.CommGroupTypeId.Any())
                {
                    paramsList.Add(new SqlParameter("@CommGroupTypeId", string.Join<short>(",", commfilterModel.CommGroupTypeId)));
                }
                if (commfilterModel.GroupId != null && commfilterModel.GroupId.Any())
                {
                    paramsList.Add(new SqlParameter("@GroupId", string.Join(",", commfilterModel.GroupId)));

                }
                if (commfilterModel.MediaLibrarySubTypeId != null && commfilterModel.MediaLibrarySubTypeId.Any())
                {
                    paramsList.Add(new SqlParameter("@MediaLibrarySubTypeId", string.Join<short>(",", commfilterModel.MediaLibrarySubTypeId)));
                }
                if (commfilterModel.IsParent != null && commfilterModel.IsParent.Any())
                {
                    paramsList.Add(new SqlParameter("@IsParent", string.Join<bool>(",", commfilterModel.IsParent)));
                }
                if (commfilterModel.LetterId != null && commfilterModel.LetterId.Any())
                {
                    paramsList.Add(new SqlParameter("@LetterId", string.Join<long>(",", commfilterModel.LetterId)));
                }
            }
            if (!(queryModel.SortOrder is null))
            {
                paramsList.Add(new SqlParameter("@sortOrder", queryModel.SortOrder));

            }
            if (!(queryModel.SortTerm is null))
            {
                paramsList.Add(new SqlParameter("@sortTerm", queryModel.SortTerm));
            }

            var response = await ExecuteStoredProcedure("[Patient].[ListCommNotes]", paramsList);
            response.PageNumber = queryModel.PageNumber;
            response.PageSize = queryModel.PageSize;
            return response;
        }

        private async Task<QueryResultList<ListCommunicationNote>> ExecuteStoredProcedure(string storedProcedureName, IEnumerable<SqlParameter> parameters)
        {
            var dbConnection = (_readOnlyDbContext.Database.GetDbConnection());
            using (dbConnection)
            {
                using (var cmd = dbConnection.CreateCommand())
                {
                    cmd.CommandText = storedProcedureName;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandTimeout = 1800;
                    foreach (var parameter in parameters)
                    {
                        cmd.Parameters.Add(parameter);
                    }
                    dbConnection.Open();
                    var reader = await cmd.ExecuteReaderAsync();
                    return SqlDatoToJson(reader);
                }
            }
        }

        private QueryResultList<ListCommunicationNote> SqlDatoToJson(DbDataReader dataReader)
        {
            var dataTable = new DataTable();
            List<ListCommunicationNote> convList = new List<ListCommunicationNote>();
            dataTable.Load(dataReader);
            try
            {
                DateTime validValue;
                convList = (from rw in dataTable.AsEnumerable()
                            select new ListCommunicationNote
                            {
                                Id = Convert.ToInt32(rw["Id"]),
                                OrgId = Convert.ToInt32(rw["OrgId"]),
                                PatientDetailsId = Convert.ToInt64(rw["PatientDetailsId"]),
                                CommTypeId = Convert.ToInt16(rw["CommTypeId"]),
                                CommGroupTypeId = Convert.ToInt16(rw["CommGroupTypeId"]),
                                MediaLibrarySubTypeId = rw["MediaLibrarySubTypeId"] == DBNull.Value ? (short?)null : Convert.ToInt16(rw["MediaLibrarySubTypeId"]),
                                Notes = rw["Notes"] == DBNull.Value ? null : Convert.ToString(rw["Notes"]),
                                StatusId = (short)(rw["StatusId"] == DBNull.Value ? (short?)null : Convert.ToInt16(rw["StatusId"])),
                                VersionNo = rw["VersionNo"] == DBNull.Value ? (short?)null : Convert.ToInt16(rw["VersionNo"]),
                                CommunicationNotesId = rw["CommunicationNotesId"] == DBNull.Value ? (long?)null : Convert.ToInt64(rw["CommunicationNotesId"]),
                                FileDetailsId = rw["FileDetailsId"] == DBNull.Value ? (long?)null : Convert.ToInt64(rw["FileDetailsId"]),
                                CreatedDate = DateTime.Parse(Convert.ToString(rw["CreatedDate"])),
                                ModifiedBy = rw["ModifiedBy"] == DBNull.Value ? (long?)null : Convert.ToInt64(rw["ModifiedBy"]),
                                ModifiedDate = DateTime.TryParse(Convert.ToString(rw["ModifiedDate"]), out validValue) ? validValue : (DateTime?)null,
                                Modifier = JsonConvert.DeserializeObject<ModifierDetails>(Convert.ToString(rw["Modifier"])),
                                GroupId = rw["GroupId"] == DBNull.Value ? null : (Guid)rw["GroupId"],
                                LetterStatusId = (rw["LetterStatusId"] == DBNull.Value ? (short?)null : Convert.ToInt16(rw["LetterStatusId"])),
                                LetterId = (rw["LetterId"] == DBNull.Value ? (long?)null : Convert.ToInt64(rw["LetterId"])),
                                EpisodesId = (rw["EpisodesId"] == DBNull.Value ? (int?)null : Convert.ToInt32(rw["EpisodesId"]))

                            }).ToList();
            }
            catch (Exception E)
            {

            }
            var dataTable2 = new DataTable();
            dataTable2.Load(dataReader);
            var Total = int.Parse(JsonConvert.SerializeObject(dataTable2.Rows[0][0]));
            var respose = new QueryResultList<ListCommunicationNote>()
            {
                ItemRecords = convList,
                CurrentCount = convList.Count(),
                TotalCount = Total,
                PageNumber = 0,
                PageSize = 0
            };
            return respose;
        }

        /// <summary>
        /// Method to update the communication notes
        /// </summary>
        /// <param name="inputCommNotes"></param>
        public async Task<int> EditCommNotesDAL(CommunicationNote inputCommNotes)
        {
            _updatableDBContext.CommunicationNotes.Update(inputCommNotes);
           int rows = await _updatableDBContext.SaveChangesAsync();
            _updatableDBContext.Entry(inputCommNotes).State = EntityState.Modified;
            return rows;
        }

        public async Task<CommunicationNote> GetLastCommNotesDAL(CommunicationNote inputCommNotes)
        {
            return await _readOnlyDbContext.CommunicationNotes.Where(p => p.Id == inputCommNotes.CommunicationNotesId || p.CommunicationNotesId == inputCommNotes.CommunicationNotesId).OrderByDescending(x => x.CreatedDate).FirstOrDefaultAsync();
        }

        public async Task<FileDetails> GetFileDetails(int orgId, long Id)
        {
            return await _readOnlyDbContext.FileDetails.Where(p => p.Id == Id).FirstOrDefaultAsync();
        }

        /// <summary>
        /// Method to get All Version Communication Note from database based on id and orgId
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<List<CommunicationNote>> GetCommNoteAllVersionDAL(int orgId, long id)
        {
            List<CommunicationNote> originalCommNotes = new();
            var commNotes = _readOnlyDbContext.CommunicationNotes.Where(p => p.Id == id).FirstOrDefault();
            if (commNotes.CommunicationNotesId != null)
            {
                originalCommNotes = await _readOnlyDbContext.CommunicationNotes.Where(p => p.Id == commNotes.CommunicationNotesId || p.CommunicationNotesId == commNotes.CommunicationNotesId).OrderByDescending(x => x.CreatedDate).ToListAsync();
            }
            else
            {
                originalCommNotes.Add(commNotes);
            }
            return originalCommNotes;
        }

        public async Task AddCommNotesRange(List<CommunicationNote> lstCommunicationNotes)
        {
            _updatableDBContext.CommunicationNotes.AddRange(lstCommunicationNotes);
            await _updatableDBContext.SaveChangesAsync();
        }

        public async Task<FileDetails> GetFileDetailsByFileName(int orgId, string fileName)
        {
            return await _readOnlyDbContext.FileDetails.Where(p => p.FileName == fileName).FirstOrDefaultAsync();
        }
    }
}
