﻿using AutoMapper;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.Patient.Interfaces;
using Capstone2.RestServices.Patient.Models;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Capstone2.Framework.RestApi;

namespace Capstone2.RestServices.Patient.Services
{
    public class PathologyRequestBAL : IPathologyRequestBAL
    {
        public readonly IPathologyRequestDAL _pathologyRequestDAL;
        public IMapper _mapper;
        public readonly AppSettings _appSettings;
        public PathologyRequestBAL(IPathologyRequestDAL pathologyRequestDAL, IMapper mapper, IOptions<AppSettings> appSettings)
        {
            _pathologyRequestDAL = pathologyRequestDAL;
            _mapper = mapper;
            _appSettings = appSettings.Value;
        }

        /// <summary>
        /// Method to save a entry in PathologyRequestData
        /// </summary>
        /// <param name="PathologyRequestPayload"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> AddPathologyRequestData(BaseHttpRequestContext baseHttpRequestContext, long patientId, PathologyRequest pathologyRequestData)
        {
            var orgId = baseHttpRequestContext.OrgId;
            var loggedInUser = baseHttpRequestContext.UserId;

            pathologyRequestData.PatientDetailsId = patientId;
            pathologyRequestData.OrgId = orgId;
            pathologyRequestData.CreatedBy = loggedInUser;
            pathologyRequestData.CreatedDate = DateTime.UtcNow;
            //pathologyRequestData.RequestDate = DateTime.UtcNow;
            pathologyRequestData.StatusId = (short)Status.Active;

            if (pathologyRequestData?.PathologyRequestDoctorsAssocs?.Any() ?? false)
            {
                pathologyRequestData.PathologyRequestDoctorsAssocs.ToList().ForEach(a => { a.OrgId = orgId; a.CreatedBy = loggedInUser; a.StatusId = (short)Status.Active; });
            }

            DateTime.SpecifyKind(pathologyRequestData.CreatedDate, DateTimeKind.Utc);
            var id = await _pathologyRequestDAL.AddPathologyRequestDataAsync(pathologyRequestData);
            if (id > 0)
            {
                ApiResponse<long?> apiResponse = new()
                {
                    StatusCode = StatusCodes.Status200OK,
                    Message = "Success",
                    Result = id
                };
                return apiResponse;
            }
            else
            {
                ApiResponse<long?> apiResponse = new()
                {
                    StatusCode = StatusCodes.Status400BadRequest,
                    Message = "Failure",
                    Result = null
                };
                return apiResponse;
            }
        }

        /// <summary>
        /// Get PathologyRequestDataby ID
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<PathologyRequestView>> GetPathologyRequestData(BaseHttpRequestContext baseHttpRequestContext, long patientId, long pathologyRequestId)
        {
            ApiResponse<PathologyRequestView> apiResposne = new();
            var orgId = baseHttpRequestContext.OrgId;
            var PathologyRequestFromDB = await _pathologyRequestDAL.GetPathologyRequestData(orgId, pathologyRequestId);
            if (PathologyRequestFromDB is null)
            {
                apiResposne.Result = null;
                apiResposne.StatusCode = StatusCodes.Status400BadRequest;
                apiResposne.Message = "Failure";
                apiResposne.Errors.Add("The PathologyRequestdoesnot exist.");
                return apiResposne;
            }
            if (PathologyRequestFromDB is not null)
            {
                PathologyRequestFromDB.CreatedDate = DateTime.SpecifyKind(PathologyRequestFromDB.CreatedDate, DateTimeKind.Utc);
                PathologyRequestFromDB.ModifiedDate = (PathologyRequestFromDB.ModifiedDate == null) ? null : DateTime.SpecifyKind((DateTime)PathologyRequestFromDB.ModifiedDate, DateTimeKind.Utc);

                apiResposne.Result = PathologyRequestFromDB;
                apiResposne.StatusCode = StatusCodes.Status200OK;
                apiResposne.Message = "Success";
                return apiResposne;
            }
            else
            {
                apiResposne.Result = null;
                apiResposne.StatusCode = StatusCodes.Status500InternalServerError;
                apiResposne.Message = "Data for given PathologyRequestID is not found";
                return apiResposne;
            }
        }

        /// <summary>
        /// GetPathologyRequestDataByCommunicationNotesId
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="patientId"></param>
        /// <param name="communicationNotesId"></param>
        /// <returns></returns>
        public async Task<ApiResponse<PathologyRequestView>> GetPathologyRequestDataByCommunicationNotesId(BaseHttpRequestContext baseHttpRequestContext, long patientId, long communicationNotesId)
        {
            ApiResponse<PathologyRequestView> apiResposne = new();
            var orgId = baseHttpRequestContext.OrgId;
            var PathologyRequestFromDB = await _pathologyRequestDAL.GetPathologyRequestDataByCommunicationNotesId(orgId, communicationNotesId);
            if (PathologyRequestFromDB is null)
            {
                apiResposne.Result = null;
                apiResposne.StatusCode = StatusCodes.Status400BadRequest;
                apiResposne.Message = "Failure";
                apiResposne.Errors.Add("The PathologyRequestdoesnot exist.");
                return apiResposne;
            }
            if (PathologyRequestFromDB is not null)
            {
                PathologyRequestFromDB.CreatedDate = DateTime.SpecifyKind(PathologyRequestFromDB.CreatedDate, DateTimeKind.Utc);
                PathologyRequestFromDB.ModifiedDate = (PathologyRequestFromDB.ModifiedDate == null) ? null : DateTime.SpecifyKind((DateTime)PathologyRequestFromDB.ModifiedDate, DateTimeKind.Utc);

                apiResposne.Result = PathologyRequestFromDB;
                apiResposne.StatusCode = StatusCodes.Status200OK;
                apiResposne.Message = "Success";
                return apiResposne;
            }
            else
            {
                apiResposne.Result = null;
                apiResposne.StatusCode = StatusCodes.Status500InternalServerError;
                apiResposne.Message = "Data for given PathologyRequestID is not found";
                return apiResposne;
            }
        }

        /// <summary>
        /// Edit Appointment Type
        /// </summary>
        /// <param name="id"></param>
        /// <param name="inputPathologyRequest"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> EditPathologyRequestData(BaseHttpRequestContext baseHttpRequestContext, long patientId, long pathologyRequestId, PathologyRequest inputPathologyRequest)
        {
            ApiResponse<long?> apiResponse = new();
            var orgId = baseHttpRequestContext.OrgId;
            var userId = baseHttpRequestContext.UserId;
            var PathologyRequest = await _pathologyRequestDAL.GetPathologyRequestDataSyncById(orgId, pathologyRequestId);

            if (inputPathologyRequest != null && inputPathologyRequest.PathologyRequestDoctorsAssocs.Any())
            {
                inputPathologyRequest.PathologyRequestDoctorsAssocs.ToList().ForEach(a => { a.OrgId = orgId; a.PathologyRequestId = pathologyRequestId; });
            }

            inputPathologyRequest.Id = pathologyRequestId;
            inputPathologyRequest.PatientDetailsId = patientId;
            inputPathologyRequest.OrgId = orgId;
            inputPathologyRequest.ModifiedDate = DateTime.UtcNow;
            inputPathologyRequest.ModifiedBy = userId;
            inputPathologyRequest.CreatedBy = PathologyRequest.CreatedBy;
            inputPathologyRequest.CreatedDate = PathologyRequest.CreatedDate;
            inputPathologyRequest.StatusId = PathologyRequest.StatusId;
            
            //inputPathologyRequest.RequestDate = PathologyRequest.RequestDate; : Commnted for CST : 4268

            inputPathologyRequest.PathologyRequestDoctorsAssocs = await EditPathologyRequestDoctorsAssocs(inputPathologyRequest.PathologyRequestDoctorsAssocs, pathologyRequestId, orgId, userId);

            await _pathologyRequestDAL.UpdatePathologyRequestData(inputPathologyRequest);

            if (PathologyRequest.RequestingDoctor != null && inputPathologyRequest.RequestingDoctor != null && PathologyRequest.RequestingDoctor != inputPathologyRequest.RequestingDoctor)
            {
                await UpdatePatientActions(pathologyRequestId, PathologyRequest.RequestingDoctor, inputPathologyRequest.RequestingDoctor, baseHttpRequestContext);
            }

            apiResponse.Result = pathologyRequestId;
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            return apiResponse;
        }


        /// <summary>
        /// Delete Appointment Type
        /// </summary>
        /// <param name="id"></param>
        /// <param name="inputPathologyRequest"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> DeletePathologyRequestData(BaseHttpRequestContext baseHttpRequestContext, long patientId, long pathologyRequestId)
        {
            ApiResponse<long?> apiResponse = new();
            var orgId = baseHttpRequestContext.OrgId;
            var userId = baseHttpRequestContext.UserId;

            PathologyRequest PathologyRequestFromDB = await _pathologyRequestDAL.GetPathologyRequestDataSyncById(orgId, pathologyRequestId);

            //PathologyRequestDoctorsAssocs : To delete the list of all the doctor's assocation, we are passing empty list of doctors. Rest will be taken care by EditPathologyRequestDoctorsAssocs
            PathologyRequestFromDB.PathologyRequestDoctorsAssocs = await EditPathologyRequestDoctorsAssocs(new List<PathologyRequestDoctorsAssocs>(), pathologyRequestId, orgId, userId);

            //PathologyRequestMediaAssocs : To delete the list of all the Media assocation, we will first fetch the list of all associated media, then by looping through it, we will delete them one by one
            var ListPathologyRequestMedia = await _pathologyRequestDAL.GetPathologyRequestMedia(orgId, pathologyRequestId);
            foreach (var mediaObj in ListPathologyRequestMedia)
            {
                var media = await _pathologyRequestDAL.GetPathologyRequestMediaSyncById(orgId, mediaObj.Id);
                media.StatusId = (short)Status.Deleted;
                media.ModifiedDate = DateTime.UtcNow;
                media.ModifiedBy = userId;
                await _pathologyRequestDAL.DeletePathologyRequestMediaAsync(media); 
            }

            //Code to delete associated patient actions will come here
            bool isDeleted = await DeletePatientActions(pathologyRequestId, baseHttpRequestContext);

            //Pathology Request : To change the status of Pathology Request to Deleted
            PathologyRequestFromDB.StatusId = (short)Status.Deleted;
            PathologyRequestFromDB.ModifiedDate = DateTime.UtcNow;
            PathologyRequestFromDB.ModifiedBy = userId;
            await _pathologyRequestDAL.DeletePathologyRequestData(PathologyRequestFromDB);

            apiResponse.Result = pathologyRequestId;
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            return apiResponse;
        }

        public async Task<bool> DeletePatientActions(long pathologyRequestId, BaseHttpRequestContext baseHttpRequestContext)
        {
            bool isDeletionSucceeded = false;

            var token = baseHttpRequestContext.BearerToken;
            string interServiceToken = baseHttpRequestContext.InterServiceToken;

            string deletePatientActionsAPiUrl = _appSettings.ApiUrls["UserServiceUrl"] + "/user/patient_actions_by_pathology_id?id=" + pathologyRequestId;
            RestClient deleteActionsRestClient = new RestClient(deletePatientActionsAPiUrl, null, token, interServiceToken);
            var deleted = await deleteActionsRestClient.DeleteAsync<ApiResponse<string>>(deletePatientActionsAPiUrl, null);

            if (deleted.StatusCode == StatusCodes.Status200OK)
                isDeletionSucceeded = true;

            return isDeletionSucceeded;
        }

        public async Task<bool> UpdatePatientActions(long pathologyRequestId, long? oldAssigneeID, long? newAssigneeID, BaseHttpRequestContext baseHttpRequestContext)
        {
            bool isUpdateSucceeded = false;

            var token = baseHttpRequestContext.BearerToken;
            string interServiceToken = baseHttpRequestContext.InterServiceToken;

            string updatePatientActionsAPiUrl = $"{_appSettings.ApiUrls["UserServiceUrl"]}/user/patient_actions_by_pathology_id?id={pathologyRequestId}&oaid={oldAssigneeID}&naid={newAssigneeID}";
            RestClient listActionsRestClient = new RestClient(updatePatientActionsAPiUrl, null, token, interServiceToken);
            var listPatientActionsResponse = await listActionsRestClient.PutAsync<ApiResponse<string>>(updatePatientActionsAPiUrl, null);

            if (listPatientActionsResponse.StatusCode == StatusCodes.Status200OK)
                isUpdateSucceeded = true;

            return isUpdateSucceeded;
        }


        private async Task<List<PathologyRequestDoctorsAssocs>> EditPathologyRequestDoctorsAssocs(ICollection<PathologyRequestDoctorsAssocs> inputUserList, long id, int orgId, long userId)
        {
            List<PathologyRequestDoctorsAssocs> addUserList = new();

            var removeUserList = await _pathologyRequestDAL.GetPathologyRequestDoctorsAssocs(orgId, id);

            foreach (var userAssoc in inputUserList)
            {
                if (userAssoc.Id > 0)
                {
                    var existingobj = removeUserList.FirstOrDefault(x => x.UserCompanyAssocsId == userAssoc.UserCompanyAssocsId);
                    removeUserList.Remove(existingobj);
                }
                else
                {
                    var userAssocDB = removeUserList.Where(x => x.UserCompanyAssocsId == userAssoc.UserCompanyAssocsId).FirstOrDefault();
                    if (userAssocDB is null)
                    {
                        userAssoc.CreatedDate = DateTime.UtcNow;
                        userAssoc.StatusId = (short)Status.Active;
                        addUserList.Add(userAssoc);
                    }
                    else
                    {
                        if (userAssocDB.StatusId == (short)Status.Deleted)
                        {
                            userAssocDB.StatusId = (short)Status.Active;
                            userAssocDB.ModifiedDate = DateTime.UtcNow;
                            userAssocDB.ModifiedBy = userId;
                            addUserList.Add(userAssocDB);
                        }
                        removeUserList.Remove(userAssocDB);
                    }
                }
            }

            foreach (var userAssoc in removeUserList.Where(x => x.StatusId == (short)Status.Active))
            {
                userAssoc.StatusId = (short)Status.Deleted;
                userAssoc.ModifiedDate = DateTime.UtcNow;
                userAssoc.ModifiedBy = userId;
            }

            addUserList = addUserList.Concat(removeUserList).ToList();
            return addUserList;
        }


        //** MEDIA  **//

        /// <summary>
        /// Add media files
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="patientId"></param>
        /// <param name="pathologyRequestId"></param>
        /// <param name="pathologyRequestData"></param>
        /// <returns></returns>
        public async Task<ApiResponse<List<PathologyRequestMediaAssocsIds>>> AddPathologyRequestMedia(BaseHttpRequestContext baseHttpRequestContext, long pathologyRequestId, List<PathologyRequestMediaAssocs> pathologyRequestData)
        {
            var orgId = baseHttpRequestContext.OrgId;
            var loggedInUser = baseHttpRequestContext.UserId;

            if (pathologyRequestData?.Any() ?? false)
            {
                pathologyRequestData.ForEach(a => { a.PathologyRequestId = pathologyRequestId; a.OrgId = orgId; a.CreatedBy = loggedInUser; a.StatusId = (short)Status.Active; });
            }

            var id = await _pathologyRequestDAL.AddPathologyRequestMediaAsync(pathologyRequestData);
            if (id > 0)
            {
                var response = await _pathologyRequestDAL.GetPathologyRequestMediaByPathologyId(orgId, pathologyRequestId);
                ApiResponse<List<PathologyRequestMediaAssocsIds>> apiResponse = new()
                {
                    StatusCode = StatusCodes.Status200OK,
                    Message = "Success",
                    Result = response
                };
                return apiResponse;
            }
            else
            {
                ApiResponse<List<PathologyRequestMediaAssocsIds>> apiResponse = new()
                {
                    StatusCode = StatusCodes.Status400BadRequest,
                    Message = "Failure",
                    Result = null
                };
                return apiResponse;
            }
        }

        /// <summary>
        /// Fetch all files
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="patientId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<List<PathologyRequestMediaAssocsView>>> GetPathologyRequestMedia(BaseHttpRequestContext baseHttpRequestContext, long pathologyRequestId)
        {
            ApiResponse<List<PathologyRequestMediaAssocsView>> apiResposne = new();
            var orgId = baseHttpRequestContext.OrgId;
            var PathologyRequestFromDB = await _pathologyRequestDAL.GetPathologyRequestMedia(orgId, pathologyRequestId);
            apiResposne.Result = PathologyRequestFromDB;
            apiResposne.StatusCode = StatusCodes.Status200OK;
            apiResposne.Message = "Success";
            return apiResposne;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="patientId"></param>
        /// <param name="pathologyRequestId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> DeletePathologyRequestMedia(BaseHttpRequestContext baseHttpRequestContext, long pathologyRequestId, long mediaId)
        {
            ApiResponse<long?> apiResponse = new();
            var orgId = baseHttpRequestContext.OrgId;
            var loggedInUser = baseHttpRequestContext.UserId;
            var media = await _pathologyRequestDAL.GetPathologyRequestMediaSyncById(orgId, mediaId);
            media.StatusId = (short)Status.Deleted;
            media.ModifiedDate = DateTime.UtcNow;
            media.ModifiedBy = loggedInUser;
            await _pathologyRequestDAL.DeletePathologyRequestMediaAsync(media);
            apiResponse.Result = mediaId;
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            return apiResponse;
        }

        //** PATHOLOGY RESULTS REVIEW  **//

        /// <summary>
        /// Add Pathology Response
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="resultMediaId"></param>
        /// <param name="PathologyResponseReview"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> AddPathologyResponseData(BaseHttpRequestContext baseHttpRequestContext, long pathologyResultMediaId, PathologyResponseReview PathologyResponseReview)
        {
            var orgId = baseHttpRequestContext.OrgId;
            var loggedInUser = baseHttpRequestContext.UserId;

            PathologyResponseReview.PathologyRequestMediaId = pathologyResultMediaId;
            PathologyResponseReview.OrgId = orgId;
            PathologyResponseReview.CreatedDate = DateTime.UtcNow;
            PathologyResponseReview.CreatedBy = loggedInUser;
            PathologyResponseReview.StatusId = (short)Status.Active;

            var id = await _pathologyRequestDAL.AddPathologyResponseData(PathologyResponseReview);
            if (id > 0)
            {
                ApiResponse<long?> apiResponse = new()
                {
                    StatusCode = StatusCodes.Status200OK,
                    Message = "Success",
                    Result = id
                };
                return apiResponse;
            }
            else
            {
                ApiResponse<long?> apiResponse = new()
                {
                    StatusCode = StatusCodes.Status400BadRequest,
                    Message = "Failure",
                    Result = null
                };
                return apiResponse;
            }
        }

        /// <summary>
        /// Get Pathology response data
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="resultMediaId"></param>
        /// <returns></returns>
        public async Task<ApiResponse<PathologyResponseReview>> GetPathologyResponseData(BaseHttpRequestContext baseHttpRequestContext, long pathologyResultMediaId)
        {
            ApiResponse<PathologyResponseReview> apiResposne = new();
            var orgId = baseHttpRequestContext.OrgId;
            var dbResponse = await _pathologyRequestDAL.GetPathologyResponseData(orgId, pathologyResultMediaId);
            apiResposne.Result = dbResponse;
            apiResposne.StatusCode = StatusCodes.Status200OK;
            apiResposne.Message = "Success";
            return apiResposne;
        }

        /// <summary>
        /// edit Pathology response
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="resultMediaId"></param>
        /// <param name="PathologyResponseReview"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> EditPathologyReponseData(BaseHttpRequestContext baseHttpRequestContext, long pathologyResultMediaId, PathologyResponseReview PathologyResponseReview)
        {
            ApiResponse<long?> apiResponse = new();
            var orgId = baseHttpRequestContext.OrgId;
            var userId = baseHttpRequestContext.UserId;
            var dbResponse = await _pathologyRequestDAL.GetPathologyResponseDataSyncById(orgId, pathologyResultMediaId);

            PathologyResponseReview.Id = dbResponse.Id;
            PathologyResponseReview.PathologyRequestMediaId = pathologyResultMediaId;
            PathologyResponseReview.OrgId = orgId;
            PathologyResponseReview.ModifiedDate = DateTime.UtcNow;
            PathologyResponseReview.ModifiedBy = userId;
            PathologyResponseReview.CreatedBy = dbResponse.CreatedBy;
            PathologyResponseReview.CreatedDate = dbResponse.CreatedDate;

            await _pathologyRequestDAL.UpdatePathologyResponseData(PathologyResponseReview);

            apiResponse.Result = pathologyResultMediaId;
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            return apiResponse;
        }

        //** PATIENT ACTIONS - PATHOLOGY DETAILS **//

        /// <summary>
        /// GetPatientActionsPathologyDetailsById
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="pathologyRequestId"></param>
        /// <returns></returns>
        public async Task<ApiResponse<PatientActionsPathologyDetails>> GetPatientActionsPathologyDetailsById(BaseHttpRequestContext baseHttpRequestContext, long pathologyRequestId)
        {
            ApiResponse<PatientActionsPathologyDetails> apiResposne = new();
            var orgId = baseHttpRequestContext.OrgId;
            var PathologyRequestFromDB = await _pathologyRequestDAL.GetPatientActionsPathologyDetailsById(orgId, pathologyRequestId);
            apiResposne.Result = PathologyRequestFromDB;
            apiResposne.StatusCode = StatusCodes.Status200OK;
            apiResposne.Message = "Success";
            return apiResposne;
        }

        /// <summary>
        /// GetPatientActionsPathologyDetailsByMediaId
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="pathologyResponseMediaId"></param>
        /// <returns></returns>
        public async Task<ApiResponse<PatientActionsPathologyDetails>> GetPatientActionsPathologyDetailsByMediaId(BaseHttpRequestContext baseHttpRequestContext, long pathologyResponseMediaId)
        {
            ApiResponse<PatientActionsPathologyDetails> apiResposne = new();
            var orgId = baseHttpRequestContext.OrgId;
            var PathologyRequestFromDB = await _pathologyRequestDAL.GetPatientActionsPathologyDetailsByMediaId(orgId, pathologyResponseMediaId);
            apiResposne.Result = PathologyRequestFromDB;
            apiResposne.StatusCode = StatusCodes.Status200OK;
            apiResposne.Message = "Success";
            return apiResposne;
        }
    }
}
