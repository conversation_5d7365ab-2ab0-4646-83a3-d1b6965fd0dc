﻿using Capstone2.RestServices.Patient.Context;
using Capstone2.RestServices.Patient.Interfaces;
using Capstone2.RestServices.Patient.Models;
using Capstone2.Shared.Models.Enum;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Security;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Patient.Services
{
    public class PathologyRequestDAL : IPathologyRequestDAL
    {
        public readonly ReadOnlyPatientDBContext _readOnlyDbContext;
        public readonly UpdatablePatientDBContext _updatableDBContext;
        public PathologyRequestDAL(ReadOnlyPatientDBContext readOnlyDbContext, UpdatablePatientDBContext updatableDBContext)
        {
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
        }

        /// <summary>
        /// Method to add PathologyRequestData to PathologyRequestData table
        /// </summary>
        /// <param name="PathologyRequestData"></param>
        /// <returns></returns>
        public async Task<long> AddPathologyRequestDataAsync(PathologyRequest pathologyRequestData)
        {
            await _updatableDBContext.PathologyRequest.AddAsync(pathologyRequestData);
            int rows = await _updatableDBContext.SaveChangesAsync();
            return rows > 0 ? pathologyRequestData.Id : 0;
        }

        /// <summary>
        /// Method to PathologyRequestData based on id
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<PathologyRequestView> GetPathologyRequestData(int orgId, long pathologyRequestId)
        {
            var PathologyRequestData = await (from CN in _readOnlyDbContext.PathologyRequest
                                                .Where(x => x.Id == pathologyRequestId && x.OrgId == orgId)
                                                 select new PathologyRequestView
                                                 {
                                                     Id = CN.Id,
                                                     OrgId = CN.OrgId,
                                                     PatientDetailsId = CN.PatientDetailsId,
                                                     CommunicationNotesId = CN.CommunicationNotesId,
                                                     AppointmentDetailsId = CN.AppointmentDetailsId,
                                                     EpisodesId = CN.EpisodesId,
                                                     RequestDate = CN.RequestDate,
                                                     ReferenceNumber = CN.ReferenceNumber,
                                                     TestsRequested = CN.TestsRequested,
                                                     ClinicalNotes = CN.ClinicalNotes,
                                                     CopyReportsTo = CN.CopyReportsTo,
                                                     RequestingDoctor = CN.RequestingDoctor,
                                                     Provier = CN.Provier,
                                                     IsUrgent = CN.IsUrgent,
                                                     IsPhone = CN.IsPhone,
                                                     IsFax = CN.IsFax,
                                                     ByTime = CN.ByTime,
                                                     PhoneNo = CN.PhoneNo,
                                                     FaxNo = CN.FaxNo,
                                                     IsStandard = CN.IsStandard,
                                                     IsSchedule = CN.IsSchedule,
                                                     IsDirectBill = CN.IsDirectBill,
                                                     VetaffairsNo = CN.VetaffairsNo,
                                                     OtherNo = CN.OtherNo,
                                                     IsFasting = CN.IsFasting,
                                                     IsNonFasting = CN.IsNonFasting,
                                                     IsPregnant = CN.IsPregnant,
                                                     IsHormTherapy = CN.IsHormTherapy,
                                                     IsLNMP = CN.IsLNMP,
                                                     IsEDC = CN.IsEDC,
                                                     IsCervix = CN.IsCervix,
                                                     IsVagina = CN.IsVagina,
                                                     IsSelfCollect = CN.IsSelfCollect,
                                                     IsClinicalPregnant = CN.IsClinicalPregnant,
                                                     IsPostnatal = CN.IsPostnatal,
                                                     IsPostMenopausal = CN.IsPostMenopausal,
                                                     IsHysterectomy = CN.IsHysterectomy,
                                                     IsHRTOCP = CN.IsHRTOCP,
                                                     IsIUD = CN.IsIUD,
                                                     IsPostMenopausalBleeding = CN.IsPostMenopausalBleeding,
                                                     IsPostCoitalBleeding = CN.IsPostCoitalBleeding,
                                                     IsPostCoitalBleedingRecurrent = CN.IsPostCoitalBleedingRecurrent,
                                                     IsUnexplainedBleeding = CN.IsUnexplainedBleeding,
                                                     IsSuspeciousCervix = CN.IsSuspeciousCervix,
                                                     IsMeetingspecificCriteria = CN.IsMeetingspecificCriteria,
                                                     IsImmuneDeficient = CN.IsImmuneDeficient,
                                                     IsDESExposed = CN.IsDESExposed,
                                                     IsPreviousAUS = CN.IsPreviousAUS,
                                                     DoctorSignature = CN.DoctorSignature,
                                                     SignedDate = CN.SignedDate,
                                                     PatientSignature = CN.PatientSignature,
                                                     ReasonForPatientNotSign = CN.ReasonForPatientNotSign,
                                                     IsPrivatePatientPrivateHospital = CN.IsPrivatePatientPrivateHospital,
                                                     IsPrivatePatientRecognisedHospital = CN.IsPrivatePatientRecognisedHospital,
                                                     IsPublicPatientRecognisedHospital = CN.IsPublicPatientRecognisedHospital,
                                                     IsOuPatientRecognisedHospital = CN.IsOuPatientRecognisedHospital,
                                                     StatusId = CN.StatusId,
                                                     CreatedBy = CN.CreatedBy,
                                                     CreatedDate = CN.CreatedDate,
                                                     ModifiedBy = CN.ModifiedBy,
                                                     ModifiedDate = CN.ModifiedDate,

                                                     PathologyRequestDoctorsAssocs = (ICollection<PathologyRequestDoctorsAssocsView>)(
                                                             from DA in _readOnlyDbContext.PathologyRequestDoctorsAssocs.Where(x => x.PathologyRequestId == CN.Id && x.OrgId == orgId && x.StatusId == (short)Status.Active)
                                                             from UCA in _readOnlyDbContext.UserCompanyAssocs
                                                            .Where(uc => uc.OrgId == orgId && (DA.UserCompanyAssocsId != null ? uc.Id == DA.UserCompanyAssocsId : false))
                                                            .DefaultIfEmpty()
                                                             from UD in _readOnlyDbContext.UserDetails
                                                                  .Where(u => u.OrgId == orgId && (UCA != null ? UCA.UserDetailsId : DA.UserDetailsId) == u.Id)
                                                                  .DefaultIfEmpty()
                                                             from CD in _readOnlyDbContext.CompanyDetails
                                                                  .Where(c => c.OrgId == orgId && (UCA != null ? UCA.CompanyId : null) == c.CompanyId)
                                                                  .DefaultIfEmpty()
                                                             select new PathologyRequestDoctorsAssocsView
                                                             {
                                                                 Id = DA.Id,
                                                                 OrgId = DA.OrgId,
                                                                 PathologyRequestId = DA.PathologyRequestId,
                                                                 UserDetailsId = DA.UserDetailsId,
                                                                 UserCompanyAssocsId = DA.UserCompanyAssocsId,
                                                                 PathologyRequestDoctorDetails = new PathologyRequestDoctorDetails
                                                                 {
                                                                     Id = UD != null ? UD.Id : 0, // Use a default value or handle null as needed
                                                                     FirstName = UD != null ? UD.FirstName : string.Empty,
                                                                     SurName = UD != null ? UD.SurName : string.Empty,
                                                                     CompanyId = CD != null ? CD.CompanyId : 0, // Use a default value or handle null as needed
                                                                     CompanyName = CD != null ? CD.Name : string.Empty
                                                                 }
                                                             }),
                                                 }).FirstOrDefaultAsync();
            return PathologyRequestData;
        }

        /// <summary>
        /// Get pathology data by Communication Notes Id
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="communicationNotesId"></param>
        /// <returns></returns>
        public async Task<PathologyRequestView> GetPathologyRequestDataByCommunicationNotesId(int orgId, long communicationNotesId)
        {
            var PathologyRequestData = await (from CN in _readOnlyDbContext.PathologyRequest
                                                .Where(x => x.CommunicationNotesId == communicationNotesId && x.OrgId == orgId)
                                              select new PathologyRequestView
                                              {
                                                  Id = CN.Id,
                                                  OrgId = CN.OrgId,
                                                  PatientDetailsId = CN.PatientDetailsId,
                                                  CommunicationNotesId = CN.CommunicationNotesId,
                                                  AppointmentDetailsId = CN.AppointmentDetailsId,
                                                  EpisodesId = CN.EpisodesId,
                                                  RequestDate = CN.RequestDate,
                                                  ReferenceNumber = CN.ReferenceNumber,
                                                  TestsRequested = CN.TestsRequested,
                                                  ClinicalNotes = CN.ClinicalNotes,
                                                  CopyReportsTo = CN.CopyReportsTo,
                                                  RequestingDoctor = CN.RequestingDoctor,
                                                  Provier = CN.Provier,
                                                  IsUrgent = CN.IsUrgent,
                                                  IsPhone = CN.IsPhone,
                                                  IsFax = CN.IsFax,
                                                  ByTime = CN.ByTime,
                                                  PhoneNo = CN.PhoneNo,
                                                  FaxNo = CN.FaxNo,
                                                  IsStandard = CN.IsStandard,
                                                  IsSchedule = CN.IsSchedule,
                                                  IsDirectBill = CN.IsDirectBill,
                                                  VetaffairsNo = CN.VetaffairsNo,
                                                  OtherNo = CN.OtherNo,
                                                  IsFasting = CN.IsFasting,
                                                  IsNonFasting = CN.IsNonFasting,
                                                  IsPregnant = CN.IsPregnant,
                                                  IsHormTherapy = CN.IsHormTherapy,
                                                  IsLNMP = CN.IsLNMP,
                                                  IsEDC = CN.IsEDC,
                                                  IsCervix = CN.IsCervix,
                                                  IsVagina = CN.IsVagina,
                                                  IsSelfCollect = CN.IsSelfCollect,
                                                  IsClinicalPregnant = CN.IsClinicalPregnant,
                                                  IsPostnatal = CN.IsPostnatal,
                                                  IsPostMenopausal = CN.IsPostMenopausal,
                                                  IsHysterectomy = CN.IsHysterectomy,
                                                  IsHRTOCP = CN.IsHRTOCP,
                                                  IsIUD = CN.IsIUD,
                                                  IsPostMenopausalBleeding = CN.IsPostMenopausalBleeding,
                                                  IsPostCoitalBleeding = CN.IsPostCoitalBleeding,
                                                  IsPostCoitalBleedingRecurrent = CN.IsPostCoitalBleedingRecurrent,
                                                  IsUnexplainedBleeding = CN.IsUnexplainedBleeding,
                                                  IsSuspeciousCervix = CN.IsSuspeciousCervix,
                                                  IsMeetingspecificCriteria = CN.IsMeetingspecificCriteria,
                                                  IsImmuneDeficient = CN.IsImmuneDeficient,
                                                  IsDESExposed = CN.IsDESExposed,
                                                  IsPreviousAUS = CN.IsPreviousAUS,
                                                  DoctorSignature = CN.DoctorSignature,
                                                  SignedDate = CN.SignedDate,
                                                  PatientSignature = CN.PatientSignature,
                                                  ReasonForPatientNotSign = CN.ReasonForPatientNotSign,
                                                  IsPrivatePatientPrivateHospital = CN.IsPrivatePatientPrivateHospital,
                                                  IsPrivatePatientRecognisedHospital = CN.IsPrivatePatientRecognisedHospital,
                                                  IsPublicPatientRecognisedHospital = CN.IsPublicPatientRecognisedHospital,
                                                  IsOuPatientRecognisedHospital = CN.IsOuPatientRecognisedHospital,
                                                  StatusId = CN.StatusId,
                                                  CreatedBy = CN.CreatedBy,
                                                  CreatedDate = CN.CreatedDate,
                                                  ModifiedBy = CN.ModifiedBy,
                                                  ModifiedDate = CN.ModifiedDate,

                                                  PathologyRequestDoctorsAssocs = (ICollection<PathologyRequestDoctorsAssocsView>)(
                                                        from DA in _readOnlyDbContext.PathologyRequestDoctorsAssocs.Where(x => x.PathologyRequestId == CN.Id && x.OrgId == orgId && x.StatusId == (short)Status.Active)
                                                        from UCA in _readOnlyDbContext.UserCompanyAssocs
                                                             .Where(uc => uc.OrgId == orgId && (DA.UserCompanyAssocsId != null ? uc.Id == DA.UserCompanyAssocsId : false))
                                                             .DefaultIfEmpty()
                                                        from UD in _readOnlyDbContext.UserDetails
                                                             .Where(u => u.OrgId == orgId && (UCA != null ? UCA.UserDetailsId : DA.UserDetailsId) == u.Id)
                                                             .DefaultIfEmpty()
                                                        from CD in _readOnlyDbContext.CompanyDetails
                                                             .Where(c => c.OrgId == orgId && (UCA != null ? UCA.CompanyId : null) == c.CompanyId)
                                                             .DefaultIfEmpty()
                                                        select new PathologyRequestDoctorsAssocsView
                                                        {
                                                            Id = DA.Id,
                                                            OrgId = DA.OrgId,
                                                            PathologyRequestId = DA.PathologyRequestId,
                                                            UserDetailsId = DA.UserDetailsId,
                                                            UserCompanyAssocsId = DA.UserCompanyAssocsId,
                                                            PathologyRequestDoctorDetails = new PathologyRequestDoctorDetails
                                                            {
                                                                Id = UD != null ? UD.Id : 0, // Use a default value or handle null as needed
                                                                FirstName = UD != null ? UD.FirstName : string.Empty,
                                                                SurName = UD != null ? UD.SurName : string.Empty,
                                                                CompanyId = CD != null ? CD.CompanyId : 0, // Use a default value or handle null as needed
                                                                CompanyName = CD != null ? CD.Name : string.Empty
                                                            }
                                                        }),
                                              }).FirstOrDefaultAsync();

            return PathologyRequestData;
        }

        /// <summary>
        /// Method to PathologyRequest based on id
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<PathologyRequest> GetPathologyRequestDataSyncById(int orgId, long id)
        {
            return await _readOnlyDbContext.PathologyRequest.Where(s => s.Id == id && s.OrgId == orgId).AsNoTracking().FirstOrDefaultAsync();
        }

        /// <summary>
        /// Fetch AppointmentTypesAnaesthetistAssocs
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="AppointmentTypesId"></param>
        /// <returns></returns>
        public async Task<List<PathologyRequestDoctorsAssocs>> GetPathologyRequestDoctorsAssocs(int orgId, long pathologyRequestId)
        {
            return await _readOnlyDbContext.PathologyRequestDoctorsAssocs.Where(s => s.PathologyRequestId == pathologyRequestId && s.OrgId == orgId).AsNoTracking().ToListAsync();
        }

        /// <summary>
        /// Get PathologyRequestMediaAssocs
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="pathologyRequestId"></param>
        /// <returns></returns>
        public async Task<List<PathologyRequestMediaAssocs>> GetPathologyRequestMediaAssocs(int orgId, long pathologyRequestId)
        {
            return await _readOnlyDbContext.PathologyRequestMediaAssocs.Where(s => s.PathologyRequestId == pathologyRequestId && s.OrgId == orgId).AsNoTracking().ToListAsync();
        }


        /// <summary>
        /// Update PathologyRequest
        /// </summary>
        /// <param name="PathologyRequest"></param>
        /// <returns></returns>
        public async Task<int> UpdatePathologyRequestData(PathologyRequest PathologyRequestData)
        {
            _updatableDBContext.PathologyRequest.Update(PathologyRequestData);
            return await _updatableDBContext.SaveChangesAsync();
        }

        /// <summary>
        /// Delete PathologyRequest
        /// </summary>
        /// <param name="PathologyRequest"></param>
        /// <returns></returns>
        public async Task<int> DeletePathologyRequestData(PathologyRequest PathologyRequestData)
        {
            _updatableDBContext.PathologyRequest.Update(PathologyRequestData);
            return await _updatableDBContext.SaveChangesAsync();
        }

        //** MEDIA  **//

        /// <summary>
        /// Add new files
        /// </summary>
        /// <param name="pathologyRequestData"></param>
        /// <returns></returns>
        public async Task<long> AddPathologyRequestMediaAsync(List<PathologyRequestMediaAssocs> pathologyRequestData)
        {
            await _updatableDBContext.PathologyRequestMediaAssocs.AddRangeAsync(pathologyRequestData);
            return await _updatableDBContext.SaveChangesAsync();
        }

        /// <summary>
        /// Fetch all media
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<List<PathologyRequestMediaAssocsView>> GetPathologyRequestMedia(int orgId, long pathologyRequestId)
        {
            var media = await (from CN in _readOnlyDbContext.PathologyRequestMediaAssocs.Where(x => x.PathologyRequestId == pathologyRequestId && x.OrgId == orgId && x.StatusId == (short)Status.Active)
                               from DA in _readOnlyDbContext.FileDetails.Where(y => y.Id == CN.FileDetailsId && y.OrgId == orgId)
                               select new PathologyRequestMediaAssocsView
                               {
                                    Id = CN.Id,
                                    OrgId = CN.OrgId,
                                    PathologyRequestId = CN.PathologyRequestId,
                                    FileDetailsId = CN.FileDetailsId,
                                    IsRequestForm = CN.IsRequestForm,                                                  
                                    StatusId = CN.StatusId,
                                    CreatedBy = CN.CreatedBy,
                                    CreatedDate = CN.CreatedDate,
                                    ModifiedBy = CN.ModifiedBy,
                                    ModifiedDate = CN.ModifiedDate,
                                    MediaName = DA.CustomFileName,
                                    MediaType = DA.MimeType
                               }).ToListAsync();
            return media;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="pathologyRequestId"></param>
        /// <returns></returns>
        public async Task<PathologyRequestMediaAssocs> GetPathologyRequestMediaSyncById(int orgId, long id)
        {
            return await _readOnlyDbContext.PathologyRequestMediaAssocs.Where(s => s.Id == id && s.OrgId == orgId).AsNoTracking().FirstOrDefaultAsync();
        }

        /// <summary>
        /// GetPathologyRequestMediaByPathologyId
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="pathologyRequestId"></param>
        /// <returns></returns>
        public async Task<List<PathologyRequestMediaAssocsIds>> GetPathologyRequestMediaByPathologyId(int orgId, long pathologyRequestId)
        {
            var media = await (from CN in _readOnlyDbContext.PathologyRequestMediaAssocs.Where(x => x.PathologyRequestId == pathologyRequestId && x.OrgId == orgId && x.StatusId == (short)Status.Active)
                               select new PathologyRequestMediaAssocsIds
                               {
                                   FileDetailsId = CN.FileDetailsId,
                                   PathologyMediaId = CN.Id
                               }).ToListAsync();
            return media;
        }

        /// <summary>
        /// Delete media
        /// </summary>
        /// <param name="pathologyRequestData"></param>
        /// <returns></returns>
        public async Task<long> DeletePathologyRequestMediaAsync(PathologyRequestMediaAssocs pathologyRequestData)
        {
            _updatableDBContext.PathologyRequestMediaAssocs.Update(pathologyRequestData);
            return await _updatableDBContext.SaveChangesAsync();
        }

        //** PATHOLOGY RESULTS REVIEW  **//

        /// <summary>
        /// Add Pathology response
        /// </summary>
        /// <param name="PathologyResponseReview"></param>
        /// <returns></returns>
        public async Task<long> AddPathologyResponseData(PathologyResponseReview PathologyResponseReview)
        {
            await _updatableDBContext.PathologyResponseReview.AddAsync(PathologyResponseReview);
            int rows = await _updatableDBContext.SaveChangesAsync();
            return rows > 0 ? PathologyResponseReview.Id : 0;
        }

        /// <summary>
        /// Get Pathology result
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="resultMediaId"></param>
        /// <returns></returns>
        public async Task<PathologyResponseReview> GetPathologyResponseData(int orgId, long pathologyResultMediaId)
        {
            var media = await (from CN in _readOnlyDbContext.PathologyResponseReview.Where(x => x.PathologyRequestMediaId == pathologyResultMediaId && x.OrgId == orgId)
                               select new PathologyResponseReview
                               {
                                   Id = CN.Id,
                                   OrgId = CN.OrgId,
                                   PathologyRequestMediaId = CN.PathologyRequestMediaId,
                                   IsExpected = CN.IsExpected,
                                   IsNormal = CN.IsNormal,
                                   IsAbnormal = CN.IsAbnormal,
                                   IsIncompleteExcision = CN.IsIncompleteExcision,
                                   IsReferralRequired = CN.IsReferralRequired,
                                   IsOther = CN.IsOther,
                                   IsFullyExcised = CN.IsFullyExcised,
                                   IsDrAdvisedPatient = CN.IsDrAdvisedPatient,
                                   DoctorReviewNotes = CN.DoctorReviewNotes,

                                   IsNoAction = CN.IsNoAction,
                                   IsNoActionCompleted = CN.IsNoActionCompleted,
                                   NoActionUserId = CN.NoActionUserId,

                                   IsCallPatient = CN.IsCallPatient,
                                   IsCallPatientCompleted = CN.IsCallPatientCompleted,
                                   CallPatientUserId = CN.CallPatientUserId,

                                   IsBookNotUrgentAppointment = CN.IsBookNotUrgentAppointment,
                                   IsBookNotUrgentAppointmentCompleted = CN.IsBookNotUrgentAppointmentCompleted,
                                   BookNotUrgentAppointmentUserId = CN.BookNotUrgentAppointmentUserId,

                                   IsBookUrgentAppointment = CN.IsBookUrgentAppointment,
                                   IsBookUrgentAppointmentCompleted = CN.IsBookUrgentAppointmentCompleted,
                                   BookUrgentAppointmentUserId = CN.BookUrgentAppointmentUserId,

                                   IsBookSurgeryAppointment = CN.IsBookSurgeryAppointment,
                                   IsBookSurgeryAppointmentCompleted = CN.IsBookSurgeryAppointmentCompleted,
                                   BookSurgeryAppointmentUserId = CN.BookSurgeryAppointmentUserId,

                                   IsBookProcedureAppointment = CN.IsBookProcedureAppointment,
                                   IsBookProcedureAppointmentCompleted = CN.IsBookProcedureAppointmentCompleted,
                                   BookProcedureAppointmentUserId = CN.BookProcedureAppointmentUserId,

                                   IsReadyToInvoice = CN.IsReadyToInvoice,
                                   IsReadyToInvoiceCompleted = CN.IsReadyToInvoiceCompleted,
                                   ReadyToInvoiceUserId = CN.ReadyToInvoiceUserId,

                                   StatusId = CN.StatusId,
                                   CreatedBy = CN.CreatedBy,
                                   CreatedDate = CN.CreatedDate,
                                   ModifiedBy = CN.ModifiedBy,
                                   ModifiedDate = CN.ModifiedDate,
                               }).FirstOrDefaultAsync();
            return media;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="resultMediaId"></param>
        /// <returns></returns>
        public async Task<PathologyResponseReview> GetPathologyResponseDataSyncById(int orgId, long pathologyResultMediaId)
        {
            return await _readOnlyDbContext.PathologyResponseReview.Where(s => s.PathologyRequestMediaId == pathologyResultMediaId && s.OrgId == orgId).AsNoTracking().FirstOrDefaultAsync();
        }

        /// <summary>
        /// Update Pathology response
        /// </summary>
        /// <param name="PathologyResponseReview"></param>
        /// <returns></returns>
        public async Task<int> UpdatePathologyResponseData(PathologyResponseReview PathologyResponseReview)
        {
            _updatableDBContext.PathologyResponseReview.Update(PathologyResponseReview);
            return await _updatableDBContext.SaveChangesAsync();
        }

        //** PATIENT ACTIONS - PATHOLOGY DETAILS **//

        /// <summary>
        /// GetPatientActionsPathologyDetailsById
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="pathologyRequestId"></param>
        /// <returns></returns>
        public async Task<PatientActionsPathologyDetails> GetPatientActionsPathologyDetailsById(int orgId, long pathologyRequestId)
        {
            return await(from PR in _readOnlyDbContext.PathologyRequest.Where(x => x.Id == pathologyRequestId && x.OrgId == orgId)
                         from PD in _readOnlyDbContext.PatientDetails.Where(x => x.Id == PR.PatientDetailsId && x.OrgId == orgId)
                         select new PatientActionsPathologyDetails
                         {
                             PatientId = PR.PatientDetailsId,
                             FirstName = PD.FirstName,
                             SurName = PD.SurName,
                             PatienPhone = PD.Mobile,
                             PathologyRequestId = PR.Id,
                             AppointmentId = PR.AppointmentDetailsId,
                             IsUrgent = PR.IsUrgent,
                             TestsRequested = PR.TestsRequested
                         }).FirstOrDefaultAsync();
        }

        /// <summary>
        /// GetPatientActionsPathologyDetailsByMediaId
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="pathologyResponseMediaId"></param>
        /// <returns></returns>
        public async Task<PatientActionsPathologyDetails> GetPatientActionsPathologyDetailsByMediaId(int orgId, long pathologyResponseMediaId)
        {
            return await (from PM in _readOnlyDbContext.PathologyRequestMediaAssocs.Where(x => x.Id == pathologyResponseMediaId && x.OrgId == orgId)
                          from PR in _readOnlyDbContext.PathologyRequest.Where(x => x.Id == PM.PathologyRequestId && x.OrgId == orgId)
                          from PD in _readOnlyDbContext.PatientDetails.Where(x => x.Id == PR.PatientDetailsId && x.OrgId == orgId)
                          select new PatientActionsPathologyDetails
                          {
                              PatientId = PR.PatientDetailsId,
                              FirstName = PD.FirstName,
                              SurName = PD.SurName,
                              PatienPhone = PD.Mobile,
                              PathologyRequestId = PR.Id,
                              AppointmentId = PR.AppointmentDetailsId,
                              IsUrgent = PR.IsUrgent,
                              TestsRequested = PR.TestsRequested
                          }).FirstOrDefaultAsync();
        }
    }
}
