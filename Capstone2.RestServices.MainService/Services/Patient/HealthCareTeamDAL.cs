﻿using Capstone2.RestServices.Patient.Context;
using Capstone2.RestServices.Patient.Interfaces;
using Capstone2.RestServices.Patient.Models;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Patient.Services
{
    public class HealthCareTeamDAL : IHealthCareTeamDAL
    {
        public readonly ReadOnlyPatientDBContext _readOnlyDbContext;
        public readonly UpdatablePatientDBContext _updatableDBContext;
        public HealthCareTeamDAL(ReadOnlyPatientDBContext readOnlyDbContext, UpdatablePatientDBContext updatableDBContext)
        {
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
        }

        /// <summary>
        /// Method to add HealthCareTeamData to HealthCareTeamData table
        /// </summary>
        /// <param name="HealthCareTeamData"></param>
        /// <returns></returns>
        public async Task<long> AddHealthCareTeamDataAsync(List<HealthCareTeam> healthCareTeamData)
        {
            _updatableDBContext.HealthCareTeams.AddRange(healthCareTeamData);
            int rows = await _updatableDBContext.SaveChangesAsync();
            return rows;
        }

        public async Task<ICollection<InputHealthCareTeam>> FetchHealthCareTeamDetails(int orgId, long? patientId)
        {
            var lstHealthCareTeam = await (from HC in _readOnlyDbContext.HealthCareTeams
                                           where HC.PatientDetailsId == patientId && HC.OrgId == orgId && HC.StatusId == (short)Status.Active
                                           select new InputHealthCareTeam
                                           {
                                               Id = HC.Id,
                                               PatientDetailsId = HC.PatientDetailsId,
                                               OrgId = HC.OrgId,
                                               ProviderCompanyAssocsId = HC.ProviderCompanyAssocsId,
                                               ProviderCompanyAssocsInfo =
                                               (ProviderInfo)(from UC in _readOnlyDbContext.UserCompanyAssocs
                                                              join CD in _readOnlyDbContext.CompanyDetails on UC.CompanyId equals CD.Id
                                                              join UD in _readOnlyDbContext.UserDetails on UC.UserDetailsId equals UD.Id
                                                              where UC.OrgId == orgId && CD.OrgId == orgId && UD.OrgId == orgId
                                                              && UC.Id == HC.ProviderCompanyAssocsId && UC.CompanyId == CD.Id && UC.UserDetailsId == UD.Id
                                                              select new ProviderInfo
                                                              {
                                                                  Id = UC.Id,
                                                                  OrgId = UC.OrgId,
                                                                  UserDetailsId = UC.UserDetailsId,
                                                                  FirstName = UD.FirstName,
                                                                  SurName = UD.SurName,
                                                                  Salutation = UD.Salutation,
                                                                  ProviderTypeId = UD.ProviderTypeId,
                                                                  ProviderType = (UD.ProviderTypeId == null) ? null : EnumExtensions.GetDescription((ProviderType)(UD.ProviderTypeId)),
                                                                  Specialisation = UD.Specialisation,
                                                                  CompanyId = UC.CompanyId,
                                                                  CompanyName = CD.Name,
                                                                  CompanyEmail = CD.Email,
                                                                  ProviderNumber = UC.ProviderNumber,
                                                                  UserTypeId = UD.UserTypeId,
                                                                  CompanyTypeId = UC.CompanyTypeId
                                                              }).FirstOrDefault(),
                                               ProviderCompanyPostalAddress =
                                               (CompanyAddress)(from UC in _readOnlyDbContext.UserCompanyAssocs
                                                join CD in _readOnlyDbContext.CompanyDetails on UC.CompanyId equals CD.Id
                                                join CA in _readOnlyDbContext.CompanyAddresses on CD.Id equals CA.CompanyDetailsId
                                                where UC.OrgId == orgId && CD.OrgId == orgId
                                                && UC.Id == HC.ProviderCompanyAssocsId && UC.CompanyId == CD.Id
                                                && CA.AddressType == (short)AddressType.Postal && CA.StatusId == (short)Status.Active
                                                select new CompanyAddress
                                                {
                                                    Id = CA.Id,
                                                    AddressLine1 = CA.AddressLine1,
                                                    AddressLine2 = CA.AddressLine2,
                                                    Suburb = CA.Suburb,
                                                    StateId = CA.StateId,
                                                    PostCode = CA.PostCode                                                   
                                                }).FirstOrDefault(),
                                               ProviderLocationPostalAddress =
                                               (CompanyAddress)(from CD in _readOnlyDbContext.CompanyDetails
                                                                join CA in _readOnlyDbContext.CompanyAddresses on CD.Id equals CA.CompanyDetailsId
                                                                where CD.Id == HC.ProviderLocationId && CD.OrgId == orgId
                                                                 && CA.AddressType == (short)AddressType.Postal && CA.StatusId == (short)Status.Active
                                                                select new CompanyAddress
                                                                {
                                                                    Id = CA.Id,
                                                                    AddressLine1 = CA.AddressLine1,
                                                                    AddressLine2 = CA.AddressLine2,
                                                                    Suburb = CA.Suburb,
                                                                    StateId = CA.StateId,
                                                                    PostCode = CA.PostCode
                                                                }).FirstOrDefault(),
                                               ProviderLocationId = HC.ProviderLocationId,
                                               ProviderLocation = (from CD in _readOnlyDbContext.CompanyDetails                                                             
                                                                   where CD.Id == HC.ProviderLocationId && CD.OrgId == orgId
                                                                   select CD.Name).SingleOrDefault(),
                                               ProviderLocationEmail = (from CD in _readOnlyDbContext.CompanyDetails
                                                                        where CD.Id == HC.ProviderLocationId && CD.OrgId == orgId
                                                                        select CD.Email).SingleOrDefault()
                                           }).ToListAsync();
            return lstHealthCareTeam;
        }

        /// <summary>
        /// Method to fetch the healthcareteam details for a patient from db
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ICollection<HealthCareTeam>> GetHealthCareTeams(int orgId, long patientId)
        {
            return await _readOnlyDbContext.HealthCareTeams.AsNoTracking().Where(x => x.PatientDetailsId == patientId && x.OrgId == orgId && x.StatusId == (short)Status.Active).ToListAsync();
        }

        /// <summary>
        /// Update HealthCareTeam
        /// </summary>
        /// <param name="HealthCareTeam"></param>
        /// <returns></returns>
        public async Task<int> UpdateHealthCareTeamData(List<HealthCareTeam> healthCareTeamData)
        {
            _updatableDBContext.HealthCareTeams.UpdateRange(healthCareTeamData);
            return await _updatableDBContext.SaveChangesAsync();
        }

        public async Task<List<ReferralDetailsPatient>> GetReferralDetailsPatient(int orgId, long patientId)
        {
            return await _readOnlyDbContext.ReferralSource_Patient.AsNoTracking().Where(x => x.OrgID == orgId && x.StatusId == (short)Status.Active && x.PatientDetailsId == patientId).ToListAsync();
        }

        public async Task<int> InsertReferralDetails(long patientId, List<ReferralDetailsPatient> referralDetailsPatient)
        {
            // Find existing records with the given patientId
            var existingReferrals = await _updatableDBContext.ReferralSource_Patient
                .Where(r => r.PatientDetailsId == patientId)
                .ToListAsync();

            // If any records are found
            if (existingReferrals.Any())
            {
                // Map CreatedDate from existingReferrals to matching records in referralDetailsPatient
                foreach (var newReferral in referralDetailsPatient)
                {
                    // Match based on relevant fields, e.g., ReferralSourceID and SubInputValue
                    var existingReferral = existingReferrals
                        .FirstOrDefault(e => e.ReferralSourceID == newReferral.ReferralSourceID
                                          && e.SubInputValue == newReferral.SubInputValue);

                    //if (existingReferral != null)
                    //{
                    //    // Map CreatedDate from the existing record
                    //    newReferral.CreatedDate = existingReferral.CreatedDate;
                    //}
                }

                // Remove existing referrals
                _updatableDBContext.ReferralSource_Patient.RemoveRange(existingReferrals);
                await _updatableDBContext.SaveChangesAsync();
            }
            
            // Add the new referral details
            _updatableDBContext.ReferralSource_Patient.AddRange(referralDetailsPatient);

            // Save the changes
            return await _updatableDBContext.SaveChangesAsync();
        }

        public async Task<int> EditReferralDetails(long patientId, List<ReferralDetailsPatient> referralDetailsPatient)
        {
            _updatableDBContext.ReferralSource_Patient.UpdateRange(referralDetailsPatient);
            return await _updatableDBContext.SaveChangesAsync();
        }

        public async Task<List<ReferralSourceModel>> GetAllReferralSource(int orgId)
        {
            //return await _readOnlyDbContext.ReferralSource.Where(x => x.OrgID == orgId && x.StatusId == (short)Status.Active).ToListAsync();
            return await _readOnlyDbContext.ReferralSource
                .Where(x => x.OrgID == orgId && x.StatusId == (short)Status.Active)
                .ToListAsync();
        }

        public async Task<List<ReferralSourceDetails>> GetAllReferralSourceSubType(int orgId)
        {
            return await _readOnlyDbContext.ReferralSource_SubType.Where(x => x.OrgID == orgId && x.StatusId == (short)Status.Active).ToListAsync();
        }

    }
}
