﻿using AutoMapper;
using Capstone2.Framework.RestApi;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.MainService .Common;
using Capstone2.RestServices.Patient.Interfaces;
using Capstone2.RestServices.Patient.Models;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Transactions;
using System.Web;

namespace Capstone2.RestServices.Patient.Services
{
    public class ConsultNoteTemplateBAL : IConsultNoteTemplateBAL
    {
        public readonly IConsultNoteTemplateDAL _consultNoteTemplateDAL;
        public IMapper _mapper;
        public readonly AppSettings _appSettings;
        private readonly ILogger<ConsultNoteTemplateBAL> _logger;

        public ConsultNoteTemplateBAL(IConsultNoteTemplateDAL consultNoteTemplateDAL, IMapper mapper, IOptions<AppSettings> appSettings, ILogger<ConsultNoteTemplateBAL> logger)
        {
            _consultNoteTemplateDAL = consultNoteTemplateDAL;
            _mapper = mapper;
            _appSettings = appSettings.Value;
            _logger = logger;
        }

        /// <summary>
        /// Method to save a entry in consultNoteTemplateData
        /// </summary>
        /// <param name="consultNoteTemplatePayload"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> AddConsultNoteTemplateData(BaseHttpRequestContext baseHttpRequestContext, long patient_id, ConsultNotes consultNoteTemplateData)
        {
            var orgId = baseHttpRequestContext.OrgId;
            var loggedInUser = baseHttpRequestContext.UserId;

            consultNoteTemplateData.PatientDetailsId = patient_id;
            consultNoteTemplateData.OrgId = orgId;
            consultNoteTemplateData.CreatedBy = loggedInUser;
            consultNoteTemplateData.CreatedDate = DateTime.UtcNow;
            consultNoteTemplateData.StatusId = (short)Status.Active;

            if (consultNoteTemplateData?.ConsultNotesControls?.Any() ?? false)
            {
                consultNoteTemplateData.ConsultNotesControls.ToList().ForEach(a => 
                { a.OrgId = orgId; a.CreatedBy = loggedInUser; a.StatusId = (short)Status.Active; 
                    if(a.ConsultNoteMediaAssocs?.Any() ?? false)
                    {
                        a.ConsultNoteMediaAssocs.ToList().ForEach(media =>
                        {
                            media.CreatedBy = loggedInUser;
                            media.StatusId = (short)Status.Active;
                            media.ModifiedDate = DateTime.UtcNow;
                            media.ModifiedBy = loggedInUser;
                            media.PatientDetailsId = patient_id;
                            media.OrgId = orgId;
                        });
                    }
                });
            }

            DateTime.SpecifyKind(consultNoteTemplateData.CreatedDate, DateTimeKind.Utc);
            var id = await _consultNoteTemplateDAL.AddConsultNoteTemplateDataAsync(consultNoteTemplateData);
            if (id > 0)
            {
                ApiResponse<long?> apiResponse = new()
                {
                    StatusCode = StatusCodes.Status200OK,
                    Message = "Success",
                    Result = id
                };
                return apiResponse;
            }
            else
            {
                ApiResponse<long?> apiResponse = new()
                {
                    StatusCode = StatusCodes.Status400BadRequest,
                    Message = "Failure",
                    Result = null
                };
                return apiResponse;
            }
        }

        /// <summary>
        /// Get ConsultNoteTemplateIds by Communication ID
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="patient_id"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<ConsultNoteTemplateIds>> GetConsultNoteTemplateId(BaseHttpRequestContext baseHttpRequestContext, long patient_id, long communicationId)
        {
            ApiResponse<ConsultNoteTemplateIds> apiResposne = new();
            var orgId = baseHttpRequestContext.OrgId;
            var consultNoteTemplateFromDB = await _consultNoteTemplateDAL.GetConsultNoteTemplateIds(orgId, communicationId);
            if (consultNoteTemplateFromDB is null)
            {
                apiResposne.Result = null;
                apiResposne.StatusCode = StatusCodes.Status400BadRequest;
                apiResposne.Message = "Failure";
                apiResposne.Errors.Add("The ConsultNoteTemplatedoesnot exist.");
                return apiResposne;
            }
            if (consultNoteTemplateFromDB is not null)
            {
                apiResposne.Result = consultNoteTemplateFromDB;
                apiResposne.StatusCode = StatusCodes.Status200OK;
                apiResposne.Message = "Success";
                return apiResposne;
            }
            else
            {
                apiResposne.Result = null;
                apiResposne.StatusCode = StatusCodes.Status500InternalServerError;
                apiResposne.Message = "Data for given ConsultNoteTemplateID is not found";
                return apiResposne;
            }
        }

        /// <summary>
        /// Get ConsultNoteTemplateDataby ID
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<ConsultNotesView>> GetConsultNoteTemplateData(BaseHttpRequestContext baseHttpRequestContext, long patient_id, long id)
        {
            ApiResponse<ConsultNotesView> apiResposne = new();
            var orgId = baseHttpRequestContext.OrgId;
            var consultNoteTemplateFromDB = await _consultNoteTemplateDAL.GetConsultNoteTemplateData(orgId, id);
            if (consultNoteTemplateFromDB is null)
            {
                apiResposne.Result = null;
                apiResposne.StatusCode = StatusCodes.Status400BadRequest;
                apiResposne.Message = "Failure";
                apiResposne.Errors.Add("The ConsultNoteTemplatedoesnot exist.");
                return apiResposne;
            }
            else
            {
                consultNoteTemplateFromDB.CreatedDate = DateTime.SpecifyKind(consultNoteTemplateFromDB.CreatedDate, DateTimeKind.Utc);
                consultNoteTemplateFromDB.ModifiedDate = (consultNoteTemplateFromDB.ModifiedDate == null) ? null : DateTime.SpecifyKind((DateTime)consultNoteTemplateFromDB.ModifiedDate, DateTimeKind.Utc);

                ConsultNotesView consultNoteTemplateFromDBInfo = _mapper.Map<ConsultNotes, ConsultNotesView>(consultNoteTemplateFromDB);
                if (consultNoteTemplateFromDBInfo.ConsultNotesControls is not null && consultNoteTemplateFromDBInfo.ConsultNotesControls.Any())
                {
                    List<long> lstFileDetailsId = new();
                    consultNoteTemplateFromDBInfo.ConsultNotesControls.ToList().ForEach(a =>
                    {
                        if (a.ConsultNoteMediaAssocs?.Any() ?? false)
                        {
                            lstFileDetailsId.AddRange(a.ConsultNoteMediaAssocs.Select(x => x.FileDetailsId).ToList());
                          
                        }
                    });
                    if (lstFileDetailsId.Count > 0)
                    {

                        List<FileDetailsOutputForId> fileResponse = new();

                        string stringFileDetailsId = string.Join(",", lstFileDetailsId);
                        var token = baseHttpRequestContext.BearerToken;
                        string interServiceToken = baseHttpRequestContext.InterServiceToken;
                        string fileAPiUrl = _appSettings.ApiUrls["FileServiceUrl"] + "/file/file_details_list?" + "stringFileDetailsId" + "=" + HttpUtility.UrlEncode(stringFileDetailsId);
                        RestClient restClient = new RestClient(fileAPiUrl, null, token, interServiceToken);
                        var fileApiResponse = await restClient.GetAsync<ApiResponse<List<FileDetailsOutputForId>>>(fileAPiUrl, null);
                        if (fileApiResponse == null || fileApiResponse.StatusCode != StatusCodes.Status200OK)
                        {
                            apiResposne.StatusCode = fileApiResponse.StatusCode;
                            apiResposne.Errors = fileApiResponse.Errors;
                            apiResposne.Message = fileApiResponse.Message;
                            return apiResposne;
                        }
                        else
                        {
                            fileResponse = fileApiResponse.Result;
                            consultNoteTemplateFromDBInfo.ConsultNotesControls.ToList().ForEach(a =>
                            {
                                if (a.ConsultNoteMediaAssocs?.Any() ?? false)
                                {
                                    a.ConsultNoteMediaAssocs.ToList().ForEach(media =>
                                    {
                                       
                                        var filedata = fileResponse.Find(x => x.FileDetail.Id == media.FileDetailsId);
                                        media.FileDetailsOutput = filedata;
                                        
                                    });

                                }
                            });


                           
                        }
                    }
                }

                apiResposne.Result = consultNoteTemplateFromDBInfo;
                apiResposne.StatusCode = StatusCodes.Status200OK;
                apiResposne.Message = "Success";
                return apiResposne;
            }
           
        }

        /// <summary>
        /// Edit Appointment Type
        /// </summary>
        /// <param name="id"></param>
        /// <param name="inputConsultNoteTemplate"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> EditConsultNoteTemplateData(BaseHttpRequestContext baseHttpRequestContext, long patient_id, long id, ConsultNotes inputConsultNotes)
        {
            ApiResponse<long?> apiResponse = new();
            var orgId = baseHttpRequestContext.OrgId;
            var userId = baseHttpRequestContext.UserId;
            var consultNotes = await _consultNoteTemplateDAL.GetConsultNoteTemplateDataSync(orgId, id);

            inputConsultNotes.Id = consultNotes.Id;
            inputConsultNotes.CommunicationNotesId = consultNotes.CommunicationNotesId;
            inputConsultNotes.PatientDetailsId = patient_id;
            inputConsultNotes.OrgId = orgId;
            inputConsultNotes.ModifiedDate = DateTime.UtcNow;
            inputConsultNotes.ModifiedBy = userId;
            inputConsultNotes.CreatedBy = consultNotes.CreatedBy;
            inputConsultNotes.CreatedDate = consultNotes.CreatedDate;

            if (inputConsultNotes != null && inputConsultNotes.ConsultNotesControls.Any())
            {
                inputConsultNotes.ConsultNotesControls.ToList().ForEach(a => { a.OrgId = orgId; a.ConsultNotesId = id; });
            }

            using (var transaction = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
            {
                inputConsultNotes.ConsultNotesControls = await EditConsultNotesControls(inputConsultNotes.ConsultNotesControls, id, orgId, userId, patient_id);

                await _consultNoteTemplateDAL.UpdateConsultNoteTemplateData(inputConsultNotes);

                apiResponse.Result = id;
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";

                transaction.Complete();
            }

            return apiResponse;
        }

        private async Task<List<ConsultNotesControls>> EditConsultNotesControls(ICollection<ConsultNotesControls> inputControlList, long id, int orgId, long userId, long patient_id)
        {
            List<ConsultNotesControls> addControlList = new();

            var removeUserList = await _consultNoteTemplateDAL.GetConsultNotesControlsSync(orgId, id);
           
            foreach (var control in inputControlList)
            {
                if (control.Id > 0)
                {
                    var existingobj = removeUserList.FirstOrDefault(x => x.Id == control.Id);
                    removeUserList.Remove(existingobj);
                    control.ModifiedDate = DateTime.UtcNow;
                    control.ModifiedBy = userId;
                    control.StatusId = (short)Status.Active;

                    var mediaAssocDB = existingobj.ConsultNoteMediaAssocs;
                    var mediaAssocs = control.ConsultNoteMediaAssocs;
                    if(mediaAssocs != null || mediaAssocDB!=null)
                    {
                        List<ConsultNoteMediaAssoc> lstAddUpdMediaAssoc = EditConsultNoteMediaAssocs(mediaAssocs, mediaAssocDB, orgId, userId);
                        if (lstAddUpdMediaAssoc?.Any() ?? false)
                        {
                            lstAddUpdMediaAssoc.ForEach(x =>
                            {
                                x.ConsultNotesControlsId = control.Id;
                                x.PatientDetailsId = patient_id;
                            });
                        }
                            
                        control.ConsultNoteMediaAssocs = (lstAddUpdMediaAssoc == null || lstAddUpdMediaAssoc.Count == 0) ? null : lstAddUpdMediaAssoc;
                    }
                   
                    
                    addControlList.Add(control);
                }
                else
                {
                    var userAssocDB = removeUserList.Where(x => x.Id == control.Id).FirstOrDefault();
                    if (userAssocDB is null)
                    {
                        control.CreatedDate = DateTime.UtcNow;
                        control.CreatedBy = userId;
                        control.StatusId = (short)Status.Active;

                        var mediaAssocs = control.ConsultNoteMediaAssocs;
                        if(mediaAssocs is not null && mediaAssocs.Count > 0)
                        {
                            mediaAssocs.ToList().ForEach(x =>
                            {
                                x.PatientDetailsId = patient_id;
                                x.OrgId = orgId;
                                x.StatusId = (short)Status.Active;
                                x.CreatedBy = userId;
                                x.CreatedDate = DateTime.UtcNow;
                                x.ModifiedBy = userId;
                            });
                            control.ConsultNoteMediaAssocs = mediaAssocs;
                        }

                        addControlList.Add(control);
                    }
                    else
                    {
                        if (userAssocDB.StatusId == (short)Status.Deleted)
                        {
                            userAssocDB.StatusId = (short)Status.Active;
                            userAssocDB.ModifiedDate = DateTime.UtcNow;
                            userAssocDB.ModifiedBy = userId;
                            addControlList.Add(userAssocDB);
                        }
                        removeUserList.Remove(userAssocDB);
                    }
                }
            }

            foreach (var userAssoc in removeUserList.Where(x => x.StatusId == (short)Status.Active))
            {
                userAssoc.StatusId = (short)Status.Deleted;
                userAssoc.ModifiedDate = DateTime.UtcNow;
                userAssoc.ModifiedBy = userId;
            }

            addControlList = addControlList.Concat(removeUserList).ToList();
            return addControlList;
        }

        private List<ConsultNoteMediaAssoc> EditConsultNoteMediaAssocs(ICollection<ConsultNoteMediaAssoc> mediaAssocs, ICollection<ConsultNoteMediaAssoc> mediaAssocDB, int orgId, long userId)
        {
            List<ConsultNoteMediaAssoc> lstAddUpdMediaAssoc = new();
            if(mediaAssocs is not null)
                foreach (var itemInput in mediaAssocs)
                {

                    var existingobj = (mediaAssocDB == null || mediaAssocDB.Count == 0) ? null : mediaAssocDB.FirstOrDefault(x => x.Id == itemInput.Id || x.FileDetailsId == itemInput.FileDetailsId);

                    if (existingobj != null)
                    {
                        itemInput.Id = existingobj.Id;
                        itemInput.StatusId = (short)Status.Active;
                        itemInput.CreatedBy = existingobj.CreatedBy;
                        itemInput.ModifiedBy = userId;
                        itemInput.CreatedDate = existingobj.CreatedDate;
                        itemInput.ConsultNotesControlsId = existingobj.ConsultNotesControlsId;
                        itemInput.ModifiedDate = DateTime.UtcNow;
                        itemInput.OrgId = existingobj.OrgId;
                        lstAddUpdMediaAssoc.Add(itemInput);
                        mediaAssocDB.Remove(existingobj);
                    }
                    else
                    {
                        itemInput.StatusId = (short)Status.Active;
                        itemInput.CreatedBy = userId;
                        itemInput.ModifiedBy = userId;
                        itemInput.CreatedDate = DateTime.UtcNow;
                        itemInput.OrgId = orgId;
                        lstAddUpdMediaAssoc.Add(itemInput);

                    }
                }
                     
            if (mediaAssocDB is not null)
            {
                foreach (var item in mediaAssocDB)
                {
                    item.ModifiedBy = userId;
                    item.ModifiedDate = DateTime.UtcNow;
                    item.StatusId = (short)Status.Deleted;
                }
                lstAddUpdMediaAssoc = lstAddUpdMediaAssoc.Concat(mediaAssocDB).ToList();
            }

            return lstAddUpdMediaAssoc;
        }
    }
}
