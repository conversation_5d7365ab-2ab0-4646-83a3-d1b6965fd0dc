﻿using Capstone2.RestServices.Patient.Context;
using Capstone2.RestServices.Patient.Interfaces;
using Capstone2.RestServices.Patient.Models;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Patient.Services
{
    public class LetterDAL : ILetterDAL
    {
        public readonly ReadOnlyPatientDBContext _readOnlyDbContext;
        public readonly UpdatablePatientDBContext _updatableDBContext;
        public LetterDAL(ReadOnlyPatientDBContext readOnlyDbContext, UpdatablePatientDBContext updatableDBContext)
        {
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
        }

        /// <summary>
        /// method to add a letter to database
        /// </summary>
        /// <param name="inputLetter"></param>
        /// <returns></returns>
        public async Task<long> AddLetterDAL(Letter inputLetter)
        {
            await _updatableDBContext.Letters.AddAsync(inputLetter);
            await _updatableDBContext.SaveChangesAsync();
            return (long)inputLetter.Id;
        }

        /// <summary>
        /// Method to update the letter in Letter table
        /// </summary>
        public async Task<int> EditLetterDAL(Letter inputLetter)
        {
            _updatableDBContext.Letters.Update(inputLetter);
            return await _updatableDBContext.SaveChangesAsync();
        }

        /// <summary>
        /// Method to get a letter from database based on id and orgId
        /// </summary>
        /// <param name="patient_id"></param>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<Letter> GetLetterDAL(long patient_id, int orgId, long id)
        {
            
            return await _readOnlyDbContext.Letters.Where(a => a.PatientDetailsId == patient_id).FirstOrDefaultAsync(p => p.Id == id && p.OrgId == orgId);
        }

        public async Task<LetterOutput> GetLetterOutDAL(long patient_id, int orgId, long id)
        {
            LetterOutput letterOut = await (from PL in _readOnlyDbContext.Letters
                                            where PL.OrgId == orgId && PL.PatientDetailsId == patient_id && PL.Id == id
                                            from LT in _readOnlyDbContext.LetterTemplates.Where(u => u.OrgId == orgId && u.Id == PL.LetterTemplatesId).DefaultIfEmpty()
                                            from CD in _readOnlyDbContext.CompanyDetails.Where(u => u.OrgId == orgId && u.Id == LT.CompanyDetailsId).DefaultIfEmpty()
                                            select new LetterOutput
                                            {

                                                Id = PL.Id,
                                                OrgId = PL.OrgId,
                                                PatientDetailsId = PL.PatientDetailsId,
                                                Name = PL.Name,
                                                Text = PL.Text,
                                                HeaderStyleTypeId = PL.HeaderStyleTypeId,
                                                FooterStyleTypeId = PL.FooterStyleTypeId,
                                                FileDetailsId = PL.FileDetailsId,
                                                SFDTFileDetailsId= PL.SFDTFileDetailsId,
                                                DeleteReason = PL.DeleteReason,
                                                LetterStatusId = PL.LetterStatusId,
                                                StatusId = PL.StatusId,
                                                CreatedDate = PL.CreatedDate,
                                                ModifiedDate = PL.ModifiedDate,
                                                CreatedBy = PL.CreatedBy,
                                                ModifiedBy = PL.ModifiedBy,
                                                LetterTemplatesId = PL.LetterTemplatesId,
                                                LetterId = PL.LetterId,
                                                VersionNo = PL.VersionNo,
                                                AssignedTo = PL.AssignedTo,
                                                CompanyDetailsId = LT.CompanyDetailsId,
                                                CompanyName = CD.Name,
                                                AddressorEntityId = PL.AddressorEntityId,
                                                AddresseeTypeId = PL.AddresseeTypeId,
                                                AddresseeEntityId = PL.AddresseeEntityId,
                                                AppointmentDetailsId = PL.AppointmentDetailsId,
                                                AddressorInfo =
                                                 (from UD in _readOnlyDbContext.UserDetails
                                                  where UD.OrgId == orgId && UD.Id == PL.AddressorEntityId

                                                  select new AddressorData
                                                  {
                                                      Id = UD.Id,
                                                      FirstName = UD.FirstName,
                                                      SurName = UD.SurName,
                                                      AddressorEmail = UD.LoginEmail

                                                  }).FirstOrDefault(),
                                                AppointmentInfo = (PL.AppointmentDetailsId != null) ?
                                                (from A in _readOnlyDbContext.AppointmentDetails
                                                 join AT in _readOnlyDbContext.AppointmentTypes on A.AppointmentTypesId equals AT.Id into JAT
                                                 from AP in JAT.DefaultIfEmpty()
                                                 join UD in _readOnlyDbContext.UserDetails on A.UserDetailsId equals UD.Id into JUD
                                                 from UD in JUD.DefaultIfEmpty()
                                                 where A.OrgId == orgId && A.Id == PL.AppointmentDetailsId
                                                 select new AppointmentData
                                                 {
                                                     Id = A.Id,
                                                     DateOfAppointment = A.DateOfAppointment,
                                                     AppointmentTypesId = A.AppointmentTypesId,
                                                     Type = (AP == null) ? null : AP.Type,
                                                     ProviderName = UD.FirstName + " " + UD.SurName 
                                                 }).FirstOrDefault()
                                                 :
                                                 null,
                                                AddresseeInfo = (PL.AddresseeTypeId == (short)AddresseeType.Patient) ?
                                                 (from PD in _readOnlyDbContext.PatientDetails
                                                  where PD.OrgId == orgId && PD.Id == PL.AddresseeEntityId

                                                  select new AddresseeData
                                                  {
                                                      Id = (long)PD.Id,
                                                      FirstName = PD.FirstName,
                                                      SurName = PD.SurName,
                                                      AddresseeEmail = PD.PersonalEmailAddress,
                                                      Salutation = PD.Salutation

                                                  }).FirstOrDefault()
                                                  : (PL.AddresseeTypeId == (short)AddresseeType.Company) ?
                                                 (from C in _readOnlyDbContext.CompanyDetails
                                                  where C.OrgId == orgId && C.Id == PL.AddresseeEntityId

                                                  select new AddresseeData
                                                  {
                                                      CompanyId = C.Id,
                                                      CompanyName = C.Name,
                                                      AddresseeEmail = C.Email

                                                  }).FirstOrDefault()
                                                  :
                                                  (from UCA in _readOnlyDbContext.UserCompanyAssocs
                                                   join CM in _readOnlyDbContext.CompanyDetails on UCA.CompanyId equals CM.Id
                                                   join UR in _readOnlyDbContext.UserDetails on UCA.UserDetailsId equals UR.Id
                                                   where UCA.OrgId == orgId && UCA.Id == PL.AddresseeEntityId
                                                   select new AddresseeData
                                                   {
                                                       Id = UCA.Id,
                                                       FirstName = UR.FirstName,
                                                       SurName = UR.SurName,
                                                       AddresseeEmail = CM.Email,
                                                       CompanyName = CM.Name,
                                                       CompanyId = CM.Id,
                                                       Salutation = UR.Salutation,
                                                       ProviderNumber=UCA.ProviderNumber

                                                   }).FirstOrDefault()

                                            }).FirstOrDefaultAsync();
            return letterOut;

        }



        /// <summary>
        /// Method to get All Version Letter from database based on id and orgId
        /// </summary>
        /// <param name="letterId"></param>
        /// <param name="patientId"></param>
        /// <returns></returns>
        public async Task<List<Letter>> GetLetterAllVersionDAL(long? letterId, long patientid)
        {
            List<Letter> letterVersions = new();
            letterVersions = await _readOnlyDbContext.Letters.Where(p => (p.LetterId == letterId || p.Id == letterId) && p.PatientDetailsId == patientid && p.StatusId == (short?)Status.Active).OrderBy(x => x.CreatedDate).ToListAsync();
            return letterVersions;
        }
        public async Task<OrganisationView> GetOrganistaionDAL(int orgId)
        {
            var organisation = await (from C1 in _readOnlyDbContext.Organisation.Where(C1 => C1.Id == orgId)
                                      select new OrganisationView()
                                      {
                                          Id = C1.Id,
                                          OrgCode = C1.OrgCode,
                                          EntityName = C1.EntityName,
                                          EntityAbn = C1.EntityAbn,
                                          EntityType = C1.EntityType,
                                          OrgIpAddress = C1.OrgIpAddress,
                                          LogoFileDetailsId = C1.LogoFileDetailsId,
                                          ContactNumber = C1.ContactNumber,
                                          FaxNumber = C1.FaxNumber,
                                          Email = C1.Email,
                                          ContactDetails = C1.ContactDetails,
                                          Services = C1.Services,
                                          RememberMeTokenExpiry = C1.RememberMeTokenExpiry,
                                          ResetPasswordTokenExpiry = C1.ResetPasswordTokenExpiry,
                                          IdleTimeOut = C1.IdleTimeOut,
                                          CreatedDate = C1.CreatedDate,
                                          ModifiedBy = C1.ModifiedBy,
                                          ModifiedDate = C1.ModifiedDate,
                                          TimeZoneMasterId = C1.TimeZoneMasterId,
                                          MedicalYearStartDate = C1.MedicalYearStartDate,
                                          WorkStartTime = C1.WorkStartTime,
                                          WorkEndTime = C1.WorkEndTime,
                                          AppointmentBlock = C1.AppointmentBlock,
                                          AdhocSmsSubscription =C1.AdhocSmsSubscription

                                      }).FirstOrDefaultAsync();

            organisation.WindowsTimeZoneData = EnumExtensions.GetDescription((WindowsTimeZone)(organisation.TimeZoneMasterId));
            organisation.LinuxTimeZoneData = EnumExtensions.GetDescription((LinuxTimeZone)(organisation.TimeZoneMasterId));
            return organisation;
        }

        //** LETTER ACTIONS  **//

        /// <summary>
        /// AddLetterActions
        /// </summary>
        /// <param name="letterActions"></param>
        /// <returns></returns>
        public async Task<long> AddLetterActions(LetterActions letterActions)
        {
            await _updatableDBContext.LetterActions.AddAsync(letterActions);
            return await _updatableDBContext.SaveChangesAsync();
        }

        /// <summary>
        /// GetLetterActions by Letter Id
        /// </summary>
        /// <param name="letterId"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<LetterActions> GetLetterActions(long letterId, int orgId)
        {
            return await _readOnlyDbContext.LetterActions.Where(s => s.LetterId == letterId && s.OrgId == orgId).AsNoTracking().FirstOrDefaultAsync();
        }

        /// <summary>
        /// UpdateLetterActions by letter Id
        /// </summary>
        /// <param name="letterActions"></param>
        /// <returns></returns>
        public async Task<int> UpdateLetterActions(LetterActions letterActions)
        {
            _updatableDBContext.LetterActions.Update(letterActions);
            return await _updatableDBContext.SaveChangesAsync();
        }

        //** PATIENT ACTIONS - LETTER DETAILS  **//

        /// <summary>
        /// GetPatientActionsLetterDetails
        /// </summary>
        /// <param name="id"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<PatientActionsLetterDetails> GetPatientActionsLetterDetails(long id, int orgId)
        {
            return await (from PR in _readOnlyDbContext.Letters.Where(x => x.Id == id && x.OrgId == orgId)
                          from PD in _readOnlyDbContext.PatientDetails.Where(x => x.Id == PR.PatientDetailsId && x.OrgId == orgId)
                          select new PatientActionsLetterDetails
                          {
                              PatientId = PR.PatientDetailsId,
                              FirstName = PD.FirstName,
                              SurName = PD.SurName,
                              PatienPhone = PD.Mobile,
                              LetterId = PR.Id,
                          }).FirstOrDefaultAsync();
        }

        public async Task<List<UserDetailInfo>> FetchUserDetailsFromIds(List<long> listIds, int orgId)
        {
            return await _readOnlyDbContext.UserDetails.Where(x => listIds.Contains(x.Id) && x.OrgId == orgId).Select(x => new UserDetailInfo
            {
                Id = x.Id,
                FirstName = x.FirstName,
                SurName = x.SurName,
                TitleId = x.TitleId,
                FileDetailsOutput = null
            }).ToListAsync();
        }

        public async Task<int> AddPatientEmail(Email email)
        {
            await _updatableDBContext.Emails.AddAsync(email);
            return await _updatableDBContext.SaveChangesAsync();
        }

        public async Task<int> AddEHealthMessage(EHealthMessage eHealthMessage)
        {
            await _updatableDBContext.EHealthMessages.AddAsync(eHealthMessage);
            return await _updatableDBContext.SaveChangesAsync();
        }
        /// <summary>
        /// Method to fetch the next value for a sequence
        /// </summary>
        /// <param name="sequenceName"></param>
        /// <returns></returns>
        public async Task<long> GetNextVal(string sequenceName)
        {
            var p = new SqlParameter("@result", System.Data.SqlDbType.BigInt);
            p.Direction = System.Data.ParameterDirection.Output;
            await _updatableDBContext.Database.ExecuteSqlRawAsync("set @result = next value for " + sequenceName, p);
            return (long)p.Value;
        }
    }
}
