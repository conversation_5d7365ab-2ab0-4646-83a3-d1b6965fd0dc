﻿using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.Patient.Interfaces;
using Capstone2.RestServices.Patient.Models;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using System;
using System.Linq;
using System.Threading.Tasks;
using System.Transactions;

namespace Capstone2.RestServices.Patient.Services
{
    public class HealthDetailsBAL : IHealthDetailsBAL
    {
        public readonly IHealthDetailsDAL _healthDetailsDAL;

        public HealthDetailsBAL(IHealthDetailsDAL healthDetailsDAL)
        {
            _healthDetailsDAL = healthDetailsDAL;
        }

        /// <summary>
        /// Method to save a entry in HealthDetailsData
        /// </summary>
        /// <param name="HealthDetailsPayload"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> AddHealthDetails(BaseHttpRequestContext baseHttpRequestContext, long patientId, HealthDetails inputHealthDetails)
        {
            ApiResponse<long?> apiResponse = new();
            var orgId = baseHttpRequestContext.OrgId;
            var userId = baseHttpRequestContext.UserId;

            inputHealthDetails.PatientDetailsId = patientId;
            inputHealthDetails.OrgId = orgId;
            inputHealthDetails.CreatedBy = userId;
            inputHealthDetails.CreatedDate = DateTime.UtcNow;
            inputHealthDetails.StatusId = (short)Status.Active;

            DateTime.SpecifyKind(inputHealthDetails.CreatedDate, DateTimeKind.Utc);

            var healthDetailsFromDB = await _healthDetailsDAL.GetHealthDetails(orgId, patientId);
            if (healthDetailsFromDB == null)
            {
                var id = await _healthDetailsDAL.AddHealthDetails(inputHealthDetails);
                if (id > 0)
                {


                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    apiResponse.Message = "Success";
                    apiResponse.Result = id;


                }
                else
                {

                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Message = "Failure";
                    apiResponse.Result = null;
                }
                
                    
                
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failed";
                apiResponse.Errors.Add("Health Details already exists.Trying to add Duplicate entry");
               
            }

                return apiResponse;
            }

        /// <summary>
        /// Get HealthDetailsDataby ID
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<HealthDetails>> GetHealthDetails(BaseHttpRequestContext baseHttpRequestContext, long patientId)
        {
            ApiResponse<HealthDetails> apiResposne = new();
            var orgId = baseHttpRequestContext.OrgId;

            var healthDetailsFromDB = await _healthDetailsDAL.FetchHealthDetails(orgId, patientId);

            return new ApiResponse<HealthDetails>
            {
                StatusCode = StatusCodes.Status200OK,
                Message = "Success",
                Result = healthDetailsFromDB
            };
        }

        /// <summary>
        /// Edit HealthDetails
        /// </summary>
        /// <param name="id"></param>
        /// <param name="inputHealthDetails"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<string>> EditHealthDetails(BaseHttpRequestContext baseHttpRequestContext, long patientId, long healthDetailsId, HealthDetails inputHealthDetails)
        {
            ApiResponse<string> apiResponse = new();
            var orgId = baseHttpRequestContext.OrgId;
            var userId = baseHttpRequestContext.UserId;

            var appointmentFromDb = await _healthDetailsDAL.GetHealthDetails(orgId, patientId);

            inputHealthDetails.Id = healthDetailsId;
            inputHealthDetails.PatientDetailsId = patientId;
            inputHealthDetails.OrgId = orgId;
            inputHealthDetails.ModifiedDate = DateTime.UtcNow;
            inputHealthDetails.ModifiedBy = userId;
            inputHealthDetails.CreatedBy = appointmentFromDb.CreatedBy;
            inputHealthDetails.CreatedDate = appointmentFromDb.CreatedDate;
            inputHealthDetails.StatusId = appointmentFromDb.StatusId;

            await _healthDetailsDAL.UpdateHealthDetails(inputHealthDetails);

            apiResponse.Result = "Success";
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";

            return apiResponse;
        }
    }
}
