﻿using Capstone2.RestServices.Utility.Context;
using Capstone2.RestServices.Utility.Interfaces;
using Capstone2.RestServices.Utility.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Enum;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Utility.Services
{
    public class RolesDAL : IRolesDAL
    {
        public readonly ReadOnlyUtilityDBContext _readOnlyDbContext;
        public UpdatableUtilityDBContext _updatableDBContext;
        public RolesDAL(ReadOnlyUtilityDBContext readOnlyDbContext, UpdatableUtilityDBContext updatableDBContext)
        {
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
        }
        /// <summary>
        /// Method to get all the roles from database based on orgId
        /// </summary>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<List<Role>> ListRolesDAL(int orgId)
        {
            List<Role> roles = new();
            roles = await _readOnlyDbContext.Roles.Where(p => p.OrgId == orgId && p.StatusId == (short?)Status.Active && !p.IsBackend).OrderBy(x => x.CreatedDate).ToListAsync();
            return roles;
        }

        /// <summary>
        /// Method to get the roles_permissions_assoc from database based on rolesId
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="rolesId"></param>
        public async Task<List<RolesPermissionsAssoc>> ListRolesPermissionsAssocsByRolesId(int rolesId, int orgId)
        {
            List<RolesPermissionsAssoc> assocs = new();
            assocs = await _readOnlyDbContext.RolesPermissionsAssocs.Where(p => p.OrgId == orgId && p.RolesId == rolesId).ToListAsync();
            return assocs;
        }

        /// <summary>
        /// Method to retrieve list of snippets
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="loggedInUser"></param>
        /// <returns></returns>
        public async Task<List<RolesPermissionsAssoc>> ListRolesPermissionsAssocsDAL(RolesFilterModel filterModel, int orgId)
        {
            var rolesQuery = (from RP in _readOnlyDbContext.RolesPermissionsAssocs
                              where RP.StatusId == (short)Status.Active && RP.OrgId == orgId
                              select RP);
            if (filterModel != null)
            {
                if (filterModel.RolesId != null)
                {
                    rolesQuery = rolesQuery.Where(x => filterModel.RolesId.Contains(x.RolesId));
                }
            }
            return await rolesQuery.ToListAsync();
        }

        public async Task<RolesPermissionsAssoc> GetRolesPermissionsAssocDAL(int id, int orgId)
        {
            RolesPermissionsAssoc assoc = await _readOnlyDbContext.RolesPermissionsAssocs.AsNoTrackingWithIdentityResolution().Where(m => m.OrgId == orgId && m.Id == id).SingleOrDefaultAsync();
            return assoc;
        }

        /// <summary>
        /// Method to create a new association
        /// </summary>
        /// <param name="tag"></param>
        /// <returns></returns>
        public async Task<int> AddRolesPermissionsAssoc(RolesPermissionsAssoc inputAssoc)
        {
            _updatableDBContext.RolesPermissionsAssocs.AddAsync(inputAssoc);
            await _updatableDBContext.SaveChangesAsync();
            return inputAssoc.Id;
        }

        /// <summary>
        /// Method to update Roles_Permissions_Assocs in bulk
        /// </summary>
        /// <param name="media"></param>
        public async Task<int> UpdateRangeRolesPermissionsAssoc(List<RolesPermissionsAssoc> inputAssocs)
        {
            _updatableDBContext.RolesPermissionsAssocs.UpdateRange(inputAssocs);
            return await _updatableDBContext.SaveChangesAsync();
        }
    }
}