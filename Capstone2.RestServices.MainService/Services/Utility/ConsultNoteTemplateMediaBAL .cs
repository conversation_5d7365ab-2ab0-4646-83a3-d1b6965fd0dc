﻿using AutoMapper;
using Capstone2.Framework.RestApi;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.Utility.Interfaces;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Web;

namespace Capstone2.RestServices.Utility.Services
{
    public class ConsultNoteTemplateMediaBAL : IConsultNoteTemplateMediaBAL 
    {
        public readonly IConsultNoteTemplateMediaDAL _consultNoteTemplateMediaDAL;
        public IMapper _mapper;
        public readonly AppSettings _appSettings;

        public ConsultNoteTemplateMediaBAL(IConsultNoteTemplateMediaDAL consultNoteTemplateMediaDAL, IMapper mapper, IOptions<AppSettings> appSettings)
        {
            _consultNoteTemplateMediaDAL = consultNoteTemplateMediaDAL;
            _mapper = mapper;
            _appSettings = appSettings.Value;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="media"></param>
        /// <param name="patientId"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> AddMedia(BaseHttpRequestContext baseHttpRequestContext, long templateId, long controlId, List<ConsultNoteTemplateMedia> media)
        {
            var orgId = baseHttpRequestContext.OrgId;
            var loggedInUser = baseHttpRequestContext.UserId;

            foreach (var item in media)
            {
                item.ConsultNoteTemplateControlsId = controlId;
                item.OrgId = orgId;
                item.CreatedBy = loggedInUser;
                item.CreatedDate = DateTime.UtcNow;
                item.StatusId = (short)Status.Active;
            }

            var id = await _consultNoteTemplateMediaDAL.AddMediaCollection(media);
            if (id > 0)
            {
                ApiResponse<long?> apiResponse = new()
                {
                    StatusCode = StatusCodes.Status200OK,
                    Message = "Success",
                    Result = id
                };
                return apiResponse;
            }
            else
            {
                ApiResponse<long?> apiResponse = new()
                {
                    StatusCode = StatusCodes.Status400BadRequest,
                    Message = "Media cannot be added at this time.",
                    Result = null
                };
                return apiResponse;
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="queryModel"></param>
        /// <param name="patient_id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<List<ConsultNoteTemplateMediaView>>> ListMediaLibrary(BaseHttpRequestContext baseHttpRequestContext, long templateId, long controlId)
        {
            ApiResponse<List<ConsultNoteTemplateMediaView>> apiResponse = new();

            var models = await _consultNoteTemplateMediaDAL.ListMediaLibrary(baseHttpRequestContext.OrgId, templateId, controlId);
            if (models is not null && models.Any())
            {
                apiResponse.Result = await FetchFileDetailsForMediaAsync(models, baseHttpRequestContext);
            }

            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            return apiResponse;
        }

        private async Task<List<ConsultNoteTemplateMediaView>> FetchFileDetailsForMediaAsync(List<ConsultNoteTemplateMediaView> paginatedList, BaseHttpRequestContext baseHttpRequestContext)
        {
            List<long> lstFileDetailsId = paginatedList.Select(x => x.FileDetailsId).ToList();
            List<FileDetailsOutputForId> fileResponse = new();
            if (lstFileDetailsId.Count > 0)
            {
                string stringFileDetailsId = string.Join(",", lstFileDetailsId);
                var token = baseHttpRequestContext.BearerToken;
                string interServiceToken = baseHttpRequestContext.InterServiceToken;
                string fileAPiUrl = _appSettings.ApiUrls["FileServiceUrl"] + "/file/file_details_list?" + "stringFileDetailsId" + "=" + HttpUtility.UrlEncode(stringFileDetailsId);
                RestClient restClient = new RestClient(fileAPiUrl, null, token, interServiceToken);
                var fileApiResponse = await restClient.GetAsync<ApiResponse<List<FileDetailsOutputForId>>>(fileAPiUrl, null);
                if (fileApiResponse.StatusCode == StatusCodes.Status200OK)
                {
                    fileResponse = fileApiResponse.Result;
                }
                if (fileResponse.Count > 0)
                {
                    paginatedList.ForEach(x =>
                    {
                        FileDetailsOutputForId fileDetailsOutputForId = fileResponse.Find(f => f.FileDetail.Id == x.FileDetailsId);
                        x.FileDetails = fileDetailsOutputForId;
                    });
                }
            }
            return paginatedList;
        }
    }
}
