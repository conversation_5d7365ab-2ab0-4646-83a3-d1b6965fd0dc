﻿using Capstone2.Framework.Business.Common;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.Utility.Interfaces;
using Capstone2.RestServices.Utility.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Capstone2.Framework.RestApi;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Transactions;
using Microsoft.Extensions.Options;
using MbsItemDetail = Capstone2.Shared.Models.Entities.MbsItemDetail;
using ListMbsData = Capstone2.Shared.Models.Entities.ListMbsData;
using MbsDataFilterModel = Capstone2.Shared.Models.Entities.MbsDataFilterModel;
using MbsItemCustomFeeAssoc = Capstone2.Shared.Models.Entities.MbsItemCustomFeeAssoc;
using MbsDataView = Capstone2.Shared.Models.Entities.MbsDataView;
using AssignCategoryInput = Capstone2.Shared.Models.Entities.AssignCategoryInput;
using AutoMapper;
using MbsItemView = Capstone2.Shared.Models.Entities.MbsItemView;

namespace Capstone2.RestServices.Utility.Services
{
    public class MbsBAL : IMbsBAL
    {
        public readonly IMbsDAL _mbsDAL;
        public readonly IEpisodeDAL _episodeDAL;
        public readonly AppSettings _appSettings;
        public IMapper _mapper;
        // In your class definition
        private readonly Capstone2.RestServices.MedicalSchedule.Interfaces.IMbsBAL _medicalScheduleMbsBAL;

        public MbsBAL(IMbsDAL mbsDAL, IEpisodeDAL episodeDAL, IOptions<AppSettings> appSettings, IMapper mapper, Capstone2.RestServices.MedicalSchedule.Interfaces.IMbsBAL medicalScheduleMbsBAL)
        {
            _mbsDAL = mbsDAL;
            _episodeDAL = episodeDAL;
            _appSettings = appSettings.Value;
            _mapper = mapper;
            _medicalScheduleMbsBAL = medicalScheduleMbsBAL;
        }
        /// <summary>
        /// Method to add new custom mbs item
        /// </summary>
        /// <param name="mbsItemDetail"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long>> AddMbsItem(MbsItemDetail mbsItemDetail,  BaseHttpRequestContext baseHttpRequestContext)
        {
            int orgId = baseHttpRequestContext.OrgId;
            long loggedInUser = baseHttpRequestContext.UserId;
            ApiResponse<long> apiResponse = new();
            if (mbsItemDetail is not null)
            {
                decimal? gst = await _episodeDAL.FetchGstForOrganisation(baseHttpRequestContext.OrgCode, orgId);
                if (mbsItemDetail.Gstinc == true && mbsItemDetail.PrivateFee != null)
                    mbsItemDetail.GstFee = CommonFinanceCalculator.CalculateGst((decimal)mbsItemDetail.PrivateFee, (decimal)gst);
                else
                    mbsItemDetail.GstFee = null;
                mbsItemDetail.ModifiedBy = loggedInUser;
                mbsItemDetail.OrgId = orgId;
                mbsItemDetail.CreatedDate = DateTime.UtcNow;
                mbsItemDetail.StatusId = (short)Status.Active;

                if (mbsItemDetail.MbsItemCustomFeeAssocs is not null && mbsItemDetail.MbsItemCustomFeeAssocs.Any())
                {
                    mbsItemDetail.MbsItemCustomFeeAssocs.ToList().ForEach(x =>
                    {
                        if (mbsItemDetail.Gstinc == true && x.Fee > default(decimal))
                            x.GstFee = CommonFinanceCalculator.CalculateGst(x.Fee, (decimal)gst);
                        else
                            x.GstFee = null;
                        x.CreatedDate = DateTime.UtcNow;
                        x.OrgId = orgId;
                        x.ModifiedBy = loggedInUser;
                        x.StatusId = (short)Status.Active;
                    });
                }
                long id = await _mbsDAL.AddMbsItem(mbsItemDetail);
                if (id > default(int))
                {
                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    apiResponse.Result = id;
                    apiResponse.Message = "Success";
                    return apiResponse;
                }

            }
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            apiResponse.Result = default(int);
            apiResponse.Message = "Failure";
            apiResponse.Errors.Add("New Mbs Item cannot be added at this time.");
            return apiResponse;
        }

        public async Task<ApiResponse<QueryResultList<ListMbsData>>> ListMbsDataBAL(QueryModel queryModel, BaseHttpRequestContext baseHttpRequestContext)
        {
            short parentCategoryId;
            List<long> childMbsList = new List<long>();
            List<long> parentmbsList = new List<long>();
            List<long> fullMbsList = new List<long>();
            QueryResultList<ListMbsData> listMbsData = new();
            List<ListMbsData> listAssignedFlag = new();
            List<long> listMbsItems = new List<long>();
            ApiResponse<QueryResultList<ListMbsData>> apiResponse = new();
            int orgId = baseHttpRequestContext.OrgId;
            long loggedInUser = baseHttpRequestContext.UserId;
            MbsDataFilterModel filterModel = PrepareFilterParametersForMbsItem(queryModel.Filter);
            int ps = queryModel.PageSize;
            int pn = queryModel.PageNumber;
            string st = queryModel.SearchTerm;
            string soo = queryModel.SortOrder;
            string sot = queryModel.SortTerm;

            if (filterModel is not null && filterModel.SearchAll is not null && filterModel.SearchAll == true)
            {
                if (st != null)
                {
                    listMbsItems = await _mbsDAL.GetListofMbsItemsBasedOnST(orgId, st, null, null);
                }
                //string trimStart = queryModel.Filter.Trim('{');
                //string trimEnd = trimStart.Trim('}');
                //string filter = "{" + trimEnd + ",ListMbsItemsOnSearch:[" + string.Join(",", listMbsItems) + "],SearchAll:true}";
                //string encodedFilter = Base64EncoderHelper.EncodeQueryModelFilterParam(filter);//Base64EncoderHelper.EncodeBase64(filter).Replace("+", "-").Replace("/", "_");
                //string medicalScheduleApiUrl = _appSettings.ApiUrls["MedicalScheduleServiceUrl"] + "/MedicalSchedule/mbs?pn=" + pn + "&ps=" + ps + "&st=" + st + "&soo=" + soo + "&sot=" + sot + "&f=" + encodedFilter;
                //RestClient restClient = new RestClient(medicalScheduleApiUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
                //var medicalScheduleApiResponse = await restClient.GetAsync<ApiResponse<QueryResultList<ListMbsData>>>(medicalScheduleApiUrl);
                // Prepare encoded filter
                string trimStart = queryModel.Filter.Trim('{');
                string trimEnd = trimStart.Trim('}');
                string rawFilter = "{" + trimEnd + ",ListMbsItemsOnSearch:[" + string.Join(",", listMbsItems) + "],SearchAll:true}";
                string encodedFilter = Base64EncoderHelper.EncodeQueryModelFilterParam(rawFilter);

                // Replace the filter in the queryModel with the encoded one
                queryModel.Filter = encodedFilter;

                // Call internal method instead of external API
                var medicalScheduleApiResponse = await _medicalScheduleMbsBAL.GetMbsDataListBAL(baseHttpRequestContext, queryModel);
                if (medicalScheduleApiResponse.StatusCode == StatusCodes.Status200OK && medicalScheduleApiResponse.Result is not null && medicalScheduleApiResponse.Result.CurrentCount > 0)
                {
                    listMbsData = medicalScheduleApiResponse.Result;
                }
                //listMbsData = await _mbsDAL.ListMbsDataDAL(orgId, queryModel, filterModel);
            }
            else if (filterModel is not null && filterModel.EpisodeCateogoriesIdLvl3 is not null && filterModel.EpisodeCateogoriesIdLvl3.Any())
            {
                List<long> listItems = new List<long>();
                parentCategoryId = await _mbsDAL.GetParentCategoryId(orgId, (short)filterModel.EpisodeCateogoriesIdLvl3[0]);
                childMbsList = await _mbsDAL.GetListofChildMbs(orgId, (short)filterModel.EpisodeCateogoriesIdLvl3[0], 3);
                parentmbsList = await _mbsDAL.GetListofParentUnassignedMbs(orgId, parentCategoryId, 3, listItems);             
                fullMbsList.AddRange(childMbsList);
                fullMbsList.AddRange(parentmbsList);
                List<long> itemList = new();

                if (fullMbsList is not null && fullMbsList.Any()) {
                    if (queryModel.SearchTerm != null)
                    {
                        listMbsItems = await _mbsDAL.GetListofMbsItemsBasedOnST(orgId, queryModel.SearchTerm, (short)filterModel.EpisodeCateogoriesIdLvl3[0], 3);
                        foreach (long itemNum in listMbsItems)
                        {
                            if (fullMbsList.Contains(itemNum))
                            {
                                itemList.Add(itemNum);
                            }
                        }
                    }
                    else
                    {
                        itemList = fullMbsList;
                    }
                    //string trimStart = queryModel.Filter.Trim('{');
                    //string trimEnd = trimStart.Trim('}');
                    //string filter = "{" + trimEnd + ",ListMbsItemsOnSearch:[" + string.Join(",", itemList) + "]}";
                    //string encodedFilter = Base64EncoderHelper.EncodeQueryModelFilterParam(filter);
                    //string medicalScheduleApiUrl = _appSettings.ApiUrls["MedicalScheduleServiceUrl"] + "/MedicalSchedule/mbs?pn=" + pn + "&ps=" + ps + "&st=" + st + "&soo=" + soo + "&sot=" + sot + "&f=" + encodedFilter;
                    //RestClient restClient = new RestClient(medicalScheduleApiUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
                    //var medicalScheduleMbsApiResponse = await restClient.GetAsync<ApiResponse<QueryResultList<ListMbsData>>>(medicalScheduleApiUrl);

                    // 👇 Encode and inject new filter
                    string trimStart = queryModel.Filter.Trim('{');
                    string trimEnd = trimStart.Trim('}');
                    string rawFilter = "{" + trimEnd + ",ListMbsItemsOnSearch:[" + string.Join(",", itemList) + "]}";
                    string encodedFilter = Base64EncoderHelper.EncodeQueryModelFilterParam(rawFilter);

                    queryModel.Filter = encodedFilter;

                    // 👇 Direct internal BAL method call
                    var medicalScheduleMbsApiResponse = await _medicalScheduleMbsBAL.GetMbsDataListBAL(baseHttpRequestContext, queryModel);
                    if (medicalScheduleMbsApiResponse.StatusCode == StatusCodes.Status200OK && medicalScheduleMbsApiResponse.Result is not null && medicalScheduleMbsApiResponse.Result.CurrentCount > 0)
                    {
                        listMbsData = medicalScheduleMbsApiResponse.Result;
                    }
                    //listMbsData = await _mbsDAL.ListMasterMbs(orgId, queryModel, fullMbsList);
                listAssignedFlag = UpdateAssignedValues((List<ListMbsData>)listMbsData.ItemRecords, childMbsList, parentmbsList);
                }
                listMbsData.ItemRecords = listAssignedFlag;

            }
            else if (filterModel is not null && filterModel.EpisodeCateogoriesIdLvl2 is not null && filterModel.EpisodeCateogoriesIdLvl2.Any())
            {
                List<long> listItems = new List<long>();
                parentCategoryId = await _mbsDAL.GetParentCategoryId(orgId, (short)filterModel.EpisodeCateogoriesIdLvl2[0]);
                childMbsList = await _mbsDAL.GetListofChildMbs(orgId, (short)filterModel.EpisodeCateogoriesIdLvl2[0], 2);
                string medicalScheduleApiUrl = _appSettings.ApiUrls["MedicalScheduleServiceUrl"] + "/MedicalSchedule/mbsClinicalCategoryData/" + parentCategoryId;
                RestClient restClient = new RestClient(medicalScheduleApiUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
                var medicalScheduleApiResponse = await restClient.GetAsync<ApiResponse<List<long>>>(medicalScheduleApiUrl);
                if (medicalScheduleApiResponse.StatusCode == StatusCodes.Status200OK && medicalScheduleApiResponse.Result is not null && medicalScheduleApiResponse.Result.Count > 0)
                {
                    listItems = medicalScheduleApiResponse.Result;
                }
                parentmbsList = await _mbsDAL.GetListofParentUnassignedMbs(orgId, parentCategoryId, 2, listItems);
                fullMbsList.AddRange(childMbsList);
                fullMbsList.AddRange(parentmbsList);
                List<long> itemList = new(); 
                if (fullMbsList is not null && fullMbsList.Any())
                {

                    if (queryModel.SearchTerm != null)
                    {
                        listMbsItems = await _mbsDAL.GetListofMbsItemsBasedOnST(orgId, queryModel.SearchTerm, (short)filterModel.EpisodeCateogoriesIdLvl2[0], 2);
                        foreach (long itemNum in listMbsItems)
                        {
                            if (fullMbsList.Contains(itemNum))
                            {
                                itemList.Add(itemNum);
                            }
                        }
                    }
                    else
                    {
                        itemList = fullMbsList;
                    }
                    //string trimStart = queryModel.Filter.Trim('{');
                    //string trimEnd = trimStart.Trim('}');
                    //string filter = "{" + trimEnd + ",ListMbsItemsOnSearch:[" + string.Join(",", itemList) + "]}";
                    //string encodedFilter = Base64EncoderHelper.EncodeQueryModelFilterParam(filter);
                    //medicalScheduleApiUrl = _appSettings.ApiUrls["MedicalScheduleServiceUrl"] + "/MedicalSchedule/mbs?pn=" + pn + "&ps=" + ps + "&st=" + st + "&soo=" + soo + "&sot=" + sot + "&f=" + encodedFilter;
                    //restClient = new RestClient(medicalScheduleApiUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
                    //var medicalScheduleMbsApiResponse = await restClient.GetAsync<ApiResponse<QueryResultList<ListMbsData>>>(medicalScheduleApiUrl);
                    string trimStart = queryModel.Filter.Trim('{');
                    string trimEnd = trimStart.Trim('}');

                    // Construct raw filter object
                    string rawFilter = "{" + trimEnd + ",ListMbsItemsOnSearch:[" + string.Join(",", itemList) + "]}";

                    // Encode it into Base64
                    queryModel.Filter = Base64EncoderHelper.EncodeQueryModelFilterParam(rawFilter);

                    // Now call internal BAL method correctly
                    var medicalScheduleMbsApiResponse = await _medicalScheduleMbsBAL.GetMbsDataListBAL(baseHttpRequestContext, queryModel);


                    if (medicalScheduleMbsApiResponse.StatusCode == StatusCodes.Status200OK && medicalScheduleMbsApiResponse.Result is not null && medicalScheduleMbsApiResponse.Result.CurrentCount > 0)
                    {
                        listMbsData = medicalScheduleMbsApiResponse.Result;
                    }
                    //listMbsData = await _mbsDAL.ListMasterMbs(orgId, queryModel, fullMbsList);
                    if(listMbsData.ItemRecords != null)
                    {
                        listAssignedFlag = UpdateAssignedValues((List<ListMbsData>)listMbsData.ItemRecords, childMbsList, parentmbsList);
                    }
                    else
                    {
                        listAssignedFlag = null;
                    }
                }
                listMbsData.ItemRecords = listAssignedFlag;

            }
            else if (filterModel is not null && filterModel.EpisodeCateogoriesIdLvl1 is not null && filterModel.EpisodeCateogoriesIdLvl1.Any())
            {
                //childMbsList = await _mbsDAL.GetListofChildMbs(orgId, (short)filterModel.EpisodeCateogoriesIdLvl1[0], 1);
                //if(childMbsList is not null && childMbsList.Any()) { 
                //listMbsData = await _mbsDAL.ListMasterMbs(orgId, queryModel, childMbsList);
                //}
                if (st != null)
                {
                    listMbsItems = await _mbsDAL.GetListofMbsItemsBasedOnST(orgId, st, (short)filterModel.EpisodeCateogoriesIdLvl1[0], 1);
                }
                //string trimStart = queryModel.Filter.Trim('{');
                //string trimEnd = trimStart.Trim('}');
                //string filter = "{" + trimEnd + ",ListMbsItemsOnSearch:[" + string.Join(",", listMbsItems) + "]}";
                //string encodedFilter = Base64EncoderHelper.EncodeQueryModelFilterParam(filter);//Base64EncoderHelper.EncodeBase64(filter).Replace("+", "-").Replace("/", "_");
                //string medicalScheduleApiUrl = _appSettings.ApiUrls["MedicalScheduleServiceUrl"] + "/MedicalSchedule/mbs?pn=" + pn + "&ps=" + ps + "&st=" + st + "&soo=" + soo + "&sot=" + sot + "&f=" + encodedFilter;
                //RestClient restClient = new RestClient(medicalScheduleApiUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
                //var medicalScheduleApiResponse = await restClient.GetAsync<ApiResponse<QueryResultList<ListMbsData>>>(medicalScheduleApiUrl);
                string trimStart = queryModel.Filter.Trim('{');
                string trimEnd = trimStart.Trim('}');
                string rawFilter = "{" + trimEnd + ",ListMbsItemsOnSearch:[" + string.Join(",", listMbsItems) + "]}";
                string encodedFilter = Base64EncoderHelper.EncodeQueryModelFilterParam(rawFilter);

                // 👇 Assign back to queryModel
                queryModel.Filter = encodedFilter;

                // 👇 Call BAL directly
                var medicalScheduleApiResponse = await _medicalScheduleMbsBAL.GetMbsDataListBAL(baseHttpRequestContext, queryModel);
                
                if (medicalScheduleApiResponse.StatusCode == StatusCodes.Status200OK && medicalScheduleApiResponse.Result is not null && medicalScheduleApiResponse.Result.CurrentCount > 0)
                {
                    listMbsData = medicalScheduleApiResponse.Result;
                }

            } 
            else
            {
                if (st != null)
                {
                    listMbsItems = await _mbsDAL.GetListofMbsItemsBasedOnST(orgId, st, null, null);
                }
                //string trimStart = queryModel.Filter.Trim('{');
                //string trimEnd = trimStart.Trim('}');
                //string filter = "{" + trimEnd + ",ListMbsItemsOnSearch:[" + string.Join(",", listMbsItems) + "]}";
                //string encodedFilter = Base64EncoderHelper.EncodeQueryModelFilterParam(filter);//Base64EncoderHelper.EncodeBase64(filter).Replace("+", "-").Replace("/", "_");
                //string medicalScheduleApiUrl = _appSettings.ApiUrls["MedicalScheduleServiceUrl"] + "/MedicalSchedule/mbs?pn=" + pn + "&ps=" + ps + "&st=" + st + "&soo=" + soo + "&sot=" + sot + "&f=" + encodedFilter;
                //RestClient restClient = new RestClient(medicalScheduleApiUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
                //var medicalScheduleApiResponse = await restClient.GetAsync<ApiResponse<QueryResultList<ListMbsData>>>(medicalScheduleApiUrl, queryModel);
                string trimStart = queryModel.Filter.Trim('{');
                string trimEnd = trimStart.Trim('}');
                string rawFilter = "{" + trimEnd + ",ListMbsItemsOnSearch:[" + string.Join(",", listMbsItems) + "]}";
                string encodedFilter = Base64EncoderHelper.EncodeQueryModelFilterParam(rawFilter);

                // 👇 Replace the filter on the QueryModel with the encoded version
                queryModel.Filter = encodedFilter;

                // 👇 Direct method call instead of API call
                var medicalScheduleApiResponse = await _medicalScheduleMbsBAL.GetMbsDataListBAL(baseHttpRequestContext, queryModel);
                if (medicalScheduleApiResponse.StatusCode == StatusCodes.Status200OK && medicalScheduleApiResponse.Result is not null && medicalScheduleApiResponse.Result.CurrentCount > 0)
                {
                    listMbsData = medicalScheduleApiResponse.Result;
                }
                //listMbsData = await _mbsDAL.ListMbsDataDAL(orgId, queryModel, filterModel);

            }
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result = listMbsData;
            return apiResponse;
        }

        private List<ListMbsData> UpdateAssignedValues(List<ListMbsData> listMbsData, List<long> childMbsList,List<long> parentmbsList)
        {
            foreach (var mbsData in listMbsData)
            {
                if (childMbsList.Contains(mbsData.ItemNum))
                {
                    mbsData.IsAssigned = true;
                }
                else
                {
                    mbsData.IsAssigned = false;

                }
            }
            return listMbsData;

        }

        private MbsDataFilterModel PrepareFilterParametersForMbsItem(string filter)
        {
            if (!(String.IsNullOrEmpty(filter)))
            {
                return JsonConvert.DeserializeObject<MbsDataFilterModel>(filter);
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// Method to list Clinical categories
        /// </summary>
        /// <param name="queryModel"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<QueryResultList<MbsCategoryList>>> ListClinicalCategories(QueryModel queryModel, BaseHttpRequestContext baseHttpRequestContext)
         {
            ApiResponse<QueryResultList<MbsCategoryList>> apiResponse = new();
            QueryResultList<MbsCategoryList> queryList = new QueryResultList<MbsCategoryList>();
            List<MbsCategoryList> lstLvl1 = new();
            int orgId = baseHttpRequestContext.OrgId;
            long loggedInUser = baseHttpRequestContext.UserId;
            var token = baseHttpRequestContext.BearerToken;
            string interServiceToken = baseHttpRequestContext.InterServiceToken;
            int ps = queryModel.PageSize;
            int pn = queryModel.PageNumber;
            string st = queryModel.SearchTerm;
            string soo = queryModel.SortOrder;
            string sot = queryModel.SortTerm;
            //EpisodeCategoryFilterModel filterModel = PrepareFilterParameters(queryModel.Filter);
            string medicalScheduleApiUrl = _appSettings.ApiUrls["MedicalScheduleServiceUrl"] + "/MedicalSchedule/clinicalCategoryData?pn="+ pn + "&ps="+ ps + "&st="+ st + "&soo="+ soo + "&sot="+ sot;
            RestClient restClient = new RestClient(medicalScheduleApiUrl, null, token, interServiceToken);
            var medicalScheduleApiResponse = await restClient.GetAsync<ApiResponse<QueryResultList<ClinicalCategory>>>(medicalScheduleApiUrl, queryModel);
            List<ClinicalCategory> clinicalCategoriesList = new() ;
            if (medicalScheduleApiResponse.StatusCode == StatusCodes.Status200OK && medicalScheduleApiResponse.Result is not null && medicalScheduleApiResponse.Result.CurrentCount > 0)
            {
                QueryResultList <ClinicalCategory> paginatedList = medicalScheduleApiResponse.Result;
                clinicalCategoriesList = paginatedList.ItemRecords.ToList();
            }
            List<MbsClinicalCategory> activeMbsCategoryList = await _mbsDAL.ListofActiveMbsCategoryDAL(orgId);
            var clinicalQueryList = UpdateStatusFromClientDB(clinicalCategoriesList, activeMbsCategoryList);
            var clinicalQueryListSorted = clinicalQueryList.OrderBy(x => x.StatusId).ThenBy(x => x.Name);
            clinicalQueryList = clinicalQueryListSorted.ToList();
            lstLvl1 = _mapper.Map<List<ClinicalCategory>, List<MbsCategoryList>>(clinicalQueryList);
            if(lstLvl1 is not null && lstLvl1.Any())
            {
                lstLvl1.ForEach(x => x.Level = 1);
            }
            if (!string.IsNullOrEmpty(st))
            {
                List<MbsCategoryList> categoriesListLvl2Lvl3 = new();
                int lvl2lv3ps = queryModel.PageSize - lstLvl1.Count;
                if (lvl2lv3ps > 0)
                {
                    queryModel.PageSize = lvl2lv3ps;
                    categoriesListLvl2Lvl3 = await LstLvl2Lvl3Categories(queryModel, orgId);
                    lstLvl1.AddRange(categoriesListLvl2Lvl3);
                }
            }

           


            if (lstLvl1 != null && lstLvl1.Count >0)
            {
                queryList.ItemRecords = lstLvl1;
                queryList.PageNumber = queryModel.PageNumber;
                queryList.PageSize = queryModel.PageSize;
                queryList.CurrentCount = lstLvl1.Count();
            }
            else
            {
                queryList.ItemRecords = null;
                queryList.CurrentCount = 0;
            }
            queryList.TotalCount = lstLvl1.Count();
            queryList.PageNumber = queryModel.PageNumber;
            queryList.PageSize = queryModel.PageSize;

            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result = queryList;
            return apiResponse;
        }

        private async Task<List<MbsCategoryList>> LstLvl2Lvl3Categories(QueryModel queryModel, int orgId)
        {
            List<MbsCategoryList> lstLvl2Lvl3 = new();
            EpisodeCategoryFilterModel filter = new();
            if (!(String.IsNullOrEmpty(queryModel.Filter)))
            {
                filter = JsonConvert.DeserializeObject<EpisodeCategoryFilterModel>(queryModel.Filter);
                if (filter.Level is null || filter.Level.Count == 0)
                {
                    filter.Level = new List<byte?>() { 2, 3 };
                    QueryResultList<EpisodeCategory> episodeCategoryList = await _episodeDAL.ListEpisodeCategories(orgId, queryModel, filter);
                    if (episodeCategoryList is not null && episodeCategoryList.CurrentCount > 0 && episodeCategoryList.ItemRecords is not null)
                    {
                        lstLvl2Lvl3 = _mapper.Map<List<EpisodeCategory>, List<MbsCategoryList>>(episodeCategoryList.ItemRecords.ToList());
                        lstLvl2Lvl3.ForEach(x => x.StatusId = 0);
                    }
                }

            }  
         
            return lstLvl2Lvl3;
        }

        /// <summary>
        /// Method to edit Episode Item
        /// </summary>
        /// <param name="id"></param>
        /// <param name="mbsItemDetail"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<string>> EditMbsItemDetailsBAL(int id, MbsItemDetail mbsItemDetail, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<string> apiResponse = new();

            int orgId = baseHttpRequestContext.OrgId;
            long loggedInUser = baseHttpRequestContext.UserId;
            MbsItemDetail mbsItemDetailDB = await _mbsDAL.FetchMbsItemFromId(id, orgId);
            if (mbsItemDetailDB is not null)
            {
                List<MbsItemCustomFeeAssoc> removeCustomFeeList = (mbsItemDetailDB.MbsItemCustomFeeAssocs is null) ? null : mbsItemDetailDB.MbsItemCustomFeeAssocs.ToList();
                List<MbsItemCustomFeeAssoc> inputCustomFeeList = (mbsItemDetail.MbsItemCustomFeeAssocs is null) ? null : mbsItemDetail.MbsItemCustomFeeAssocs.ToList();
                inputCustomFeeList.ForEach(x => { x.MbsItemDetailsId = id; x.OrgId = orgId; });
                mbsItemDetail.MbsItemCustomFeeAssocs = null;
                decimal? gst = await _episodeDAL.FetchGstForOrganisation(baseHttpRequestContext.OrgCode, orgId);
                if (mbsItemDetail.Gstinc == true && mbsItemDetail.PrivateFee != null)
                    mbsItemDetail.GstFee = CommonFinanceCalculator.CalculateGst((decimal)mbsItemDetail.PrivateFee, (decimal)gst);
                else
                    mbsItemDetail.GstFee = null;
                using (var transaction = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
                {
                    mbsItemDetail.MbsItemCustomFeeAssocs = EditMbsItemCustomFeeAssocs(removeCustomFeeList, inputCustomFeeList, orgId, loggedInUser, gst, mbsItemDetail.Gstinc);
                    mbsItemDetail.ModifiedDate = DateTime.UtcNow;
                    mbsItemDetail.ModifiedBy = loggedInUser;
                    mbsItemDetail.OrgId = orgId;
                    mbsItemDetail.StatusId = (short)Status.Active;
                    mbsItemDetail.ItemNum = mbsItemDetailDB.ItemNum;

                    await _mbsDAL.UpdateMbsItemDetail(mbsItemDetail);
                    apiResponse.Result = "Success";
                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    apiResponse.Message = "Success";

                    transaction.Complete();
                }
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = null;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("Episode Item not found.");
            }

            return apiResponse;
        }

        private ICollection<MbsItemCustomFeeAssoc> EditMbsItemCustomFeeAssocs(List<MbsItemCustomFeeAssoc> removeCustomFeeList, List<MbsItemCustomFeeAssoc> inputCustomFeeList, int orgId, long loggedInUser, decimal? gst,bool gstInc)
        {
            List<MbsItemCustomFeeAssoc> lstAddUpdCustomFee = new();
            foreach (var customFee in inputCustomFeeList)
            {
                if (customFee.Id > 0)
                {
                    var existingobj = removeCustomFeeList.FirstOrDefault(x => x.Id == customFee.Id);
                    if (customFee.Name != existingobj.Name || customFee.Fee != existingobj.Fee )
                    {
                        if (gstInc == true && customFee.Fee > default(decimal))
                            customFee.GstFee = CommonFinanceCalculator.CalculateGst(customFee.Fee, (decimal)gst);
                        else
                            customFee.GstFee = null;
                        customFee.ModifiedBy = loggedInUser;
                        customFee.ModifiedDate = DateTime.UtcNow;
                        customFee.OrgId = orgId;
                        customFee.StatusId = (short)Status.Active;
                        lstAddUpdCustomFee.Add(customFee);
                    }
                    removeCustomFeeList.Remove(existingobj);
                }
                else
                {
                    if (gstInc == true && customFee.Fee > default(decimal))
                        customFee.GstFee = CommonFinanceCalculator.CalculateGst(customFee.Fee, (decimal)gst);
                    else
                        customFee.GstFee = null;
                    customFee.CreatedDate = DateTime.UtcNow;
                    customFee.OrgId = orgId;
                    customFee.ModifiedBy = loggedInUser;
                    customFee.StatusId = (short)Status.Active;
                    lstAddUpdCustomFee.Add(customFee);
                }
            }
            foreach (var customFee in removeCustomFeeList)
            {
                customFee.ModifiedBy = loggedInUser;
                customFee.ModifiedDate = DateTime.UtcNow;
                customFee.OrgId = orgId;
                customFee.StatusId = (short)Status.Deleted;
            }
            lstAddUpdCustomFee = lstAddUpdCustomFee.Concat(removeCustomFeeList).ToList();
            return lstAddUpdCustomFee;
        }

        /// <summary>
        /// Method to fetch Mbs Item based on Id
        /// </summary>
        /// <param name="itemnumber"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<MbsDataView>> GetMbsDataBAL(long itemnum, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<MbsDataView> apiResponse = new();
            var token = baseHttpRequestContext.BearerToken;
            string interServiceToken = baseHttpRequestContext.InterServiceToken;
            string medicalScheduleApiUrl = _appSettings.ApiUrls["MedicalScheduleServiceUrl"] + "/MedicalSchedule/mbs_data/"+itemnum;
            RestClient restClient = new RestClient(medicalScheduleApiUrl, null, token, interServiceToken);
            var medicalScheduleApiResponse = await restClient.GetAsync<ApiResponse<MbsDataView>>(medicalScheduleApiUrl);
            MbsDataView mbsDataFromDB = null;
            if (medicalScheduleApiResponse.StatusCode == StatusCodes.Status200OK && medicalScheduleApiResponse.Result is not null)
            {
                MbsDataView mbsItem = medicalScheduleApiResponse.Result;
                mbsDataFromDB = await _mbsDAL.GetMbsDataDAL(itemnum, mbsItem, baseHttpRequestContext.OrgId);
            }
            if (mbsDataFromDB is null)
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = null;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("Episode Item not found.");
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";
                apiResponse.Result = mbsDataFromDB;
            }

            return apiResponse;

        }

        /// <summary>
        /// Method to assign category to exixting mbsitem or create new mbsitemdetails
        /// </summary>
        /// <param name="mbsItemDetail"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<string>> AssignCategoryBAL(long itemnum, AssignCategoryInput assignCategoryInput, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<string> apiResponse = new();

            int orgId = baseHttpRequestContext.OrgId;
            long loggedInUser = baseHttpRequestContext.UserId;
            MbsItemDetail mbsItemDetailDB = await _mbsDAL.FetchMbsItemFromItemnum(itemnum, orgId);
            if(assignCategoryInput.EpisodeCateogoriesIdLvl2 is null)
            {
                assignCategoryInput.EpisodeCateogoriesIdLvl3 = null;
            }
            if (mbsItemDetailDB is not null)
            {
                mbsItemDetailDB.ModifiedDate = DateTime.UtcNow;
                mbsItemDetailDB.ModifiedBy = loggedInUser;
                mbsItemDetailDB.EpisodeCateogoriesIdLvl2 = assignCategoryInput.EpisodeCateogoriesIdLvl2;
                mbsItemDetailDB.EpisodeCateogoriesIdLvl3 = assignCategoryInput.EpisodeCateogoriesIdLvl3;

                await _mbsDAL.UpdateMbsItemDetail(mbsItemDetailDB);
                apiResponse.Result = "Success";
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";
                   
            }
            else
            {
                MbsItemDetail mbsItemDetail = new();
                mbsItemDetail.ModifiedBy = loggedInUser;
                mbsItemDetail.ItemNum = itemnum;
                mbsItemDetail.OrgId = orgId;
                mbsItemDetail.CreatedDate = DateTime.UtcNow;
                mbsItemDetail.StatusId = (short)Status.Active;
                mbsItemDetail.EpisodeCateogoriesIdLvl2 = assignCategoryInput.EpisodeCateogoriesIdLvl2;
                mbsItemDetail.EpisodeCateogoriesIdLvl3 = assignCategoryInput.EpisodeCateogoriesIdLvl3;
                mbsItemDetail.EpisodeCateogoriesIdLvl1 = assignCategoryInput.EpisodeCateogoriesIdLvl1;



                long id = await _mbsDAL.AddMbsItem(mbsItemDetail);
                if (id > default(int))
                {
                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    apiResponse.Result = "Success";
                    apiResponse.Message = "Success";
                    return apiResponse;
                }

            }

            return apiResponse;
        }

        /// <summary>
        /// Method to create or update the Mbs category 
        /// </summary>
        /// <param name="mbsInput"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<string>> UpdateMbsCategoryBAL(short categoryid, MbsClinicalCategoryInput mbsInput, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<string> apiResponse = new();

            int orgId = baseHttpRequestContext.OrgId;
            long loggedInUser = baseHttpRequestContext.UserId;
            MbsClinicalCategory mbsCategoryFromDB = await _mbsDAL.FetchMbsCategoryFromId(categoryid, orgId);

            if (mbsCategoryFromDB is not null)
            {
                mbsCategoryFromDB.ModifiedDate = DateTime.UtcNow;
                mbsCategoryFromDB.ModifiedBy = loggedInUser;
                mbsCategoryFromDB.StatusId = mbsInput.StatusId;

                await _mbsDAL.UpdateMbsClinicalCategory(mbsCategoryFromDB);
                apiResponse.Result = "Success";
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";

            }
            else
            {
                MbsClinicalCategory mbsCategory = new();
                mbsCategory.ModifiedBy = loggedInUser;
                mbsCategory.OrgId = orgId;
                mbsCategory.CreatedDate = DateTime.UtcNow;
                mbsCategory.StatusId = (short)Status.Active;
                mbsCategory.Id = categoryid;


                long id = await _mbsDAL.AddMbsCategory(mbsCategory);
                if (id > default(int))
                {
                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    apiResponse.Result = "Success";
                    apiResponse.Message = "Success";
                    return apiResponse;
                }

            }

            return apiResponse;
        }

        private List<ClinicalCategory>  UpdateStatusFromClientDB(List<ClinicalCategory>  clinicalQueryList,List<MbsClinicalCategory> activeMbsCategoryList)
        {
            foreach (var item in clinicalQueryList)
            {
                var categoryInClient = activeMbsCategoryList.FirstOrDefault(x => x.Id == item.Id);
                if(categoryInClient != null)
                {
                    item.StatusId = categoryInClient.StatusId;
                }
                else
                {
                    item.StatusId = (short)Status.Inactive;
                }

            }

            return clinicalQueryList;
        }
        /// <summary>
        /// Fetching lit of Active MBS CLinical Categories
        /// </summary>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<List<MbsClinicalCategory>> ListofActiveMbsCategory(int orgId)
        {
            return await _mbsDAL.ListofActiveMbsCategoryDAL(orgId);
        }


        public async Task<List<MbsItemView>> ListMbsItemFromItemNums(List<long> itemnums, BaseHttpRequestContext baseHttpRequestContext)
        {
            return await _mbsDAL.ListMbsItemFromItemNums(itemnums, baseHttpRequestContext.OrgId);

        }



    }

}


