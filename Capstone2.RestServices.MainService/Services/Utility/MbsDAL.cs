﻿using Capstone2.RestServices.Utility.Context;
using Capstone2.RestServices.Utility.Interfaces;
using Capstone2.RestServices.Utility.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ListMbsData = Capstone2.Shared.Models.Entities.ListMbsData;
using MbsDataFilterModel = Capstone2.Shared.Models.Entities.MbsDataFilterModel;
using MbsDataView = Capstone2.Shared.Models.Entities.MbsDataView;
using MbsItemCustomFeeInfoAssoc = Capstone2.Shared.Models.Entities.MbsItemCustomFeeInfoAssoc;
using MbsItemDetail = Capstone2.Shared.Models.Entities.MbsItemDetail;
using MbsItemView = Capstone2.Shared.Models.Entities.MbsItemView;

namespace Capstone2.RestServices.Utility.Services
{
    public class MbsDAL : IMbsDAL
    {
        public readonly ReadOnlyUtilityDBContext _readOnlyDbContext;
        public readonly UpdatableUtilityDBContext _updatableDBContext;
        //public readonly ReadOnlyDBMasterContext _readOnlyDBMasterContext;
        public MbsDAL(ReadOnlyUtilityDBContext readOnlyDbContext, UpdatableUtilityDBContext updatableDBContext)
        {
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
        }


        /// <summary>
        /// Method to add anew episode item
        /// </summary>
        /// <param name="episodeItemDetail"></param>
        /// <returns></returns>
        public async Task<long> AddMbsItem(MbsItemDetail mbsItemDetail)
        {
            await _updatableDBContext.MbsItemDetails.AddAsync(mbsItemDetail);
            int rows = await _updatableDBContext.SaveChangesAsync();
            if (rows > 0)
            {
                return mbsItemDetail.Id;
            }
            return default(long);
        }

        /// <summary>
        /// Method to fetch parent category id
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="">filterModel</param>
        /// <returns></returns>
        public async Task<short> GetParentCategoryId(int orgId,short cateroryId)
        {
            // get the parent categories ID 
            var  parentcategory = (short)await (from EC in _readOnlyDbContext.EpisodeCategories
                                                where EC.OrgId == orgId && EC.StatusId == (short)Status.Active && EC.Id == cateroryId
                                                select EC.ParentEpisodeCategoriesId).FirstOrDefaultAsync();

            return parentcategory;
        }


        public async Task<List<long>> GetListofChildMbs(int orgId, short childCategoryId,short level)
        {
            List<long> ListofchildMBS  = new();

            if (level == 3)
            {
                 ListofchildMBS = await (from MBI in _readOnlyDbContext.MbsItemDetails
                                                   where MBI.StatusId == (short)Status.Active && (MBI.EpisodeCateogoriesIdLvl3 == childCategoryId)
                                                   select MBI.ItemNum).ToListAsync();
            }
            else if(level == 2)
            {
                 ListofchildMBS = await (from MBI in _readOnlyDbContext.MbsItemDetails
                                                   where MBI.StatusId == (short)Status.Active && (MBI.EpisodeCateogoriesIdLvl2 == childCategoryId)
                                                   select MBI.ItemNum).ToListAsync();
            }
            //else if(level == 1)
            //{
            //     ListofchildMBS = await (from MCC in _readOnlyDBMasterContext.MbsClinicalCategoryData
            //                                       where MCC.StatusId == (short)Status.Active && (MCC.ClinicalCategoryId == childCategoryId)
            //                                       select MCC.ItemNum).ToListAsync();
            //}

            return ListofchildMBS;
        }

        public async Task<List<long>> GetListofMbsItemsBasedOnST(int orgId, string searchTerm, short? categoryId, short? level)
        {
            List<long> ListofMbsItems = new();
            if (level == 3)
            {
                ListofMbsItems = await (from MBI in _readOnlyDbContext.MbsItemDetails
                                        where MBI.StatusId == (short)Status.Active && MBI.UserDefinedDescription.Contains(searchTerm) && MBI.EpisodeCateogoriesIdLvl3 == categoryId
                                        select MBI.ItemNum).ToListAsync();
            }
            if (level == 2)
            {
                ListofMbsItems = await (from MBI in _readOnlyDbContext.MbsItemDetails
                                        where MBI.StatusId == (short)Status.Active && MBI.UserDefinedDescription.Contains(searchTerm) && MBI.EpisodeCateogoriesIdLvl2 == categoryId
                                        select MBI.ItemNum).ToListAsync();
            }
            if (level == 1)
            {
                ListofMbsItems = await (from MBI in _readOnlyDbContext.MbsItemDetails
                                        where MBI.StatusId == (short)Status.Active && MBI.UserDefinedDescription.Contains(searchTerm) && MBI.EpisodeCateogoriesIdLvl1 == categoryId
                                        select MBI.ItemNum).ToListAsync();
            }
            else
            {
                ListofMbsItems = await (from MBI in _readOnlyDbContext.MbsItemDetails
                                        where MBI.StatusId == (short)Status.Active && MBI.UserDefinedDescription.Contains(searchTerm)
                                        select MBI.ItemNum).ToListAsync();
            }
            return ListofMbsItems;
        }

        public async Task<List<long>> GetListofParentUnassignedMbs(int orgId, short parentCategoryId, short level, List<long> masterParentList)
        {
            List<long> ListofParentMBS = new();


            if (level == 3)
            {
                ListofParentMBS = await (from MBI in _readOnlyDbContext.MbsItemDetails
                                        where MBI.StatusId == (short)Status.Active && MBI.EpisodeCateogoriesIdLvl2 == parentCategoryId && MBI.EpisodeCateogoriesIdLvl3 == null
                                        select MBI.ItemNum).ToListAsync();
            }
            else if (level == 2)
            {
               var  ListofClientParentMBS = await (from MBI in _readOnlyDbContext.MbsItemDetails
                                        where MBI.StatusId == (short)Status.Active && MBI.EpisodeCateogoriesIdLvl1 == parentCategoryId && MBI.EpisodeCateogoriesIdLvl2 != null
                                        select MBI.ItemNum).ToListAsync();

                ListofParentMBS = masterParentList.Except(ListofClientParentMBS).ToList();
            }

            return ListofParentMBS;
        }

        //public async Task<QueryResultList<ListMbsData>> ListMasterMbs(int orgId, QueryModel queryModel,List<long> mbsIdList)
        //{
        //    IQueryable<ListMbsData> mbsItemQuery = from MBS in _readOnlyDBMasterContext.MbsData
        //                                           where MBS.StatusId == (short)Status.Active && mbsIdList.Contains(MBS.ItemNum)
        //                                           select new ListMbsData
        //                                           {
        //                                               Id = MBS.Id,
        //                                               ItemNum = MBS.ItemNum,
        //                                               Description = MBS.Description

        //                                           };

        //    if (!(string.IsNullOrWhiteSpace(queryModel.SearchTerm)))
        //    {
        //        mbsItemQuery = SearchMbsItems(mbsItemQuery, queryModel.SearchTerm);

        //    }

        //    if (!(string.IsNullOrWhiteSpace(queryModel.SortOrder)) && !(string.IsNullOrWhiteSpace(queryModel.SortTerm)))
        //    {
        //        mbsItemQuery = SortMbsItems(mbsItemQuery, queryModel.SortTerm, queryModel.SortOrder);
        //    }
        //    QueryResultList<ListMbsData> paginatedList = await PaginatedResultListForItems(mbsItemQuery, queryModel);

        //    return paginatedList;

        //}






        /// <summary>
        /// Method to list Mbs Data Items
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="queryModel"></param>
        /// <param name="filterModel"></param>
        /// <returns></returns>
        //public async Task<QueryResultList<ListMbsData>> ListMbsDataDAL(int orgId, QueryModel queryModel, MbsDataFilterModel filterModel)
        //{
        //    IQueryable<ListMbsData> mbsItemQuery = from MBS in _readOnlyDBMasterContext.MbsData
        //                                           where MBS.StatusId == (short)Status.Active
        //                                            select new ListMbsData
        //                                            {
        //                                                Id = MBS.Id,
        //                                                ItemNum = MBS.ItemNum,
        //                                                Description = MBS.Description
                                                                   
        //                                            };
        //    if (!(string.IsNullOrWhiteSpace(queryModel.SearchTerm)))
        //    {
        //        mbsItemQuery = SearchMbsItems(mbsItemQuery, queryModel.SearchTerm);

        //    }

        //    if (!(string.IsNullOrWhiteSpace(queryModel.SortOrder)) && !(string.IsNullOrWhiteSpace(queryModel.SortTerm)))
        //    {
        //        mbsItemQuery = SortMbsItems(mbsItemQuery, queryModel.SortTerm, queryModel.SortOrder);
        //    }
        //    QueryResultList<ListMbsData> paginatedList = await PaginatedResultListForItems(mbsItemQuery, queryModel);

        //    return paginatedList;
        //}

        private async Task<QueryResultList<ListMbsData>> PaginatedResultListForItems(IQueryable<ListMbsData> mbsItemQuery, QueryModel queryModel)
        {
            QueryResultList<ListMbsData> queryList = new QueryResultList<ListMbsData>();
            List<ListMbsData> paginatedList = null;
            if (queryModel.PageNumber > 0 && queryModel.PageSize > 0)
            {
                if (mbsItemQuery.Any())
                {
                    paginatedList = await mbsItemQuery.Skip((queryModel.PageNumber - 1) * queryModel.PageSize)
                 .Take(queryModel.PageSize).ToListAsync();
                }
            }

            if (paginatedList != null)
            {
                queryList.ItemRecords = paginatedList;
                queryList.PageNumber = queryModel.PageNumber;
                queryList.PageSize = queryModel.PageSize;
                queryList.CurrentCount = paginatedList.Count();
            }
            else
            {
                queryList.ItemRecords = null;
                queryList.CurrentCount = 0;
            }
            queryList.TotalCount = mbsItemQuery.Count();
            queryList.PageNumber = queryModel.PageNumber;
            queryList.PageSize = queryModel.PageSize;

            return queryList;
        }

        private IQueryable<ListMbsData> SortMbsItems(IQueryable<ListMbsData> mbsItemQuery, string sortTerm, string sortOrder)
        {
            switch (sortTerm.ToLower())
            {

                case "id":

                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            mbsItemQuery = mbsItemQuery.OrderBy(x => x.Id);
                        }
                        else
                        {

                            mbsItemQuery = mbsItemQuery.OrderByDescending(x => x.Id);

                        }
                        break;
                    }
                case "itemnum":

                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            mbsItemQuery = mbsItemQuery.OrderBy(x => x.ItemNum);
                        }
                        else
                        {

                            mbsItemQuery = mbsItemQuery.OrderByDescending(x => x.ItemNum);

                        }
                        break;
                    }


                default:
                    mbsItemQuery = mbsItemQuery.OrderBy(x => x.ItemNum);

                    break;
            }
            return mbsItemQuery;
        }

        private IQueryable<ListMbsData> SearchMbsItems(IQueryable<ListMbsData> mbsItemQuery, string searchTerm)
        {
            return mbsItemQuery.Where(s => s.Description.Contains(searchTerm) || s.ItemNum.ToString() == searchTerm);
        }

        //public async Task<List<ClinicalCategory>> ListClinicalCategoriesDAL(int orgId, QueryModel queryModel)
        //{
        //    IQueryable<ClinicalCategory> clinicalCategoryQuery = from CC in _readOnlyDBMasterContext.ClinicalCategories
        //                                                       where  CC.StatusId == (short)Status.Active
        //                                                       select CC;

        //    if (!(string.IsNullOrWhiteSpace(queryModel.SearchTerm)))
        //    {
        //        clinicalCategoryQuery = SearchClinicalCategories(clinicalCategoryQuery, queryModel.SearchTerm);

        //    }

        //  /*  if (!(string.IsNullOrWhiteSpace(queryModel.SortOrder)) && !(string.IsNullOrWhiteSpace(queryModel.SortTerm)))
        //    {
        //        clinicalCategoryQuery = SortClinicaleCategories(clinicalCategoryQuery, queryModel.SortTerm, queryModel.SortOrder);
        //    }*/

        //    List<ClinicalCategory> paginatedList = await PaginatedResultList(clinicalCategoryQuery, queryModel);

        //    return paginatedList;


        //}

        private IQueryable<ClinicalCategory> SearchClinicalCategories(IQueryable<ClinicalCategory> clinicalCategoryQuery, string searchTerm)
        {
            return clinicalCategoryQuery.Where(s => s.Name.Contains(searchTerm));
        }

        private IQueryable<ClinicalCategory> SortClinicaleCategories(IQueryable<ClinicalCategory> clinicalCategoryQuery, string sortTerm, string sortOrder)
        {
            switch (sortTerm.ToLower())
            {

                case "id":

                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            clinicalCategoryQuery = clinicalCategoryQuery.OrderBy(x => x.Id);
                        }
                        else
                        {

                            clinicalCategoryQuery = clinicalCategoryQuery.OrderByDescending(x => x.Id);

                        }
                        break;
                    }
                case "name":

                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            clinicalCategoryQuery = clinicalCategoryQuery.OrderBy(x => x.Name);
                        }
                        else
                        {

                            clinicalCategoryQuery = clinicalCategoryQuery.OrderByDescending(x => x.Name);

                        }
                        break;
                    }
                default:
                    clinicalCategoryQuery = clinicalCategoryQuery.OrderByDescending(x => x.Id);

                    break;
            }
            return clinicalCategoryQuery;

        }

        private async Task<List<ClinicalCategory>> PaginatedResultList(IQueryable<ClinicalCategory> clinicalCategoryQuery, QueryModel queryModel)
        {
            List<ClinicalCategory> paginatedList = null;
            if (queryModel.PageNumber > 0 && queryModel.PageSize > 0)
            {
                if (clinicalCategoryQuery.Any())
                {
                    paginatedList = await clinicalCategoryQuery.Skip((queryModel.PageNumber - 1) * queryModel.PageSize)
                 .Take(queryModel.PageSize).ToListAsync();
                }
            }

            return paginatedList;

        }

        /// <summary>
        /// Method to fetch Mbs item from id
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<MbsItemDetail> FetchMbsItemFromId(int id, int orgId)
        {
            return await _readOnlyDbContext.MbsItemDetails.Where(x => x.Id == id && x.OrgId == orgId).Include(x => x.MbsItemCustomFeeAssocs).AsNoTracking().FirstOrDefaultAsync();
        }

        /// <summary>
        /// Method to update Mbs item
        /// </summary>
        /// <param name="mbsItemDetail"></param>
        /// <returns></returns>
        public async Task<int> UpdateMbsItemDetail(MbsItemDetail mbsItemDetail)
        {
            _updatableDBContext.MbsItemDetails.Update(mbsItemDetail);
            return await _updatableDBContext.SaveChangesAsync();
        }


        /// <summary>
        /// Method to fetch Mbs Data Detail
        /// </summary>
        /// <param name="itemnum"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<MbsDataView> GetMbsDataDAL(long itemnum, MbsDataView mbsItem, int orgId)
        {

            mbsItem.MbsItemView = await  FetchMbsItemFromItemNum(itemnum, orgId);
            return mbsItem;

        }

        /// <summary>
        /// Method to fetch Mbs item from item number
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        private async Task<MbsItemView> FetchMbsItemFromItemNum(long itemnumber, int orgId)
        {
            var clientMbsResult = await (from MBSI in _readOnlyDbContext.MbsItemDetails
                                      where MBSI.ItemNum == itemnumber
                                      join EC2 in _readOnlyDbContext.EpisodeCategories on MBSI.EpisodeCateogoriesIdLvl2 equals EC2.Id into JC
                                      from EC2 in JC.DefaultIfEmpty()
                                      join EC3 in _readOnlyDbContext.EpisodeCategories on MBSI.EpisodeCateogoriesIdLvl3 equals EC3.Id into JC2
                                      from EC3 in JC2.DefaultIfEmpty()

                                      select new MbsItemView
                                      {
                                          Id = MBSI.Id,
                                          EpisodeCateogoriesIdLvl1 = MBSI.EpisodeCateogoriesIdLvl1,
                                          EpisodeCateogoriesIdLvl2 = MBSI.EpisodeCateogoriesIdLvl2,
                                          EpisodeCateogoriesIdLvl3 = MBSI.EpisodeCateogoriesIdLvl3,
                                          ECLvl2Name = EC2.Name,
                                          ECLvl3Name = EC3.Name,
                                          UserDefinedDescription = MBSI.UserDefinedDescription,
                                          Gstinc = MBSI.Gstinc,
                                          PrivateFee = MBSI.PrivateFee,
                                          GstFee = MBSI.GstFee,
                                          ItemNum = MBSI.ItemNum,
                                          OrgId = MBSI.OrgId,
                                          MbsItemCustomFeeAssocs = (ICollection<MbsItemCustomFeeInfoAssoc>)
                                                    (from CFA in _readOnlyDbContext.MbsItemCustomFeeAssocs
                                                     where CFA.OrgId == orgId && CFA.MbsItemDetailsId == MBSI.Id && CFA.StatusId == (short)Status.Active
                                                     select new MbsItemCustomFeeInfoAssoc
                                                     {
                                                         Id = CFA.Id,
                                                         MbsItemDetailsId = CFA.MbsItemDetailsId,
                                                         Fee = CFA.Fee,
                                                         Name = CFA.Name,
                                                         GstFee = CFA.GstFee,
                                                         OrgId = CFA.OrgId,
                                                         CreatedDate = CFA.CreatedDate,
                                                         ModifiedBy = CFA.ModifiedBy,
                                                         StatusId = CFA.StatusId
                                                     
                                                     })
                                      }).FirstOrDefaultAsync();


            return clientMbsResult;


        }

        /// <summary>
        /// Method to fetch Mbs item from item numbers
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<List<MbsItemView>> ListMbsItemFromItemNums(List<long> itemNums, int orgId)
        {
            var clientMbsResult = await (from MBSI in _readOnlyDbContext.MbsItemDetails
                                         where itemNums.Contains(MBSI.ItemNum) && MBSI.OrgId==orgId
                                         //join EC2 in _readOnlyDbContext.EpisodeCategories on MBSI.EpisodeCateogoriesIdLvl2 equals EC2.Id into JC
                                         //from EC2 in JC.DefaultIfEmpty()
                                         //join EC3 in _readOnlyDbContext.EpisodeCategories on MBSI.EpisodeCateogoriesIdLvl3 equals EC3.Id into JC2
                                         //from EC3 in JC2.DefaultIfEmpty()

                                         select new MbsItemView
                                         {
                                             Id = MBSI.Id,
                                             EpisodeCateogoriesIdLvl1 = MBSI.EpisodeCateogoriesIdLvl1,
                                             EpisodeCateogoriesIdLvl2 = MBSI.EpisodeCateogoriesIdLvl2,
                                             EpisodeCateogoriesIdLvl3 = MBSI.EpisodeCateogoriesIdLvl3,
                                             //ECLvl2Name = EC2.Name,
                                             //ECLvl3Name = EC3.Name,
                                             UserDefinedDescription = MBSI.UserDefinedDescription,
                                             Gstinc = MBSI.Gstinc,
                                             PrivateFee = MBSI.PrivateFee,
                                             GstFee = MBSI.GstFee,
                                             ItemNum = MBSI.ItemNum,
                                             OrgId = MBSI.OrgId,
                                             MbsItemCustomFeeAssocs = (ICollection<MbsItemCustomFeeInfoAssoc>)
                                                       (from CFA in _readOnlyDbContext.MbsItemCustomFeeAssocs
                                                        where CFA.OrgId == orgId && CFA.MbsItemDetailsId == MBSI.Id && CFA.StatusId == (short)Status.Active
                                                        select new MbsItemCustomFeeInfoAssoc
                                                        {
                                                            Id = CFA.Id,
                                                            MbsItemDetailsId = CFA.MbsItemDetailsId,
                                                            Fee = CFA.Fee,
                                                            Name = CFA.Name,
                                                            GstFee = CFA.GstFee,
                                                            OrgId = CFA.OrgId,
                                                            CreatedDate = CFA.CreatedDate,
                                                            ModifiedBy = CFA.ModifiedBy,
                                                            StatusId = CFA.StatusId

                                                        })
                                         }).ToListAsync();


            return clientMbsResult;


        }
        /// <summary>
        /// Method to fetch Mbs item from itemnum
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="itemnum"></param>
        /// <returns></returns>
        public async Task<MbsItemDetail> FetchMbsItemFromItemnum(long itemnum, int orgId)
        {
            return await _readOnlyDbContext.MbsItemDetails.Where(x => x.ItemNum == itemnum && x.OrgId == orgId ).AsNoTracking().FirstOrDefaultAsync();
        }

        /// <summary>
        /// Method to fetch mbs category item from id
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="itemnum"></param>
        /// <returns></returns>
        public async Task<MbsClinicalCategory> FetchMbsCategoryFromId(short categoryId, int orgId)
        {
            return await _readOnlyDbContext.MbsClinicalCategories.Where(x => x.Id == categoryId && x.OrgId == orgId).AsNoTracking().FirstOrDefaultAsync();
        }

        /// <summary>
        /// Method to update Mbs Category
        /// </summary>
        /// <param name="mbsCategory"></param>
        /// <returns></returns>
        public async Task<int> UpdateMbsClinicalCategory(MbsClinicalCategory mbsCategory)
        {
            _updatableDBContext.MbsClinicalCategories.Update(mbsCategory);
            return await _updatableDBContext.SaveChangesAsync();
        }

        /// <summary>
        /// Method to add a new mbs category
        /// </summary>
        /// <param name="mbsCategory"></param>
        /// <returns></returns>
        public async Task<short> AddMbsCategory(MbsClinicalCategory mbsCategory)
        {
            await _updatableDBContext.MbsClinicalCategories.AddAsync(mbsCategory);
            int rows = await _updatableDBContext.SaveChangesAsync();
            if (rows > 0)
            {
                return mbsCategory.Id;
            }
            return default(short);
        }

        /// <summary>
        /// Method to get the list of active mbs clinical categories 
        /// </summary>
        /// <param name="mbsCategory"></param>
        /// <returns></returns>


        public async Task<List<MbsClinicalCategory>> ListofActiveMbsCategoryDAL(int orgId)
        {

            List<MbsClinicalCategory> clinicalCategoryList = new();
            clinicalCategoryList = await   (from mbsCC in _readOnlyDbContext.MbsClinicalCategories
                                            where mbsCC.StatusId == (short)Status.Active && mbsCC.OrgId == orgId
                                            select mbsCC).ToListAsync();

            return clinicalCategoryList;


        }



    }
}
