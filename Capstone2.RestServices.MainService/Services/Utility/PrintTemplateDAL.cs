﻿using Capstone2.RestServices.Utility.Context;
using Capstone2.RestServices.Utility.Interfaces;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Text.RegularExpressions;

namespace Capstone2.RestServices.Utility.Services
{
    public class PrintTemplateDAL : IPrintTemplateDAL
    {
        public readonly ReadOnlyUtilityDBContext _readOnlyDbContext;
        public readonly UpdatableUtilityDBContext _updatableDBContext;
        public PrintTemplateDAL(ReadOnlyUtilityDBContext readOnlyDbContext, UpdatableUtilityDBContext updatableDBContext)
        {
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
        }

        /// <summary>
        /// Method to add PrintTemplate to PrintTemplate table
        /// </summary>
        /// <param name="PrintTemplate"></param>
        /// <returns></returns>
        public async Task<long> AddPrintTemplateAsync(PrintTemplate printTemplate)
        {
            await _updatableDBContext.PrintTemplate.AddAsync(printTemplate);
            int rows = await _updatableDBContext.SaveChangesAsync();
            return rows > 0 ? printTemplate.Id : 0;
        }

        /// <summary>
        /// Method to retrieve list of PrintTemplates
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="loggedInUser"></param>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        public async Task<QueryResultList<PrintTemplateList>> ListPrintTemplate(int orgId, long loggedInUser, QueryModel queryModel)
        {
            var PrintTemplateQuery = (from CN in _readOnlyDbContext.PrintTemplate
                                            join UD in _readOnlyDbContext.UserDetails on CN.CreatedBy equals UD.Id
                                            where CN.OrgId == orgId
                                            select new PrintTemplateList
                                            {
                                                Id = CN.Id,
                                                OrgId = CN.OrgId,
                                                Name = CN.Name,
                                                StatusId = CN.StatusId,
                                                CreatedBy = CN.CreatedBy,
                                                CreatedDate = CN.CreatedDate,
                                                CreatedByFirstName = UD.FirstName,
                                                CreatedBySurName = UD.SurName
                                            });
            if (!(string.IsNullOrWhiteSpace(queryModel.SearchTerm)))
            {
                PrintTemplateQuery = SearchPrintTemplates(PrintTemplateQuery, queryModel.SearchTerm);
            }
            if (!(string.IsNullOrWhiteSpace(queryModel.SortOrder)) && !(string.IsNullOrWhiteSpace(queryModel.SortTerm)))
            {
                PrintTemplateQuery = SortPrintTemplate(PrintTemplateQuery, queryModel.SortTerm, queryModel.SortOrder);
            }

            var paginatedList = await CreatePaginatedListAsync(PrintTemplateQuery, queryModel);

            var queryList = new QueryResultList<PrintTemplateList>();
            if (paginatedList != null)
            {
                queryList.ItemRecords = paginatedList;
                queryList.PageNumber = queryModel.PageNumber;
                queryList.PageSize = queryModel.PageSize;
                queryList.CurrentCount = paginatedList.Count();
            }
            else
            {
                queryList.ItemRecords = null;
                queryList.CurrentCount = 0;
            }

            queryList.TotalCount = PrintTemplateQuery.Count();
            queryList.PageNumber = queryModel.PageNumber;
            queryList.PageSize = queryModel.PageSize;

            return queryList;
        }

        /// <summary>
        /// Search PrintTemplates
        /// </summary>
        /// <param name="PrintTemplateQuery"></param>
        /// <param name="searchTerm"></param>
        /// <returns></returns>
        private IQueryable<PrintTemplateList> SearchPrintTemplates(IQueryable<PrintTemplateList> PrintTemplateQuery, string searchTerm)
        {
            bool isTwoWordSearch = Regex.IsMatch(searchTerm, @"\s");
            if (!isTwoWordSearch)
            {
                PrintTemplateQuery = PrintTemplateQuery.Where(s => s.Name.Contains(searchTerm) || s.CreatedByFirstName.Contains(searchTerm) || s.CreatedBySurName.Contains(searchTerm));
            }
            else
            {
                string[] search = searchTerm.Split(" ");
                PrintTemplateQuery = PrintTemplateQuery.Where(s => s.Name.Contains(searchTerm) || (s.CreatedByFirstName.Contains(search[0]) && s.CreatedBySurName.Contains(search[1])));
            }
            return PrintTemplateQuery;
        }

        private async Task<List<PrintTemplateList>> CreatePaginatedListAsync(IQueryable<PrintTemplateList> PrintTemplate, QueryModel queryModel)
        {
            if (queryModel.PageNumber > 0 && queryModel.PageSize > 0)
            {
                if (PrintTemplate.Any())
                {
                    return await PrintTemplate
                            .Skip((queryModel.PageNumber - 1) * queryModel.PageSize)
                            .Take(queryModel.PageSize)
                            .ToListAsync();
                }
            }
            return null;
        }

        /// <summary>
        /// Method to PrintTemplate based on id
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<PrintTemplateView> GetPrintTemplate(int orgId, long id)
        {
            var PrintTemplate = (from CN in _readOnlyDbContext.PrintTemplate.Where(x => x.Id == id && x.OrgId == orgId)
                                       select new PrintTemplateView
                                       {
                                            Id = CN.Id,
                                            OrgId = CN.OrgId,
                                            Name = CN.Name,
                                            PageSize = CN.PageSize,
                                            FileDetailsId = CN.FileDetailsId,
                                            StatusId = CN.StatusId,
                                            CreatedBy = CN.CreatedBy,
                                            CreatedDate = CN.CreatedDate,
                                            ModifiedBy = CN.ModifiedBy,
                                            ModifiedDate = CN.ModifiedDate,
                                            PrintTemplateControls = (ICollection<PrintTemplateControls>)(
                                                from CC in _readOnlyDbContext.PrintTemplateControls
                                                    .Where(x => x.PrintTemplateId == id && x.OrgId == orgId && x.StatusId == (short)Status.Active)
                                                select new PrintTemplateControls
                                                {
                                                    Id = CC.Id,
                                                    OrgId = CC.OrgId,
                                                    PrintTemplateId = CC.PrintTemplateId,
                                                    Key = CC.Key,
                                                    Type = CC.Type,
                                                    Left = CC.Left,
                                                    Top = CC.Top,
                                                    Width = CC.Width,
                                                    Height = CC.Height,
                                                    FontSize = CC.FontSize,
                                                    PlaceHolder = CC.PlaceHolder,
                                                    Label = CC.Label,
                                                    Value = CC.Value,
                                                    StatusId = CC.StatusId,
                                                    CreatedBy = CC.CreatedBy,
                                                    CreatedDate = CC.CreatedDate,
                                                    ModifiedBy = CC.ModifiedBy,
                                                    ModifiedDate = CC.ModifiedDate
                                                })
                                      });
            return await PrintTemplate.FirstOrDefaultAsync();
        }

        /// <summary>
        /// Method to PrintTemplate based on id
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<PrintTemplate> GetPrintTemplateSync(int orgId, long id)
        {
            return await _readOnlyDbContext.PrintTemplate.Where(s => s.Id == id && s.OrgId == orgId).AsNoTracking().FirstOrDefaultAsync();
        }

        /// <summary>
        /// GetPrintTemplateControls Sync
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="PrintTemplateId"></param>
        /// <returns></returns>
        public async Task<List<PrintTemplateControls>> GetPrintTemplateControlsSync(int orgId, long PrintTemplateId)
        {
            return await _readOnlyDbContext.PrintTemplateControls.Where(s => s.PrintTemplateId == PrintTemplateId && s.OrgId == orgId).AsNoTracking().ToListAsync();
        }

        /// <summary>
        /// Update PrintTemplate
        /// </summary>
        /// <param name="PrintTemplate"></param>
        /// <returns></returns>
        public async Task<int> UpdatePrintTemplate(PrintTemplate PrintTemplate)
        {
            _updatableDBContext.PrintTemplate.Update(PrintTemplate);
            return await _updatableDBContext.SaveChangesAsync();
        }

        /// <summary>
        /// Method to check if the PrintTemplate exists in the database
        /// </summary>
        /// <param name="search_term"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<bool> CheckPrintTemplateName(string search_term, int orgId)
        {
            var name = await _readOnlyDbContext.PrintTemplate
                       .Where(p => p.Name.Equals(search_term) && p.OrgId == orgId)
                       .FirstOrDefaultAsync();

            return name != null;
        }

        #region Private methods
        private IQueryable<PrintTemplateList> SortPrintTemplate(IQueryable<PrintTemplateList> PrintTemplates, string sortTerm, string sortOrder)
        {
            switch (sortTerm.ToLower())
            {
                case "id":
                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            PrintTemplates = PrintTemplates.OrderBy(x => x.StatusId).ThenBy(y => y.Id);
                        }
                        else
                        {
                            PrintTemplates = PrintTemplates.OrderBy(x => x.StatusId).ThenByDescending(y => y.Id);
                        }
                        break;
                    }
                case "name":
                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            PrintTemplates = PrintTemplates.OrderBy(x => x.StatusId).ThenBy(y => y.Name);
                        }
                        else
                        {
                            PrintTemplates = PrintTemplates.OrderBy(x => x.StatusId).ThenByDescending(y => y.Name);
                        }
                        break;
                    }
            }

            return PrintTemplates;
        }
        #endregion Private methods
    }
}
