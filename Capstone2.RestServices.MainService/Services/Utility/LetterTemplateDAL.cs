﻿using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.Utility.Context;
using Capstone2.RestServices.Utility.Interfaces;
using Capstone2.RestServices.Utility.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Utility.Services
{
    public class LetterTemplateDAL : ILetterTemplateDAL
    {
        public readonly ReadOnlyUtilityDBContext _readOnlyDbContext;
        public readonly UpdatableUtilityDBContext _updatableDBContext;
        public LetterTemplateDAL(ReadOnlyUtilityDBContext readOnlyDbContext, UpdatableUtilityDBContext updatableDBContext)
        {
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
        }

        /// <summary>
        /// Method to add LetterTemplate to LetterTemplates table
        /// </summary>
        /// <param name="inputLetterTemplate"></param>
        /// <returns></returns>
        public async Task<long> AddLetterTemplateAsync(LetterTemplate inputletterTemplate)
        {
            await _updatableDBContext.LetterTemplates.AddAsync(inputletterTemplate);
            int rows = await _updatableDBContext.SaveChangesAsync();
            if (rows > 0)
                return inputletterTemplate.Id;
            return 0;
        }

        /// <summary>
        /// Method to check if the letter template name exists in the database
        /// </summary>
        /// <param name="templateTypeId"></param>
        /// <param name="search_term"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<bool> CheckLetterTemplateNameDAL(long templateTypeId, string search_term, int orgId)
        {
            var name = await _readOnlyDbContext.LetterTemplates
                   .Where(p => p.Name.Equals(search_term) && p.OrgId == orgId && p.TemplateTypeId == templateTypeId && p.StatusId == (short)Status.Active).FirstOrDefaultAsync();
            if (name == null)
                return false;
            else
                return true;
        }

        /// <summary>
        /// Method to letter template based on id
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<LetterTemplateView> GetLetterTemplateDAL(int orgId, long id)
        {
            var letterTemplate = await (from LT in _readOnlyDbContext.LetterTemplates.Where(x => x.Id == id && x.OrgId == orgId)
                                        from CD in _readOnlyDbContext.CompanyDetails.Where(u => u.OrgId == orgId && u.Id == LT.CompanyDetailsId).DefaultIfEmpty()
                                        select new LetterTemplateView()
                                        {
                                            Id = LT.Id,
                                            OrgId = LT.OrgId,
                                            Name = LT.Name,
                                            Text = LT.Text,
                                            HeaderStyleTypeId = LT.HeaderStyleTypeId,
                                            FooterStyleTypeId = LT.FooterStyleTypeId,
                                            FileDetailsId = LT.FileDetailsId,
                                            TemplateSubTypeID = LT.TemplateSubTypeID,
                                            CompanyDetailsId = LT.CompanyDetailsId,
                                            CompanyDetails = (LT.CompanyDetailsId == null) ? null : new LetterCompanyDetails
                                            {
                                                Id = CD.Id,
                                                Name = CD.Name
                                            },
                                            DeleteReason = LT.DeleteReason,
                                            StatusId = LT.StatusId,
                                            CreatedDate = LT.CreatedDate,
                                            CreatedBy = LT.CreatedBy,
                                            ModifiedBy = LT.ModifiedBy,
                                            ModifiedDate = LT.ModifiedDate,
                                            LetterTemplateUserAssocs = (ICollection<LetterTemplateUserAssocView>)
                                            (from LU in _readOnlyDbContext.LetterTemplateUserAssocs.Where(x => x.LetterTemplatesId == id && x.OrgId == orgId && x.StatusId == (short)Status.Active)
                                             from UD in _readOnlyDbContext.UserDetails.Where(u => u.OrgId == orgId && u.Id == LU.UserDetailsId)
                                             select new LetterTemplateUserAssocView()
                                             {
                                                 Id = LU.Id,
                                                 OrgId = LU.OrgId,
                                                 UserDetailsId = (long)LU.UserDetailsId,
                                                 CreatedDate = LU.CreatedDate,
                                                 LetterTemplatesId = LU.LetterTemplatesId,
                                                 UserDetails = new LetterUserDetail
                                                 {
                                                     Id = UD.Id,
                                                     OrgId = UD.OrgId,
                                                     FirstName = UD.FirstName,
                                                     SurName = UD.SurName
                                                 }
                                             })

                                        }).FirstOrDefaultAsync();

            return letterTemplate;
        }

        /// <summary>
        /// Method to retrieve list of letter templates 
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        public async Task<QueryResultList<ListLetterTemplate>> ListLetterTemplateDAL(BaseHttpRequestContext baseHttpRequestContext, RolePermissionFilterModel rolePermissionFilterModel, QueryModel queryModel, LetterTemplateFilterModel letterTemplateFilterModel)
        {
            var letterQuery = (from LT in _readOnlyDbContext.LetterTemplates
                               join UD in _readOnlyDbContext.UserDetails on LT.CreatedBy equals UD.Id
                               where LT.StatusId == (short)Status.Active && LT.OrgId == baseHttpRequestContext.OrgId
                               select new ListLetterTemplate()
                               {
                                   Id = LT.Id,
                                   OrgId = LT.OrgId,
                                   Name = LT.Name,
                                   Text = LT.Text,
                                   CreatedBy = LT.CreatedBy,
                                   CreatedDate = LT.CreatedDate,
                                   CreatedByFirstName = UD.FirstName,
                                   CreatedBySurName = UD.SurName,
                                   TemplateTypeID = LT.TemplateTypeId,
                                   FileDetailsId = LT.FileDetailsId,
                                   TemplateSubTypeID = LT.TemplateSubTypeID
                               });

            if (!(string.IsNullOrWhiteSpace(queryModel.SearchTerm)))
            {
                letterQuery = SearchLetterTemplates(letterQuery, queryModel.SearchTerm);

            }
            if (!(string.IsNullOrWhiteSpace(queryModel.SortOrder)) && !(string.IsNullOrWhiteSpace(queryModel.SortTerm)))
            {
                letterQuery = SortLetterTemplate(letterQuery, queryModel.SortTerm, queryModel.SortOrder);
            }

            if (letterTemplateFilterModel != null)
            {
                letterQuery = letterQuery.Where(x => letterTemplateFilterModel.TemplateType.Contains(x.TemplateTypeID));
            }

            if (baseHttpRequestContext.RoleId != (int)RoleType.SuperUser && rolePermissionFilterModel.isDeleteOnly)
                letterQuery = letterQuery.Where(s => s.CreatedBy == baseHttpRequestContext.UserId);

            var paginatedList = await CreatePaginatedListAsync(letterQuery, queryModel);
            QueryResultList<ListLetterTemplate> queryList = new QueryResultList<ListLetterTemplate>();

            if (letterTemplateFilterModel != null && letterTemplateFilterModel.TemplateSubType != null)
            {
                paginatedList = paginatedList.Where(x => x.TemplateTypeID == (short)TemplateType.Finance_Template && letterTemplateFilterModel.TemplateSubType.Contains(x.TemplateSubTypeID)).ToList();
            }

            if (paginatedList != null)
            {
                queryList.ItemRecords = paginatedList;
                queryList.CurrentCount = paginatedList.Count();
            }
            else
            {
                queryList.ItemRecords = null;
                queryList.CurrentCount = 0;
            }
            queryList.TotalCount = letterQuery.Count();
            queryList.PageNumber = queryModel.PageNumber;
            queryList.PageSize = queryModel.PageSize;

            return queryList;
        }

        private async Task<List<ListLetterTemplate>> CreatePaginatedListAsync(IQueryable<ListLetterTemplate> letterQuery, QueryModel queryModel)
        {
            if (queryModel.PageNumber > 0 && queryModel.PageSize > 0)
            {
                if (letterQuery.Any())
                {
                    List<ListLetterTemplate> paginatedList = await letterQuery.Skip((queryModel.PageNumber - 1) * queryModel.PageSize)
                 .Take(queryModel.PageSize).ToListAsync();

                    return paginatedList;
                }
            }
            return null;
        }


        /// <summary>
        /// method to enable search of letter templates
        /// </summary>
        /// <param name="LetterQuery"></param>
        /// <param name="searchTerm"></param>
        /// <returns></returns>
        private IQueryable<ListLetterTemplate> SearchLetterTemplates(IQueryable<ListLetterTemplate> LetterQuery, string searchTerm)
        {
            string filterString = string.Empty;
            bool istwoWordSearch = Regex.IsMatch(searchTerm, @"\s");
            if (!istwoWordSearch)
            {
                LetterQuery = LetterQuery.Where(s => s.Name.Contains(searchTerm) || s.CreatedByFirstName.Contains(searchTerm) || s.CreatedBySurName.Contains(searchTerm));
            }
            else
            {
                string[] search = searchTerm.Split(" ");
                LetterQuery = LetterQuery.Where(s => s.Name.Contains(searchTerm) || (s.CreatedByFirstName.Contains(search[0]) && s.CreatedBySurName.Contains(search[1])));
            }
            return LetterQuery;
        }

        private IQueryable<ListLetterTemplate> SortLetterTemplate(IQueryable<ListLetterTemplate> letterQuery, string sortTerm, string sortOrder)
        {
            switch (sortTerm.ToLower())
            {
                case "id":
                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            letterQuery = letterQuery.OrderBy(x => x.Id);
                        }
                        else
                        {
                            letterQuery = letterQuery.OrderByDescending(x => x.Id);
                        }
                        break;
                    }
                case "name":
                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            letterQuery = letterQuery.OrderBy(x => x.Name);
                        }
                        else
                        {
                            letterQuery = letterQuery.OrderByDescending(x => x.Name);
                        }
                        break;
                    }
            }

            return letterQuery;
        }


        /// <summary>
        /// Fetch letter Template
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<LetterTemplate> GetletterTemplateFromId(int orgId, long id)
        {
            return await _readOnlyDbContext.LetterTemplates.Where(s => s.Id == id && s.OrgId == orgId).AsNoTracking().FirstOrDefaultAsync();
        }

        /// <summary>
        /// Fetch LetterTemplateUserAssocs
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="letterTemplateId"></param>
        /// <returns></returns>
        public async Task<List<LetterTemplateUserAssoc>> GetletterTemplateUserAssoc(int orgId, long letterTemplateId)
        {
            return await _readOnlyDbContext.LetterTemplateUserAssocs.Where(s => s.LetterTemplatesId == letterTemplateId && s.OrgId == orgId).AsNoTracking().ToListAsync();
        }

        /// <summary>
        /// Method to update a letter template 
        /// </summary>
        /// <param name="snippet"></param>

        public async Task<int> UpdateLetterTemplate(LetterTemplate letterTemplate)
        {
            _updatableDBContext.LetterTemplates.Update(letterTemplate);
            return await _updatableDBContext.SaveChangesAsync();
        }

        public async Task<List<int>> GetCompaniesForUser(int orgId, long id)
        {
            var companyList = await (from UCA in _readOnlyDbContext.UserCompanyAssocs
                                     where UCA.StatusId == (short)Status.Active && UCA.OrgId == orgId && UCA.UserDetailsId == id
                                     select UCA.CompanyId).ToListAsync();

            return companyList;
        }

        public async Task<LetterTemplateForUser> GetUserLetterTemplates(int orgId, long id, long templateTypeId)
        {
            var userLetterTemplates = await (from UD in _readOnlyDbContext.UserDetails
                                             where UD.Id == id && UD.OrgId == orgId
                                             select new LetterTemplateForUser
                                             {
                                                 Id = UD.Id,
                                                 FirstName = UD.FirstName,
                                                 SurName = UD.SurName,
                                                 UserLetterTemplates = (List<LetterTemplateData>)(ICollection<LetterTemplateData>)
                                                 (from LTU in _readOnlyDbContext.LetterTemplateUserAssocs
                                                  join LT in _readOnlyDbContext.LetterTemplates on LTU.LetterTemplatesId equals LT.Id
                                                  where LTU.StatusId == (short)Status.Active && LTU.OrgId == orgId && LTU.UserDetailsId == id
                                                    && LT.TemplateTypeId == templateTypeId
                                                    && (templateTypeId == (short)TemplateType.Finance_Template ? LT.TemplateSubTypeID == (short)TemplateSubTypesEnum.GeneralTemplate : true)
                                                  select new LetterTemplateData
                                                  {
                                                      Id = LT.Id,
                                                      Name = LT.Name

                                                  })

                                             }).FirstOrDefaultAsync();
            return userLetterTemplates;
        }

        public async Task<List<CompanyLetterTemplate>> GetCompanyLetterList(int orgId, List<int> companyList, long letterTemplateId)
        {
            var companyLetterList = await (from CD in _readOnlyDbContext.CompanyDetails
                                           where CD.StatusId == (short)CompanyStatus.Active && CD.OrgId == orgId && companyList.Contains(CD.Id) && CD.CompanyTypeId==(short)CompanyType.InternalCompany
                                           select new CompanyLetterTemplate
                                           {
                                               Id = CD.Id,
                                               Name = CD.Name,
                                               CompanyLetterTemplateData = (List<LetterTemplateData>)(ICollection<LetterTemplateData>)
                                               (from LT in _readOnlyDbContext.LetterTemplates
                                                where CD.Id == LT.CompanyDetailsId && LT.StatusId == (short)Status.Active
                                                && LT.TemplateTypeId == letterTemplateId
                                                && (letterTemplateId == (short)TemplateType.Finance_Template ? LT.TemplateSubTypeID == (short)TemplateSubTypesEnum.GeneralTemplate : true)
                                                select new LetterTemplateData()
                                                {
                                                    Id = LT.Id,
                                                    Name = LT.Name
                                                })
                                           }).ToListAsync();

            return companyLetterList;
        }

        /// <summary>
        /// Method to check if the letter template name with templateSubtype exists
        /// </summary>
        /// <param name="search_term"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<bool> VerifyTemplateExits(long templateSubTypeId, int orgId)
        {
            var template = await _readOnlyDbContext.LetterTemplates
                   .Where(p => p.TemplateSubTypeID == templateSubTypeId && p.OrgId == orgId && p.StatusId == (short)Status.Active).FirstOrDefaultAsync();
            if (template == null)
                return false;
            else
                return true;
        }


        /// <summary>
        /// /
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="templateTypeId"></param>
        /// <param name="templateSubTypeId"></param>
        /// <returns></returns>
        public async Task<LetterTemplate> GetletterTemplateFromSubType(int orgId, short templateTypeId, short templateSubTypeId)
        {
            return await _readOnlyDbContext.LetterTemplates.Where(s => s.StatusId == (short)Status.Active && s.TemplateTypeId == templateTypeId && s.OrgId == orgId && s.TemplateSubTypeID == templateSubTypeId).AsNoTracking().FirstOrDefaultAsync();
        }
    }

}