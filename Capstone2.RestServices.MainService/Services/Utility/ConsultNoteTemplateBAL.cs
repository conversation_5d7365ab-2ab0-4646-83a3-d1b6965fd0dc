﻿using AutoMapper;
using Capstone2.Framework.RestApi;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.Utility.Interfaces;
using Capstone2.RestServices.Utility.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Transactions;
using System.Web;

namespace Capstone2.RestServices.Utility.Services
{
    public class ConsultNoteTemplateBAL : IConsultNoteTemplateBAL
    {
        public readonly IConsultNoteTemplateDAL _consultNoteTemplateDAL;
        public IMapper _mapper;
        public readonly AppSettings _appSettings;
        public readonly IRolesBAL _rolesBAL;

        public ConsultNoteTemplateBAL(IConsultNoteTemplateDAL consultNoteTemplateDAL, IRolesBAL rolesBAL, IMapper mapper, IOptions<AppSettings> appSettings)
        {
            _consultNoteTemplateDAL = consultNoteTemplateDAL;
            _mapper = mapper;
            _rolesBAL = rolesBAL;
            _appSettings = appSettings.Value;
        }

        /// <summary>
        /// Method to save a entry in ConsultNoteTemplate
        /// </summary>
        /// <param name="consultNoteTemplatePayload"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> AddConsultNoteTemplate(BaseHttpRequestContext baseHttpRequestContext, ConsultNoteTemplate consultNoteTemplate)
        {
            var orgId = baseHttpRequestContext.OrgId;
            var loggedInUser = baseHttpRequestContext.UserId;

            consultNoteTemplate.OrgId = orgId;
            consultNoteTemplate.CreatedBy = loggedInUser;
            consultNoteTemplate.CreatedDate = DateTime.UtcNow;
            consultNoteTemplate.StatusId = (short)Status.Active;

            if (consultNoteTemplate?.ConsultNoteTemplateControls?.Any() ?? false)
            {
                consultNoteTemplate.ConsultNoteTemplateControls.ToList().ForEach(a =>
                {
                    a.OrgId = orgId; a.CreatedBy = loggedInUser; a.StatusId = (short)Status.Active;
                    a.Images?.ToList()?.ForEach(b => { b.OrgId = orgId; b.CreatedBy = loggedInUser; b.StatusId = (short)Status.Active; });
                });
            }

            if (consultNoteTemplate?.ConsultNoteTemplateUserAssocs?.Any() ?? false)
            {
                consultNoteTemplate.ConsultNoteTemplateUserAssocs.ToList().ForEach(a => { a.OrgId = orgId; a.CreatedBy = loggedInUser; a.StatusId = (short)Status.Active; });
            }

            DateTime.SpecifyKind(consultNoteTemplate.CreatedDate, DateTimeKind.Utc);
            var id = await _consultNoteTemplateDAL.AddConsultNoteTemplateAsync(consultNoteTemplate);
            if (id > 0)
            {
                ApiResponse<long?> apiResponse = new()
                {
                    StatusCode = StatusCodes.Status200OK,
                    Message = "Success",
                    Result = id
                };
                return apiResponse;
            }
            else
            {
                ApiResponse<long?> apiResponse = new()
                {
                    StatusCode = StatusCodes.Status400BadRequest,
                    Message = "Failure",
                    Result = null
                };
                return apiResponse;
            }
        }

        /// <summary>
        /// Method to generate a paginated list of ConsultNoteTemplate based on search criteria
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        public async Task<ApiResponse<QueryResultList<ConsultNoteTemplateList>>> ListConsultNoteTemplate(BaseHttpRequestContext baseHttpRequestContext, QueryModel queryModel)
        {
            ApiResponse<QueryResultList<ConsultNoteTemplateList>> apiResponse = new();
            RolesFilterModel rolesFilterModel = new RolesFilterModel() { RolesId = new List<int?>() { baseHttpRequestContext.RoleId } };

            RolePermissionFilterModel rolePermissionFilterModel =
                await _rolesBAL.IsDeleteOnlyPermissionsBAL(baseHttpRequestContext, ModulesEnum.ConsultNoteTemplates);

            var queryList = await _consultNoteTemplateDAL.ListConsultNoteTemplate(baseHttpRequestContext, rolePermissionFilterModel, queryModel);

            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result = queryList;
            return apiResponse;
        }

        /// <summary>
        /// Get ConsultNoteTemplateby ID
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<ConsultNoteTemplateView>> GetConsultNoteTemplate(BaseHttpRequestContext baseHttpRequestContext, long id)
        {
            ApiResponse<ConsultNoteTemplateView> apiResposne = new();
            var orgId = baseHttpRequestContext.OrgId;
            var consultNoteTemplateFromDB = await _consultNoteTemplateDAL.GetConsultNoteTemplate(orgId, id);
            if (consultNoteTemplateFromDB is null)
            {
                apiResposne.Result = null;
                apiResposne.StatusCode = StatusCodes.Status400BadRequest;
                apiResposne.Message = "Failure";
                apiResposne.Errors.Add("The ConsultNoteTemplatedoesnot exist.");
                return apiResposne;
            }
            if (consultNoteTemplateFromDB is not null)
            {
                consultNoteTemplateFromDB.CreatedDate = DateTime.SpecifyKind(consultNoteTemplateFromDB.CreatedDate, DateTimeKind.Utc);
                consultNoteTemplateFromDB.ModifiedDate = (consultNoteTemplateFromDB.ModifiedDate == null) ? null : DateTime.SpecifyKind((DateTime)consultNoteTemplateFromDB.ModifiedDate, DateTimeKind.Utc);

                // TODO: make parallel calls for all image controls..
                if (consultNoteTemplateFromDB.ConsultNoteTemplateControls != null &&
                    consultNoteTemplateFromDB.ConsultNoteTemplateControls.Any(x => x.Images != null && x.Images.Count > 0))
                {
                    foreach (var item in consultNoteTemplateFromDB.ConsultNoteTemplateControls)
                    {
                        if (item.Images != null && item.Images.Count > 0)
                        {
                            item.Images = await FetchFileDetailsForMediaAsync(item.Images, baseHttpRequestContext);
                        }
                    }
                }

                apiResposne.Result = consultNoteTemplateFromDB;
                apiResposne.StatusCode = StatusCodes.Status200OK;
                apiResposne.Message = "Success";
                return apiResposne;
            }
            else
            {
                apiResposne.Result = null;
                apiResposne.StatusCode = StatusCodes.Status500InternalServerError;
                apiResposne.Message = "Data for given ConsultNoteTemplateID is not found";
                return apiResposne;
            }
        }

        private async Task<ICollection<ConsultNoteTemplateMediaView>> FetchFileDetailsForMediaAsync(ICollection<ConsultNoteTemplateMediaView> paginatedList, BaseHttpRequestContext baseHttpRequestContext)
        {
            var fileDetailsOutputForId = new List<FileDetailsOutputForId>();
            List<long> lstFileDetailsId = paginatedList.Select(x => x.FileDetailsId).ToList();
            List<FileDetailsOutputForId> fileResponse = new();

            if (lstFileDetailsId.Count > 0)
            {
                string stringFileDetailsId = string.Join(",", lstFileDetailsId);
                var token = baseHttpRequestContext.BearerToken;
                string interServiceToken = baseHttpRequestContext.InterServiceToken;
                string fileAPiUrl = _appSettings.ApiUrls["FileServiceUrl"] + "/file/file_details_list?" + "stringFileDetailsId" + "=" + HttpUtility.UrlEncode(stringFileDetailsId);
                var restClient = new RestClient(fileAPiUrl, null, token, interServiceToken);
                var fileApiResponse = await restClient.GetAsync<ApiResponse<List<FileDetailsOutputForId>>>(fileAPiUrl, null);
                if (fileApiResponse.StatusCode == StatusCodes.Status200OK)
                {
                    fileResponse = fileApiResponse.Result;
                }
                if (fileResponse.Count > 0)
                {
                    paginatedList.ToList().ForEach(x =>
                    {
                        x.FileDetails = fileResponse.Find(f => f.FileDetail.Id == x.FileDetailsId);
                    });
                }
            }
            return paginatedList;
        }

        /// <summary>
        /// Edit Appointment Type
        /// </summary>
        /// <param name="id"></param>
        /// <param name="inputConsultNoteTemplate"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> EditConsultNoteTemplate(BaseHttpRequestContext baseHttpRequestContext, long id, ConsultNoteTemplate inputConsultNoteTemplate)
        {
            ApiResponse<long?> apiResponse = new();
            var orgId = baseHttpRequestContext.OrgId;
            var userId = baseHttpRequestContext.UserId;
            var consultNoteTemplateFromDB = await _consultNoteTemplateDAL.GetConsultNoteTemplateSync(orgId, id);

            inputConsultNoteTemplate.OrgId = orgId;
            inputConsultNoteTemplate.ModifiedDate = DateTime.UtcNow;
            inputConsultNoteTemplate.ModifiedBy = userId;
            inputConsultNoteTemplate.CreatedBy = consultNoteTemplateFromDB.CreatedBy;
            inputConsultNoteTemplate.CreatedDate = consultNoteTemplateFromDB.CreatedDate;
            inputConsultNoteTemplate.Id = consultNoteTemplateFromDB.Id;

            if (inputConsultNoteTemplate != null && inputConsultNoteTemplate.ConsultNoteTemplateControls.Any())
            {
                inputConsultNoteTemplate.ConsultNoteTemplateControls.ToList().ForEach(a =>
                {
                    a.OrgId = orgId; a.StatusId = (short)Status.Active;
                    a.Images?.ToList()?.ForEach(b => { b.OrgId = orgId; b.StatusId = (short)Status.Active; });
                });
            }

            if (inputConsultNoteTemplate != null && inputConsultNoteTemplate.ConsultNoteTemplateUserAssocs.Any())
            {
                inputConsultNoteTemplate.ConsultNoteTemplateUserAssocs.ToList().ForEach(a => { a.OrgId = orgId; a.ConsultNoteTemplateId = id; });
            }

            using (var transaction = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
            {
                inputConsultNoteTemplate.ConsultNoteTemplateControls = await EditConsultNoteTemplateControls(inputConsultNoteTemplate.ConsultNoteTemplateControls, id, orgId, userId);

                foreach (var control in inputConsultNoteTemplate.ConsultNoteTemplateControls)
                {
                    if (control.ControlId == 147 && control.Id > 0)
                    {
                        control.Images = await EditConsultNoteTemplateMedia(control.Images, control.Id, orgId, userId);
                    }
                }

                inputConsultNoteTemplate.ConsultNoteTemplateUserAssocs = await EditConsultNoteTemplateUserAssocs(inputConsultNoteTemplate.ConsultNoteTemplateUserAssocs, id, orgId, userId);

                await _consultNoteTemplateDAL.UpdateConsultNoteTemplate(inputConsultNoteTemplate);

                apiResponse.Result = id;
                apiResponse.Message = "Success";
                apiResponse.StatusCode = StatusCodes.Status200OK;

                transaction.Complete();
            }

            return apiResponse;
        }

        private async Task<List<ConsultNoteTemplateControls>> EditConsultNoteTemplateControls(ICollection<ConsultNoteTemplateControls> inputControlList, long id, int orgId, long userId)
        {
            List<ConsultNoteTemplateControls> addControlList = new();

            var removeUserList = await _consultNoteTemplateDAL.GetConsultNoteTemplateControlsSync(orgId, id);

            foreach (var control in inputControlList)
            {
                if (control.Id > 0)
                {
                    var existingobj = removeUserList.FirstOrDefault(x => x.Id == control.Id);
                    removeUserList.Remove(existingobj);
                    control.ModifiedDate = DateTime.UtcNow;
                    control.ModifiedBy = userId;
                    control.StatusId = (short)Status.Active;
                    addControlList.Add(control);
                }
                else
                {
                    control.CreatedDate = DateTime.UtcNow;
                    control.CreatedBy = userId;
                    control.StatusId = (short)Status.Active;
                    addControlList.Add(control);

                    //var userAssocDB = removeUserList.Where(x => x.Key == control.Key).FirstOrDefault();
                    //if (userAssocDB is null)
                    //{
                    //    control.CreatedDate = DateTime.UtcNow;
                    //    control.CreatedBy = userId;
                    //    control.StatusId = (short)Status.Active;
                    //    addControlList.Add(control);
                    //}
                    //else
                    //{
                    //    if (userAssocDB.StatusId == (short)Status.Deleted)
                    //    {
                    //        userAssocDB.StatusId = (short)Status.Active;
                    //        userAssocDB.ModifiedDate = DateTime.UtcNow;
                    //        userAssocDB.ModifiedBy = userId;
                    //        addControlList.Add(userAssocDB);
                    //    }
                    //    removeUserList.Remove(userAssocDB);
                    //}
                }
            }

            foreach (var userAssoc in removeUserList.Where(x => x.StatusId == (short)Status.Active))
            {
                userAssoc.StatusId = (short)Status.Deleted;
                userAssoc.ModifiedDate = DateTime.UtcNow;
                userAssoc.ModifiedBy = userId;
            }

            addControlList = addControlList.Concat(removeUserList).ToList();
            return addControlList;
        }

        private async Task<List<ConsultNoteTemplateMedia>> EditConsultNoteTemplateMedia(ICollection<ConsultNoteTemplateMedia> inputControlList, long id, int orgId, long userId)
        {
            List<ConsultNoteTemplateMedia> addControlList = new();

            var removeUserList = await _consultNoteTemplateDAL.GetConsultNoteTemplateMediaSync(orgId, id);

            foreach (var control in inputControlList)
            {
                if (control.Id > 0)
                {
                    var existingobj = removeUserList.FirstOrDefault(x => x.Id == control.Id);
                    removeUserList.Remove(existingobj);
                    control.ModifiedDate = DateTime.UtcNow;
                    control.ModifiedBy = userId;
                    control.StatusId = (short)Status.Active;
                    addControlList.Add(control);
                }
                else
                {
                    var userAssocDB = removeUserList.Where(x => x.FileDetailsId == control.FileDetailsId).FirstOrDefault();
                    if (userAssocDB is null)
                    {
                        control.CreatedDate = DateTime.UtcNow;
                        control.CreatedBy = userId;
                        control.StatusId = (short)Status.Active;
                        addControlList.Add(control);
                    }
                    else
                    {
                        if (userAssocDB.StatusId == (short)Status.Deleted)
                        {
                            userAssocDB.StatusId = (short)Status.Active;
                            userAssocDB.ModifiedDate = DateTime.UtcNow;
                            userAssocDB.ModifiedBy = userId;
                            addControlList.Add(userAssocDB);
                        }
                        removeUserList.Remove(userAssocDB);
                    }
                }
            }

            foreach (var userAssoc in removeUserList.Where(x => x.StatusId == (short)Status.Active))
            {
                userAssoc.StatusId = (short)Status.Deleted;
                userAssoc.ModifiedDate = DateTime.UtcNow;
                userAssoc.ModifiedBy = userId;
            }

            addControlList = addControlList.Concat(removeUserList).ToList();
            return addControlList;
        }

        private async Task<List<ConsultNoteTemplateUserAssocs>> EditConsultNoteTemplateUserAssocs(ICollection<ConsultNoteTemplateUserAssocs> inputUserList, long id, int orgId, long userId)
        {
            List<ConsultNoteTemplateUserAssocs> addUserList = new();

            var removeUserList = await _consultNoteTemplateDAL.GetConsultNoteTemplateUserAssocsSync(orgId, id);

            foreach (var userAssoc in inputUserList)
            {
                if (userAssoc.Id > 0)
                {
                    var existingobj = removeUserList.FirstOrDefault(x => x.Id == userAssoc.Id);
                    removeUserList.Remove(existingobj);
                }
                else
                {
                    var userAssocDB = removeUserList.Where(x => x.UserDetailsId == userAssoc.UserDetailsId).FirstOrDefault();
                    if (userAssocDB is null)
                    {
                        userAssoc.CreatedDate = DateTime.UtcNow;
                        userAssoc.StatusId = (short)Status.Active;
                        addUserList.Add(userAssoc);
                    }
                    else
                    {
                        if (userAssocDB.StatusId == (short)Status.Deleted)
                        {
                            userAssocDB.StatusId = (short)Status.Active;
                            userAssocDB.ModifiedDate = DateTime.UtcNow;
                            userAssocDB.ModifiedBy = userId;
                            addUserList.Add(userAssocDB);
                        }
                        removeUserList.Remove(userAssocDB);
                    }
                }
            }

            foreach (var userAssoc in removeUserList.Where(x => x.StatusId == (short)Status.Active))
            {
                userAssoc.StatusId = (short)Status.Deleted;
                userAssoc.ModifiedDate = DateTime.UtcNow;
                userAssoc.ModifiedBy = userId;
            }

            addUserList = addUserList.Concat(removeUserList).ToList();
            return addUserList;
        }

        /// <summary>
        /// Method to delete a letter template
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> DeleteConsultNoteTemplate(BaseHttpRequestContext baseHttpRequestContext, long id, ConsultNoteTemplateDelete deleteData)
        {
            var orgId = baseHttpRequestContext.OrgId;
            var loggedInUser = baseHttpRequestContext.UserId;
            ApiResponse<long?> apiResponse = new();

            if (string.IsNullOrWhiteSpace(deleteData?.Reason))
            {
                apiResponse.Errors.Add("Can not Delete Consult Note Template without Delete Reason");
                apiResponse.Message = "Failure";
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                return apiResponse;
            }

            var consultNoteTemplate = await _consultNoteTemplateDAL.GetConsultNoteTemplateSync(orgId, id);
            consultNoteTemplate.ConsultNoteTemplateControls = await _consultNoteTemplateDAL.GetConsultNoteTemplateControlsSync(orgId, id);

            // TODO: Add owner check once Roles and permission changes are done.
            if (consultNoteTemplate is not null)
            {
                consultNoteTemplate.StatusId = (short)Status.Deleted;
                consultNoteTemplate.ModifiedBy = loggedInUser;
                consultNoteTemplate.ModifiedDate = DateTime.UtcNow;
                consultNoteTemplate.DeleteReason = deleteData.Reason;

                if (consultNoteTemplate.ConsultNoteTemplateControls is not null && consultNoteTemplate.ConsultNoteTemplateControls.Any())
                {
                    consultNoteTemplate.ConsultNoteTemplateControls.ToList().ForEach(u => { u.StatusId = (short)Status.Deleted; u.ModifiedBy = loggedInUser; u.ModifiedDate = DateTime.UtcNow; });
                }

                if (consultNoteTemplate != null && consultNoteTemplate.ConsultNoteTemplateUserAssocs.Any())
                {
                    consultNoteTemplate.ConsultNoteTemplateUserAssocs.ToList().ForEach(u => { u.StatusId = (short)Status.Deleted; u.ModifiedBy = loggedInUser; u.ModifiedDate = DateTime.UtcNow; });
                }

                int rows = await _consultNoteTemplateDAL.UpdateConsultNoteTemplate(consultNoteTemplate);

                if (rows > 0)
                {
                    apiResponse.Result = id;
                    apiResponse.Message = "Success";
                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    return apiResponse;
                }
                else
                {
                    apiResponse.Errors.Add("Consult Note Template cannot be deleted at this time.");
                }
            }
            else
            {
                apiResponse.Errors.Add("Consult Note Template can not be found.");
            }
            apiResponse.Message = "Failure";
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            return apiResponse;

        }

        /// <summary>
        /// Method to check if the entered ConsultNoteTemplateis unique
        /// </summary>
        /// <param name="search_term"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<ApiResponse<bool>> CheckConsultNoteTemplateName(BaseHttpRequestContext baseHttpRequestContext, string search_term)
        {
            var nameBool = await _consultNoteTemplateDAL.CheckConsultNoteTemplateName(search_term, baseHttpRequestContext.OrgId);
            return new ApiResponse<bool>
            {
                Result = nameBool,
                Message = "Success",
                StatusCode = StatusCodes.Status200OK
            };
        }

        /// <summary>
        /// Method to generate a paginated list of ConsultNoteTemplate based on user Id
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        public async Task<ApiResponse<ConsultNoteTemplateForUser>> GetConsultNoteTemplateForUser(BaseHttpRequestContext baseHttpRequestContext, long userId)
        {
            var orgId = baseHttpRequestContext.OrgId;
            ApiResponse<ConsultNoteTemplateForUser> apiResposne = new();

            var result = await _consultNoteTemplateDAL.GetConsultNoteTemplateForUser(orgId, userId);

            var listOfCompaniesForUser = await _consultNoteTemplateDAL.GetCompaniesForUser(orgId, userId);

            if (listOfCompaniesForUser != null && listOfCompaniesForUser.Count > 0)
            {
                result.CompanyConsultNoteTemplates = await _consultNoteTemplateDAL.GetCompanyConsultNoteTemplateList(orgId, listOfCompaniesForUser);
            }

            apiResposne.Result = result;
            apiResposne.StatusCode = StatusCodes.Status200OK;
            apiResposne.Message = "Success";
            return apiResposne;
        }
    }
}