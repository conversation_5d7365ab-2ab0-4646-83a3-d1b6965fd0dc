﻿using Capstone2.RestServices.Utility.Context;
using Capstone2.RestServices.Utility.Interfaces;
using Capstone2.RestServices.Utility.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Enum;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Utility.Services
{
    public class ChecklistDAL : IChecklistDAL
    {
        public readonly ReadOnlyUtilityDBContext _readOnlyDbContext;
        public UpdatableUtilityDBContext _updatableDBContext;
        public ChecklistDAL(ReadOnlyUtilityDBContext readOnlyDbContext, UpdatableUtilityDBContext updatableDBContext)
        {
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;

        }
        /// <summary>
        /// Method to create a new checklist
        /// </summary>
        /// <param name="checklist"></param>
        /// <returns></returns>
        public async Task<long> AddChecklistDAL(Checklist checklist)
        {
            await _updatableDBContext.Checklists.AddAsync(checklist);
            await _updatableDBContext.SaveChangesAsync();
            return checklist.Id;
        }

        public async Task<InputChecklist> FetchChecklistDetails(int orgId, long id)
        {
            IQueryable<InputChecklist> checklistQuery = from checklist in _readOnlyDbContext.Checklists
                                                            //from SU in SUA.Where(u=>u.OrgId==orgId).DefaultIfEmpty()
                                                        where checklist.OrgId == orgId && checklist.Id == id
                                                        select new InputChecklist()
                                                        {
                                                            Id = checklist.Id,
                                                            OrgId = checklist.OrgId,
                                                            Name = checklist.Name,
                                                            StatusId = checklist.StatusId,
                                                            CreatedBy = checklist.CreatedBy,
                                                            CreatedDate = checklist.CreatedDate,
                                                            ModifiedDate = checklist.ModifiedDate,
                                                            ModifiedBy = checklist.ModifiedBy,
                                                            ChecklistActivityAssocs = (ICollection<InputChecklistActivityAssoc>)(from assoc in _readOnlyDbContext.ChecklistActivityAssocs.Where(x => x.ChecklistId == id && x.OrgId == orgId && x.StatusId == (short)Status.Active)
                                                                                                                                 from AD in _readOnlyDbContext.Activities.Where(u => u.OrgId == orgId && u.Id == assoc.ActivityId)
                                                                                                                                 orderby AD.Days ascending
                                                                                                                                 select new InputChecklistActivityAssoc()
                                                                                                                                 {
                                                                                                                                     Id = assoc.Id,
                                                                                                                                     OrgId = assoc.OrgId,
                                                                                                                                     CreatedDate = assoc.CreatedDate,
                                                                                                                                     Activity = new Activity
                                                                                                                                     {
                                                                                                                                         Id = AD.Id,
                                                                                                                                         OrgId = AD.OrgId,
                                                                                                                                         Name = AD.Name,
                                                                                                                                         CategoryId = AD.CategoryId,
                                                                                                                                         ActivityTypeId = AD.ActivityTypeId,
                                                                                                                                         IsBefore = AD.IsBefore,
                                                                                                                                         Days = AD.Days,
                                                                                                                                         StatusId = AD.StatusId,
                                                                                                                                         CreatedBy = AD.CreatedBy,
                                                                                                                                         CreatedDate = AD.CreatedDate,
                                                                                                                                         ModifiedDate = AD.ModifiedDate,
                                                                                                                                         ModifiedBy = AD.ModifiedBy
                                                                                                                                     }
                                                                                                                                 })


                                                        };
            return await checklistQuery.FirstOrDefaultAsync();
        }

        /// <summary>
        /// Method to fetch a checklist
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        public async Task<Checklist> GetChecklistDAL(int orgId, long id)
        {
            var checklist = _readOnlyDbContext.Checklists.Where(x => x.Id == id && x.OrgId == orgId).AsNoTracking().FirstOrDefaultAsync();
            return await checklist;
        }

        /// <summary>
        /// Fetch ChecklistActivityAssocs
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="checklistId"></param>
        /// <returns></returns>
        public async Task<List<ChecklistActivityAssoc>> GetChecklistActivityAssocs(int orgId, long checklistId)
        {
            return await _readOnlyDbContext.ChecklistActivityAssocs.Where(s => s.ChecklistId == checklistId && s.OrgId == orgId).AsNoTracking().ToListAsync();
        }

        /// <summary>
        /// Method to update a checklist
        /// </summary>
        /// <param name="checklist"></param>

        public async Task<int> EditChecklistDAL(Checklist checklist)
        {
            _updatableDBContext.Checklists.Update(checklist);
            return await _updatableDBContext.SaveChangesAsync();
        }

        /// <summary>
        /// Method to retrieve list of checklists
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="queryModel"></param>
        /// <param name="activityFilterModel"></param>
        /// <returns></returns>
        public async Task<QueryResultList<ListChecklist>> ListChecklistsDAL(int orgId, QueryModel queryModel, ChecklistFilterModel checklistFilterModel)
        {
            IQueryable<ListChecklist> checklistQuery = from checklist in _readOnlyDbContext.Checklists
                                                       where checklist.OrgId == orgId
                                                       select new ListChecklist()
                                                       {
                                                           Id = checklist.Id,
                                                           OrgId = checklist.OrgId,
                                                           Name = checklist.Name,
                                                           StatusId = checklist.StatusId,
                                                           CreatedBy = checklist.CreatedBy,
                                                           CreatedDate = checklist.CreatedDate,
                                                           ModifiedDate = checklist.ModifiedDate,
                                                           ModifiedBy = checklist.ModifiedBy
                                                           //ChecklistActivityAssocs = (ICollection<InputChecklistActivityAssoc>)(from assoc in _readOnlyDbContext.ChecklistActivityAssocs.Where(x => x.ChecklistId == checklist.Id && x.OrgId == orgId && x.StatusId == (short)Status.Active)
                                                           //                                                                     from AD in _readOnlyDbContext.Activities.Where(u => u.OrgId == orgId && u.Id == assoc.ActivityId)
                                                           //                                                                     select new InputChecklistActivityAssoc()
                                                           //                                                                     {
                                                           //                                                                         Id = assoc.Id,
                                                           //                                                                         OrgId = assoc.OrgId,
                                                           //                                                                         CreatedDate = assoc.CreatedDate,
                                                           //                                                                         ActivityId = assoc.ActivityId,
                                                           //                                                                         Activity = new Activity
                                                           //                                                                         {
                                                           //                                                                             Id = AD.Id,
                                                           //                                                                             OrgId = AD.OrgId,
                                                           //                                                                             Name = AD.Name,
                                                           //                                                                             CategoryId = AD.CategoryId,
                                                           //                                                                             ActivityTypeId = AD.ActivityTypeId,
                                                           //                                                                             IsBefore = AD.IsBefore,
                                                           //                                                                             Days = AD.Days,
                                                           //                                                                             StatusId = AD.StatusId,
                                                           //                                                                             CreatedBy = AD.CreatedBy,
                                                           //                                                                             CreatedDate = AD.CreatedDate,
                                                           //                                                                             ModifiedDate = AD.ModifiedDate,
                                                           //                                                                             ModifiedBy = AD.ModifiedBy
                                                           //                                                                         }
                                                           //                                                                     })


                                                       };

            if (checklistFilterModel != null)
            {
                if (checklistFilterModel.StatusId != null)
                {
                    checklistQuery = checklistQuery.Where(x => checklistFilterModel.StatusId.Contains(x.StatusId));
                }
                if (checklistFilterModel.ActivityId != null)
                {
                    List<int?> lstChecklistId = await _readOnlyDbContext.ChecklistActivityAssocs.Where(x => x.StatusId == (short)Status.Active && checklistFilterModel.ActivityId.Contains(x.ActivityId))
                                                .Select(x => x.ChecklistId).ToListAsync();
                    if (lstChecklistId is not null)
                        checklistQuery = checklistQuery.Where(x => lstChecklistId.Contains(x.Id));
                }
            }
            if (!(string.IsNullOrWhiteSpace(queryModel.SearchTerm)))
            {
                checklistQuery = SearchChecklists(checklistQuery, queryModel.SearchTerm);

            }
            if (!(string.IsNullOrWhiteSpace(queryModel.SortOrder)) && !(string.IsNullOrWhiteSpace(queryModel.SortTerm)))
            {
                checklistQuery = SortChecklists(checklistQuery, queryModel.SortTerm, queryModel.SortOrder);
            }
            QueryResultList<ListChecklist> paginatedList = await PaginatedResultListAsync(checklistQuery, queryModel);
            return paginatedList;
            //return await checklistQuery.ToListAsync();
        }

        private async Task<QueryResultList<ListChecklist>> PaginatedResultListAsync(IQueryable<ListChecklist> checklistQuery, QueryModel queryModel)
        {
            QueryResultList<ListChecklist> queryList = new QueryResultList<ListChecklist>();
            List<ListChecklist> paginatedList = null;
            if (queryModel.PageNumber > 0 && queryModel.PageSize > 0)
            {
                if (checklistQuery.Any())
                {
                    paginatedList = await checklistQuery.Skip((queryModel.PageNumber - 1) * queryModel.PageSize)
                 .Take(queryModel.PageSize).ToListAsync();
                }
            }

            if (paginatedList != null)
            {
                queryList.ItemRecords = paginatedList;
                queryList.PageNumber = queryModel.PageNumber;
                queryList.PageSize = queryModel.PageSize;
                queryList.CurrentCount = paginatedList.Count();
            }
            else
            {
                queryList.ItemRecords = null;
                queryList.CurrentCount = 0;
            }
            queryList.TotalCount = checklistQuery.Count();
            queryList.PageNumber = queryModel.PageNumber;
            queryList.PageSize = queryModel.PageSize;

            return queryList;
        }

        /// <summary>
        /// method to enable search of activities
        /// </summary>
        /// <param name="ActivityQuery"></param>
        /// <param name="searchTerm"></param>
        /// <returns></returns>
        private IQueryable<ListChecklist> SearchChecklists(IQueryable<ListChecklist> checklistQuery, string searchTerm)
        {
            if (searchTerm != null)
            {
                checklistQuery = checklistQuery.Where(s => s.Name.Contains(searchTerm));
            }
            return checklistQuery;

        }


        private IQueryable<ListChecklist> SortChecklists(IQueryable<ListChecklist> checklistQuery, string sortTerm, string sortOrder)
        {
            switch (sortTerm.ToLower())
            {

                case "id":

                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            checklistQuery = checklistQuery.OrderBy(x => x.StatusId).ThenBy(x=>x.Id);
                        }
                        else
                        {

                            checklistQuery = checklistQuery.OrderBy(x => x.StatusId).ThenByDescending(x=>x.Id);

                        }
                        break;
                    }
                case "name":

                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            checklistQuery = checklistQuery.OrderBy(x => x.Name);
                        }
                        else
                        {

                            checklistQuery = checklistQuery.OrderByDescending(x => x.Name);

                        }
                        break;
                    }
                default:
                    checklistQuery = checklistQuery.OrderBy(x => x.StatusId).ThenByDescending(x => x.Id);

                    break;

            }

            return checklistQuery;
        }

        /// <summary>
        /// Method to check if the checklist name exists in the database
        /// </summary>
        /// <param name="search_term"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<bool> CheckChecklistName(string search_term, int orgId)
        {
            var name = await _readOnlyDbContext.Checklists
                   .Where(p => p.Name.Equals(search_term) && p.OrgId == orgId && p.StatusId != (short)Status.Deleted).FirstOrDefaultAsync();
            if (name == null)
            {
                return false;
            }
            else
            {
                return true;
            }
        }

    }
}
