﻿using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.Utility.Context;
using Capstone2.RestServices.Utility.Interfaces;
using Capstone2.RestServices.Utility.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Utility.Services
{
    public class MedicareParticipantDetailsDAL: IMedicareParticipantDetailsDAL
    {
        public readonly ReadOnlyUtilityDBContext _readOnlyDbContext;
        public readonly UpdatableUtilityDBContext _updatableDBContext;
        public MedicareParticipantDetailsDAL(ReadOnlyUtilityDBContext readOnlyDbContext, UpdatableUtilityDBContext updatableDBContext)
        {
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
        }

        /// <summary>
        /// Method to add Medical Contractor
        /// </summary>
        /// <param name="PrintTemplate"></param>
        /// <returns></returns>
        public async Task<int> AddMedContDAL(MedicalContractor inputMedicalContractor)
        {
            await _updatableDBContext.MedicalContractors.AddAsync(inputMedicalContractor);
            int rows = await _updatableDBContext.SaveChangesAsync();
            return rows > 0 ? inputMedicalContractor.Id : 0;
        }

        /// <summary>
        /// Method to create a new MedicalContractorBandAssocs
        /// </summary>
        /// <param name="mcBandAssocs"></param>
        /// <returns></returns>
        public async Task<long> AddMCBandAssocsDAL(List<MedicalContractorBandAssoc> mcBandAssocs)
        {
            await _updatableDBContext.MedicalContractorBandAssocs.AddRangeAsync(mcBandAssocs);
            return await _updatableDBContext.SaveChangesAsync();
        }

        /// <summary>
        /// Method to fetch a Medical Contractors
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        public async Task<List<MedicalContractorList>> GetMedContListAsync(int orgId)
        {           
            List<MedicalContractorList> mcData = await (from MC in _readOnlyDbContext.MedicalContractors
                                                    join CD in _readOnlyDbContext.CompanyDetails on MC.CompanyDetailsId equals CD.Id into JC
                                                    from CD in JC.DefaultIfEmpty()
                                                    where MC.OrgId == orgId
                                                    select new MedicalContractorList
                                                    {
                                                        Id = MC.Id,
                                                        MasterMedicalParticipantsId = MC.MasterMedicalParticipantsId,
                                                        MedicalContractorTypeId = MC.MedicalContractorTypeId,
                                                        OrgId = MC.OrgId,
                                                        ParticipantId = MC.ParticipantId,
                                                        Name = MC.Name,
                                                        CreatedDate = MC.CreatedDate,
                                                        ModifiedDate = MC.ModifiedDate,
                                                        MedicalContractorParentId = MC.MedicalContractorParentId,
                                                        StatusId = (short)MC.StatusId,
                                                        CompanyDetailsId = MC.CompanyDetailsId,
                                                        CompanyName = CD.Name
                                                       
                                                    }).AsNoTracking().ToListAsync();

            return mcData;
        }


        public async Task<List<MedicalContractorBandAssoc>> GetMedContBandAssocDAL(int orgId, int medContId, MedContBandFilterModel mcFilterModel)
        {
            //initially consider all as parent 
            int parentMC = medContId;
            short versionNum = 0;
            List<MedicalContractorBandAssoc> MedContBandList = new();

            // Get latest version 
            MedicalContractorBandVersion latestVersion = await GetMCBandVersionLatestFull(orgId, medContId);
            
            if(latestVersion is null)
            {
                return MedContBandList;
            }
            
            versionNum = (short)latestVersion.VersionNo;

            //if in latest version mc is a child 
            if (latestVersion.MedicalContractorParentId is not null)
            {
                parentMC = (int)latestVersion.MedicalContractorParentId;
                versionNum = (short)latestVersion.ParentVersionNo;
            }

            var query = from MCA in _readOnlyDbContext.MedicalContractorBandAssocs
                        where MCA.OrgId == orgId && MCA.StatusId == (short)Status.Active
                        && MCA.MedicalContractorId == parentMC
                        select MCA;


            if (mcFilterModel is not null)
            {
                if (mcFilterModel.VersionNo != null)
                {
                    query = query.Where(x => x.VersionNo == mcFilterModel.VersionNo);

                }
            }
            else
            {
               
                query = query.Where(x => x.VersionNo == versionNum);
            }

             MedContBandList = await query.AsNoTracking().ToListAsync();

            return MedContBandList;
        }

        /// <summary>
        /// GetMCBandAssocByMultipleIds
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="mcBandassocs"></param>
        /// <returns></returns>
        public async Task<List<MedicalContractorBandAssoc>> GetMCBandAssocByMultipleIds(int orgId, List<int> mcBandassocs)
        {
            return await _readOnlyDbContext.MedicalContractorBandAssocs
                .Where(s => mcBandassocs.Contains(s.Id) && s.OrgId == orgId)
                .ToListAsync();
        }

        /// <summary>
        /// EditMCBandAssoc
        /// </summary>
        /// <param name="mcBandassocs"></param>
        /// <returns></returns>
        public async Task<int> EditMcBandAssoc(List<MedicalContractorBandAssoc> mcBandassocs)
        {
            _updatableDBContext.MedicalContractorBandAssocs.UpdateRange(mcBandassocs);
            return await _updatableDBContext.SaveChangesAsync();
        }

        public async Task<MedicalContractorView> GetMCByIds(int orgId, int mcId)
        {

            // return await _readOnlyDbContext.MedicalContractors.Include(e => e.MedicalContractorAddresses.Where(e => e.StatusId == (short)Status.Active)).Where(C1 => C1.OrgId == orgId && C1.Id == mcId).FirstOrDefaultAsync();



            MedicalContractorView mcData = await (from mc in _readOnlyDbContext.MedicalContractors
                                                        .Include(e => e.MedicalContractorAddresses.Where(e => e.StatusId == (short)Status.Active))
                                                  join CD in _readOnlyDbContext.CompanyDetails on mc.CompanyDetailsId equals CD.Id into JC
                                                  from CD in JC.DefaultIfEmpty()

                                                  where mc.OrgId == orgId && mc.Id == mcId
                                                        select new MedicalContractorView
                                                        {
                                                            Id = mc.Id,
                                                            MasterMedicalParticipantsId = mc.MasterMedicalParticipantsId,
                                                            OrgId = mc.OrgId,
                                                            MedicalContractorTypeId = mc.MedicalContractorTypeId,
                                                            Name = mc.Name,
                                                            ParticipantId = mc.ParticipantId,
                                                            Services = mc.Services,
                                                            StatusId = mc.StatusId,
                                                            CreatedDate = mc.CreatedDate,
                                                            ModifiedDate = mc.ModifiedDate,
                                                            ModifiedBy = mc.ModifiedBy,
                                                            ABN = mc.ABN,
                                                            HCPCode = mc.HCPCode,
                                                            OfficePhoneNo = mc.OfficePhoneNo,
                                                            Email = mc.Email,
                                                            Website = mc.Website,
                                                            MedicalContractorParentId = mc.MedicalContractorParentId,
                                                            CompanyDetailsId = mc.CompanyDetailsId,
                                                            CompanyName = CD.Name,
                                                            MedicalContractorAddresses = (from mca in _readOnlyDbContext.MedicalContractorAddresses
                                                                                          where mca.MedicalContractorId == mc.Id && mca.StatusId == (short)Status.Active
                                                                                          select mca).ToList(),
                                                            ParentData = (mc.MedicalContractorParentId == null) ? null : (from pmc in _readOnlyDbContext.MedicalContractors
                                                                                                                          where pmc.Id == mc.MedicalContractorParentId
                                                                                                                          select new MedicalContractorParent {
                                                                                                                              Id = pmc.Id,
                                                                                                                               OrgId = pmc.OrgId,
                                                                                                                               MedicalContractorTypeId = pmc.MedicalContractorTypeId,
                                                                                                                               MasterMedicalParticipantsId = pmc.MasterMedicalParticipantsId,
                                                                                                                               Name = pmc.Name,
                                                                                                                               ParticipantId = pmc.ParticipantId,
                                                                                                                               StatusId = pmc.StatusId

                                                                                                                          }).FirstOrDefault()
                                                        }).FirstOrDefaultAsync();

            return mcData;
        }

        public async Task<MedicalContractor> GetMCForEdit(int orgId, int mcId)
        {

            return await _readOnlyDbContext.MedicalContractors.AsNoTracking().Where(C1 => C1.OrgId == orgId && C1.Id == mcId).FirstOrDefaultAsync();

        }

        public async Task<MedicalContractorBandVersion> GetLatestMCBandVersion(int orgId, int mcId)
        {

            return await _readOnlyDbContext.MedicalContractorBandVersions.AsNoTracking().Where(C1 => C1.OrgId == orgId && C1.MedicalContractorId == mcId).OrderByDescending(x=>x.VersionNo).FirstOrDefaultAsync();

        }

        public async Task<int> EditMc(MedicalContractor mcObj)
        {
            _updatableDBContext.MedicalContractors.Update(mcObj);
            return await _updatableDBContext.SaveChangesAsync();
        }

        public async Task<List<MedicalContractorAddress>> GetMcAddresses(int Id, int orgId)
        {
            return await _readOnlyDbContext.MedicalContractorAddresses.AsNoTracking().Where(x => x.MedicalContractorId == Id && x.OrgId == orgId && x.StatusId == (short)Status.Active).ToListAsync();
        }

        public async Task UpdateMcAddressList(List<MedicalContractorAddress> removeList)
        {
            _updatableDBContext.MedicalContractorAddresses.UpdateRange(removeList);
            await _updatableDBContext.SaveChangesAsync();

        }
        public async Task AddMcAddressList(List<MedicalContractorAddress> addList)
        {
            _updatableDBContext.MedicalContractorAddresses.AddRange(addList);
            await _updatableDBContext.SaveChangesAsync();
        }

        /// <summary>
        /// Method to create a new Medical Contractor Personal Details
        /// </summary>
        /// <param name="mcBandAssocs"></param>
        /// <returns></returns>
        public async Task<int> AddMCPersonalDetails(List<MedContPersonalDetail> mcpersonalDetails)
        {
            await _updatableDBContext.MedContPersonalDetails.AddRangeAsync(mcpersonalDetails);
            return await _updatableDBContext.SaveChangesAsync();
        }


       public async Task<List<MedContPersonalDetail>> GetMedContPersonalDetails(int orgId, int medContId)
        {
            return await _readOnlyDbContext.MedContPersonalDetails.Where(s => s.OrgId == orgId && s.MedicalContractorId == medContId && s.StatusId == (short)Status.Active).AsNoTracking().ToListAsync();
        }


        /// <summary>
        /// GetMCPersonalDetailsByIds
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="mcpersonaDetailIds"></param>
        /// <returns></returns>
        public async Task<List<MedContPersonalDetail>> GetMCPersonalDetailsByIds(int orgId, List<int> mcpersonaDetailIds)
        {
            return await _readOnlyDbContext.MedContPersonalDetails
                .Where(s => mcpersonaDetailIds.Contains(s.Id) && s.OrgId == orgId)
                .ToListAsync();
        }

        /// <summary>
        /// EditMCPersonalDetails
        /// </summary>
        /// <param name="mcPersonalDetails"></param>
        /// <returns></returns>
        public async Task<int> EditMCPersonalDetails(List<MedContPersonalDetail> mcPersonalDetails)
        {
            _updatableDBContext.MedContPersonalDetails.UpdateRange(mcPersonalDetails);
            return await _updatableDBContext.SaveChangesAsync();
        }

        public async Task<List<MedicalContractorBandAssoc>> GetMedContBandAssocFromFilter(QueryModel queryModel, MedicalContractorFilter mcFilterModel, BaseHttpRequestContext baseHttpRequestContext)
        {


            return await _readOnlyDbContext.MedicalContractorBandAssocs.Where(s => s.OrgId == baseHttpRequestContext.OrgId &&
          (mcFilterModel != null && mcFilterModel.MedicalContractorId != null && mcFilterModel.MedicalContractorId.Count > 0 && mcFilterModel.MedicalContractorId.Contains(s.MedicalContractorId))
          && (mcFilterModel == null || mcFilterModel.MedicalContBandTypeId == null || mcFilterModel.MedicalContBandTypeId.Count == 0 || mcFilterModel.MedicalContBandTypeId.Contains(s.MedicalContBandTypeId))


          && s.StatusId == (short)Status.Active).AsNoTracking().ToListAsync();
          


        }

        public async Task<List<int?>> FetchMedicalContractorIds(MedicalContractorFilter mcFilterModel)
        {
           return await  _readOnlyDbContext.MedicalContractors.Where(x => ((mcFilterModel.ParticipantId != null && mcFilterModel.ParticipantId.Count > 0 && mcFilterModel.ParticipantId.Contains(x.ParticipantId))
            || (mcFilterModel.MasterMedicalParticipantsId != null && mcFilterModel.MasterMedicalParticipantsId.Count > 0 && mcFilterModel.MasterMedicalParticipantsId.Contains(x.MasterMedicalParticipantsId)))
            )
                .Select(x => (int?)x.Id).ToListAsync();
        }
        public async Task<List<MedicalContractor>> FetchMedicalContractors(MedicalContractorFilter mcFilterModel)
        {
            return await _readOnlyDbContext.MedicalContractors.Where(x => ((mcFilterModel.ParticipantId != null && mcFilterModel.ParticipantId.Count > 0 && mcFilterModel.ParticipantId.Contains(x.ParticipantId))
            || (mcFilterModel.MasterMedicalParticipantsId != null && mcFilterModel.MasterMedicalParticipantsId.Count > 0 && mcFilterModel.MasterMedicalParticipantsId.Contains(x.MasterMedicalParticipantsId)))
             )
                .ToListAsync();
        }

        /// <summary>
        /// Method to add Medical Contractor Band Version
        /// </summary>
        /// <param name="PrintTemplate"></param>
        /// <returns></returns>
        public async Task<int> AddMedContBandVersionDAL(MedicalContractorBandVersion inputMCBandVersion)
        {
            await _updatableDBContext.MedicalContractorBandVersions.AddAsync(inputMCBandVersion);
            int rows = await _updatableDBContext.SaveChangesAsync();
            return rows > 0 ? inputMCBandVersion.Id : 0;
        }


        /// <summary>
        /// Method to add Medical Contractor Band Version
        /// </summary>
        /// <param name="PrintTemplate"></param>
        /// <returns></returns>
        public async Task<int> AddMCBandVersionMultipleDAL(List<MedicalContractorBandVersion> inputMCBandVersion)
        {
            await _updatableDBContext.MedicalContractorBandVersions.AddRangeAsync(inputMCBandVersion);
            int rows = await _updatableDBContext.SaveChangesAsync();
            return rows > 0 ? rows : 0;
        }

        public async Task<short?> GetMCBandVersionLatest(int orgId, int medContId)
        {
            var query = await (from MCV in _readOnlyDbContext.MedicalContractorBandVersions
                         where MCV.OrgId == orgId && MCV.StatusId == (short)Status.Active
                         && MCV.MedicalContractorId == medContId
                         orderby MCV.VersionNo descending
                         select MCV.VersionNo).FirstOrDefaultAsync();

            return query;
        }

        public async Task<List<MCBandVersionView>> GetMedContBandVersionDAL(int orgId, int mcId)
        {


            List<MCBandVersionView> mvData = await (from mv in _readOnlyDbContext.MedicalContractorBandVersions
                                                    join mc in _readOnlyDbContext.MedicalContractors on mv.MedicalContractorParentId equals mc.Id into JC
                                                    from mc in JC.DefaultIfEmpty()
                                                    where mv.OrgId == orgId && mv.MedicalContractorId == mcId
                                                    select new MCBandVersionView
                                                    {
                                                        Id = mv.Id,
                                                        OrgId = mv.OrgId,
                                                        VersionNo = mv.VersionNo,
                                                        ParentVersionNo = mv.ParentVersionNo,
                                                        StartDate = mv.StartDate,
                                                        EndDate = mv.EndDate,
                                                        MedicalContractorId = mv.MedicalContractorId,
                                                        MedicalContractorParentId = mv.MedicalContractorParentId,
                                                        StatusId = mv.StatusId,
                                                        ParentMasterMedicalParticipantsId = mc.MasterMedicalParticipantsId,
                                                        MedicalContractorParentName = mc.Name,
                                                        MedicalContractorParentParticipantsId = mc.ParticipantId
                                                    }).ToListAsync();

            return mvData;

        }

        public async Task<bool> CheckMCVersionDatesDAL(int mcId, int orgId, DateTime startDate, DateTime? endDate)
        {
            bool isValid = false;


            if (endDate is null)
            {

              isValid = await _readOnlyDbContext.MedicalContractorBandVersions.Where(p => p.MedicalContractorId.Equals(mcId) && p.OrgId == orgId
                                                           && (p.StartDate >= startDate || p.EndDate >= startDate)).AnyAsync();



            }
            else { 

            isValid = await  _readOnlyDbContext.MedicalContractorBandVersions.Where(p => p.MedicalContractorId.Equals(mcId) && p.OrgId == orgId
                                                         && (p.StartDate >= startDate || p.EndDate >= startDate || p.StartDate >= endDate || p.EndDate >= endDate)).AnyAsync();

            }
            return isValid;
        }

        public async Task<MedicalContractorBandVersion> GetMCBandVersionLatestFull(int orgId, int medContId)
        {
            var query = await (from MCV in _readOnlyDbContext.MedicalContractorBandVersions
                               where MCV.OrgId == orgId && MCV.StatusId == (short)Status.Active
                               && MCV.MedicalContractorId == medContId
                               orderby MCV.VersionNo descending
                               select MCV).FirstOrDefaultAsync();

            return query;
        }

        public async Task<MedicalContractorBandVersion> GetMCBandGivenVersion(int orgId, int medContId,short versionNum)
        {
            var query = await (from MCV in _readOnlyDbContext.MedicalContractorBandVersions
                               where MCV.OrgId == orgId && MCV.StatusId == (short)Status.Active
                               && MCV.MedicalContractorId == medContId && MCV.VersionNo == versionNum
                               orderby MCV.VersionNo descending
                               select MCV).FirstOrDefaultAsync();

            return query;
        }

        /// <summary>
        /// GetMCChildsForGivenParent
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="mcBandassocs"></param>
        /// <returns></returns>
        public async Task<List<int>> GetMCChildForParent(int orgId, int mcparent)
        {
            return await _readOnlyDbContext.MedicalContractors
                .Where(s => s.MedicalContractorParentId == mcparent && s.OrgId == orgId && (s.StatusId==null ||  s.StatusId == (short)Status.Active))
                .Select(s=> s.Id)
                .ToListAsync();
        }


        /// <summary>
        /// GetMCChildsForGivenVersionandparent
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="mcBandassocs"></param>
        /// <returns></returns>
        public async Task<List<MedicalContractorBandVersion>> GetMCChildForParentVersion(int orgId, int mcparent,short versionNo)
        {
            return await _readOnlyDbContext.MedicalContractorBandVersions
                .Where(s => s.MedicalContractorParentId == mcparent && s.OrgId == orgId && s.StatusId == (short)Status.Active
                       && s.ParentVersionNo == versionNo)                
                .ToListAsync();
        }

        /*    public async Task<List<MedicalContractorBandVersion>> GetMCAllLatestChildData(int orgId, List<int> medContIds)
            {
                var query = await (from MCV in _readOnlyDbContext.MedicalContractorBandVersions
                                   where MCV.OrgId == orgId && MCV.StatusId == (short)Status.Active
                                         && medContIds.Contains(MCV.MedicalContractorId)
                                   group MCV by MCV.MedicalContractorId into grp
                                   select grp.OrderByDescending(x => x.VersionNo).FirstOrDefault())
                                  .ToListAsync();

                return query;
            }*/

        public async Task<List<MedicalContractorBandVersion>> GetMCAllLatestChildData(int orgId, List<int> medContIds)
        {
            var query = await (from MCV in _readOnlyDbContext.MedicalContractorBandVersions
                               where MCV.OrgId == orgId && MCV.StatusId == (short)Status.Active
                                     && medContIds.Contains(MCV.MedicalContractorId)
                               select MCV)
                              .ToListAsync();

            var latestVersions = query
                .GroupBy(m => m.MedicalContractorId)
                .Select(grp => grp.OrderByDescending(x => x.VersionNo).FirstOrDefault())
                .ToList();

            return latestVersions;
        }

        /// <summary>
        /// EditMCBandVersions 
        /// </summary>
        /// <param name="mcBandassocs"></param>
        /// <returns></returns>
        public async Task<int> EditMcBandVersions(List<MedicalContractorBandVersion> mcBandVersions)
        {
            _updatableDBContext.MedicalContractorBandVersions.UpdateRange(mcBandVersions);
            return await _updatableDBContext.SaveChangesAsync();
        }

        public async Task<int?> GetMCIdForParticipantId(string ParticipantId,int orgId)
        {

            int? mcId = await (from MC in _readOnlyDbContext.MedicalContractors
                              where MC.ParticipantId == ParticipantId && MC.OrgId ==orgId
                              select MC.Id).FirstOrDefaultAsync();
            return mcId;
        }

        public async Task<List<MedicalContractorBandAssoc>> GetMCBandAssocForParticipantId(int orgId, int mcId, EstimateFilter mcEstimateFilter)
        {

            int dbmcId = 0;
            short dbVersionNo = 0;

            MedicalContractorBandVersion versionData = await (from MCV in _readOnlyDbContext.MedicalContractorBandVersions
                                                                where MCV.StartDate <= mcEstimateFilter.ServiceDate && MCV.MedicalContractorId == mcId
                                                                && MCV.StatusId == (short)Status.Active
                                                                orderby MCV.StartDate descending
                                                                select MCV).FirstOrDefaultAsync();

            if (versionData is null)
            {
                return null;
            }

            // To fetch the values for Parent 
            if (versionData.MedicalContractorParentId is null)
            {
                dbmcId = mcId;
                dbVersionNo = (short)versionData.VersionNo;
            }
            else // To Fetch Values for child 
            {
                dbmcId = (int)versionData.MedicalContractorParentId;
                dbVersionNo = (short)versionData.ParentVersionNo;
              
            }

            var query = from MCA in _readOnlyDbContext.MedicalContractorBandAssocs
                        where MCA.OrgId == orgId && MCA.StatusId == (short)Status.Active
                        && MCA.MedicalContractorId == dbmcId && MCA.VersionNo == dbVersionNo
                        select MCA;

            if (mcEstimateFilter is not null)
            {
                if (mcEstimateFilter.MedicalContBandTypeIds != null)
                {
                    query = query.Where(x => mcEstimateFilter.MedicalContBandTypeIds.Contains(x.MedicalContBandTypeId));
                }
            }
            if (mcEstimateFilter is not null)
            {
                if (mcEstimateFilter.BandIds != null)
                {
                    query = query.Where(x => mcEstimateFilter.BandIds.Contains(x.Banding));
                }
            }

            List<MedicalContractorBandAssoc> MedContBandList = await query.AsNoTracking().ToListAsync();

            return MedContBandList;
        }

        public async Task<int?> GetMCIdFromMasterMedPartId(int masterMedPartId, int orgId)
        {
            int? mcId = await (from MC in _readOnlyDbContext.MedicalContractors
                               where MC.MasterMedicalParticipantsId == masterMedPartId && MC.OrgId == orgId
                               select MC.Id).FirstOrDefaultAsync();
            return mcId;

        }

        public async Task<int?> GetMCIdFromCompanyId(int CompanyDetailsId, int OrgId)
        {

            int? mcId = await(from MC in _readOnlyDbContext.MedicalContractors
                              where MC.CompanyDetailsId == CompanyDetailsId && MC.OrgId == OrgId
                              select MC.Id).FirstOrDefaultAsync();
            return mcId;
        }

        /// <summary>
        /// Method to check if the Company Id exixts in the tables 
        /// </summary>
        /// <param name="search_term"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<bool> CheckMedContCompanyDAL(int search_id, int orgId)
        {
            MedicalContractor name = await _readOnlyDbContext.MedicalContractors
                   .Where(p => p.CompanyDetailsId.Equals(search_id) && p.OrgId == orgId && p.StatusId == (short)Status.Active).FirstOrDefaultAsync();
            if (name == null)
                return false;
            else
                return true;
        }

        public async Task<MedicalContractorAddressInfo> FetchMCPostalAddress(int? mcId, int orgId)
        {
            var query = from Addr in _readOnlyDbContext.MedicalContractorAddresses.Where(x => x.OrgId == orgId && x.StatusId == (short)Status.Active && x.AddressType == (short)AddressType.Postal && x.MedicalContractorId == mcId)
                 select  new MedicalContractorAddressInfo
                        {
                            AddressLine1 = Addr.AddressLine1,
                            AddressLine2 = Addr.AddressLine2,
                            Suburb = Addr.Suburb,
                            StateId = Addr.StateId,
                            CountryId = Addr.CountryId,
                            PostCode = Addr.PostCode,
                            AddressType = Addr.AddressType
                        };

            return await query.FirstOrDefaultAsync();
                   
        }
    }
}
