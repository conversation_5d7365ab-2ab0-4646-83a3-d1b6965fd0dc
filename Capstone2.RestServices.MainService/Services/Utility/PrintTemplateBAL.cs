﻿using AutoMapper;
using Capstone2.Framework.RestApi;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.Utility.Interfaces;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Transactions;
using System.Web;

namespace Capstone2.RestServices.Utility.Services
{
    public class PrintTemplateBAL : IPrintTemplateBAL
    {
        public readonly IPrintTemplateDAL _printTemplateDAL;
        public IMapper _mapper;
        public readonly AppSettings _appSettings;

        public PrintTemplateBAL(IPrintTemplateDAL printTemplateDAL, IMapper mapper, IOptions<AppSettings> appSettings)
        {
            _printTemplateDAL = printTemplateDAL;
            _mapper = mapper;
            _appSettings = appSettings.Value;
        }

        /// <summary>
        /// Method to save a entry in PrintTemplate
        /// </summary>
        /// <param name="PrintTemplatePayload"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> AddPrintTemplate(BaseHttpRequestContext baseHttpRequestContext, PrintTemplate PrintTemplate)
        {
            var orgId = baseHttpRequestContext.OrgId;
            var loggedInUser = baseHttpRequestContext.UserId;

            PrintTemplate.OrgId = orgId;
            PrintTemplate.CreatedBy = loggedInUser;
            PrintTemplate.CreatedDate = DateTime.UtcNow;
            PrintTemplate.StatusId = (short)Status.Active;

            if (PrintTemplate?.PrintTemplateControls?.Any() ?? false)
            {
                PrintTemplate.PrintTemplateControls.ToList().ForEach(a => 
                { 
                    a.OrgId = orgId; a.CreatedBy = loggedInUser; a.StatusId = (short)Status.Active;
                });
            }

            DateTime.SpecifyKind(PrintTemplate.CreatedDate, DateTimeKind.Utc);
            var id = await _printTemplateDAL.AddPrintTemplateAsync(PrintTemplate);
            if (id > 0)
            {
                ApiResponse<long?> apiResponse = new()
                {
                    StatusCode = StatusCodes.Status200OK,
                    Message = "Success",
                    Result = id
                };
                return apiResponse;
            }
            else
            {
                ApiResponse<long?> apiResponse = new()
                {
                    StatusCode = StatusCodes.Status400BadRequest,
                    Message = "Failure",
                    Result = null
                };
                return apiResponse;
            }
        }

        /// <summary>
        /// Method to generate a paginated list of PrintTemplate based on search criteria
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        public async Task<ApiResponse<QueryResultList<PrintTemplateList>>> ListPrintTemplate(BaseHttpRequestContext baseHttpRequestContext, QueryModel queryModel)
        {
            ApiResponse<QueryResultList<PrintTemplateList>> apiResponse = new();
            var orgId = baseHttpRequestContext.OrgId;
            var loggedInUser = baseHttpRequestContext.UserId;

            var queryList = await _printTemplateDAL.ListPrintTemplate(orgId, loggedInUser, queryModel);

            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result = queryList;
            return apiResponse;
        }

        /// <summary>
        /// Get PrintTemplateby ID
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<PrintTemplateView>> GetPrintTemplate(BaseHttpRequestContext baseHttpRequestContext, long id)
        {
            ApiResponse<PrintTemplateView> apiResposne = new();
            var orgId = baseHttpRequestContext.OrgId;
            var PrintTemplateFromDB = await _printTemplateDAL.GetPrintTemplate(orgId, id);
            if (PrintTemplateFromDB is null)
            {
                apiResposne.Result = null;
                apiResposne.StatusCode = StatusCodes.Status400BadRequest;
                apiResposne.Message = "Failure";
                apiResposne.Errors.Add("The PrintTemplatedoesnot exist.");
                return apiResposne;
            }
            if (PrintTemplateFromDB is not null)
            {
                PrintTemplateFromDB.CreatedDate = DateTime.SpecifyKind(PrintTemplateFromDB.CreatedDate, DateTimeKind.Utc);
                PrintTemplateFromDB.ModifiedDate = (PrintTemplateFromDB.ModifiedDate == null) ? null : DateTime.SpecifyKind((DateTime)PrintTemplateFromDB.ModifiedDate, DateTimeKind.Utc);

                if (PrintTemplateFromDB.FileDetailsId != null)
                {
                    PrintTemplateFromDB.FileDetails = await FetchFileDetailsForMediaAsync(PrintTemplateFromDB.FileDetailsId, baseHttpRequestContext);
                }

                apiResposne.Result = PrintTemplateFromDB;
                apiResposne.StatusCode = StatusCodes.Status200OK;
                apiResposne.Message = "Success";
                return apiResposne;
            }
            else
            {
                apiResposne.Result = null;
                apiResposne.StatusCode = StatusCodes.Status500InternalServerError;
                apiResposne.Message = "Data for given PrintTemplateID is not found";
                return apiResposne;
            }
        }

        private async Task<FileDetailsOutputForId> FetchFileDetailsForMediaAsync(long? fileDetailsId, BaseHttpRequestContext baseHttpRequestContext)
        {
            var token = baseHttpRequestContext.BearerToken;
            string interServiceToken = baseHttpRequestContext.InterServiceToken;
            string fileAPiUrl = _appSettings.ApiUrls["FileServiceUrl"] + "/file/file_details_list?" + "stringFileDetailsId" + "=" + HttpUtility.UrlEncode(fileDetailsId.ToString());
            var restClient = new RestClient(fileAPiUrl, null, token, interServiceToken);
            var fileApiResponse = await restClient.GetAsync<ApiResponse<List<FileDetailsOutputForId>>>(fileAPiUrl, null);
            if (fileApiResponse.StatusCode == StatusCodes.Status200OK)
            {
                return fileApiResponse.Result[0];
            }
            return null;
        }

        /// <summary>
        /// Edit Appointment Type
        /// </summary>
        /// <param name="id"></param>
        /// <param name="inputPrintTemplate"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> EditPrintTemplate(BaseHttpRequestContext baseHttpRequestContext, long id, PrintTemplate inputPrintTemplate)
        {
            ApiResponse<long?> apiResponse = new();
            var orgId = baseHttpRequestContext.OrgId;
            var userId = baseHttpRequestContext.UserId;
            var PrintTemplateFromDB = await _printTemplateDAL.GetPrintTemplateSync(orgId, id);

            inputPrintTemplate.OrgId = orgId;
            inputPrintTemplate.ModifiedDate = DateTime.UtcNow;
            inputPrintTemplate.ModifiedBy = userId;
            inputPrintTemplate.CreatedBy = PrintTemplateFromDB.CreatedBy;
            inputPrintTemplate.CreatedDate = PrintTemplateFromDB.CreatedDate;
            inputPrintTemplate.Id = PrintTemplateFromDB.Id;

            if (inputPrintTemplate != null && inputPrintTemplate.PrintTemplateControls.Any())
            {
                inputPrintTemplate.PrintTemplateControls.ToList().ForEach(a =>
                {
                    a.OrgId = orgId; a.StatusId = (short)Status.Active; a.PrintTemplateId = id;
                });
            }

            using (var transaction = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
            {
                inputPrintTemplate.PrintTemplateControls = await EditPrintTemplateControls(inputPrintTemplate.PrintTemplateControls, id, orgId, userId);

                await _printTemplateDAL.UpdatePrintTemplate(inputPrintTemplate);

                apiResponse.Result = id;
                apiResponse.Message = "Success";
                apiResponse.StatusCode = StatusCodes.Status200OK;

                transaction.Complete();
            }

            return apiResponse;
        }

        private async Task<List<PrintTemplateControls>> EditPrintTemplateControls(ICollection<PrintTemplateControls> inputControlList, long id, int orgId, long userId)
        {
            List<PrintTemplateControls> addControlList = new();

            var removeUserList = await _printTemplateDAL.GetPrintTemplateControlsSync(orgId, id);

            foreach (var control in inputControlList)
            {
                if (control.Id > 0)
                {
                    var existingobj = removeUserList.FirstOrDefault(x => x.Id == control.Id);
                    removeUserList.Remove(existingobj);
                    control.ModifiedDate = DateTime.UtcNow;
                    control.ModifiedBy = userId;
                    control.StatusId = (short)Status.Active;
                    addControlList.Add(control);
                }
                else
                {
                    control.CreatedDate = DateTime.UtcNow;
                    control.CreatedBy = userId;
                    control.StatusId = (short)Status.Active;
                    addControlList.Add(control);

                    //var userAssocDB = removeUserList.Where(x => x.Key == control.Key).FirstOrDefault();
                    //if (userAssocDB is null)
                    //{
                    //    control.CreatedDate = DateTime.UtcNow;
                    //    control.CreatedBy = userId;
                    //    control.StatusId = (short)Status.Active;
                    //    addControlList.Add(control);
                    //}
                    //else
                    //{
                    //    if (userAssocDB.StatusId == (short)Status.Deleted)
                    //    {
                    //        userAssocDB.StatusId = (short)Status.Active;
                    //        userAssocDB.ModifiedDate = DateTime.UtcNow;
                    //        userAssocDB.ModifiedBy = userId;
                    //        addControlList.Add(userAssocDB);
                    //    }
                    //    removeUserList.Remove(userAssocDB);
                    //}
                }
            }

            foreach (var userAssoc in removeUserList.Where(x => x.StatusId == (short)Status.Active))
            {
                userAssoc.StatusId = (short)Status.Deleted;
                userAssoc.ModifiedDate = DateTime.UtcNow;
                userAssoc.ModifiedBy = userId;
            }

            addControlList = addControlList.Concat(removeUserList).ToList();
            return addControlList;
        }

        /// <summary>
        /// Method to check if the entered PrintTemplateis unique
        /// </summary>
        /// <param name="search_term"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<ApiResponse<bool>> CheckPrintTemplateName(BaseHttpRequestContext baseHttpRequestContext, string search_term)
        {
            var nameBool = await _printTemplateDAL.CheckPrintTemplateName(search_term, baseHttpRequestContext.OrgId);
            return new ApiResponse<bool>
            {
                Result = nameBool,
                Message = "Success",
                StatusCode = StatusCodes.Status200OK
            };
        }
    }
}
