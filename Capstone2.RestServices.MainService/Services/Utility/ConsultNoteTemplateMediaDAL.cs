﻿using Capstone2.RestServices.Utility.Context;
using Capstone2.RestServices.Utility.Interfaces;
using Capstone2.Shared.Models.Entities;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Utility.Services
{
    public class ConsultNoteTemplateMediaDAL : IConsultNoteTemplateMediaDAL
    {
        public readonly ReadOnlyUtilityDBContext _readOnlyDbContext;
        public readonly UpdatableUtilityDBContext _updatableDBContext;
        public ConsultNoteTemplateMediaDAL(ReadOnlyUtilityDBContext readOnlyDbContext, UpdatableUtilityDBContext updatableDBContext)
        {
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="media"></param>
        /// <returns></returns>
        public async Task<long?> AddMedia(ConsultNoteTemplateMedia media)
        {
            await _updatableDBContext.ConsultNoteTemplateMedia.AddAsync(media);
            await _updatableDBContext.SaveChangesAsync();
            return media.Id;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="mediaCollection"></param>
        /// <returns></returns>
        public async Task<int> AddMediaCollection(List<ConsultNoteTemplateMedia> mediaCollection)
        {
            _updatableDBContext.ConsultNoteTemplateMedia.AddRange(mediaCollection);
            return await _updatableDBContext.SaveChangesAsync();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="consultNoteTemplateId"></param>
        /// <returns></returns>
        public async Task<List<ConsultNoteTemplateMediaView>> ListMediaLibrary(int orgId, long templateId, long controlId)
        {
            var medias = (from CN in _readOnlyDbContext.ConsultNoteTemplateMedia
                          where CN.ConsultNoteTemplateControlsId == controlId && CN.OrgId == orgId
                          select new ConsultNoteTemplateMediaView
                          {
                                Id = CN.Id,
                                OrgId = CN.OrgId,
                                ConsultNoteTemplateControlsId = CN.ConsultNoteTemplateControlsId,
                                FileDetailsId = CN.FileDetailsId,
                                MediaName = CN.MediaName,
                                MediaTypeId = CN.MediaTypeId,
                                StatusId = CN.StatusId,
                                CreatedBy = CN.CreatedBy,
                                CreatedDate = CN.CreatedDate
                          }); 
            return await medias.ToListAsync();
        }
    }
}
