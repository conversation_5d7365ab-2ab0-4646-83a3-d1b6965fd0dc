﻿using Capstone2.RestServices.Utility.Context;
using Capstone2.RestServices.Utility.Interfaces;
using Capstone2.RestServices.Utility.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AppointmentTypes = Capstone2.RestServices.Utility.Models.AppointmentTypes;

namespace Capstone2.RestServices.Utility.Services
{
    public class AppointmentTypeDAL : IAppointmentTypeDAL
    {
        public readonly ReadOnlyUtilityDBContext _readOnlyDbContext;
        public readonly UpdatableUtilityDBContext _updatableDBContext;
        public AppointmentTypeDAL(ReadOnlyUtilityDBContext readOnlyDbContext, UpdatableUtilityDBContext updatableDBContext)
        {
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
        }

        /// <summary>
        /// Method to add AppointmentType to AppointmentTypes table
        /// </summary>
        /// <param name="AppointmentType"></param>
        /// <returns></returns>
        public async Task<long> AddAppointmentTypesAsync(AppointmentTypes appointmentType)
        {
            await _updatableDBContext.AppointmentTypes.AddAsync(appointmentType);
            int rows = await _updatableDBContext.SaveChangesAsync();
            return rows > 0 ? appointmentType.Id : 0;
        }

        /// <summary>
        /// Method to retrieve list of Appointment Types
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="loggedInUser"></param>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        public async Task<QueryResultList<AppointmentTypeList>> ListAppointmentTypes(int orgId, long loggedInUser, QueryModel queryModel, AppointmentTypeFilterModel appointmentTypeFilterModel)
        {
            var appointmentTypeQuery = (from AT in _readOnlyDbContext.AppointmentTypes
                                        where AT.OrgId == orgId
                                        join C in _readOnlyDbContext.Checklists on AT.ChecklistId equals C.Id into JC
                                        from C in JC.DefaultIfEmpty()
                                        select new AppointmentTypeList
                                        {
                                            Id = AT.Id,
                                            OrgId = AT.OrgId,
                                            Category = AT.Category,
                                            Type = AT.Type,
                                            Colour = AT.Colour,
                                            StatusId = AT.StatusId,
                                            CreatedBy = AT.CreatedBy,
                                            CreatedDate = AT.CreatedDate,
                                            TimeAllocated = AT.TimeAllocated,
                                            ChecklistId = AT.ChecklistId,
                                            ChecklistName = C == null ? null : C.Name
                                        });
            if (appointmentTypeFilterModel is not null)
            {
                if (appointmentTypeFilterModel.StatusId is not null && appointmentTypeFilterModel.StatusId.Any())
                {
                    appointmentTypeQuery = appointmentTypeQuery.Where(a => appointmentTypeFilterModel.StatusId.Contains(a.StatusId));
                }
                if (appointmentTypeFilterModel.AppointmentCategoryId is not null && appointmentTypeFilterModel.AppointmentCategoryId.Any())
                {
                    appointmentTypeQuery = appointmentTypeQuery.Where(a => appointmentTypeFilterModel.AppointmentCategoryId.Contains(a.Category));
                }
            }

            if (!(string.IsNullOrWhiteSpace(queryModel.SearchTerm)))
            {
                appointmentTypeQuery = appointmentTypeQuery.Where(s => s.Type.Contains(queryModel.SearchTerm));
            }
            if (!(string.IsNullOrWhiteSpace(queryModel.SortOrder)) && !(string.IsNullOrWhiteSpace(queryModel.SortTerm)))
            {
                appointmentTypeQuery = SortAppointmentTypes(appointmentTypeQuery, queryModel.SortTerm, queryModel.SortOrder);
            }

            var paginatedList = await CreatePaginatedListAsync(appointmentTypeQuery, queryModel);

            var queryList = new QueryResultList<AppointmentTypeList>();
            if (paginatedList != null)
            {
                queryList.ItemRecords = paginatedList;
                queryList.PageNumber = queryModel.PageNumber;
                queryList.PageSize = queryModel.PageSize;
                queryList.CurrentCount = paginatedList.Count();
            }
            else
            {
                queryList.ItemRecords = null;
                queryList.CurrentCount = 0;
            }

            queryList.TotalCount = appointmentTypeQuery.Count();
            queryList.PageNumber = queryModel.PageNumber;
            queryList.PageSize = queryModel.PageSize;

            return queryList;
        }

        private async Task<List<AppointmentTypeList>> CreatePaginatedListAsync(IQueryable<AppointmentTypeList> appointmentType, QueryModel queryModel)
        {
            if (queryModel.PageNumber > 0 && queryModel.PageSize > 0)
            {
                if (appointmentType.Any())
                {
                    return await appointmentType
                            .Skip((queryModel.PageNumber - 1) * queryModel.PageSize)
                            .Take(queryModel.PageSize)
                            .ToListAsync();
                }
            }
            return null;
        }

        /// <summary>
        /// Method to Appointment Type based on id
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<AppointmentTypeView> GetAppointmentTypes(int orgId, long id)
        {
            var appointmentType = (from AT in _readOnlyDbContext.AppointmentTypes.Where(x => x.Id == id && x.OrgId == orgId)
                                   join C in _readOnlyDbContext.Checklists on AT.ChecklistId equals C.Id into JC
                                   from C in JC.DefaultIfEmpty()
                                   select new AppointmentTypeView
                                   {
                                       Id = AT.Id,
                                       OrgId = AT.OrgId,
                                       Category = AT.Category,
                                       Type = AT.Type,
                                       Colour = AT.Colour,
                                       TimeAllocated = AT.TimeAllocated,
                                       ChecklistAllocated = AT.ChecklistAllocated,
                                       IsAllowDuplicateOn = AT.IsAllowDuplicateOn,
                                       IsInpatient = AT.IsInpatient,
                                       Location = AT.Location,
                                       IsDepositRequired = AT.IsDepositRequired,
                                       IsDepositPaid = AT.IsDepositPaid,
                                       IsSmsReminderOn = AT.IsSmsReminderOn,
                                       IsReferralRequired = AT.IsReferralRequired,
                                       IsInsured = AT.IsInsured,
                                       IsEquipmentRequired = AT.IsEquipmentRequired,
                                       EquipmentName = AT.EquipmentName,
                                       IsHospitalRequired = AT.IsHospitalRequired,
                                       IsHcp = AT.IsHcp,
                                       IsAnaesthetistRequired = AT.IsAnaesthetistRequired,
                                       IsAssistantRequired = AT.IsAssistantRequired,
                                       StatusId = AT.StatusId,
                                       CreatedBy = AT.CreatedBy,
                                       CreatedDate = AT.CreatedDate,
                                       ModifiedBy = AT.ModifiedBy,
                                       ModifiedDate = AT.ModifiedDate,
                                       ChecklistId = AT.ChecklistId,
                                       ChecklistName = C == null ? null : C.Name,
                                       ConfirmationSMSTemplateID = AT.ConfirmationSMSTemplateID,
                                       MainLocation = AT.MainLocation,
                                       AppointmentConfirmationTemplate = (from AC in _readOnlyDbContext.LetterTemplates.Where(x => x.Id == AT.ConfirmationSMSTemplateID && x.OrgId == orgId && x.StatusId == (short)Status.Active)
                                                                          select new LetterTemplateData
                                                                          {
                                                                              Name = AC.Name,
                                                                              Id = AC.Id,
                                                                              Text = AC.Text,
                                                                          }).FirstOrDefault(),

                                       HospitalAssocs = (ICollection<AppointmentTypeHospitalAssocsView>)(
                                                             from AH in _readOnlyDbContext.AppointmentTypesHospitalAssocs.Where(x => x.AppointmentTypesId == id && x.OrgId == orgId && x.StatusId == (short)Status.Active)
                                                             from UD in _readOnlyDbContext.CompanyDetails.Where(u => u.OrgId == orgId && u.Id == AH.CompanyDetailsId && u.StatusId == (short)CompanyStatus.Active)
                                                             select new AppointmentTypeHospitalAssocsView
                                                             {
                                                                 Id = AH.Id,
                                                                 OrgId = AH.OrgId,
                                                                 AppointmentTypesId = AH.AppointmentTypesId,
                                                                 CompanyDetailsId = AH.CompanyDetailsId,
                                                                 CompanyDetails = new AppointmentTypeCompanyDetails
                                                                 {
                                                                     Id = UD.Id,
                                                                     Name = UD.Name
                                                                 }
                                                             }),

                                       AnaesthetistsAssocs = (ICollection<AppointmentTypesAnaesthetistAssocsView>)(
                                                             from AU in _readOnlyDbContext.AppointmentTypesAnaesthetistAssocs.Where(x => x.AppointmentTypesId == id && x.OrgId == orgId && x.StatusId == (short)Status.Active)
                                                             from UD in _readOnlyDbContext.UserDetails.Where(u => u.OrgId == orgId && u.Id == AU.UserDetailsId)
                                                             select new AppointmentTypesAnaesthetistAssocsView
                                                             {
                                                                 Id = AU.Id,
                                                                 OrgId = AU.OrgId,
                                                                 AppointmentTypesId = AU.AppointmentTypesId,
                                                                 UserDetailsId = AU.UserDetailsId,
                                                                 UserDetails = new AppointmentTypeUserDetails
                                                                 {
                                                                     Id = UD.Id,
                                                                     FirstName = UD.FirstName,
                                                                     SurName = UD.SurName
                                                                 }
                                                             }),

                                       AssistantsAssocs = (ICollection<AppointmentTypesAssistantAssocsView>)(
                                                             from AU in _readOnlyDbContext.AppointmentTypesAssistantAssocs.Where(x => x.AppointmentTypesId == id && x.OrgId == orgId && x.StatusId == (short)Status.Active)
                                                             from UD in _readOnlyDbContext.UserDetails.Where(u => u.OrgId == orgId && u.Id == AU.UserDetailsId)
                                                             select new AppointmentTypesAssistantAssocsView
                                                             {
                                                                 Id = AU.Id,
                                                                 OrgId = AU.OrgId,
                                                                 AppointmentTypesId = AU.AppointmentTypesId,
                                                                 UserDetailsId = AU.UserDetailsId,
                                                                 UserDetails = new AppointmentTypeUserDetails
                                                                 {
                                                                     Id = UD.Id,
                                                                     FirstName = UD.FirstName,
                                                                     SurName = UD.SurName
                                                                 }
                                                             }),

                                       SMSAssocs = (ICollection<AppointmentTypesSMSAssocsView>)(
                                                             from AS in _readOnlyDbContext.AppointmentTypesSMSAssocs.Where(x => x.AppointmentTypesId == id && x.OrgId == orgId && x.StatusId == (short)Status.Active)
                                                             from SD in _readOnlyDbContext.LetterTemplates.Where(u => u.OrgId == orgId && u.Id == AS.SMSTemplateId)
                                                             select new AppointmentTypesSMSAssocsView
                                                             {
                                                                 Id = AS.Id,
                                                                 OrgId = AS.OrgId,
                                                                 AppointmentTypesId = AS.AppointmentTypesId,
                                                                 SMSTemplateId = AS.SMSTemplateId,
                                                                 Days = AS.Days,
                                                                 IsBefore = AS.IsBefore,
                                                                 IsReplyExpected = AS.IsReplyExpected,
                                                                 SMSTemplate = new AppointmentTypeSMSDetails
                                                                 {
                                                                     Id = SD.Id,
                                                                     Text = SD.Text,
                                                                     Name = SD.Name,
                                                                 }
                                                             }),
                                   });
            return await appointmentType.FirstOrDefaultAsync();
        }

        /// <summary>
        /// Fetch letter Template
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<AppointmentTypes> GetAppointmentTypesFromId(int orgId, long id)
        {
            return await _readOnlyDbContext.AppointmentTypes.Where(s => s.Id == id && s.OrgId == orgId).AsNoTracking().FirstOrDefaultAsync();
        }

        /// <summary>
        /// Fetch AppointmentTypesHospitalAssocs
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="AppointmentTypesId"></param>
        /// <returns></returns>
        public async Task<List<AppointmentTypesHospitalAssocs>> GetAppointmentTypesHospitalAssoc(int orgId, long AppointmentTypesId)
        {
            return await _readOnlyDbContext.AppointmentTypesHospitalAssocs.Where(s => s.AppointmentTypesId == AppointmentTypesId && s.OrgId == orgId).AsNoTracking().ToListAsync();
        }

        /// <summary>
        /// Fetch AppointmentTypesAnaesthetistAssocs
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="AppointmentTypesId"></param>
        /// <returns></returns>
        public async Task<List<AppointmentTypesAnaesthetistAssocs>> GetAppointmentTypesAnaesthetistAssocs(int orgId, long AppointmentTypesId)
        {
            return await _readOnlyDbContext.AppointmentTypesAnaesthetistAssocs.Where(s => s.AppointmentTypesId == AppointmentTypesId && s.OrgId == orgId).AsNoTracking().ToListAsync();
        }

        /// <summary>
        /// Fetch AppointmentTypesAssistantAssocs
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="AppointmentTypesId"></param>
        /// <returns></returns>
        public async Task<List<AppointmentTypesAssistantAssocs>> GetAppointmentTypesAssistantAssocs(int orgId, long AppointmentTypesId)
        {
            return await _readOnlyDbContext.AppointmentTypesAssistantAssocs.Where(s => s.AppointmentTypesId == AppointmentTypesId && s.OrgId == orgId).AsNoTracking().ToListAsync();
        }

        /// <summary>
        /// Update Appointment Type
        /// </summary>
        /// <param name="appointmentType"></param>
        /// <returns></returns>
        public async Task<int> UpdateAppointmentTypes(AppointmentTypes appointmentType)
        {
            _updatableDBContext.AppointmentTypes.Update(appointmentType);
            return await _updatableDBContext.SaveChangesAsync();
        }

        /// <summary>
        /// Method to check if the Appointment Type exists in the database
        /// </summary>
        /// <param name="search_term"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<bool> CheckAppointmentType(string search_term, int orgId)
        {
            var name = await _readOnlyDbContext.AppointmentTypes
                   .Where(p => p.Type.Equals(search_term) && p.OrgId == orgId)
                   .FirstOrDefaultAsync();

            return name != null;
        }

        /// <summary>
        /// Method to check if the Appointment Colour exists in the database
        /// </summary>
        /// <param name="search_term"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<bool> CheckAppointmentColour(string search_term, int orgId)
        {
            var name = await _readOnlyDbContext.AppointmentTypes
                   .Where(p => p.Colour.Equals(search_term) && p.OrgId == orgId)
                   .FirstOrDefaultAsync();

            return name != null;
        }

        /// <summary>
        /// Method to fetch list of Checklists
        /// </summary>
        /// <param name="searchTerm"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<List<Checklist>> ListChecklists(string searchTerm, int orgId)
        {
            IQueryable<Checklist> lstChecklists = _readOnlyDbContext.Checklists.Where(x => x.OrgId == orgId && x.StatusId == (short)Status.Active).OrderBy(x => x.Name);
            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                lstChecklists = lstChecklists.Where(x => x.Name.StartsWith(searchTerm));
            }
            return await lstChecklists.ToListAsync();
        }

        private IQueryable<AppointmentTypeList> SortAppointmentTypes(IQueryable<AppointmentTypeList> AppointmentTypeQuery, string sortTerm, string sortOrder)
        {
            switch (sortTerm.ToLower())
            {
                case "id":
                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            AppointmentTypeQuery = AppointmentTypeQuery.OrderBy(x => x.StatusId).ThenBy(y => y.Id);
                        }
                        else
                        {
                            AppointmentTypeQuery = AppointmentTypeQuery.OrderBy(x => x.StatusId).ThenByDescending(y => y.Id);
                        }
                        break;
                    }
                case "name":
                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            AppointmentTypeQuery = AppointmentTypeQuery.OrderBy(x => x.StatusId).ThenBy(y => y.Type);
                        }
                        else
                        {
                            AppointmentTypeQuery = AppointmentTypeQuery.OrderBy(x => x.StatusId).ThenByDescending(y => y.Type);
                        }
                        break;
                    }
            }

            return AppointmentTypeQuery;
        }

        /// <summary>
        /// Fetch AppointmentTypesSMSAssocs
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="AppointmentTypesId"></param>
        /// <returns></returns>
        public async Task<List<AppointmentTypesSMSAssocs>> GetAppointmentTypesSMSAssocs(int orgId, long AppointmentTypesId)
        {
            return await _readOnlyDbContext.AppointmentTypesSMSAssocs.Where(s => s.AppointmentTypesId == AppointmentTypesId && s.OrgId == orgId && s.StatusId == (short)Status.Active).AsNoTracking().ToListAsync();
        }
    }
}
