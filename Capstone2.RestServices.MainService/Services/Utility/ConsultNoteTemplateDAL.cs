﻿using Capstone2.RestServices.Utility.Context;
using Capstone2.RestServices.Utility.Interfaces;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Text.RegularExpressions;
using Capstone2.RestServices.Utility.Models;
using Capstone2.Framework.RestApi.Dtos;
using System;

namespace Capstone2.RestServices.Utility.Services
{
    public class ConsultNoteTemplateDAL : IConsultNoteTemplateDAL
    {
        public readonly ReadOnlyUtilityDBContext _readOnlyDbContext;
        public readonly UpdatableUtilityDBContext _updatableDBContext;
        public ConsultNoteTemplateDAL(ReadOnlyUtilityDBContext readOnlyDbContext, UpdatableUtilityDBContext updatableDBContext)
        {
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
        }

        /// <summary>
        /// Method to add ConsultNoteTemplate to ConsultNoteTemplate table
        /// </summary>
        /// <param name="ConsultNoteTemplate"></param>
        /// <returns></returns>
        public async Task<long> AddConsultNoteTemplateAsync(ConsultNoteTemplate consultNoteTemplate)
        {
            await _updatableDBContext.ConsultNoteTemplate.AddAsync(consultNoteTemplate);
            int rows = await _updatableDBContext.SaveChangesAsync();
            return rows > 0 ? consultNoteTemplate.Id : 0;
        }

        /// <summary>
        /// Method to retrieve list of ConsultNoteTemplates
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="loggedInUser"></param>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        public async Task<QueryResultList<ConsultNoteTemplateList>> ListConsultNoteTemplate(BaseHttpRequestContext baseHttpRequestContext, RolePermissionFilterModel rolePermissionFilterModel, QueryModel queryModel)
        {
            var consultNoteTemplateQuery = (from CN in _readOnlyDbContext.ConsultNoteTemplate
                                            join UD in _readOnlyDbContext.UserDetails on CN.CreatedBy equals UD.Id
                                            where CN.OrgId == baseHttpRequestContext.OrgId
                                            select new ConsultNoteTemplateList
                                            {
                                                Id = CN.Id,
                                                OrgId = CN.OrgId,
                                                Name = CN.Name,
                                                StatusId = CN.StatusId,
                                                CreatedBy = CN.CreatedBy,
                                                CreatedDate = CN.CreatedDate,
                                                CreatedByFirstName = UD.FirstName,
                                                CreatedBySurName = UD.SurName
                                            });
            if (!(string.IsNullOrWhiteSpace(queryModel.SearchTerm)))
            {
                consultNoteTemplateQuery = SearchConsultNoteTemplates(consultNoteTemplateQuery, queryModel.SearchTerm);
            }
            if (!(string.IsNullOrWhiteSpace(queryModel.SortOrder)) && !(string.IsNullOrWhiteSpace(queryModel.SortTerm)))
            {
                consultNoteTemplateQuery = SortConsultNoteTemplate(consultNoteTemplateQuery, queryModel.SortTerm, queryModel.SortOrder);
            }

            if (baseHttpRequestContext.RoleId != (int)RoleType.SuperUser && rolePermissionFilterModel.isDeleteOnly)
                consultNoteTemplateQuery = consultNoteTemplateQuery.Where(s => s.CreatedBy == baseHttpRequestContext.UserId);


            var paginatedList = await CreatePaginatedListAsync(consultNoteTemplateQuery, queryModel);

            var queryList = new QueryResultList<ConsultNoteTemplateList>();
            if (paginatedList != null)
            {
                queryList.ItemRecords = paginatedList;
                queryList.PageNumber = queryModel.PageNumber;
                queryList.PageSize = queryModel.PageSize;
                queryList.CurrentCount = paginatedList.Count();
            }
            else
            {
                queryList.ItemRecords = null;
                queryList.CurrentCount = 0;
            }

            queryList.TotalCount = consultNoteTemplateQuery.Count();
            queryList.PageNumber = queryModel.PageNumber;
            queryList.PageSize = queryModel.PageSize;

            return queryList;
        }

        /// <summary>
        /// Search ConsultNoteTemplates
        /// </summary>
        /// <param name="consultNoteTemplateQuery"></param>
        /// <param name="searchTerm"></param>
        /// <returns></returns>
        private IQueryable<ConsultNoteTemplateList> SearchConsultNoteTemplates(IQueryable<ConsultNoteTemplateList> consultNoteTemplateQuery, string searchTerm)
        {
            bool isTwoWordSearch = Regex.IsMatch(searchTerm, @"\s");
            if (!isTwoWordSearch)
            {
                consultNoteTemplateQuery = consultNoteTemplateQuery.Where(s => s.Name.Contains(searchTerm) || s.CreatedByFirstName.Contains(searchTerm) || s.CreatedBySurName.Contains(searchTerm));
            }
            else
            {
                string[] search = searchTerm.Split(" ");
                consultNoteTemplateQuery = consultNoteTemplateQuery.Where(s => s.Name.Contains(searchTerm) || (s.CreatedByFirstName.Contains(search[0]) && s.CreatedBySurName.Contains(search[1])));
            }
            return consultNoteTemplateQuery;
        }

        private async Task<List<ConsultNoteTemplateList>> CreatePaginatedListAsync(IQueryable<ConsultNoteTemplateList> consultNoteTemplate, QueryModel queryModel)
        {
            if (queryModel.PageNumber > 0 && queryModel.PageSize > 0)
            {
                if (consultNoteTemplate.Any())
                {
                    return await consultNoteTemplate
                            .Skip((queryModel.PageNumber - 1) * queryModel.PageSize)
                            .Take(queryModel.PageSize)
                            .ToListAsync();
                }
            }
            return null;
        }

        /// <summary>
        /// Method to ConsultNoteTemplate based on id
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ConsultNoteTemplateView> GetConsultNoteTemplate(int orgId, long id)
        {
            var consultNoteTemplate = (from CN in _readOnlyDbContext.ConsultNoteTemplate.Where(x => x.Id == id && x.OrgId == orgId)
                                       select new ConsultNoteTemplateView
                                       {
                                           Id = CN.Id,
                                           OrgId = CN.OrgId,
                                           Name = CN.Name,
                                           CompanyDetailsId = CN.CompanyDetailsId,
                                           NumberOfRows = CN.NumberOfRows,
                                           NumberOfColumnsJson = CN.NumberOfColumnsJson,
                                           StatusId = CN.StatusId,
                                           CreatedBy = CN.CreatedBy,
                                           CreatedDate = CN.CreatedDate,
                                           ModifiedBy = CN.ModifiedBy,
                                           ModifiedDate = CN.ModifiedDate,
                                           ConsultNoteTemplateControls = (ICollection<ConsultNoteTemplateControlsView>)(
                                                from CC in _readOnlyDbContext.ConsultNoteTemplateControls
                                                    .Where(x => x.ConsultNoteTemplateId == id && x.OrgId == orgId && x.StatusId == (short)Status.Active)
                                                    .OrderBy(x => x.Order)
                                                select new ConsultNoteTemplateControlsView
                                                {
                                                    Id = CC.Id,
                                                    OrgId = CC.OrgId,
                                                    ConsultNoteTemplateId = CC.ConsultNoteTemplateId,
                                                    ControlType = CC.ControlType,
                                                    ControlId = CC.ControlId,
                                                    Key = CC.Key,
                                                    Label = CC.Label,
                                                    IsRequired = CC.IsRequired,
                                                    Width = CC.Width,
                                                    Position = CC.Position,
                                                    RowNumber = CC.RowNumber,
                                                    ColumnNumber = CC.ColumnNumber,
                                                    MinLength = CC.MinLength,
                                                    MaxLength = CC.MaxLength,
                                                    Heading = CC.Heading,
                                                    Content = CC.Content,
                                                    ShowHorizontally = CC.ShowHorizontally,
                                                    AllowMultiSelect = CC.AllowMultiSelect,
                                                    ShowSingleOption = CC.ShowSingleOption,
                                                    MinimumDate = CC.MinimumDate,
                                                    CanEnlarge = CC.CanEnlarge,
                                                    AutoplayOn = CC.AutoplayOn,
                                                    OptionsJson = CC.OptionsJson,
                                                    Disabled = CC.Disabled,
                                                    Order = CC.Order,
                                                    CreatedBy = CC.CreatedBy,
                                                    CreatedDate = CC.CreatedDate,
                                                    ModifiedBy = CC.ModifiedBy,
                                                    ModifiedDate = CC.ModifiedDate,
                                                    Images = (ICollection<ConsultNoteTemplateMediaView>)(
                                                        from CM in _readOnlyDbContext.ConsultNoteTemplateMedia
                                                            .Where(x => x.ConsultNoteTemplateControlsId == CC.Id && x.OrgId == orgId && x.StatusId == (short)Status.Active)
                                                        select new ConsultNoteTemplateMediaView
                                                        {
                                                            Id = CM.Id,
                                                            OrgId = CM.OrgId,
                                                            ConsultNoteTemplateControlsId = CM.ConsultNoteTemplateControlsId,
                                                            FileDetailsId = CM.FileDetailsId,
                                                            MediaName = CM.MediaName,
                                                            CustomMediaName = CM.CustomMediaName,
                                                            MediaTypeId = CM.MediaTypeId,
                                                            StatusId = CM.StatusId,
                                                            CreatedBy = CM.CreatedBy,
                                                            CreatedDate = CM.CreatedDate,
                                                            ModifiedBy = CM.ModifiedBy,
                                                            ModifiedDate = CM.ModifiedDate
                                                        }),
                                                }),

                                           ConsultNoteTemplateUserAssocs = (ICollection<ConsultNoteTemplateUserAssocsView>)(
                                                from AU in _readOnlyDbContext.ConsultNoteTemplateUserAssocs.Where(x => x.ConsultNoteTemplateId == id && x.OrgId == orgId && x.StatusId == (short)Status.Active)
                                                from UD in _readOnlyDbContext.UserDetails.Where(u => u.OrgId == orgId && u.Id == AU.UserDetailsId)
                                                select new ConsultNoteTemplateUserAssocsView
                                                {
                                                    Id = AU.Id,
                                                    OrgId = AU.OrgId,
                                                    ConsultNoteTemplateId = AU.ConsultNoteTemplateId,
                                                    UserDetailsId = AU.UserDetailsId,
                                                    UserDetails = new ConsultNoteTemplateUserDetails
                                                    {
                                                        Id = UD.Id,
                                                        FirstName = UD.FirstName,
                                                        SurName = UD.SurName
                                                    }
                                                })
                                       });
            return await consultNoteTemplate.FirstOrDefaultAsync();
        }

        /// <summary>
        /// Method to ConsultNoteTemplate based on id
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ConsultNoteTemplate> GetConsultNoteTemplateSync(int orgId, long id)
        {
            return await _readOnlyDbContext.ConsultNoteTemplate.Where(s => s.Id == id && s.OrgId == orgId).AsNoTracking().FirstOrDefaultAsync();
        }

        /// <summary>
        /// GetConsultNoteTemplateControls Sync
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="consultNoteTemplateId"></param>
        /// <returns></returns>
        public async Task<List<ConsultNoteTemplateControls>> GetConsultNoteTemplateControlsSync(int orgId, long consultNoteTemplateId)
        {
            return await _readOnlyDbContext.ConsultNoteTemplateControls.Where(s => s.ConsultNoteTemplateId == consultNoteTemplateId && s.OrgId == orgId).AsNoTracking().ToListAsync();
        }


        public async Task<List<ConsultNoteTemplateMedia>> GetConsultNoteTemplateMediaSync(int orgId, long consultNoteTemplateControlId)
        {
            return await _readOnlyDbContext.ConsultNoteTemplateMedia.Where(s => s.ConsultNoteTemplateControlsId == consultNoteTemplateControlId && s.OrgId == orgId).AsNoTracking().ToListAsync();
        }

        /// <summary>
        /// Fetch AppointmentTypesAssistantAssocs
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="AppointmentTypesId"></param>
        /// <returns></returns>
        public async Task<List<ConsultNoteTemplateUserAssocs>> GetConsultNoteTemplateUserAssocsSync(int orgId, long AppointmentTypesId)
        {
            return await _readOnlyDbContext.ConsultNoteTemplateUserAssocs.Where(s => s.ConsultNoteTemplateId == AppointmentTypesId && s.OrgId == orgId).AsNoTracking().ToListAsync();
        }

        /// <summary>
        /// Update ConsultNoteTemplate
        /// </summary>
        /// <param name="ConsultNoteTemplate"></param>
        /// <returns></returns>
        public async Task<int> UpdateConsultNoteTemplate(ConsultNoteTemplate consultNoteTemplate)
        {
            _updatableDBContext.ConsultNoteTemplate.Update(consultNoteTemplate);
            return await _updatableDBContext.SaveChangesAsync();
        }

        /// <summary>
        /// Method to check if the ConsultNoteTemplate exists in the database
        /// </summary>
        /// <param name="search_term"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<bool> CheckConsultNoteTemplateName(string search_term, int orgId)
        {
            var name = await _readOnlyDbContext.ConsultNoteTemplate
                       .Where(p => p.Name.Equals(search_term) && p.OrgId == orgId)
                       .FirstOrDefaultAsync();

            return name != null;
        }

        /// <summary>
        /// GetConsultNoteTemplateForUser
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ConsultNoteTemplateForUser> GetConsultNoteTemplateForUser(int orgId, long id)
        {
            var templates = await (from UD in _readOnlyDbContext.UserDetails
                                   where UD.Id == id && UD.OrgId == orgId
                                   select new ConsultNoteTemplateForUser
                                   {
                                       Id = UD.Id,
                                       FirstName = UD.FirstName,
                                       SurName = UD.SurName,
                                       UserConsultNoteTemplates = (List<ConsultNoteTemplateList>)(ICollection<ConsultNoteTemplateList>)(
                                            from CNU in _readOnlyDbContext.ConsultNoteTemplateUserAssocs
                                            join CN in _readOnlyDbContext.ConsultNoteTemplate on CNU.ConsultNoteTemplateId equals CN.Id
                                            where CN.OrgId == orgId && CN.StatusId == (short)Status.Active && CNU.UserDetailsId == id
                                            select new ConsultNoteTemplateList
                                            {
                                                Id = CN.Id,
                                                OrgId = CN.OrgId,
                                                Name = CN.Name,
                                                CreatedBy = CN.CreatedBy,
                                                CreatedDate = CN.CreatedDate
                                            })
                                   }).FirstOrDefaultAsync();
            return templates;
        }

        /// <summary>
        /// GetCompaniesForUser
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<List<int>> GetCompaniesForUser(int orgId, long id)
        {
            var companyList = await (from UCA in _readOnlyDbContext.UserCompanyAssocs
                                     where UCA.StatusId == (short)Status.Active && UCA.OrgId == orgId && UCA.UserDetailsId == id
                                     select UCA.CompanyId).ToListAsync();
            return companyList;
        }

        /// <summary>
        /// GetCompanyConsultNoteTemplateList
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="companyList"></param>
        /// <returns></returns>
        public async Task<List<CompanyConsultNoteTemplate>> GetCompanyConsultNoteTemplateList(int orgId, List<int> companyList)
        {
            var companyConsultNoteTemplate = await (from CD in _readOnlyDbContext.CompanyDetails
                                                    where CD.StatusId == (short)CompanyStatus.Active && CD.OrgId == orgId && companyList.Contains(CD.Id) && CD.CompanyTypeId==(short)CompanyType.InternalCompany
                                                    select new CompanyConsultNoteTemplate
                                                    {
                                                        Id = CD.Id,
                                                        Name = CD.Name,
                                                        CompanyConsultNoteTemplateList = (List<ConsultNoteTemplateList>)(ICollection<ConsultNoteTemplateList>)
                                                          (from LT in _readOnlyDbContext.ConsultNoteTemplate
                                                           where CD.Id == LT.CompanyDetailsId && LT.StatusId == (short)Status.Active
                                                           select new ConsultNoteTemplateList
                                                           {
                                                               Id = LT.Id,
                                                               Name = LT.Name
                                                           })
                                                    }).ToListAsync();

            return companyConsultNoteTemplate;
        }

        #region Private methods
        private IQueryable<ConsultNoteTemplateList> SortConsultNoteTemplate(IQueryable<ConsultNoteTemplateList> consultNoteTemplates, string sortTerm, string sortOrder)
        {
            switch (sortTerm.ToLower())
            {
                case "id":
                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            consultNoteTemplates = consultNoteTemplates.OrderBy(x => x.StatusId).ThenBy(y => y.Id);
                        }
                        else
                        {
                            consultNoteTemplates = consultNoteTemplates.OrderBy(x => x.StatusId).ThenByDescending(y => y.Id);
                        }
                        break;
                    }
                case "name":
                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            consultNoteTemplates = consultNoteTemplates.OrderBy(x => x.StatusId).ThenBy(y => y.Name);
                        }
                        else
                        {
                            consultNoteTemplates = consultNoteTemplates.OrderBy(x => x.StatusId).ThenByDescending(y => y.Name);
                        }
                        break;
                    }
            }

            return consultNoteTemplates;
        }
        #endregion Private methods
    }
}