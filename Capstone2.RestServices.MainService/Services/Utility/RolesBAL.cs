﻿using AutoMapper;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.Master.Common;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.Utility.Interfaces;
using Capstone2.RestServices.Utility.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Capstone2.Framework.Business.Common;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Transactions;
using Capstone2.Shared.Models.Dtos;
using BaseHttpRequestContext = Capstone2.Framework.RestApi.Dtos.BaseHttpRequestContext;


namespace Capstone2.RestServices.Utility.Services
{
    public class RolesBAL : IRolesBAL
    {
        private readonly IRolesDAL _rolesDAL;
        public readonly AppSettings _appSettings;
        private IDistributedCacheHelper _redisCache;

        public RolesBAL(IRolesDAL rolesDAL, IDistributedCacheHelper cache, IMapper mapper, IOptions<AppSettings> appSettings)
        {
            _rolesDAL = rolesDAL;
            _redisCache = cache;
            _appSettings = appSettings.Value;
        }
        /// <summary>
        /// Method to Fetch all roles
        /// </summary>
        /// <returns></returns>
        public async Task<ApiResponse<List<Role>>> ListRolesBAL(BaseHttpRequestContext baseHttpRequestContext)
        {
            var roles = await _rolesDAL.ListRolesDAL(baseHttpRequestContext.OrgId);
            var apiResponse = new ApiResponse<List<Role>>
            {
                Result = roles,
                Message = "Success",
                StatusCode = StatusCodes.Status200OK

            };
            return apiResponse;

        }

        /// <summary>
        /// Method to generate a list of roles permissions assocs
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        public async Task<ApiResponse<List<RolesPermissionsAssoc>>> ListRolesPermissionsAssocsBAL(BaseHttpRequestContext baseHttpRequestContext, QueryModel queryModel)
        {
            ApiResponse<List<RolesPermissionsAssoc>> apiResponse = new();
            int orgId = baseHttpRequestContext.OrgId;
            RolesFilterModel filterModel = PrepareFilterParameters(queryModel.Filter);
            List<RolesPermissionsAssoc> queryList = null;

            queryList = await _rolesDAL.ListRolesPermissionsAssocsDAL(filterModel, orgId);

            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result = queryList;
            return apiResponse;
        }

        /// <summary>
        /// Method to check for 
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="RolesFilterModel"></param>
        /// <returns></returns>
        /// Reason for using 2 different cache key is in other place of application if it is only require  
        /// List<RolesPermissionsAssoc> then we can get. 
        /// also if we require to check only permission like delete  only or viewonly then we can also check easily 
        public async Task<RolePermissionFilterModel> IsDeleteOnlyPermissionsBAL(BaseHttpRequestContext baseHttpRequestContext, ModulesEnum modulesEnum)
        {
            RolePermissionFilterModel responseRolePermissionFilterModel = null;
            int orgId = baseHttpRequestContext.OrgId;

            RolesFilterModel rolesFilterModel = new RolesFilterModel() { RolesId = new List<int?>() { baseHttpRequestContext.RoleId } };

            // Caching
            string cacheKeyOrgId_RoleID_Module = string.Format(CachedKeys.Tbl_RolesPermissionsAssoc_Org_RoleID_Module,
                baseHttpRequestContext.OrgId, baseHttpRequestContext.RoleId, (int)modulesEnum);

            responseRolePermissionFilterModel = await _redisCache.GetFromCache<RolePermissionFilterModel>(cacheKeyOrgId_RoleID_Module);
            if (responseRolePermissionFilterModel == null)
            {
                responseRolePermissionFilterModel = new RolePermissionFilterModel();

                #region Get RolesPermissionsAssoc
                // Caching
                string cacheKeyOrgId_RoleID = string.Format(CachedKeys.Tbl_RolesPermissionsAssoc_Org_RoleID,
                    baseHttpRequestContext.OrgId, baseHttpRequestContext.RoleId);

                List<RolesPermissionsAssoc> responseData = await _redisCache.GetFromCache<List<RolesPermissionsAssoc>>(cacheKeyOrgId_RoleID);
                if (responseData == null)
                {
                    responseData = await _rolesDAL.ListRolesPermissionsAssocsDAL(rolesFilterModel, orgId);
                    await _redisCache.SetIntoCache(responseData, cacheKeyOrgId_RoleID);
                }

                #endregion

                if (responseData != null)
                {
                    //get static role permission by module for compare 
                    var rolePermissionsArgs = RolePermissionsArgsList.RolePermissions.FirstOrDefault(x => x.ModuleType == modulesEnum);


                    //filter the DB role permission list 
                    var rolesPermissions = responseData.Where(x => (x.PermissionsId == (int)rolePermissionsArgs.CreatOrEditPermission ||
                                                x.PermissionsId == (int)rolePermissionsArgs.ViewPermission ||
                                                x.PermissionsId == (int)rolePermissionsArgs.DeletePermission) &&
                                                x.IsAllowed == true);

                    #region Set responseRolePermissionFilterModel return Data

                    if (rolesPermissions?.ToList()?.Count == 1)
                    {
                        if (rolesPermissions?.ToList()[0].PermissionsId == (int)rolePermissionsArgs.CreatOrEditPermission)
                            responseRolePermissionFilterModel.isAddOrEditOnly = true;
                        else if (rolesPermissions?.ToList()[0].PermissionsId == (int)rolePermissionsArgs.DeletePermission)
                            responseRolePermissionFilterModel.isDeleteOnly = true;
                        else if (rolesPermissions?.ToList()[0].PermissionsId == (int)rolePermissionsArgs.ViewPermission)
                            responseRolePermissionFilterModel.isViewOnly = true;
                    }

                    #endregion

                    string cacheKeyOrgId_RoleID_AllModule = string.Format(CachedKeys.Tbl_RolesPermissionsAssoc_Org_RoleID_AllModule,
                                                             baseHttpRequestContext.OrgId, baseHttpRequestContext.RoleId);

                    await _redisCache.SetIntoCacheRolePermissionModuleWise(responseRolePermissionFilterModel,
                        cacheKeyOrgId_RoleID_Module, cacheKeyOrgId_RoleID_AllModule);
                }
            }


            return responseRolePermissionFilterModel;
        }

        /// <summary>
        /// method to generate the filter parameters from the request
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        private RolesFilterModel PrepareFilterParameters(string filter)
        {
            if (!(String.IsNullOrEmpty(filter)))
            {
                return JsonConvert.DeserializeObject<RolesFilterModel>(filter);
            }
            else
            {
                return null;
            }
        }

        private async void CleanCache(int rolesId, int orgId)
        {
            string cacheKeyOrgId_RoleID =
             string.Format(CachedKeys.Tbl_RolesPermissionsAssoc_Org_RoleID, orgId, rolesId);

            await _redisCache.RemoveCache(cacheKeyOrgId_RoleID);

            string cacheKeyOrgId_RoleID_AllModule =
             string.Format(CachedKeys.Tbl_RolesPermissionsAssoc_Org_RoleID_AllModule, orgId, rolesId);
            await _redisCache.DeleteAllChildKeysCache(cacheKeyOrgId_RoleID_AllModule);
        }

        public async Task<ApiResponse<string>> EditRolesPermissionsAssocsBAL(RolesPermissionsAssoc[] inputAssocs, BaseHttpRequestContext baseHttpRequestContext, int rolesId)
        {
            ApiResponse<string> apiResponse = new ApiResponse<string>();
            if (rolesId == 1)
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Errors.Add("Roles and Permissions of Super User can not be modified");
                apiResponse.Message = "Failure";
                return apiResponse;
            }
            List<RolesPermissionsAssoc> lstAddRolesPermissionsAssocs = new List<RolesPermissionsAssoc>();
            List<RolesPermissionsAssoc> lstUpdateRolesPermissionsAssocs = new List<RolesPermissionsAssoc>();
            List<RolesPermissionsAssoc> lstInputRolesPermissionsAssocs = new();
            List<RolesPermissionsAssoc> lstDbRolesPermissionsAssocs = new();
            if (inputAssocs is not null && inputAssocs.Length > 0)
            {
                lstInputRolesPermissionsAssocs = inputAssocs.ToList();
                lstDbRolesPermissionsAssocs = await _rolesDAL.ListRolesPermissionsAssocsByRolesId(rolesId, baseHttpRequestContext.OrgId);
                lstInputRolesPermissionsAssocs.ForEach(rolesPermissionsAssoc =>
                {
                    if (rolesPermissionsAssoc.Id > 0)
                    {
                        var existingobj = lstDbRolesPermissionsAssocs.FirstOrDefault(x => x.Id == rolesPermissionsAssoc.Id && x.RolesId == rolesId && x.PermissionsId == rolesPermissionsAssoc.PermissionsId);
                        if (existingobj.IsAllowed == rolesPermissionsAssoc.IsAllowed)
                        {
                            lstDbRolesPermissionsAssocs.Remove(existingobj);
                        }
                        else
                        {
                            existingobj.IsAllowed = rolesPermissionsAssoc.IsAllowed;
                            existingobj.ModifiedBy = baseHttpRequestContext.UserId;
                            existingobj.ModifiedDate = DateTime.UtcNow;
                            lstUpdateRolesPermissionsAssocs.Add(existingobj);
                        }
                    }
                    else
                    {
                        RolesPermissionsAssoc rolesPermissionsAssocDB = lstDbRolesPermissionsAssocs.Where(x => x.RolesId == rolesId && x.PermissionsId == rolesPermissionsAssoc.PermissionsId).FirstOrDefault();
                        if (rolesPermissionsAssocDB is null)
                        {
                            RolesPermissionsAssoc newRolesPermissionsAssoc = new();
                            newRolesPermissionsAssoc.OrgId = baseHttpRequestContext.OrgId;
                            newRolesPermissionsAssoc.RolesId = rolesId;
                            newRolesPermissionsAssoc.PermissionsId = rolesPermissionsAssoc.PermissionsId;
                            newRolesPermissionsAssoc.IsAllowed = rolesPermissionsAssoc.IsAllowed;
                            newRolesPermissionsAssoc.CreatedBy = baseHttpRequestContext.UserId;
                            newRolesPermissionsAssoc.CreatedDate = DateTime.UtcNow;
                            newRolesPermissionsAssoc.StatusId = (short?)Status.Active;
                            lstAddRolesPermissionsAssocs.Add(newRolesPermissionsAssoc);
                        }
                        else
                        {
                            rolesPermissionsAssocDB.OrgId = baseHttpRequestContext.OrgId;
                            rolesPermissionsAssocDB.ModifiedBy = baseHttpRequestContext.UserId;
                            rolesPermissionsAssocDB.ModifiedDate = DateTime.UtcNow;
                            rolesPermissionsAssocDB.PermissionsId = rolesPermissionsAssoc.PermissionsId;
                            rolesPermissionsAssocDB.IsAllowed = rolesPermissionsAssoc.IsAllowed;
                            lstUpdateRolesPermissionsAssocs.Add(rolesPermissionsAssocDB);
                        }
                    }
                });
                using (var transaction = new TransactionScope(TransactionScopeOption.Required, TransactionScopeAsyncFlowOption.Enabled))
                {
                    List<RolesPermissionsAssoc> rolesPermissionsAssocs = lstAddRolesPermissionsAssocs.Concat(lstUpdateRolesPermissionsAssocs).ToList();

                    int id = await _rolesDAL.UpdateRangeRolesPermissionsAssoc(rolesPermissionsAssocs);
                    transaction.Complete();
                    CleanCache(rolesId, baseHttpRequestContext.OrgId);
                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    apiResponse.Result = "Success";
                    apiResponse.Message = "Success";
                    return apiResponse;
                }
            }
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            apiResponse.Errors.Add("Roles permissions associations cannot be updated at this time.");
            apiResponse.Message = "Failure";
            return apiResponse;
        }

    }
}