﻿using AutoMapper;
using Capstone2.Framework.Business.Common;
using Capstone2.Framework.RestApi;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.Utility.Interfaces;
using Capstone2.RestServices.Utility.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Transactions;
using ListMbsData = Capstone2.Shared.Models.Entities.ListMbsData;
using MbsDataView = Capstone2.Shared.Models.Entities.MbsDataView;
using MbsItemCustomFeeInfoAssoc = Capstone2.Shared.Models.Entities.MbsItemCustomFeeInfoAssoc;
using MbsItemView = Capstone2.Shared.Models.Entities.MbsItemView;

namespace Capstone2.RestServices.Utility.Services
{
    public class EpisodeBAL : IEpisodeBAL
    {
        public readonly IEpisodeDAL _episodeDAL;
        public readonly IMbsBAL _mbsBAL;
        public IMapper _mapper;
        public readonly AppSettings _appSettings;

        public EpisodeBAL(IEpisodeDAL episodeDAL, IMbsBAL mbsBAL, IMapper mapper, IOptions<AppSettings> appSettings)
        {
            _episodeDAL = episodeDAL;
            _mbsBAL = mbsBAL;
            _mapper = mapper;
            _appSettings = appSettings.Value;
        }
        /// <summary>
        /// Method to add a new Episode Cateogry
        /// </summary>
        /// <param name="episodeCategory"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<short>> AddEpisodeCategory(EpisodeCategory episodeCategory, BaseHttpRequestContext baseHttpRequestContext)
        {
            int orgId = baseHttpRequestContext.OrgId;
            long loggedInUser = baseHttpRequestContext.UserId;
            ApiResponse<short> apiResponse = new();
            if (episodeCategory is not null)
            {
                if (episodeCategory.ParentEpisodeCategoriesId is not null && episodeCategory.EpisodeTypeId != (short)EpisodeTypes.MBS)
                {
                    EpisodeCategory parentCategory = await _episodeDAL.GetEpisodeCategoryFromId(orgId, (short)episodeCategory.ParentEpisodeCategoriesId);
                    if (parentCategory.Level != episodeCategory.Level - 1)
                    {
                        apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                        apiResponse.Result = default(short);
                        apiResponse.Message = "Failure";
                        apiResponse.Errors.Add("Invalid Level passed for Episode Category.");
                        return apiResponse;
                    }
                    if (parentCategory.StatusId != (short)Status.Active)
                    {
                        apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                        apiResponse.Result = default(short);
                        apiResponse.Message = "Failure";
                        apiResponse.Errors.Add("Episode Category cannot be created with deleted Parent Category.");
                        return apiResponse;
                    }
                }
                if (episodeCategory.ParentEpisodeCategoriesId is not null && episodeCategory.EpisodeTypeId == (short)EpisodeTypes.MBS && episodeCategory.Level == 3)
                {
                    EpisodeCategory parentCategory = await _episodeDAL.GetEpisodeCategoryFromId(orgId, (short)episodeCategory.ParentEpisodeCategoriesId);
                    if (parentCategory.Level != episodeCategory.Level - 1)
                    {
                        apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                        apiResponse.Result = default(short);
                        apiResponse.Message = "Failure";
                        apiResponse.Errors.Add("Invalid Level passed for Episode Category.");
                        return apiResponse;
                    }
                    if (parentCategory.StatusId != (short)Status.Active)
                    {
                        apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                        apiResponse.Result = default(short);
                        apiResponse.Message = "Failure";
                        apiResponse.Errors.Add("Episode Category cannot be created with deleted Parent Category.");
                        return apiResponse;
                    }
                }
                bool isValidName = await ValidateCategoryName(episodeCategory, orgId);
                if (!isValidName)
                {
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Result = default(short);
                    apiResponse.Message = "Failure";
                    apiResponse.Errors.Add("Episode Category name must be unique.");
                    return apiResponse;
                }
                episodeCategory.CreatedBy = loggedInUser;
                episodeCategory.OrgId = orgId;
                episodeCategory.CreatedDate = DateTime.UtcNow;
                episodeCategory.StatusId = (short?)Status.Active;
                short id = await _episodeDAL.AddEpisodeCategory(episodeCategory);
                if (id > default(short))
                {
                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    apiResponse.Result = id;
                    apiResponse.Message = "Success";
                    return apiResponse;
                }
            }
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            apiResponse.Result = default(short);
            apiResponse.Message = "Failure";
            apiResponse.Errors.Add("New Episode Category cannot be added at this time.");
            return apiResponse;
        }

        private async Task<bool> ValidateCategoryName(EpisodeCategory episodeCategory, int orgId)
        {
            bool isValid = true;
            EpisodeCategory episodeCategoryDB = await _episodeDAL.FetchEpisodeCategoryFromName(episodeCategory.Name, episodeCategory.ParentEpisodeCategoriesId, episodeCategory.EpisodeTypeId, orgId);
            if (episodeCategoryDB is not null && episodeCategory.Id != episodeCategoryDB.Id)
            {
                isValid = false;
            }
            return isValid;
        }

        /// <summary>
        /// Method to add new custom episode item
        /// </summary>
        /// <param name="episodeItemDetail"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<int>> AddEpisodeItem(EpisodeItemDetail episodeItemDetail, BaseHttpRequestContext baseHttpRequestContext)
        {
            int orgId = baseHttpRequestContext.OrgId;
            long loggedInUser = baseHttpRequestContext.UserId;
            ApiResponse<int> apiResponse = new();
            if (episodeItemDetail is not null)
            {
                episodeItemDetail.ItemNumber = await SetItemNumber(episodeItemDetail.EpisodeTypeId);
                decimal? gst = await _episodeDAL.FetchGstForOrganisation(baseHttpRequestContext.OrgCode, orgId);
                if (episodeItemDetail.GSTInc == true && episodeItemDetail.PrivateFee != null)
                    episodeItemDetail.GstFee = (decimal)episodeItemDetail.GstFee - (decimal)episodeItemDetail.PrivateFee;
                else
                    episodeItemDetail.GstFee = null;
                episodeItemDetail.ModifiedBy = loggedInUser;
                episodeItemDetail.OrgId = orgId;
                episodeItemDetail.CreatedDate = DateTime.UtcNow;
                if (episodeItemDetail.EpisodeItemCustomFeeAssocs is not null && episodeItemDetail.EpisodeItemCustomFeeAssocs.Any())
                {
                    episodeItemDetail.EpisodeItemCustomFeeAssocs.ToList().ForEach(x =>
                    {
                        if (episodeItemDetail.GSTInc == true && x.Fee > default(decimal))
                            x.GstFee -= x.Fee;
                        else
                            x.GstFee = null;
                        x.CreatedDate = DateTime.UtcNow;
                        x.OrgId = orgId;
                        x.ModifiedBy = loggedInUser;
                        x.StatusId = (short)Status.Active;
                    });
                }
                int id = await _episodeDAL.AddEpisodeItem(episodeItemDetail);
                if (id > default(int))
                {
                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    apiResponse.Result = id;
                    apiResponse.Message = "Success";
                    return apiResponse;
                }

            }
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            apiResponse.Result = default(int);
            apiResponse.Message = "Failure";
            apiResponse.Errors.Add("New Episode Item cannot be added at this time.");
            return apiResponse;
        }

        private async Task<int> SetItemNumber(short episodeTypeId)
        {
            switch (episodeTypeId)
            {
                case (short)EpisodeTypes.Custom_Surgical:
                    {
                        return await _episodeDAL.GetNextVal("[Utility].[SeqCustomSurgicalItemId]");

                    }
                case (short)EpisodeTypes.Custom_Product:
                    {
                        return await _episodeDAL.GetNextVal("[Utility].[SeqCustomProductItemId]");

                    }
                case (short)EpisodeTypes.Custom_Procedure:
                    {
                        return await _episodeDAL.GetNextVal("[Utility].[SeqCustomProcedureItemId]");

                    }
                default:
                    return default(int);
            }

        }

        /// <summary>
        /// Method to delete episode category
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<short>> DeleteEpisodeCategory(BaseHttpRequestContext baseHttpRequestContext, short id)
        {
            int orgId = baseHttpRequestContext.OrgId;
            long loggedInUser = baseHttpRequestContext.UserId;
            ApiResponse<short> apiResponse = new();
            EpisodeCategory episodeCategoryDB = await _episodeDAL.GetEpisodeCategoryFromId(orgId, id);
            if (episodeCategoryDB is not null)
            {

                List<EpisodeCategory> lstCategories = new();
                List<EpisodeCategory> lstChildCategories = await _episodeDAL.GetChildEpisodeCategories(orgId, id);
                lstCategories.Add(episodeCategoryDB);
                lstCategories.AddRange(lstChildCategories);
                lstCategories.ForEach(x =>
                {
                    x.StatusId = (short?)Status.Deleted;
                    x.ModifiedDate = DateTime.UtcNow;
                    x.ModifiedBy = loggedInUser;
                });
                List<EpisodeItemDetail> lstItemUpd = new();
                List<short> lstLevel1Ids = lstCategories.Where(x => x.Level == 1).Select(x => x.Id).ToList();
                List<short> lstLevel2Ids = lstCategories.Where(x => x.Level == 2).Select(x => x.Id).ToList();
                List<short> lstLevel3Ids = lstCategories.Where(x => x.Level == 3).Select(x => x.Id).ToList();

                List<EpisodeItemDetail> lstItemDetailsLevel1 = await _episodeDAL.FetchEpisodeItemsFromLvl1Ids(orgId, lstLevel1Ids);

                List<EpisodeItemDetail> lstItemDetailsLevel2 = await _episodeDAL.FetchEpisodeItemsFromLvl2Ids(orgId, lstLevel2Ids);

                List<EpisodeItemDetail> lstItemDetailsLevel3 = await _episodeDAL.FetchEpisodeItemsFromLvl3Ids(orgId, lstLevel3Ids);

                short parentLevel = episodeCategoryDB.Level;

                List<EpisodeItemDetail> lstItemDetailsUpdLevel1Level2 = lstItemDetailsLevel1.Where(y => (lstItemDetailsLevel2.Select(x => x.Id).ToList()).Contains(y.Id)).ToList();
                //lstItemDetailsLevel1 = lstItemDetailsLevel1.Except(lstItemDetailsUpdLevel1Level2).ToList();
                List<EpisodeItemDetail> lstItemDetailsUpdLevel1 = lstItemDetailsLevel1.Except(lstItemDetailsLevel1.Where(x => lstItemDetailsUpdLevel1Level2.Exists(y => y.Id == x.Id))).ToList();
                List<EpisodeItemDetail> lstItemDetailsUpdLevel2 = lstItemDetailsLevel2;
                lstItemDetailsUpdLevel2 = lstItemDetailsUpdLevel2.Except(lstItemDetailsLevel2.Where(x => lstItemDetailsUpdLevel1Level2.Exists(y => y.Id == x.Id))).ToList();




                List<EpisodeItemDetail> lstItemDetailsUpdLevel3 = lstItemDetailsLevel3;

                List<EpisodeItemDetail> lstItemDetailsUpdLevel2Level3 = lstItemDetailsUpdLevel2.Where(y => (lstItemDetailsUpdLevel3.Select(x => x.Id).ToList()).Contains(y.Id)).ToList();
                lstItemDetailsUpdLevel2 = lstItemDetailsUpdLevel2.Except(lstItemDetailsLevel2.Where(x => lstItemDetailsUpdLevel2Level3.Exists(y => y.Id == x.Id))).ToList();
                lstItemDetailsUpdLevel3 = lstItemDetailsUpdLevel3.Except(lstItemDetailsUpdLevel3.Where(x => lstItemDetailsUpdLevel2Level3.Exists(y => y.Id == x.Id))).ToList();


                List<EpisodeItemDetail> lstItemDetailsUpdLevel1Level2Level3 = lstItemDetailsUpdLevel1Level2.Where(y => (lstItemDetailsLevel3.Select(x => x.Id).ToList()).Contains(y.Id)).ToList();
                lstItemDetailsUpdLevel1Level2 = lstItemDetailsUpdLevel1Level2.Except(lstItemDetailsUpdLevel1Level2.Where(x => lstItemDetailsUpdLevel1Level2Level3.Exists(y => y.Id == x.Id))).ToList();
                lstItemDetailsUpdLevel3 = lstItemDetailsUpdLevel3.Except(lstItemDetailsUpdLevel3.Where(x => lstItemDetailsUpdLevel1Level2Level3.Exists(y => y.Id == x.Id))).ToList();

                lstItemDetailsUpdLevel2 = lstItemDetailsUpdLevel2.Except(lstItemDetailsUpdLevel2.Where(x => lstItemDetailsUpdLevel1Level2Level3.Exists(y => y.Id == x.Id))).ToList();

                lstItemDetailsUpdLevel3.ForEach(x =>
                {
                    x.EpisodeCateogoriesIdLvl3 = null;
                    x.ModifiedBy = loggedInUser;
                    x.ModifiedDate = DateTime.UtcNow;
                });
                lstItemUpd.AddRange(lstItemDetailsUpdLevel3);
                lstItemDetailsUpdLevel2.ForEach(x =>
                {
                    x.EpisodeCateogoriesIdLvl2 = null;
                    x.ModifiedBy = loggedInUser;
                    x.ModifiedDate = DateTime.UtcNow;
                });
                lstItemUpd.AddRange(lstItemDetailsUpdLevel2);

                lstItemDetailsUpdLevel1.ForEach(x =>
                {
                    x.EpisodeCateogoriesIdLvl1 = null;
                    x.ModifiedBy = loggedInUser;
                    x.ModifiedDate = DateTime.UtcNow;
                });
                lstItemUpd.AddRange(lstItemDetailsUpdLevel1);

                lstItemDetailsUpdLevel1Level2Level3.ForEach(x =>
                {
                    x.EpisodeCateogoriesIdLvl1 = null;
                    x.EpisodeCateogoriesIdLvl2 = null;
                    x.EpisodeCateogoriesIdLvl3 = null;
                    x.ModifiedBy = loggedInUser;
                    x.ModifiedDate = DateTime.UtcNow;
                });
                lstItemUpd.AddRange(lstItemDetailsUpdLevel1Level2Level3);
                lstItemDetailsUpdLevel1Level2.ForEach(x =>
                {
                    x.EpisodeCateogoriesIdLvl1 = null;
                    x.EpisodeCateogoriesIdLvl2 = null;
                    x.ModifiedBy = loggedInUser;
                    x.ModifiedDate = DateTime.UtcNow;
                });
                lstItemUpd.AddRange(lstItemDetailsUpdLevel1Level2);

                lstItemDetailsUpdLevel2Level3.ForEach(x =>
                {
                    x.EpisodeCateogoriesIdLvl2 = null;
                    x.EpisodeCateogoriesIdLvl3 = null;
                    x.ModifiedBy = loggedInUser;
                    x.ModifiedDate = DateTime.UtcNow;
                });
                lstItemUpd.AddRange(lstItemDetailsUpdLevel2Level3);

                using (var transaction = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
                {
                    int rows = await _episodeDAL.UpdateEpisodeCategoryRange(lstCategories);
                    if (rows > 0)
                    {
                        await _episodeDAL.UpdateEpisodeItemDetailsRange(lstItemUpd);
                        apiResponse.StatusCode = StatusCodes.Status200OK;
                        apiResponse.Result = id;
                        apiResponse.Message = "Success";
                        transaction.Complete();
                    }
                    else
                    {
                        apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                        apiResponse.Result = default(short);
                        apiResponse.Message = "Failure";
                        apiResponse.Errors.Add("Episode Category cannot be deleted at this time.");
                    }
                    transaction.Dispose();
                }

            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = default(short);
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("Episode Category not found.");
            }

            return apiResponse;
        }
        /// <summary>
        /// Method to update Episode Cateogry name
        /// </summary>
        /// <param name="id"></param>
        /// <param name="episodeCategory"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<short>> EditEpisodeCategory(short id, EpisodeCategory episodeCategory, BaseHttpRequestContext baseHttpRequestContext)
        {
            int orgId = baseHttpRequestContext.OrgId;
            long loggedInUser = baseHttpRequestContext.UserId;
            ApiResponse<short> apiResponse = new();
            EpisodeCategory episodeCategoryDB = await _episodeDAL.GetEpisodeCategoryFromId(orgId, id);
            if (episodeCategoryDB is not null)
            {

                bool isValid = await ValidateCategoryName(episodeCategory, orgId);
                if (!isValid)
                {
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Result = id;
                    apiResponse.Message = "Failure";
                    apiResponse.Errors.Add("Episode Category name should be unique.");
                    return apiResponse;
                }
                episodeCategory.StatusId = episodeCategoryDB.StatusId;
                episodeCategory.ModifiedDate = DateTime.UtcNow;
                episodeCategory.ModifiedBy = loggedInUser;
                episodeCategory.OrgId = orgId;
                episodeCategory.CreatedBy = episodeCategoryDB.CreatedBy;
                episodeCategory.CreatedDate = episodeCategoryDB.CreatedDate;
                episodeCategory.ParentEpisodeCategoriesId = episodeCategoryDB.ParentEpisodeCategoriesId;
                episodeCategory.Level = episodeCategoryDB.Level;
                episodeCategory.Id = id;

                int rows = await _episodeDAL.UpdateEpisodeCategory(episodeCategory);
                if (rows > 0)
                {
                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    apiResponse.Result = id;
                    apiResponse.Message = "Success";

                }
                else
                {
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Result = id;
                    apiResponse.Message = "Failure";
                    apiResponse.Errors.Add("Episode Category cannot be Edited.Please try again later.");
                }
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = id;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("Episode Category not found.");
            }
            return apiResponse;
        }
        /// <summary>
        /// Method to list episode categories
        /// </summary>
        /// <param name="queryModel"></param>
        /// <param name="episodeTypeId"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<QueryResultList<EpisodeCategory>>> ListEpisodeCategories(QueryModel queryModel, short episodeTypeId, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<QueryResultList<EpisodeCategory>> apiResponse = new();
            QueryResultList<EpisodeCategory> episodeQueryList = new();
            int orgId = baseHttpRequestContext.OrgId;
            long loggedInUser = baseHttpRequestContext.UserId;
            EpisodeCategoryFilterModel filterModel = PrepareFilterParameters(queryModel.Filter);
            QueryModel customQueryModel = queryModel;
            if (filterModel.EpisodeTypeId.Contains((short)EpisodeTypes.MBS) && filterModel.Level.Contains(1))
            {
                List<MbsClinicalCategory> lstMbsClinicalCategories = await _mbsBAL.ListofActiveMbsCategory(baseHttpRequestContext.OrgId);
                List<short> lvl1Ids = null;
                int ps = queryModel.PageSize;
                int pn = queryModel.PageNumber;
                string st = queryModel.SearchTerm;
                string soo = queryModel.SortOrder;
                string sot = queryModel.SortTerm;
                var token = baseHttpRequestContext.BearerToken;
                string interServiceToken = baseHttpRequestContext.InterServiceToken;
                if (lstMbsClinicalCategories is not null && lstMbsClinicalCategories.Any())
                {
                    lvl1Ids = lstMbsClinicalCategories.Select(x => x.Id).ToList();


                    //EpisodeCategoryFilterModel filterModel = PrepareFilterParameters(queryModel.Filter);
                    string filter = "{MbsLvl1Ids:[" + string.Join(",", lvl1Ids) + "]}";
                    string encodedFilter = Base64EncoderHelper.EncodeQueryModelFilterParam(filter);
                    string medicalScheduleApiUrl = _appSettings.ApiUrls["MedicalScheduleServiceUrl"] + "/MedicalSchedule/clinicalCategoryData?pn=" + pn + "&ps=" + ps + "&st=" + st + "&soo=" + soo + "&sot=" + sot + "&f=" + encodedFilter;
                    RestClient restClient = new RestClient(medicalScheduleApiUrl, null, token, interServiceToken);
                    var medicalScheduleApiResponse = await restClient.GetAsync<ApiResponse<QueryResultList<ClinicalCategory>>>(medicalScheduleApiUrl, queryModel);
                    List<ClinicalCategory> clinicalCategoriesList = new();
                    List<ClinicalCategory> filteredList = new();
                    if (medicalScheduleApiResponse.StatusCode == StatusCodes.Status200OK && medicalScheduleApiResponse.Result is not null && medicalScheduleApiResponse.Result.CurrentCount > 0)
                    {
                        QueryResultList<ClinicalCategory> paginatedList = medicalScheduleApiResponse.Result;
                        clinicalCategoriesList = paginatedList.ItemRecords.ToList();
                    }
                    int mbsPageSize = (clinicalCategoriesList is not null) ? clinicalCategoriesList.Count : 0;
                    int newPageSize = queryModel.PageSize - mbsPageSize;
                    customQueryModel.PageSize = newPageSize;
                    var filteredListSorted = clinicalCategoriesList.OrderBy(x => x.Name).ThenBy(x => x.Id);
                    filteredList = filteredListSorted.ToList();
                    List<EpisodeCategory> lstLvl1 = _mapper.Map<List<ClinicalCategory>, List<EpisodeCategory>>(filteredList);
                    lstLvl1.ForEach(l => l.EpisodeTypeId = (short)EpisodeTypes.MBS);
                    if (lstLvl1 != null && lstLvl1.Count > 0)
                    {
                        episodeQueryList.ItemRecords = lstLvl1;
                        episodeQueryList.PageNumber = queryModel.PageNumber;
                        episodeQueryList.PageSize = queryModel.PageSize;
                        episodeQueryList.CurrentCount = lstLvl1.Count();
                    }
                    else
                    {
                        episodeQueryList.ItemRecords = null;
                        episodeQueryList.CurrentCount = 0;
                    }
                    episodeQueryList.TotalCount = lstLvl1.Count();
                    episodeQueryList.PageNumber = queryModel.PageNumber;
                    episodeQueryList.PageSize = queryModel.PageSize;
                }
            }
            QueryResultList<EpisodeCategory> recordsList = await _episodeDAL.ListEpisodeCategories(orgId, customQueryModel, filterModel);
            int recordCount = 0;
            if (recordsList.ItemRecords != null && episodeQueryList.ItemRecords != null)
            {
                foreach (var item in recordsList.ItemRecords)
                {
                    episodeQueryList.ItemRecords.Add(item);
                    recordCount += 1;
                }
                episodeQueryList.CurrentCount += recordCount;
                episodeQueryList.TotalCount += recordCount;
                episodeQueryList.PageNumber = queryModel.PageNumber;
                episodeQueryList.PageSize = queryModel.PageSize;
            }
            else if (episodeQueryList.ItemRecords == null)
            {
                episodeQueryList = recordsList;
            }
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result = episodeQueryList;
            return apiResponse;
        }

        private EpisodeCategoryFilterModel PrepareFilterParameters(string filter)
        {
            if (!(String.IsNullOrEmpty(filter)))
            {
                return JsonConvert.DeserializeObject<EpisodeCategoryFilterModel>(filter);
            }
            else
            {
                return null;
            }
        }

        public async Task<ApiResponse<QueryResultList<ListEpisodeItemDetail>>> ListEpisodeItemDetails(QueryModel queryModel, short episodeTypeId, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<QueryResultList<ListEpisodeItemDetail>> apiResponse = new();
            int orgId = baseHttpRequestContext.OrgId;
            long loggedInUser = baseHttpRequestContext.UserId;
            EpisodeItemFilterModel filterModel = PrepareFilterParametersForEpisodeItem(queryModel.Filter);
            QueryResultList<ListEpisodeItemDetail> episodeQueryList = await _episodeDAL.ListEpisodeItemDetails(orgId, queryModel, filterModel);
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result = episodeQueryList;
            return apiResponse;
        }

        private EpisodeItemFilterModel PrepareFilterParametersForEpisodeItem(string filter)
        {
            if (!(String.IsNullOrEmpty(filter)))
            {
                return JsonConvert.DeserializeObject<EpisodeItemFilterModel>(filter);
            }
            else
            {
                return null;
            }
        }
        /// <summary>
        /// Method to fetch Episode Item based on Id
        /// </summary>
        /// <param name="id"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<EpisodeItemDetailInfo>> GetEpisodeItemDetails(int id, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<EpisodeItemDetailInfo> apiResponse = new();
            EpisodeItemDetailInfo episodeItemDetail = await _episodeDAL.GetEpisodeItemDetail(id, baseHttpRequestContext.OrgId);
            if (episodeItemDetail is null)
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = null;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("Episode Item not found.");
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";
                apiResponse.Result = episodeItemDetail;
            }

            return apiResponse;

        }
        /// <summary>
        /// Method to edit Episode Item
        /// </summary>
        /// <param name="id"></param>
        /// <param name="episodeItemDetail"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<string>> EditEpisodeItemDetails(int id, EpisodeItemDetail episodeItemDetail, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<string> apiResponse = new();

            int orgId = baseHttpRequestContext.OrgId;
            long loggedInUser = baseHttpRequestContext.UserId;
            EpisodeItemDetail episodeItemDetailDB = await _episodeDAL.FetchEpisodeItemFromId(id, orgId);
            if (episodeItemDetailDB is not null)
            {
                List<EpisodeItemCustomFeeAssoc> removeCustomFeeList = (episodeItemDetailDB.EpisodeItemCustomFeeAssocs is null) ? null : episodeItemDetailDB.EpisodeItemCustomFeeAssocs.ToList();
                List<EpisodeItemCustomFeeAssoc> inputCustomFeeList = (episodeItemDetail.EpisodeItemCustomFeeAssocs is null) ? null : episodeItemDetail.EpisodeItemCustomFeeAssocs.ToList();
                inputCustomFeeList.ForEach(x => { x.EpisodeItemDetailsId = id; x.OrgId = orgId; });
                episodeItemDetail.EpisodeItemCustomFeeAssocs = null;
                decimal? gst = await _episodeDAL.FetchGstForOrganisation(baseHttpRequestContext.OrgCode, orgId);
                if (episodeItemDetail.GSTInc == true && episodeItemDetail.PrivateFee != null)
                    episodeItemDetail.GstFee = (decimal)episodeItemDetail.GstFee - (decimal)episodeItemDetail.PrivateFee;
                else
                    episodeItemDetail.GstFee = null;
                using (var transaction = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
                {
                    episodeItemDetail.EpisodeItemCustomFeeAssocs = EditEpisodeItemCustomFeeAssocs(removeCustomFeeList, inputCustomFeeList, orgId, loggedInUser, gst, episodeItemDetail.GSTInc);
                    episodeItemDetail.ModifiedDate = DateTime.UtcNow;
                    episodeItemDetail.ModifiedBy = loggedInUser;
                    episodeItemDetail.CreatedDate = episodeItemDetailDB.CreatedDate;
                    episodeItemDetail.ItemNumber = episodeItemDetailDB.ItemNumber;
                    episodeItemDetail.OrgId = orgId;
                    await _episodeDAL.UpdateEpisodeItemDetail(episodeItemDetail);
                    apiResponse.Result = "Success";
                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    apiResponse.Message = "Success";

                    transaction.Complete();
                }
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = null;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("Episode Item not found.");
            }

            return apiResponse;
        }

        private ICollection<EpisodeItemCustomFeeAssoc> EditEpisodeItemCustomFeeAssocs(List<EpisodeItemCustomFeeAssoc> removeCustomFeeList, List<EpisodeItemCustomFeeAssoc> inputCustomFeeList, int orgId, long loggedInUser, decimal? gst, bool gSTInc)
        {
            List<EpisodeItemCustomFeeAssoc> lstAddUpdCustomFee = new();
            foreach (var customFee in inputCustomFeeList)
            {
                if (customFee.Id > 0)
                {
                    var existingobj = removeCustomFeeList.FirstOrDefault(x => x.Id == customFee.Id);
                    if (customFee.Name != existingobj.Name || customFee.Fee != existingobj.Fee || customFee.GstFee != existingobj.GstFee)
                    {
                        if (gSTInc == true && customFee.Fee > default(decimal)) customFee.GstFee -= customFee.Fee;
                        //customFee.GstFee = CommonFinanceCalculator.CalculateGst(customFee.Fee, (decimal)gst);
                        else
                            customFee.GstFee = null;
                        customFee.ModifiedBy = loggedInUser;
                        customFee.ModifiedDate = DateTime.UtcNow;
                        customFee.OrgId = orgId;
                        lstAddUpdCustomFee.Add(customFee);
                    }
                    removeCustomFeeList.Remove(existingobj);
                }
                else
                {

                    if (gSTInc == true && customFee.Fee > default(decimal)) customFee.GstFee -= customFee.Fee;
                    //customFee.GstFee = CommonFinanceCalculator.CalculateGst(customFee.Fee, (decimal)gst);
                    else
                        customFee.GstFee = null;

                    customFee.CreatedDate = DateTime.UtcNow;
                    customFee.OrgId = orgId;
                    customFee.ModifiedBy = loggedInUser;
                    customFee.StatusId = (short)Status.Active;
                    lstAddUpdCustomFee.Add(customFee);
                }
            }
            foreach (var customFee in removeCustomFeeList)
            {
                customFee.ModifiedBy = loggedInUser;
                customFee.ModifiedDate = DateTime.UtcNow;
                customFee.OrgId = orgId;
                customFee.StatusId = (short)Status.Deleted;
            }
            lstAddUpdCustomFee = lstAddUpdCustomFee.Concat(removeCustomFeeList).ToList();
            return lstAddUpdCustomFee;
        }

        public async Task<ApiResponse<QueryResultList<ListFetchEpisodeItemDetail>>> ListFetchItemDetails(QueryModel queryModel, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<QueryResultList<ListFetchEpisodeItemDetail>> apiResponse = new();
            int orgId = baseHttpRequestContext.OrgId;
            long loggedInUser = baseHttpRequestContext.UserId;
            //EpisodeItemFilterModel filterModel = PrepareFilterParametersForEpisodeItem(queryModel.Filter);
            QueryResultList<ListFetchEpisodeItemDetail> itemList = await _episodeDAL.ListFetchEpisodeItemDetails(orgId, queryModel, null);
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result = itemList;
            return apiResponse;
        }
        /// <summary>
        /// Method to list items from custom and mbs
        /// </summary>
        /// <param name="queryModel"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<QueryResultList<ListEpisodeItemDetail>>> ListItemDetails(QueryModel queryModel, BaseHttpRequestContext baseHttpRequestContext)
        {
            QueryResultList<ListEpisodeItemDetail> queryItemList = null;
            EpisodeItemFilterModel mainFilter = null;
            if (queryModel.Filter != null)
            {
                mainFilter = PrepareFilterParametersForEpisodeItem(queryModel.Filter);
            }
            queryItemList = await _episodeDAL.ListMbsCustomItemDetails(queryModel, mainFilter, baseHttpRequestContext.OrgId);


            QueryModel queryModelMbs = new()
            {
                PageSize = (queryItemList is not null && queryItemList.CurrentCount > 0) ? queryModel.PageSize - queryItemList.CurrentCount : queryModel.PageSize,
                PageNumber = queryModel.PageNumber,
                SortOrder = queryModel.SortOrder,
                SortTerm = queryModel.SortTerm,
                SearchTerm = queryModel.SearchTerm
            };
            ApiResponse<QueryResultList<ListMbsData>> apiResponseMbs = null;
            if (mainFilter is null || (mainFilter is not null && mainFilter.EpisodeTypeId != null && mainFilter.EpisodeTypeId.Contains((short)EpisodeTypes.MBS)))
            {
                string filter = string.Empty;
                string encodedFilter = string.Empty;
                List<MbsClinicalCategory> lstMbsClinicalCategories = await _mbsBAL.ListofActiveMbsCategory(baseHttpRequestContext.OrgId);
                if (lstMbsClinicalCategories is not null && lstMbsClinicalCategories.Any())
                {
                    List<short> lvl1Ids = lstMbsClinicalCategories.Select(x => x.Id).ToList();
                    filter = string.Format("EpisodeTypeId:[{0}],EpisodeCateogoriesIdLvl1:[{1}],SearchAll:false", (short)EpisodeTypes.MBS, string.Join(",", lvl1Ids));
                    filter = "{" + filter + "}";
                }
                else
                {
                    filter = "{SearchAll:true}";
                }
                encodedFilter = Base64EncoderHelper.EncodeQueryModelFilterParam(filter);
                queryModelMbs.Filter = encodedFilter;
                apiResponseMbs = await _mbsBAL.ListMbsDataBAL(queryModelMbs, baseHttpRequestContext);
            }


            if (apiResponseMbs is not null && apiResponseMbs.StatusCode == StatusCodes.Status200OK && apiResponseMbs.Result is not null)
            {
                QueryResultList<ListMbsData> mbsQueryList = apiResponseMbs.Result;
                if (mbsQueryList.ItemRecords is not null && mbsQueryList.ItemRecords.Count > 0)
                {
                    List<ListEpisodeItemDetail> mbsItms = _mapper.Map<List<ListMbsData>, List<ListEpisodeItemDetail>>(mbsQueryList.ItemRecords.ToList());
                    if (mbsItms is not null)
                    {
                        mbsItms.ForEach(x => x.EpisodeTypeId = (short)EpisodeTypes.MBS);
                        if (queryItemList.ItemRecords is not null)
                            mbsItms.AddRange(queryItemList.ItemRecords.ToList());
                    }

                    queryItemList.ItemRecords = mbsItms;
                    queryItemList.CurrentCount = mbsItms.Count;
                    queryItemList.TotalCount = queryItemList.TotalCount + mbsQueryList.TotalCount;
                }
            }
            ApiResponse<QueryResultList<ListEpisodeItemDetail>> apiResponse = new();
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result = queryItemList;
            return apiResponse;
        }

        public async Task<ApiResponse<ListFetchEpisodeItemDetail>> FetchRatesForItems(EpisodeMbsCustomItemFilterModel filterModel, BaseHttpRequestContext baseHttpRequestContext)
        {
            ListFetchEpisodeItemDetail episodeItemDetail = new();
            ApiResponse<ListFetchEpisodeItemDetail> apiResponse = new();
            if (filterModel is null || filterModel.EpisodeTypeId <= default(long) || filterModel.ItemNumber <= default(long))
            {

                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = null;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("Episode Item not found.");
            }
            else if (!(Enum.IsDefined(typeof(EpisodeTypes), (int)filterModel.EpisodeTypeId)))
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = null;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("Episode Item cannot be fetched with the parameters.");
            }
            else
            {
                if (filterModel.EpisodeTypeId == (short)EpisodeTypes.MBS)
                {
                    ApiResponse<MbsDataView> apiResponeMbs = await _mbsBAL.GetMbsDataBAL(filterModel.ItemNumber, baseHttpRequestContext);
                    if (apiResponeMbs is not null && apiResponeMbs.StatusCode == StatusCodes.Status200OK && apiResponeMbs.Result is not null)
                    {
                        MbsDataView mbsDataView = apiResponeMbs.Result;
                        episodeItemDetail = ProcessMbsDataView(mbsDataView);
                        episodeItemDetail.EpisodeTypeId = filterModel.EpisodeTypeId;
                        episodeItemDetail.ItemNumber = (int)mbsDataView.ItemNum;
                    }
                }
                else
                {
                    episodeItemDetail = await _episodeDAL.FetchRatesForCustomItems(filterModel, baseHttpRequestContext.OrgId);
                    episodeItemDetail.EpisodeTypeId = filterModel.EpisodeTypeId;

                }
            }

            if (episodeItemDetail is null)
            {
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";
                apiResponse.Result = episodeItemDetail;
            }
            return apiResponse;
        }

        private ListFetchEpisodeItemDetail ProcessMbsDataView(MbsDataView mbsDataView)
        {
            ListFetchEpisodeItemDetail episodeItemDetail = new();
            episodeItemDetail = _mapper.Map<MbsDataView, ListFetchEpisodeItemDetail>(mbsDataView);
            episodeItemDetail.ItemNumber = (int)mbsDataView.ItemNum;
            List<EpisodeItemFeeAssoc> lstEpisodeItemFeeAssoc = new();
            if (mbsDataView is not null)
            {
                lstEpisodeItemFeeAssoc.Add(new EpisodeItemFeeAssoc
                {
                    Fee = (decimal)mbsDataView.ScheduleFee,
                    Name = "Schedule Fee",
                    GstFee = 0
                });
                if (mbsDataView.Benefit75 > 0)
                {
                    lstEpisodeItemFeeAssoc.Add(new EpisodeItemFeeAssoc
                    {
                        Fee = (decimal)mbsDataView.Benefit75,
                        Name = "Benefit75 Fee",
                        GstFee = 0
                    });
                }
                if (mbsDataView.Benefit85 > 0)
                {
                    lstEpisodeItemFeeAssoc.Add(new EpisodeItemFeeAssoc
                    {
                        Fee = (decimal)mbsDataView.Benefit85,
                        Name = "Benefit85 Fee",
                        GstFee = 0
                    });
                }
                if (mbsDataView.Benefit100 > 0)
                {
                    lstEpisodeItemFeeAssoc.Add(new EpisodeItemFeeAssoc
                    {
                        Fee = (decimal)mbsDataView.Benefit100,
                        Name = "Benefit100 Fee",
                        GstFee = 0
                    });
                }
                //if (mbsDataView.Benefit100 > 0)
                //{
                //    lstEpisodeItemFeeAssoc.Add(new EpisodeItemFeeAssoc
                //    {
                //        Fee = (decimal)mbsDataView.Benefit85,
                //        Name = "Benefit85 Fee",
                //        // GstFee = mbsDataView.MbsItemView.GstFee == null ? 0 : mbsDataView.MbsItemView.GstFee,
                //    });
                //}
                if (mbsDataView.MbsItemView is not null)
                {
                    episodeItemDetail.GSTInc = mbsDataView.MbsItemView.Gstinc;
                    episodeItemDetail.PrivateFee = (mbsDataView.MbsItemView.PrivateFee == null) ? 0 : mbsDataView.MbsItemView.PrivateFee;
                    episodeItemDetail.GstFee = mbsDataView.MbsItemView.GstFee == null ? 0 : mbsDataView.MbsItemView.GstFee;
                    lstEpisodeItemFeeAssoc.Add(new EpisodeItemFeeAssoc
                    {
                        Fee = mbsDataView.MbsItemView.PrivateFee == null ? 0 : (decimal)mbsDataView.MbsItemView.PrivateFee,
                        Name = "Private Fee",
                        GstFee = mbsDataView.MbsItemView.GstFee == null ? 0 : mbsDataView.MbsItemView.GstFee,
                    });

                    if (mbsDataView.MbsItemView.MbsItemCustomFeeAssocs is not null && mbsDataView.MbsItemView.MbsItemCustomFeeAssocs.Count > 0)
                    {


                        List<MbsItemCustomFeeInfoAssoc> lstMbsFeeAssoc = mbsDataView.MbsItemView.MbsItemCustomFeeAssocs.ToList();
                        lstEpisodeItemFeeAssoc.AddRange(_mapper.Map<List<MbsItemCustomFeeInfoAssoc>, List<EpisodeItemFeeAssoc>>(lstMbsFeeAssoc));
                    }
                }
                if (mbsDataView.DvaData is not null)
                {

                    decimal result;
                    if (!string.IsNullOrWhiteSpace(mbsDataView.DvaData.LMOFee))
                    {
                        if (decimal.TryParse(mbsDataView.DvaData.LMOFee.Trim(), out result))
                        {
                            lstEpisodeItemFeeAssoc.Add(new EpisodeItemFeeAssoc
                            {
                                Fee = result,//(mbsDataView.DvaData.LMOFee == null || string.IsNullOrEmpty(mbsDataView.DvaData.LMOFee) || mbsDataView.DvaData.LMOFee == "D") ? 0 : Convert.ToDecimal(mbsDataView.DvaData.LMOFee.Trim()),
                                Name = "LMO Fee",
                                GstFee = 0//mbsDataView.MbsItemView.GstFee == null ? 0 : mbsDataView.DvaData.GstFee,
                            });
                        }
                    }

                    if (!string.IsNullOrWhiteSpace(mbsDataView.DvaData.RMFSInHospitalFee))
                    {
                        if (decimal.TryParse(mbsDataView.DvaData.RMFSInHospitalFee.Trim(), out result))
                        {
                            lstEpisodeItemFeeAssoc.Add(new EpisodeItemFeeAssoc
                            {
                                Fee = result,//(mbsDataView.DvaData.LMOFee == null || string.IsNullOrEmpty(mbsDataView.DvaData.LMOFee) || mbsDataView.DvaData.LMOFee == "D") ? 0 : Convert.ToDecimal(mbsDataView.DvaData.LMOFee.Trim()),
                                Name = "RMFSInHospital Fee",
                                GstFee = 0//mbsDataView.MbsItemView.GstFee == null ? 0 : mbsDataView.DvaData.GstFee,
                            });
                        }
                    }
                    if (!string.IsNullOrWhiteSpace(mbsDataView.DvaData.RMFSOutOfHospitalFee))
                    {
                        if (decimal.TryParse(mbsDataView.DvaData.RMFSOutOfHospitalFee.Trim(), out result))
                        {
                            lstEpisodeItemFeeAssoc.Add(new EpisodeItemFeeAssoc
                            {
                                Fee = result,//(mbsDataView.DvaData.LMOFee == null || string.IsNullOrEmpty(mbsDataView.DvaData.LMOFee) || mbsDataView.DvaData.LMOFee == "D") ? 0 : Convert.ToDecimal(mbsDataView.DvaData.LMOFee.Trim()),
                                Name = "RMFSOutOfHospital Fee",
                                GstFee = 0//mbsDataView.MbsItemView.GstFee == null ? 0 : mbsDataView.DvaData.GstFee,
                            });
                        }
                    }
                    //lstEpisodeItemFeeAssoc.Add(new EpisodeItemFeeAssoc
                    //{
                    //    Fee = mbsDataView.DvaData.RMFSInHospitalFee == null || string.IsNullOrEmpty(mbsDataView.DvaData.RMFSInHospitalFee) ? 0 : Convert.ToDecimal(mbsDataView.DvaData.RMFSInHospitalFee),
                    //    Name = "RMFSInHospital Fee",
                    //    GstFee = 0//mbsDataView.MbsItemView.GstFee == null ? 0 : mbsDataView.DvaData.GstFee,
                    //});
                    //lstEpisodeItemFeeAssoc.Add(new EpisodeItemFeeAssoc
                    //{
                    //    Fee = mbsDataView.DvaData.RMFSOutOfHospitalFee == null || string.IsNullOrEmpty(mbsDataView.DvaData.RMFSOutOfHospitalFee) ? 0 : Convert.ToDecimal(mbsDataView.DvaData.RMFSOutOfHospitalFee),
                    //    Name = "RMFSOutOfHospital Fee",
                    //    GstFee = 0//mbsDataView.MbsItemView.GstFee == null ? 0 : mbsDataView.DvaData.GstFee,
                    //});
                }
            }


            episodeItemDetail.EpisodeItemFeeAssocs = lstEpisodeItemFeeAssoc;
            return episodeItemDetail;
        }

        /// <summary>
        /// Method to display list of episode groups
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        public async Task<ApiResponse<List<ListEpisode>>> GetAllEpisodeGroupsBAL(long patient_id, QueryModel queryModel, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<List<ListEpisode>> apiResponse = new();
            int ps = queryModel.PageSize;
            int pn = queryModel.PageNumber;
            string st = queryModel.SearchTerm;
            string soo = queryModel.SortOrder;
            string sot = queryModel.SortTerm;
            var token = baseHttpRequestContext.BearerToken;
            string interServiceToken = baseHttpRequestContext.InterServiceToken;
            //EpisodeCategoryFilterModel filterModel = PrepareFilterParameters(queryModel.Filter);
            string medicalScheduleApiUrl = _appSettings.ApiUrls["MedicalScheduleServiceUrl"] + "/MedicalSchedule/clinicalCategoryData?pn=" + pn + "&ps=" + ps + "&st=" + st + "&soo=" + soo + "&sot=" + sot;
            RestClient restClient = new RestClient(medicalScheduleApiUrl, null, token, interServiceToken);
            var medicalScheduleApiResponse = await restClient.GetAsync<ApiResponse<QueryResultList<ClinicalCategory>>>(medicalScheduleApiUrl, queryModel);
            List<ClinicalCategory> clinicalCategoriesList = new();
            if (medicalScheduleApiResponse.StatusCode == StatusCodes.Status200OK && medicalScheduleApiResponse.Result is not null && medicalScheduleApiResponse.Result.CurrentCount > 0)
            {
                QueryResultList<ClinicalCategory> paginatedList = medicalScheduleApiResponse.Result;
                clinicalCategoriesList = paginatedList.ItemRecords.ToList();
            }

            List<ListEpisode> episodesList = await _episodeDAL.GetAllEpisodeGroupsDAL(patient_id, clinicalCategoriesList, baseHttpRequestContext.OrgId);

            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result = episodesList;
            return apiResponse;
        }

        /// <summary>
        /// Method to add new episode
        /// </summary>
        /// <param name="episodeItemDetail"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<int>> AddEpisode(Episode episode, BaseHttpRequestContext baseHttpRequestContext)
        {
            int orgId = baseHttpRequestContext.OrgId;
            long loggedInUser = baseHttpRequestContext.UserId;
            ApiResponse<int> apiResponse = new();
            if (episode is not null)
            {
                // Check for existing episode combination
                var isEpisodeExist = await _episodeDAL.GetEpisodeByPatientId(orgId, episode.PatientDetailsId);
                if (isEpisodeExist?.Any(x => x.EpisodeTypeId == episode.EpisodeTypeId &&
                x.EpisodeCategoryL1id == episode.EpisodeCategoryL1id &&
                x.EpisodeCategoryL2id == episode.EpisodeCategoryL2id &&
                x.EpisodeCategoryL3id == episode.EpisodeCategoryL3id) == true)
                {
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Result = default(int);
                    apiResponse.Message = "Failure";
                    apiResponse.Errors.Add("This Episode is already existed.");

                    return apiResponse;
                }

                episode.CreatedBy = loggedInUser;
                episode.OrgId = orgId;
                episode.CreatedDate = DateTime.UtcNow;
                episode.StatusId = (short?)Status.Active;
                int id = await _episodeDAL.AddEpisodeDAL(episode);
                if (id > 0)
                {
                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    apiResponse.Result = id;
                    apiResponse.Message = "Success";
                    return apiResponse;
                }

            }
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            apiResponse.Result = default(int);
            apiResponse.Message = "Failure";
            apiResponse.Errors.Add("New Episode cannot be added at this time.");
            return apiResponse;
        }

        public async Task<ApiResponse<List<ListFetchEpisodeItemDetail>>> FetchRatesForMultpileItems(List<EpisodeMbsCustomItemFilterModel> episodeMbsCustomItemFilterModels, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<List<ListFetchEpisodeItemDetail>> apiResponse = new();
            List<ListFetchEpisodeItemDetail> lstEpisodeDetail = new();
            List<long> lstMbsItems = (episodeMbsCustomItemFilterModels is null) ? null : episodeMbsCustomItemFilterModels.Where(x => x.EpisodeTypeId == (short)EpisodeTypes.MBS).Select(x => x.ItemNumber).ToList();
            List<EpisodeMbsCustomItemFilterModel> lstCustomItems = (episodeMbsCustomItemFilterModels is null) ? null : episodeMbsCustomItemFilterModels.Where(x => x.EpisodeTypeId != (short)EpisodeTypes.MBS).ToList();

            if (lstMbsItems is not null && lstMbsItems.Count > 0)
            {
                ApiResponse<List<MbsDataView>> apiResponseMbs = new();
                string filter = "{ListMbsItems:[" + string.Join(",", lstMbsItems) + "]}";
                string encodedFilter = Base64EncoderHelper.EncodeQueryModelFilterParam(filter);//Base64EncoderHelper.EncodeBase64(filter).Replace("+", "-").Replace("/", "_");
                string medicalScheduleApiUrl = _appSettings.ApiUrls["MedicalScheduleServiceUrl"] + "/MedicalSchedule/mbs_data?pn=1" + "&ps=" + lstMbsItems.Count + "&f=" + encodedFilter;
                RestClient restClient = new RestClient(medicalScheduleApiUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
                apiResponseMbs = await restClient.GetAsync<ApiResponse<List<MbsDataView>>>(medicalScheduleApiUrl);
                if (apiResponseMbs.StatusCode == StatusCodes.Status200OK && apiResponseMbs.Result is not null && apiResponseMbs.Result.Count > 0)
                {
                    List<MbsDataView> lstMbsDataView = apiResponseMbs.Result;
                    List<MbsItemView> lstMbsItemVIew = await _mbsBAL.ListMbsItemFromItemNums(lstMbsItems, baseHttpRequestContext);
                    lstMbsDataView.ForEach(mbsData =>
                    {
                        mbsData.MbsItemView = (lstMbsItemVIew is not null && lstMbsItemVIew.Count > 0) ? lstMbsItemVIew.Where(x => x.ItemNum == mbsData.ItemNum).FirstOrDefault() : null;
                        ListFetchEpisodeItemDetail episodeItemDetail = ProcessMbsDataView(mbsData);
                        episodeItemDetail.EpisodeTypeId = (short)EpisodeTypes.MBS;
                        lstEpisodeDetail.Add(episodeItemDetail);
                    });
                }
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Result = lstEpisodeDetail;
                //return apiResponse;
            }

            if (lstCustomItems != null && lstCustomItems.Count > 0)
            {
                List<EpisodeMbsCustomItemFilterModel> lstProduct = lstCustomItems.Where(x => x.EpisodeTypeId == (short)EpisodeTypes.Custom_Product).ToList();
                List<EpisodeMbsCustomItemFilterModel> lstProcedure = lstCustomItems.Where(x => x.EpisodeTypeId == (short)EpisodeTypes.Custom_Procedure).ToList();
                List<EpisodeMbsCustomItemFilterModel> lstSurgical = lstCustomItems.Where(x => x.EpisodeTypeId == (short)EpisodeTypes.Custom_Surgical).ToList();
                if (lstProduct != null && lstProduct.Count > 0)
                {
                    List<EpisodeItemDetail> lst1 = await _episodeDAL.ListEpisodeItemDetailsFromFilter(baseHttpRequestContext.OrgId, (short)EpisodeTypes.Custom_Product, lstProduct.Select(x => x.Id).ToList(), lstProduct.Select(x => x.ItemNumber).ToList());
                    if (lst1 != null && lst1.Count > 0)
                    {
                        List<ListFetchEpisodeItemDetail> lstPdt = _mapper.Map<List<EpisodeItemDetail>, List<ListFetchEpisodeItemDetail>>(lst1);
                        lst1.ForEach(x =>
                        {
                            if (x.EpisodeItemCustomFeeAssocs != null && x.EpisodeItemCustomFeeAssocs.Count > 0)
                            {
                                ListFetchEpisodeItemDetail item = lstPdt.Where(y => y.Id == x.Id).FirstOrDefault();
                                if (item != null)
                                {
                                    List<EpisodeItemFeeAssoc> lstFeeAssoc = _mapper.Map<List<EpisodeItemCustomFeeAssoc>, List<EpisodeItemFeeAssoc>>(x.EpisodeItemCustomFeeAssocs.ToList());
                                    item.EpisodeItemFeeAssocs = lstFeeAssoc;
                                }
                            }

                        });
                        if (lstEpisodeDetail != null && lstPdt != null) lstEpisodeDetail.AddRange(lstPdt);
                    }
                }
                if (lstProcedure != null && lstProcedure.Count > 0)
                {
                    List<EpisodeItemDetail> lst2 = await _episodeDAL.ListEpisodeItemDetailsFromFilter(baseHttpRequestContext.OrgId, (short)EpisodeTypes.Custom_Procedure, lstProcedure.Select(x => x.Id).ToList(), lstProcedure.Select(x => x.ItemNumber).ToList());
                    if (lst2 != null && lst2.Count > 0)
                    {
                        List<ListFetchEpisodeItemDetail> lstProc = _mapper.Map<List<EpisodeItemDetail>, List<ListFetchEpisodeItemDetail>>(lst2);
                        lst2.ForEach(x =>
                        {
                            if (x.EpisodeItemCustomFeeAssocs != null && x.EpisodeItemCustomFeeAssocs.Count > 0)
                            {
                                ListFetchEpisodeItemDetail item = lstProc.Where(y => y.Id == x.Id).FirstOrDefault();
                                if (item != null)
                                {
                                    List<EpisodeItemFeeAssoc> lstFeeAssoc = _mapper.Map<List<EpisodeItemCustomFeeAssoc>, List<EpisodeItemFeeAssoc>>(x.EpisodeItemCustomFeeAssocs.ToList());
                                    item.EpisodeItemFeeAssocs = lstFeeAssoc;
                                }
                            }

                        });
                        if (lstEpisodeDetail != null && lstProc != null) lstEpisodeDetail.AddRange(lstProc);
                    }
                }
                if (lstSurgical != null && lstSurgical.Count > 0)
                {
                    List<EpisodeItemDetail> lst3 = await _episodeDAL.ListEpisodeItemDetailsFromFilter(baseHttpRequestContext.OrgId, (short)EpisodeTypes.Custom_Surgical, lstSurgical.Select(x => x.Id).ToList(), lstSurgical.Select(x => x.ItemNumber).ToList());
                    if (lst3 != null && lst3.Count > 0)
                    {
                        List<ListFetchEpisodeItemDetail> lstSurg = _mapper.Map<List<EpisodeItemDetail>, List<ListFetchEpisodeItemDetail>>(lst3);
                        lst3.ForEach(x =>
                        {
                            if (x.EpisodeItemCustomFeeAssocs != null && x.EpisodeItemCustomFeeAssocs.Count > 0)
                            {
                                ListFetchEpisodeItemDetail item = lstSurg.Where(y => y.Id == x.Id).FirstOrDefault();
                                if (item != null)
                                {
                                    List<EpisodeItemFeeAssoc> lstFeeAssoc = _mapper.Map<List<EpisodeItemCustomFeeAssoc>, List<EpisodeItemFeeAssoc>>(x.EpisodeItemCustomFeeAssocs.ToList());
                                    item.EpisodeItemFeeAssocs = lstFeeAssoc;
                                }
                            }

                        });

                        if (lstEpisodeDetail != null && lstSurg != null) lstEpisodeDetail.AddRange(lstSurg);
                    }
                }
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Result = lstEpisodeDetail;
            }
            if (apiResponse != null && apiResponse.StatusCode == StatusCodes.Status200OK) return apiResponse;
            else
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Mbs Item Details cannot be fetched with the data provided.");
            }
            return apiResponse;
        }
    }
}
