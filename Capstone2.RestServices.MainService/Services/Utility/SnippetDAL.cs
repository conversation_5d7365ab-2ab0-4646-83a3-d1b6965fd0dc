﻿using AutoMapper;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.Utility.Context;
using Capstone2.RestServices.Utility.Interfaces;
using Capstone2.RestServices.Utility.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Utility.Services
{
    public class SnippetDAL : ISnippetDAL
    {
        public readonly ReadOnlyUtilityDBContext _readOnlyDbContext;
        public readonly UpdatableUtilityDBContext _updatableDBContext;
        public IMapper _mapper;
        public SnippetDAL(ReadOnlyUtilityDBContext readOnlyDbContext, UpdatableUtilityDBContext updatableDBContext, IMapper mapper)
        {
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
            _mapper = mapper;
        }

        /// <summary>
        /// Method to add snippet to snippetdetails table
        /// </summary>
        /// <param name="snippetDetail"></param>
        /// <returns></returns>
        public async Task<long> AddSnippetDetailsAsync(SnippetDetail snippetDetail)
        {
            await _updatableDBContext.SnippetDetails.AddAsync(snippetDetail);
            int rows = await _updatableDBContext.SaveChangesAsync();
            if (rows > 0)
                return snippetDetail.Id;
            return 0;
        }

        /// <summary>
        /// Method to check if the snippet shortcut exists in the database
        /// </summary>
        /// <param name="search_term"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<bool> CheckSnippetShortcut(string search_term, int orgId)
        {
            var shortcut = await _readOnlyDbContext.SnippetDetails
                   .Where(p => p.Shortcut.Equals(search_term) && p.OrgId == orgId && p.StatusId == (short)Status.Active).FirstOrDefaultAsync();
            if (shortcut == null)
                return false;
            else
                return true;
        }

        /// <summary>
        /// Method to check if the snippet name exists in the database
        /// </summary>
        /// <param name="search_term"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<bool> CheckSnippetName(string search_term, int orgId)
        {
            var name = await _readOnlyDbContext.SnippetDetails
                   .Where(p => p.Name.Equals(search_term) && p.OrgId == orgId && p.StatusId == (short)Status.Active).FirstOrDefaultAsync();
            if (name == null)
                return false;
            else
                return true;
        }

        /// <summary>
        /// Method to retrieve list of snippets
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="loggedInUser"></param>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        public async Task<QueryResultList<SnippetDetail>> ListSnippets(BaseHttpRequestContext baseHttpRequestContext, RolePermissionFilterModel rolePermissionFilterModel, QueryModel queryModel, SnippetFilterModel filterModel)
        {
            var snippetQuery = (from SD in _readOnlyDbContext.SnippetDetails
                                where SD.StatusId == (short)Status.Active && SD.OrgId == baseHttpRequestContext.OrgId
                                select SD);
            if (filterModel is not null && filterModel.UserDetailsId is not null && filterModel.UserDetailsId.Any())
            {
                List<long> lstSnippetDetailsId = await _readOnlyDbContext.SnippetUserAssocs
                    .Where(x => x.OrgId == baseHttpRequestContext.OrgId && x.StatusId == (short)Status.Active &&
                      filterModel.UserDetailsId.Contains(x.UserDetailsId))
                       .Select(x => x.SnippetDetailsId).ToListAsync();
                if (lstSnippetDetailsId is not null)
                    snippetQuery = snippetQuery.Where(x => lstSnippetDetailsId.Contains(x.Id));
            }

            if (!(string.IsNullOrWhiteSpace(queryModel.SearchTerm)))
            {
                snippetQuery = SearchSnippets(snippetQuery, queryModel.SearchTerm,queryModel.SortTerm);

            }
            if (!(string.IsNullOrWhiteSpace(queryModel.SortOrder)) && !(string.IsNullOrWhiteSpace(queryModel.SortTerm)))
            {
                snippetQuery = SortUsers(snippetQuery, queryModel.SortTerm, queryModel.SortOrder, queryModel.SearchTerm);
            }

            if (baseHttpRequestContext.RoleId != (int)RoleType.SuperUser && rolePermissionFilterModel.isDeleteOnly)
                snippetQuery = snippetQuery.Where(s => s.CreatedBy == baseHttpRequestContext.UserId);


            var queryList = await CreatePaginatedListAsync(snippetQuery, queryModel);

            return queryList;
        }

        private async Task<QueryResultList<SnippetDetail>> CreatePaginatedListAsync(IQueryable<SnippetDetail> snippetQuery, QueryModel queryModel)
        {
            List<SnippetDetail> paginatedList = null;
            if (queryModel.PageNumber > 0 && queryModel.PageSize > 0)
            {
                if (snippetQuery.Any())
                {
                    paginatedList = await snippetQuery.Skip((queryModel.PageNumber - 1) * queryModel.PageSize)
                 .Take(queryModel.PageSize).ToListAsync();
                }
            }

            QueryResultList<SnippetDetail> queryList = new QueryResultList<SnippetDetail>();
            if (paginatedList != null)
            {
                queryList.ItemRecords = paginatedList;
                queryList.PageNumber = queryModel.PageNumber;
                queryList.PageSize = queryModel.PageSize;
                queryList.CurrentCount = paginatedList.Count();
            }
            else
            {
                queryList.ItemRecords = null;
                queryList.CurrentCount = 0;
            }
            queryList.TotalCount = snippetQuery.Count();
            queryList.PageNumber = queryModel.PageNumber;
            queryList.PageSize = queryModel.PageSize;

            return queryList;
        }

        /// <summary>
        /// method to enable search of snippets
        /// </summary>
        /// <param name="snippetQuery"></param>
        /// <param name="searchTerm"></param>
        /// <returns></returns>
        private IQueryable<SnippetDetail> SearchSnippets(IQueryable<SnippetDetail> snippetQuery, string searchTerm,string sortTerm)
        {
            if(sortTerm.ToLower() == "shortcut")
            {
                return snippetQuery.Where(s => s.Shortcut.ToLower().StartsWith(searchTerm.ToLower()));
            }
            else 
            { 
                 return snippetQuery.Where(s => s.Name.Contains(searchTerm) || s.Shortcut.Contains(searchTerm));
            }
        }

        private IQueryable<SnippetDetail> SortUsers(IQueryable<SnippetDetail> snippetQuery, string sortTerm, string sortOrder,string searchTerm)
        {
            switch (sortTerm.ToLower())
            {
                case "id":
                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            snippetQuery = snippetQuery.OrderBy(x => x.Id);
                        }
                        else
                        {
                            snippetQuery = snippetQuery.OrderByDescending(x => x.Id);
                        }
                        break;
                    }
                case "name":
                    {

                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            snippetQuery = snippetQuery.OrderBy(x => x.Name);

                        }
                        else
                        {
                            snippetQuery = snippetQuery.OrderByDescending(x => x.Name);

                        }
                        break;
                    }
                case "shortcut":
                    {

                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            snippetQuery = snippetQuery.OrderBy(x => x.Shortcut);

                        }
                        else
                        {
                            snippetQuery = snippetQuery.OrderByDescending(x => x.Shortcut);

                        }
                        break;
                    }
                
            }
            
            return snippetQuery;

        }

        /// <summary>
        /// Method to fetch details of a snippet based on id
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<InputSnippetDetail> GetSnippetDetails(int orgId, long id)
        {
            var snippet = await (from SD in _readOnlyDbContext.SnippetDetails.Where(x => x.Id == id && x.OrgId == orgId)
                                 from UC in _readOnlyDbContext.UserDetails.Where(u => u.OrgId == orgId && u.Id == SD.CreatedBy)
                                 select new InputSnippetDetail()
                                 {
                                     Id = SD.Id,
                                     Name = SD.Name,
                                     StatusId = SD.StatusId,
                                     Shortcut = SD.Shortcut,
                                     Text = SD.Text,
                                     SFDTText = SD.SFDTText,
                                     CreatedDate = SD.CreatedDate,
                                     CreatedBy = SD.CreatedBy,
                                     CreatedByUserInfo = new UserDetailInfo
                                     {
                                         Id = UC.Id,
                                         FirstName = UC.FirstName,
                                         SurName = UC.SurName,
                                         OrgId = UC.OrgId
                                     },
                                     ModifiedBy = SD.ModifiedBy,
                                     ModifiedDate = SD.ModifiedDate,
                                     OrgId = SD.OrgId,
                                     SnippetUserAssocs = (ICollection<InputSnippetUserAssoc>)(from SU in _readOnlyDbContext.SnippetUserAssocs.Where(x => x.SnippetDetailsId == id && x.OrgId == orgId && x.StatusId == (short)Status.Active)
                                                                                              from UD in _readOnlyDbContext.UserDetails.Where(u => u.OrgId == orgId && u.Id == SU.UserDetailsId)
                                                                                              select new InputSnippetUserAssoc()
                                                                                              {
                                                                                                  Id = SU.Id,
                                                                                                  UserDetailsId = (long)SU.UserDetailsId,
                                                                                                  CreatedDate = SU.CreatedDate,
                                                                                                  UserDetails = new UserDetailInfo
                                                                                                  {
                                                                                                      Id = UD.Id,
                                                                                                      OrgId = UD.OrgId,
                                                                                                      FirstName = UD.FirstName,
                                                                                                      SurName = UD.SurName,
                                                                                                      PhotoFileDetailsId = UD.PhotoFileDetailsId
                                                                                                  }
                                                                                              })

                                 }).FirstOrDefaultAsync();
            return snippet;
        }

        /// <summary>
        /// Method to update a snippet
        /// </summary>
        /// <param name="snippet"></param>
        public async Task<int> UpdateSnippet(SnippetDetail snippet)
        {
            _updatableDBContext.SnippetDetails.Update(snippet);
            return await _updatableDBContext.SaveChangesAsync();
        }

        /// <summary>
        /// Fetch SnippetDetails
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<SnippetDetail> GetSnippetFromId(int orgId, long id)
        {
            return await _readOnlyDbContext.SnippetDetails.Where(s => s.Id == id && s.OrgId == orgId).AsNoTracking().FirstOrDefaultAsync();
        }

        /// <summary>
        /// Fetch SnippetUserAssocs
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="snippetDetailsId"></param>
        /// <returns></returns>
        public async Task<List<SnippetUserAssoc>> GetSnippetUserAssoc(int orgId, long snippetDetailsId)
        {
            return await _readOnlyDbContext.SnippetUserAssocs.Where(s => s.SnippetDetailsId == snippetDetailsId && s.OrgId == orgId).AsNoTracking().ToListAsync();
        }

        public async Task<QueryResultList<SnippetDetail>> ListSnippetsSearchUser(BaseHttpRequestContext baseHttpRequestContext, RolePermissionFilterModel rolePermissionFilterModel, QueryModel queryModel, SnippetFilterModel filterModel)
        {
            IQueryable<ListSnippetDetail> queryForAssignedUser = null;
            if (!(string.IsNullOrWhiteSpace(queryModel.SearchTerm)))
            {
                queryForAssignedUser = await ListofSnippetAssignedUser(baseHttpRequestContext.OrgId, queryModel.SearchTerm);
            }
            var snippetQuery = (from SD in _readOnlyDbContext.SnippetDetails
                                join UD in _readOnlyDbContext.UserDetails on SD.CreatedBy equals UD.Id
                                where SD.StatusId == (short)Status.Active && SD.OrgId == baseHttpRequestContext.OrgId
                                select new ListSnippetDetail
                                {
                                    Id = SD.Id,
                                    OrgId = SD.OrgId,
                                    Name = SD.Name,
                                    Shortcut = SD.Shortcut,
                                    Text = SD.Text,
                                    SFDTText = SD.SFDTText,
                                    CreatedBy = SD.CreatedBy,
                                    ModifiedBy = SD.ModifiedBy,
                                    CreatedDate = SD.CreatedDate,
                                    ModifiedDate = SD.ModifiedDate,
                                    DeleteReason = SD.DeleteReason,
                                    StatusId = SD.StatusId,
                                    CreatedByFirstName = UD.FirstName,
                                    CreatedBySurName = UD.SurName
                                });

            if (!(string.IsNullOrWhiteSpace(queryModel.SearchTerm)))
            {
                snippetQuery = SearchSnippetsWithCreatedUser(snippetQuery, queryModel.SearchTerm);
               
                if(queryForAssignedUser is not null)
                {
                   snippetQuery = snippetQuery.Union((IEnumerable<ListSnippetDetail>)queryForAssignedUser);
                }

            }
            if (!(string.IsNullOrWhiteSpace(queryModel.SortOrder)) && !(string.IsNullOrWhiteSpace(queryModel.SortTerm)))
            {
                snippetQuery = SortSnippetsWithCreatedUser(snippetQuery, queryModel.SortTerm, queryModel.SortOrder,queryModel.SearchTerm);
            }

            if (baseHttpRequestContext.RoleId != (int)RoleType.SuperUser && rolePermissionFilterModel.isDeleteOnly)
                snippetQuery = snippetQuery.Where(s => s.CreatedBy == baseHttpRequestContext.UserId);

            var queryList = await PaginatedResultListAsync(snippetQuery, queryModel);

            return queryList;
        }

        public async Task<QueryResultList<SnippetDetail>> PaginatedResultListAsync(IQueryable<ListSnippetDetail> snippetQuery, QueryModel queryModel)
        {
            List<ListSnippetDetail> paginatedList = null;
            QueryResultList<SnippetDetail> queryList = new QueryResultList<SnippetDetail>();
            if (queryModel.PageNumber > 0 && queryModel.PageSize > 0)
            {
                if (snippetQuery.Any())
                {
                    paginatedList = await snippetQuery.Skip((queryModel.PageNumber - 1) * queryModel.PageSize)
                 .Take(queryModel.PageSize).ToListAsync();
                }
            }

            if(paginatedList != null)
            {
                var snippetQueryMap = paginatedList.Select(x => _mapper.Map<ListSnippetDetail, SnippetDetail>(x)).ToList();

                if (paginatedList != null)
                {
                    queryList.ItemRecords = snippetQueryMap;
                    queryList.PageNumber = queryModel.PageNumber;
                    queryList.PageSize = queryModel.PageSize;
                    queryList.CurrentCount = snippetQueryMap.Count();
                }
                else
                {
                    queryList.ItemRecords = null;
                    queryList.CurrentCount = 0;
                }
                queryList.TotalCount = snippetQuery.Count();
                queryList.PageNumber = queryModel.PageNumber;
                queryList.PageSize = queryModel.PageSize;
            }

            return queryList;
        }

        private IQueryable<ListSnippetDetail> SortSnippetsWithCreatedUser(IQueryable<ListSnippetDetail> snippetQuery, string sortTerm, string sortOrder,string searchTerm)
        {
            switch (sortTerm.ToLower())
                {
                    case "id":
                        {
                            if ("asc".Equals(sortOrder.ToLower()))
                            {
                                snippetQuery = snippetQuery.OrderBy(x => x.Id);
                            }
                            else
                            {
                                snippetQuery = snippetQuery.OrderByDescending(x => x.Id);
                            }
                            break;
                        }
                    case "name":
                        {

                            if ("asc".Equals(sortOrder.ToLower()))
                            {
                                snippetQuery = snippetQuery.OrderBy(x => x.Name);
                                             
                            }
                            else
                            {
                                snippetQuery = snippetQuery.OrderByDescending(x => x.Name);
                                             
                            }
                            break;
                        }
                }
             return snippetQuery;

        }
        private IQueryable<ListSnippetDetail> SearchSnippetsWithCreatedUser(IQueryable<ListSnippetDetail> snippetQuery, string searchTerm)
        {
            string filterString = string.Empty;
            bool istwoWordSearch = Regex.IsMatch(searchTerm, @"\s");

            if (!istwoWordSearch)
            {
                snippetQuery = snippetQuery.Where(s => s.Name.Contains(searchTerm) || s.Shortcut.Contains(searchTerm) || s.CreatedByFirstName.StartsWith(searchTerm) || s.CreatedBySurName.StartsWith(searchTerm));
            }
            else
            {
                string[] search = searchTerm.Split(" ");
                snippetQuery = snippetQuery.Where(s => s.Name.Contains(searchTerm) || s.Shortcut.Contains(searchTerm) || (s.CreatedByFirstName.StartsWith(search[0]) && s.CreatedBySurName.StartsWith(search[1])));
            }
            return snippetQuery;
        }

        private async Task<IQueryable<ListSnippetDetail>> ListofSnippetAssignedUser(int orgId,string searchTerm)
        {
            string filterString = string.Empty;
            List<UserDetail> userListObj = null;
            bool istwoWordSearch = Regex.IsMatch(searchTerm, @"\s");

            if (!istwoWordSearch)
            {
                userListObj = await _readOnlyDbContext.UserDetails.Where(s => s.OrgId == orgId && (s.FirstName.StartsWith(searchTerm) || s.SurName.StartsWith(searchTerm))).ToListAsync();
            }
            else
            {
                string[] search = searchTerm.Split(" ");
                userListObj = await _readOnlyDbContext.UserDetails.Where(s =>  (s.OrgId == orgId && (s.FirstName.StartsWith(search[0]) && s.SurName.StartsWith(search[1])))).ToListAsync();
            }

     
            var userList = userListObj.Select(x => x.Id).ToList();

            if (userList is not null && userList.Count > 0)
            {
                var snippetList = await (from SU in _readOnlyDbContext.SnippetUserAssocs
                                         where SU.StatusId == (short)Status.Active && userList.Contains(SU.UserDetailsId) && SU.OrgId == orgId
                                         select SU.SnippetDetailsId).ToListAsync();



                var snippetObjList  = (from SD in _readOnlyDbContext.SnippetDetails
                                        join UD in _readOnlyDbContext.UserDetails on SD.CreatedBy equals UD.Id
                                        where SD.StatusId == (short)Status.Active && SD.OrgId == orgId && snippetList.Contains(SD.Id)
                                       select new ListSnippetDetail
                                        {
                                            Id = SD.Id,
                                            OrgId = SD.OrgId,
                                            Name = SD.Name,
                                            Shortcut = SD.Shortcut,
                                            Text = SD.Text,
                                            SFDTText = SD.SFDTText,
                                            CreatedBy = SD.CreatedBy,
                                            ModifiedBy = SD.ModifiedBy,
                                            CreatedDate = SD.CreatedDate,
                                            ModifiedDate = SD.ModifiedDate,
                                            DeleteReason = SD.DeleteReason,
                                            StatusId = SD.StatusId,
                                            CreatedByFirstName = UD.FirstName,
                                            CreatedBySurName = UD.SurName
                                        });            

                return snippetObjList;
            }

            return null;
           

        }
    }
}