﻿using AutoMapper;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.Framework.RestApi.Utility;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.Utility.Interfaces;
using Capstone2.RestServices.Utility.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Transactions;
using AppointmentTypes = Capstone2.RestServices.Utility.Models.AppointmentTypes;

namespace Capstone2.RestServices.Utility.Services
{
    public class AppointmentTypeBAL : IAppointmentTypeBAL
    {
        public readonly IAppointmentTypeDAL _appointmentTypeDAL;
        public IMapper _mapper;
        public readonly AppSettings _appSettings;
        private IASBMessageSenderHelper _asbMessageSenderHelper;
        private readonly IConfiguration _configuration;

        public AppointmentTypeBAL(IAppointmentTypeDAL appointmentTypeDAL, IMapper mapper, IOptions<AppSettings> appSettings, IASBMessageSenderHelper asbMessageSenderHelper, IConfiguration configuration)
        {
            _appointmentTypeDAL = appointmentTypeDAL;
            _mapper = mapper;
            _appSettings = appSettings.Value;
            this._asbMessageSenderHelper = asbMessageSenderHelper;
            this._configuration = configuration;

        }

        /// <summary>
        /// Method to save a entry in AppointmentTypes
        /// </summary>
        /// <param name="appointmentTypePayload"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> AddAppointmentType(AppointmentTypes appointmentType, BaseHttpRequestContext baseHttpRequestContext)
        {
            var orgId = baseHttpRequestContext.OrgId;
            var loggedInUser = baseHttpRequestContext.UserId;

            appointmentType.OrgId = orgId;
            appointmentType.CreatedBy = loggedInUser;
            appointmentType.CreatedDate = DateTime.UtcNow;
            appointmentType.StatusId = (short)Status.Active;

            var checkColour = await CheckAppointmentColour(appointmentType.Colour, baseHttpRequestContext);
            if(checkColour != null && checkColour.Result)
            {
                ApiResponse<long?> apiResponse = new()
                {
                    StatusCode = StatusCodes.Status400BadRequest,
                    Message = "Duplicate color code.",
                    Result = null
                };
                return apiResponse;
            }

            var checkType = await CheckAppointmentType(appointmentType.Type, baseHttpRequestContext);
            if (checkType != null && checkType.Result)
            {
                ApiResponse<long?> apiResponse = new()
                {
                    StatusCode = StatusCodes.Status400BadRequest,
                    Message = "Duplicate Appointment Type.",
                    Result = null
                };
                return apiResponse;
            }

            if (appointmentType?.HospitalAssocs?.Any() ?? false)
            {
                appointmentType.HospitalAssocs.ToList().ForEach(a => { a.OrgId = orgId; a.CreatedBy = loggedInUser; a.StatusId = (short)Status.Active; });
            }

            if (appointmentType?.AnaesthetistsAssocs?.Any() ?? false)
            {
                appointmentType.AnaesthetistsAssocs.ToList().ForEach(a => { a.OrgId = orgId; a.CreatedBy = loggedInUser; a.StatusId = (short)Status.Active; });
            }

            if (appointmentType?.AssistantsAssocs?.Any() ?? false)
            {
                appointmentType.AssistantsAssocs.ToList().ForEach(a => { a.OrgId = orgId; a.CreatedBy = loggedInUser; a.StatusId = (short)Status.Active; });
            }

            if(appointmentType.Category == (short)AppointmentType.Surgical_Bookings || appointmentType.Category == (short)AppointmentType.Patient_Appointment)
            {
                if (appointmentType?.SMSAssocs?.Any() ?? false)
                {
                    appointmentType.SMSAssocs.ToList().ForEach(a => { a.OrgId = orgId; a.CreatedBy = loggedInUser; a.StatusId = (short)Status.Active; });
                }
            }
            else
            {
                appointmentType.SMSAssocs = null;
                appointmentType.IsSmsReminderOn = false;
            }

            DateTime.SpecifyKind(appointmentType.CreatedDate, DateTimeKind.Utc);
            var id = await _appointmentTypeDAL.AddAppointmentTypesAsync(appointmentType);
            if (id > 0)
            {
                ApiResponse<long?> apiResponse = new()
                {
                    StatusCode = StatusCodes.Status200OK,
                    Message = "Success",
                    Result = id
                };
                return apiResponse;
            }
            else
            {
                ApiResponse<long?> apiResponse = new()
                {
                    StatusCode = StatusCodes.Status400BadRequest,
                    Message = "Failure",
                    Result = null
                };
                return apiResponse;
            }
        }

        /// <summary>
        /// Method to generate a paginated list of AppointmentTypes based on search criteria
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        public async Task<ApiResponse<QueryResultList<AppointmentTypeList>>> ListAppointmentTypes(BaseHttpRequestContext baseHttpRequestContext, QueryModel queryModel)
        {
            ApiResponse<QueryResultList<AppointmentTypeList>> apiResponse = new();
            var orgId = baseHttpRequestContext.OrgId;
            var loggedInUser = baseHttpRequestContext.UserId;
            AppointmentTypeFilterModel appointmentTypeFilterModel = PrepareFilterParameters(queryModel.Filter);
            var queryList = await _appointmentTypeDAL.ListAppointmentTypes(orgId, loggedInUser, queryModel, appointmentTypeFilterModel);

            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result = queryList;
            return apiResponse;
        }
        private AppointmentTypeFilterModel PrepareFilterParameters(string filter)
        {
            if (!(String.IsNullOrEmpty(filter)))
            {
                return JsonConvert.DeserializeObject<AppointmentTypeFilterModel>(filter);
            }
            else
            {
                return null;
            }
        }
       
        /// <summary>
        /// Get Appointment Type by ID
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<AppointmentTypeView>> GetAppointmentType(BaseHttpRequestContext baseHttpRequestContext, long id)
        {
            ApiResponse<AppointmentTypeView> apiResposne = new();
            var orgId = baseHttpRequestContext.OrgId;
            var appointmentTypeFromDB = await _appointmentTypeDAL.GetAppointmentTypes(orgId, id);
            if (appointmentTypeFromDB is null)
            {
                apiResposne.Result = null;
                apiResposne.StatusCode = StatusCodes.Status400BadRequest;
                apiResposne.Message = "Failure";
                apiResposne.Errors.Add("The Appointment Type doesnot exist.");
                return apiResposne;
            }
            if (appointmentTypeFromDB is not null)
            {
                appointmentTypeFromDB.CreatedDate = DateTime.SpecifyKind(appointmentTypeFromDB.CreatedDate, DateTimeKind.Utc);
                appointmentTypeFromDB.ModifiedDate = (appointmentTypeFromDB.ModifiedDate == null) ? null : DateTime.SpecifyKind((DateTime)appointmentTypeFromDB.ModifiedDate, DateTimeKind.Utc);

                apiResposne.Result = appointmentTypeFromDB;
                apiResposne.StatusCode = StatusCodes.Status200OK;
                apiResposne.Message = "Success";
                return apiResposne;
            }
            else
            {
                apiResposne.Result = null;
                apiResposne.StatusCode = StatusCodes.Status500InternalServerError;
                apiResposne.Message = "Data for given Appointment Type ID is not found";
                return apiResposne;
            }
        }

        /// <summary>
        /// Edit Appointment Type
        /// </summary>
        /// <param name="id"></param>
        /// <param name="inputAppointmentType"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<string>> EditAppointmentType(long id, AppointmentTypes inputAppointmentType, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<string> apiResponse = new();
            var orgId = baseHttpRequestContext.OrgId;
            var userId = baseHttpRequestContext.UserId;
            var appointmentFromDb = await _appointmentTypeDAL.GetAppointmentTypesFromId(orgId, id);

            inputAppointmentType.OrgId = orgId;
            inputAppointmentType.ModifiedDate = DateTime.UtcNow;
            inputAppointmentType.ModifiedBy = userId;
            inputAppointmentType.CreatedBy = appointmentFromDb.CreatedBy;
            inputAppointmentType.CreatedDate = appointmentFromDb.CreatedDate;
            inputAppointmentType.Id = appointmentFromDb.Id;
            short propertyType = default(short);
            if(inputAppointmentType.Colour != appointmentFromDb.Colour)
            {
                var checkColour = await CheckAppointmentColour(inputAppointmentType.Colour, baseHttpRequestContext);
                if (checkColour != null && checkColour.Result)
                {
                    ApiResponse<long?> apiResponses = new()
                    {
                        StatusCode = StatusCodes.Status400BadRequest,
                        Message = "Duplicate color code.",
                        Result = null
                    };
                    return apiResponse;
                }
            }

            if(inputAppointmentType.Type != appointmentFromDb.Type)
            {
                var checkType = await CheckAppointmentType(inputAppointmentType.Type, baseHttpRequestContext);
                if (checkType != null && checkType.Result)
                {
                    ApiResponse<long?> apiResponses = new()
                    {
                        StatusCode = StatusCodes.Status400BadRequest,
                        Message = "Duplicate Appointment Type.",
                        Result = null
                    };
                    return apiResponse;
                }
            }
            if (inputAppointmentType!=null && inputAppointmentType.IsSmsReminderOn == true && (appointmentFromDb.IsSmsReminderOn == null || appointmentFromDb.IsSmsReminderOn == false))
                propertyType = (short)AppointmentRequestDataType.AppointmentType_SmsReminder_ON;
            else if (inputAppointmentType != null && inputAppointmentType.IsSmsReminderOn == false && (appointmentFromDb!=null && appointmentFromDb.IsSmsReminderOn == true))
                propertyType = (short)AppointmentRequestDataType.AppointmentType_SmsReminder_OFF;

            if (inputAppointmentType != null && inputAppointmentType.HospitalAssocs.Any())
            {
                inputAppointmentType.HospitalAssocs.ToList().ForEach(a => { a.OrgId = orgId; a.AppointmentTypesId = id; });
            }
            if (inputAppointmentType != null && inputAppointmentType.AnaesthetistsAssocs.Any())
            {
                inputAppointmentType.AnaesthetistsAssocs.ToList().ForEach(a => { a.OrgId = orgId; a.AppointmentTypesId = id; });
            }
            if (inputAppointmentType != null && inputAppointmentType.AssistantsAssocs.Any())
            {
                inputAppointmentType.AssistantsAssocs.ToList().ForEach(a => { a.OrgId = orgId; a.AppointmentTypesId = id; });
            }
            if (inputAppointmentType != null && inputAppointmentType.SMSAssocs.Any())
            {
                inputAppointmentType.SMSAssocs.ToList().ForEach(a => { a.OrgId = orgId; a.AppointmentTypesId = id; });


            }
            inputAppointmentType.HospitalAssocs = await EditAppointmentTypesHospitalAssoc(inputAppointmentType.HospitalAssocs, id, orgId, userId);

            inputAppointmentType.AnaesthetistsAssocs = await EditAppointmentTypesAnaesthetistAssocs(inputAppointmentType.AnaesthetistsAssocs, id, orgId, userId);

            inputAppointmentType.AssistantsAssocs = await EditAppointmentTypesAssistantAssocs(inputAppointmentType.AssistantsAssocs, id, orgId, userId);

            inputAppointmentType.SMSAssocs = await EditAppointmentTypesSMSAssocs(inputAppointmentType.SMSAssocs, id, orgId, userId);
            if (inputAppointmentType != null && inputAppointmentType.SMSAssocs.Any())
            {
               // inputAppointmentType.SMSAssocs.ToList().ForEach(a => { a.OrgId = orgId; a.AppointmentTypesId = id; });

                if (propertyType == default(short))
                    propertyType = (short)AppointmentRequestDataType.AppointmentType_SmsReminder_Update;
            }
            //using (var transaction = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
            //{
            //if (inputAppointmentType != null && inputAppointmentType.SMSAssocs.Any())
            //{
            //    inputAppointmentType.SMSAssocs.ToList().ForEach(a => { a.OrgId = orgId; a.AppointmentTypesId = id; });

            //    if (propertyType == default(short))
            //        propertyType = (short)AppointmentRequestDataType.AppointmentType_SmsReminder_Update;
            //}

            int rows = await _appointmentTypeDAL.UpdateAppointmentTypes(inputAppointmentType);

                apiResponse.Result = "Success";
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";

            //    transaction.Complete();
            //}
            if (propertyType > default(short))
                await StoreAppointmentTypeMessage(baseHttpRequestContext.OrgCode, id, propertyType);
                return apiResponse;
        }

        /// <summary>
        /// Method to check if the entered Appointment Type is unique
        /// </summary>
        /// <param name="search_term"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<ApiResponse<bool>> CheckAppointmentType(string search_term, BaseHttpRequestContext baseHttpRequestContext)
        {
            var nameBool = await _appointmentTypeDAL.CheckAppointmentType(search_term, baseHttpRequestContext.OrgId);
            return new ApiResponse<bool>
            {
                Result = nameBool,
                Message = "Success",
                StatusCode = StatusCodes.Status200OK
            };
        }

        /// <summary>
        /// Method to check if the entered Appointment Colour is unique
        /// </summary>
        /// <param name="search_term"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<ApiResponse<bool>> CheckAppointmentColour(string search_term, BaseHttpRequestContext baseHttpRequestContext)
        {
            var nameBool = await _appointmentTypeDAL.CheckAppointmentColour(search_term, baseHttpRequestContext.OrgId);
            return new ApiResponse<bool>
            {
                Result = nameBool,
                Message = "Success",
                StatusCode = StatusCodes.Status200OK
            };
        }

        private async Task<List<AppointmentTypesHospitalAssocs>> EditAppointmentTypesHospitalAssoc(ICollection<AppointmentTypesHospitalAssocs> inputUserList, long id, int orgId, long userId)
        {
            List<AppointmentTypesHospitalAssocs> addUserList = new();

            var removeUserList = await _appointmentTypeDAL.GetAppointmentTypesHospitalAssoc(orgId, id);

            foreach (var userAssoc in inputUserList)
            {
                if (userAssoc.Id > 0)
                {
                    var existingobj = removeUserList.FirstOrDefault(x => x.Id == userAssoc.Id);
                    removeUserList.Remove(existingobj);
                }
                else
                {
                    var userAssocDB = removeUserList.Where(x => x.CompanyDetailsId == userAssoc.CompanyDetailsId).FirstOrDefault();
                    if (userAssocDB is null)
                    {
                        userAssoc.CreatedDate = DateTime.UtcNow;
                        userAssoc.StatusId = (short)Status.Active;
                        addUserList.Add(userAssoc);
                    }
                    else
                    {
                        if (userAssocDB.StatusId == (short)Status.Deleted)
                        {
                            userAssocDB.StatusId = (short)Status.Active;
                            userAssocDB.ModifiedDate = DateTime.UtcNow;
                            userAssocDB.ModifiedBy = userId;
                            addUserList.Add(userAssocDB);
                        }
                        removeUserList.Remove(userAssocDB);
                    }
                }
            }

            foreach (var userAssoc in removeUserList.Where(x => x.StatusId == (short)Status.Active))
            {
                userAssoc.StatusId = (short)Status.Deleted;
                userAssoc.ModifiedDate = DateTime.UtcNow;
                userAssoc.ModifiedBy = userId;
            }

            addUserList = addUserList.Concat(removeUserList).ToList();
            return addUserList;
        }

        private async Task<List<AppointmentTypesAnaesthetistAssocs>> EditAppointmentTypesAnaesthetistAssocs(ICollection<AppointmentTypesAnaesthetistAssocs> inputUserList, long id, int orgId, long userId)
        {
            List<AppointmentTypesAnaesthetistAssocs> addUserList = new();

            var removeUserList = await _appointmentTypeDAL.GetAppointmentTypesAnaesthetistAssocs(orgId, id);

            foreach (var userAssoc in inputUserList)
            {
                if (userAssoc.Id > 0)
                {
                    var existingobj = removeUserList.FirstOrDefault(x => x.Id == userAssoc.Id);
                    removeUserList.Remove(existingobj);
                }
                else
                {
                    var userAssocDB = removeUserList.Where(x => x.UserDetailsId == userAssoc.UserDetailsId).FirstOrDefault();
                    if (userAssocDB is null)
                    {
                        userAssoc.CreatedDate = DateTime.UtcNow;
                        userAssoc.StatusId = (short)Status.Active;
                        addUserList.Add(userAssoc);
                    }
                    else
                    {
                        if (userAssocDB.StatusId == (short)Status.Deleted)
                        {
                            userAssocDB.StatusId = (short)Status.Active;
                            userAssocDB.ModifiedDate = DateTime.UtcNow;
                            userAssocDB.ModifiedBy = userId;
                            addUserList.Add(userAssocDB);
                        }
                        removeUserList.Remove(userAssocDB);
                    }
                }
            }

            foreach (var userAssoc in removeUserList.Where(x => x.StatusId == (short)Status.Active))
            {
                userAssoc.StatusId = (short)Status.Deleted;
                userAssoc.ModifiedDate = DateTime.UtcNow;
                userAssoc.ModifiedBy = userId;
            }

            addUserList = addUserList.Concat(removeUserList).ToList();
            return addUserList;
        }

        private async Task<List<AppointmentTypesAssistantAssocs>> EditAppointmentTypesAssistantAssocs(ICollection<AppointmentTypesAssistantAssocs> inputUserList, long id, int orgId, long userId)
        {
            List<AppointmentTypesAssistantAssocs> addUserList = new();

            var removeUserList = await _appointmentTypeDAL.GetAppointmentTypesAssistantAssocs(orgId, id);

            foreach (var userAssoc in inputUserList)
            {
                if (userAssoc.Id > 0)
                {
                    var existingobj = removeUserList.FirstOrDefault(x => x.Id == userAssoc.Id);
                    removeUserList.Remove(existingobj);
                }
                else
                {
                    var userAssocDB = removeUserList.Where(x => x.UserDetailsId == userAssoc.UserDetailsId).FirstOrDefault();
                    if (userAssocDB is null)
                    {
                        userAssoc.CreatedDate = DateTime.UtcNow;
                        userAssoc.StatusId = (short)Status.Active;
                        addUserList.Add(userAssoc);
                    }
                    else
                    {
                        if (userAssocDB.StatusId == (short)Status.Deleted)
                        {
                            userAssocDB.StatusId = (short)Status.Active;
                            userAssocDB.ModifiedDate = DateTime.UtcNow;
                            userAssocDB.ModifiedBy = userId;
                            addUserList.Add(userAssocDB);
                        }
                        removeUserList.Remove(userAssocDB);
                    }
                }
            }

            foreach (var userAssoc in removeUserList.Where(x => x.StatusId == (short)Status.Active))
            {
                userAssoc.StatusId = (short)Status.Deleted;
                userAssoc.ModifiedDate = DateTime.UtcNow;
                userAssoc.ModifiedBy = userId;
            }

            addUserList = addUserList.Concat(removeUserList).ToList();
            return addUserList;
        }

        /// <summary>
        /// Method to retrieve Checklists
        /// </summary>
        /// <param name="searchTerm"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<List<Checklist>>> ListChecklists(string searchTerm, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<List<Checklist>> apiResponse = new();
            apiResponse.Result = await _appointmentTypeDAL.ListChecklists(searchTerm, baseHttpRequestContext.OrgId);
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            return apiResponse;

        }

        /// <summary>
        /// Method to retrieve AppointmentTypesSMSAssocs
        /// </summary>
        /// <param name="appointmentTypeId"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns>AppintmentTypesSMSAssocs</returns>
        public async Task<ApiResponse<List<AppointmentTypesSMSAssocsView>>> GetAppointmentTypesSMSAssocs(long appointmentTypeId, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<List<AppointmentTypesSMSAssocsView>> apiResponse = new();
            var appointmentTypesSMSAssocs = await _appointmentTypeDAL.GetAppointmentTypesSMSAssocs(baseHttpRequestContext.OrgId, appointmentTypeId);
            apiResponse.Result = _mapper.Map<List<AppointmentTypesSMSAssocs>, List<AppointmentTypesSMSAssocsView>>(appointmentTypesSMSAssocs);
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            return apiResponse;
        }

        private async Task<List<AppointmentTypesSMSAssocs>> EditAppointmentTypesSMSAssocs(ICollection<AppointmentTypesSMSAssocs> inputSMSList, long id, int orgId, long userId)
        {
            List<AppointmentTypesSMSAssocs> addSMSList = new();

            var removeSMSList = await _appointmentTypeDAL.GetAppointmentTypesSMSAssocs(orgId, id);

            foreach(var sms in inputSMSList)
            {
                if(sms.Id == 0)
                {
                    sms.CreatedDate = DateTime.UtcNow;
                    sms.StatusId = (short)Status.Active;
                    sms.AppointmentTypesId = id;
                    sms.CreatedBy = userId;
                    addSMSList.Add(sms);
                }
                else
                {
                    var existingobj = removeSMSList.FirstOrDefault(x => x.Id == sms.Id);

                    if(existingobj.Days!= sms.Days || existingobj.SMSTemplateId!= sms.SMSTemplateId || existingobj.IsBefore!= sms.IsBefore || existingobj.IsReplyExpected!= sms.IsReplyExpected)
                    {
                        existingobj.Days = sms.Days;
                        existingobj.SMSTemplateId = sms.SMSTemplateId;
                        existingobj.IsBefore = sms.IsBefore;
                        existingobj.IsReplyExpected = sms.IsReplyExpected;
                        existingobj.ModifiedDate = DateTime.UtcNow;
                        existingobj.ModifiedBy = userId;

                        addSMSList.Add(existingobj);
                    }
                  
                    removeSMSList.Remove(existingobj);


                }
            }


            foreach (var userAssoc in removeSMSList.Where(x => x.StatusId == (short)Status.Active))
            {
                userAssoc.StatusId = (short)Status.Deleted;
                userAssoc.ModifiedDate = DateTime.UtcNow;
                userAssoc.ModifiedBy = userId;
            }

            addSMSList = addSMSList.Concat(removeSMSList).ToList();
            return addSMSList;
        }


        private async Task StoreAppointmentTypeMessage(string orgCode, long appointmentTypeId, short propertyType)
        {

            PaymentRequestDataModel updateRequestDataModel = new PaymentRequestDataModel
            {
                OrgName = orgCode,
                PropertyType = propertyType,
                PropertyId = new long[] { appointmentTypeId },
            };
            Dictionary<string, string> userPros = new Dictionary<string, string>
                        {
                            { "RequestType","AppointmentTypeUpdate"},
                            { "to",_configuration["AzureAD:ASBSubNameAppointmentType"]}
                        };

            byte[] jsonContentByteArr = Encoding.GetEncoding("iso-8859-1").GetBytes(JsonConvert.SerializeObject(updateRequestDataModel));
            await _asbMessageSenderHelper.MessageSenderTopic(jsonContentByteArr, userPros, _configuration["AzureAD:AzSBConnStringAppointment"], _configuration["AzureAD:ASBTopicAppointment"]);

        }
    }
}
