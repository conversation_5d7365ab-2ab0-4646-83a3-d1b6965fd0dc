﻿using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.Utility.Interfaces;
using Capstone2.RestServices.Utility.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Responses;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.Options;
using Capstone2.RestServices.MainService.Common;
using Capstone2.Framework.RestApi;
using Microsoft.AspNetCore.Http;
using System;
using Capstone2.RestServices.Utility.Controllers;
using Microsoft.Extensions.Logging;
using Capstone2.Shared.Models.Enum;
using AutoMapper;
using Capstone2.Framework.Business.Common;
using System.Linq;
using System.Transactions;
using Newtonsoft.Json;
using Capstone2.Shared.Models.Entities;

namespace Capstone2.RestServices.Utility.Services
{
    public class MedicareParticipantDetailsBAL : IMedicareParticipantDetailsBAL
    {
        public readonly AppSettings _appSettings;
        public IMapper _mapper;
        private readonly ILogger<MedicareParticipantDetailsBAL> _logger;
        public readonly IMedicareParticipantDetailsDAL _medicareParticipantDetailsDAL;
        private IDistributedCacheHelper _redisCache;
        public MedicareParticipantDetailsBAL(IOptions<AppSettings> appSettings,ILogger<MedicareParticipantDetailsBAL> logger, IMedicareParticipantDetailsDAL medicareParticipantDetailsDAL, IMapper mapper, IDistributedCacheHelper cache)
        {
            _appSettings = appSettings.Value;
            _logger = logger;
            _medicareParticipantDetailsDAL = medicareParticipantDetailsDAL;
            _mapper = mapper;
            _redisCache = cache;
        }
        public async Task<ApiResponse<QueryResultList<MedicalContractorList>>> GetMedicareParticipantDetails(BaseHttpRequestContext baseHttpRequestContext, 
            QueryModel queryModel)
        {
            int ps = 0;
            int pn = 0;
            if (queryModel is not null)
            {
                ps = queryModel.PageSize;
                pn = queryModel.PageNumber;
                string st = queryModel.SearchTerm;
                string soo = queryModel.SortOrder;
                string sot = queryModel.SortTerm;
            }
            ApiResponse<QueryResultList<MedicalContractorList>> apiResponse = new();
            apiResponse.Result = new();
            List<MedicalContractorList> finalContList = await GetMedicalContractors(baseHttpRequestContext);
            MedicalContractorFilterModel mcFilterModel = FilterParametersForMc(queryModel.Filter);

            if(mcFilterModel is not null)
            {
                if (mcFilterModel.StatusId is not null && mcFilterModel.StatusId.Any())
                {
                    finalContList = finalContList.Where(a => mcFilterModel.StatusId.Contains(a.StatusId)).ToList();
                }

                if (mcFilterModel.MedicalContractorParentId is not null && mcFilterModel.MedicalContractorParentId != 0)
                {
                    finalContList = finalContList.Where(a => a.MedicalContractorParentId == mcFilterModel.MedicalContractorParentId).ToList();
                }

                if (mcFilterModel.MedicalContractorParentId is null)
                {
                    finalContList = finalContList.Where(a => a.MedicalContractorParentId == null).ToList();
                }
                if (mcFilterModel.IdExists is not null && mcFilterModel.IdExists == true)
                {
                    finalContList = finalContList.Where(a => a.Id != null).ToList();
                }
                if (mcFilterModel.NameNotEqual is not null)
                {
                    finalContList = finalContList.Where(a => a.Name != mcFilterModel.NameNotEqual).ToList();
                }

                if (mcFilterModel.MedicalContractorTypeId is not null && mcFilterModel.MedicalContractorTypeId.Any())
                {
                    finalContList = finalContList.Where(a => mcFilterModel.MedicalContractorTypeId.Contains(a.MedicalContractorTypeId)).ToList();
                }


            }

            if (!(string.IsNullOrWhiteSpace(queryModel.SearchTerm)))
            {
                finalContList = finalContList.Where(s => s.Name.ToLower().Contains(queryModel.SearchTerm.ToLower())).ToList();
            }

            // TODO: Pagination will be added later since the client table will be used
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result.PageNumber = pn;
            apiResponse.Result.PageSize = ps;
            apiResponse.Result.TotalCount = finalContList.Count;
            apiResponse.Result.ItemRecords = finalContList;
            return apiResponse;
        }

        /// <summary>
        /// Method to save a entry in Medical Contractor
        /// </summary>
        /// <param name="inputMedicalContractor"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> AddMedContBAL(MedicalContractor inputMedicalContractor,BaseHttpRequestContext baseHttpRequestContext)
        {
            var orgId = baseHttpRequestContext.OrgId;
            var loggedInUser = baseHttpRequestContext.UserId;
            int mcId = 0;
            MedicalContractorBandVersion childVersion = new();

            inputMedicalContractor.OrgId = orgId;
            inputMedicalContractor.ModifiedBy = loggedInUser;
            inputMedicalContractor.ModifiedDate = DateTime.UtcNow;

            if (inputMedicalContractor != null && inputMedicalContractor.MedicalContractorAddresses.Any())
            {
                inputMedicalContractor.MedicalContractorAddresses.ToList().ForEach(a => { a.OrgId = baseHttpRequestContext.OrgId; a.ModifiedBy = baseHttpRequestContext.UserId; a.StatusId = (short)Status.Active; });
            }

            if(inputMedicalContractor.MedicalContractorParentId is not null)
            {

              MedicalContractorBandVersion parentLatestVer = await  _medicareParticipantDetailsDAL.GetMCBandVersionLatestFull(orgId, (int)inputMedicalContractor.MedicalContractorParentId);
                childVersion.VersionNo = 1;
                childVersion.ParentVersionNo = parentLatestVer.VersionNo;
                childVersion.StartDate = parentLatestVer.StartDate;
                childVersion.EndDate = parentLatestVer.EndDate;
                childVersion.OrgId = orgId;
                childVersion.StatusId = (short)Status.Active;
                childVersion.ModifiedBy = loggedInUser;
                childVersion.MedicalContractorParentId = inputMedicalContractor.MedicalContractorParentId;
            }

            using (var transaction = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
            {
                mcId = await _medicareParticipantDetailsDAL.AddMedContDAL(inputMedicalContractor);
                if (inputMedicalContractor.MedicalContractorParentId is not null)
                {
                    childVersion.MedicalContractorId = mcId;
                    int addedMCBandVersion = await _medicareParticipantDetailsDAL.AddMedContBandVersionDAL(childVersion);
                }
                transaction.Complete();
            }

           
            if (mcId > 0)
            {
                ApiResponse<long?> apiResponse = new()
                {
                    StatusCode = StatusCodes.Status200OK,
                    Message = "Success",
                    Result = mcId
                };
                return apiResponse;
            }
            else
            {
                ApiResponse<long?> apiResponse = new()
                {
                    StatusCode = StatusCodes.Status400BadRequest,
                    Message = "Failure",
                    Result = null
                };
                return apiResponse;
            }
        }

        /// <summary>
        /// AddMedicalContractorBandAssocs
        /// </summary>
        /// <param name="mcBandAssocs"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> AddMCBandAssocsBAL(int mcId, MCBandAssocInput mcBandAssocInput, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<long?> apiResposne = new();
            MedicalContractorBandVersion versionData = new();
            List<MedicalContractorBandAssoc> mcBandAssocs = mcBandAssocInput.MCBandAssocs;
            List<MedicalContractorBandVersion> childVersionsToadd = new();
            long NumofMCAssoc = 0;
            var userId = baseHttpRequestContext.UserId;
            var orgId = baseHttpRequestContext.OrgId;
            short? latestVer = null;

            if (mcBandAssocInput.IsOverride == false)
            {
                MedicalContractor mcFromDB = await _medicareParticipantDetailsDAL.GetMCForEdit(orgId, mcId);
                if (mcFromDB.MedicalContractorParentId is not null)
                {
                    apiResposne.StatusCode = StatusCodes.Status400BadRequest;
                    apiResposne.Result = null;
                    apiResposne.Message = "Failure";
                    apiResposne.Errors.Add("MedicalContractorBandAssocs cannot be added for Child HealthFunds.Please update the Parent Health Fund Data.");
                    return apiResposne;
                }

                latestVer = await _medicareParticipantDetailsDAL.GetMCBandVersionLatest(orgId, mcId);
                if (latestVer == null)
                {
                    latestVer = 1;
                }
                else
                {
                    latestVer += 1;
                }

                versionData.VersionNo = latestVer;
                versionData.OrgId = orgId;
                versionData.StartDate = mcBandAssocs[0].StartDate;
                versionData.EndDate = mcBandAssocs[0].EndDate;
                versionData.MedicalContractorId = mcId;
                versionData.StatusId = (short)Status.Active;
                versionData.ModifiedBy = userId;

                List<int> childMCs = await _medicareParticipantDetailsDAL.GetMCChildForParent(orgId, mcId);

                if (childMCs is not null && childMCs.Any())
                {

                    List<MedicalContractorBandVersion> bandVersions = await _medicareParticipantDetailsDAL.GetMCAllLatestChildData(orgId, childMCs);
                    foreach (var child in bandVersions)
                    {
                        MedicalContractorBandVersion newChild = new();
                        newChild.VersionNo = (short)(child.VersionNo + 1);
                        newChild.OrgId = orgId;
                        newChild.StartDate = versionData.StartDate;
                        newChild.EndDate = versionData.EndDate;
                        newChild.MedicalContractorId = child.MedicalContractorId;
                        newChild.MedicalContractorParentId = mcId;
                        newChild.ParentVersionNo = versionData.VersionNo;
                        newChild.StatusId = (short)Status.Active;
                        newChild.ModifiedBy = userId;

                        childVersionsToadd.Add(newChild);

                    }

                }
            }


            foreach (var bandAssoc in mcBandAssocs)
            {
                
                bandAssoc.OrgId = orgId;
                bandAssoc.ModifiedBy = userId;
                bandAssoc.StatusId = (short)Status.Active;
                bandAssoc.MedicalContractorId = mcId;
                if(mcBandAssocInput.IsOverride == false) 
                { 
                     bandAssoc.VersionNo = latestVer;
                }
            }

            using (var transaction = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
            {
                if (mcBandAssocInput.IsOverride == false)
                {
                    int addedMCBandVersion = await _medicareParticipantDetailsDAL.AddMedContBandVersionDAL(versionData);
                }

                if(childVersionsToadd.Count > 0)
                {
                    int addedChildVersions = await _medicareParticipantDetailsDAL.AddMCBandVersionMultipleDAL(childVersionsToadd);
                }

                NumofMCAssoc = await _medicareParticipantDetailsDAL.AddMCBandAssocsDAL(mcBandAssocs);

                transaction.Complete();
            }
        

            if (NumofMCAssoc > 0)
            {
                apiResposne.StatusCode = StatusCodes.Status200OK;
                apiResposne.Result = NumofMCAssoc;
                apiResposne.Message = "Success";
            }
            else
            {
                apiResposne.StatusCode = StatusCodes.Status400BadRequest;
                apiResposne.Result = null;
                apiResposne.Message = "Failure";
                apiResposne.Errors.Add("MedicalContractorBandAssocs cannot be added at this time.Please try again later.");
            }
            return apiResposne;
        }

        public async Task<ApiResponse<List<MedicalContractorBandAssoc>>> GetMedContBandAssocBAL(BaseHttpRequestContext baseHttpRequestContext, int id,QueryModel queryModel)
        {
            ApiResponse<List<MedicalContractorBandAssoc>> apiResponse = new();
            MedContBandFilterModel mcFilterModel = FilterForGetMcBand(queryModel.Filter);
            List<MedicalContractorBandAssoc> mcAssocsFromDB = await _medicareParticipantDetailsDAL.GetMedContBandAssocDAL(baseHttpRequestContext.OrgId, id, mcFilterModel);
            if (mcAssocsFromDB is null)
            {
                apiResponse.Result = null;
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("The MedicalContractorBandAssocs doesnot exist.");
                return apiResponse;
            }

            /*  List<MedicalContractorBand> masterBandData = await _redisCache.GetFromCache<List<MedicalContractorBand>>($"{CachedKeys.Tbl_MedicalContractorBand}");
            if (masterBandData == null)
            {
                masterBandData = await GetMasterMedicalContractorBand(baseHttpRequestContext);
            }
           
            mcAssocsFromDB.ForEach(a => {
                var masterObj = masterBandData.Find(f => f.Id == a.MasterMedicalContractorBandId);
                if (masterObj is not null)
                {
                    if (a.MedicalContBandTypeId == 431)
                    {
                        a.Banding = masterObj.Banding;
                    }
                 
                    a.Description = masterObj.Description;
                }
               
            });*/


            apiResponse.Result = mcAssocsFromDB;
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            return apiResponse;
           
        }

        private async Task<List<MedicalContractorBand>> GetMasterMedicalContractorBand(BaseHttpRequestContext baseHttpRequestContext)
        {
            List<MedicalContractorBand> listMasterBands = new();

            string medicalContBandApiUrl = _appSettings.ApiUrls["MasterServiceUrl"] + "/master/MedicalContractorBand";
            RestClient restClient = new RestClient(medicalContBandApiUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            var httpResponse = await restClient.GetAsync<ApiResponse<List<MedicalContractorBand>>>(medicalContBandApiUrl);
            if (httpResponse.StatusCode == StatusCodes.Status200OK && httpResponse.Result is not null)
            {
                listMasterBands = httpResponse.Result;
            }

            return listMasterBands;

        }

        /// <summary>
        /// EditMCBandAssocs
        /// </summary>
        /// <param name="mcBandassocs"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> EditMCBandAssocsBAL(int mcId,List<MedicalContractorBandAssoc> mcBandassocs, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<long?> apiResponse = new();
            var userId = baseHttpRequestContext.UserId;
            var orgId = baseHttpRequestContext.OrgId;
            short versionNo = (short)mcBandassocs[0].VersionNo;
            DateTime? newEndDate = null;
            DateTime newStartDate = (DateTime)mcBandassocs[0].StartDate;
            if (mcBandassocs[0].EndDate is not null)
            {
                 newEndDate = (DateTime)mcBandassocs[0].EndDate;
            }
            else
            {
                 newEndDate = null;
            }
            List<MedicalContractorBandVersion> versionUpdateList = new();
            long id = 0;


            var dbAssocs = await _medicareParticipantDetailsDAL.GetMCBandAssocByMultipleIds(orgId, mcBandassocs.Select(x => (int)x.Id).ToList());

            if (dbAssocs[0].StartDate != newStartDate || (dbAssocs[0].EndDate is not null && newEndDate is not null && dbAssocs[0].EndDate != newEndDate))
            //new code
            {
               /* bool datesVerify = await _medicareParticipantDetailsDAL.CheckMCVersionDatesDAL(mcId, orgId, newStartDate, newEndDate);
                if (datesVerify == true)
                {
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Message = "Failed";
                    apiResponse.Errors.Add("Medical Contractor Parent Version Dates OverRide with the Child Existing Rates.Change the child end date to apply the Parent ");
                    return apiResponse;
                }*/

                //get existing mc data 
                MedicalContractorBandVersion parentLatestVer = await _medicareParticipantDetailsDAL.GetMCBandGivenVersion(orgId, mcId, versionNo);
                parentLatestVer.StartDate = newStartDate;
                parentLatestVer.EndDate = newEndDate;
                parentLatestVer.ModifiedBy = userId;
                parentLatestVer.ModifiedDate = DateTime.UtcNow;
                versionUpdateList.Add(parentLatestVer);

                List<MedicalContractorBandVersion> childLatestVer = await _medicareParticipantDetailsDAL.GetMCChildForParentVersion(orgId, mcId, versionNo);
                if (childLatestVer is not null && childLatestVer.Any())
                {
                    foreach (var child in childLatestVer)
                    {
                        child.StartDate = newStartDate;
                        child.EndDate = newEndDate;
                        child.ModifiedBy = userId;
                        child.ModifiedDate = DateTime.UtcNow;

                        versionUpdateList.Add(child);
                    }

                }
            }
            
            //old code
        
            foreach (var action in mcBandassocs)
            {
                action.OrgId = orgId;
                action.CreatedDate = dbAssocs.Find(x => x.Id == action.Id).CreatedDate;
                action.ModifiedBy = userId;
                action.ModifiedDate = DateTime.UtcNow;
            }

            using (var transaction = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
            {
                id = await _medicareParticipantDetailsDAL.EditMcBandAssoc(mcBandassocs);

                if (versionUpdateList.Count > 0 )
                {
                    int updatedMCBandVersion = await _medicareParticipantDetailsDAL.EditMcBandVersions(versionUpdateList);
                }
                transaction.Complete();
            }

           

            if (id > 0)
            {
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Result = id;
                apiResponse.Message = "Success";
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = null;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("MedicalContractorBandAssoc cannot be Updated at this time.Please try again later.");
            }
            return apiResponse;
        }

        /// <summary>
        /// DeleteMCBandAssocs
        /// </summary>
        /// <param name="mcBandassocIds"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> DeleteMCBandAssocsBAL(int mcid, string deleteIds, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<long?> apiResposne = new();
            var userId = baseHttpRequestContext.UserId;
            var orgId = baseHttpRequestContext.OrgId;

            List<int> mcBandassocIds = deleteIds.Split(',').ToList().Select(s => int.Parse(s)).ToList();

            var dbAssocs = await _medicareParticipantDetailsDAL.GetMCBandAssocByMultipleIds(orgId, mcBandassocIds);

            foreach (var action in dbAssocs)
            {
                if(action.MasterMedicalContractorBandId is not null)
                {
                    apiResposne.StatusCode = StatusCodes.Status400BadRequest;
                    apiResposne.Result = null;
                    apiResposne.Message = "Failure";
                    apiResposne.Errors.Add("Master Band Assocs can not be deleted");

                }
                action.StatusId = (short)Status.Deleted;
                action.ModifiedBy = userId;
                action.ModifiedDate = DateTime.UtcNow;
            }

            long id = await _medicareParticipantDetailsDAL.EditMcBandAssoc(dbAssocs);

            if (id > 0)
            {
                apiResposne.StatusCode = StatusCodes.Status200OK;
                apiResposne.Result = id;
                apiResposne.Message = "Success";
            }
            else
            {
                apiResposne.StatusCode = StatusCodes.Status400BadRequest;
                apiResposne.Result = null;
                apiResposne.Message = "Failure";
                apiResposne.Errors.Add("MedicalContractorBandAssoc cannot be Deleted at this time.Please try again later.");
            }
            return apiResposne;

        }

        /// <summary>
        /// EditMC
        /// </summary>
        /// <param name="mcObj"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> EditMedContBAL(int mcId, MedicalContractor mcObj, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<long?> apiResponse = new();
            var userId = baseHttpRequestContext.UserId;
            var orgId = baseHttpRequestContext.OrgId;
            MedicalContractorBandVersion childVersion = new();
            bool addchild = false;
            short childVerNum = 0;

            var mcFromDB = await _medicareParticipantDetailsDAL.GetMCForEdit(orgId, mcId);
            mcFromDB.MedicalContractorAddresses = await _medicareParticipantDetailsDAL.GetMcAddresses(mcId, orgId);
            if (mcFromDB == null)
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failed";
                apiResponse.Errors.Add("Medical Contractor not found");
                return apiResponse;
            }
            if ((mcFromDB.MedicalContractorParentId is null && mcObj.MedicalContractorParentId is not null)|| (mcFromDB.MedicalContractorParentId is not null && mcObj.MedicalContractorParentId is not null))
            {
                if (mcFromDB.MedicalContractorParentId is not null && mcFromDB.MedicalContractorParentId == mcObj.MedicalContractorParentId)
                {
                    addchild = false;
                }
                else
                {
                    addchild = true;

                    MedicalContractorBandVersion parentLatestVer = await _medicareParticipantDetailsDAL.GetMCBandVersionLatestFull(orgId, (int)mcObj.MedicalContractorParentId);
                    MedicalContractorBandVersion childLatestVer = await _medicareParticipantDetailsDAL.GetMCBandVersionLatestFull(orgId, mcId);
                    bool datesVerify = await _medicareParticipantDetailsDAL.CheckMCVersionDatesDAL(mcId, orgId, (DateTime)parentLatestVer.StartDate, parentLatestVer.EndDate);
                    if (datesVerify == true)
                    {
                        apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                        apiResponse.Message = "Failed";
                        apiResponse.Errors.Add("Medical Contractor Parent Version Dates OverRide with the Child Existing Rates.Change the child end date to apply the Parent ");
                        return apiResponse;
                    }
                    if (childLatestVer == null)
                    {
                        childVerNum = 1;
                    }
                    else
                    {
                        childVerNum = (short)(childLatestVer.VersionNo + 1);
                    }
                    childVersion.MedicalContractorId = mcId;
                    childVersion.VersionNo = childVerNum;
                    childVersion.ParentVersionNo = parentLatestVer.VersionNo;
                    childVersion.StartDate = parentLatestVer.StartDate;
                    childVersion.EndDate = parentLatestVer.EndDate;
                    childVersion.OrgId = orgId;
                    childVersion.StatusId = (short)Status.Active;
                    childVersion.ModifiedBy = userId;
                    childVersion.MedicalContractorParentId = mcObj.MedicalContractorParentId;

                }                
            }

          
            mcObj.OrgId = orgId;
            mcObj.ModifiedBy = userId;
            mcObj.ModifiedDate = DateTime.UtcNow;
            mcObj.Id = mcFromDB.Id;
            mcObj.CreatedDate = mcFromDB.CreatedDate;           
            mcObj.MasterMedicalParticipantsId = mcFromDB.MasterMedicalParticipantsId;


            using (var transaction = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
            {
                //Edit the Addresses
                var addAddressList = new List<MedicalContractorAddress>();
                var removeAddressList = mcFromDB.MedicalContractorAddresses.ToList();
                var inputAddressList = mcObj.MedicalContractorAddresses;
                mcObj.MedicalContractorAddresses = null;

                // Medical contractor edit
                long id = await _medicareParticipantDetailsDAL.EditMc(mcObj);

                foreach (var address in inputAddressList)
                {
                    if (address.Id > 0)
                    {
                        var existingobj = removeAddressList.FirstOrDefault(x => x.Id == address.Id);
                        var result = removeAddressList.Remove(existingobj);
                    }
                    else
                    {
                        var AddressTodb = new MedicalContractorAddress();
                        AddressTodb = address;
                        AddressTodb.OrgId = orgId;
                        AddressTodb.MedicalContractorId = mcId;
                        AddressTodb.ModifiedBy = userId;
                        AddressTodb.StatusId = (short)Status.Active;
                        addAddressList.Add(AddressTodb);
                    }
                }

                foreach (var address in removeAddressList)
                {
                    address.StatusId = (short)Status.Deleted;
                    address.ModifiedDate = DateTime.UtcNow;
                    address.ModifiedBy = userId;
                }

                if (removeAddressList.Count > 0)
                {
                    await _medicareParticipantDetailsDAL.UpdateMcAddressList(removeAddressList);
                }
                if (addAddressList.Count > 0)
                {
                    await _medicareParticipantDetailsDAL.AddMcAddressList(addAddressList);
                }
                if(addchild == true)
                {
                    await _medicareParticipantDetailsDAL.AddMedContBandVersionDAL(childVersion);
                }

                transaction.Complete();
                apiResponse.Result = id;
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";
            }
               
            return apiResponse;
        }

        public async Task<ApiResponse<MedicalContractorView>> GetMedContByIdBAL(int mcId, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<MedicalContractorView> apiResposne = new();
            var userId = baseHttpRequestContext.UserId;
            var orgId = baseHttpRequestContext.OrgId;

            MedicalContractorView clientMedicalCont = await _medicareParticipantDetailsDAL.GetMCByIds(orgId, mcId);
           
            if (clientMedicalCont is null)
            {
                apiResposne.StatusCode = StatusCodes.Status400BadRequest;
                apiResposne.Result = null;
                apiResposne.Message = "Failure";
                apiResposne.Errors.Add("MedicalContractor is not present for given Id.Please try again later.");
                return apiResposne;
            }
            
            if(clientMedicalCont.MasterMedicalParticipantsId is not null)
            {
                MedicareParticipantDetails masterData = await GetMedicareParticipantsData(clientMedicalCont.MasterMedicalParticipantsId, baseHttpRequestContext); 
                clientMedicalCont.ParticipantId = masterData.ParticipantId;
                clientMedicalCont.Name = masterData.ParticipantName;
                clientMedicalCont.Services = masterData.Services;
                clientMedicalCont.StatusId = masterData.StatusId;
            }

            if (clientMedicalCont.ParentData is not null && clientMedicalCont.ParentData.MasterMedicalParticipantsId is not null)
            {
                MedicareParticipantDetails masterDataForParent = await GetMedicareParticipantsData(clientMedicalCont.ParentData.MasterMedicalParticipantsId, baseHttpRequestContext);
                clientMedicalCont.ParentData.ParticipantId = masterDataForParent.ParticipantId;
                clientMedicalCont.ParentData.Name = masterDataForParent.ParticipantName;
                clientMedicalCont.ParentData.StatusId = masterDataForParent.StatusId;
                clientMedicalCont.ParentData.Id = masterDataForParent.Id;
                             
            }


            apiResposne.StatusCode = StatusCodes.Status200OK;
            apiResposne.Result = clientMedicalCont;
            apiResposne.Message = "Success";
            return apiResposne;
        }

        public async Task<MedicareParticipantDetails> GetMedicareParticipantsData(int? masterParticipantId,BaseHttpRequestContext baseHttpRequestContext)
        {
            MedicareParticipantDetails masterData = new();

            string medicalScheduleApiUrl = _appSettings.ApiUrls["MasterServiceUrl"] + "/master/medicare_participants/" + masterParticipantId;
            RestClient restClient = new RestClient(medicalScheduleApiUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            var httpResponse = await restClient.GetAsync<ApiResponse<MedicareParticipantDetails>>(medicalScheduleApiUrl);
            if (httpResponse.StatusCode == StatusCodes.Status200OK && httpResponse.Result is not null)
            {
                masterData = httpResponse.Result;
            }

            return masterData;
        }
        
        public async Task<List<MedicalContractorList>> GetMedicalContractors(BaseHttpRequestContext baseHttpRequestContext)
        {
            
            List<MedicareParticipantDetails> listMedicareParticipantData = new();          

            string medicalScheduleApiUrl = _appSettings.ApiUrls["MasterServiceUrl"] + "/master/medicare_participants" + "?f=eyJTdGF0dXNJZCI6WzMwLDMxXX0%3D";
        
            RestClient restClient = new RestClient(medicalScheduleApiUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            var httpResponse = await restClient.GetAsync<ApiResponse<List<MedicareParticipantDetails>>>(medicalScheduleApiUrl);
            if (httpResponse.StatusCode == StatusCodes.Status200OK && httpResponse.Result is not null)
            {
                listMedicareParticipantData = httpResponse.Result;
            }
           
            List<MedicalContractorList> masterMedicalContList = _mapper.Map<List<MedicareParticipantDetails>, List<MedicalContractorList>>(listMedicareParticipantData);

            List<MedicalContractorList> MedConList = await _medicareParticipantDetailsDAL.GetMedContListAsync(baseHttpRequestContext.OrgId);

            masterMedicalContList.ForEach(a => {
                var clientObj = MedConList.Find(f => f.MasterMedicalParticipantsId == a.Id);
                a.MedicalContractorTypeId = (short)MedicalContractorType.Health_Fund;
                if (clientObj is not null)
                {
                    a.Id = clientObj.Id;
                    a.MasterMedicalParticipantsId = clientObj.MasterMedicalParticipantsId;
                    a.ModifiedDate = clientObj.ModifiedDate;
                    MedConList.RemoveAll(obj => obj.Id == clientObj.Id);
                    a.MedicalContractorParentId = clientObj.MedicalContractorParentId;
                }
                else
                {
                    a.MasterMedicalParticipantsId = a.Id;
                    a.Id = null;
                }
            });

            List<MedicalContractorList> ClintMedicalContList = MedConList;

            List<MedicalContractorList> finalMedicalContList = masterMedicalContList.Concat(ClintMedicalContList).OrderByDescending(a => a.ModifiedDate).ToList();
            
            return finalMedicalContList;

        }

        /// <summary>
        /// Method to check if the Medical contractor name is duplicate
        /// </summary>
        /// <param name="search_term"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<ApiResponse<bool>> CheckMedContName(string search_term, BaseHttpRequestContext baseHttpRequestContext)
        {

            bool boolName = false;   
            List<MedicalContractorList> medContList= await GetMedicalContractors(baseHttpRequestContext);

            medContList = medContList.Where(x => x.MedicalContractorTypeId != (short)MedicalContractorType.Internal_Company).ToList();

            MedicalContractorList MedContname =  medContList
                  .Where(p => p.Name.ToLower().Equals(search_term.ToLower())).FirstOrDefault();
            if (MedContname == null)
                boolName = false;
            else
                boolName = true;


            var apiResponse = new ApiResponse<bool>
            {
                Result = boolName,
                Message = "Success",
                StatusCode = StatusCodes.Status200OK
            };
            return apiResponse;
        }


        public async Task<ApiResponse<long?>> AddMCPersonalDetails(int mcId, List<MedContPersonalDetail> mcPersonalDetails, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<long?> apiResposne = new();
            var userId = baseHttpRequestContext.UserId;
            var orgId = baseHttpRequestContext.OrgId;


            foreach (var perDetail in mcPersonalDetails)
            {

                perDetail.OrgId = orgId;
                perDetail.ModifiedBy = userId;
                perDetail.StatusId = (short)Status.Active;
                perDetail.MedicalContractorId = mcId;
            }

            long id = await _medicareParticipantDetailsDAL.AddMCPersonalDetails(mcPersonalDetails);

            if (id > 0)
            {
                apiResposne.StatusCode = StatusCodes.Status200OK;
                apiResposne.Result = id;
                apiResposne.Message = "Success";
            }
            else
            {
                apiResposne.StatusCode = StatusCodes.Status400BadRequest;
                apiResposne.Result = null;
                apiResposne.Message = "Failure";
                apiResposne.Errors.Add("MedicalContractorPersonalDetails cannot be added at this time.Please try again later.");
            }
            return apiResposne;


        }

       public async Task<ApiResponse<List<MedContPersonalDetail>>> GetMedContPersonalDetails(BaseHttpRequestContext baseHttpRequestContext, int id)
        {
            ApiResponse<List<MedContPersonalDetail>> apiResposne = new();
            List<MedContPersonalDetail> mcPersonalDetailsDB = await _medicareParticipantDetailsDAL.GetMedContPersonalDetails(baseHttpRequestContext.OrgId, id);
            if (mcPersonalDetailsDB is null)
            {
                apiResposne.Result = null;
                apiResposne.StatusCode = StatusCodes.Status400BadRequest;
                apiResposne.Message = "Failure";
                apiResposne.Errors.Add("The MedContPersonalDetail doesnot exist.");
                return apiResposne;
            }

           
            apiResposne.Result = mcPersonalDetailsDB;
            apiResposne.StatusCode = StatusCodes.Status200OK;
            apiResposne.Message = "Success";
            return apiResposne;

        }

        public async Task<ApiResponse<int?>> EditMCPersonalDetails(int mcId, List<MedContPersonalDetail> mcPersonalDetails, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<int?> apiResposne = new();
            var userId = baseHttpRequestContext.UserId;
            var orgId = baseHttpRequestContext.OrgId;

            var personalDetailsFromDB = await _medicareParticipantDetailsDAL.GetMCPersonalDetailsByIds(orgId, mcPersonalDetails.Select(x => (int)x.Id).ToList());

            foreach (var action in mcPersonalDetails)
            {
                action.OrgId = orgId;
                action.CreatedDate = personalDetailsFromDB.Find(x => x.Id == action.Id).CreatedDate;
                action.ModifiedBy = userId;
                action.ModifiedDate = DateTime.UtcNow;
            }

            int id = await _medicareParticipantDetailsDAL.EditMCPersonalDetails(mcPersonalDetails);

            if (id > 0)
            {
                apiResposne.StatusCode = StatusCodes.Status200OK;
                apiResposne.Result = id;
                apiResposne.Message = "Success";
            }
            else
            {
                apiResposne.StatusCode = StatusCodes.Status400BadRequest;
                apiResposne.Result = null;
                apiResposne.Message = "Failure";
                apiResposne.Errors.Add("MedicalContractorBandAssoc cannot be Updated at this time.Please try again later.");
            }
            return apiResposne;
        }

        /// <summary>
        /// DeleteMCPersonalDetails
        /// </summary>
        /// <param name="deleteIds"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<int?>> DeleteMCPersonalDetails(int mcid, string deleteIds, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<int?> apiResposne = new();
            var userId = baseHttpRequestContext.UserId;
            var orgId = baseHttpRequestContext.OrgId;

            List<int> mcperDetailsIds = deleteIds.Split(',').ToList().Select(s => int.Parse(s)).ToList();

            var dbAssocs = await _medicareParticipantDetailsDAL.GetMCPersonalDetailsByIds(orgId, mcperDetailsIds);

            foreach (var action in dbAssocs)
            {
                
                action.StatusId = (short)Status.Deleted;
                action.ModifiedBy = userId;
                action.ModifiedDate = DateTime.UtcNow;
            }

            int id = await _medicareParticipantDetailsDAL.EditMCPersonalDetails(dbAssocs);

            if (id > 0)
            {
                apiResposne.StatusCode = StatusCodes.Status200OK;
                apiResposne.Result = id;
                apiResposne.Message = "Success";
            }
            else
            {
                apiResposne.StatusCode = StatusCodes.Status400BadRequest;
                apiResposne.Result = null;
                apiResposne.Message = "Failure";
                apiResposne.Errors.Add("MedicalContractorPersonalDetails cannot be Deleted at this time.Please try again later.");
            }
            return apiResposne;

        }

        private MedicalContractorFilterModel FilterParametersForMc(string filter)
        {
            if (!(String.IsNullOrEmpty(filter)))
            {
                return JsonConvert.DeserializeObject<MedicalContractorFilterModel>(filter);
            }
            else
            {
                return null;
            }
        }

        public async Task<ApiResponse<List<MedicalContractorBandAssoc>>> GetMedContBandAssocFromFilter(BaseHttpRequestContext baseHttpRequestContext, QueryModel queryModel)
        {
            ApiResponse<List<MedicalContractorBandAssoc>> apiResponse = new();
            
           //List<MedicalContractorList> finalContList = await GetMedicalContractors(baseHttpRequestContext);

            if(queryModel is not null && !string.IsNullOrWhiteSpace(queryModel.Filter))
            {
                
                MedicalContractorFilter mcFilterModel = FilterParametersForMcBand(queryModel.Filter);
                if((mcFilterModel.ParticipantId is not null && mcFilterModel.ParticipantId.Count > 0) ||(mcFilterModel.MasterMedicalParticipantsId is not null && mcFilterModel.MasterMedicalParticipantsId.Count > 0))
                {
                    List<int?> medicalContractorIds = await _medicareParticipantDetailsDAL.FetchMedicalContractorIds(mcFilterModel);
                    if(medicalContractorIds is not null && medicalContractorIds.Count > 0)
                    {
                        if (mcFilterModel.MedicalContractorId is null) mcFilterModel.MedicalContractorId = medicalContractorIds;
                        else mcFilterModel.MedicalContractorId.AddRange((IEnumerable<int?>)medicalContractorIds);
                    }
                }
                List<MedicalContractorBandAssoc> lstBanding = await _medicareParticipantDetailsDAL.GetMedContBandAssocFromFilter(queryModel, mcFilterModel, baseHttpRequestContext);
                apiResponse.Result = lstBanding;
                apiResponse.StatusCode = StatusCodes.Status200OK;
                return apiResponse;
            }
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            return apiResponse;
        }



        private MedicalContractorFilter FilterParametersForMcBand(string filter)
        {
            if (!(String.IsNullOrEmpty(filter)))
            {
                return JsonConvert.DeserializeObject<MedicalContractorFilter>(filter);
            }
            else
            {
                return null;
            }
        }

        private MedContBandFilterModel FilterForGetMcBand(string filter)
        {
            if (!(String.IsNullOrEmpty(filter)))
            {
                return JsonConvert.DeserializeObject<MedContBandFilterModel>(filter);
            }
            else
            {
                return null;
            }
        }

        public async Task<ApiResponse<List<MCBandVersionView>>> GetMedContBandVersionBAL(BaseHttpRequestContext baseHttpRequestContext, int mcId)
        {
            ApiResponse<List<MCBandVersionView>> apiResponse = new();
            List<MCBandVersionView> mcVersionsFromDB = await _medicareParticipantDetailsDAL.GetMedContBandVersionDAL(baseHttpRequestContext.OrgId, mcId);
            
            foreach(var mmc in mcVersionsFromDB)
            {
                if (mmc.ParentMasterMedicalParticipantsId is not null)
                {
                    MedicareParticipantDetails masterData = await GetMedicareParticipantsData(mmc.ParentMasterMedicalParticipantsId, baseHttpRequestContext);
                    mmc.MedicalContractorParentName = masterData.ParticipantName;
                    mmc.MedicalContractorParentParticipantsId = masterData.ParticipantId;                  
                }
            }
            
            if (mcVersionsFromDB is null)
            {
                apiResponse.Result = null;
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("The MedicalContractorBandAssocs doesnot exist.");
                return apiResponse;
            }

           
            apiResponse.Result = mcVersionsFromDB;
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            return apiResponse;


        }

        public async Task<ApiResponse<bool>> CheckMCVersionDatesBAL(int id, BaseHttpRequestContext baseHttpRequestContext, DateTime startDate, DateTime? endDate)
        {
            ApiResponse<bool> apiResponse = new();

            bool isValid = await _medicareParticipantDetailsDAL.CheckMCVersionDatesDAL(id, baseHttpRequestContext.OrgId,startDate,endDate);

            apiResponse.Result = isValid;
            apiResponse.Message = "Success";
            apiResponse.StatusCode = StatusCodes.Status200OK;
            
            return apiResponse;

        }

        public async Task<ApiResponse<List<MedicalContractorBandAssoc>>> GetMCBandAssocByPaticipantId(BaseHttpRequestContext baseHttpRequestContext, QueryModel queryModel)
        {
            ApiResponse<List<MedicalContractorBandAssoc>> apiResponse = new();
            EstimateFilter mcEstimateFilter = FilterForEstimateFilter(queryModel.Filter);

            if(mcEstimateFilter.ServiceDate is null)
            {
                apiResponse.Result = null;
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("ParticipantId and ServiceDate are Mandatory Filter to Get Data");
                return apiResponse;
            }

            int? mcId = await GetMedicalContractorId(baseHttpRequestContext, mcEstimateFilter);
            if(mcId == null || mcId == 0)
            {
                apiResponse.Result = null;
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("Medical Contractor For Given ParticipantId Can not be found");
                return apiResponse;
            }

            List<MedicalContractorBandAssoc> mcAssocsFromDB = await _medicareParticipantDetailsDAL.GetMCBandAssocForParticipantId(baseHttpRequestContext.OrgId, (int)mcId, mcEstimateFilter);
            if (mcAssocsFromDB is null)
            {
                apiResponse.Result = null;
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Data does not exists";
                return apiResponse;
            }

            apiResponse.Result = mcAssocsFromDB;
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            return apiResponse;
        }

        private EstimateFilter FilterForEstimateFilter(string filter)
        {
            if (!(String.IsNullOrEmpty(filter)))
            {
                return JsonConvert.DeserializeObject<EstimateFilter>(filter);
            }
            else
            {
                return null;
            }
        }

        public async Task<int?> GetMedicalContractorId(BaseHttpRequestContext baseHttpRequestContext,EstimateFilter mcEstimateFilter)
        {
            int? mcId;

            if(mcEstimateFilter.CompanyDetailsId is not null)
            {
                mcId = await _medicareParticipantDetailsDAL.GetMCIdFromCompanyId((int)mcEstimateFilter.CompanyDetailsId, baseHttpRequestContext.OrgId);
            }
           else  if (mcEstimateFilter.MasterMedicalParticipantsId is null)
            { 
                // Get Medical contractorId from ParticipantId 
                mcId = await _medicareParticipantDetailsDAL.GetMCIdForParticipantId(mcEstimateFilter.ParticipantId, baseHttpRequestContext.OrgId);

               //Get the Medical Contractor from Master then find Id in Client 
                 if(mcId == null || mcId == 0)
                 {
                     List<MedicareParticipantsInfo> masterData = new();

                     string medicalScheduleApiUrl = _appSettings.ApiUrls["MasterServiceUrl"] + "/master/medicare_participants/participantId?ParticipantIds=" + mcEstimateFilter.ParticipantId.ToString(); 
                     RestClient restClient = new RestClient(medicalScheduleApiUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
                     var httpResponse = await restClient.GetAsync<ApiResponse<List<MedicareParticipantsInfo>>>(medicalScheduleApiUrl);
                     if (httpResponse.StatusCode == StatusCodes.Status200OK && httpResponse.Result is not null)
                     {
                         masterData = httpResponse.Result;
                     }
                     if (masterData.Count > 0)
                     {
                         mcId = await _medicareParticipantDetailsDAL.GetMCIdFromMasterMedPartId(masterData[0].Id, baseHttpRequestContext.OrgId);
                     }

                 }
            }
            else
            {
                //Get Medical Contractor from MasterMedicalParticipantId : As this is the Master participantId
                mcId = await _medicareParticipantDetailsDAL.GetMCIdFromMasterMedPartId((int)mcEstimateFilter.MasterMedicalParticipantsId, baseHttpRequestContext.OrgId);
            }


            return mcId;
        }

        public async  Task<ApiResponse<List<Shared.Models.Entities.MedicalContractorBandAssocInfo>>> ListMCBandAssocByIds(BaseHttpRequestContext baseHttpRequestContext, string ids)
        {
            ApiResponse<List<MedicalContractorBandAssocInfo>> apiResponse = new();


            if (!string.IsNullOrWhiteSpace(ids))
            
            {
                List<int> lstIds = ids.Split(',').ToList().Select(s => int.Parse(s)).ToList();
                if(lstIds is not null)
                {
                    List<MedicalContractorBandAssoc> lstBandAssoc = await _medicareParticipantDetailsDAL.GetMCBandAssocByMultipleIds(baseHttpRequestContext.OrgId, lstIds);
                    if (lstBandAssoc is not null && lstBandAssoc.Count > 0)
                    {
                        List<MedicalContractorBandAssocInfo> lstBandAssocInfo = _mapper.Map<List<MedicalContractorBandAssoc>, List<MedicalContractorBandAssocInfo>>(lstBandAssoc);
                        apiResponse.StatusCode = StatusCodes.Status200OK;
                        apiResponse.Result = lstBandAssocInfo;
                        return apiResponse;
                    }
                }

                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Error while getting MedicalContractorBandAssoc");


            }
            return apiResponse;
        }

        /// <summary>
        /// Method to check if the entered CompanyId is unique
        /// </summary>
        /// <param name="search_term"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<ApiResponse<bool>> CheckMedContCompany(int search_id, BaseHttpRequestContext baseHttpRequestContext)
        {
            bool nameBool = await _medicareParticipantDetailsDAL.CheckMedContCompanyDAL(search_id, baseHttpRequestContext.OrgId);
            var apiResponse = new ApiResponse<bool>
            {
                Result = nameBool,
                Message = "Success",
                StatusCode = StatusCodes.Status200OK
            };
            return apiResponse;
        }

        public async Task<ApiResponse<MedicalContractorInfo>> GetMedicalContractorAddress(string strParticipantId, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<MedicalContractorInfo> apiResponse = new();
            MedicalContractorInfo medicalContractorInfo = new();

            if (string.IsNullOrWhiteSpace(strParticipantId))
            {
                apiResponse.Result = null;
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("ParticipantId Filter is mandatory.");
                return apiResponse;

            }


            MedicalContractorFilter mcFilterModel = new()
            {
                ParticipantId = new List<string> { strParticipantId}
            };
            List<MedicalContractor> medicalContractors = await _medicareParticipantDetailsDAL.FetchMedicalContractors(mcFilterModel);

            int? mcId = null;
            //only 1 participantid

            if (medicalContractors is null || medicalContractors.Count!= mcFilterModel.ParticipantId.Count)
            {
                // mcId = await GetMedicalContractorId(baseHttpRequestContext, mcFilterModel);

                List<MedicareParticipantsInfo> masterData = new();

                string medicalScheduleApiUrl = _appSettings.ApiUrls["MasterServiceUrl"] + "/master/medicare_participants/participantId?ParticipantIds=" + mcFilterModel.ParticipantId[0].ToString();
                RestClient restClient = new RestClient(medicalScheduleApiUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
                var httpResponse = await restClient.GetAsync<ApiResponse<List<MedicareParticipantsInfo>>>(medicalScheduleApiUrl);
                if (httpResponse.StatusCode == StatusCodes.Status200OK && httpResponse.Result is not null)
                {
                    masterData = httpResponse.Result;
                }
                if (masterData.Count > 0)
                {
                    mcId = await _medicareParticipantDetailsDAL.GetMCIdFromMasterMedPartId(masterData[0].Id, baseHttpRequestContext.OrgId);
                    medicalContractorInfo.Name = masterData[0].ParticipantName;
                }
                if (mcId == null || mcId == 0)
                {
                    apiResponse.Result = null;
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Message = "Failure";
                    apiResponse.Errors.Add("Medical Contractor For Given ParticipantId Can not be found");
                    return apiResponse;
                }
            }
            else
            {
                mcId = medicalContractors[0].Id;
                medicalContractorInfo.Name = medicalContractors[0].Name;



            }
            if(mcId != null)
            {
                MedicalContractorAddressInfo medicalContractorAddress = await _medicareParticipantDetailsDAL.FetchMCPostalAddress(mcId, baseHttpRequestContext.OrgId);
                medicalContractorInfo.ParticipantId = mcFilterModel.ParticipantId[0];

                medicalContractorInfo.MedicalContractorAddressInfo = medicalContractorAddress;
               
                apiResponse.Result = medicalContractorInfo;
                apiResponse.StatusCode = StatusCodes.Status200OK;
                return apiResponse;
            }
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            apiResponse.Message = "Failure";
            apiResponse.Errors.Add("Medical Contractor For Given ParticipantId Can not be found");
            return apiResponse;
        }
    }
}
