﻿using AutoMapper;
using Capstone2.Framework.RestApi;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.Utility.Interfaces;
using Capstone2.RestServices.Utility.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Transactions;
using System.Web;

namespace Capstone2.RestServices.Utility.Services
{
    public class ActivityBAL : IActivityBAL
    {
        private readonly IActivityDAL _activityDAL;
        public readonly AppSettings _appSettings;

        public ActivityBAL(IActivityDAL activityDAL, IMapper mapper, IOptions<AppSettings> appSettings)
        {
            _activityDAL = activityDAL;
            _appSettings = appSettings.Value;
        }
        /// <summary>
        /// Method to create a new category
        /// </summary>
        /// <param name="inputCategory"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> AddCategoryBAL(Category inputCategory, BaseHttpRequestContext baseHttpRequestContext)
        {
            inputCategory.OrgId = baseHttpRequestContext.OrgId;
            inputCategory.StatusId = (short)Status.Active;
            inputCategory.CreatedBy = baseHttpRequestContext.UserId;
            inputCategory.CreatedDate = DateTime.UtcNow;
            ApiResponse<long?> apiResponse = new();
            if (!await _activityDAL.CheckCategoryName(inputCategory.Name, inputCategory.OrgId))
            {

                long id = await _activityDAL.AddCategoryDAL(inputCategory);

                if (id > 0)
                {
                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    apiResponse.Result = id;
                    apiResponse.Message = "Success";

                }
                else
                {
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Result = null;
                    apiResponse.Message = "Failure";
                    apiResponse.Errors.Add("Category cannot be added at this time.Please try again later.");
                }
                return apiResponse;
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = null;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("Category Name already exists.");
            }
            return apiResponse;
        }

        /// <summary>
        /// Method to fetch category
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<Category>> GetCategoryBAL(BaseHttpRequestContext baseHttpRequestContext, long id)
        {
            int orgId = baseHttpRequestContext.OrgId;
            ApiResponse<Category> apiResponse = new();
            Category categoryFromDB = await _activityDAL.GetCategoryDAL(orgId, id);
            if (categoryFromDB is null)
            {
                apiResponse.Result = null;
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("The Category doesnot exist.");
                return apiResponse;
            }

            apiResponse.Result = categoryFromDB;
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            return apiResponse;
        }

        /// <summary>
        /// Method to update a category
        /// </summary>
        /// <param name="id"></param>
        /// <param name="inputCategory"></param>
        /// <returns></returns>
        public async Task<ApiResponse<string>> EditCategoryBAL(long id, Category inputCategory, BaseHttpRequestContext baseHttpRequestContext)
        {
            int orgId = baseHttpRequestContext.OrgId;
            long userId = baseHttpRequestContext.UserId;
            ApiResponse<string> apiResponse = new();
            Category categoryFromDb = await _activityDAL.GetCategoryDAL(orgId, id);
            if (categoryFromDb is null)
            {
                apiResponse.Result = null;
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("The Category doesnot exist.");
                return apiResponse;
            }
            inputCategory.Id = categoryFromDb.Id;
            inputCategory.OrgId = orgId;
            inputCategory.ModifiedDate = DateTime.UtcNow;
            inputCategory.ModifiedBy = userId;
            inputCategory.CreatedBy = categoryFromDb.CreatedBy;
            inputCategory.CreatedDate = categoryFromDb.CreatedDate;
            inputCategory.StatusId = categoryFromDb.StatusId;
            int row = await _activityDAL.EditCategoryDAL(inputCategory);

            apiResponse.Result = "Success";
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";

            return apiResponse;
        }

        /// <summary>
        /// Method to save an entry in Activity
        /// </summary>
        /// <param name="inputActivity"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> AddActivityBAL(Activity inputActivity, BaseHttpRequestContext baseHttpRequestContext)
        {
            int orgId = baseHttpRequestContext.OrgId;
            long loggedInUser = baseHttpRequestContext.UserId;
            ApiResponse<long?> apiResponse = new();
            using (var transaction = new TransactionScope(TransactionScopeOption.Required, TransactionScopeAsyncFlowOption.Enabled))
            {
                if (inputActivity != null && (inputActivity.ActivityUserAssocs != null))
                {
                    if (inputActivity.ActivityUserAssocs.Any())
                    {
                        inputActivity.ActivityUserAssocs.ToList().ForEach(a => { a.OrgId = orgId; a.ModifiedBy = loggedInUser; a.StatusId = (short)Status.Active; a.CreatedDate = DateTime.UtcNow; });
                    } 
                }
                inputActivity.CreatedBy = loggedInUser;
                inputActivity.OrgId = orgId;
                inputActivity.StatusId = (short)Status.Active;
                inputActivity.CreatedDate = DateTime.UtcNow;

                long id = await _activityDAL.AddActivityDAL(inputActivity);
                if (id > 0)
                {
                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    apiResponse.Message = "Success";
                    apiResponse.Result = id;
                }
                else
                {
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Message = "Failure";
                    apiResponse.Result = null;
                }
                transaction.Complete();
                transaction.Dispose();
            }
            return apiResponse;
        }

        /// <summary>
        /// Method to fetch Activity details
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<InputActivityDetail>> GetActivityBAL(BaseHttpRequestContext baseHttpRequestContext, long id)
        {
            int orgId = baseHttpRequestContext.OrgId;
            ApiResponse<InputActivityDetail> apiResposne = new();
            InputActivityDetail activityFromDB = await _activityDAL.FetchActivityDetails(orgId, id);
            if (activityFromDB is null)
            {
                apiResposne.Result = null;
                apiResposne.StatusCode = StatusCodes.Status400BadRequest;
                apiResposne.Message = "Failure";
                apiResposne.Errors.Add("The Activity doesnot exist.");
                return apiResposne;
            }

            else
            {
                if (activityFromDB is not null && activityFromDB.ActivityUserAssocs is not null && activityFromDB.ActivityUserAssocs.Any())
                    activityFromDB.ActivityUserAssocs = await FormatUserDetails(activityFromDB.ActivityUserAssocs, baseHttpRequestContext);
                apiResposne.Result = activityFromDB;
                apiResposne.StatusCode = StatusCodes.Status200OK;
                apiResposne.Message = "Success";
                return apiResposne;
            }
        }
        /// <summary>
        /// Method to fetch the profile pic for assigned users
        /// </summary>
        /// <param name="inputSnippetUserAssocs"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        private async Task<ICollection<InputActivityUserAssoc>> FormatUserDetails(ICollection<InputActivityUserAssoc> inputActivityUserAssocs, BaseHttpRequestContext baseHttpRequestContext)
        {
            foreach (var activityUser in inputActivityUserAssocs)
            {
                if (activityUser.UserDetails != null)
                {
                    if (activityUser.UserDetails.PhotoFileDetailsId != null)
                    {
                        long fileId = (long)activityUser.UserDetails.PhotoFileDetailsId;
                        var token = baseHttpRequestContext.BearerToken;
                        string interServiceToken = baseHttpRequestContext.InterServiceToken;
                        string fileAPiUrl = _appSettings.ApiUrls["FileServiceUrl"] + "/file/file_details/" + fileId;
                        RestClient restClient = new RestClient(fileAPiUrl, null, token, interServiceToken);
                        var fileApiResponse = await restClient.GetAsync<ApiResponse<FileDetailsOutputForId>>(fileAPiUrl, null);
                        if (fileApiResponse.StatusCode == StatusCodes.Status200OK)
                        {
                            activityUser.UserDetails.FileDetailsOutput = fileApiResponse.Result;
                        }
                    }

                    activityUser.CreatedDate = DateTime.Parse(activityUser.CreatedDate.ToString("u"));
                }
            }
            return inputActivityUserAssocs;
        }
        /// <summary>
        /// Method to update an Activity
        /// </summary>
        /// <param name="id"></param>
        /// <param name="inputActivity"></param>
        /// <param name="orgId"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task<ApiResponse<string>> EditActivityBAL(long id, Activity inputActivity, BaseHttpRequestContext baseHttpRequestContext)
        {
            int orgId = baseHttpRequestContext.OrgId;
            long userId = baseHttpRequestContext.UserId;
            ApiResponse<string> apiResponse = new();
            Activity activityFromDb = await _activityDAL.GetActivityDAL(orgId, id);

            inputActivity.OrgId = orgId;
            inputActivity.ModifiedDate = DateTime.UtcNow;
            inputActivity.ModifiedBy = userId;
            inputActivity.CreatedBy = activityFromDb.CreatedBy;
            inputActivity.CreatedDate = activityFromDb.CreatedDate;
            inputActivity.Id = activityFromDb.Id;
            if (inputActivity.StatusId == (short)Status.Inactive && activityFromDb.StatusId == (short)Status.Active)
            {
                inputActivity.CategoryId = null;
            }
            if (inputActivity != null && inputActivity.ActivityUserAssocs.Any())
            {
                inputActivity.ActivityUserAssocs.ToList().ForEach(a => { a.OrgId = orgId; a.ActivityId = id; });
            }

            var removeUserList = await _activityDAL.GetActivityUserAssocs(orgId, id);
            var inputUserList = inputActivity.ActivityUserAssocs;


            inputActivity.ActivityUserAssocs = null;

            using (var transaction = new TransactionScope(TransactionScopeOption.Required, TransactionScopeAsyncFlowOption.Enabled))
            {
                List<ActivityUserAssoc> userList = EditActivityUserAssocs(removeUserList, inputUserList, userId, activityFromDb.CreatedBy);
                inputActivity.ActivityUserAssocs = userList;

                await _activityDAL.EditActivityDAL(inputActivity);

                apiResponse.Result = "Success";
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";

                transaction.Complete();
                transaction.Dispose();
            }

            return apiResponse;
        }

        private List<ActivityUserAssoc> EditActivityUserAssocs(ICollection<ActivityUserAssoc> removeUserList, ICollection<ActivityUserAssoc> inputUserList, long userId, long? createdBy)
        {
            List<ActivityUserAssoc> addUserList = new();

            foreach (var userAssoc in inputUserList)
            {
                if (userAssoc.Id > 0)
                {
                    var existingobj = removeUserList.FirstOrDefault(x => x.Id == userAssoc.Id);
                    removeUserList.Remove(existingobj);
                }
                else
                {
                    ActivityUserAssoc userAssocDB = removeUserList.Where(x => x.UserDetailsId == userAssoc.UserDetailsId).FirstOrDefault();
                    if (userAssocDB is null)
                    {
                        userAssoc.CreatedDate = DateTime.UtcNow;
                        userAssoc.StatusId = (short)Status.Active;
                        addUserList.Add(userAssoc);
                    }
                    else
                    {
                        if (userAssocDB.StatusId == (short)Status.Deleted)
                        {
                            userAssocDB.StatusId = (short)Status.Active;
                            userAssocDB.ModifiedDate = DateTime.UtcNow;
                            userAssocDB.ModifiedBy = userId;
                            addUserList.Add(userAssocDB);

                        }
                        removeUserList.Remove(userAssocDB);

                    }


                }
            }

            foreach (var userAssoc in removeUserList.Where(x => x.StatusId == (short)Status.Active))
            {
                userAssoc.StatusId = (short)Status.Deleted;
                userAssoc.ModifiedDate = DateTime.UtcNow;
                userAssoc.ModifiedBy = userId;
            }
            addUserList = addUserList.Concat(removeUserList).ToList();

            return addUserList;
        }

        /// <summary>
        /// Method to Fetch all categories
        /// </summary>
        /// <param name="st"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<ApiResponse<QueryResultList<ListCategory>>> GetCategories(QueryModel queryModel, BaseHttpRequestContext baseHttpRequestContext)
        {
            ActivityFilterModel filterModel = PrepareFilterParameters(queryModel.Filter);
            var categories = await _activityDAL.GetCategories(queryModel, filterModel, baseHttpRequestContext.OrgId);
            var apiResponse = new ApiResponse<QueryResultList<ListCategory>>
            {
                Result = categories,
                Message = "Success",
                StatusCode = StatusCodes.Status200OK

            };
            return apiResponse;

        }

        /// <summary>
        /// Method to check if the entered category name is unique
        /// </summary>
        /// <param name="search_term"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<ApiResponse<bool>> CheckCategoryName(string search_term, int orgId)
        {
            bool nameBool = await _activityDAL.CheckCategoryName(search_term, orgId);
            var apiResponse = new ApiResponse<bool>
            {
                Result = nameBool,
                Message = "Success",
                StatusCode = StatusCodes.Status200OK

            };
            return apiResponse;
        }

        /// <summary>
        /// Method to generate a list of Activities
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        public async Task<ApiResponse<QueryResultList<ListActivity>>> ListActivitiesBAL(BaseHttpRequestContext baseHttpRequestContext, QueryModel queryModel)
        {
            ApiResponse<QueryResultList<ListActivity>> apiResponse = new();
            int orgId = baseHttpRequestContext.OrgId;
            long loggedInUser = baseHttpRequestContext.UserId;
            ActivityFilterModel filterModel = PrepareFilterParameters(queryModel.Filter);
            List<ListActivity> activityQueryList = await _activityDAL.ListActivitiesDAL(orgId, queryModel, filterModel);
            //List<ListActivity> paginatedList = await CreatePaginatedListAsync(activityQuery, queryModel);
            QueryResultList<ListActivity> queryList = new QueryResultList<ListActivity>();
            List<long> ListFileDetailsId = new();
            List<FileDetailsOutputForId> fileResponse = new();
            if (activityQueryList != null)
            {

                queryList.ItemRecords = activityQueryList;
                queryList.CurrentCount = activityQueryList.Count();

                foreach (var item in activityQueryList)
                {
                    foreach (var userassoc in item.ActivityUserAssocs)
                    {
                        var fileid = userassoc.UserDetails.PhotoFileDetailsId;
                        if (fileid != null)
                        {
                            ListFileDetailsId.Add((long)fileid);
                        }
                    }

                }
                if (ListFileDetailsId.Count > 0)
                {

                    string stringFileDetailsId = string.Join(",", ListFileDetailsId);
                    var token = baseHttpRequestContext.BearerToken;
                    string interServiceToken = baseHttpRequestContext.InterServiceToken;
                    string fileAPiUrl = _appSettings.ApiUrls["FileServiceUrl"] + "/file/file_details_list?" + "stringFileDetailsId" + "=" + HttpUtility.UrlEncode(stringFileDetailsId);
                    RestClient restClient = new RestClient(fileAPiUrl, null, token, interServiceToken);
                    var fileApiResponse = await restClient.GetAsync<ApiResponse<List<FileDetailsOutputForId>>>(fileAPiUrl, null);
                    if (fileApiResponse.StatusCode == StatusCodes.Status200OK)
                    {
                        fileResponse = fileApiResponse.Result;
                    }
                    else
                    {
                        apiResponse.StatusCode = fileApiResponse.StatusCode;
                        apiResponse.Errors = fileApiResponse.Errors;
                        apiResponse.Message = fileApiResponse.Message;
                        return apiResponse;
                    }

                    foreach (var item in activityQueryList)
                    {
                        foreach (var userassoc in item.ActivityUserAssocs)
                        {
                            var fileid = userassoc.UserDetails.PhotoFileDetailsId;
                            if (fileid != null)
                            {
                                var filedata = fileResponse.Find(x => x.FileDetail.Id == fileid);
                                userassoc.UserDetails.FileDetailsOutput = filedata;
                            }
                        }
                    }
                }

            }
            else
            {
                queryList.ItemRecords = null;
                queryList.CurrentCount = 0;

            }
            queryList.TotalCount = activityQueryList.Count;
            //queryList.PageNumber = queryModel.PageNumber;
            //queryList.PageSize = queryModel.PageSize;
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result = queryList;
            return apiResponse;
        }

        /// <summary>
        /// method to generate the filter parameters from the request
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        private ActivityFilterModel PrepareFilterParameters(string filter)
        {
            if (!(String.IsNullOrEmpty(filter)))
            {
                return JsonConvert.DeserializeObject<ActivityFilterModel>(filter);
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// Method to delete a category
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> DeleteCategoryBAL(BaseHttpRequestContext baseHttpRequestContext, int id, DeleteObject deleteObject)
        {
            int orgId = baseHttpRequestContext.OrgId;
            long loggedInUser = baseHttpRequestContext.UserId;
            ApiResponse<long?> apiResponse = new();

            if (string.IsNullOrEmpty(deleteObject.DeleteReason))
            {
                apiResponse.Errors.Add("Can not Delete the category without Delete Reason");
                apiResponse.Message = "Failure";
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                return apiResponse;
            }
            if (await _activityDAL.GetActivityCount(id) == 0)
            {

                Category category = await _activityDAL.GetCategoryDAL(orgId, id);
                if (category is not null)
                {
                    category.StatusId = (short)Status.Deleted;
                    category.ModifiedBy = loggedInUser;
                    category.ModifiedDate = DateTime.UtcNow;
                    category.DeleteReason = deleteObject.DeleteReason;
                    category.Id = id;
                    int rows = await _activityDAL.EditCategoryDAL(category);

                    if (rows > 0)
                    {
                        apiResponse.Result = id;
                        apiResponse.Message = "Success";
                        apiResponse.StatusCode = StatusCodes.Status200OK;
                        return apiResponse;
                    }
                    else
                    {
                        apiResponse.Errors.Add("Category cannot be deleted at this time.");
                        apiResponse.Message = "Failure";
                        apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                        return apiResponse;
                    }

                }
            }
            apiResponse.Errors.Add("Category cannot be deleted with active activities");
            apiResponse.Message = "Failure";
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            return apiResponse;
        }
    }
}
