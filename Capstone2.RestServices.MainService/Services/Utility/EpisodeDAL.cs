﻿using Capstone2.RestServices.Utility.Context;
using Capstone2.RestServices.Utility.Interfaces;
using Capstone2.RestServices.Utility.Models;
using Capstone2.Shared.Models.Common;
using System.Threading.Tasks;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using Capstone2.Shared.Models.Enum;
using System;
using System.Collections.Generic;
using Microsoft.Data.SqlClient;
using AutoMapper;
using System.Data;
using System.Data.Common;
using Newtonsoft.Json;
using Capstone2.Shared.Models.Entities;

namespace Capstone2.RestServices.Utility.Services
{
    public class EpisodeDAL : IEpisodeDAL
    {
        public readonly ReadOnlyUtilityDBContext _readOnlyDbContext;
        public readonly UpdatableUtilityDBContext _updatableDBContext;
        public IMapper _mapper;

        public EpisodeDAL(ReadOnlyUtilityDBContext readOnlyDbContext, UpdatableUtilityDBContext updatableDBContext, IMapper mapper)
        {
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
            _mapper = mapper;
        }
        /// <summary>
        /// Method to save a new episode category in db
        /// </summary>
        /// <param name="episodeCategory"></param>
        /// <returns></returns>
        public async Task<short> AddEpisodeCategory(EpisodeCategory episodeCategory)
        {
            await _updatableDBContext.EpisodeCategories.AddAsync(episodeCategory);
            int rows = await _updatableDBContext.SaveChangesAsync();
            if (rows > 0)
            {
                return episodeCategory.Id;
            }
            return default(short);
        }
        /// <summary>
        /// Fetch episidecategory from db 
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<EpisodeCategory> GetEpisodeCategoryFromId(int orgId, short id)
        {
            return await _readOnlyDbContext.EpisodeCategories.Where(x => x.Id == id && x.OrgId == orgId).AsNoTracking().FirstOrDefaultAsync();
        }

        /// <summary>
        /// Method to retireve list of episode categories
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="queryModel"></param>
        /// <returns></returns>

        public async Task<QueryResultList<EpisodeCategory>> ListEpisodeCategories(int orgId, QueryModel queryModel, EpisodeCategoryFilterModel filterModel)
        {
            IQueryable<EpisodeCategory> episodeCategoryQuery = from EC in _readOnlyDbContext.EpisodeCategories
                                                               where EC.OrgId == orgId && EC.StatusId == (short)Status.Active
                                                               select EC;
            if (!(string.IsNullOrWhiteSpace(queryModel.SearchTerm)))
            {
                episodeCategoryQuery = SearchEpisodeCategories(episodeCategoryQuery, queryModel.SearchTerm);

            }
            if (filterModel != null)
            {
                if (filterModel.StatusId != null)
                {
                    episodeCategoryQuery = episodeCategoryQuery.Where(x => filterModel.StatusId.Contains(x.StatusId));
                }
                else
                {
                    episodeCategoryQuery = episodeCategoryQuery.Where(x => x.StatusId == (short)Status.Active);
                }
                if (filterModel.EpisodeTypeId is not null && filterModel.EpisodeTypeId.Any())
                {
                    episodeCategoryQuery = episodeCategoryQuery.Where(x => filterModel.EpisodeTypeId.Contains(x.EpisodeTypeId));
                }
                if (filterModel.Level != null && filterModel.Level.Any())
                {
                    episodeCategoryQuery = episodeCategoryQuery.Where(x => filterModel.Level.Contains(x.Level));
                }

                if (filterModel.ParentEpisodeCategoriesId != null && filterModel.ParentEpisodeCategoriesId.Any())
                {
                    episodeCategoryQuery = episodeCategoryQuery.Where(x => filterModel.ParentEpisodeCategoriesId.Contains(x.ParentEpisodeCategoriesId)); ;
                }

            }
            if (!(string.IsNullOrWhiteSpace(queryModel.SortOrder)) && !(string.IsNullOrWhiteSpace(queryModel.SortTerm)))
            {
                episodeCategoryQuery = SortEpisodeCategories(episodeCategoryQuery, queryModel.SortTerm, queryModel.SortOrder);
            }
            QueryResultList<EpisodeCategory> paginatedList = await PaginatedResultList(episodeCategoryQuery, queryModel);

            return paginatedList;
        }

        private IQueryable<EpisodeCategory> SearchEpisodeCategories(IQueryable<EpisodeCategory> episodeCategoryQuery, string searchTerm)
        {
            return episodeCategoryQuery.Where(s => s.Name.Contains(searchTerm));
        }

        /// <summary>
        /// Method to update Episode Category
        /// </summary>
        /// <param name="episodeCategoryDB"></param>
        /// <returns></returns>
        public async Task<int> UpdateEpisodeCategory(EpisodeCategory episodeCategory)
        {
            _updatableDBContext.EpisodeCategories.Update(episodeCategory);
            return await _updatableDBContext.SaveChangesAsync();
        }

        private async Task<QueryResultList<EpisodeCategory>> PaginatedResultList(IQueryable<EpisodeCategory> episodeCategoryQuery, QueryModel queryModel)
        {
            QueryResultList<EpisodeCategory> queryList = new QueryResultList<EpisodeCategory>();
            List<EpisodeCategory> paginatedList = null;
            if (queryModel.PageNumber > 0 && queryModel.PageSize > 0)
            {
                if (episodeCategoryQuery.Any())
                {
                    paginatedList = await episodeCategoryQuery.Skip((queryModel.PageNumber - 1) * queryModel.PageSize)
                 .Take(queryModel.PageSize).ToListAsync();
                }
            }

            if (paginatedList != null)
            {
                queryList.ItemRecords = paginatedList;
                queryList.PageNumber = queryModel.PageNumber;
                queryList.PageSize = queryModel.PageSize;
                queryList.CurrentCount = paginatedList.Count();
            }
            else
            {
                queryList.ItemRecords = null;
                queryList.CurrentCount = 0;
            }
            queryList.TotalCount = episodeCategoryQuery.Count();
            queryList.PageNumber = queryModel.PageNumber;
            queryList.PageSize = queryModel.PageSize;

            return queryList;
        }

        private IQueryable<EpisodeCategory> SortEpisodeCategories(IQueryable<EpisodeCategory> episodeCategoryQuery, string sortTerm, string sortOrder)
        {
            switch (sortTerm.ToLower())
            {

                case "id":

                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            episodeCategoryQuery = episodeCategoryQuery.OrderBy(x => x.Id);
                        }
                        else
                        {

                            episodeCategoryQuery = episodeCategoryQuery.OrderByDescending(x => x.Id);

                        }
                        break;
                    }
                case "name":

                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            episodeCategoryQuery = episodeCategoryQuery.OrderBy(x => x.Name);
                        }
                        else
                        {

                            episodeCategoryQuery = episodeCategoryQuery.OrderByDescending(x => x.Name);

                        }
                        break;
                    }
                default:
                    episodeCategoryQuery = episodeCategoryQuery.OrderByDescending(x => x.Id);

                    break;
            }
            return episodeCategoryQuery;

        }

        public async Task<List<EpisodeCategory>> GetChildEpisodeCategories(int orgId, short parentId)
        {
            List<EpisodeCategory> lstCategory = new();
            if (await _readOnlyDbContext.EpisodeCategories.Where(x => x.OrgId == orgId && x.ParentEpisodeCategoriesId == parentId && x.StatusId == (short)Status.Active).AsNoTracking().AnyAsync())
            {
                List<EpisodeCategory> lstChild = await _readOnlyDbContext.EpisodeCategories.Where(x => x.OrgId == orgId && x.ParentEpisodeCategoriesId == parentId && x.StatusId == (short)Status.Active).AsNoTracking().ToListAsync();
                lstCategory.AddRange(lstChild);
                foreach (EpisodeCategory category in lstChild)
                {
                    lstCategory.AddRange(await GetChildEpisodeCategories(orgId, category.Id));

                }
            }

            return lstCategory;
        }
        /// <summary>
        /// Method to update list of episode categories
        /// </summary>
        /// <param name="lstCategories"></param>
        /// <returns></returns>
        public async Task<int> UpdateEpisodeCategoryRange(List<EpisodeCategory> lstCategories)
        {
            _updatableDBContext.EpisodeCategories.UpdateRange(lstCategories);
            return await _updatableDBContext.SaveChangesAsync();
        }
        /// <summary>
        /// Method to add anew episode item
        /// </summary>
        /// <param name="episodeItemDetail"></param>
        /// <returns></returns>
        public async Task<int> AddEpisodeItem(EpisodeItemDetail episodeItemDetail)
        {
            await _updatableDBContext.EpisodeItemDetails.AddAsync(episodeItemDetail);
            int rows = await _updatableDBContext.SaveChangesAsync();
            if (rows > 0)
            {
                return episodeItemDetail.Id;
            }
            return default(int);
        }
        /// <summary>
        /// Method to fetch the next value for a sequence
        /// </summary>
        /// <param name="sequenceName"></param>
        /// <returns></returns>
        public async Task<int> GetNextVal(string sequenceName)
        {
            var p = new SqlParameter("@result", System.Data.SqlDbType.Int);
            p.Direction = System.Data.ParameterDirection.Output;
            await _updatableDBContext.Database.ExecuteSqlRawAsync("set @result = next value for " + sequenceName, p);
            return (int)p.Value;
        }
        /// <summary>
        /// Method to fetch episode items based on level 1 category ids
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="lstLevel1Ids"></param>
        /// <returns></returns>
        public async Task<List<EpisodeItemDetail>> FetchEpisodeItemsFromLvl1Ids(int orgId, List<short> lstLevel1Ids)
        {
            return await _readOnlyDbContext.EpisodeItemDetails.Where(x => lstLevel1Ids.Contains((short)x.EpisodeCateogoriesIdLvl1)).AsNoTracking().ToListAsync();
        }
        /// <summary>
        /// Method to fetch episode items based on level 2 category ids
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="lstLevel2Ids"></param>
        /// <returns></returns>
        public async Task<List<EpisodeItemDetail>> FetchEpisodeItemsFromLvl2Ids(int orgId, List<short> lstLevel2Ids)
        {
            return await _readOnlyDbContext.EpisodeItemDetails.Where(x => lstLevel2Ids.Contains((short)x.EpisodeCateogoriesIdLvl2)).AsNoTracking().ToListAsync();
        }
        /// <summary>
        /// Method to fetch episode items based on level 3 category ids
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="lstLevel3Ids"></param>
        /// <returns></returns>
        public async Task<List<EpisodeItemDetail>> FetchEpisodeItemsFromLvl3Ids(int orgId, List<short> lstLevel3Ids)
        {
            return await _readOnlyDbContext.EpisodeItemDetails.Where(x => lstLevel3Ids.Contains((short)x.EpisodeCateogoriesIdLvl3)).AsNoTracking().ToListAsync();
        }

        public async Task<int> UpdateEpisodeItemDetailsRange(List<EpisodeItemDetail> lstLev1Lev2Lev3Items)
        {
            _updatableDBContext.EpisodeItemDetails.UpdateRange(lstLev1Lev2Lev3Items);
            return await _updatableDBContext.SaveChangesAsync();
        }
        /// <summary>
        /// Method to list Episode Items
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="queryModel"></param>
        /// <param name="filterModel"></param>
        /// <returns></returns>
        public async Task<QueryResultList<ListEpisodeItemDetail>> ListEpisodeItemDetails(int orgId, QueryModel queryModel, EpisodeItemFilterModel filterModel)
        {
            IQueryable<ListEpisodeItemDetail> episodeItemQuery = from EIT in _readOnlyDbContext.EpisodeItemDetails
                                                                 where EIT.OrgId == orgId //&& EIT.StatusId == (short)Status.Active
                                                                                          //join EC1 in _readOnlyDbContext.EpisodeCategories on EIT.EpisodeCateogoriesIdLvl1 equals EC1.Id into Lvl1
                                                                                          //from EC1 in Lvl1.DefaultIfEmpty()
                                                                                          //join EC2 in _readOnlyDbContext.EpisodeCategories on EIT.EpisodeCateogoriesIdLvl2 equals EC2.Id into Lvl2
                                                                                          //from EC2 in Lvl2.DefaultIfEmpty()
                                                                                          //join EC3 in _readOnlyDbContext.EpisodeCategories on EIT.EpisodeCateogoriesIdLvl3 equals EC3.Id into Lvl3
                                                                                          //from EC3 in Lvl3.DefaultIfEmpty()
                                                                 select new ListEpisodeItemDetail
                                                                 {
                                                                     Id = EIT.Id,
                                                                     ItemNumber = EIT.ItemNumber,
                                                                     OrgId = EIT.OrgId,
                                                                     EpisodeCateogoriesIdLvl1 = EIT.EpisodeCateogoriesIdLvl1,
                                                                     //EpisodeCateogoryLvl1 = (EC1 == null)? null: EC1.Name,
                                                                     EpisodeCateogoriesIdLvl2 = EIT.EpisodeCateogoriesIdLvl2,
                                                                     //EpisodeCateogoryLvl2 = (EC2 == null) ? null : EC2.Name,
                                                                     EpisodeCateogoriesIdLvl3 = EIT.EpisodeCateogoriesIdLvl3,
                                                                     //EpisodeCateogoryLvl3 = (EC3 == null) ? null : EC3.Name,
                                                                     PrivateFee = EIT.PrivateFee,
                                                                     Description = EIT.Description,
                                                                     StatusId = EIT.StatusId,
                                                                     EpisodeTypeId = EIT.EpisodeTypeId
                                                                 };
            if (!(string.IsNullOrWhiteSpace(queryModel.SearchTerm)))
            {
                episodeItemQuery = SearchEpisodeItems(episodeItemQuery, queryModel.SearchTerm);

            }
            if (filterModel != null)
            {
                if (filterModel.ItemId != null && filterModel.ItemId.Any())
                {
                    episodeItemQuery = episodeItemQuery.Where(x => filterModel.ItemId.Contains(x.Id));
                }

                if (filterModel.EpisodeTypeId is not null && filterModel.EpisodeTypeId.Any())
                {
                    episodeItemQuery = episodeItemQuery.Where(x => filterModel.EpisodeTypeId.Contains(x.EpisodeTypeId));
                }
                if (filterModel.SearchAll == false)
                {
                    if (filterModel.EpisodeCateogoriesIdLvl1 != null && filterModel.EpisodeCateogoriesIdLvl1.Any())
                    {
                        episodeItemQuery = episodeItemQuery.Where(x => filterModel.EpisodeCateogoriesIdLvl1.Contains(x.EpisodeCateogoriesIdLvl1));
                    }

                    if (filterModel.EpisodeCateogoriesIdLvl2 != null && filterModel.EpisodeCateogoriesIdLvl2.Any())
                    {
                        episodeItemQuery = episodeItemQuery.Where(x => filterModel.EpisodeCateogoriesIdLvl2.Contains(x.EpisodeCateogoriesIdLvl2)); ;
                    }
                    if (filterModel.EpisodeCateogoriesIdLvl3 != null && filterModel.EpisodeCateogoriesIdLvl3.Any())
                    {
                        episodeItemQuery = episodeItemQuery.Where(x => filterModel.EpisodeCateogoriesIdLvl3.Contains(x.EpisodeCateogoriesIdLvl3)); ;
                    }
                }
            }



            episodeItemQuery = SortEpisodeItems(episodeItemQuery, queryModel.SortTerm, queryModel.SortOrder);

            QueryResultList<ListEpisodeItemDetail> paginatedList = await PaginatedResultListForItems(episodeItemQuery, queryModel);

            return paginatedList;
        }

        private async Task<QueryResultList<ListEpisodeItemDetail>> PaginatedResultListForItems(IQueryable<ListEpisodeItemDetail> episodeItemQuery, QueryModel queryModel)
        {
            QueryResultList<ListEpisodeItemDetail> queryList = new QueryResultList<ListEpisodeItemDetail>();
            List<ListEpisodeItemDetail> paginatedList = null;
            if (queryModel.PageNumber > 0 && queryModel.PageSize > 0)
            {
                if (episodeItemQuery.Any())
                {
                    paginatedList = await episodeItemQuery.Skip((queryModel.PageNumber - 1) * queryModel.PageSize)
                 .Take(queryModel.PageSize).ToListAsync();
                    paginatedList.ForEach(x => x.Status = Enum.GetName(typeof(Status), x.StatusId));
                }
            }

            if (paginatedList != null)
            {
                queryList.ItemRecords = paginatedList;
                queryList.PageNumber = queryModel.PageNumber;
                queryList.PageSize = queryModel.PageSize;
                queryList.CurrentCount = paginatedList.Count();
            }
            else
            {
                queryList.ItemRecords = null;
                queryList.CurrentCount = 0;
            }
            queryList.TotalCount = episodeItemQuery.Count();
            queryList.PageNumber = queryModel.PageNumber;
            queryList.PageSize = queryModel.PageSize;

            return queryList;
        }

        private IQueryable<ListEpisodeItemDetail> SortEpisodeItems(IQueryable<ListEpisodeItemDetail> episodeItemQuery, string sortTerm, string sortOrder)
        {
            sortTerm = (sortTerm is null) ? string.Empty : sortTerm;
            sortOrder = (sortOrder is null) ? string.Empty : sortOrder;
            switch (sortTerm.ToLower())
            {

                case "id":

                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            episodeItemQuery = episodeItemQuery.OrderBy(x => x.Id);
                        }
                        else
                        {

                            episodeItemQuery = episodeItemQuery.OrderByDescending(x => x.Id);

                        }
                        break;
                    }

                default:
                    episodeItemQuery = episodeItemQuery.OrderBy(x => x.StatusId).ThenBy(x => x.Description);

                    break;
            }
            return episodeItemQuery;
        }

        private IQueryable<ListEpisodeItemDetail> SearchEpisodeItems(IQueryable<ListEpisodeItemDetail> episodeItemQuery, string searchTerm)
        {
            return episodeItemQuery.Where(s => s.Description.Contains(searchTerm));
        }
        /// <summary>
        /// Method to fetch Episode Item Detail
        /// </summary>
        /// <param name="id"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<EpisodeItemDetailInfo> GetEpisodeItemDetail(int id, int orgId)
        {
            return await (from EIT in _readOnlyDbContext.EpisodeItemDetails
                          where EIT.OrgId == orgId && EIT.Id == id
                          join EC1 in _readOnlyDbContext.EpisodeCategories on EIT.EpisodeCateogoriesIdLvl1 equals EC1.Id into Lvl1
                          from EC1 in Lvl1.DefaultIfEmpty()
                          join EC2 in _readOnlyDbContext.EpisodeCategories on EIT.EpisodeCateogoriesIdLvl2 equals EC2.Id into Lvl2
                          from EC2 in Lvl2.DefaultIfEmpty()
                          join EC3 in _readOnlyDbContext.EpisodeCategories on EIT.EpisodeCateogoriesIdLvl3 equals EC3.Id into Lvl3
                          from EC3 in Lvl3.DefaultIfEmpty()
                          select new EpisodeItemDetailInfo
                          {
                              Id = EIT.Id,
                              ItemNumber = EIT.ItemNumber,
                              OrgId = EIT.OrgId,
                              EpisodeCateogoriesIdLvl1 = EIT.EpisodeCateogoriesIdLvl1,
                              EpisodeCateogoryLvl1 = (EC1 == null) ? null : EC1.Name,
                              EpisodeCateogoriesIdLvl2 = EIT.EpisodeCateogoriesIdLvl2,
                              EpisodeCateogoryLvl2 = (EC2 == null) ? null : EC2.Name,
                              EpisodeCateogoriesIdLvl3 = EIT.EpisodeCateogoriesIdLvl3,
                              EpisodeCateogoryLvl3 = (EC3 == null) ? null : EC3.Name,
                              GSTInc = EIT.GSTInc,
                              PrivateFee = EIT.PrivateFee,
                              GstFee = EIT.GstFee,
                              Description = EIT.Description,
                              Notes = EIT.Notes,
                              UserDefinedText = EIT.UserDefinedText,
                              Msr = EIT.Msr,
                              Msrlevel1 = EIT.Msrlevel1,
                              Msrlevel2 = EIT.Msrlevel2,
                              Msrlevel3 = EIT.Msrlevel3,
                              StatusId = EIT.StatusId,
                              EpisodeTypeId = EIT.EpisodeTypeId,
                              EpisodeItemCustomFeeAssocs = (from ECF in _readOnlyDbContext.EpisodeItemCustomFeeAssocs
                                                            where ECF.OrgId == orgId && ECF.EpisodeItemDetailsId == id && ECF.StatusId == (short)Status.Active
                                                            select new EpisodeItemCustomFeeInfoAssoc
                                                            {
                                                                Id = ECF.Id,
                                                                EpisodeItemDetailsId = ECF.EpisodeItemDetailsId,
                                                                Name = ECF.Name,
                                                                Fee = ECF.Fee,
                                                                GstFee = ECF.GstFee,
                                                                OrgId = ECF.OrgId,
                                                                CreatedDate = ECF.CreatedDate,
                                                                ModifiedBy = ECF.ModifiedBy,
                                                                StatusId = ECF.StatusId
                                                            }
                                                                ).ToList()
                          }).FirstOrDefaultAsync();
        }
        /// <summary>
        /// Method to fetch episode item from id
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<EpisodeItemDetail> FetchEpisodeItemFromId(int id, int orgId)
        {
            return await _readOnlyDbContext.EpisodeItemDetails.Where(x => x.Id == id && x.OrgId == orgId).Include(x => x.EpisodeItemCustomFeeAssocs).AsNoTracking().FirstOrDefaultAsync();
        }
        /// <summary>
        /// Method to update Episode item
        /// </summary>
        /// <param name="episodeItemDetail"></param>
        /// <returns></returns>
        public async Task<int> UpdateEpisodeItemDetail(EpisodeItemDetail episodeItemDetail)
        {
            _updatableDBContext.Update(episodeItemDetail);
            return await _updatableDBContext.SaveChangesAsync();
        }


        public async Task<EpisodeCategory> FetchEpisodeCategoryFromName(string name, int? parentId, short episodeTypeId, int orgId)
        {
            return await _readOnlyDbContext.EpisodeCategories.Where(x => x.OrgId == orgId && x.EpisodeTypeId == episodeTypeId && (parentId == null || x.ParentEpisodeCategoriesId == parentId) && x.Name == name && x.StatusId == (short)Status.Active).AsNoTracking().FirstOrDefaultAsync();
        }



        public async Task<QueryResultList<ListFetchEpisodeItemDetail>> ListFetchEpisodeItemDetails(int orgId, QueryModel queryModel, EpisodeItemFilterModel filterModel)
        {
            List<SqlParameter> paramsList = new List<SqlParameter>();
            var limit = queryModel.PageSize;
            var offset = (queryModel.PageNumber - 1) * queryModel.PageSize;

            paramsList.Add(new SqlParameter("@orgId", orgId));
            paramsList.Add(new SqlParameter("@offset", offset));
            paramsList.Add(new SqlParameter("@limit", limit));
            //paramsList.Add(new SqlParameter("@patientId", patient_id));

            if (!(string.IsNullOrEmpty(queryModel.SearchTerm)))
            {
                paramsList.Add(new SqlParameter("@searchTerm", queryModel.SearchTerm));
            }
            if (!(filterModel is null))
            {

            }

            var response = await ExecuteStoredProcedure("[Utility].[ListEpisodeItems]", paramsList);
            if (response is not null)
            {
                response.PageNumber = queryModel.PageNumber;
                response.PageSize = queryModel.PageSize;
            }
            return response;

        }

        public async Task<QueryResultList<ListFetchEpisodeItemDetail>> ExecuteStoredProcedure(string storedProcedureName, IEnumerable<SqlParameter> parameters)
        {

            var dbConnection = (_readOnlyDbContext.Database.GetDbConnection());
            var ds = string.Empty;

            await dbConnection.OpenAsync().ConfigureAwait(false);

            using (var cmd = dbConnection.CreateCommand())
            {
                cmd.CommandText = storedProcedureName;
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 1800;
                foreach (var parameter in parameters)
                {
                    cmd.Parameters.Add(parameter);
                }
                var reader = await cmd.ExecuteReaderAsync();
                // 
                return SqlDataToJson(reader);
                //dbConnection.Close();

            }


        }
        private QueryResultList<ListFetchEpisodeItemDetail> SqlDataToJson(DbDataReader dataReader)
        {
            var dataTable = new DataTable();
            List<ListFetchEpisodeItemDetail> itemList = new List<ListFetchEpisodeItemDetail>();
            dataTable.Load(dataReader);

            itemList = (from rw in dataTable.AsEnumerable()
                        select new ListFetchEpisodeItemDetail
                        {
                            Id = Convert.ToInt32(rw["Id"]),
                            OrgId = Convert.ToInt32(rw["OrgId"]),
                            Description = (string)(rw["Description"] == DBNull.Value ? null : rw["Description"]),
                            PrivateFee = rw["PrivateFee"] == DBNull.Value ? null : Convert.ToDecimal(rw["PrivateFee"]),
                            GstFee = rw["GstFee"] == DBNull.Value ? null : Convert.ToDecimal(rw["GstFee"]),
                            EpisodeItemFeeAssocs = JsonConvert.DeserializeObject<List<EpisodeItemFeeAssoc>>(Convert.ToString(rw["Fees"])),
                            Msr = rw["MSR"] == DBNull.Value ? null : Convert.ToBoolean(rw["MSR"]),
                            Msrlevel1 = rw["MSRLevel1"] == DBNull.Value ? null : Convert.ToDecimal(rw["MSRLevel1"]),
                            Msrlevel2 = rw["MSRLevel2"] == DBNull.Value ? null : Convert.ToDecimal(rw["MSRLevel2"]),
                            Msrlevel3 = rw["MSRLevel3"] == DBNull.Value ? null : Convert.ToDecimal(rw["MSRLevel3"]),
                            EpisodeTypeId = Convert.ToInt16(rw["EpisodeTypeId"]),
                            GSTInc = Convert.ToBoolean(rw["GSTInc"]),

                        }).ToList();
            var dataTable2 = new DataTable();
            dataTable2.Load(dataReader);
            var Total = int.Parse(JsonConvert.SerializeObject(dataTable2.Rows[0][0]));
            var response = new QueryResultList<ListFetchEpisodeItemDetail>()
            {
                ItemRecords = itemList,
                CurrentCount = itemList.Count(),
                TotalCount = Total

            };
            return response;
        }
        /// <summary>
        /// method to fetch gst for company
        /// </summary>
        /// <param name="orgCode"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<decimal?> FetchGstForOrganisation(string orgCode, int orgId)
        {
            decimal? gst = await _readOnlyDbContext.Organisation.Where(C => C.Id == orgId && C.OrgCode == orgCode).Select(x => x.Gst).FirstOrDefaultAsync();
            if (gst is null)
                return default(decimal);
            return (decimal)gst;
        }

        public async Task<QueryResultList<ListEpisodeItemDetail>> ListMbsCustomItemDetails(QueryModel queryModel, EpisodeItemFilterModel mainFilter, int orgId)
        {
            IQueryable<ListEpisodeItemDetail> itemQuery = from EIT in _readOnlyDbContext.EpisodeItemDetails
                                                          where EIT.OrgId == orgId && EIT.StatusId == (short)Status.Active
                                                          select new ListEpisodeItemDetail
                                                          {
                                                              Id = EIT.Id,
                                                              ItemNumber = EIT.ItemNumber,
                                                              Description = EIT.Description,
                                                              EpisodeTypeId = EIT.EpisodeTypeId,
                                                              GSTInc = EIT.GSTInc
                                                          };

            if (mainFilter is not null && mainFilter.EpisodeTypeId is not null && mainFilter.EpisodeTypeId.Any())
            {
                itemQuery = itemQuery.Where(x => mainFilter.EpisodeTypeId.Contains(x.EpisodeTypeId));
            }
            if (!(string.IsNullOrWhiteSpace(queryModel.SearchTerm)))
            {
                itemQuery = SearchCustomItems(itemQuery, queryModel.SearchTerm);

            }



            QueryResultList<ListEpisodeItemDetail> paginatedList = await PaginatedResultListForMbsCustomItems(itemQuery, queryModel);

            return paginatedList;

        }

        private IQueryable<ListEpisodeItemDetail> SearchCustomItems(IQueryable<ListEpisodeItemDetail> itemQuery, string searchTerm)
        {
            return itemQuery.Where(s => s.Description.Contains(searchTerm));
        }
        private async Task<QueryResultList<ListEpisodeItemDetail>> PaginatedResultListForMbsCustomItems(IQueryable<ListEpisodeItemDetail> itemQuery, QueryModel queryModel)
        {
            QueryResultList<ListEpisodeItemDetail> queryList = new QueryResultList<ListEpisodeItemDetail>();
            List<ListEpisodeItemDetail> paginatedList = null;
            if (queryModel.PageNumber > 0 && queryModel.PageSize > 0)
            {
                if (itemQuery.Any())
                {
                    paginatedList = await itemQuery.Skip((queryModel.PageNumber - 1) * queryModel.PageSize)
                 .Take(queryModel.PageSize).ToListAsync();
                }
            }

            if (paginatedList != null)
            {
                queryList.ItemRecords = paginatedList;
                queryList.PageNumber = queryModel.PageNumber;
                queryList.PageSize = queryModel.PageSize;
                queryList.CurrentCount = paginatedList.Count();
            }
            else
            {
                queryList.ItemRecords = null;
                queryList.CurrentCount = 0;
            }
            queryList.TotalCount = itemQuery.Count();
            queryList.PageNumber = queryModel.PageNumber;
            queryList.PageSize = queryModel.PageSize;

            return queryList;
        }

        public async Task<ListFetchEpisodeItemDetail> FetchRatesForCustomItems(EpisodeMbsCustomItemFilterModel filterModel, int orgId)
        {
            ListFetchEpisodeItemDetail episodeitem = await (from EIT in _readOnlyDbContext.EpisodeItemDetails
                                                            where EIT.OrgId == orgId && EIT.EpisodeTypeId == filterModel.EpisodeTypeId
                                                            && EIT.ItemNumber == filterModel.ItemNumber
                                                            && EIT.Id == filterModel.Id
                                                            select new ListFetchEpisodeItemDetail
                                                            {
                                                                Id = EIT.Id,
                                                                PrivateFee = EIT.PrivateFee,
                                                                GSTInc = EIT.GSTInc,
                                                                GstFee = EIT.GstFee,
                                                                ItemNumber = EIT.ItemNumber,
                                                                EpisodeTypeId = EIT.EpisodeTypeId,
                                                                EpisodeItemFeeAssocs = (from EITF in _readOnlyDbContext.EpisodeItemCustomFeeAssocs
                                                                                        where EITF.OrgId == orgId && EITF.StatusId == (short)Status.Active && EITF.EpisodeItemDetailsId == EIT.Id
                                                                                        select new EpisodeItemFeeAssoc
                                                                                        {
                                                                                            Id = EITF.Id,
                                                                                            Fee = EITF.Fee,
                                                                                            GstFee = EITF.GstFee,
                                                                                            Name = EITF.Name
                                                                                        }).ToList()
                                                            }).FirstOrDefaultAsync();


            episodeitem.EpisodeItemFeeAssocs.Add(new EpisodeItemFeeAssoc
            {
                Fee = episodeitem.PrivateFee == null ? 0 : (decimal)episodeitem.PrivateFee,
                Name = "Private Fee",
                GstFee = episodeitem.GstFee == null ? 0 : episodeitem.GstFee,
            });

            return episodeitem;
        }

        public async Task<List<ListEpisode>> GetAllEpisodeGroupsDAL(long patient_id, List<ClinicalCategory> clinicalCategoriesList, int orgId)
        {
            var resultList = await _readOnlyDbContext.Episodes.Where(x => x.OrgId == orgId && x.PatientDetailsId == patient_id).ToListAsync();
            var episodeCategoriesList = await _readOnlyDbContext.EpisodeCategories.Where(x => x.OrgId == orgId).ToListAsync();
            string L1Name = null;
            List<ListEpisode> episodesList = new List<ListEpisode>();
            foreach (var item in resultList)
            {
                if (item.EpisodeTypeId == (short)EpisodeTypes.MBS)
                {
                    L1Name = clinicalCategoriesList.Where(x => x.Id == item.EpisodeCategoryL1id).Select(x => x.Name).FirstOrDefault();
                }
                else
                {
                    L1Name = episodeCategoriesList.Where(x => x.Id == item.EpisodeCategoryL1id).Select(x => x.Name).FirstOrDefault();
                }
                ListEpisode episodeGroup = new ListEpisode
                {
                    Id = item.Id,
                    OrgId = item.OrgId,
                    PatientDetailsId = item.PatientDetailsId,
                    EpisodeCategoryL1id = item.EpisodeCategoryL1id,
                    EpisodeCategoryL2id = (item.EpisodeCategoryL2id == null) ? null : item.EpisodeCategoryL2id,
                    EpisodeCategoryL3id = (item.EpisodeCategoryL2id == null) ? null : item.EpisodeCategoryL3id,
                    EpisodeCategoryL1Name = L1Name,
                    EpisodeCategoryL2Name = (item.EpisodeCategoryL2id == null) ? null : episodeCategoriesList.Where(x => x.Id == item.EpisodeCategoryL2id).Select(x => x.Name).FirstOrDefault(),
                    EpisodeCategoryL3Name = (item.EpisodeCategoryL3id == null) ? null : episodeCategoriesList.Where(x => x.Id == item.EpisodeCategoryL3id).Select(x => x.Name).FirstOrDefault(),
                    EpisodeTypeId = item.EpisodeTypeId,
                    StatusId = item.StatusId
                };
                episodesList.Add(episodeGroup);
            }
            return episodesList;
        }

        /// <summary>
        /// Method to save a new episode in db
        /// </summary>
        /// <param name="episode"></param>
        /// <returns></returns>
        public async Task<int> AddEpisodeDAL(Episode episode)
        {
            await _updatableDBContext.Episodes.AddAsync(episode);
            int rows = await _updatableDBContext.SaveChangesAsync();
            if (rows > 0)
            {
                return episode.Id;
            }
            return 0;
        }

        public async Task<List<Episode>> GetEpisodeByPatientId(int orgId, long patientId)
        {
            return await _readOnlyDbContext.Episodes.Where(x => x.OrgId == orgId && x.PatientDetailsId == patientId && x.StatusId == (short)Status.Active).ToListAsync();
        }

        public async Task<List<EpisodeItemDetail>> ListEpisodeItemDetailsFromFilter(int orgId, short episodeType, List<long> lstId, List<long> lstItemNUmber)
        {

            var query = _readOnlyDbContext.EpisodeItemDetails.Include(x => x.EpisodeItemCustomFeeAssocs.Where(x => x.StatusId == (short)Status.Active)).Where(e => e.EpisodeTypeId == episodeType);


            if (lstId != null && lstId.Count > 0)
            {
                lstId.Remove(0);
                query = query.Where(x => lstId.Contains(x.Id));

            }

            if (lstItemNUmber != null && lstItemNUmber.Count > 0)
                query = query.Where(x => lstItemNUmber.Contains(x.ItemNumber));

            return await query.ToListAsync();
        }

    }
}
