﻿using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.Utility.Interfaces;
using Capstone2.RestServices.Utility.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Transactions;

namespace Capstone2.RestServices.Utility.Services
{
    public class ChecklistBAL : IChecklistBAL
    {
        private readonly IChecklistDAL _checklistDAL;
        public readonly AppSettings _appSettings;

        public ChecklistBAL(IChecklistDAL checklistDAL, IOptions<AppSettings> appSettings)
        {
            _checklistDAL = checklistDAL;
            _appSettings = appSettings.Value;
        }

        /// <summary>
        /// Method to save an entry in Checklist
        /// </summary>
        /// <param name="inputChecklist"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> AddChecklistBAL(Checklist inputChecklist, BaseHttpRequestContext baseHttpRequestContext)
        {
            int orgId = baseHttpRequestContext.OrgId;
            long loggedInUser = baseHttpRequestContext.UserId;
            ApiResponse<long?> apiResponse = new();
            using (var transaction = new TransactionScope(TransactionScopeOption.Required, TransactionScopeAsyncFlowOption.Enabled))
            {
                if (inputChecklist != null && inputChecklist.ChecklistActivityAssocs.Any())
                {
                    inputChecklist.ChecklistActivityAssocs.ToList().ForEach(a => { a.OrgId = orgId; a.ModifiedBy = loggedInUser; a.StatusId = (short)Status.Active; a.CreatedDate = DateTime.UtcNow; });
                }
                inputChecklist.CreatedBy = loggedInUser;
                inputChecklist.OrgId = orgId;
                inputChecklist.StatusId = (short)Status.Active;
                inputChecklist.CreatedDate = DateTime.UtcNow;

                if (!await _checklistDAL.CheckChecklistName(inputChecklist.Name, inputChecklist.OrgId))
                {
                    long id = await _checklistDAL.AddChecklistDAL(inputChecklist);
                    if (id > 0)
                    {
                        apiResponse.StatusCode = StatusCodes.Status200OK;
                        apiResponse.Message = "Success";
                        apiResponse.Result = id;
                    }
                    else
                    {
                        apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                        apiResponse.Message = "Failure";
                        apiResponse.Result = null;
                    }
                }
                else
                {
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Result = null;
                    apiResponse.Message = "Failure";
                    apiResponse.Errors.Add("Checklist cannot be added at this time.Please try again later.");
                }
                transaction.Complete();
                transaction.Dispose();
            }
            return apiResponse;
        }

        /// <summary>
        /// Method to fetch Checklist details
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<InputChecklist>> GetChecklistBAL(BaseHttpRequestContext baseHttpRequestContext, long id)
        {
            int orgId = baseHttpRequestContext.OrgId;
            ApiResponse<InputChecklist> apiResposne = new();
            InputChecklist checklistFromDB = await _checklistDAL.FetchChecklistDetails(orgId, id);
            if (checklistFromDB is null)
            {
                apiResposne.Result = null;
                apiResposne.StatusCode = StatusCodes.Status400BadRequest;
                apiResposne.Message = "Failure";
                apiResposne.Errors.Add("The Checklist doesnot exist.");
                return apiResposne;
            }

            else
            {
                ICollection<InputChecklistActivityAssoc> checklistActivityAssocs = checklistFromDB.ChecklistActivityAssocs;
                checklistActivityAssocs.ToList().ForEach(a => { a.Activity.Days = (short?)(a.Activity.IsBefore == true ? a.Activity.Days * -1 : a.Activity.Days);});
                checklistFromDB.ChecklistActivityAssocs = checklistActivityAssocs.OrderBy(x => x.Activity.Days).ThenBy(x => x.Activity.Name).ToList();
                apiResposne.Result = checklistFromDB;
                apiResposne.StatusCode = StatusCodes.Status200OK;
                apiResposne.Message = "Success";
                return apiResposne;
            }
        }

        /// <summary>
        /// Method to update an Checklist
        /// </summary>
        /// <param name="id"></param>
        /// <param name="inputActivity"></param>
        /// <param name="orgId"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task<ApiResponse<string>> EditChecklistBAL(int id, Checklist inputChecklist, BaseHttpRequestContext baseHttpRequestContext)
        {
            int orgId = baseHttpRequestContext.OrgId;
            long userId = baseHttpRequestContext.UserId;
            ApiResponse<string> apiResponse = new();
            Checklist checklistFromDb = await _checklistDAL.GetChecklistDAL(orgId, id);

            inputChecklist.OrgId = orgId;
            inputChecklist.ModifiedDate = DateTime.UtcNow;
            inputChecklist.ModifiedBy = userId;
            inputChecklist.CreatedBy = checklistFromDb.CreatedBy;
            inputChecklist.CreatedDate = checklistFromDb.CreatedDate;
            inputChecklist.Id = checklistFromDb.Id;
            if (inputChecklist != null && inputChecklist.ChecklistActivityAssocs.Any())
            {
                inputChecklist.ChecklistActivityAssocs.ToList().ForEach(a => { a.OrgId = orgId; a.ChecklistId = id; });
            }

            var removeActivityList = await _checklistDAL.GetChecklistActivityAssocs(orgId, id);
            var inputActivityList = inputChecklist.ChecklistActivityAssocs;


            inputChecklist.ChecklistActivityAssocs = null;

            using (var transaction = new TransactionScope(TransactionScopeOption.Required, TransactionScopeAsyncFlowOption.Enabled))
            {
                List<ChecklistActivityAssoc> userList = EditChecklistActivityAssocs(removeActivityList, inputActivityList, userId);
                inputChecklist.ChecklistActivityAssocs = userList;

                await _checklistDAL.EditChecklistDAL(inputChecklist);

                apiResponse.Result = "Success";
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";

                transaction.Complete();
                transaction.Dispose();
            }

            return apiResponse;
        }

        private List<ChecklistActivityAssoc> EditChecklistActivityAssocs(ICollection<ChecklistActivityAssoc> removeActivityList, ICollection<ChecklistActivityAssoc> inputActivityList, long userId)
        {
            List<ChecklistActivityAssoc> addActivityList = new();

            foreach (var activityAssoc in inputActivityList)
            {
                if (activityAssoc.Id > 0)
                {
                    var existingobj = removeActivityList.FirstOrDefault(x => x.Id == activityAssoc.Id);
                    removeActivityList.Remove(existingobj);
                }
                else
                {
                    ChecklistActivityAssoc activityAssocDB = removeActivityList.Where(x => x.ActivityId == activityAssoc.ActivityId).FirstOrDefault();
                    if (activityAssocDB is null)
                    {
                        activityAssoc.CreatedDate = DateTime.UtcNow;
                        activityAssoc.StatusId = (short)Status.Active;
                        addActivityList.Add(activityAssoc);
                    }
                    else
                    {
                        if (activityAssocDB.StatusId == (short)Status.Deleted)
                        {
                            activityAssocDB.StatusId = (short)Status.Active;
                            activityAssocDB.ModifiedDate = DateTime.UtcNow;
                            activityAssocDB.ModifiedBy = userId;
                            addActivityList.Add(activityAssocDB);

                        }
                        removeActivityList.Remove(activityAssocDB);

                    }


                }
            }

            foreach (var userAssoc in removeActivityList.Where(x => x.StatusId == (short)Status.Active))
            {
                userAssoc.StatusId = (short)Status.Deleted;
                userAssoc.ModifiedDate = DateTime.UtcNow;
                userAssoc.ModifiedBy = userId;
            }
            addActivityList = addActivityList.Concat(removeActivityList).ToList();
            return addActivityList;
        }

        /// <summary>
        /// Method to generate a list of Checklists
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        public async Task<ApiResponse<QueryResultList<ListChecklist>>> ListChecklistsBAL(BaseHttpRequestContext baseHttpRequestContext, QueryModel queryModel)
        {
            ApiResponse<QueryResultList<ListChecklist>> apiResponse = new();
            int orgId = baseHttpRequestContext.OrgId;
            long loggedInUser = baseHttpRequestContext.UserId;
            ChecklistFilterModel filterModel = PrepareFilterParameters(queryModel.Filter);
            QueryResultList<ListChecklist> checklistQueryList = await _checklistDAL.ListChecklistsDAL(orgId, queryModel, filterModel);
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result = checklistQueryList;
            return apiResponse;
        }

        /// <summary>
        /// method to generate the filter parameters from the request
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        private ChecklistFilterModel PrepareFilterParameters(string filter)
        {
            if (!(String.IsNullOrEmpty(filter)))
            {
                return JsonConvert.DeserializeObject<ChecklistFilterModel>(filter);
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// Method to check if the entered checklist name is unique
        /// </summary>
        /// <param name="search_term"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<ApiResponse<bool>> CheckChecklistName(string search_term, int orgId)
        {
            bool nameBool = await _checklistDAL.CheckChecklistName(search_term, orgId);
            var apiResponse = new ApiResponse<bool>
            {
                Result = nameBool,
                Message = "Success",
                StatusCode = StatusCodes.Status200OK

            };
            return apiResponse;
        }
    }
}
