﻿using Capstone2.RestServices.Utility.Context;
using Capstone2.RestServices.Utility.Interfaces;
using Capstone2.RestServices.Utility.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Utility.Services
{
    public class ActivityDAL : IActivityDAL
    {
        public readonly ReadOnlyUtilityDBContext _readOnlyDbContext;
        public UpdatableUtilityDBContext _updatableDBContext;
        public ActivityDAL(ReadOnlyUtilityDBContext readOnlyDbContext, UpdatableUtilityDBContext updatableDBContext)
        {
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
        }

        /// <summary>
        /// Method to create a new Category
        /// </summary>
        /// <param name="category"></param>
        /// <returns></returns>
        public async Task<long> AddCategoryDAL(Category category)
        {
            await _updatableDBContext.Categories.AddAsync(category);
            await _updatableDBContext.SaveChangesAsync();
            return category.Id;
        }

        /// <summary>
        /// Method to fetch a category
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        public async Task<Category> GetCategoryDAL(int orgId, long id)
        {
            var category = await _readOnlyDbContext.Categories.Where(x => x.Id == id && x.OrgId == orgId).AsNoTracking().FirstOrDefaultAsync();
            return category;
        }

        /// <summary>
        /// Method to update a category
        /// </summary>
        /// <param name="category"></param>

        public async Task<int> EditCategoryDAL(Category category)
        {
            _updatableDBContext.Categories.Update(category);
            return await _updatableDBContext.SaveChangesAsync();
        }

        /// <summary>
        /// Method to create a new activity
        /// </summary>
        /// <param name="activity"></param>
        /// <returns></returns>
        public async Task<long> AddActivityDAL(Activity activity)
        {
            await _updatableDBContext.Activities.AddAsync(activity);
            await _updatableDBContext.SaveChangesAsync();
            return activity.Id;
        }

        /// <summary>
        /// Method to fetch an activity
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        public async Task<Activity> GetActivityDAL(int orgId, long id)
        {
            return await _readOnlyDbContext.Activities.Where(x => x.Id == id && x.OrgId == orgId).AsNoTracking().FirstOrDefaultAsync();
        }

        /// <summary>
        /// Method to update an activity
        /// </summary>
        /// <param name="activity"></param>

        public async Task<int> EditActivityDAL(Activity activity)
        {
            _updatableDBContext.Activities.Update(activity);
            return await _updatableDBContext.SaveChangesAsync();
        }

        /// <summary>
        /// Fetch ActivityUserAssocs
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="activityId"></param>
        /// <returns></returns>
        public async Task<List<ActivityUserAssoc>> GetActivityUserAssocs(int orgId, long activityId)
        {
            return await _readOnlyDbContext.ActivityUserAssocs.Where(s => s.ActivityId == activityId && s.OrgId == orgId).AsNoTracking().ToListAsync();
        }

        public async Task<int> GetActivityCount(int categoryId)
        {
            return await _readOnlyDbContext.Activities.Where(a => a.CategoryId == categoryId).CountAsync();
        }
        /// <summary>
        /// Fetch Categories
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="st"></param>
        /// <returns></returns>
        public async Task<List<ListCategory>> GetCategories(string st, int orgId)
        {
            if (st == null)
            {
                IQueryable<ListCategory> categoryQuery = from category in _readOnlyDbContext.Categories
                                                         where category.OrgId == orgId && category.StatusId == (short)Status.Active
                                                         select new ListCategory()
                                                         {
                                                             Id = category.Id,
                                                             OrgId = category.OrgId,
                                                             Name = category.Name,
                                                             StatusId = category.StatusId,
                                                             CreatedBy = category.CreatedBy,
                                                             CreatedDate = category.CreatedDate,
                                                             ModifiedDate = category.ModifiedDate,
                                                             ModifiedBy = category.ModifiedBy,
                                                             ActivityCount = _readOnlyDbContext.Activities.Where(a => a.CategoryId == category.Id).Count(),
                                                         };
                return await categoryQuery.ToListAsync();
            }
            else
            {
                return await _readOnlyDbContext.Categories
                        .Where(p => p.Name.StartsWith(st) && p.OrgId == orgId && p.StatusId == (short)Status.Active).Select(category => new ListCategory()
                        {
                            Id = category.Id,
                            OrgId = category.OrgId,
                            Name = category.Name,
                            StatusId = category.StatusId,
                            CreatedBy = category.CreatedBy,
                            CreatedDate = category.CreatedDate,
                            ModifiedDate = category.ModifiedDate,
                            ModifiedBy = category.ModifiedBy,
                            ActivityCount = _readOnlyDbContext.Activities.Where(a => a.CategoryId == category.Id).Count(),
                        })
                        .ToListAsync();
            }
        }

        /// <summary>
        /// Method to check if the category name exists in the database
        /// </summary>
        /// <param name="search_term"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<bool> CheckCategoryName(string search_term, int orgId)
        {
            var name = await _readOnlyDbContext.Categories
                   .Where(p => p.Name.Equals(search_term) && p.OrgId == orgId && p.StatusId == (short)Status.Active).FirstOrDefaultAsync();
            if (name == null)
            {
                return false;
            }
            else
            {
                return true;
            }
        }

        /// <summary>
        /// Method to retrieve list of activities
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="queryModel"></param>
        /// <param name="activityFilterModel"></param>
        /// <returns></returns>
        public async Task<List<ListActivity>> ListActivitiesDAL(int orgId, QueryModel queryModel, ActivityFilterModel activityFilterModel)
        {
            IQueryable<ListActivity> activityQuery = (from activity in _readOnlyDbContext.Activities
                                                      join category in _readOnlyDbContext.Categories on activity.CategoryId equals category.Id into JAC
                                                      from category in JAC.DefaultIfEmpty()
                                                      //join UD in _readOnlyDbContext.UserDetails on activity.CreatedBy equals UD.Id
                                                      //from SU in SUA.Where(u=>u.OrgId==orgId).DefaultIfEmpty()
                                                      where activity.OrgId == orgId
                                                      select new ListActivity()
                                                      {
                                                          Id = activity.Id,
                                                          OrgId = activity.OrgId,
                                                          Name = activity.Name,
                                                          CategoryId = activity.CategoryId,
                                                          ActivityTypeId = activity.ActivityTypeId,
                                                          IsBefore = activity.IsBefore,
                                                          Days = activity.Days,
                                                          StatusId = activity.StatusId,
                                                          CreatedBy = activity.CreatedBy,
                                                          CreatedDate = activity.CreatedDate,
                                                          ModifiedDate = activity.ModifiedDate,
                                                          ModifiedBy = activity.ModifiedBy,
                                                          Category = category,
                                                          ActivityUserAssocs = (ICollection<InputActivityUserAssoc>)(from SU in _readOnlyDbContext.ActivityUserAssocs.Where(x => x.ActivityId == activity.Id && x.OrgId == orgId && x.StatusId == (short)Status.Active)
                                                                                                                     from UD in _readOnlyDbContext.UserDetails.Where(u => u.OrgId == orgId && u.Id == SU.UserDetailsId)
                                                                                                                     select new InputActivityUserAssoc()
                                                                                                                     {
                                                                                                                         Id = SU.Id,
                                                                                                                         UserDetailsId = (long)SU.UserDetailsId,
                                                                                                                         CreatedDate = SU.CreatedDate,
                                                                                                                         UserDetails = new UserDetailInfo
                                                                                                                         {
                                                                                                                             Id = UD.Id,
                                                                                                                             OrgId = UD.OrgId,
                                                                                                                             FirstName = UD.FirstName,
                                                                                                                             SurName = UD.SurName,
                                                                                                                             PhotoFileDetailsId = UD.PhotoFileDetailsId,
                                                                                                                         }
                                                                                                                     })
                                                      });

            if (activityFilterModel != null)
            {
                if (activityFilterModel.ActivityTypeId != null)
                {
                    activityQuery = activityQuery.Where(x => activityFilterModel.ActivityTypeId.Contains(x.ActivityTypeId));
                }

                if (activityFilterModel.CategoryId != null)
                {
                    activityQuery = activityQuery.Where(x => activityFilterModel.CategoryId.Contains(x.CategoryId));
                }
                if (activityFilterModel.StatusId != null)
                {
                    activityQuery = activityQuery.Where(x => activityFilterModel.StatusId.Contains(x.StatusId));
                }
            }

            if (!(string.IsNullOrWhiteSpace(queryModel.SearchTerm)))
            {
                activityQuery = SearchActivities(activityQuery, queryModel.SearchTerm);

            }
            if (!(string.IsNullOrWhiteSpace(queryModel.SortOrder)) && !(string.IsNullOrWhiteSpace(queryModel.SortTerm)))
            {
                activityQuery = SortActivities(activityQuery, queryModel.SortTerm, queryModel.SortOrder);

            }
            return await activityQuery.ToListAsync();
        }

        /// <summary>
        /// method to enable search of activities
        /// </summary>
        /// <param name="ActivityQuery"></param>
        /// <param name="searchTerm"></param>
        /// <returns></returns>
        private IQueryable<ListActivity> SearchActivities(IQueryable<ListActivity> activityQuery, string searchTerm)
        {

            string filterString = string.Empty;
            bool istwoWordSearch = Regex.IsMatch(searchTerm, @"\s");

            if (!istwoWordSearch)
            {
                activityQuery = activityQuery.Where(s => s.Name.Contains(searchTerm));
            }
            else
            {
                string[] search = searchTerm.Split(" ");
                activityQuery = activityQuery.Where(s => s.Name.Contains(searchTerm));
            }
            return activityQuery;

        }


        private IQueryable<ListActivity> SortActivities(IQueryable<ListActivity> activityQuery, string sortTerm, string sortOrder)
        {
            switch (sortTerm.ToLower())
            {

                case "id":

                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            activityQuery = activityQuery.OrderBy(x => x.Id);
                        }
                        else
                        {
                            activityQuery = activityQuery.OrderByDescending(x => x.Id);
                        }
                        break;
                    }
                case "name":

                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            activityQuery = activityQuery.OrderBy(x => x.Name);
                        }
                        else
                        {

                            activityQuery = activityQuery.OrderByDescending(x => x.Name);

                        }
                        break;
                    }


            }

            return activityQuery;
        }

        public async Task<QueryResultList<ListCategory>> GetCategories(QueryModel queryModel, ActivityFilterModel filterModel, int orgId)
        {
            List<SqlParameter> paramsList = new List<SqlParameter>();
            var limit = queryModel.PageSize;
            var offset = (queryModel.PageNumber - 1) * queryModel.PageSize;

            paramsList.Add(new SqlParameter("@orgId", orgId));
            //paramsList.Add(new SqlParameter("@offset", offset));
            //paramsList.Add(new SqlParameter("@limit", limit));
            //paramsList.Add(new SqlParameter("@patientId", patient_id));

            if (!(string.IsNullOrEmpty(queryModel.SearchTerm)))
            {
                paramsList.Add(new SqlParameter("@searchTerm", queryModel.SearchTerm));
            }
            if (!(filterModel is null))
            {
                if (filterModel.ActivityTypeId != null && filterModel.ActivityTypeId.Any())
                {
                    paramsList.Add(new SqlParameter("@ActivityTypeId", string.Join<short?>(",", filterModel.ActivityTypeId)));
                }
                if (filterModel.CategoryId != null && filterModel.CategoryId.Any())
                {
                    paramsList.Add(new SqlParameter("@CategoryId", string.Join<int?>(",", filterModel.CategoryId)));
                }
                if (!(string.IsNullOrEmpty(filterModel.ActivityName)))
                {
                    paramsList.Add(new SqlParameter("@ActivityName", filterModel.ActivityName));
                }
            }

            var response = await ExecuteStoredProcedure("[Utility].[ListCategories]", paramsList);

            return response;

        }

        public async Task<QueryResultList<ListCategory>> ExecuteStoredProcedure(string storedProcedureName, IEnumerable<SqlParameter> parameters)
        {
            List<ListCategory> lstCategory = null;
            // _readOnlyDbContext.Database.Sq

            var dbConnection = (_readOnlyDbContext.Database.GetDbConnection());
            var ds = string.Empty;

            await dbConnection.OpenAsync().ConfigureAwait(false);

            using (var cmd = dbConnection.CreateCommand())
            {
                cmd.CommandText = storedProcedureName;
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 1800;
                foreach (var parameter in parameters)
                {
                    cmd.Parameters.Add(parameter);
                }
                var reader = await cmd.ExecuteReaderAsync();
                // 
                return SqlDataToJson(reader);
                //dbConnection.Close();

            }


        }
        private QueryResultList<ListCategory> SqlDataToJson(DbDataReader dataReader)
        {
            var dataTable = new DataTable();
            List<ListCategory> categoryList = new List<ListCategory>();
            dataTable.Load(dataReader);

            DateTime validValue;
            categoryList = (from rw in dataTable.AsEnumerable()
                            select new ListCategory
                            {
                                Id = Convert.ToInt32(rw["Id"]),
                                OrgId = Convert.ToInt32(rw["OrgId"]),
                                Name = (string)(rw["Name"] == DBNull.Value ? null : rw["Name"]),
                                CreatedDate = Convert.ToDateTime(rw["CreatedDate"]),
                                ModifiedDate = rw["ModifiedDate"] == DBNull.Value ? null : Convert.ToDateTime(rw["ModifiedDate"]),
                                CreatedBy = rw["CreatedBy"] == DBNull.Value ? null : Convert.ToInt64(rw["CreatedBy"]),
                                ModifiedBy = rw["ModifiedBy"] == DBNull.Value ? null : Convert.ToInt64(rw["ModifiedBy"]),
                                ActivityCount = (rw["ActivityCount"] == DBNull.Value ? (int?)null : Convert.ToInt32(rw["ActivityCount"])),
                                StatusId = Convert.ToInt16(rw["StatusId"])
                            }).ToList();

            var response = new QueryResultList<ListCategory>()
            {
                ItemRecords = categoryList,
                CurrentCount = categoryList.Count(),
                TotalCount = categoryList.Count()

            };
            return response;
        }

        public async Task<InputActivityDetail> FetchActivityDetails(int orgId, long id)
        {
            IQueryable<InputActivityDetail> activityQuery = from activity in _readOnlyDbContext.Activities
                                                            join category in _readOnlyDbContext.Categories on activity.CategoryId equals category.Id
                                                            //from SU in SUA.Where(u=>u.OrgId==orgId).DefaultIfEmpty()
                                                            where activity.OrgId == orgId && activity.Id == id
                                                            select new InputActivityDetail()
                                                            {
                                                                Id = activity.Id,
                                                                OrgId = activity.OrgId,
                                                                Name = activity.Name,
                                                                CategoryId = activity.CategoryId,
                                                                ActivityTypeId = activity.ActivityTypeId,
                                                                IsBefore = activity.IsBefore,
                                                                Days = activity.Days,
                                                                StatusId = activity.StatusId,
                                                                CreatedBy = activity.CreatedBy,
                                                                CreatedDate = activity.CreatedDate,
                                                                ModifiedDate = activity.ModifiedDate,
                                                                ModifiedBy = activity.ModifiedBy,
                                                                Category = category,
                                                                ActivityUserAssocs = (ICollection<InputActivityUserAssoc>)(from SU in _readOnlyDbContext.ActivityUserAssocs.Where(x => x.ActivityId == id && x.OrgId == orgId && x.StatusId == (short)Status.Active)
                                                                                                                           from UD in _readOnlyDbContext.UserDetails.Where(u => u.OrgId == orgId && u.Id == SU.UserDetailsId)
                                                                                                                           select new InputActivityUserAssoc()
                                                                                                                           {
                                                                                                                               Id = SU.Id,
                                                                                                                               UserDetailsId = (long)SU.UserDetailsId,
                                                                                                                               CreatedDate = SU.CreatedDate,
                                                                                                                               UserDetails = new UserDetailInfo
                                                                                                                               {
                                                                                                                                   Id = UD.Id,
                                                                                                                                   OrgId = UD.OrgId,
                                                                                                                                   FirstName = UD.FirstName,
                                                                                                                                   SurName = UD.SurName,
                                                                                                                                   PhotoFileDetailsId = UD.PhotoFileDetailsId
                                                                                                                               }
                                                                                                                           })


                                                            };
            return await activityQuery.FirstOrDefaultAsync();
        }


    }
}
