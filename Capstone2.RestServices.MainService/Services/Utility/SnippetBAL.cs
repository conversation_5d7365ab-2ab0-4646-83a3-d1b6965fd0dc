﻿using AutoMapper;
using Capstone2.Framework.RestApi;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.Utility.Interfaces;
using Capstone2.RestServices.Utility.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch.Internal;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Transactions;

namespace Capstone2.RestServices.Utility.Services
{
    public class SnippetBAL : ISnippetBAL
    {
        public readonly ISnippetDAL _snippetDAL;
        public IMapper _mapper;
        public readonly AppSettings _appSettings;
        public readonly IRolesBAL _rolesBAL;

        public SnippetBAL(ISnippetDAL snippetDAL, IRolesBAL rolesBAL, IMapper mapper, IOptions<AppSettings> appSettings)
        {
            _snippetDAL = snippetDAL;
            _mapper = mapper;
            _rolesBAL = rolesBAL;
            _appSettings = appSettings.Value;
        }

        /// <summary>
        /// Method to save a entry in snippetdetails
        /// </summary>
        /// <param name="snippetDetail"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        /// 

        public async Task<ApiResponse<long?>> AddSnippetDetails(SnippetDetail snippetDetail, BaseHttpRequestContext baseHttpRequestContext)
        {
            int orgId = baseHttpRequestContext.OrgId;
            long loggedInUser = baseHttpRequestContext.UserId;
            if (snippetDetail != null && (snippetDetail.SnippetUserAssocs.Count == 0 || snippetDetail.SnippetUserAssocs.Where(x => x.UserDetailsId == loggedInUser).Count() == 0))
            {
                SnippetUserAssoc createdUserAssoc = new SnippetUserAssoc()
                {
                    UserDetailsId = loggedInUser
                };
                snippetDetail.SnippetUserAssocs.Add(createdUserAssoc);
            }
            if (snippetDetail != null && snippetDetail.SnippetUserAssocs.Any())
            {
                snippetDetail.SnippetUserAssocs.ToList().ForEach(a => { a.OrgId = orgId; a.CreatedBy = loggedInUser; a.StatusId = (short)Status.Active; });

            }
            snippetDetail.CreatedBy = loggedInUser;
            snippetDetail.OrgId = orgId;
            snippetDetail.StatusId = (short)Status.Active;
            snippetDetail.CreatedDate = DateTime.UtcNow;

            //getSFDT Text 
            var sfdtResult = await GetSFDTTextData(snippetDetail.Text, baseHttpRequestContext);
            if (sfdtResult.StatusCode != StatusCodes.Status200OK)
            {
                ApiResponse<long?> apiResponse = new()
                {
                    StatusCode = StatusCodes.Status400BadRequest,
                    Message = "Text to SFDT conversion failed.",
                    Result = null
                };
                return apiResponse;
            }

            snippetDetail.SFDTText = sfdtResult.Result.ToString();

            DateTime.SpecifyKind(snippetDetail.CreatedDate, DateTimeKind.Utc);
            long id = await _snippetDAL.AddSnippetDetailsAsync(snippetDetail);
            if (id > 0)
            {
                ApiResponse<long?> apiResponse = new()
                {
                    StatusCode = StatusCodes.Status200OK,
                    Message = "Success",
                    Result = id
                };
                return apiResponse;
            }
            else
            {
                ApiResponse<long?> apiResponse = new()
                {
                    StatusCode = StatusCodes.Status400BadRequest,
                    Message = "Failure",
                    Result = null
                };
                return apiResponse;
            }
        }

        /// <summary>
        /// Method to check if the entered shortcut is unique
        /// </summary>
        /// <param name="search_term"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<ApiResponse<bool>> CheckSnippetShortcut(string search_term, int orgId)
        {
            bool shortcutBool = await _snippetDAL.CheckSnippetShortcut(search_term, orgId);
            var apiResponse = new ApiResponse<bool>
            {
                Result = shortcutBool,
                Message = "Success",
                StatusCode = StatusCodes.Status200OK
            };
            return apiResponse;
        }

        /// <summary>
        /// Method to check if the entered snippet name is unique
        /// </summary>
        /// <param name="search_term"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<ApiResponse<bool>> CheckSnippetName(string search_term, int orgId)
        {
            bool nameBool = await _snippetDAL.CheckSnippetName(search_term, orgId);
            var apiResponse = new ApiResponse<bool>
            {
                Result = nameBool,
                Message = "Success",
                StatusCode = StatusCodes.Status200OK
            };
            return apiResponse;
        }

        /// <summary>
        /// Method to generate a paginated list of snippets based on search criteria
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        public async Task<ApiResponse<QueryResultList<SnippetDetail>>> ListSnippets(BaseHttpRequestContext baseHttpRequestContext, QueryModel queryModel)
        {
            ApiResponse<QueryResultList<SnippetDetail>> apiResponse = new();
            RolePermissionFilterModel rolePermissionFilterModel =
                await _rolesBAL.IsDeleteOnlyPermissionsBAL(baseHttpRequestContext, ModulesEnum.Snippets);

            SnippetFilterModel filterModel = PrepareFilterParameters(queryModel.Filter);
            QueryResultList<SnippetDetail> snippetQuery = null;
            if (filterModel == null || filterModel.UserDetailsId is null || filterModel.UserDetailsId.Count == 0)
            {
                //LIsting with search based on snippet name,shortcut and created user
                snippetQuery = await _snippetDAL.ListSnippetsSearchUser(baseHttpRequestContext, rolePermissionFilterModel, queryModel, filterModel);
            }
            else
            {
                snippetQuery = await _snippetDAL.ListSnippets(baseHttpRequestContext, rolePermissionFilterModel, queryModel, filterModel);
            }

            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result = snippetQuery;
            return apiResponse;
        }

        /// <summary>
        /// method to generate the filter parameters from the request
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        private SnippetFilterModel PrepareFilterParameters(string filter)
        {
            if (!(String.IsNullOrEmpty(filter)))
            {
                return JsonConvert.DeserializeObject<SnippetFilterModel>(filter);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// Method to fetch snippet details
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<InputSnippetDetail>> GetSnippetDetails(BaseHttpRequestContext baseHttpRequestContext, long id)
        {
            int orgId = baseHttpRequestContext.OrgId;
            ApiResponse<InputSnippetDetail> apiResposne = new();
            InputSnippetDetail inputSnippet = await _snippetDAL.GetSnippetDetails(orgId, id);
            if (inputSnippet is null)
            {
                apiResposne.Result = null;
                apiResposne.StatusCode = StatusCodes.Status400BadRequest;
                apiResposne.Message = "Failure";
                apiResposne.Errors.Add("The snippet doesnot exist.");
                return apiResposne;
            }

            if (inputSnippet is not null && inputSnippet.SnippetUserAssocs is not null && inputSnippet.SnippetUserAssocs.Any())
                inputSnippet.SnippetUserAssocs = await FormatUserDetails(inputSnippet.SnippetUserAssocs, baseHttpRequestContext);
            inputSnippet.CreatedDate = DateTime.SpecifyKind(inputSnippet.CreatedDate, DateTimeKind.Utc);
            inputSnippet.ModifiedDate = (inputSnippet.ModifiedDate == null) ? null : DateTime.SpecifyKind((DateTime)inputSnippet.ModifiedDate, DateTimeKind.Utc);

            apiResposne.Result = inputSnippet;
            apiResposne.StatusCode = StatusCodes.Status200OK;
            apiResposne.Message = "Success";
            return apiResposne;
        }

        /// <summary>
        /// Method to fetch the profile pic for assigned users
        /// </summary>
        /// <param name="inputSnippetUserAssocs"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        private async Task<ICollection<InputSnippetUserAssoc>> FormatUserDetails(ICollection<InputSnippetUserAssoc> inputSnippetUserAssocs, BaseHttpRequestContext baseHttpRequestContext)
        {
            foreach (var snippetUser in inputSnippetUserAssocs)
            {
                if (snippetUser.UserDetails != null)
                {
                    if (snippetUser.UserDetails.PhotoFileDetailsId != null)
                    {
                        long fileId = (long)snippetUser.UserDetails.PhotoFileDetailsId;
                        var token = baseHttpRequestContext.BearerToken;
                        string interServiceToken = baseHttpRequestContext.InterServiceToken;
                        string fileAPiUrl = _appSettings.ApiUrls["FileServiceUrl"] + "/file/file_details/" + fileId;
                        RestClient restClient = new RestClient(fileAPiUrl, null, token, interServiceToken);
                        var fileApiResponse = await restClient.GetAsync<ApiResponse<FileDetailsOutputForId>>(fileAPiUrl, null);
                        if (fileApiResponse.StatusCode == StatusCodes.Status200OK)
                        {
                            snippetUser.UserDetails.FileDetailsOutput = fileApiResponse.Result;
                        }
                    }

                    snippetUser.CreatedDate = DateTime.Parse(snippetUser.CreatedDate.ToString("u"));
                }
            }
            return inputSnippetUserAssocs;
        }

        /// <summary>
        /// Method to delete a snippet
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> DeleteSnippet(BaseHttpRequestContext baseHttpRequestContext, long id, DeleteObject deleteObject)
        {
            int orgId = baseHttpRequestContext.OrgId;
            long loggedInUser = baseHttpRequestContext.UserId;
            ApiResponse<long?> apiResponse = new();

            SnippetDetail snippet = await _snippetDAL.GetSnippetFromId(orgId, id);
            snippet.SnippetUserAssocs = await _snippetDAL.GetSnippetUserAssoc(orgId, id);

            //TODO:Add owner check that only owner can delete snippet.
            if (snippet is not null)
            {
                snippet.StatusId = (short)Status.Deleted;
                snippet.ModifiedBy = loggedInUser;
                snippet.ModifiedDate = DateTime.UtcNow;
                snippet.DeleteReason = deleteObject == null ? null : deleteObject.DeleteReason;
                if (snippet.SnippetUserAssocs is not null && snippet.SnippetUserAssocs.Any())
                {
                    snippet.SnippetUserAssocs.ToList().ForEach(u => { u.StatusId = (short)Status.Deleted; u.ModifiedBy = loggedInUser; u.ModifiedDate = DateTime.UtcNow; });
                }
                int rows = await _snippetDAL.UpdateSnippet(snippet);

                if (rows > 0)
                {
                    apiResponse.Result = id;
                    apiResponse.Message = "Success";
                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    return apiResponse;
                }
                else
                {
                    apiResponse.Errors.Add("Snippet Details cannot be deleted at this time.");
                }
            }
            else
            {
                apiResponse.Errors.Add("Snippet details can be deleted only by its owner.");
            }
            apiResponse.Message = "Failure";
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            return apiResponse;
        }

        /// <summary>
        /// Method to update a snippet
        /// </summary>
        /// <param name="id"></param>
        /// <param name="inputSnippetDetail"></param>
        /// <param name="orgId"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task<ApiResponse<string>> EditSnippetDetails(long id, SnippetDetail inputSnippetDetail, BaseHttpRequestContext baseHttpRequestContext)
        {
            int orgId = baseHttpRequestContext.OrgId;
            long userId = baseHttpRequestContext.UserId;
            ApiResponse<string> apiResponse = new();
            SnippetDetail snippetFromDb = await _snippetDAL.GetSnippetFromId(orgId, id);

            inputSnippetDetail.OrgId = orgId;
            inputSnippetDetail.ModifiedDate = DateTime.UtcNow;
            inputSnippetDetail.ModifiedBy = userId;
            inputSnippetDetail.CreatedBy = snippetFromDb.CreatedBy;
            inputSnippetDetail.CreatedDate = snippetFromDb.CreatedDate;
            inputSnippetDetail.StatusId = snippetFromDb.StatusId;

            //TODO: Add owner check after RolesAndPermission Chnages started
            //if (snippetFromDb is not null && snippetFromDb.CreatedBy != userId)
            //{
            //    apiResponse.Errors.Add("Snippet details can be edited only by its owner.");
            //    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            //    apiResponse.Message = "Failure";
            //    return apiResponse;
            //}
            if (inputSnippetDetail != null && inputSnippetDetail.SnippetUserAssocs.Any())
            {
                inputSnippetDetail.SnippetUserAssocs.ToList().ForEach(a => { a.OrgId = orgId; a.SnippetDetailsId = id; });
            }

            var removeUserList = await _snippetDAL.GetSnippetUserAssoc(orgId, id);
            var inputUserList = inputSnippetDetail.SnippetUserAssocs;
            inputSnippetDetail.SnippetUserAssocs = null;

            //Get SFDT Text 
            var sfdtResult = await GetSFDTTextData(inputSnippetDetail.Text, baseHttpRequestContext);
            if (sfdtResult.StatusCode != StatusCodes.Status200OK)
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Text to SFDT conversion failed";
                apiResponse.Result = null;
                return apiResponse;
            }
            inputSnippetDetail.SFDTText = sfdtResult.Result.ToString();


            using (var transaction = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
            {
                List<SnippetUserAssoc> userList = EditSnippetUserAssocs(removeUserList, inputUserList, userId, snippetFromDb.CreatedBy);
                inputSnippetDetail.SnippetUserAssocs = userList;

                await _snippetDAL.UpdateSnippet(inputSnippetDetail);

                apiResponse.Result = "Success";
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";

                transaction.Complete();
            }

            return apiResponse;
        }

        private List<SnippetUserAssoc> EditSnippetUserAssocs(ICollection<SnippetUserAssoc> removeUserList, ICollection<SnippetUserAssoc> inputUserList, long userId, long? createdBy)
        {
            List<SnippetUserAssoc> addUserList = new();

            foreach (var userAssoc in inputUserList)
            {
                if (userAssoc.Id > 0)
                {
                    var existingobj = removeUserList.FirstOrDefault(x => x.Id == userAssoc.Id);
                    removeUserList.Remove(existingobj);
                }
                else
                {
                    SnippetUserAssoc userAssocDB = removeUserList.Where(x => x.UserDetailsId == userAssoc.UserDetailsId).FirstOrDefault();
                    if (userAssocDB is null)
                    {
                        userAssoc.CreatedDate = DateTime.UtcNow;
                        userAssoc.CreatedBy = userId;
                        userAssoc.StatusId = (short)Status.Active;
                        addUserList.Add(userAssoc);
                    }
                    else
                    {
                        if (userAssocDB.StatusId == (short)Status.Deleted)
                        {
                            userAssocDB.StatusId = (short)Status.Active;
                            userAssocDB.ModifiedDate = DateTime.UtcNow;
                            userAssocDB.ModifiedBy = userId;
                            addUserList.Add(userAssocDB);

                        }
                        removeUserList.Remove(userAssocDB);

                    }
                }
            }

            foreach (var userAssoc in removeUserList.Where(x => x.StatusId == (short)Status.Active))
            {
                userAssoc.StatusId = (short)Status.Deleted;
                userAssoc.ModifiedDate = DateTime.UtcNow;
                userAssoc.ModifiedBy = userId;
            }
            addUserList = addUserList.Concat(removeUserList).ToList();
            //UserAssoc for snippet owner should not be deleted/edited
            if (addUserList is not null && addUserList.Any())
                addUserList.Remove(addUserList.Where(x => x.UserDetailsId == createdBy).FirstOrDefault());
            return addUserList;
        }
        private async Task<ApiResponse<JObject>> GetSFDTTextData(string htmlText, BaseHttpRequestContext baseHttpRequestContext)
        { 
            var token = baseHttpRequestContext.BearerToken;
            string interServiceToken = baseHttpRequestContext.InterServiceToken;
            string synFusionAPiUrl = _appSettings.ApiUrls["SyncFusionServiceUrl"] + "/syncfusion/ConvertHTMLTOSFDT";
            RestClient restClient = new RestClient(synFusionAPiUrl, null, token, interServiceToken);
            return await restClient.PostAsync<ApiResponse<JObject>>(synFusionAPiUrl, htmlText);
            ;
        }
    }
}