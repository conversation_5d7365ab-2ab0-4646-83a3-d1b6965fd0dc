﻿using AutoMapper;
using Capstone2.Framework.RestApi;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.Utility.Interfaces;
using Capstone2.RestServices.Utility.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Transactions;

namespace Capstone2.RestServices.Utility.Services
{
    public class LetterTemplateBAL : ILetterTemplateBAL
    {
        public readonly ILetterTemplateDAL _letterTemplateDAL;
        public IMapper _mapper;
        public readonly AppSettings _appSettings;
        public readonly IRolesBAL _rolesBAL;

        public LetterTemplateBAL(ILetterTemplateDAL letterTemplateDAL, IRolesBAL rolesBAL, IMapper mapper, IOptions<AppSettings> appSettings)
        {
            _letterTemplateDAL = letterTemplateDAL;
            _mapper = mapper;
            _rolesBAL = rolesBAL;
            _appSettings = appSettings.Value;
        }

        /// <summary>
        /// Method to save a entry in LetterTemplate
        /// </summary>
        /// <param name="letterTemplate"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> AddLetterTemplateBAL(LetterTemplate inputLetterTemplate, BaseHttpRequestContext baseHttpRequestContext)
        {

            //CheckIftemplateSubTypeIsPresent
            if (inputLetterTemplate.TemplateTypeId == (short)TemplateType.Finance_Template && inputLetterTemplate.TemplateSubTypeID.HasValue)
            {
                var result = await VerifyTemplateExits(inputLetterTemplate.TemplateSubTypeID.Value, baseHttpRequestContext);
                if (result.Result)
                {
                    ApiResponse<long?> apiResponse = new()
                    {
                        StatusCode = StatusCodes.Status400BadRequest,
                        Message = "Template with subtype already exists.",
                        Result = 0
                    };
                    return apiResponse;
                }
            }

            int orgId = baseHttpRequestContext.OrgId;
            long loggedInUser = baseHttpRequestContext.UserId;
            if (inputLetterTemplate != null && (inputLetterTemplate.LetterTemplateUserAssocs.Count == 0 || inputLetterTemplate.LetterTemplateUserAssocs.Where(x => x.UserDetailsId == loggedInUser).Count() == 0))
            {
                LetterTemplateUserAssoc createdUserAssoc = new LetterTemplateUserAssoc()
                {
                    UserDetailsId = loggedInUser
                };
                inputLetterTemplate.LetterTemplateUserAssocs.Add(createdUserAssoc);
            }
            if (inputLetterTemplate != null && inputLetterTemplate.LetterTemplateUserAssocs.Any())
            {
                inputLetterTemplate.LetterTemplateUserAssocs.ToList().ForEach(a => { a.OrgId = orgId; a.ModifiedBy = loggedInUser; a.StatusId = (short)Status.Active; });

            }
            inputLetterTemplate.CreatedBy = loggedInUser;
            inputLetterTemplate.OrgId = orgId;
            inputLetterTemplate.StatusId = (short)Status.Active;
            inputLetterTemplate.CreatedDate = DateTime.UtcNow;

            DateTime.SpecifyKind(inputLetterTemplate.CreatedDate, DateTimeKind.Utc);
            long id = await _letterTemplateDAL.AddLetterTemplateAsync(inputLetterTemplate);
            if (id > 0)
            {
                ApiResponse<long?> apiResponse = new()
                {
                    StatusCode = StatusCodes.Status200OK,
                    Message = "Success",
                    Result = id
                };
                return apiResponse;
            }
            else
            {
                ApiResponse<long?> apiResponse = new()
                {
                    StatusCode = StatusCodes.Status400BadRequest,
                    Message = "Failure",
                    Result = null
                };
                return apiResponse;
            }
        }

        /// <summary>
        /// Method to check if the entered letter template name is unique
        /// </summary>
        /// <param name="templateTypeId"></param>
        /// <param name="search_term"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<ApiResponse<bool>> CheckLetterTemplateName(long templateTypeId, string search_term, int orgId)
        {
            bool nameBool = await _letterTemplateDAL.CheckLetterTemplateNameDAL(templateTypeId, search_term, orgId);
            var apiResponse = new ApiResponse<bool>
            {
                Result = nameBool,
                Message = "Success",
                StatusCode = StatusCodes.Status200OK
            };
            return apiResponse;
        }

        /// <summary>
        /// Method to fetch Letter Template details
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<LetterTemplateView>> GetLetterTemplate(BaseHttpRequestContext baseHttpRequestContext, long id)
        {
            int orgId = baseHttpRequestContext.OrgId;
            ApiResponse<LetterTemplateView> apiResposne = new();
            LetterTemplateView letterFromDB = await _letterTemplateDAL.GetLetterTemplateDAL(orgId, id);
            if (letterFromDB is null)
            {
                apiResposne.Result = null;
                apiResposne.StatusCode = StatusCodes.Status400BadRequest;
                apiResposne.Message = "Failure";
                apiResposne.Errors.Add("The Letter Template doesnot exist.");
                return apiResposne;
            }

            if (letterFromDB is not null && letterFromDB.FileDetailsId is not null)
            {
                letterFromDB = await FormatLetterTemplate(letterFromDB, baseHttpRequestContext);
            }
            if (letterFromDB is not null)
            {

                letterFromDB.CreatedDate = DateTime.SpecifyKind(letterFromDB.CreatedDate, DateTimeKind.Utc);
                letterFromDB.ModifiedDate = (letterFromDB.ModifiedDate == null) ? null : DateTime.SpecifyKind((DateTime)letterFromDB.ModifiedDate, DateTimeKind.Utc);

                apiResposne.Result = letterFromDB;
                apiResposne.StatusCode = StatusCodes.Status200OK;
                apiResposne.Message = "Success";
                return apiResposne;
            }
            else
            {
                apiResposne.Result = null;
                apiResposne.StatusCode = StatusCodes.Status500InternalServerError;
                apiResposne.Message = "Data for given letter templateId is not found";
                return apiResposne;
            }
        }

        /// <summary>
        /// Method to fetch the Filedetailsoutput for the 
        /// </summary>
        /// <param name="inputSnippetUserAssocs"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        private async Task<LetterTemplateView> FormatLetterTemplate(LetterTemplateView inputLetterTemplate, BaseHttpRequestContext baseHttpRequestContext)
        {
            long fileId = (long)inputLetterTemplate.FileDetailsId;
            var token = baseHttpRequestContext.BearerToken;
            string interServiceToken = baseHttpRequestContext.InterServiceToken;
            string fileAPiUrl = _appSettings.ApiUrls["FileServiceUrl"] + "/file/file_details/" + fileId;
            RestClient restClient = new RestClient(fileAPiUrl, null, token, interServiceToken);
            var fileApiResponse = await restClient.GetAsync<ApiResponse<FileDetailsOutputForId>>(fileAPiUrl, null);
            if (fileApiResponse.StatusCode == StatusCodes.Status200OK)
            {
                inputLetterTemplate.FileDetailsOutput = fileApiResponse.Result;
            }

            return inputLetterTemplate;
        }

        /// <summary>
        /// Method to generate a paginated list of letter templates based on search criteria
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        public async Task<ApiResponse<QueryResultList<ListLetterTemplate>>> ListLetterTemplatesBAL(BaseHttpRequestContext baseHttpRequestContext, QueryModel queryModel)
        {
            ApiResponse<QueryResultList<ListLetterTemplate>> apiResponse = new();

            RolesFilterModel rolesFilterModel = new RolesFilterModel() { RolesId = new List<int?>() { baseHttpRequestContext.RoleId } };

            RolePermissionFilterModel rolePermissionFilterModel = new RolePermissionFilterModel();

            LetterTemplateFilterModel letterTemplateFilter = null;
            if (queryModel.Filter != null)
            {
                letterTemplateFilter = JsonConvert.DeserializeObject<LetterTemplateFilterModel>(queryModel.Filter);
            }

            if (letterTemplateFilter?.TemplateType?.Count == 1)
            {
                if (letterTemplateFilter?.TemplateType[0] == (short)TemplateTypesEnum.Letter_Template)
                    rolePermissionFilterModel =
                   await _rolesBAL.IsDeleteOnlyPermissionsBAL(baseHttpRequestContext, ModulesEnum.LetterTemplates);
                else if (letterTemplateFilter?.TemplateType[0] == (short)TemplateTypesEnum.SMS_Template)
                    rolePermissionFilterModel =
                       await _rolesBAL.IsDeleteOnlyPermissionsBAL(baseHttpRequestContext, ModulesEnum.SmsEmailTemplates);
            }

            var queryList = await _letterTemplateDAL.ListLetterTemplateDAL(baseHttpRequestContext, rolePermissionFilterModel, queryModel, letterTemplateFilter);

            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result = queryList;
            return apiResponse;
        }


        /// <summary>
        /// Method to delete a letter template
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> DeleteLetterTemplateBAL(BaseHttpRequestContext baseHttpRequestContext, long id, LetterDeleteObject letterDelete)
        {
            int orgId = baseHttpRequestContext.OrgId;
            long loggedInUser = baseHttpRequestContext.UserId;
            ApiResponse<long?> apiResponse = new();

            if (string.IsNullOrEmpty(letterDelete.DeleteReason))
            {
                apiResponse.Errors.Add("Can not Delete the Letter Template without Delete Reason");
                apiResponse.Message = "Failure";
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                return apiResponse;
            }

            LetterTemplate letterTemplate = await _letterTemplateDAL.GetletterTemplateFromId(orgId, id);
            letterTemplate.LetterTemplateUserAssocs = await _letterTemplateDAL.GetletterTemplateUserAssoc(orgId, id);

            // TODO: Add CreatedBy check once role and Permission changes are done.
            if (letterTemplate is not null)
            {
                letterTemplate.StatusId = (short)Status.Deleted;
                letterTemplate.ModifiedBy = loggedInUser;
                letterTemplate.ModifiedDate = DateTime.UtcNow;
                letterTemplate.DeleteReason = letterDelete.DeleteReason;
                if (letterTemplate.LetterTemplateUserAssocs is not null && letterTemplate.LetterTemplateUserAssocs.Any())
                {
                    letterTemplate.LetterTemplateUserAssocs.ToList().ForEach(u => { u.StatusId = (short)Status.Deleted; u.ModifiedBy = loggedInUser; u.ModifiedDate = DateTime.UtcNow; });
                }

                int rows = await _letterTemplateDAL.UpdateLetterTemplate(letterTemplate);

                if (rows > 0)
                {
                    apiResponse.Result = id;
                    apiResponse.Message = "Success";
                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    return apiResponse;
                }
                else
                {
                    apiResponse.Errors.Add("letter template cannot be deleted at this time.");
                }
            }
            else
            {
                apiResponse.Errors.Add("Letter cannot be found.");
            }
            apiResponse.Message = "Failure";
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            return apiResponse;
        }

        /// <summary>
        /// Method to update a Letter Tempplate
        /// </summary>
        /// <param name="id"></param>
        /// <param name="inputLetterTemplate"></param>
        /// <param name="orgId"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task<ApiResponse<string>> EditLetterTemplateBAL(long id, LetterTemplate inputLetterTemplate, BaseHttpRequestContext baseHttpRequestContext)
        {
            int orgId = baseHttpRequestContext.OrgId;
            long userId = baseHttpRequestContext.UserId;
            ApiResponse<string> apiResponse = new();
            LetterTemplate letterFromDb = await _letterTemplateDAL.GetletterTemplateFromId(orgId, id);

            inputLetterTemplate.OrgId = orgId;
            inputLetterTemplate.ModifiedDate = DateTime.UtcNow;
            inputLetterTemplate.ModifiedBy = userId;
            inputLetterTemplate.CreatedBy = letterFromDb.CreatedBy;
            inputLetterTemplate.CreatedDate = letterFromDb.CreatedDate;
            inputLetterTemplate.StatusId = letterFromDb.StatusId;
            inputLetterTemplate.Id = letterFromDb.Id;

            //TODO: Add CreatedBy check once rolesAndPermission changes are done.
            //if (letterFromDb is not null && letterFromDb.CreatedBy != userId)
            //{
            //    apiResponse.Errors.Add("Letter Templates can be edited only by its owner.");
            //    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            //    apiResponse.Message = "Failure";
            //    return apiResponse;
            //}

            //TODO: Add check to restrict edit action for lodgment,Deposit,statement template
            //if (letterFromDb is not null && letterFromDb.TemplateSubTypeID.HasValue)
            //{
            //    if(letterFromDb.TemplateSubTypeID != (short)TemplateSubTypesEnum.GeneralTemplate)
            //    {
            //        apiResponse.Errors.Add("Does not have permission to edit template.");
            //        apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            //        apiResponse.Message = "Failure";
            //        return apiResponse;
            //    }
            //}

            if (inputLetterTemplate != null && inputLetterTemplate.LetterTemplateUserAssocs.Any())
            {
                inputLetterTemplate.LetterTemplateUserAssocs.ToList().ForEach(a => { a.OrgId = orgId; a.LetterTemplatesId = id; });
            }

            var removeUserList = await _letterTemplateDAL.GetletterTemplateUserAssoc(orgId, id);
            var inputUserList = inputLetterTemplate.LetterTemplateUserAssocs;
            inputLetterTemplate.LetterTemplateUserAssocs = null;
            using (var transaction = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
            {
                List<LetterTemplateUserAssoc> userList = EditLetterTemplateUserAssocs(removeUserList, inputUserList, userId, letterFromDb.CreatedBy);
                inputLetterTemplate.LetterTemplateUserAssocs = userList;

                await _letterTemplateDAL.UpdateLetterTemplate(inputLetterTemplate);

                apiResponse.Result = "Success";
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";

                transaction.Complete();
            }

            return apiResponse;
        }

        private List<LetterTemplateUserAssoc> EditLetterTemplateUserAssocs(ICollection<LetterTemplateUserAssoc> removeUserList, ICollection<LetterTemplateUserAssoc> inputUserList, long userId, long? createdBy)
        {
            List<LetterTemplateUserAssoc> addUserList = new();
            foreach (var userAssoc in inputUserList)
            {
                if (userAssoc.Id > 0)
                {
                    var existingobj = removeUserList.FirstOrDefault(x => x.Id == userAssoc.Id);
                    removeUserList.Remove(existingobj);
                }
                else
                {
                    LetterTemplateUserAssoc userAssocDB = removeUserList.Where(x => x.UserDetailsId == userAssoc.UserDetailsId).FirstOrDefault();
                    if (userAssocDB is null)
                    {
                        userAssoc.CreatedDate = DateTime.UtcNow;
                        userAssoc.StatusId = (short)Status.Active;
                        addUserList.Add(userAssoc);
                    }
                    else
                    {
                        if (userAssocDB.StatusId == (short)Status.Deleted)
                        {
                            userAssocDB.StatusId = (short)Status.Active;
                            userAssocDB.ModifiedDate = DateTime.UtcNow;
                            userAssocDB.ModifiedBy = userId;
                            addUserList.Add(userAssocDB);

                        }
                        removeUserList.Remove(userAssocDB);
                    }
                }
            }

            foreach (var userAssoc in removeUserList.Where(x => x.StatusId == (short)Status.Active))
            {
                userAssoc.StatusId = (short)Status.Deleted;
                userAssoc.ModifiedDate = DateTime.UtcNow;
                userAssoc.ModifiedBy = userId;
            }
            addUserList = addUserList.Concat(removeUserList).ToList();
            //UserAssoc for snippet owner should not be deleted/edited
            if (addUserList is not null && addUserList.Any())
                addUserList.Remove(addUserList.Where(x => x.UserDetailsId == createdBy).FirstOrDefault());
            return addUserList;
        }

        /// <summary>
        /// Method to fetch Letter Templates for given User
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<LetterTemplateForUser>> LetterTemplatesForUserBAL(BaseHttpRequestContext baseHttpRequestContext, long id, long templateTypeId)
        {
            int orgId = baseHttpRequestContext.OrgId;
            ApiResponse<LetterTemplateForUser> apiResposne = new();
            List<int> listOfCompaniesForUser = await _letterTemplateDAL.GetCompaniesForUser(orgId, id);
            LetterTemplateForUser result = new();
            result = await _letterTemplateDAL.GetUserLetterTemplates(orgId, id, templateTypeId);
            List<CompanyLetterTemplate> companyLetterList = new();
            if (listOfCompaniesForUser != null)
            {
                companyLetterList = await _letterTemplateDAL.GetCompanyLetterList(orgId, listOfCompaniesForUser, templateTypeId);
            }

            result.CompanyLetterTemplates = companyLetterList;
            apiResposne.Result = result;
            apiResposne.StatusCode = StatusCodes.Status200OK;
            apiResposne.Message = "Success";
            return apiResposne;
        }

        /// <summary>
        /// Method to check if the entered templatetype already present
        /// </summary>
        /// <param name="templateTypeId"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<bool>> VerifyTemplateExits(long templateSubTypeId, BaseHttpRequestContext baseHttpRequestContext)
        {
            var apiResponse = new ApiResponse<bool>();
            if (templateSubTypeId != (short)TemplateSubTypesEnum.GeneralTemplate)
            {
                bool nameBool = await _letterTemplateDAL.VerifyTemplateExits(templateSubTypeId, baseHttpRequestContext.OrgId);

                apiResponse.Result = nameBool;
                apiResponse.Message = "Success";
                apiResponse.StatusCode = StatusCodes.Status200OK;
                return apiResponse;
            }
            apiResponse.Result = false;
            apiResponse.Message = "Success";
            apiResponse.StatusCode = StatusCodes.Status200OK;
            return apiResponse;
        }

        public async Task<ApiResponse<LetterTemplate>> FettchLetterTemplateFromSubType(BaseHttpRequestContext baseHttpRequestContext, short templateSubTypeId, short templateTypeId)
        {
            int orgId = baseHttpRequestContext.OrgId;
            ApiResponse<LetterTemplate> apiResposne = new();
            LetterTemplate letterFromDB = await _letterTemplateDAL.GetletterTemplateFromSubType(orgId, templateTypeId, templateSubTypeId);
            if (letterFromDB is not null)
            {
                apiResposne.Result = letterFromDB;
                apiResposne.StatusCode = StatusCodes.Status200OK;
                apiResposne.Message = "Success";
                return apiResposne;
            }


            apiResposne.Result = null;
            apiResposne.StatusCode = StatusCodes.Status400BadRequest;
            apiResposne.Message = "Template for given type  is not found";
            return apiResposne;

        }
    }
}