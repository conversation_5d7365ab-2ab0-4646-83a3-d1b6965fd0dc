using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Capstone2.RestServices.MainService.Services.Performance
{
    /// <summary>
    /// Background service to monitor and optimize thread pool performance
    /// Based on memory dump analysis findings
    /// </summary>
    public class ThreadPoolOptimizationService : BackgroundService
    {
        private readonly ILogger<ThreadPoolOptimizationService> _logger;
        private readonly ThreadPoolOptimizationOptions _options;
        private Timer _monitoringTimer;

        public ThreadPoolOptimizationService(
            ILogger<ThreadPoolOptimizationService> logger,
            IOptions<ThreadPoolOptimizationOptions> options)
        {
            _logger = logger;
            _options = options.Value;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("ThreadPoolOptimizationService started");

            // Start monitoring timer
            _monitoringTimer = new Timer(MonitorThreadPool, null, TimeSpan.Zero, TimeSpan.FromMinutes(1));

            // Keep the service running
            while (!stoppingToken.IsCancellationRequested)
            {
                await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken).ConfigureAwait(false);
            }
        }

        private void MonitorThreadPool(object state)
        {
            try
            {
                ThreadPool.GetAvailableThreads(out int workerThreads, out int completionPortThreads);
                ThreadPool.GetMaxThreads(out int maxWorkerThreads, out int maxCompletionPortThreads);
                ThreadPool.GetMinThreads(out int minWorkerThreads, out int minCompletionPortThreads);

                var workerThreadsInUse = maxWorkerThreads - workerThreads;
                var completionPortThreadsInUse = maxCompletionPortThreads - completionPortThreads;

                // Log thread pool statistics
                _logger.LogInformation(
                    "ThreadPool Stats - Worker: {WorkerInUse}/{MaxWorker} ({WorkerAvailable} available), " +
                    "CompletionPort: {CompletionInUse}/{MaxCompletion} ({CompletionAvailable} available)",
                    workerThreadsInUse, maxWorkerThreads, workerThreads,
                    completionPortThreadsInUse, maxCompletionPortThreads, completionPortThreads);

                // Check for thread starvation
                var workerUtilization = (double)workerThreadsInUse / maxWorkerThreads;
                var completionUtilization = (double)completionPortThreadsInUse / maxCompletionPortThreads;

                if (workerUtilization > 0.8 || completionUtilization > 0.8)
                {
                    _logger.LogWarning(
                        "High thread pool utilization detected - Worker: {WorkerUtil:P}, Completion: {CompletionUtil:P}",
                        workerUtilization, completionUtilization);

                    // Attempt to increase thread pool size if configured
                    if (_options.EnableDynamicAdjustment)
                    {
                        AdjustThreadPoolSize(workerUtilization, completionUtilization);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error monitoring thread pool");
            }
        }

        private void AdjustThreadPoolSize(double workerUtilization, double completionUtilization)
        {
            try
            {
                ThreadPool.GetMaxThreads(out int maxWorkerThreads, out int maxCompletionPortThreads);
                ThreadPool.GetMinThreads(out int minWorkerThreads, out int minCompletionPortThreads);

                var newMaxWorker = maxWorkerThreads;
                var newMaxCompletion = maxCompletionPortThreads;
                var newMinWorker = minWorkerThreads;
                var newMinCompletion = minCompletionPortThreads;

                // Increase max threads if utilization is high
                if (workerUtilization > 0.8 && maxWorkerThreads < _options.MaxAllowedWorkerThreads)
                {
                    newMaxWorker = Math.Min(maxWorkerThreads + _options.ThreadIncrementSize, _options.MaxAllowedWorkerThreads);
                }

                if (completionUtilization > 0.8 && maxCompletionPortThreads < _options.MaxAllowedCompletionThreads)
                {
                    newMaxCompletion = Math.Min(maxCompletionPortThreads + _options.ThreadIncrementSize, _options.MaxAllowedCompletionThreads);
                }

                // Increase min threads if needed
                if (workerUtilization > 0.6 && minWorkerThreads < newMaxWorker / 4)
                {
                    newMinWorker = Math.Min(minWorkerThreads + _options.ThreadIncrementSize / 2, newMaxWorker / 4);
                }

                if (completionUtilization > 0.6 && minCompletionPortThreads < newMaxCompletion / 4)
                {
                    newMinCompletion = Math.Min(minCompletionPortThreads + _options.ThreadIncrementSize / 2, newMaxCompletion / 4);
                }

                // Apply changes if needed
                if (newMaxWorker != maxWorkerThreads || newMaxCompletion != maxCompletionPortThreads)
                {
                    ThreadPool.SetMaxThreads(newMaxWorker, newMaxCompletion);
                    _logger.LogInformation(
                        "Adjusted max threads - Worker: {OldWorker} -> {NewWorker}, Completion: {OldCompletion} -> {NewCompletion}",
                        maxWorkerThreads, newMaxWorker, maxCompletionPortThreads, newMaxCompletion);
                }

                if (newMinWorker != minWorkerThreads || newMinCompletion != minCompletionPortThreads)
                {
                    ThreadPool.SetMinThreads(newMinWorker, newMinCompletion);
                    _logger.LogInformation(
                        "Adjusted min threads - Worker: {OldWorker} -> {NewWorker}, Completion: {OldCompletion} -> {NewCompletion}",
                        minWorkerThreads, newMinWorker, minCompletionPortThreads, newMinCompletion);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adjusting thread pool size");
            }
        }

        public override void Dispose()
        {
            _monitoringTimer?.Dispose();
            base.Dispose();
        }
    }

    public class ThreadPoolOptimizationOptions
    {
        public bool EnableDynamicAdjustment { get; set; } = true;
        public int MaxAllowedWorkerThreads { get; set; } = 1000;
        public int MaxAllowedCompletionThreads { get; set; } = 1000;
        public int ThreadIncrementSize { get; set; } = 50;
    }
}
