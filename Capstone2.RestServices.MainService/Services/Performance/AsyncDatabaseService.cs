using System;
using System.Data;
using System.Data.SqlClient;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Capstone2.RestServices.MainService.Services.Performance
{
    /// <summary>
    /// Optimized database service based on memory dump analysis
    /// Prevents connection pool exhaustion and implements proper async patterns
    /// </summary>
    public class AsyncDatabaseService
    {
        private readonly ILogger<AsyncDatabaseService> _logger;
        private readonly AsyncDatabaseOptions _options;
        private readonly SemaphoreSlim _connectionLimiter;

        public AsyncDatabaseService(
            ILogger<AsyncDatabaseService> logger,
            IOptions<AsyncDatabaseOptions> options)
        {
            _logger = logger;
            _options = options.Value;
            _connectionLimiter = new SemaphoreSlim(_options.MaxConcurrentConnections, _options.MaxConcurrentConnections);
        }

        /// <summary>
        /// Execute database operation with proper async patterns and connection management
        /// </summary>
        public async Task<T> ExecuteDatabaseOperationAsync<T>(
            string connectionString,
            Func<SqlConnection, CancellationToken, Task<T>> operation,
            CancellationToken cancellationToken = default)
        {
            await _connectionLimiter.WaitAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                using var connection = new SqlConnection(connectionString);
                
                // Use async connection opening to prevent thread blocking
                await connection.OpenAsync(cancellationToken).ConfigureAwait(false);
                
                var startTime = DateTime.UtcNow;
                try
                {
                    var result = await operation(connection, cancellationToken).ConfigureAwait(false);
                    
                    var duration = DateTime.UtcNow - startTime;
                    if (duration.TotalMilliseconds > _options.SlowQueryThresholdMs)
                    {
                        _logger.LogWarning("Slow database operation detected: {Duration}ms", duration.TotalMilliseconds);
                    }
                    
                    return result;
                }
                catch (Exception ex)
                {
                    var duration = DateTime.UtcNow - startTime;
                    _logger.LogError(ex, "Database operation failed after {Duration}ms", duration.TotalMilliseconds);
                    throw;
                }
            }
            finally
            {
                _connectionLimiter.Release();
            }
        }

        /// <summary>
        /// Execute database operation with retry logic
        /// </summary>
        public async Task<T> ExecuteDatabaseOperationWithRetryAsync<T>(
            string connectionString,
            Func<SqlConnection, CancellationToken, Task<T>> operation,
            int maxRetries = 3,
            CancellationToken cancellationToken = default)
        {
            var attempt = 0;
            Exception lastException = null;

            while (attempt <= maxRetries)
            {
                try
                {
                    return await ExecuteDatabaseOperationAsync(connectionString, operation, cancellationToken).ConfigureAwait(false);
                }
                catch (Exception ex) when (attempt < maxRetries && IsRetriableException(ex))
                {
                    lastException = ex;
                    attempt++;
                    
                    var delay = TimeSpan.FromMilliseconds(Math.Pow(2, attempt) * 500); // Exponential backoff
                    _logger.LogWarning(ex, "Database operation failed on attempt {Attempt}, retrying in {Delay}ms", 
                        attempt, delay.TotalMilliseconds);
                    
                    await Task.Delay(delay, cancellationToken).ConfigureAwait(false);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Database operation failed on attempt {Attempt} (non-retriable)", attempt + 1);
                    throw;
                }
            }

            _logger.LogError(lastException, "Database operation failed after {MaxRetries} retries", maxRetries);
            throw lastException ?? new InvalidOperationException("Unexpected retry loop exit");
        }

        /// <summary>
        /// Execute multiple database operations concurrently with proper async patterns
        /// </summary>
        public async Task<T[]> ExecuteMultipleDatabaseOperationsAsync<T>(
            string connectionString,
            Func<SqlConnection, CancellationToken, Task<T>>[] operations,
            CancellationToken cancellationToken = default)
        {
            if (operations == null || operations.Length == 0)
                return Array.Empty<T>();

            var tasks = new Task<T>[operations.Length];
            
            for (int i = 0; i < operations.Length; i++)
            {
                var operation = operations[i];
                tasks[i] = ExecuteDatabaseOperationAsync(connectionString, operation, cancellationToken);
            }

            try
            {
                return await Task.WhenAll(tasks).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "One or more database operations failed in batch execution");
                throw;
            }
        }

        /// <summary>
        /// Execute database command with proper async patterns
        /// </summary>
        public async Task<T> ExecuteCommandAsync<T>(
            string connectionString,
            string commandText,
            Func<SqlCommand, CancellationToken, Task<T>> commandExecution,
            SqlParameter[] parameters = null,
            CommandType commandType = CommandType.Text,
            int? timeoutSeconds = null,
            CancellationToken cancellationToken = default)
        {
            return await ExecuteDatabaseOperationAsync(connectionString, async (connection, ct) =>
            {
                using var command = new SqlCommand(commandText, connection)
                {
                    CommandType = commandType,
                    CommandTimeout = timeoutSeconds ?? _options.DefaultCommandTimeoutSeconds
                };

                if (parameters != null)
                {
                    command.Parameters.AddRange(parameters);
                }

                return await commandExecution(command, ct).ConfigureAwait(false);
            }, cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        /// Execute scalar query with proper async patterns
        /// </summary>
        public async Task<T> ExecuteScalarAsync<T>(
            string connectionString,
            string commandText,
            SqlParameter[] parameters = null,
            CommandType commandType = CommandType.Text,
            int? timeoutSeconds = null,
            CancellationToken cancellationToken = default)
        {
            return await ExecuteCommandAsync(connectionString, commandText, async (command, ct) =>
            {
                var result = await command.ExecuteScalarAsync(ct).ConfigureAwait(false);
                return result == null || result == DBNull.Value ? default(T) : (T)Convert.ChangeType(result, typeof(T));
            }, parameters, commandType, timeoutSeconds, cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        /// Execute non-query command with proper async patterns
        /// </summary>
        public async Task<int> ExecuteNonQueryAsync(
            string connectionString,
            string commandText,
            SqlParameter[] parameters = null,
            CommandType commandType = CommandType.Text,
            int? timeoutSeconds = null,
            CancellationToken cancellationToken = default)
        {
            return await ExecuteCommandAsync(connectionString, commandText, async (command, ct) =>
            {
                return await command.ExecuteNonQueryAsync(ct).ConfigureAwait(false);
            }, parameters, commandType, timeoutSeconds, cancellationToken).ConfigureAwait(false);
        }

        private static bool IsRetriableException(Exception ex)
        {
            if (ex is SqlException sqlEx)
            {
                // Retry on transient SQL errors
                return sqlEx.Number == 2 ||      // Timeout
                       sqlEx.Number == 53 ||     // Network error
                       sqlEx.Number == 121 ||    // Semaphore timeout
                       sqlEx.Number == 1205 ||   // Deadlock
                       sqlEx.Number == 1222 ||   // Lock request timeout
                       sqlEx.Number == 8645 ||   // Memory error
                       sqlEx.Number == 8651;     // Low memory condition
            }

            return ex is TimeoutException ||
                   ex is TaskCanceledException ||
                   (ex is OperationCanceledException && !(ex is TaskCanceledException));
        }

        public void Dispose()
        {
            _connectionLimiter?.Dispose();
        }
    }

    public class AsyncDatabaseOptions
    {
        public int MaxConcurrentConnections { get; set; } = 100;
        public int DefaultCommandTimeoutSeconds { get; set; } = 30;
        public int SlowQueryThresholdMs { get; set; } = 5000;
    }
}
