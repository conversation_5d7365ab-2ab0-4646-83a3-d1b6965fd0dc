using System;
using System.Collections.Concurrent;
using System.Linq;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Capstone2.RestServices.MainService.Services.Performance
{
    /// <summary>
    /// Optimized Medicare API service based on memory dump analysis
    /// Implements proper async patterns to prevent thread pool starvation
    /// </summary>
    public class AsyncMedicareService
    {
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly ILogger<AsyncMedicareService> _logger;
        private readonly AsyncMedicareOptions _options;
        private readonly SemaphoreSlim _concurrencyLimiter;
        private readonly ConcurrentDictionary<string, SemaphoreSlim> _endpointLimiters;

        public AsyncMedicareService(
            IHttpClientFactory httpClientFactory,
            ILogger<AsyncMedicareService> logger,
            IOptions<AsyncMedicareOptions> options)
        {
            _httpClientFactory = httpClientFactory;
            _logger = logger;
            _options = options.Value;
            _concurrencyLimiter = new SemaphoreSlim(_options.MaxConcurrentRequests, _options.MaxConcurrentRequests);
            _endpointLimiters = new ConcurrentDictionary<string, SemaphoreSlim>();
        }

        /// <summary>
        /// Execute Medicare API call with proper async patterns and concurrency control
        /// </summary>
        public async Task<T> ExecuteMedicareApiCallAsync<T>(
            string endpoint,
            Func<HttpClient, CancellationToken, Task<T>> apiCall,
            CancellationToken cancellationToken = default)
        {
            // Get endpoint-specific limiter
            var endpointLimiter = _endpointLimiters.GetOrAdd(endpoint, 
                _ => new SemaphoreSlim(_options.MaxConcurrentRequestsPerEndpoint, _options.MaxConcurrentRequestsPerEndpoint));

            // Wait for global concurrency slot
            await _concurrencyLimiter.WaitAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                // Wait for endpoint-specific slot
                await endpointLimiter.WaitAsync(cancellationToken).ConfigureAwait(false);
                try
                {
                    using var httpClient = _httpClientFactory.CreateClient("medicare");
                    
                    // Configure timeout for this specific request
                    using var timeoutCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
                    timeoutCts.CancelAfter(_options.RequestTimeout);

                    var startTime = DateTime.UtcNow;
                    try
                    {
                        var result = await apiCall(httpClient, timeoutCts.Token).ConfigureAwait(false);
                        
                        var duration = DateTime.UtcNow - startTime;
                        _logger.LogDebug("Medicare API call to {Endpoint} completed in {Duration}ms", 
                            endpoint, duration.TotalMilliseconds);
                        
                        return result;
                    }
                    catch (OperationCanceledException) when (timeoutCts.Token.IsCancellationRequested && !cancellationToken.IsCancellationRequested)
                    {
                        var duration = DateTime.UtcNow - startTime;
                        _logger.LogWarning("Medicare API call to {Endpoint} timed out after {Duration}ms", 
                            endpoint, duration.TotalMilliseconds);
                        throw new TimeoutException($"Medicare API call to {endpoint} timed out after {duration.TotalMilliseconds}ms");
                    }
                    catch (Exception ex)
                    {
                        var duration = DateTime.UtcNow - startTime;
                        _logger.LogError(ex, "Medicare API call to {Endpoint} failed after {Duration}ms", 
                            endpoint, duration.TotalMilliseconds);
                        throw;
                    }
                }
                finally
                {
                    endpointLimiter.Release();
                }
            }
            finally
            {
                _concurrencyLimiter.Release();
            }
        }

        /// <summary>
        /// Execute multiple Medicare API calls concurrently with proper async patterns
        /// </summary>
        public async Task<T[]> ExecuteMultipleMedicareApiCallsAsync<T>(
            (string endpoint, Func<HttpClient, CancellationToken, Task<T>> apiCall)[] calls,
            CancellationToken cancellationToken = default)
        {
            if (calls == null || calls.Length == 0)
                return Array.Empty<T>();

            var tasks = new Task<T>[calls.Length];
            
            for (int i = 0; i < calls.Length; i++)
            {
                var call = calls[i];
                tasks[i] = ExecuteMedicareApiCallAsync(call.endpoint, call.apiCall, cancellationToken);
            }

            try
            {
                return await Task.WhenAll(tasks).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "One or more Medicare API calls failed in batch execution");
                
                // Collect results from successful tasks
                var results = new T[calls.Length];
                var hasErrors = false;
                
                for (int i = 0; i < tasks.Length; i++)
                {
                    if (tasks[i].IsCompletedSuccessfully)
                    {
                        results[i] = tasks[i].Result;
                    }
                    else
                    {
                        hasErrors = true;
                        _logger.LogError(tasks[i].Exception, "Medicare API call {Index} failed", i);
                    }
                }
                
                if (hasErrors)
                {
                    throw new AggregateException("One or more Medicare API calls failed", 
                        Array.FindAll(tasks, t => t.IsFaulted).Select(t => t.Exception).ToArray());
                }
                
                return results;
            }
        }

        /// <summary>
        /// Execute Medicare API call with retry logic
        /// </summary>
        public async Task<T> ExecuteMedicareApiCallWithRetryAsync<T>(
            string endpoint,
            Func<HttpClient, CancellationToken, Task<T>> apiCall,
            int maxRetries = 3,
            CancellationToken cancellationToken = default)
        {
            var attempt = 0;
            Exception lastException = null;

            while (attempt <= maxRetries)
            {
                try
                {
                    return await ExecuteMedicareApiCallAsync(endpoint, apiCall, cancellationToken).ConfigureAwait(false);
                }
                catch (Exception ex) when (attempt < maxRetries && IsRetriableException(ex))
                {
                    lastException = ex;
                    attempt++;
                    
                    var delay = TimeSpan.FromMilliseconds(Math.Pow(2, attempt) * 1000); // Exponential backoff
                    _logger.LogWarning(ex, "Medicare API call to {Endpoint} failed on attempt {Attempt}, retrying in {Delay}ms", 
                        endpoint, attempt, delay.TotalMilliseconds);
                    
                    await Task.Delay(delay, cancellationToken).ConfigureAwait(false);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Medicare API call to {Endpoint} failed on attempt {Attempt} (non-retriable)", 
                        endpoint, attempt + 1);
                    throw;
                }
            }

            _logger.LogError(lastException, "Medicare API call to {Endpoint} failed after {MaxRetries} retries", 
                endpoint, maxRetries);
            throw lastException ?? new InvalidOperationException("Unexpected retry loop exit");
        }

        private static bool IsRetriableException(Exception ex)
        {
            return ex is HttpRequestException ||
                   ex is TaskCanceledException ||
                   ex is TimeoutException ||
                   (ex is OperationCanceledException && !(ex is TaskCanceledException));
        }

        public void Dispose()
        {
            _concurrencyLimiter?.Dispose();
            foreach (var limiter in _endpointLimiters.Values)
            {
                limiter?.Dispose();
            }
        }
    }

    public class AsyncMedicareOptions
    {
        public int MaxConcurrentRequests { get; set; } = 50;
        public int MaxConcurrentRequestsPerEndpoint { get; set; } = 10;
        public TimeSpan RequestTimeout { get; set; } = TimeSpan.FromSeconds(30);
    }
}
