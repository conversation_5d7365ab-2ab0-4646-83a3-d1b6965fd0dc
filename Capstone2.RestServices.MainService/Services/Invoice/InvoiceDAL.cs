﻿using Capstone2.RestServices.Invoice.Interfaces;
using Capstone2.RestServices.Invoice.Context;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Threading.Tasks;
using Capstone2.RestServices.Invoice.Models;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Common;
using System;
using System.Collections.Generic;
using System.Reflection;
using System.Globalization;
using Microsoft.Data.SqlClient;
using Capstone2.Shared.Models.Entities;
using System.Data.Common;
using System.Data;
using Newtonsoft.Json;

namespace Capstone2.RestServices.Invoice.Services
{
    public class InvoiceDAL : IInvoiceDAL
    {
        public readonly ReadOnlyInvoiceDBContext _readOnlyDbContext;
        public readonly UpdatableInvoiceDBContext _updatableDBContext;

        public InvoiceDAL(ReadOnlyInvoiceDBContext readOnlyDbContext, UpdatableInvoiceDBContext updatableDBContext)
        {
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
        }
        /// <summary>
        /// Method to add a new entry in InvoiceSummary table
        /// </summary>
        /// <param name="invoiceSummary"></param>
        /// <returns></returns>
        public async Task<InvoiceSummary> AddInvoiceSummary(InvoiceSummary invoiceSummary)
        {
            await _updatableDBContext.InvoiceSummaries.AddAsync(invoiceSummary);
            int rows = await _updatableDBContext.SaveChangesAsync();
            if (rows > 0)
            {
                return invoiceSummary;
            }
            return invoiceSummary;
        }
        /// <summary>
        /// Method to fetch from InvoiceSUmmary based on id and all the related tables
        /// </summary>
        /// <param name="id"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<InvoiceSummary> GetInvoiceSummaryWithInvoices(long id, int orgId)
        {
            return await (from summary in _readOnlyDbContext.InvoiceSummaries
            .Include(summary => summary.InvoiceDetails.Where(x => x.InvoiceStatusId != (short)InvoiceStatus.Deleted))
            .ThenInclude(invoice => invoice.InvoiceEpisodeItemAssocs.Where(x => x.StatusId == (short)Status.Active)).ThenInclude(item => item.InvoiceEpisodeItemsIndicatorAssocs).DefaultIfEmpty()
            .Include(summary => summary.InvoiceDetails.Where(x => x.InvoiceStatusId != (short)InvoiceStatus.Deleted))
            .ThenInclude(invoice => invoice.InvoiceMedicareAssocs.Where(x => x.StatusId == (short)Status.Active && x.OrgId == orgId))
            .ThenInclude(invItem => invItem.InvoiceItemsMedicareAssocs).DefaultIfEmpty()
            .Include(summary => summary.InvoiceDetails.Where(x => x.InvoiceStatusId != (short)InvoiceStatus.Deleted)).ThenInclude(inv => inv.InvoiceAdditionalDetails)
                          where summary.OrgId == orgId && summary.Id == id
                          select summary).FirstOrDefaultAsync();
        }
        /// <summary>
        /// Method to list Invoice Summary
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="queryModel"></param>
        /// <param name="filterModel"></param>
        /// <returns></returns>
        public async Task<QueryResultList<ListInvoiceSummary>> ListInvoiceSummary(int orgId, QueryModel queryModel, InvoiceSummaryFilterModel filterModel)
        {
            List<short> invoiceStatusFilter = new()
            {
                (short)InvoiceStatus.Void,
                (short)InvoiceStatus.Deleted
            };
            IQueryable<ListInvoiceSummary> summaryQuery = from summary in _readOnlyDbContext.InvoiceSummaries
                                                          //join invoice in _readOnlyDbContext.InvoiceDetails on summary.Id equals invoice.InvoiceSummaryId into JI
                                                          //from invoice in JI.DefaultIfEmpty()
                                                          where summary.OrgId == orgId //&& !invoiceStatusFilter.Contains(invoice.InvoiceStatusId)
                                                          select new ListInvoiceSummary
                                                          {
                                                              Id = summary.Id,
                                                              RecordId = summary.RecordId,
                                                              OrgId = summary.OrgId,
                                                              PatientDetailsId = summary.PatientDetailsId,
                                                              InvoiceStatusId = summary.InvoiceStatusId,
                                                              InvoiceStatus = EnumExtensions.GetDescription((InvoiceStatus)summary.InvoiceStatusId),
                                                              InvoiceTypeId = summary.InvoiceTypeId,
                                                              InvoiceType = EnumExtensions.GetDescription((InvoiceType)summary.InvoiceTypeId),
                                                              CreatedDate = summary.CreatedDate,
                                                              ModifiedDate = summary.ModifiedDate,
                                                              FinanceTypeId = summary.FinanceTypeId,
                                                              InvoiceDetails = (ICollection<InvoiceDetail>)summary.InvoiceDetails.Where(x=>!invoiceStatusFilter.Contains(x.InvoiceStatusId)),
                                                             // TotalAmount = (decimal)summary.InvoiceDetails.Where(x => !invoiceStatusFilter.Contains(x.InvoiceStatusId)).ToList().Sum(x=>x.TotalAmount)
                                                             Details = (summary.InvoiceTemplatesId==null|| summary.InvoiceTemplatesId==default(long))?null:(from invTemplate in _readOnlyDbContext.InvoiceTemplates where invTemplate.Id == summary.InvoiceTemplatesId select invTemplate.Name).FirstOrDefault()
                                                          };

            if (!(string.IsNullOrWhiteSpace(queryModel.SearchTerm)))
            {
                summaryQuery = SearchInvoiceSummary(summaryQuery, queryModel.SearchTerm);

            }
            if (filterModel != null)
            {
                if (filterModel.PatientDetailsId != null && filterModel.PatientDetailsId > default(long))
                {
                    summaryQuery = summaryQuery.Where(x => x.PatientDetailsId == filterModel.PatientDetailsId);
                }
                if (filterModel.InvoiceStatusId != null && filterModel.InvoiceStatusId.Any())
                {
                    summaryQuery = summaryQuery.Where(x => filterModel.InvoiceStatusId.Contains(x.InvoiceStatusId));
                }
                if (filterModel.InvoiceTypeId != null && filterModel.InvoiceTypeId.Any())
                {
                    summaryQuery = summaryQuery.Where(x => filterModel.InvoiceTypeId.Contains(x.InvoiceTypeId));
                }
                if (filterModel.StartDate != null )
                {
                    summaryQuery = summaryQuery.Where(x => x.CreatedDate >= filterModel.StartDate);
                }
                if (filterModel.EndDate != null)
                {
                    summaryQuery = summaryQuery.Where(x => x.CreatedDate <= filterModel.EndDate);
                }
            }
            summaryQuery = SortEpisodeItems(summaryQuery, queryModel.SortTerm, queryModel.SortOrder);

            QueryResultList<ListInvoiceSummary> paginatedList = await PaginatedResultListForInvoiceSummary(summaryQuery, queryModel);

            return paginatedList;
        }

        private async Task<QueryResultList<ListInvoiceSummary>> PaginatedResultListForInvoiceSummary(IQueryable<ListInvoiceSummary> summaryQuery, QueryModel queryModel)
        {
            QueryResultList<ListInvoiceSummary> queryList = new QueryResultList<ListInvoiceSummary>();
            List<ListInvoiceSummary> paginatedList = null;
            if (queryModel.PageNumber > 0 && queryModel.PageSize > 0)
            {
                if (summaryQuery.Any())
                {
                    paginatedList = await summaryQuery.Skip((queryModel.PageNumber - 1) * queryModel.PageSize)
                 .Take(queryModel.PageSize).ToListAsync();
                    // paginatedList.ForEach(x => x.Status = Enum.GetName(typeof(Status), x.StatusId));
                }
            }

            if (paginatedList != null)
            {
                queryList.ItemRecords = paginatedList;
                queryList.PageNumber = queryModel.PageNumber;
                queryList.PageSize = queryModel.PageSize;
                queryList.CurrentCount = paginatedList.Count();
            }
            else
            {
                queryList.ItemRecords = null;
                queryList.CurrentCount = 0;
            }
            queryList.TotalCount = summaryQuery.Count();
            queryList.PageNumber = queryModel.PageNumber;
            queryList.PageSize = queryModel.PageSize;

            return queryList;
        }

        private IQueryable<ListInvoiceSummary> SortEpisodeItems(IQueryable<ListInvoiceSummary> summaryQuery, string sortTerm, string sortOrder)
        {
            sortTerm = (sortTerm is null) ? string.Empty : sortTerm;
            sortOrder = (sortOrder is null) ? string.Empty : sortOrder;
            switch (sortTerm.ToLower())
            {

                case "id":

                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            summaryQuery = summaryQuery.OrderBy(x => x.Id);
                        }
                        else
                        {

                            summaryQuery = summaryQuery.OrderByDescending(x => x.Id);

                        }
                        break;
                    }
                case "RecordId":

                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            summaryQuery = summaryQuery.OrderBy(x => x.RecordId);
                        }
                        else
                        {

                            summaryQuery = summaryQuery.OrderByDescending(x => x.RecordId);

                        }
                        break;
                    }
                case "DateCreated":

                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            summaryQuery = summaryQuery.OrderBy(x => x.CreatedDate);
                        }
                        else
                        {

                            summaryQuery = summaryQuery.OrderByDescending(x => x.CreatedDate);

                        }
                        break;
                    }
                default:
                    summaryQuery = summaryQuery.OrderBy(x => x.CreatedDate);

                    break;
            }
            return summaryQuery;
        }

        private IQueryable<ListInvoiceSummary> SearchInvoiceSummary(IQueryable<ListInvoiceSummary> summaryQuery, string searchTerm)
        {
            return summaryQuery.Where(summary => summary.RecordId.ToString().StartsWith(searchTerm));
        }

        private IQueryable<ListInvoiceDetails> SearchInvoiceDetails(IQueryable<ListInvoiceDetails> summaryQuery, string searchTerm)
        {
            return summaryQuery.Where(summary => summary.InvoiceRecordId.ToString().Contains(searchTerm));
        }
        /// <summary>
        /// Method to update invoice summary and its child tables
        /// </summary>
        /// <param name="invoiceSummary"></param>
        /// <returns></returns>
        public async Task<InvoiceSummary> UpdateInvoiceSummary(InvoiceSummary invoiceSummary)
        {
            _updatableDBContext.InvoiceSummaries.Update(invoiceSummary);
            invoiceSummary.AffectedRows = await _updatableDBContext.SaveChangesAsync();
            return invoiceSummary;
        }
        /// <summary>
        /// Method to add Invoice Summary Expiry Detail
        /// </summary>
        /// <param name="invoiceSummary"></param>
        /// <returns></returns>
        public async Task<long> AddInvoiceSummaryExpiryDetail(InvoiceSummaryExpiryDetail invoiceSummaryExpiryDetail)
        {
            await _updatableDBContext.InvoiceSummaryExpiryDetails.AddAsync(invoiceSummaryExpiryDetail);
            return await _updatableDBContext.SaveChangesAsync();
        }

        /// <summary>
        /// method to delete the entry for invoicesummary when the type is other than estimate
        /// </summary>
        /// <param name="id"></param>
        /// <param name="orgId"></param>
        public async Task DeleteInvoiceSummaryExpiryDetail(long invoiceSummaryId, int orgId)
        {
            InvoiceSummaryExpiryDetail invoiceSummaryExpiryDetail = await _updatableDBContext.InvoiceSummaryExpiryDetails.Where(x => x.InvoiceSummaryId == invoiceSummaryId && x.OrgId == orgId).FirstOrDefaultAsync();
            if (invoiceSummaryExpiryDetail is not null)
            {
                _updatableDBContext.InvoiceSummaryExpiryDetails.Remove(invoiceSummaryExpiryDetail);
                await _updatableDBContext.SaveChangesAsync();
            }
        }
        /// <summary>
        /// Method to fetch invoice summary table
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<InvoiceSummary> GetInvoiceSummaryFromId(int orgId, long id)
        {
            return await _readOnlyDbContext.InvoiceSummaries.Where(x => x.Id == id && x.OrgId == orgId).AsNoTracking().FirstOrDefaultAsync();
        }

        public async Task<int> PartialUpdateInvoiceSummary(Dictionary<string, object> nameValuePairProperties, long userId, InvoiceSummary invoiceSummaryDB)
        {
            _updatableDBContext.InvoiceSummaries.Attach(invoiceSummaryDB);
            var currentValues = _updatableDBContext.Entry(invoiceSummaryDB).CurrentValues;
            var type = invoiceSummaryDB.GetType();
            PropertyInfo[] p = type.GetProperties();
            foreach (string key in nameValuePairProperties.Keys)
            {
                if (p.ToList().Where(p => p.Name == key).Any())
                {
                    var value = nameValuePairProperties[key];

                    currentValues[key] = Convert.ChangeType(value, currentValues[key].GetType(), CultureInfo.InvariantCulture);

                }
            }
            if (_updatableDBContext.Entry(invoiceSummaryDB).State == EntityState.Modified)
            {
                invoiceSummaryDB.ModifiedDate = DateTime.UtcNow;
                invoiceSummaryDB.ModifiedBy = userId;
            }
            return await _updatableDBContext.SaveChangesAsync();
        }
        /// <summary>
        /// Method to retireve invoicedetail  with invoice items
        /// </summary>
        /// <param name="id"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<InvoiceDetail> GetInvoiceDetailsWithInvoiceItems(long id, int orgId, bool isIncludeClaimedItem)
        {
            return await (from invoice in _readOnlyDbContext.InvoiceDetails
            .Include(invoiceitems => invoiceitems.InvoiceEpisodeItemAssocs.Where(x => x.StatusId == (short)Status.Active
                                     && (isIncludeClaimedItem == false ? x.ClaimStatusId != (short)ClaimStatus.Complete : isIncludeClaimedItem)))
            .ThenInclude(item => item.InvoiceEpisodeItemsIndicatorAssocs).DefaultIfEmpty()
            .Include(med => med.InvoiceMedicareAssocs.Where(x => x.StatusId == (short)Status.Active && x.OrgId == orgId))
            .ThenInclude(invItem => invItem.InvoiceItemsMedicareAssocs)
            .Include(inv => inv.InvoiceAdditionalDetails)
                          where invoice.Id == id && invoice.OrgId == orgId
                          select invoice).FirstOrDefaultAsync();
        }

        /// <summary>
        /// Method to retireve invoicedetail  with invoice items
        /// </summary>
        /// <param name="id"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<InvoiceDetail> GetInvoiceDetailsWithOnlyInvoiceItems(long id, int orgId)
        {
            return await (from invoice in _readOnlyDbContext.InvoiceDetails
            .Include(invoiceitems => invoiceitems.InvoiceEpisodeItemAssocs.Where(x => x.StatusId == (short)Status.Active && x.InvoiceDetailsId == id && x.OrgId == orgId))
                          where invoice.Id == id && invoice.OrgId == orgId
                          select invoice).FirstOrDefaultAsync();
        }
        /// <summary>
        /// Method to retireve invoicedetail  with no invoice items
        /// </summary>
        /// <param name="id"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<InvoiceDetail> GetInvoiceDetailsFromId(long id, int orgId)
        {
            return await (from invoice in _readOnlyDbContext.InvoiceDetails
                          where invoice.Id == id && invoice.OrgId == orgId
                          select invoice).AsNoTracking().FirstOrDefaultAsync();
        }
        /// <summary>
        /// Method to update InvoiceDetail table
        /// </summary>
        /// <param name="invoice"></param>
        /// <returns></returns>
        public async Task<InvoiceDetail> UpdateInvoiceDetail(InvoiceDetail invoice)
        {
            _updatableDBContext.InvoiceDetails.Update(invoice);
            await _updatableDBContext.SaveChangesAsync();
            return invoice;
        }

        /// <summary>
        /// Method to add a new entry in InvoiceDetail table
        /// </summary>
        /// <param name="invoiceSummary"></param>
        /// <returns></returns>
        public async Task<InvoiceDetail> AddInvoiceDetail(InvoiceDetail invoiceDetail)
        {
            await _updatableDBContext.InvoiceDetails.AddAsync(invoiceDetail);
            int rows = await _updatableDBContext.SaveChangesAsync();
            if (rows > 0)
            {
                return invoiceDetail;
            }
            return invoiceDetail;
        }

        /// <summary>
        /// Method to add a new InvocieTemplate
        /// </summary>
        /// <param name="invoiceTemplate"></param>
        /// <returns></returns>
        public async Task<long> AddInvoiceTemplate(InvoiceTemplate invoiceTemplate)
        {
            await _updatableDBContext.InvoiceTemplates.AddAsync(invoiceTemplate);
            int rows = await _updatableDBContext.SaveChangesAsync();
            if (rows > 0)
            {
                return invoiceTemplate.Id;
            }
            return default(long);
        }

        /// <summary>
        /// Method to update InvocieTemplate
        /// </summary>
        /// <param name="invoiceSummary"></param>
        /// <returns></returns>
        public async Task<InvoiceTemplate> UpdateInvoiceTemplate(InvoiceTemplate invoiceTemplate)
        {
            _updatableDBContext.InvoiceTemplates.Update(invoiceTemplate);
            await _updatableDBContext.SaveChangesAsync();

            return invoiceTemplate;
        }

        /// <summary>
        /// Method to fetch the next value for a sequence
        /// </summary>
        /// <param name="sequenceName"></param>
        /// <returns></returns>
        public async Task<long> GetNextVal(string sequenceName)
        {
            var p = new SqlParameter("@result", System.Data.SqlDbType.BigInt);
            p.Direction = System.Data.ParameterDirection.Output;
            await _updatableDBContext.Database.ExecuteSqlRawAsync("set @result = next value for " + sequenceName, p);
            return (long)p.Value;
        }
        /// <summary>
        /// Method to fetch Estimate Template
        /// </summary>
        /// <param name="id"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<InvoiceTemplate> GetInvoiceTemplateWithInvoiceSummary(long id, int orgId)
        {
            //from T in _readOnlyDbContext.InvoiceTemplates
            //where T.OrgId == orgId && T.Id == id
            //join S in _readOnlyDbContext.InvoiceSummaries on T.Id equals S.InvoiceTemplatesId into ST
            //from S in ST.DefaultIfEmpty()
            //select new Invo
            return await (from template in _readOnlyDbContext.InvoiceTemplates
            .Include(template => template.InvoiceSummaries)
                          where template.OrgId == orgId && template.Id == id
                          select template).FirstOrDefaultAsync();
        }

        public async Task<QueryResultList<InvoiceTemplate>> ListInvoiceTemplate(int orgId, QueryModel queryModel)
        {
            IQueryable<InvoiceTemplate> templateQuery = from template in _readOnlyDbContext.InvoiceTemplates
                                                        where template.OrgId == orgId && template.StatusId == (short)Status.Active
                                                        select template;


            if (!(string.IsNullOrWhiteSpace(queryModel.SearchTerm)))
            {
                templateQuery = SearchInvoiceTemplate(templateQuery, queryModel.SearchTerm);

            }

            templateQuery = SortTemplate(templateQuery, queryModel.SortTerm, queryModel.SortOrder);

            QueryResultList<InvoiceTemplate> paginatedList = await PaginatedResultListForInvoiceTemplate(templateQuery, queryModel);

            return paginatedList;
        }

        private async Task<QueryResultList<InvoiceTemplate>> PaginatedResultListForInvoiceTemplate(IQueryable<InvoiceTemplate> templateQuery, QueryModel queryModel)
        {
            QueryResultList<InvoiceTemplate> queryList = new QueryResultList<InvoiceTemplate>();
            List<InvoiceTemplate> paginatedList = null;
            if (queryModel.PageNumber > 0 && queryModel.PageSize > 0)
            {
                if (templateQuery.Any())
                {
                    paginatedList = await templateQuery.Skip((queryModel.PageNumber - 1) * queryModel.PageSize)
                 .Take(queryModel.PageSize).ToListAsync();
                    // paginatedList.ForEach(x => x.Status = Enum.GetName(typeof(Status), x.StatusId));
                }
            }

            if (paginatedList != null)
            {
                queryList.ItemRecords = paginatedList;
                queryList.PageNumber = queryModel.PageNumber;
                queryList.PageSize = queryModel.PageSize;
                queryList.CurrentCount = paginatedList.Count();
            }
            else
            {
                queryList.ItemRecords = null;
                queryList.CurrentCount = 0;
            }
            queryList.TotalCount = templateQuery.Count();
            queryList.PageNumber = queryModel.PageNumber;
            queryList.PageSize = queryModel.PageSize;

            return queryList;
        }

        private IQueryable<InvoiceTemplate> SortTemplate(IQueryable<InvoiceTemplate> templateQuery, string sortTerm, string sortOrder)
        {
            sortTerm = (sortTerm is null) ? string.Empty : sortTerm;
            sortOrder = (sortOrder is null) ? string.Empty : sortOrder;
            switch (sortTerm.ToLower())
            {

                case "id":

                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            templateQuery = templateQuery.OrderBy(x => x.Id);
                        }
                        else
                        {

                            templateQuery = templateQuery.OrderByDescending(x => x.Id);

                        }
                        break;
                    }
                case "name":

                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            templateQuery = templateQuery.OrderBy(x => x.Name);
                        }
                        else
                        {

                            templateQuery = templateQuery.OrderByDescending(x => x.Name);

                        }
                        break;
                    }
                default:
                    templateQuery = templateQuery.OrderBy(x => x.Id);

                    break;
            }
            return templateQuery;
        }

        private IQueryable<InvoiceTemplate> SearchInvoiceTemplate(IQueryable<InvoiceTemplate> templateQuery, string searchTerm)
        {
            return templateQuery.Where(x => x.Name.Contains(searchTerm));

        }
        /// <summary>
        /// Method to fetch Invoice temnplate based on id
        /// </summary>
        /// <param name="id"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<InvoiceTemplate> GetInvoiceTemplateFromId(long id, int orgId)
        {
            return await _readOnlyDbContext.InvoiceTemplates.Where(x => x.Id == id && x.OrgId == orgId).AsNoTracking().FirstOrDefaultAsync();
        }
        /// <summary>
        /// Methodto paial update InvoiceTemplate
        /// </summary>
        /// <param name="property
        /// "></param>
        /// <param name="userId"></param>
        /// <param name="invoiceTemplateDB"></param>
        /// <returns></returns>
        public async Task<int> PartialUpdateInvoiceTemplate(Dictionary<string, object> propertyDictionary, long userId, InvoiceTemplate invoiceTemplateDB)
        {
            _updatableDBContext.InvoiceTemplates.Attach(invoiceTemplateDB);
            var currentValues = _updatableDBContext.Entry(invoiceTemplateDB).CurrentValues;
            var type = invoiceTemplateDB.GetType();
            PropertyInfo[] p = type.GetProperties();
            foreach (string key in propertyDictionary.Keys)
            {
                if (p.ToList().Where(p => p.Name == key).Any())
                {
                    var value = propertyDictionary[key];

                    currentValues[key] = Convert.ChangeType(value, currentValues[key].GetType(), CultureInfo.InvariantCulture);

                }
            }
            if (_updatableDBContext.Entry(invoiceTemplateDB).State == EntityState.Modified)
            {
                invoiceTemplateDB.ModifiedDate = DateTime.UtcNow;
                invoiceTemplateDB.ModifiedBy = userId;
            }
            return await _updatableDBContext.SaveChangesAsync();
        }
        /// <summary>
        /// Method to check if template name is duplicate
        /// </summary>
        /// <param name="search_term"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<bool> CheckTemplateName(string search_term, int orgId)
        {
            var name = await _readOnlyDbContext.InvoiceTemplates
                   .Where(p => p.Name.Equals(search_term) && p.OrgId == orgId && p.StatusId == (short)Status.Active).AsNoTracking().FirstOrDefaultAsync();
            if (name == null)
                return false;
            else
                return true;
        }
        /// <summary>
        /// Method to fetch InvoiceTemplate with InvoiceSummaries ,InvoiceDetails and InvoiceEpisodeItemAssocs
        /// </summary>
        /// <param name="id"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<InvoiceTemplate> GetInvoiceTemplateWithInvoices(long id, int orgId)
        {
            return await (from template in _readOnlyDbContext.InvoiceTemplates
                          .Include(template => template.InvoiceSummaries)
                            .ThenInclude(summary => summary.InvoiceDetails.Where(x => x.InvoiceStatusId != (short)InvoiceStatus.Deleted))
                            .ThenInclude(invoice => invoice.InvoiceEpisodeItemAssocs.Where(x => x.StatusId == (short)Status.Active))
                          where template.OrgId == orgId && template.Id == id
                          select template).FirstOrDefaultAsync();
        }

        public async Task<int> PartialUpdateInvoiceDetail(Dictionary<string, object> nameValuePairProperties, long userId, InvoiceDetail invoiceDetailDB)
        {
            _updatableDBContext.InvoiceDetails.Attach(invoiceDetailDB);
            var currentValues = _updatableDBContext.Entry(invoiceDetailDB).CurrentValues;
            if (nameValuePairProperties is not null)
            {
                var type = invoiceDetailDB.GetType();
                PropertyInfo[] p = type.GetProperties();
                foreach (string key in nameValuePairProperties.Keys)
                {
                    if (p.ToList().Where(p => p.Name == key).Any())
                    {
                        var value = nameValuePairProperties[key];

                        if (key == "Deposit" || key == "Owing" || key == "TotalAmount" || key == "ExcessOwing")
                        {
                            currentValues[key] = (decimal?)value;
                        }
                        else if (key == "InvoiceStatusId")
                        {
                            currentValues[key] = (short)value;

                        }

                    }
                }
            }

            if (_updatableDBContext.Entry(invoiceDetailDB).State == EntityState.Modified)
            {
                invoiceDetailDB.ModifiedDate = DateTime.UtcNow;
                invoiceDetailDB.ModifiedBy = userId;
            }
            return await _updatableDBContext.SaveChangesAsync();
        }

        /// <summary>
        /// Method to fetch InvoiceMedicareAssoc based on invoice detailid
        /// </summary>
        /// <param name="id"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<List<InvoiceMedicareAssoc>> GetInvoiceMedicareAssocsForClaimType(long invoice_detail_id, short claimTypeId, int orgId)
        {
            //return await (from medicareassoc in _readOnlyDbContext.InvoiceMedicareAssocs
            //                where medicareassoc.OrgId == orgId && medicareassoc.InvoiceDetailsId == invoice_detail_id && medicareassoc.StatusId == (short)Status.Active &&  medicareassoc.ClaimTypeId==claimTypeId select medicareassoc).ToListAsync();
            return await _readOnlyDbContext.InvoiceMedicareAssocs.Where(x => x.InvoiceDetailsId == invoice_detail_id && x.OrgId == orgId && x.StatusId == (short)Status.Active && x.ClaimTypeId == claimTypeId).AsNoTracking().ToListAsync();

        }

        /// <summary>
        /// Method to fetch InvoiceMedicareAssoc based on id
        /// </summary>
        /// <param name="id"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<InvoiceMedicareAssoc> GetInvoiceMedicareAssocsFromId(long id, int orgId)
        {
            //return await (from medicareassoc in _readOnlyDbContext.InvoiceMedicareAssocs
            //                where medicareassoc.OrgId == orgId && medicareassoc.InvoiceDetailsId == invoice_detail_id && medicareassoc.StatusId == (short)Status.Active &&  medicareassoc.ClaimTypeId==claimTypeId select medicareassoc).ToListAsync();
            return await _readOnlyDbContext.InvoiceMedicareAssocs.Where(x => x.Id == id && x.OrgId == orgId).FirstOrDefaultAsync();

        }

        /// <summary>
        /// Method to Add InvoiceMedicareAssoc
        /// </summary>
        /// <param name="id"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<InvoiceMedicareAssoc> AddInvoiceMedicareAssoc(InvoiceMedicareAssoc invoiceMedicareAssoc)
        {
            await _updatableDBContext.InvoiceMedicareAssocs.AddAsync(invoiceMedicareAssoc);
            int rows = await _updatableDBContext.SaveChangesAsync();
            //if (rows > 0)
            //{
            //    return invoiceMedicareAssoc.Id;
            //}
            return invoiceMedicareAssoc;
        }


        /// <summary>
        /// Method to Update InvoiceMedicareAssoc
        /// </summary>
        /// <param name="id"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<int> UpdateInvoiceMedicareAssoc(InvoiceMedicareAssoc invoiceMedicareAssoc)
        {
            _updatableDBContext.InvoiceMedicareAssocs.Update(invoiceMedicareAssoc);
            return await _updatableDBContext.SaveChangesAsync();

        }
        /// <summary>
        /// Method to Update InvoicePatientEftAssoc
        /// </summary>
        /// <param name="id"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<int> UpdateInvoicePatientEftAssoc(InvoicePatientEftAssoc invoicePatientEftAssoc)
        {
            _updatableDBContext.InvoicePatientEftAssocs.Update(invoicePatientEftAssoc);
            return await _updatableDBContext.SaveChangesAsync();

        }
        /// <summary>
        /// Method to Add InvoicePatientEftAssoc
        /// </summary>
        /// <param name="id"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<int> AddInvoicePatientEftAssoc(InvoicePatientEftAssoc invoicePatientEftAssoc)
        {
            await _updatableDBContext.InvoicePatientEftAssocs.AddAsync(invoicePatientEftAssoc);
            return await _updatableDBContext.SaveChangesAsync();

        }
        /// <summary>
        /// Method to fetch InvoicePatientEftAssoc
        /// </summary>
        /// <param name="invoiceDetailsId"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<InvoicePatientEftAssoc> FetchInvoicePatientEftAssoc(long invoiceDetailsId,int orgId)
        {
            return await _readOnlyDbContext.InvoicePatientEftAssocs.Where(x => x.OrgId == orgId && x.InvoiceDetailsId == invoiceDetailsId && x.StatusId==(short)Status.Active).FirstOrDefaultAsync();

        }
        /// <summary>
        /// Method to 
        /// </summary>
        /// <param name="invoiceDetailsId"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<InvoiceMedicareAssoc> GetLatestMedicareTransactionForClaimType(long invoiceDetailsId, short claimTypeId, int orgId)
        {
            return await _readOnlyDbContext.InvoiceMedicareAssocs.Where(x => x.InvoiceDetailsId == invoiceDetailsId && x.OrgId == orgId && x.StatusId == (short)Status.Active && x.ClaimTypeId == claimTypeId).OrderBy(x => x.CreatedDate).AsNoTracking().LastOrDefaultAsync();
        }
        public async Task<List<InvoiceMedicareAssoc>> GetActiveMedicareTransactionForClaimType(long invoiceDetailsId, short claimTypeId, int orgId)
        {
            return await _readOnlyDbContext.InvoiceMedicareAssocs.Where(x => x.InvoiceDetailsId == invoiceDetailsId && x.OrgId == orgId && x.StatusId == (short)Status.Active && x.ClaimTypeId == claimTypeId).OrderBy(x => x.CreatedDate).AsNoTracking().ToListAsync();
        }
        /// <summary>
        /// Method to in
        /// </summary>
        /// <param name="lstMedicareAssocs"></param>
        /// <returns></returns>
        public async Task<int> UpdateInvoiceMedicareAssocRange(List<InvoiceMedicareAssoc> lstMedicareAssocs)
        {
            _updatableDBContext.UpdateRange(lstMedicareAssocs);
            return await _updatableDBContext.SaveChangesAsync();
        }


        public async Task<int> AddInvoiceMedicareAssocRange(List<InvoiceMedicareAssoc> lstMedicareAssocs)
        {
            await _updatableDBContext.AddRangeAsync(lstMedicareAssocs);
            return await _updatableDBContext.SaveChangesAsync();
        }


        public async Task<int> AddInvoiceEpisodeItemAssocRange(List<InvoiceEpisodeItemAssoc> lstInvoiceEpisodeItemAssocs)
        {
            await _updatableDBContext.AddRangeAsync(lstInvoiceEpisodeItemAssocs);
            return await _updatableDBContext.SaveChangesAsync();
        }


        public async Task<List<InvoiceEpisodeItemAssoc>> FetchInvoiceEpisodeItemAssocsFromPaymentDetailsId(long paymentDetailId, long invoicedetailsId, int orgId)
        {
            return await _readOnlyDbContext.InvoiceEpisodeItemAssocs.Where(x => x.OrgId == orgId && x.InvoiceDetailsId == invoicedetailsId && x.PaymentDetailsId == paymentDetailId).ToListAsync();
        }

        /// <summary>
        /// Method to list Invoice details by appointmentId
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="queryModel"></param>
        /// <param name="filterModel"></param>
        /// <returns></returns>
        public async Task<List<ListInvoiceDetails>> GetAppointmentInvoiceList(int orgId, QueryModel queryModel, InvoiceSummaryFilterModel filterModel)
        {
            IQueryable<ListInvoiceDetails> summaryQuery = from summary in _readOnlyDbContext.InvoiceSummaries
                                                          join invoice in _readOnlyDbContext.InvoiceDetails 
                                                          on summary.Id equals invoice.InvoiceSummaryId
                                                          where summary.OrgId == orgId && summary.InvoiceStatusId != (short)InvoiceStatus.Void
                                                          select new ListInvoiceDetails
                                                          {
                                                              Id = invoice.Id,
                                                              InvoiceRecordId = invoice.RecordId,
                                                              InvoiceSummaryId = summary.Id,
                                                              InvoiceSummaryRecordId = summary.RecordId,
                                                              OrgId = summary.OrgId,
                                                              InvoiceStatusId = summary.InvoiceStatusId,
                                                              InvoiceStatus = EnumExtensions.GetDescription((InvoiceStatus)summary.InvoiceStatusId),
                                                              InvoiceTypeId = summary.InvoiceTypeId,
                                                              InvoiceType = EnumExtensions.GetDescription((InvoiceType)summary.InvoiceTypeId),
                                                              CreatedDate = summary.CreatedDate,
                                                              ModifiedDate = summary.ModifiedDate,
                                                              AppointmentDetailsId = invoice.AppointmentDetailsId,
                                                              PatientDetailsId = summary.PatientDetailsId,
                                                              CompanyDetailsId = invoice.CompanyDetailsId,
                                                              DateOfService = invoice.DateOfService,
                                                              ProviderId = invoice.ProviderId
                                                          };

            if (!(string.IsNullOrWhiteSpace(queryModel.SearchTerm)))
            {
                summaryQuery = SearchInvoiceDetails(summaryQuery, queryModel.SearchTerm);

            }
            if (filterModel != null)
            {
                if (filterModel.AppointmentDetailsId != null && filterModel.AppointmentDetailsId > default(long))
                {
                    summaryQuery = summaryQuery.Where(x => x.AppointmentDetailsId == filterModel.AppointmentDetailsId);
                }

            }
            summaryQuery = SortInvoiceDetailsItems(summaryQuery, queryModel.SortTerm, queryModel.SortOrder);

            return summaryQuery.ToList();
        }

        private IQueryable<ListInvoiceDetails> SortInvoiceDetailsItems(IQueryable<ListInvoiceDetails> summaryQuery, string sortTerm, string sortOrder)
        {
            sortTerm = (sortTerm is null) ? string.Empty : sortTerm;
            sortOrder = (sortOrder is null) ? string.Empty : sortOrder;
            switch (sortTerm.ToLower())
            {

                case "id":

                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            summaryQuery = summaryQuery.OrderBy(x => x.Id);
                        }
                        else
                        {

                            summaryQuery = summaryQuery.OrderByDescending(x => x.Id);

                        }
                        break;
                    }
                case "RecordId":

                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            summaryQuery = summaryQuery.OrderBy(x => x.InvoiceRecordId);
                        }
                        else
                        {

                            summaryQuery = summaryQuery.OrderByDescending(x => x.InvoiceRecordId);

                        }
                        break;
                    }
                default:
                    summaryQuery = summaryQuery.OrderBy(x => x.InvoiceRecordId);

                    break;
            }
            return summaryQuery;
        }
        /// <summary>
        /// Method to partially update InvoiceMedicareAssoc
        /// </summary>
        /// <param name="propertyDictionary"></param>
        /// <param name="userId"></param>
        /// <param name="invoiceMedicareAssoc"></param>
        /// <returns></returns>
        public async  Task<int> PartialUpdateInvoiceMedicareAssoc(Dictionary<string, object> propertyDictionary, long userId, InvoiceMedicareAssoc invoiceMedicareAssoc)
        {
            _updatableDBContext.InvoiceMedicareAssocs.Attach(invoiceMedicareAssoc);
            var currentValues = _updatableDBContext.Entry(invoiceMedicareAssoc).CurrentValues;
            var type = invoiceMedicareAssoc.GetType();
            PropertyInfo[] p = type.GetProperties();
            foreach (string key in propertyDictionary.Keys)
            {
                if (p.ToList().Where(p => p.Name == key).Any())
                {
                    var value = propertyDictionary[key];

                    if (key == "FileDetailsId" )
                    {
                        currentValues[key] = (long?)value;
                    }

                }
            }
            if (_updatableDBContext.Entry(invoiceMedicareAssoc).State == EntityState.Modified)
            {
                invoiceMedicareAssoc.ModifiedDate = DateTime.UtcNow;
                invoiceMedicareAssoc.ModifiedBy = userId;
            }
            return await _updatableDBContext.SaveChangesAsync();
        }
        /// <summary>
        /// Method to retrieve finance summary
        /// </summary>
        /// <param name="patientDetailsId"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<List<FinanceSummaryGroupView>> GetInvoiceSummaryForPatient(long patientDetailsId, string searchTerm, int orgId, FinanceSummaryFilter filter)
        {
            List<short> invoiceStatusFilter = new()
            {
                (short)InvoiceStatus.Void,
                (short)InvoiceStatus.Deleted
            };
            List<short> invoiceStatusAllowedFilter = null;
            if (filter!=null && filter.InvoiceStatusId != null && filter.InvoiceStatusId.Any())
            {
                invoiceStatusAllowedFilter = filter.InvoiceStatusId;
            }
            List<short> invoiceTypeAllowedFilter = null;
            if (filter != null && filter.InvoiceTypeId != null && filter.InvoiceTypeId.Any())
            {
                invoiceTypeAllowedFilter = filter.InvoiceTypeId;
            }
            DateTime? startDate = null;
            if (filter != null && filter.StartDate != null)
            {
                startDate = filter.StartDate;
            }
            DateTime? endDate = null;

            if (filter != null && filter.EndDate != null)
            {
                endDate = filter.EndDate;
            }
            IQueryable<FinanceSummaryGroupView> query;
            if (filter is not null && filter.InvoiceSummaryId > default(long))
            {
                query = from invoice in _readOnlyDbContext.InvoiceDetails
                        join summary in _readOnlyDbContext.InvoiceSummaries on invoice.InvoiceSummaryId equals summary.Id
                        where summary.PatientDetailsId == patientDetailsId && !invoiceStatusFilter.Contains(invoice.InvoiceStatusId) && summary.Id == filter.InvoiceSummaryId
                       
                        
                        group invoice by invoice.InvoiceTypeId into g
                        select new FinanceSummaryGroupView()
                        {

                            InvoiceTypeId = g.Key,
                            Total = g.Where(x => x.TotalAmount != null).Sum(x => (decimal)x.TotalAmount),
                            Owing = g.Where(x => x.Owing != null).Sum(x => (decimal)x.Owing),
                            PatientDetailsId = patientDetailsId,
                            OrgId = orgId

                        };
                //return await query.ToListAsync();

            }
            else
            {
                query = from invoice in _readOnlyDbContext.InvoiceDetails
                        join summary in _readOnlyDbContext.InvoiceSummaries on invoice.InvoiceSummaryId equals summary.Id
                        where summary.PatientDetailsId == patientDetailsId && !invoiceStatusFilter.Contains(invoice.InvoiceStatusId) 
                         && (invoiceStatusAllowedFilter == null || invoiceStatusAllowedFilter.Contains(summary.InvoiceStatusId))
                        && (invoiceTypeAllowedFilter == null || invoiceTypeAllowedFilter.Contains(summary.InvoiceTypeId))
                       && (endDate == null || summary.CreatedDate<=endDate)
                       && (startDate == null || summary.CreatedDate >= startDate) && (string.IsNullOrWhiteSpace(searchTerm) || summary.RecordId.ToString().StartsWith(searchTerm))

                        group invoice by invoice.InvoiceTypeId into g
                        select new FinanceSummaryGroupView()
                        {

                            InvoiceTypeId = g.Key,
                            Total = g.Where(x => x.TotalAmount != null).Sum(x => (decimal)x.TotalAmount),
                            Owing = g.Where(x => x.Owing != null).Sum(x => (decimal)x.Owing),
                            PatientDetailsId = patientDetailsId,
                            OrgId=orgId

                        };


            }
            
           

            return await query.ToListAsync();

        }

        public async Task<InvoiceDetailUpdate> GetInvoiceDetailInfoForPayment(long id, int orgId)
        {
            return await _readOnlyDbContext.InvoiceDetails.AsNoTracking().Where(x => x.Id == id && x.OrgId == orgId)
                .Select(x => new InvoiceDetailUpdate()
                {
                    TotalInvoiceAmount = x.TotalAmount,
                    TotalAmoutPaid = (((x.TotalAmount != null) ? (decimal)x.TotalAmount : 0) - ((x.Owing != null) ? (decimal)x.Owing : 0)),
                    TotalDepositPaid = ((x.Deposit != null) ? (decimal)x.Deposit : 0),
                    CompanyDetailsId = x.CompanyDetailsId


                }).FirstOrDefaultAsync();
        }


        public async Task<List<ListInvoiceDetails>> ListInvoicesForAppointment(int orgId, long appointmentDetailsId,InvoiceSummaryFilterModel filterModel)
        {
            List<SqlParameter> paramsList = new List<SqlParameter>();
            paramsList.Add(new SqlParameter("@OrgId", orgId));
            paramsList.Add(new SqlParameter("@appointmentDetailsId", appointmentDetailsId));
            // paramsList.Add(new SqlParameter("@referralStatus", (short)Status.Active));
            //0,1 corresponding to values in the isdefault column any other value the isdefault column will be removed from the filter
            //paramsList.Add(new SqlParameter("@isDefault", IsDefault));
            if (filterModel is not null && filterModel.CategoryTypeId is not null &&  filterModel.CategoryTypeId.Count > 0)
                paramsList.Add(new SqlParameter("@categoryTypeId", string.Join<short>(",", filterModel.CategoryTypeId)));

            var response = await ExecuteStoredProcedure("[Invoice].[ListInvoicesForAppointment]", paramsList);
            return response;
        }
        public async Task<List<ListInvoiceDetails>> ExecuteStoredProcedure(string storedProcedureName, IEnumerable<SqlParameter> parameters)
        {
            List<ListInvoiceDetails> referrals = null;
            var dbConnection = (_readOnlyDbContext.Database.GetDbConnection());
            dbConnection.Open();
            using (var cmd = dbConnection.CreateCommand())
            {
                cmd.CommandText = storedProcedureName;
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 900;
                foreach (var parameter in parameters)
                {
                    cmd.Parameters.Add(parameter);
                }
                var reader = await cmd.ExecuteReaderAsync();
                referrals = SqlDataToJson(reader);
            }
            return referrals;
        }
        private List<ListInvoiceDetails> SqlDataToJson(DbDataReader dataReader)
        {
            var dataTable = new DataTable();
            List<ListInvoiceDetails> invList = new List<ListInvoiceDetails>();
            dataTable.Load(dataReader);

            invList = (from rw in dataTable.AsEnumerable()
                       select new ListInvoiceDetails
                       {
                           Id = rw["Id"] == DBNull.Value ? 0 : Convert.ToInt64(rw["Id"]),
                           OrgId = Convert.ToInt32(rw["OrgId"]),
                           InvoiceSummaryRecordId = rw["InvoiceSummaryRecordId"] == DBNull.Value ? null : Convert.ToInt64(rw["InvoiceSummaryRecordId"]),
                           InvoiceSummaryId = rw["InvoiceSummaryId"] == DBNull.Value ? 0 : Convert.ToInt64(rw["InvoiceSummaryId"]),
                           AppointmentDetailsId = rw["AppointmentDetailsId"] == DBNull.Value ? default(long) : Convert.ToInt64(rw["AppointmentDetailsId"]),
                           ProviderId = rw["UserDetailsId"] == DBNull.Value ? default(long) : Convert.ToInt64(rw["UserDetailsId"]),
                           CompanyDetailsId = rw["CompanyDetailsId"] == DBNull.Value ? default(int) : Convert.ToInt32(rw["CompanyDetailsId"]),
                           InvoiceRecordId = rw["RecordId"] == DBNull.Value ? default(int) : Convert.ToInt64(rw["RecordId"]),
                           TotalAmount = rw["TotalAmount"] == DBNull.Value ? default(decimal) : Convert.ToDecimal(rw["TotalAmount"]),
                           DateOfService = rw["DateOfAppointment"] == DBNull.Value ? null : Convert.ToDateTime(rw["DateOfAppointment"]),
                           InvoiceTypeId = rw["InvoiceTypeId"] == DBNull.Value ? default(short) : Convert.ToInt16(rw["InvoiceTypeId"]),
                           CreatedDate = rw["CreatedDate"] == DBNull.Value ? default(DateTime) : Convert.ToDateTime(rw["CreatedDate"]),
                           InvoiceStatusId = (short)(rw["InvoiceStatusId"] == DBNull.Value ? 0 : Convert.ToInt16(rw["InvoiceStatusId"])),
                           ProviderDetails = rw["ProviderDetails"] == DBNull.Value ? null : JsonConvert.DeserializeObject<UserDetailInfo>(Convert.ToString(rw["ProviderDetails"])),
                           CompanyDetailInfo = rw["CompanyDetails"] == DBNull.Value ? null : JsonConvert.DeserializeObject<CompanyDetailInfo>(Convert.ToString(rw["CompanyDetails"])),
                           AccountHolderTypeId = (short)(rw["AccountHolderTypeId"] == DBNull.Value ? 0 : Convert.ToInt16(rw["AccountHolderTypeId"])),
                           BillingScheduleTypeId = (short)(rw["BillingScheduleTypeId"] == DBNull.Value ? 0 : Convert.ToInt16(rw["BillingScheduleTypeId"])),
                           HealthFundParticipantId =rw["HealthFundParticipantId"] == DBNull.Value ? string.Empty : rw["HealthFundParticipantId"].ToString(),
                           HealthFundMembershipNo = rw["HealthFundMembershipNo"] == DBNull.Value ? string.Empty : rw["HealthFundMembershipNo"].ToString(),

                       }).ToList();


            return invList;
        }
    }


}