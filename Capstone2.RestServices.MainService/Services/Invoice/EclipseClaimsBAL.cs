﻿using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.Invoice.Common;

using Capstone2.RestServices.Invoice.Interfaces;
using Capstone2.RestServices.Invoice.Models;
using Capstone2.RestServices.Invoice.Utility;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Threading.Tasks;
using System.Linq;
using Capstone2.Shared.Models.Entities;
using Capstone2.Framework.Business.Common;
using Capstone2.Framework.RestApi;
using ProviderType = Capstone2.Shared.Models.Medicare.ProviderType;
using System.Collections.Generic;
using Capstone2.Shared.Models.Medicare;
using Newtonsoft.Json;
using System.Runtime.InteropServices;
using System.Text;
using Capstone2.Framework.RestApi.Utility;
using Microsoft.Extensions.Configuration;
using AutoMapper;
using InvoiceDetailView = Capstone2.RestServices.Invoice.Models.InvoiceDetailView;

namespace Capstone2.RestServices.Invoice.Services
{
    public class EclipseClaimsBAL : IEclipseClaimsBAL
    {
        public readonly IInvoiceBAL _invoiceBAL;
        public readonly IInvoiceDAL _invoiceDAL;
        public readonly IClaimsBAL _claimsBAL;

        public IMapper _mapper;
        public readonly AppSettings _appSettings;
        private readonly ILogger<ClaimsBAL> _logger;
        private IASBMessageSenderHelper _asbMessageSenderHelper;
        private readonly IConfiguration _configuration;
        public EclipseClaimsBAL(IInvoiceBAL invoiceBAL, IClaimsBAL claimsBAL,IInvoiceDAL invoiceDAL, IMapper mapper, IOptions<AppSettings> appSettings, ILogger<ClaimsBAL> logger, IConfiguration configuration, IASBMessageSenderHelper asbMessageSenderHelper)
        {
            _invoiceBAL = invoiceBAL;
            _invoiceDAL = invoiceDAL;
            _claimsBAL = claimsBAL;
            _mapper = mapper;
            _appSettings = appSettings.Value;
            _logger = logger;
            this._asbMessageSenderHelper = asbMessageSenderHelper;
            this._configuration = configuration;
        }
        public async Task<ApiResponse<dynamic>> SubmitOEC(long invoice_Details_Id, long? patientDetailsId, BaseHttpRequestContext baseHttpRequestContext)
        {
            InvoiceDetailIdObject invoiceDetailIdObject = new()
            {
                Id = invoice_Details_Id,
                PatientDetailsId = patientDetailsId
            };
            return await SubmitEclipseClaim(invoiceDetailIdObject, baseHttpRequestContext, (short)ClaimType.OEC);
        }

        public async Task<ApiResponse<dynamic>> SubmitEclipseClaim(InvoiceDetailIdObject invoiceDetailIdObject, BaseHttpRequestContext baseHttpRequestContext,short claimTypeId)
        {
            long invoice_Details_Id = invoiceDetailIdObject.Id;
            long? patientDetailsId = invoiceDetailIdObject.PatientDetailsId;
            bool isWindows = System.Runtime.InteropServices.RuntimeInformation
                                                  .IsOSPlatform(OSPlatform.Windows);
            ApiResponse<dynamic> apiResponse = new();
            string serviceProviderNumber = string.Empty;
            InvoiceDetailView invoiceDetailView = null;
            string facilityId = string.Empty;
            bool error = false;
            
            if(patientDetailsId is null || patientDetailsId <= default(long))
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = null;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("Online Eligibility check cannot be performed with the data provided.");
                return apiResponse;
            }
            ApiResponse<InvoiceDetailView> apiResponseInv = await _invoiceBAL.GetInvoiceDetail(invoice_Details_Id, 0, baseHttpRequestContext);
            if (apiResponseInv is not null && apiResponseInv.StatusCode == StatusCodes.Status200OK && apiResponseInv.Result is not null)
            {
                invoiceDetailView = apiResponseInv.Result;
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = null;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("Invoice not found.");
                return apiResponse;
            }
            string minorId = string.Empty;
            string inPatientFacilityId = string.Empty;
            CompanyDetailInfo companyDetailInfo = null;
            if (invoiceDetailView is not null && invoiceDetailView.InvoiceStatusId != (short)InvoiceStatus.Void)
            {
                List<int> companyIds = new List<int> { (int)invoiceDetailView.CompanyDetailsId };
                if (invoiceDetailView.InvoiceAdditionalDetails is not null && invoiceDetailView.InvoiceAdditionalDetails.InPatientCompanyDetailsId is not null)
                    companyIds.Add((int)invoiceDetailView.InvoiceAdditionalDetails.InPatientCompanyDetailsId);
                List<CompanyDetailInfo> lstCompany = await _claimsBAL.GetCompanyByIds(companyIds, baseHttpRequestContext);
                string timeZone = string.Empty;
                if (lstCompany is not null)
                {
                    companyDetailInfo = (lstCompany is not null && lstCompany.Count > 0) ? lstCompany.Where(x=>x.Id==invoiceDetailView.CompanyDetailsId).FirstOrDefault() : null;
                    short? timeZoneId = (companyDetailInfo is null) ? null : companyDetailInfo.TimeZoneId;
                    if (timeZoneId is not null)
                    {
                        timeZone = (isWindows) ? EnumExtensions.GetDescription((WindowsTimeZone)(timeZoneId)) : EnumExtensions.GetDescription((LinuxTimeZone)(timeZoneId));
                    }
                    else
                    {
                        OrganisationView orgDetails = await _claimsBAL.FetchMasterCompanyDetails(baseHttpRequestContext);
                        timeZone = (isWindows) ? orgDetails.WindowsTimeZoneData : orgDetails.LinuxTimeZoneData;
                    }
                    minorId = (companyDetailInfo is null) ? null : companyDetailInfo.MinorId;

                    if (invoiceDetailView.InvoiceAdditionalDetails is not null && invoiceDetailView.InvoiceAdditionalDetails.InPatientCompanyDetailsId is not null)
                    {
                        inPatientFacilityId = lstCompany.Where(x => x.Id == invoiceDetailView.InvoiceAdditionalDetails.InPatientCompanyDetailsId).FirstOrDefault().FacilityId;
                    }
                }

                if (string.IsNullOrWhiteSpace(timeZone))
                {
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Result = null;
                    apiResponse.Message = "Failure.";
                    apiResponse.Errors.Add("Timezone could not be fetched correctly.");

                    return apiResponse;
                }
                if (string.IsNullOrWhiteSpace(minorId))
                {
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Result = null;
                    apiResponse.Message = "Failure";
                    apiResponse.Errors.Add("Minor Id could not be fetched correctly.");
                    return apiResponse;
                }
                DateTime todayDate = (DateTime)DateTimeConversion.ConvertTimeFromUtc(DateTime.UtcNow, timeZone);

                ProviderType serviceProvider = new();
                string serviceTypeCode = string.Empty;

                short? providerTypeId = (invoiceDetailView.ProviderDetails is not null) ? invoiceDetailView.ProviderDetails.ProviderTypeId : null;
                if (providerTypeId is not null && providerTypeId > default(short))
                {
                    serviceTypeCode = FetchServiceType(providerTypeId);

                }

                apiResponse = _claimsBAL.ValidateInvoiceForClaim(invoiceDetailView, todayDate, serviceTypeCode, baseHttpRequestContext, claimTypeId);
                if (apiResponse is not null && apiResponse.Errors is not null && apiResponse.Errors.Count > 0)
                {
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Result = null;
                    apiResponse.Message = "Failure";
                    return apiResponse;
                }
              
                DateTime? dateOfService = null;
                if (claimTypeId == (short)ClaimType.OEC || claimTypeId == (short)ClaimType.ECF || claimTypeId == (short)ClaimType.ECM)
                {
                    if (invoiceDetailView.DateOfService is not null)
                    {
                        dateOfService = invoiceDetailView.DateOfService;
                    }
                    else if (invoiceDetailView.AppointmentDetails is not null && invoiceDetailView.AppointmentDetails.DateOfAppointment is not null)
                    {
                        dateOfService = invoiceDetailView.AppointmentDetails.DateOfAppointment;
                    }
                }
                else  if(claimTypeId == (short)ClaimType.IMC){
                    if (invoiceDetailView.AppointmentDetails is not null && invoiceDetailView.AppointmentDetails.DateOfAppointment is not null)
                    {
                        dateOfService = invoiceDetailView.AppointmentDetails.DateOfAppointment;
                    }
                }

                if (dateOfService is null)
                {
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Result = null;
                    apiResponse.Message = "Failure";
                    apiResponse.Errors.Add("Date Of Service/Appointment Date is mandatory.");
                    return apiResponse;
                }
                ClaimantPatientDetailInfo claimantPatientDetailInfo = await _claimsBAL.FetchClaimantPatientInfo((long)patientDetailsId, baseHttpRequestContext, Constants.CLAIMTYPE_GENERAL);
                
                if (claimantPatientDetailInfo is not null && (
                    (claimantPatientDetailInfo.AccountHolderAssocInfos is not null && claimantPatientDetailInfo.AccountHolderAssocInfos.Where(x => x.AccountHolderType == (short)AccountHolderTypes.Medicare).Count() > 0)
                    || (claimantPatientDetailInfo.AccountHolderHealthfundAssocs is not null && claimantPatientDetailInfo.AccountHolderHealthfundAssocs.Count > 0)))
                {
                    
                    apiResponse = _claimsBAL.ValidatePatientDetails(invoiceDetailView, claimantPatientDetailInfo, todayDate, serviceTypeCode, (DateTime)dateOfService, claimTypeId);
                    if (apiResponse is not null && apiResponse.Errors is not null && apiResponse.Errors.Count > 0)
                    {
                        apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                        apiResponse.Result = null;
                        apiResponse.Message = "Failure";
                        return apiResponse;
                    }
                }
                else
                {
                    if (apiResponse is not null && apiResponse.Errors is not null && apiResponse.Errors.Count > 0)
                    {
                        apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                        apiResponse.Result = null;
                        apiResponse.Message = "Health Fund/Medicare Details are mandatory for performing Online Eligibility Check.";
                        return apiResponse;
                    }
                }

                string typeCode = FetchTypeCodeFromPatientInfo(claimantPatientDetailInfo, invoiceDetailView.IsInPatient);
                if (string.IsNullOrWhiteSpace(typeCode))
                {
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Result = null;
                    apiResponse.Errors.Add("Medicare and/ Fund details are mandatory");
                    return apiResponse;
                }
                if (Constants.ECLIPSE_OEC_TYPECODE_OEC == typeCode || claimTypeId==(short)ClaimType.ECF)
                {
                    if (claimTypeId == (short)ClaimType.ECF) typeCode = Constants.ECLIPSE_OEC_TYPECODE_ECF;
                    else if (claimTypeId == (short)ClaimType.ECM) typeCode = Constants.ECLIPSE_OEC_TYPECODE_ECM;

                    facilityId = (string.IsNullOrWhiteSpace(inPatientFacilityId)) ? ((companyDetailInfo!=null)?companyDetailInfo.FacilityId:string.Empty ): inPatientFacilityId;
                    if (string.IsNullOrWhiteSpace(facilityId))
                    {
                        apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                        apiResponse.Result = null;
                        apiResponse.Errors.Add("Facility Id must be supplied for Type Code OEC");
                        return apiResponse;
                    }
                }
                apiResponse = ValidateInvoiceAdditionalDetailsForClaim(invoiceDetailView, dateOfService, todayDate, typeCode, claimantPatientDetailInfo, baseHttpRequestContext, claimTypeId) ;
                if (apiResponse is not null && apiResponse.Errors is not null && apiResponse.Errors.Count > 0)
                {
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Result = null;
                    apiResponse.Message = "Failure";
                    return apiResponse;
                }

                //short claimTypeId = (short)ClaimType.OEC;
                if (apiResponse is not null && (apiResponse.Errors is null || apiResponse.Errors.Count == 0))
                {
                    ApiResponse<dynamic> apiResp = null;
                    if (claimTypeId == (short)ClaimType.OEC|| claimTypeId == (short)ClaimType.ECF || claimTypeId == (short)ClaimType.ECM)
                    {
                        OnlineEligibilityCheckRequestType oecRequestType = await PrepareOECBody(invoiceDetailView, companyDetailInfo, facilityId, serviceTypeCode, claimantPatientDetailInfo, todayDate, timeZone, baseHttpRequestContext, typeCode, serviceProviderNumber);
                        apiResp = await CallEclispeService(oecRequestType, null, claimTypeId, baseHttpRequestContext, minorId);
                        if (apiResp is not null)
                        {
                            apiResponse = await ProcessOECResponse(invoiceDetailView, (long)patientDetailsId, apiResp, claimTypeId, oecRequestType, minorId, baseHttpRequestContext);
                            return apiResponse;
                        }
                    }
                    else if (claimTypeId == (short)ClaimType.IMC)
                    {
                        InPatientMedicalClaimRequestType imcRequestType = await PrepareIMCBody(invoiceDetailView, companyDetailInfo, facilityId, serviceTypeCode, claimantPatientDetailInfo, todayDate, timeZone, baseHttpRequestContext, typeCode, serviceProviderNumber, invoiceDetailIdObject.EftDetails);
                        apiResp = await CallEclispeService(null, imcRequestType, claimTypeId, baseHttpRequestContext, minorId);


                        if (apiResp is not null)
                        {
                            apiResponse =await  ProcessIMCResponse(invoiceDetailView, (long)patientDetailsId,apiResp, claimTypeId, imcRequestType, minorId,baseHttpRequestContext);
                            return apiResponse;
                            //apiResponse.StatusCode = StatusCodes.Status200OK;
                            //apiResponse.Result = invoiceDetailView.Id;
                            //apiResponse.Message = "Success";
                        }
                    }
                    else
                    {
                        apiResponse.Errors.Add("Eclipse claims cannot be raised with the data provided.");
                        apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                        apiResponse.Result = null;
                        apiResponse.Message = "Failure";
                        return apiResponse;
                    }
                    
                    //}
                }
            }
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            return apiResponse;
        }

        private async Task<ApiResponse<dynamic>> ProcessIMCResponse(InvoiceDetailView invoiceDetailView, long patientDetailsId, ApiResponse<dynamic> apiResp, short claimTypeId, InPatientMedicalClaimRequestType imcRequestType, string minorId, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<dynamic> apiResponse = new();
            string errorText = string.Empty;
            string oecStatus = string.Empty;
            List<string> lstErrors =new();
            List<InvoiceMedicareAssoc> lstMedicareAssoc = null;
            short claimStatusId = default(short);
            InPatientMedicalClaimResponseType inPatientMedicalClaimResponse = null;
            bool sendToMedicareException = false;
            if (claimTypeId == (short)ClaimType.IMC)
            {
                object respObj = ConvertMedicareReponse(apiResp.Result, (short)ClaimType.IMC);
                if (respObj != null && "servicemessagestype" == respObj.GetType().Name.ToLower())
                {
                    claimStatusId = (short)ClaimStatus.Error;

                    ServiceMessagesType serviceMessageType = (ServiceMessagesType)respObj;
                    if (serviceMessageType is not null && serviceMessageType.ServiceMessage is not null)
                    {
                        serviceMessageType.ServiceMessage?.ToList().ForEach(msg =>
                        {
                            errorText = errorText + msg.Code + ":" + msg.Reason;
                        });
                        apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                        apiResponse.Errors.Add(errorText);
                    }

                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                }
                else if (respObj != null && "medicarejsonerrorresponse" == respObj.GetType().Name.ToLower())
                {
                    MedicareJsonErrorResponse errorResponse = (MedicareJsonErrorResponse)respObj;
                    if (errorResponse is not null && !string.IsNullOrWhiteSpace(errorResponse.Message))
                    {
                        errorText = errorText + errorResponse.Code+":" + errorResponse.Message;
                    }
                    claimStatusId = (short)ClaimStatus.Error;

                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Errors.Add(errorText);
                }
                else if (respObj != null && "inpatientmedicalclaimresponsetype" == respObj.GetType().Name.ToLower())
                {
                    inPatientMedicalClaimResponse = (InPatientMedicalClaimResponseType)respObj;
                    if (inPatientMedicalClaimResponse is not null)
                    {
                        if (inPatientMedicalClaimResponse.Status != null)
                        {
                            if (inPatientMedicalClaimResponse.Status.Equals(Constants.IMC_STATUS_MEDICARE_REJECTED) || inPatientMedicalClaimResponse.Status.Equals(Constants.IMC_STATUS_HEALTH_FUND_REJECTED))
                            {
                                claimStatusId = (short)ClaimStatus.Rejected;
                                if (inPatientMedicalClaimResponse.MedicareStatus != null && inPatientMedicalClaimResponse.MedicareStatus.Status != null)
                                {
                                    apiResponse.Errors.Add(inPatientMedicalClaimResponse.MedicareStatus.Status.Code + ":" + inPatientMedicalClaimResponse.MedicareStatus.Status.Text);
                                }
                                if (inPatientMedicalClaimResponse.HealthFundStatus != null && inPatientMedicalClaimResponse.HealthFundStatus.Status != null)
                                {
                                    apiResponse.Errors.Add(inPatientMedicalClaimResponse.HealthFundStatus.Status.Code + ":" + inPatientMedicalClaimResponse.HealthFundStatus.Status.Text);
                                }

                            }
                            else
                            {
                                if (inPatientMedicalClaimResponse.MedicareStatus != null && inPatientMedicalClaimResponse.MedicareStatus.Status != null)
                                {
                                    lstErrors.Add((inPatientMedicalClaimResponse.MedicareStatus.Status.Code + ":" + inPatientMedicalClaimResponse.MedicareStatus.Status.Text));
                                }
                                if (inPatientMedicalClaimResponse.HealthFundStatus != null && inPatientMedicalClaimResponse.HealthFundStatus.Status != null)
                                {
                                    lstErrors.Add(inPatientMedicalClaimResponse.HealthFundStatus.Status.Code + ":" + inPatientMedicalClaimResponse.HealthFundStatus.Status.Text);
                                }
                                claimStatusId = (short)ClaimStatus.Success;

                            }
                            apiResponse.StatusCode = StatusCodes.Status200OK;

                        }
                        else
                        {
                            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                            apiResponse.Errors.Add("IMC claim response could not be processed.");
                        }



                            //apiResponse.Result = inPatientMedicalClaimResponse;
                            // apiResponse.StatusCode = StatusCodes.Status200OK;
                            // apiResponse.Errors.Add("OEC response could not be processed.");
                    }
                }
                else
                {
                    if (apiResp.StatusCode == (StatusCodes.Status400BadRequest))
                    {
                        apiResponse = apiResp;
                    }
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Errors.Add("IMC claim response could not be processed.");
                }
                errorText = errorText +( (lstErrors != null && lstErrors.Count > 0) ? string.Join(',', lstErrors) : string.Empty);
                errorText = errorText+( (apiResponse.Errors is null || apiResponse.Errors.Count == 0) ? string.Empty : string.Join(',', apiResponse.Errors));

                lstMedicareAssoc = await SetInvoiceMedicareAssocForIMC(invoiceDetailView, patientDetailsId, minorId, claimTypeId, errorText, apiResp.TransactionId, baseHttpRequestContext, imcRequestType, inPatientMedicalClaimResponse, claimStatusId); ;
                if (lstMedicareAssoc is not null && lstMedicareAssoc.Any())
                {
                  //  InvoiceMedicareAssoc medicareAssoc= lstMedicareAssoc.FirstOrDefault();
                    apiResponse.Result = lstMedicareAssoc;                    
                    await StoreMedicareExceptionMessageForEclipse(baseHttpRequestContext.OrgCode, lstMedicareAssoc.Select(x=>x.Id).ToList());//TODO:Send message to Service Bus for MedicareException report

                }
                else
                    apiResponse.Result = null;
            }


            return apiResponse;
        }
         private async Task StoreMedicareExceptionMessageForEclipse(string orgCode, List<long> medicareAssocIdLst)
        {
            PaymentRequestDataModel paymentRequestDataModel = new PaymentRequestDataModel
            {
                OrgName = orgCode,
                PropertyType = MedicareRequestType.IMC,//(int)EODRequestDataType.EODReport,
                PropertyId = medicareAssocIdLst.ToArray(),

                PropertyValue = ""
            };
            Dictionary<string, string> userPros = new Dictionary<string, string>
                        {
                            { "RequestType","MedicareException"},
                            { "to",_configuration["AzureAD:ASBSubNameIMCWClaims"]}
                        };
            byte[] jsonContentByteArr = Encoding.GetEncoding("iso-8859-1").GetBytes(JsonConvert.SerializeObject(paymentRequestDataModel));
            await _asbMessageSenderHelper.MessageSenderTopic(jsonContentByteArr, userPros, _configuration["AzureAD:AzSBConnStringMedicare"], _configuration["AzureAD:ASBTopicMedicare"]);
        }
        private async Task<List<InvoiceMedicareAssoc>> SetInvoiceMedicareAssocForIMC(InvoiceDetailView invoiceDetailView, long patientDetailsId, string minorId, short claimTypeId, string error, string transactionId, BaseHttpRequestContext baseHttpRequestContext, InPatientMedicalClaimRequestType imcRequest,  InPatientMedicalClaimResponseType inPatientMedicalClaimResponse,short claimStatusId)
        {
            List<InvoiceMedicareAssoc> lstMedicareAssoc = new();

            List<InvoiceMedicareAssoc> lstDelete = await _invoiceDAL.GetActiveMedicareTransactionForClaimType(invoiceDetailView.Id, claimTypeId, baseHttpRequestContext.OrgId);
            lstDelete.ForEach(medicareAssocDelete =>
            {
                medicareAssocDelete.StatusId = (short)Status.Deleted;
                medicareAssocDelete.ModifiedBy = baseHttpRequestContext.UserId;
                medicareAssocDelete.ModifiedDate = DateTime.UtcNow;
            });

            foreach (EclipseMedicalEventType medEvent in imcRequest.Claim.MedicalEvent)
            {
                InvoiceMedicareAssoc medicareAssoc = new();
                medicareAssoc.InvoiceDetailsId = invoiceDetailView.Id;
                medicareAssoc.OrgId = baseHttpRequestContext.OrgId;
                medicareAssoc.MedicalEventId = medEvent.Id;
                medicareAssoc.PatientDetailsId = invoiceDetailView.AppointmentDetails.PatientDetailsId;
                medicareAssoc.IsInPatient = invoiceDetailView.IsInPatient;
                medicareAssoc.TransactionId = transactionId;
                medicareAssoc.ErrorText = error;
                medicareAssoc.MinorId = minorId;
                medicareAssoc.ServiceProviderNumber = medEvent.ServiceProvider.ProviderNumber;
                medicareAssoc.PayeeProviderNumber = imcRequest.Claim.PrincipalProvider.ProviderNumber;
                medicareAssoc.InvoiceMedicareStatus = (inPatientMedicalClaimResponse!=null && inPatientMedicalClaimResponse.Status!=null)?inPatientMedicalClaimResponse.Status:"ERROR";
                medicareAssoc.CreatedDate = DateTime.UtcNow;
                medicareAssoc.ModifiedDate = DateTime.UtcNow; ;
                medicareAssoc.ModifiedBy = baseHttpRequestContext.UserId;
                medicareAssoc.StatusId = (short)Status.Active;
                medicareAssoc.ClaimTypeId = claimTypeId;
                medicareAssoc.ClaimStatusId  = claimStatusId;
                medicareAssoc.ClaimTypeCode = (imcRequest.Claim is not null )?imcRequest.Claim.ClaimTypeCode:null;
                medicareAssoc.ClaimRequestBody = JsonConvert.SerializeObject(imcRequest);
                //string MedicareNo = medEvent.Patient?.Medicare?.MemberNumber;
                medicareAssoc.ClaimResponseBody = JsonConvert.SerializeObject(inPatientMedicalClaimResponse);
                //bool isValid = MedicareValidityCheck(MedicareNo);//TO check valid, member card
                //if (!isValid || string.IsNullOrWhiteSpace(MedicareNo))
                //{
                //    medicareAssoc.ErrorText = Constants.INVALID_MEDICARE_NUMBER_400;
                //    medicareAssoc.ClaimStatusId = 321;//(short)ClaimStatus.Error;
                //    medicareAssoc.InvoiceMedicareStatus = "Error";//ClaimStatus.Error.ToString();
                //}

                List<InvoiceItemsMedicareAssoc> lstItemsMedicareAssocs = new();
                medEvent.Service?.ToList().ForEach(service =>
                {
                    InvoiceItemsMedicareAssoc itemMedicareAssoc = new();
                    itemMedicareAssoc.OrgId = baseHttpRequestContext.OrgId;
                    itemMedicareAssoc.CreatedDate = DateTime.UtcNow;
                    itemMedicareAssoc.ModifiedDate = DateTime.UtcNow; ;
                    itemMedicareAssoc.ModifiedBy = baseHttpRequestContext.UserId;
                    itemMedicareAssoc.StatusId = (short)Status.Active;
                    itemMedicareAssoc.InvoiceEpisodeItemAssocsId = service.InvoiceEpisodeItemAssocsId;
                    itemMedicareAssoc.ServiceId = service.Id;
                    itemMedicareAssoc.ItemNumber = service.ItemNumber;
                    lstItemsMedicareAssocs.Add(itemMedicareAssoc);
                });
                medicareAssoc.InvoiceItemsMedicareAssocs = lstItemsMedicareAssocs;
                lstMedicareAssoc.Add(medicareAssoc);
            }
            if (lstDelete is not null && lstDelete.Count > 0)
                await _invoiceDAL.UpdateInvoiceMedicareAssocRange(lstDelete);
            await _invoiceDAL.AddInvoiceMedicareAssocRange(lstMedicareAssoc);
            return lstMedicareAssoc;
        }

        private async Task<InPatientMedicalClaimRequestType> PrepareIMCBody(InvoiceDetailView invoiceDetailView, CompanyDetailInfo companyDetailInfo, string facilityId, string serviceTypeCode, ClaimantPatientDetailInfo claimantPatientDetailInfo, DateTime todayDate, string timeZone, BaseHttpRequestContext baseHttpRequestContext, string typeCode, string serviceProviderNumber,InvoicePatientEftAssoc eftDetails =null)
        {
            InPatientMedicalClaimRequestType imcRequestType = new();
            InPatientMedicalClaimType imcClaim = await SetIMCClaim(invoiceDetailView, claimantPatientDetailInfo, companyDetailInfo, facilityId, serviceTypeCode, timeZone);
            if (imcClaim.ClaimTypeCode == Constants.ECLIPSE_CLAIMTYPE_PC)
            {
                imcClaim.Claimant = SetClaimant(claimantPatientDetailInfo, todayDate, eftDetails);
            }
            imcRequestType.Claim = imcClaim;
            return imcRequestType;
        }

        private async Task<InPatientMedicalClaimType> SetIMCClaim(InvoiceDetailView invoiceDetailView, ClaimantPatientDetailInfo claimantPatientDetailInfo, CompanyDetailInfo companyDetailInfo, string facilityId, string serviceTypeCode, string timeZone)
        {
            FundPatientType patient = SetFundPatientType(claimantPatientDetailInfo);

            InPatientMedicalClaimType imcClaim = new();
            imcClaim.Patient = patient;

            imcClaim.ServiceTypeCode = serviceTypeCode;
          
           
              
            if (invoiceDetailView.BillingScheduleTypeId == (short)BillingSchedules.No_Gap)
                imcClaim.ClaimTypeCode = Constants.ECLIPSE_CLAIMTYPE_AG;
            else if (invoiceDetailView.BillingScheduleTypeId == (short)BillingSchedules.Known_Gap)
                imcClaim.ClaimTypeCode = Constants.ECLIPSE_CLAIMTYPE_SC;
            else if (invoiceDetailView.AccountHolderTypeId == (short)AccountHolderTypes.Patient && invoiceDetailView.BillingScheduleTypeId == (short)BillingSchedules.Private)
                imcClaim.ClaimTypeCode = Constants.ECLIPSE_CLAIMTYPE_PC;

            
            if(imcClaim.ClaimTypeCode==Constants.ECLIPSE_CLAIMTYPE_AG || imcClaim.ClaimTypeCode == Constants.ECLIPSE_CLAIMTYPE_SC)
            {
                imcClaim.AccountPaidInd = "N";
                imcClaim.AccountReferenceId = (await _invoiceDAL.GetNextVal("[Invoice].[SeqIMCAccountRefId]")).ToString();
                imcClaim.FundPayeeId = (invoiceDetailView.InvoiceAdditionalDetails is not null && !string.IsNullOrWhiteSpace(invoiceDetailView.InvoiceAdditionalDetails.FundPayeeId)) ? invoiceDetailView.InvoiceAdditionalDetails.FundPayeeId : null;

            }
            else if(imcClaim.ClaimTypeCode == Constants.ECLIPSE_CLAIMTYPE_PC)
            {
                imcClaim.AccountReferenceId = (await _invoiceDAL.GetNextVal("[Invoice].[SeqIMC_PCAccountRefId]")).ToString();
                imcClaim.AccountReferenceId = (imcClaim.AccountReferenceId.Length > 9) ? imcClaim.AccountReferenceId.Substring(0, 9) : imcClaim.AccountReferenceId;
                imcClaim.SubmissionAuthorityInd = "Y";

                imcClaim.AccountPaidInd = "Y";

                if (invoiceDetailView.Owing <= 0)
                    imcClaim.AccountPaidInd = "Y";
                else
                {
                    imcClaim.AccountPaidInd = "N";
                }
            }

            imcClaim.FacilityId = facilityId;
            if (!string.IsNullOrWhiteSpace(companyDetailInfo.EclipseContactEmailAddress))
            {
                if (imcClaim.SenderContact is null)
                    imcClaim.SenderContact = new();
                imcClaim.SenderContact.EmailAddress = companyDetailInfo.EclipseContactEmailAddress;
            }
            if (!string.IsNullOrWhiteSpace(companyDetailInfo.EclipseContactPhoneNo))
            {
                if (imcClaim.SenderContact is null)
                    imcClaim.SenderContact = new();
                imcClaim.SenderContact.PhoneNumber = companyDetailInfo.EclipseContactPhoneNo;
            }

            //imcClaim.PeaRequestInd = (invoiceDetailView.InvoiceAdditionalDetails is not null && invoiceDetailView.InvoiceAdditionalDetails.PreExistingAilmentInd is not null && invoiceDetailView.InvoiceAdditionalDetails.PreExistingAilmentInd == true) ? "Y" : "N";
            imcClaim.CompensationClaimInd = (invoiceDetailView.InvoiceAdditionalDetails is not null && invoiceDetailView.InvoiceAdditionalDetails.CompensationClaimInd is not null && invoiceDetailView.InvoiceAdditionalDetails.CompensationClaimInd == true) ? "Y" : "N";
            //imcClaim.PresentingIllnessCode = (invoiceDetailView.InvoiceAdditionalDetails is not null && !string.IsNullOrWhiteSpace(invoiceDetailView.InvoiceAdditionalDetails.PresentingIllnessCode)) ? invoiceDetailView.InvoiceAdditionalDetails.PresentingIllnessCode : null;
            //if (string.IsNullOrWhiteSpace(imcClaim.PresentingIllnessCode))
            //{
            //    imcClaim.PresentingIllnessItemNumber = (invoiceDetailView.InvoiceAdditionalDetails is not null && !string.IsNullOrWhiteSpace(invoiceDetailView.InvoiceAdditionalDetails.PresentingIllnessItemNum)) ? invoiceDetailView.InvoiceAdditionalDetails.PresentingIllnessItemNum : null;

            //}
            imcClaim.Accident = new();
            imcClaim.Accident.AccidentInd = (invoiceDetailView.InvoiceAdditionalDetails is not null && invoiceDetailView.InvoiceAdditionalDetails.AccidentInd is not null && invoiceDetailView.InvoiceAdditionalDetails.AccidentInd == true) ? "Y" : "N";
            if (imcClaim.Accident.AccidentInd == "Y")
                imcClaim.Accident.AccidentDate = invoiceDetailView.InvoiceAdditionalDetails.AccidentDate;

            //if (invoiceDetailView.InvoiceAdditionalDetails is not null && invoiceDetailView.InvoiceAdditionalDetails.SameDayTypeId is not null)
            //{
            //    imcClaim.SameDayInd = (invoiceDetailView.InvoiceAdditionalDetails.SameDayTypeId == (short)SurgicalStayType.Day_only) ? "Y" : "N";
            //    if (imcClaim.SameDayInd == "N" && invoiceDetailView.InvoiceAdditionalDetails.AdmissionDate != null)
            //    {
            //        DateTime admissionDate = (DateTime)invoiceDetailView.InvoiceAdditionalDetails.AdmissionDate;
            //        DateTime dischargeDate = (DateTime)invoiceDetailView.InvoiceAdditionalDetails.DischargeDate;
            //        imcClaim.LengthOfStay = (dischargeDate - admissionDate).Days;
            //        if (imcClaim.LengthOfStay == 0) oecClaim.LengthOfStay = 1;
            //    }
            //}




         

            string serviceProviderNumber = invoiceDetailView.ProviderDetails.LstUserCompanyInfo.Where(x => x.UserCompanyId == invoiceDetailView.CompanyDetailsId).Select(x => x.ProviderNumber).FirstOrDefault();
            if(invoiceDetailView.PayeeCompanyDetailsId is not null && invoiceDetailView.PayeeProviderId is not null)
            {
                string principalProviderNumber = invoiceDetailView.PayeeProviderDetails?.LstUserCompanyInfo?.Where(x => x.UserCompanyId == invoiceDetailView.PayeeCompanyDetailsId).Select(x => x.ProviderNumber).FirstOrDefault();
                if(!string.IsNullOrWhiteSpace(principalProviderNumber))
                    imcClaim.PrincipalProvider = new()
                    {
                        ProviderNumber = principalProviderNumber
                    };
            }
           if(imcClaim.PrincipalProvider is null)
                imcClaim.PrincipalProvider = new()
                {
                    ProviderNumber = serviceProviderNumber
                };
            
           
         
            List<EclipseMedicalEventType> lstEclipseMedicalEvent = PopulateEclipseMedicalEventType(invoiceDetailView, claimantPatientDetailInfo,  timeZone, serviceProviderNumber,serviceTypeCode);
            imcClaim.MedicalEvent = lstEclipseMedicalEvent;
            //oecClaim.MedicalEvent = lstOECMedicalEvent;
            ////oecClaim.FundPayeeId = 
            ////oecClaim.emergencyAdmissionInd=
            ////oecClaim.hospitalInd

            ////oecClaim.PeaRequestInd
            ////oecClaim.PresentingIllnessCode;
            //// oecClaim.PresentingIllnessItemNumber

            ////if(invoiceDetailView.AppointmentDetails.SurgicalStayTypeId !=null && invoiceDetailView.AppointmentDetails.SurgicalStayTypeId == (short)SurgicalStayType.Day_only)
            ////{
            ////    oecClaim.SameDayInd = "Y";
            ////}
            ////else
            ////{
            ////    oecClaim.LengthOfStay = invoiceDetailView.AppointmentDetails.Days;
            ////    oecClaim.SameDayInd = "N";
            ////}
            return imcClaim;
        }

        private  List<EclipseMedicalEventType> PopulateEclipseMedicalEventType(InvoiceDetailView invoiceDetailView, ClaimantPatientDetailInfo claimantPatientDetailInfo, string timeZone, string serviceProviderNumber,string serviceTypeCode)
        {
            List<EclipseMedicalEventType> lstMedicalEvent = new();



            DateTime dateOfService = new();
            string timeOfService = null;
           
            if (invoiceDetailView.AppointmentDetails is not null && invoiceDetailView.AppointmentDetails.DateOfAppointment is not null)
            {
                dateOfService = (DateTime)invoiceDetailView.AppointmentDetails.DateOfAppointment;
            }
            TimeSpan? startTime = invoiceDetailView.AppointmentDetails.StartTime;
            if (startTime is not null)
            {
                DateTime appointmentDate = dateOfService.Add((TimeSpan)startTime);
                DateTime? appointmentDateTZ = DateTimeConversion.SetTimeZoneToDateTIme(appointmentDate, timeZone);
                if (appointmentDateTZ is not null)
                {
                    TimeSpan? utcOffSet = DateTimeConversion.GetUtcOffset((DateTime)appointmentDateTZ, timeZone);
                    if (utcOffSet is not null)
                    {
                        DateTimeOffset dtOffset = new DateTimeOffset((DateTime)appointmentDateTZ, (TimeSpan)utcOffSet);
                        timeOfService = dtOffset.ToString("HH:mm:sszzz");
                       
                    }
                }
            }
          
            int medicalEventCounter = 1;

            EclipseMedicalEventType eclipseMedicalEvent =InitialiseMedicalEvent(invoiceDetailView, medicalEventCounter,serviceProviderNumber, serviceTypeCode);

            List<EclipseServiceType> lstService = new();
           
            int serviceCounter = 0;
            int totalServiceCounter = 0;
            int totalMedicalEventServiceCounter = 0;
            invoiceDetailView.InvoiceEpisodeItemAssocs.ToList().ForEach(item =>
            {
                EclipseServiceType service = new();

                if (item.EpisodeTypeId == (short)EpisodeTypes.MBS && item.StatusId == (short)Status.Active && (item.ClaimStatusId == null ||(item.ClaimStatusId != (short)ClaimStatus.Complete && item.ClaimStatusId!=(short)ClaimStatus.Success) ))
                {
                    service.InvoiceEpisodeItemAssocsId = item.Id;
                    serviceCounter++;
                    totalServiceCounter++;
                    totalMedicalEventServiceCounter++;
                    service.Id = serviceCounter.ToString().PadLeft(4, '0');
                    service.DateOfService = dateOfService;
                    service.TimeOfService = timeOfService;
                    if (serviceTypeCode == Constants.CLAIMTYPE_SPECIALIST)
                    {
                        if (item.Quantity != null && item.Quantity > 1 && item.Quantity <=5)
                        {
                            service.FieldQuantity = item.Quantity.ToString();
                        }
                    }
                    if (totalMedicalEventServiceCounter > 14)
                    {
                        eclipseMedicalEvent.Service = lstService;
                        lstMedicalEvent.Add(eclipseMedicalEvent);

                        lstService = new();
                        medicalEventCounter++;
                        eclipseMedicalEvent = eclipseMedicalEvent.Clone();
                        eclipseMedicalEvent.Id = medicalEventCounter.ToString().PadLeft(2, '0');

                        totalMedicalEventServiceCounter = 1;
                        eclipseMedicalEvent.Service = null;
                        serviceCounter = 0;
                        

                    }
                    service.InvoiceEpisodeItemAssocsId = item.Id;
                    service.ItemNumber = item.ItemNumber.ToString();
                    string strAmount = (item.Fee != null) ? (item.Fee).ToString() : "0";
                    if (strAmount.IndexOf(".") != -1)
                    {
                        strAmount = strAmount.Replace(".", null);
                    }
                    if (!string.IsNullOrWhiteSpace(item.Notes))
                        service.Text = item.Notes;
                    if (!string.IsNullOrWhiteSpace(item.TimeDuration))
                    {
                        service.TimeDuration = item.TimeDuration.PadLeft(3, '0');
                    }

                    item.InvoiceEpisodeItemsIndicatorAssocs?.Where(x => x.StatusId == (short)Status.Active).ToList().ForEach(indicator =>
                    {
                        service = SetItemIndicators(service, indicator);
                    });
                    

                    service.ChargeAmount = strAmount;
                    lstService.Add(service);

                }
            });
            if (lstService is not null && lstService.Count > 0)
            {
                eclipseMedicalEvent.Service = lstService;
                lstMedicalEvent.Add(eclipseMedicalEvent);
            }
           
            return lstMedicalEvent;
        }

        private EclipseServiceType SetItemIndicators(EclipseServiceType service, InvoiceEpisodeItemsIndicatorViewAssoc indicator)
        {
            switch (indicator.InvoiceItemIndicatorTypeId)
            {
                case (short)Shared.Models.Enum.InvoiceItemIndicatorType.Restrictive_Override:
                    {
                        service.RestrictiveOverrideCode = indicator.Value;
                        return service;

                    }
                case (short)Shared.Models.Enum.InvoiceItemIndicatorType.Self_Deemed_Code:
                    {
                        service.SelfDeemedCode = indicator.Value;
                        return service;

                    }
                case (short)Shared.Models.Enum.InvoiceItemIndicatorType.Not_Duplicate:
                    {
                        if (indicator.Value == "Y")
                            service.DuplicateServiceOverrideInd = indicator.Value;
                        return service;

                    }
                case (short)Shared.Models.Enum.InvoiceItemIndicatorType.Not_Normal_Aftercare:
                    {
                        if (indicator.Value == "Y")
                            service.AftercareOverrideInd = indicator.Value;
                        return service;

                    }
                case (short)Shared.Models.Enum.InvoiceItemIndicatorType.Not_Multiple:
                    {
                        if (indicator.Value == "Y")
                            service.MultipleProcedureOverrideInd = indicator.Value;
                        return service;

                    }
                default:
                    return service;
            }
        }

        private EclipseMedicalEventType InitialiseMedicalEvent(InvoiceDetailView invoiceDetailView, int medicalEventCounter,string serviceProviderNumber,string serviceTypeCode)
        {
            EclipseMedicalEventType medEvent = new();
            medEvent.Id = medicalEventCounter.ToString().PadLeft(2, '0');
            medEvent.InvoiceDetailsId = invoiceDetailView.Id;
            if(invoiceDetailView.InvoiceAdditionalDetails is not null)
            {
                medEvent.AdmissionDate = invoiceDetailView.InvoiceAdditionalDetails.AdmissionDate;
                medEvent.DischargeDate = invoiceDetailView.InvoiceAdditionalDetails.DischargeDate;
                medEvent.DischargeDate = invoiceDetailView.InvoiceAdditionalDetails.DischargeDate;

                medEvent.FinancialInterestDisclosureInd = (invoiceDetailView.InvoiceAdditionalDetails.FinancialInterestDisclosureInd!=null && invoiceDetailView.InvoiceAdditionalDetails.FinancialInterestDisclosureInd==true)?"Y":"N" ;
                medEvent.IfcIssueCode = (string.IsNullOrWhiteSpace(invoiceDetailView.InvoiceAdditionalDetails.IfcissueCode))?null: invoiceDetailView.InvoiceAdditionalDetails.IfcissueCode;
                if (serviceTypeCode == Constants.CLAIMTYPE_SPECIALIST)
                {
                    if (string.IsNullOrWhiteSpace(invoiceDetailView.ReferralOverrideCode))
                    {
                        medEvent.Referral = _claimsBAL.SetReferralDetails(invoiceDetailView.AppointmentDetails.ReferralDetail, serviceTypeCode);
                    }
                    else
                    {
                        medEvent.ReferralOverrideCode = invoiceDetailView.ReferralOverrideCode;
                    }
                }
                if (!string.IsNullOrWhiteSpace(serviceProviderNumber))
                    medEvent.ServiceProvider = new ProviderType()
                    {
                        ProviderNumber = serviceProviderNumber
                    };
            }
            return medEvent;
        }

        private string FetchTypeCodeFromPatientInfo(ClaimantPatientDetailInfo claimantPatientDetailInfo, bool isInPatient)
        {
            //if (!isInPatient)
            //    return Constants.ECLIPSE_OEC_TYPECODE_ECM;
            //else return Constants.ECLIPSE_OEC_TYPECODE_OEC;
            AccountHolderAssocInfo accountHolderMedicare = (claimantPatientDetailInfo.AccountHolderAssocInfos is null) ? null : claimantPatientDetailInfo.AccountHolderAssocInfos.Where(x => x.AccountHolderType == (short)AccountHolderTypes.Medicare).FirstOrDefault();
            if (claimantPatientDetailInfo.AccountHolderHealthfundAssocs is not null && claimantPatientDetailInfo.AccountHolderHealthfundAssocs.Count > 0 && accountHolderMedicare is not null)
            {
                //if (accountHolderMedicare is null)
                //{
                //    return Constants.ECLIPSE_OEC_TYPECODE_ECF;
                //}

                return Constants.ECLIPSE_OEC_TYPECODE_OEC;
            }
            else if (accountHolderMedicare is not null)
            {
               return Constants.ECLIPSE_OEC_TYPECODE_ECM;
            }
            else if(claimantPatientDetailInfo.AccountHolderHealthfundAssocs is not null && claimantPatientDetailInfo.AccountHolderHealthfundAssocs.Count > 0)
            {
                return Constants.ECLIPSE_OEC_TYPECODE_ECF;
            }
            return string.Empty;
        }

        private ApiResponse<dynamic> ValidateInvoiceAdditionalDetailsForClaim(InvoiceDetailView invoiceDetailView, DateTime? dateOfService, DateTime todayDate, string typeCode, ClaimantPatientDetailInfo claimantPatientDetailInfo, BaseHttpRequestContext baseHttpRequestContext,short claimTypeId)
        {

            ApiResponse<dynamic> apiResponse = new();
            if (Constants.ECLIPSE_OEC_TYPECODE_ECM == typeCode || Constants.ECLIPSE_OEC_TYPECODE_OEC==typeCode)
            {
                AccountHolderAssocInfo accountHolderMedicare = (claimantPatientDetailInfo.AccountHolderAssocInfos is null) ? null : claimantPatientDetailInfo.AccountHolderAssocInfos.Where(x => x.AccountHolderType == (short)AccountHolderTypes.Medicare).FirstOrDefault();
                if(accountHolderMedicare is null ||(string.IsNullOrWhiteSpace(accountHolderMedicare.AccountNumber)|| string.IsNullOrWhiteSpace(accountHolderMedicare.AccountSubNumber)))
                {
                    apiResponse.Errors.Add("Patient Medicare Card Number and Patient Medicare Individual Reference Number must be supplied.");
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                }
            }
            if ( Constants.ECLIPSE_OEC_TYPECODE_OEC == typeCode || claimTypeId==(short)ClaimType.IMC || Constants.ECLIPSE_OEC_TYPECODE_ECF == typeCode)
            {
                if((claimantPatientDetailInfo.AccountHolderHealthfundAssocs is  null || claimantPatientDetailInfo.AccountHolderHealthfundAssocs.Count ==0))
                { 
                    apiResponse.Errors.Add("Patient Health Fund Details must be supplied for Type Codes OEC (Online Eligibility Check), ECF (Eligibility Check Fund).");
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                }
                else if(claimantPatientDetailInfo.AccountHolderHealthfundAssocs.Count >0)
                {
                    if(/*string.IsNullOrWhiteSpace(claimantPatientDetailInfo.AccountHolderHealthfundAssocs.FirstOrDefault().FundId)||*/ string.IsNullOrWhiteSpace(claimantPatientDetailInfo.AccountHolderHealthfundAssocs.FirstOrDefault().MembershipNumber))
                    {
                        apiResponse.Errors.Add("Health Fund Member Number must be supplied.");
                        apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    }
                }
                if (invoiceDetailView.InvoiceAdditionalDetails is null)
                {
                    apiResponse.Errors.Add("In patient details are mandatory.");
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    return apiResponse;
                }
                else
                {
                    if (string.IsNullOrWhiteSpace(invoiceDetailView.InvoiceAdditionalDetails.PresentingIllnessCode) && string.IsNullOrWhiteSpace(invoiceDetailView.InvoiceAdditionalDetails.PresentingIllnessItemNum))
                    {
                        apiResponse.Errors.Add("Presenting Illness Item Number must be supplied when the presenting Illness Code has not been supplied.");
                        apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    }
                    if (invoiceDetailView.InvoiceAdditionalDetails.SameDayTypeId is not null && invoiceDetailView.InvoiceAdditionalDetails.SameDayTypeId != (short)SurgicalStayType.Day_only)
                    {
                        if (invoiceDetailView.InvoiceAdditionalDetails.DischargeDate is null)
                        {
                            apiResponse.Errors.Add("Discharge Date must be supplied.");
                            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                        }
                    }
                    if(invoiceDetailView.InvoiceAdditionalDetails.AccidentInd is not null && invoiceDetailView.InvoiceAdditionalDetails.AccidentInd == true)
                    {
                        if(invoiceDetailView.InvoiceAdditionalDetails.AccidentDate is null)
                        {
                            apiResponse.Errors.Add("Accident Date must be supplied when Patient Accident Indicator is set.");
                            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                        }
                        else if (invoiceDetailView.InvoiceAdditionalDetails.AccidentDate is not null && invoiceDetailView.InvoiceAdditionalDetails.AccidentDate > todayDate)
                        {
                            apiResponse.Errors.Add("Invalid value supplied for Accident Date. The date supplied must not be in the future.");
                            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                        }
                    }
                    if (invoiceDetailView.InvoiceAdditionalDetails is not null && invoiceDetailView.InvoiceAdditionalDetails.AdmissionDate is null)
                    {
                        apiResponse.Errors.Add("Admission Date must be supplied for Type Codes OEC (Online Eligibility Check) or ECF (Eligibility Check Fund).");
                        apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    }
                    else
                    {
                        if(invoiceDetailView.InvoiceAdditionalDetails.AdmissionDate < claimantPatientDetailInfo.DateofBirth)
                        {
                            apiResponse.Errors.Add("Admission Date must not be before Patient Date of Birth.");
                            apiResponse.StatusCode = StatusCodes.Status400BadRequest;                            
                        }
                       

                        if (invoiceDetailView.InvoiceAdditionalDetails.AdmissionDate > dateOfService)
                        {
                            apiResponse.Errors.Add("The Admission date must not be after Date of Service.");
                            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                        }
                        if (invoiceDetailView.InvoiceAdditionalDetails.AdmissionDate > todayDate.AddMonths(12))
                        {
                            apiResponse.Errors.Add("The Admission Date must not be more than 12 months in the future.");
                            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                        }
                        else if (invoiceDetailView.InvoiceAdditionalDetails.AdmissionDate <todayDate.AddDays(-7) && claimTypeId==(short)ClaimType.OEC)
                        {
                            apiResponse.Errors.Add("The Admission Date must not be more than 7 days before the Date of Receipt.");
                            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                        }
                        if (invoiceDetailView.InvoiceAdditionalDetails.DischargeDate!=null && invoiceDetailView.InvoiceAdditionalDetails.DischargeDate < invoiceDetailView.InvoiceAdditionalDetails.AdmissionDate)
                        {
                            apiResponse.Errors.Add("The Discharge Date supplied must not be before Admission Date.");
                            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                        }
                        else if (invoiceDetailView.InvoiceAdditionalDetails.DischargeDate !=null && invoiceDetailView.InvoiceAdditionalDetails.DischargeDate > invoiceDetailView.InvoiceAdditionalDetails.AdmissionDate && claimTypeId == (short)ClaimType.OEC)
                        {
                            TimeSpan diff = (TimeSpan)(invoiceDetailView.InvoiceAdditionalDetails.DischargeDate - invoiceDetailView.InvoiceAdditionalDetails.AdmissionDate);
                            if (diff.Days > 999)
                            {
                                apiResponse.Errors.Add("The Length of Stay must not be greater than 999 days.");
                                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                            }
                        }
                       
                    }

                    
                }
                
            }

            if (Constants.ECLIPSE_OEC_TYPECODE_OEC == typeCode || Constants.ECLIPSE_OEC_TYPECODE_ECF == typeCode)
            {
                if(invoiceDetailView.AccountHolderTypeId != (short)AccountHolderTypes.Health_Fund && invoiceDetailView.AccountHolderTypeId != (short)AccountHolderTypes.Patient)
                {
                    apiResponse.Errors.Add("Invalid AccountHolder / Billing Schedule.");
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                }
                else if (invoiceDetailView.AccountHolderTypeId == (short)AccountHolderTypes.Health_Fund && (invoiceDetailView.BillingScheduleTypeId is not null && !(invoiceDetailView.BillingScheduleTypeId == (short)BillingSchedules.No_Gap || invoiceDetailView.BillingScheduleTypeId == (short)BillingSchedules.Known_Gap)))
                {
                    apiResponse.Errors.Add("Invalid AccountHolder / Billing Schedule.");
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                }
                else if (invoiceDetailView.AccountHolderTypeId == (short)AccountHolderTypes.Patient && invoiceDetailView.BillingScheduleTypeId != (short)BillingSchedules.Private )
                {
                    apiResponse.Errors.Add("Invalid AccountHolder / Billing Schedule.");
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                }
            }
            if (apiResponse.Errors is not null && apiResponse.Errors.Count > 0)
                return apiResponse;

           
            return apiResponse;
        }

        private async Task<ApiResponse<dynamic>> ProcessOECResponse(InvoiceDetailView invoiceDetailView, long patientDetailsId, ApiResponse<dynamic> apiResp, short claimTypeId, OnlineEligibilityCheckRequestType oecRequestType, string minorId, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<dynamic> apiResponse = new();
               string errorText = string.Empty;
            string oecStatus = string.Empty;
            List<InvoiceMedicareAssoc> lstMedicareAssoc = null;

            OnlineEligibilityCheckResponseType onlineEligibilityCheckResponse  = null;
            if (claimTypeId == (short)ClaimType.OEC || claimTypeId == (short)ClaimType.ECF || claimTypeId == (short)ClaimType.ECM)
            {
                object respObj = ConvertMedicareReponse(apiResp.Result, claimTypeId);
                if (respObj != null && "servicemessagestype" == respObj.GetType().Name.ToLower())
                {
                    ServiceMessagesType serviceMessageType = (ServiceMessagesType)respObj;
                    if (serviceMessageType is not null && serviceMessageType.ServiceMessage is not null)
                    {
                        serviceMessageType.ServiceMessage?.ToList().ForEach(msg =>
                        {
                            errorText = errorText + msg.Code + ":" + msg.Reason;
                        });
                        apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                        apiResponse.Errors.Add(errorText);
                    }

                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    //apiResponse.Errors.Add(errorText);
                }
                else if (respObj != null && "medicarejsonerrorresponse" == respObj.GetType().Name.ToLower())
                {
                    MedicareJsonErrorResponse errorResponse = (MedicareJsonErrorResponse)respObj;
                    if (errorResponse is not null && !string.IsNullOrWhiteSpace(errorResponse.Message))
                    {
                        errorText = errorText + errorResponse.Message;
                    }

                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Errors.Add(errorText);
                }
                else if (respObj != null && "onlineeligibilitycheckresponsetype" == respObj.GetType().Name.ToLower())
                {
                    onlineEligibilityCheckResponse = (OnlineEligibilityCheckResponseType)respObj;
                    if (onlineEligibilityCheckResponse is not null)
                    {
                        oecStatus = (onlineEligibilityCheckResponse.Status is null || string.IsNullOrWhiteSpace(onlineEligibilityCheckResponse.Status)) ? "ERROR" : onlineEligibilityCheckResponse.Status;
                        if (lstMedicareAssoc is not null && lstMedicareAssoc.Any())
                            apiResponse.Result = lstMedicareAssoc.FirstOrDefault()?.ClaimResponseBody;
                        else

                            apiResponse.Result = onlineEligibilityCheckResponse;
                        apiResponse.StatusCode = StatusCodes.Status200OK;
                       // apiResponse.Errors.Add("OEC response could not be processed.");
                    }
                }
                else
                {
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Errors.Add("OEC response could not be processed.");
                }
                string error = (apiResponse.Errors is null || apiResponse.Errors.Count ==0) ? string.Empty : string.Join(',', apiResponse.Errors);
                string fundName = null;
                if(oecRequestType.Claim.Patient.HealthFund is not null && !string.IsNullOrWhiteSpace(oecRequestType.Claim.Patient.HealthFund.Organisation))
                {
                    fundName = oecRequestType.Claim.Patient.HealthFund.Organisation;
                }
                string principalProviderNumber = oecRequestType.Claim.PrincipalProvider?.ProviderNumber;
                lstMedicareAssoc = await SetInvoiceMedicareAssoc(invoiceDetailView, patientDetailsId, minorId, claimTypeId, error, apiResp.TransactionId, baseHttpRequestContext, oecRequestType.Claim.MedicalEvent.ToList(), fundName,onlineEligibilityCheckResponse, principalProviderNumber);
                if (lstMedicareAssoc is not null && lstMedicareAssoc.Any())
                    apiResponse.Result = lstMedicareAssoc.FirstOrDefault();
                else
                    apiResponse.Result = null;
            }


            return apiResponse;
        }
        public object ConvertMedicareReponse(string response, short claimTypeId = 0)
        {
            if (response.ToLower().Contains("highestseverity"))
            {
                return JsonConvert.DeserializeObject<ServiceMessagesType>(response);
            }

            else if (claimTypeId!=(short)ClaimType.IMC && (response.ToLower().Contains("claimsummary") || response.ToLower().Contains("healthfundstatus") || response.ToLower().Contains("medicarestatus")))
            {
                return JsonConvert.DeserializeObject<OnlineEligibilityCheckResponseType>(response);

            }
            //else if (response.ToLower().Contains("status") && claimTypeId == (short)ClaimType.SDD)
            //{
            //    return JsonConvert.DeserializeObject<SameDayDeleteResponseType>(response);
            //}
            else if (response.ToLower().Contains("dhsein") && response.ToLower().Contains("codetype"))
            {
                return JsonConvert.DeserializeObject<MedicareJsonErrorResponse>(response);
            }
            else if (claimTypeId == (short)ClaimType.IMC &&((response.ToLower().Contains("success") && response.ToLower().Contains("status")) || response.ToLower().Contains("claimsummary") || response.ToLower().Contains("healthfundstatus") || response.ToLower().Contains("medicarestatus")))
            {
                return JsonConvert.DeserializeObject<InPatientMedicalClaimResponseType>(response);
            }
            
            return response;
        }
        private async  Task<OnlineEligibilityCheckRequestType> PrepareOECBody(InvoiceDetailView invoiceDetailView,CompanyDetailInfo companyDetailInfo, string facilityId,string serviceTypeCode, ClaimantPatientDetailInfo claimantPatientDetailInfo, DateTime todayDate, string timeZone, BaseHttpRequestContext baseHttpRequestContext, string typeCode, string serviceProviderNumber)
        {
            
            OnlineEligibilityCheckRequestType oecRequestType = new();
            OnlineEligibilityCheckClaimType oecClaim =await  SetOECClaim(invoiceDetailView, claimantPatientDetailInfo, companyDetailInfo, facilityId, serviceTypeCode,timeZone,typeCode);
            oecRequestType.Claim = oecClaim;
            return oecRequestType;
        }

        private async Task<OnlineEligibilityCheckClaimType> SetOECClaim(InvoiceDetailView invoiceDetailView, ClaimantPatientDetailInfo claimantPatientDetailInfo, CompanyDetailInfo companyDetailInfo,string facilityId, string serviceTypeCode, string timeZone,string typeCode)
        {
            FundPatientType patient = SetFundPatientType(claimantPatientDetailInfo, typeCode);

            OnlineEligibilityCheckClaimType oecClaim = new();
            oecClaim.Patient = patient;
            oecClaim.SubmissionAuthorityInd = "Y";
            oecClaim.TypeCode = typeCode;
            if (oecClaim.TypeCode == Constants.ECLIPSE_OEC_TYPECODE_ECM)
            {
                oecClaim.ServiceTypeCode = serviceTypeCode;
                oecClaim.AccountReferenceId = "ECM" + await _invoiceDAL.GetNextVal("[Invoice].[SeqOECAccountRefId]");

            }

            else if (oecClaim.TypeCode== Constants.ECLIPSE_OEC_TYPECODE_OEC)
            {
                oecClaim.ServiceTypeCode = serviceTypeCode;
                if (invoiceDetailView.BillingScheduleTypeId == (short)BillingSchedules.No_Gap)
                    oecClaim.ClaimTypeCode = Constants.ECLIPSE_CLAIMTYPE_AG;
                else if (invoiceDetailView.BillingScheduleTypeId == (short)BillingSchedules.Known_Gap)
                    oecClaim.ClaimTypeCode = Constants.ECLIPSE_CLAIMTYPE_SC;
                else if (invoiceDetailView.AccountHolderTypeId==(short)AccountHolderTypes.Patient && invoiceDetailView.BillingScheduleTypeId == (short)BillingSchedules.Private)
                    oecClaim.ClaimTypeCode = Constants.ECLIPSE_CLAIMTYPE_PC;
                else if (invoiceDetailView.AccountHolderTypeId == (short)AccountHolderTypes.Health_Fund && invoiceDetailView.BillingScheduleTypeId is null)
                    oecClaim.ClaimTypeCode = Constants.ECLIPSE_CLAIMTYPE_AG;
                oecClaim.AccountReferenceId = "OEC" + await _invoiceDAL.GetNextVal("[Invoice].[SeqOECAccountRefId]");

            }
            else if (oecClaim.TypeCode == Constants.ECLIPSE_OEC_TYPECODE_ECF)
            {
                oecClaim.AccountReferenceId = "ECF" + await _invoiceDAL.GetNextVal("[Invoice].[SeqOECAccountRefId]");

            }

            if (!string.IsNullOrWhiteSpace(facilityId))
                oecClaim.FacilityId = facilityId;
            if (!string.IsNullOrWhiteSpace(companyDetailInfo.EclipseContactEmailAddress))
            {
                if (oecClaim.SenderContact is null)
                    oecClaim.SenderContact = new();
                oecClaim.SenderContact.EmailAddress = companyDetailInfo.EclipseContactEmailAddress;
            }
            if (!string.IsNullOrWhiteSpace(companyDetailInfo.EclipseContactPhoneNo))
            {
                if (oecClaim.SenderContact is null)
                    oecClaim.SenderContact = new();
                oecClaim.SenderContact.PhoneNumber = companyDetailInfo.EclipseContactPhoneNo;
            }
            if (oecClaim.TypeCode!= Constants.ECLIPSE_OEC_TYPECODE_ECM)
            {   
                oecClaim.PeaRequestInd = (invoiceDetailView.InvoiceAdditionalDetails is not null && invoiceDetailView.InvoiceAdditionalDetails.PreExistingAilmentInd is not null && invoiceDetailView.InvoiceAdditionalDetails.PreExistingAilmentInd == true) ? "Y" : "N";
                oecClaim.CompensationClaimInd = (invoiceDetailView.InvoiceAdditionalDetails is not null && invoiceDetailView.InvoiceAdditionalDetails.CompensationClaimInd is not null && invoiceDetailView.InvoiceAdditionalDetails.CompensationClaimInd == true) ? "Y" : "N";
                oecClaim.PresentingIllnessCode = (invoiceDetailView.InvoiceAdditionalDetails is not null && !string.IsNullOrWhiteSpace(invoiceDetailView.InvoiceAdditionalDetails.PresentingIllnessCode)) ? invoiceDetailView.InvoiceAdditionalDetails.PresentingIllnessCode : null;
                if (string.IsNullOrWhiteSpace(oecClaim.PresentingIllnessCode))
                {
                    oecClaim.PresentingIllnessItemNumber = (invoiceDetailView.InvoiceAdditionalDetails is not null && !string.IsNullOrWhiteSpace(invoiceDetailView.InvoiceAdditionalDetails.PresentingIllnessItemNum)) ? invoiceDetailView.InvoiceAdditionalDetails.PresentingIllnessItemNum : null;

                }
                oecClaim.Accident = new();
                oecClaim.Accident.AccidentInd = (invoiceDetailView.InvoiceAdditionalDetails is not null && invoiceDetailView.InvoiceAdditionalDetails.AccidentInd is not null && invoiceDetailView.InvoiceAdditionalDetails.AccidentInd == true) ? "Y" : "N";
                if (oecClaim.Accident.AccidentInd == "Y")
                {
                    oecClaim.Accident.AccidentDate = invoiceDetailView.InvoiceAdditionalDetails.AccidentDate;
                }
                if (invoiceDetailView.InvoiceAdditionalDetails is not null && invoiceDetailView.InvoiceAdditionalDetails.SameDayTypeId is not null)
                {
                    oecClaim.SameDayInd = (invoiceDetailView.InvoiceAdditionalDetails.SameDayTypeId == (short)SurgicalStayType.Day_only) ? "Y" : "N";
                    if (oecClaim.SameDayInd=="N" && invoiceDetailView.InvoiceAdditionalDetails.AdmissionDate != null)
                    {
                        DateTime admissionDate = (DateTime)invoiceDetailView.InvoiceAdditionalDetails.AdmissionDate;
                        DateTime dischargeDate = (DateTime)invoiceDetailView.InvoiceAdditionalDetails.DischargeDate;
                        oecClaim.LengthOfStay = (dischargeDate - admissionDate).Days;
                        if (oecClaim.LengthOfStay == 0) oecClaim.LengthOfStay = 1;
                    }
                }

            }

        
            if (oecClaim.TypeCode == Constants.ECLIPSE_OEC_TYPECODE_OEC)
            {
                
                oecClaim.FundPayeeId = (invoiceDetailView.InvoiceAdditionalDetails is not null && !string.IsNullOrWhiteSpace(invoiceDetailView.InvoiceAdditionalDetails.FundPayeeId)) ? invoiceDetailView.InvoiceAdditionalDetails.FundPayeeId : null;
            }
            
            string serviceProviderNumber = invoiceDetailView.ProviderDetails.LstUserCompanyInfo.Where(x => x.UserCompanyId == invoiceDetailView.CompanyDetailsId).Select(x => x.ProviderNumber).FirstOrDefault();
            if (oecClaim.TypeCode != Constants.ECLIPSE_OEC_TYPECODE_ECF)
            {
                if (invoiceDetailView.PayeeCompanyDetailsId is not null && invoiceDetailView.PayeeProviderId is not null)
                {
                    string principalProviderNumber = invoiceDetailView.PayeeProviderDetails?.LstUserCompanyInfo?.Where(x => x.UserCompanyId == invoiceDetailView.PayeeCompanyDetailsId).Select(x => x.ProviderNumber).FirstOrDefault();
                    if (!string.IsNullOrWhiteSpace(principalProviderNumber))
                        oecClaim.PrincipalProvider = new()
                        {
                            ProviderNumber = principalProviderNumber
                        };
                }
                if (oecClaim.PrincipalProvider is null)
                    oecClaim.PrincipalProvider = new()
                    {
                        ProviderNumber = serviceProviderNumber
                    };
            }
              

            List<OnlineEligibilityCheckMedicalEventType> lstOECMedicalEvent = PopulateOECMedicalEventType(invoiceDetailView, claimantPatientDetailInfo, oecClaim.TypeCode, timeZone, serviceProviderNumber);
            oecClaim.MedicalEvent = lstOECMedicalEvent;
            //oecClaim.FundPayeeId = 
            //oecClaim.emergencyAdmissionInd=
            //oecClaim.hospitalInd

            //oecClaim.PeaRequestInd
            //oecClaim.PresentingIllnessCode;
            // oecClaim.PresentingIllnessItemNumber

            //if(invoiceDetailView.AppointmentDetails.SurgicalStayTypeId !=null && invoiceDetailView.AppointmentDetails.SurgicalStayTypeId == (short)SurgicalStayType.Day_only)
            //{
            //    oecClaim.SameDayInd = "Y";
            //}
            //else
            //{
            //    oecClaim.LengthOfStay = invoiceDetailView.AppointmentDetails.Days;
            //    oecClaim.SameDayInd = "N";
            //}
            return oecClaim;

        }

        private FundPatientType SetFundPatientType(ClaimantPatientDetailInfo claimantPatientDetailInfo,string typeCode =null)
        {
            FundPatientType patient = new();
            IdentityType identity = new();
            MembershipType medicare = new();
            FundMembershipType healthFund = new();
            identity.DateOfBirth = new DateTimeOffset((DateTime)claimantPatientDetailInfo.DateofBirth,TimeSpan.Zero);
            //identity.DateOfBirth = .;//DateTimeOffset/2010 - 11 - 04 "2010-11-04";
            identity.GivenName = claimantPatientDetailInfo.FirstName.Trim();
            identity.FamilyName = claimantPatientDetailInfo.SurName.Trim();
            patient.Identity = identity;
            patient.Identity.Sex = SetGender(claimantPatientDetailInfo.GenderId);

            string[] alsoKnownAsArr = null;
            if (!string.IsNullOrWhiteSpace(claimantPatientDetailInfo.AlsoKnownAs) && Constants.ECLIPSE_OEC_TYPECODE_ECM != typeCode)
            {
                alsoKnownAsArr = claimantPatientDetailInfo.AlsoKnownAs.Split(' ', StringSplitOptions.RemoveEmptyEntries);
                if (alsoKnownAsArr?.Length == 2)
                {
                    IdentityType alsoKnownAs = new();
                    alsoKnownAs.GivenName = alsoKnownAsArr[0];
                    alsoKnownAs.FamilyName = alsoKnownAsArr[1];
                    patient.AlsoKnownAs = alsoKnownAs;

                }
                else if (alsoKnownAsArr?.Length == 1)
                {
                    IdentityType alsoKnownAs = new();

                    alsoKnownAs.GivenName = alsoKnownAsArr[0];
                    patient.AlsoKnownAs = alsoKnownAs;

                }
            }

            AccountHolderAssocInfo accountHolderMedicare = (claimantPatientDetailInfo.AccountHolderAssocInfos is null) ? null : claimantPatientDetailInfo.AccountHolderAssocInfos.Where(x => x.AccountHolderType == (short)AccountHolderTypes.Medicare).FirstOrDefault();
            if(accountHolderMedicare != null  && Constants.ECLIPSE_OEC_TYPECODE_ECF != typeCode)
            {
                medicare.MemberNumber = accountHolderMedicare.AccountNumber;
                medicare.MemberRefNumber = accountHolderMedicare.AccountSubNumber;
                patient.Medicare = medicare;
            }

            if(claimantPatientDetailInfo.AccountHolderHealthfundAssocs is not null && claimantPatientDetailInfo.AccountHolderHealthfundAssocs.Any() && Constants.ECLIPSE_OEC_TYPECODE_ECM!=typeCode)
            {
                AccountHolderHealthfundAssoc healthFundAssoc = claimantPatientDetailInfo.AccountHolderHealthfundAssocs.FirstOrDefault();
                healthFund.MemberNumber = healthFundAssoc.MembershipNumber;                
                healthFund.MemberRefNumber = (string.IsNullOrWhiteSpace(healthFundAssoc.FundId))?null: healthFundAssoc.FundId;
                healthFund.Organisation = healthFundAssoc.Name;

                patient.HealthFund = healthFund;
            }
           
            //patientClaimInteractiveType.Patient = patient;

            //if (!patientMinor)
            //{
            //    claimant.Identity = identity;
            //    claimant.Medicare = medicare;

            //}
            //else
            //{
            //    IdentityType identityClaimant = new();
            //    MembershipType medicareClaimant = new();

            //    if (claimantPatientDetailInfo.AccountHolderParentGuardianAssocInfo is not null)
            //    {
            //        identityClaimant.DateOfBirth = claimantPatientDetailInfo.AccountHolderParentGuardianAssocInfo.DateOfBirth;
            //        identityClaimant.FamilyName = claimantPatientDetailInfo.AccountHolderParentGuardianAssocInfo.LastName.Trim();
            //        identityClaimant.GivenName = claimantPatientDetailInfo.AccountHolderParentGuardianAssocInfo.FirstName.Trim();

            //        medicareClaimant.MemberNumber = claimantPatientDetailInfo.AccountHolderParentGuardianAssocInfo.MedicareCardNumber;
            //        medicareClaimant.MemberRefNumber = claimantPatientDetailInfo.AccountHolderParentGuardianAssocInfo.MedicareCardIrn;

            //        claimant.Identity = identityClaimant;
            //        claimant.Medicare = medicareClaimant;
            //    }
            //}
            //patientClaimInteractiveType.Claimant = claimant;

            return patient;
        }


        private ClaimantType SetClaimant(ClaimantPatientDetailInfo claimantPatientDetailInfo,DateTime todayDate, InvoicePatientEftAssoc eftDetails = null)
        {
            ClaimantType claimant = new();
            IdentityType identity = new();
            MembershipType medicare = new();
            FundMembershipType healthFund = new();
            Capstone2.Shared.Models.Medicare.AddressType residentialAddres = new();           

            bool patientMinor = _claimsBAL.CheckPatientMinor((DateTime)claimantPatientDetailInfo.DateofBirth, todayDate);
            if (!patientMinor)
            {
                identity.DateOfBirth = claimantPatientDetailInfo.DateofBirth;//DateTimeOffset/2010 - 11 - 04 "2010-11-04";
                identity.GivenName = claimantPatientDetailInfo.FirstName.Trim();
                identity.FamilyName = claimantPatientDetailInfo.SurName.Trim();
                claimant.Identity = identity;
                
                AccountHolderAssocInfo accountHolderMedicare = (claimantPatientDetailInfo.AccountHolderAssocInfos is null) ? null : claimantPatientDetailInfo.AccountHolderAssocInfos.Where(x => x.AccountHolderType == (short)AccountHolderTypes.Medicare).FirstOrDefault();

                medicare.MemberNumber = accountHolderMedicare.AccountNumber;
                medicare.MemberRefNumber = accountHolderMedicare.AccountSubNumber;
                claimant.Medicare = medicare;
                ContactType contactDetails = new();
                if (claimantPatientDetailInfo.PatientAddress is not null)
                {
                    residentialAddres.AddressLineOne = claimantPatientDetailInfo.PatientAddress.AddressLine1;
                    residentialAddres.AddressLineTwo = string.IsNullOrWhiteSpace(claimantPatientDetailInfo.PatientAddress.AddressLine2) ? null : claimantPatientDetailInfo.PatientAddress.AddressLine2;
                    residentialAddres.Locality = claimantPatientDetailInfo.PatientAddress.Suburb;
                    residentialAddres.Postcode = claimantPatientDetailInfo.PatientAddress.PostCode;
                    claimant.ResidentialAddress = residentialAddres;


                }
                if (!string.IsNullOrWhiteSpace(claimantPatientDetailInfo.Mobile))
                {
                    contactDetails.PhoneNumber = claimantPatientDetailInfo.Mobile;
                    claimant.ContactDetails = contactDetails;
                }
                else if (!string.IsNullOrWhiteSpace(claimantPatientDetailInfo.HomeContact))
                {

                    contactDetails.PhoneNumber = claimantPatientDetailInfo.HomeContact;
                }
                else if (!string.IsNullOrWhiteSpace(claimantPatientDetailInfo.WorkContact))
                {
                    contactDetails.PhoneNumber = claimantPatientDetailInfo.WorkContact;
                    claimant.ContactDetails = contactDetails;

                }
            }

            else
            {
                if (claimantPatientDetailInfo.AccountHolderParentGuardianAssocInfo is not null)
                {
                    identity.DateOfBirth = claimantPatientDetailInfo.AccountHolderParentGuardianAssocInfo.DateOfBirth;
                    identity.FamilyName = claimantPatientDetailInfo.AccountHolderParentGuardianAssocInfo.LastName.Trim();
                    identity.GivenName = claimantPatientDetailInfo.AccountHolderParentGuardianAssocInfo.FirstName.Trim();

                    medicare.MemberNumber = claimantPatientDetailInfo.AccountHolderParentGuardianAssocInfo.MedicareCardNumber;
                    medicare.MemberRefNumber = claimantPatientDetailInfo.AccountHolderParentGuardianAssocInfo.MedicareCardIrn;

                    claimant.Identity = identity;
                    claimant.Medicare = medicare;
                    if (claimantPatientDetailInfo.AccountHolderAddress is not null && claimantPatientDetailInfo.AccountHolderAddress.AccountHolderType == (short)AccountHolderTypes.ParentOrGuardian)
                    {
                        residentialAddres.AddressLineOne = claimantPatientDetailInfo.AccountHolderAddress.AddressLine1;
                        residentialAddres.AddressLineTwo = string.IsNullOrWhiteSpace(claimantPatientDetailInfo.AccountHolderAddress.AddressLine2) ? null : claimantPatientDetailInfo.AccountHolderAddress.AddressLine2;
                        residentialAddres.Locality = claimantPatientDetailInfo.AccountHolderAddress.Suburb;
                        residentialAddres.Postcode = claimantPatientDetailInfo.AccountHolderAddress.PostCode;
                        claimant.ResidentialAddress = residentialAddres;
                    }

                }
            }
            if(claimant is not null && eftDetails is not null)
            {
                BankAccountType tempEftDetails = _mapper.Map<InvoicePatientEftAssoc, BankAccountType>(eftDetails);
                claimant.EftDetails = tempEftDetails;
            }
            return claimant;
        }
        private async Task<List<InvoiceMedicareAssoc>> SetInvoiceMedicareAssoc
           (InvoiceDetailView invoiceDetailView, long patientDetailsId, string minorId,short claimTypeId,string errorText,string transactionId,
           BaseHttpRequestContext baseHttpRequestContext, List<OnlineEligibilityCheckMedicalEventType> lstMedicalEvent,string fundName,OnlineEligibilityCheckResponseType oecResponse,string principalProvider=null)
        {
            List<InvoiceMedicareAssoc> lstMedicareAssoc = new();

            List<InvoiceMedicareAssoc> lstDelete = await _invoiceDAL.GetActiveMedicareTransactionForClaimType(invoiceDetailView.Id, claimTypeId, baseHttpRequestContext.OrgId);
            lstDelete.ForEach(medicareAssocDelete =>
            {
                medicareAssocDelete.StatusId = (short)Status.Deleted;
                medicareAssocDelete.ModifiedBy = baseHttpRequestContext.UserId;
                medicareAssocDelete.ModifiedDate = DateTime.UtcNow;
            });
           Dictionary<long, string> lstDictionary = new();
            foreach (OnlineEligibilityCheckMedicalEventType medEvent in lstMedicalEvent)
            {
                InvoiceMedicareAssoc medicareAssoc = new();
                medicareAssoc.InvoiceDetailsId = invoiceDetailView.Id;
                medicareAssoc.OrgId = baseHttpRequestContext.OrgId;
                medicareAssoc.PatientDetailsId = patientDetailsId;
                medicareAssoc.IsInPatient = invoiceDetailView.IsInPatient;
                medicareAssoc.FacilityId = null;
                medicareAssoc.MinorId = minorId;
                medicareAssoc.ErrorText = errorText;
                medicareAssoc.PayeeProviderNumber = principalProvider;
                //medicareAssoc.ServiceProviderNumber = null;
                //medicareAssoc.ServiceTypeCode = serviceTypeCode;
                medicareAssoc.CreatedDate = DateTime.UtcNow;
                medicareAssoc.ModifiedDate = DateTime.UtcNow; ;
                medicareAssoc.ModifiedBy = baseHttpRequestContext.UserId;
                medicareAssoc.StatusId = (short)Status.Active;
                medicareAssoc.ClaimTypeId = claimTypeId;
                medicareAssoc.ClaimStatusId = null;
                medicareAssoc.ClaimRequestBody = null;
                medicareAssoc.TransactionId = transactionId;
                medicareAssoc.FundName = fundName;
               // string MedicareNo = medEvent.Patient?.Medicare?.MemberNumber;
               //  bool isValid = MedicareValidityCheck(MedicareNo);//TO check valid, member card
               //if (!isValid || string.IsNullOrWhiteSpace(MedicareNo))
               //{
               //    medicareAssoc.ErrorText = Constants.INVALID_MEDICARE_NUMBER_400;
               //    medicareAssoc.ClaimStatusId = 321;//(short)ClaimStatus.Error;
               //    medicareAssoc.InvoiceMedicareStatus = "Error";//ClaimStatus.Error.ToString();
               //}

                List<InvoiceItemsMedicareAssoc> lstItemsMedicareAssocs = new();
                medEvent.Service?.ToList().ForEach(service =>
                {
                    InvoiceItemsMedicareAssoc itemMedicareAssoc = new();
                    itemMedicareAssoc.OrgId = baseHttpRequestContext.OrgId;
                    itemMedicareAssoc.CreatedDate = DateTime.UtcNow;
                    itemMedicareAssoc.ModifiedDate = DateTime.UtcNow; ;
                    itemMedicareAssoc.ModifiedBy = baseHttpRequestContext.UserId;
                    itemMedicareAssoc.StatusId = (short)Status.Active;
                    itemMedicareAssoc.InvoiceEpisodeItemAssocsId = service.InvoiceEpisodeItemAssocsId;
                    itemMedicareAssoc.ServiceId = service.Id;
                    itemMedicareAssoc.ItemNumber = service.ItemNumber;
                    lstItemsMedicareAssocs.Add(itemMedicareAssoc);
                    if(service.InvoiceEpisodeItemAssocsId!=null)
                        lstDictionary.Add((long)service.InvoiceEpisodeItemAssocsId, service.Id);
                });
                medicareAssoc.InvoiceItemsMedicareAssocs = lstItemsMedicareAssocs;
                if (oecResponse!=null && oecResponse.MedicareClaimEstimation is not null && oecResponse.MedicareClaimEstimation.MedicalEvent is not null && oecResponse.MedicareClaimEstimation.MedicalEvent.Any())
                {
                    MedicareMedicalEventEstimationType medEventResp = oecResponse.MedicareClaimEstimation.MedicalEvent.Where(x => x.Id == medEvent.Id).FirstOrDefault();
                    medEventResp.Service?.ToList().ForEach(serviceResp =>
                        {
                            serviceResp.InvoiceEpisodeItemAssocsId = lstDictionary.FirstOrDefault(x => x.Value == serviceResp.Id).Key;
                        });
                   
                }
                if (oecResponse != null && oecResponse.HealthFundClaimEstimation is not null && oecResponse.HealthFundClaimEstimation.MedicalEvent is not null && oecResponse.HealthFundClaimEstimation.MedicalEvent.Any())
                {
                    HealthFundMedicalEventEstimationType medEventResp = oecResponse.HealthFundClaimEstimation.MedicalEvent.Where(x => x.Id == medEvent.Id).FirstOrDefault();
                    medEventResp.Service?.ToList().ForEach(serviceResp =>
                    {
                        serviceResp.InvoiceEpisodeItemAssocsId = lstDictionary.FirstOrDefault(x => x.Value == serviceResp.Id).Key;
                    });

                }
                medicareAssoc.ClaimResponseBody = (oecResponse is null)?null:JsonConvert.SerializeObject(oecResponse);
                lstMedicareAssoc.Add(medicareAssoc);
            }
            
            
            //if (oecResponse.HealthFundClaimEstimation is not null && oecResponse.HealthFundClaimEstimation.MedicalEvent is not null && oecResponse.HealthFundClaimEstimation.MedicalEvent.Any())
            //{
            //    oecResponse.HealthFundClaimEstimation.MedicalEvent.ToList().ForEach(x => {
            //        x.Service?.ToList().ForEach(serviceResp =>
            //        {
            //            serviceResp.InvoiceEpisodeItemAssocsId = lstDictionary.FirstOrDefault(x => x.Value == serviceResp.Id).Key;
            //        });
            //    });
            //}


            if (lstDelete is not null && lstDelete.Count > 0)
                await _invoiceDAL.UpdateInvoiceMedicareAssocRange(lstDelete);
            await _invoiceDAL.AddInvoiceMedicareAssocRange(lstMedicareAssoc);
            return lstMedicareAssoc;

        }
        private string SetGender(short? genderId)
        {
            switch (genderId)
            {
                case (short)Shared.Models.Enum.Gender.Male:
                    {
                        
                        return "1";

                    }
                case (short)Shared.Models.Enum.Gender.Female:
                    {
                        return "2";

                    }
                case (short)Shared.Models.Enum.Gender.Other:
                    {
                        
                        return "3";

                    }
                case (short)Shared.Models.Enum.Gender.Unknown:
                    {
                       
                        return "9";

                    }
              
                default:
                    return "9";
            }
        }

        private List<OnlineEligibilityCheckMedicalEventType> PopulateOECMedicalEventType(InvoiceDetailView invoiceDetailView, ClaimantPatientDetailInfo claimantPatientDetailInfo,  string typeCode,string timeZone, string serviceProviderNumber)
        {
            List<OnlineEligibilityCheckMedicalEventType> lstMedicalEvent = new();

            OnlineEligibilityCheckMedicalEventType oecMedicalEvent = new();


            DateTime? dateOfService = null;
            if (invoiceDetailView.DateOfService is not null)
            {
                dateOfService = invoiceDetailView.DateOfService;
            }
            else if (invoiceDetailView.AppointmentDetails is not null && invoiceDetailView.AppointmentDetails.DateOfAppointment is not null)
            {
                dateOfService = invoiceDetailView.AppointmentDetails.DateOfAppointment;
            }
            //if (invoiceDetailView.AppointmentDetails is not null && invoiceDetailView.AppointmentDetails.DateOfAppointment is not null)
            //{
            //    dateOfService = invoiceDetailView.AppointmentDetails.DateOfAppointment;
            //}
            //else if (invoiceDetailView.DateOfService is not null)
            //{
            //    dateOfService = invoiceDetailView.DateOfService;
            //}
            //TimeSpan? startTime = invoiceDetailView.AppointmentDetails.StartTime;
            //if (startTime is not null)
            //{
            //    appointmentDate = appointmentDate.Add((TimeSpan)startTime);
            //    DateTime? appointmentDateTZ = DateTimeConversion.SetTimeZoneToDateTIme(appointmentDate, timeZone);
            //    if (appointmentDateTZ is not null)
            //    {
            //        TimeSpan? utcOffSet = DateTimeConversion.GetUtcOffset((DateTime)appointmentDateTZ, timeZone);
            //        if (utcOffSet is not null)
            //        {
            //            DateTimeOffset dtOffset = new DateTimeOffset((DateTime)appointmentDateTZ, (TimeSpan)utcOffSet);
            //            string mTime = dtOffset.ToString("HH:mm:sszzz");
           
            //            //bBSMedicalEvent.MedicalEventTime = mTime;
            //        }
            //    }
            //}
            List<OnlineEligibilityCheckServiceType> lstService = new();
            int medicalEventCounter = 1;
            OnlineEligibilityCheckServiceType service;
            oecMedicalEvent.Id = medicalEventCounter.ToString().PadLeft(2, '0');
            oecMedicalEvent.InvoiceDetailsId = invoiceDetailView.Id;
            int serviceCounter = 0;
            int totalServiceCounter = 0;
            int totalMedicalEventServiceCounter = 0;
            invoiceDetailView.InvoiceEpisodeItemAssocs.ToList().ForEach(item =>
            {
                OnlineEligibilityCheckServiceType service = new();

                if (item.EpisodeTypeId == (short)EpisodeTypes.MBS && item.StatusId == (short)Status.Active && (item.ClaimStatusId == null || item.ClaimStatusId != (short)ClaimStatus.Complete))
                {
                    service.InvoiceEpisodeItemAssocsId = item.Id;
                    serviceCounter++;
                    totalServiceCounter++;
                    totalMedicalEventServiceCounter++;
                    service.Id = serviceCounter.ToString().PadLeft(4, '0');
                    service.DateOfService = dateOfService;
                    service.ServiceCodeTypeCode = (typeCode != Constants.ECLIPSE_OEC_TYPECODE_ECF)?Constants.ECLIPSE_SERVICECODETYPECODE_MBS: Constants.ECLIPSE_SERVICECODETYPECODE_MISC;
                    if (item.Quantity != null && item.Quantity > 1)
                    {
                        service.FieldQuantity = item.Quantity.ToString();
                    }
                    if (totalMedicalEventServiceCounter > 14)
                    {
                        oecMedicalEvent.Service = lstService;
                        lstMedicalEvent.Add(oecMedicalEvent);

                        lstService = new();

                        oecMedicalEvent = new();

                        totalMedicalEventServiceCounter = 1;
                        oecMedicalEvent.Service = null;
                        serviceCounter = 0;
                        medicalEventCounter++;
                        oecMedicalEvent.Id = medicalEventCounter.ToString().PadLeft(2, '0');

                    }
                    string strAmount = (item.Fee != null) ? (item.Fee).ToString() : "0";

                    if (strAmount.IndexOf(".") != -1)
                    {
                        strAmount = strAmount.Replace(".", null);
                    }
                    service.InvoiceEpisodeItemAssocsId = item.Id;
                    if (typeCode != Constants.ECLIPSE_OEC_TYPECODE_ECF)
                    {
                        service.ItemNumber = item.ItemNumber.ToString();

                    }
                    else
                    {
                        service.ServiceCode = item.ItemNumber.ToString();
                        service.ServiceQuantity = item.Quantity;
                        service.ServiceRate = Convert.ToInt32(strAmount);
                    }
                    if (!string.IsNullOrWhiteSpace(item.Notes))
                        service.Text = item.Notes;

                    if (item?.InvoiceEpisodeItemsIndicatorAssocs?.Count > 0 && typeCode != Constants.ECLIPSE_OEC_TYPECODE_ECF)
                    {
                        item.InvoiceEpisodeItemsIndicatorAssocs?.Where(x => x.StatusId == (short)Status.Active).ToList().ForEach(indicator =>
                        {
                            service = SetItemIndicators(service, indicator);
                        });
                    }

                    service.ChargeAmount = strAmount;
                    lstService.Add(service);

                }
            });
            if (lstService is not null && lstService.Count > 0)
            {
                oecMedicalEvent.Service = lstService;
                lstMedicalEvent.Add(oecMedicalEvent);
            }
            lstMedicalEvent?.ForEach(medEvent =>
            {
                if (typeCode != Constants.ECLIPSE_OEC_TYPECODE_ECM)
                {
                    medEvent.AdmissionDate = invoiceDetailView.InvoiceAdditionalDetails.AdmissionDate;
                    medEvent.DischargeDate = invoiceDetailView.InvoiceAdditionalDetails.DischargeDate;

                }
                if (typeCode != Constants.ECLIPSE_OEC_TYPECODE_ECF)
                {
                    if (!string.IsNullOrWhiteSpace(serviceProviderNumber))
                        medEvent.ServiceProvider = new ProviderType()
                        {
                            ProviderNumber = serviceProviderNumber
                        };
                }
            });
            return lstMedicalEvent;
        }
        private OnlineEligibilityCheckServiceType SetItemIndicators(OnlineEligibilityCheckServiceType service, InvoiceEpisodeItemsIndicatorViewAssoc indicator)
        {
            switch (indicator.InvoiceItemIndicatorTypeId)
            {
                case (short)Shared.Models.Enum.InvoiceItemIndicatorType.Restrictive_Override:
                    {
                       if(!string.IsNullOrEmpty(indicator.Value) && (indicator.Value=="SP"|| indicator.Value == "NR" || indicator.Value == "NC"))
                        {
                            service.RestrictionOverrideInd = "Y";
                           
                        }
                        return service;
                    }
                case (short)Shared.Models.Enum.InvoiceItemIndicatorType.Self_Deemed_Code:
                    {
                        service.SelfDeemedCode = indicator.Value;
                        return service;

                    }
                case (short)Shared.Models.Enum.InvoiceItemIndicatorType.Not_Duplicate:
                    {
                        if (indicator.Value == "Y")
                            service.DuplicateServiceOverrideInd = indicator.Value;
                        return service;

                    }
                case (short)Shared.Models.Enum.InvoiceItemIndicatorType.Not_Normal_Aftercare:
                    {
                        if (indicator.Value == "Y")
                            service.AftercareOverrideInd = indicator.Value;
                        return service;

                    }
                case (short)Shared.Models.Enum.InvoiceItemIndicatorType.Not_Multiple:
                    {
                        if (indicator.Value == "Y")
                            service.MultipleProcedureOverrideInd = indicator.Value;
                        return service;

                    }
                default:
                    return service;
            }
        }
        private string FetchServiceType(short? providerTypeId)
        {
            switch (providerTypeId)
            {
                case (short)Shared.Models.Enum.ProviderType.General_Practitioner:
                    {
                        return Constants.CLAIMTYPE_GENERAL;

                    }
                case (short)Shared.Models.Enum.ProviderType.Specialist_and_consultant_physician:
                    {
                        return Constants.CLAIMTYPE_SPECIALIST;

                    }

                default:
                    return Constants.CLAIMTYPE_GENERAL;
            }
        }
        private async  Task<ApiResponse<dynamic>> CallEclispeService(OnlineEligibilityCheckRequestType oecRequestType,InPatientMedicalClaimRequestType imcRequestType,short claimTypeId, BaseHttpRequestContext baseHttpRequestContext, string minorId)
        {
            ApiResponse<dynamic> apiResponse = new();
            string medicareSeviceUrl = medicareSeviceUrl = _appSettings.ApiUrls["MedicareServiceUrl"];
            string endpoint = String.Empty;
            Dictionary<string, string> headers = new();
            //{
            //    { "TransactionId", origTransactionId }
            //};
            if (!string.IsNullOrWhiteSpace(minorId))
            {
                headers.Add("MinorId", minorId);
            }


          
            RestClient restClientMedicare;
            //if (string.IsNullOrWhiteSpace(origTransactionId))
            //    restClientMedicare = new RestClient(medicareSeviceUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            //else
            restClientMedicare = new RestClient(medicareSeviceUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken, headers);
            if (claimTypeId == (short)ClaimType.OEC || claimTypeId == (short)ClaimType.ECF || claimTypeId == (short)ClaimType.ECM)
            {
                endpoint = "/medicare/onlineeligibilitycheck";
                medicareSeviceUrl = _appSettings.ApiUrls["MedicareServiceUrl"] + endpoint;

                apiResponse = await restClientMedicare.PostAsync<ApiResponse<dynamic>>(medicareSeviceUrl, oecRequestType);

            }
            if (claimTypeId == (short)ClaimType.IMC)
            {
                endpoint = "/medicare/submitinpatientmedicalclaim";
                medicareSeviceUrl = _appSettings.ApiUrls["MedicareServiceUrl"] + endpoint;

                apiResponse = await restClientMedicare.PostAsync<ApiResponse<dynamic>>(medicareSeviceUrl, imcRequestType);

            }
            return apiResponse;
        }
    }

       
    }