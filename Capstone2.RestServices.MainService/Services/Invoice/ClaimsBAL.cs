﻿using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.MainService.Common;

using Capstone2.RestServices.Invoice.Interfaces;
using Capstone2.RestServices.Invoice.Models;
using Capstone2.RestServices.Invoice.Utility;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Threading.Tasks;
using System.Linq;
using Capstone2.Shared.Models.Entities;
using Capstone2.Framework.Business.Common;
using Capstone2.Framework.RestApi;
using ProviderType = Capstone2.Shared.Models.Medicare.ProviderType;
using System.Collections.Generic;
using Capstone2.Shared.Models.Medicare;
using Newtonsoft.Json;
using System.Runtime.InteropServices;
using System.Text;
using Capstone2.Framework.RestApi.Utility;
using Microsoft.Extensions.Configuration;
using AutoMapper;
using InvoiceDetailView = Capstone2.RestServices.Invoice.Models.InvoiceDetailView;
using DVAMedicare = Capstone2.Shared.Models.DVA.Medicare;

namespace Capstone2.RestServices.Invoice.Services
{
    public class ClaimsBAL : IClaimsBAL
    {
        public readonly IInvoiceBAL _invoiceBAL;
        public readonly IInvoiceDAL _invoiceDAL;

        public IMapper _mapper;
        public readonly AppSettings _appSettings;
        private readonly ILogger<ClaimsBAL> _logger;
        private IASBMessageSenderHelper _asbMessageSenderHelper;
        private readonly IConfiguration _configuration;
        public ClaimsBAL(IInvoiceBAL invoiceBAL, IInvoiceDAL invoiceDAL, IMapper mapper, IOptions<AppSettings> appSettings, ILogger<ClaimsBAL> logger, IConfiguration configuration, IASBMessageSenderHelper asbMessageSenderHelper)
        {
            _invoiceBAL = invoiceBAL;
            _invoiceDAL = invoiceDAL;
            _mapper = mapper;
            _appSettings = appSettings.Value;
            _logger = logger;
            this._asbMessageSenderHelper = asbMessageSenderHelper;
            this._configuration = configuration;
        }

        public async Task<ApiResponse<dynamic>> SubmitPCI(InvoiceDetailIdObject invoiceDetailIdObject, BaseHttpRequestContext baseHttpRequestContext, string origTransactionId = null)
        {
            long invoice_Details_Id = invoiceDetailIdObject.Id;
            bool isWindows = System.Runtime.InteropServices.RuntimeInformation
                                                  .IsOSPlatform(OSPlatform.Windows);
            ApiResponse<dynamic> apiResponse = new();
            string serviceProviderNumber = string.Empty;
            InvoiceDetailView invoiceDetailView = null;
            string facilityId = string.Empty;
            bool error = false;
            PatientClaimInteractiveRequestType patientClaimInteractiveRequestType = new();
            PatientClaimInteractiveType patientClaimInteractiveType = new();
            ApiResponse<InvoiceDetailView> apiResponseInv = await _invoiceBAL.GetInvoiceDetail(invoice_Details_Id, 0, baseHttpRequestContext);
            if (apiResponseInv is not null && apiResponseInv.StatusCode == StatusCodes.Status200OK && apiResponseInv.Result is not null)
            {
                invoiceDetailView = apiResponseInv.Result;
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = null;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("Invoice not found.");
                return apiResponse;
            }
            string inPatientFacilityId = string.Empty;
            CompanyDetailInfo companyDetailInfo = null;
            if (invoiceDetailView is not null && invoiceDetailView.InvoiceTypeId == (short)InvoiceType.Invoice && invoiceDetailView.InvoiceStatusId != (short)InvoiceStatus.Void)
            {
                List<int> companyIds = new List<int> { (int)invoiceDetailView.CompanyDetailsId };
                if (invoiceDetailView.InvoiceAdditionalDetails is not null && invoiceDetailView.InvoiceAdditionalDetails.InPatientCompanyDetailsId is not null)
                    companyIds.Add((int)invoiceDetailView.InvoiceAdditionalDetails.InPatientCompanyDetailsId);
                List<CompanyDetailInfo> lstCompany = await GetCompanyByIds(companyIds, baseHttpRequestContext);
                string timeZone = string.Empty;
                string minorId = string.Empty;

                if (lstCompany is not null)
                {
                    companyDetailInfo = (lstCompany is not null && lstCompany.Count > 0) ? lstCompany.Where(x => x.Id == invoiceDetailView.CompanyDetailsId).FirstOrDefault() : null;

                    short? timeZoneId = (companyDetailInfo is null) ? null : companyDetailInfo.TimeZoneId;
                    if (timeZoneId is not null)
                    {
                        timeZone = (isWindows) ? EnumExtensions.GetDescription((WindowsTimeZone)(timeZoneId)) : EnumExtensions.GetDescription((LinuxTimeZone)(timeZoneId));
                    }
                    else
                    {
                        OrganisationView orgDetails = await FetchMasterCompanyDetails(baseHttpRequestContext);
                        timeZone = (isWindows) ? orgDetails.WindowsTimeZoneData : orgDetails.LinuxTimeZoneData;
                    }
                    minorId = (companyDetailInfo is null) ? null : companyDetailInfo.MinorId;
                    if (invoiceDetailView.InvoiceAdditionalDetails is not null && invoiceDetailView.InvoiceAdditionalDetails.InPatientCompanyDetailsId is not null)
                    {
                        inPatientFacilityId = lstCompany.Where(x => x.Id == invoiceDetailView.InvoiceAdditionalDetails.InPatientCompanyDetailsId).FirstOrDefault().FacilityId;
                    }
                }

                if (string.IsNullOrWhiteSpace(timeZone))
                {
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Result = null;
                    apiResponse.Message = "TImezone could not be fetched correctly.";
                    return apiResponse;
                }

                DateTime todayDate = (DateTime)DateTimeConversion.ConvertTimeFromUtc(DateTime.UtcNow, timeZone);

                ProviderType serviceProvider = new();
                string serviceTypeCode = string.Empty;

                short? providerTypeId = (invoiceDetailView.ProviderDetails is not null) ? invoiceDetailView.ProviderDetails.ProviderTypeId : null;
                if (providerTypeId is not null && providerTypeId > default(short))
                {
                    serviceTypeCode = FetchServiceType(providerTypeId);

                }

                apiResponse = ValidateInvoiceForClaim(invoiceDetailView, todayDate, serviceTypeCode, baseHttpRequestContext, (short)ClaimType.PCI);
                if (apiResponse is not null && apiResponse.Errors is not null && apiResponse.Errors.Count > 0)
                {
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Result = null;
                    apiResponse.Message = "Failure";
                    return apiResponse;
                }
                patientClaimInteractiveType.AccountReferenceId = "PCI"+(await _invoiceDAL.GetNextVal("[Invoice].[SeqPCIAccountRefId]")).ToString();
                patientClaimInteractiveType.AccountReferenceId = (patientClaimInteractiveType.AccountReferenceId.Length > 9) ? patientClaimInteractiveType.AccountReferenceId.Substring(0, 9) : patientClaimInteractiveType.AccountReferenceId;


                if (invoiceDetailView.Owing <=0)
                    patientClaimInteractiveType.AccountPaidInd = "Y";
                else
                {
                    patientClaimInteractiveType.AccountPaidInd = "N";
                }
                patientClaimInteractiveType.SubmissionAuthorityInd = "Y";
                patientClaimInteractiveType.AuthorisationDate = todayDate;

                
                if (string.IsNullOrWhiteSpace(minorId))
                {
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Result = null;
                    apiResponse.Message = "Failure";
                    apiResponse.Errors.Add("MinorId is mandatory.");
                    error = true;
                }
                if (invoiceDetailView.IsInPatient)
                {
                    facilityId = (string.IsNullOrWhiteSpace(inPatientFacilityId)) ? ((companyDetailInfo!=null)?companyDetailInfo.FacilityId :string.Empty): inPatientFacilityId;
                    
                    if (string.IsNullOrWhiteSpace(facilityId))
                    {
                        error = true;
                        apiResponse.Errors.Add("Faciltity Id is mandatory for In patient claims.");
                    }
                }

                if (!error && invoiceDetailView.ProviderDetails is not null)
                {

                    //short? providerTypeId = (invoiceDetailView.ProviderDetails is not null) ? invoiceDetailView.ProviderDetails.ProviderTypeId : null;
                    //if (providerTypeId is not null && providerTypeId > default(short))
                    //{
                    //    serviceTypeCode = FetchServiceType(providerTypeId);

                    //}
                    if (!(string.IsNullOrWhiteSpace(invoiceDetailView.ReferralOverrideCode)))
                        patientClaimInteractiveType.ReferralOverrideCode = invoiceDetailView.ReferralOverrideCode;

                    serviceProviderNumber = invoiceDetailView.ProviderDetails.LstUserCompanyInfo.Where(x => x.UserCompanyId == invoiceDetailView.CompanyDetailsId).Select(x => x.ProviderNumber).FirstOrDefault();
                    if (!string.IsNullOrWhiteSpace(serviceProviderNumber))
                    {
                        serviceProvider.ProviderNumber = serviceProviderNumber;
                        patientClaimInteractiveType.ServiceProvider = serviceProvider;
                    }
                    else
                    {
                        error = true;
                        apiResponse.Errors.Add("Service Provider number is mandatory.");
                    }

                    if (invoiceDetailView.PayeeProviderId is not null && invoiceDetailView.PayeeProviderId > default(long) && invoiceDetailView.PayeeProviderDetails is not null)
                    {
                        string payeeProviderNumber = invoiceDetailView.PayeeProviderDetails.LstUserCompanyInfo.Where(x => x.UserCompanyId == invoiceDetailView.PayeeCompanyDetailsId).Select(x => x.ProviderNumber).FirstOrDefault();
                        if (!string.IsNullOrWhiteSpace(payeeProviderNumber))
                        {
                            if (!payeeProviderNumber.Equals(serviceProviderNumber))
                            {
                                ProviderType payeeProvider = new();
                                payeeProvider.ProviderNumber = payeeProviderNumber;
                                patientClaimInteractiveType.PayeeProvider = payeeProvider;
                            }

                        }
                        else
                        {
                            error = true;
                            apiResponse.Errors.Add("Payee Provider number is mandatory if a payee provider is selected.");
                        }
                    }
                }

                if (!error && invoiceDetailView.AppointmentDetails is not null)
                {

                    patientClaimInteractiveType = SetMedicalEvent(patientClaimInteractiveType, invoiceDetailView, serviceTypeCode, timeZone);

                    if (patientClaimInteractiveType.MedicalEvent is null || patientClaimInteractiveType.MedicalEvent.Count == 0)
                    {
                        error = true;
                        apiResponse.Errors.Add("Medical Event is mandatory.");
                    }
                    if (invoiceDetailView.IsInPatient)
                    {

                        patientClaimInteractiveType.MedicalEvent.ToList().ForEach(m =>
                        {
                            m.Service.ToList().ForEach(service =>
                            {
                                service.FacilityId = facilityId;
                                service.HospitalInd = "Y";
                            });
                        });
                    }

                    if (!error)
                    {
                        long patientId = (long)invoiceDetailView.AppointmentDetails.PatientDetailsId;
                        ClaimantPatientDetailInfo claimantPatientDetailInfo = null;

                        claimantPatientDetailInfo = await FetchClaimantPatientInfo(patientId, baseHttpRequestContext, serviceTypeCode);
                        apiResponse = ValidatePatientDetails(invoiceDetailView, claimantPatientDetailInfo, todayDate, serviceTypeCode, invoiceDetailView.AppointmentDetails.DateOfAppointment, (short)ClaimType.PCI);
                        if (apiResponse is not null && apiResponse.Errors is not null && apiResponse.Errors.Count > 0)
                        {
                            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                            apiResponse.Result = null;
                            apiResponse.Message = "Failure";
                            return apiResponse;
                        }
                        bool patientMinor = CheckPatientMinor((DateTime)claimantPatientDetailInfo.DateofBirth, todayDate);
                        //AccountHolderParentGuardianAssocInfo accountHolderGuardian = null;
                        //if (patientMinor)
                        //{
                        //    accountHolderGuardian = claimantPatientDetailInfo.AccountHolderParentGuardianAssocInfo;
                        //}

                        AccountHolderAssocInfo accountHolderMedicare = (claimantPatientDetailInfo.AccountHolderAssocInfos is null) ? null : claimantPatientDetailInfo.AccountHolderAssocInfos.Where(x => x.AccountHolderType == (short)AccountHolderTypes.Medicare).FirstOrDefault();

                        patientClaimInteractiveType = SetPatientClaimantInfo(patientClaimInteractiveType, claimantPatientDetailInfo, accountHolderMedicare, patientMinor, invoiceDetailIdObject);



                        if (serviceTypeCode == Constants.CLAIMTYPE_SPECIALIST && string.IsNullOrEmpty(patientClaimInteractiveType.ReferralOverrideCode))
                        {
                            Shared.Models.Medicare.ReferralType referralType = SetReferralDetails(invoiceDetailView.AppointmentDetails.ReferralDetail, serviceTypeCode);
                            if (referralType is not null)
                            {
                                patientClaimInteractiveType.Referral = referralType;
                                patientClaimInteractiveType.Referral.TypeCode = serviceTypeCode;

                            }
                            else
                            {
                                apiResponse.Errors.Add("Referral Details including Issue Date,Expiry Date(for Non Standard referrals),Referral Type and Referral Provider's Provider No. is mandatory.");
                                error = true;
                            }
                            //if (patientClaimInteractiveType.Referral is null)
                            //{
                            //    apiResponse.Errors.Add("Referral Details including Issue Date,Expiry Date(for Non Standard referrals),Referral Type and Referral Provider's Provider No. is mandatory.");
                            //    error = true;
                            //}
                            //else
                            //{
                            // }
                        }
                    }
                    if (error)
                    {
                        return apiResponse;
                    }
                    patientClaimInteractiveRequestType.PatientClaimInteractive = patientClaimInteractiveType;
                    ApiResponse<dynamic> apiResp = await CallMedicareServicePCI(patientClaimInteractiveRequestType, serviceTypeCode, baseHttpRequestContext, origTransactionId, minorId);
                    if (apiResp is not null)
                    {

                        List<InvoiceMedicareAssoc> lstMedicareAssocs = await _invoiceDAL.GetInvoiceMedicareAssocsForClaimType(invoice_Details_Id, (short)ClaimType.PCI, baseHttpRequestContext.OrgId);
                        lstMedicareAssocs?.ForEach(x =>
                        {
                            x.StatusId = (short)Status.Deleted;
                            x.ModifiedBy = baseHttpRequestContext.UserId;
                            x.ModifiedDate = DateTime.UtcNow;
                        });
                        string medicareStatus = string.Empty;
                        string transactionId = apiResp.TransactionId;

                        string errorText = string.Empty;
                        object respObj = ConvertMedicareReponse(apiResp.Result, (short)ClaimType.PCI);
                        if (respObj != null && "servicemessagestype" == respObj.GetType().Name.ToLower())
                        {
                            ServiceMessagesType serviceMessageType = (ServiceMessagesType)respObj;
                            if (serviceMessageType is not null && serviceMessageType.ServiceMessage is not null)
                            {
                                serviceMessageType.ServiceMessage?.ToList().ForEach(msg =>
                                {
                                    errorText = errorText + msg.Code + ":" + msg.Reason;
                                });
                            }


                        }
                        else if (respObj != null && "patientclaiminteractiveresponsetype" == respObj.GetType().Name.ToLower())
                        {
                            PatientClaimInteractiveResponseType patientClaimInteractiveResponse = (PatientClaimInteractiveResponseType)respObj;
                            if (patientClaimInteractiveResponse is not null)
                            {
                                medicareStatus = (patientClaimInteractiveResponse.Status is null || string.IsNullOrWhiteSpace(patientClaimInteractiveResponse.Status)) ? "ERROR" : patientClaimInteractiveResponse.Status;
                                errorText = (medicareStatus == "MEDICARE_ASSESSED" || medicareStatus == "MEDICARE_PENDED") ? string.Empty : SetErrorText(patientClaimInteractiveResponse);
                            }
                        }

                        else if (respObj != null && "medicarejsonerrorresponse" == respObj.GetType().Name.ToLower())
                        {
                            MedicareJsonErrorResponse errorResponse = (MedicareJsonErrorResponse)respObj;
                            if (errorResponse is not null && !string.IsNullOrWhiteSpace(errorResponse.Message))
                            {
                                errorText = errorText + errorResponse.Message;
                            }
                        }
                        else if (apiResp is not null && apiResp.StatusCode == StatusCodes.Status400BadRequest)
                        {
                            return apiResp;
                        }
                        //try
                        //{
                        //   PatientClaimInteractiveResponseType patientClaimInteractiveResponse = JsonConvert.DeserializeObject<PatientClaimInteractiveResponseType>(apiResp.Result);
                        //   transactionId = apiResp.TransactionId;
                        //   medicareStatus = (patientClaimInteractiveResponse.Status is null || string.IsNullOrWhiteSpace(patientClaimInteractiveResponse.Status)) ? "ERROR" : patientClaimInteractiveResponse.Status;
                        //   errorText = (medicareStatus == "MEDICARE_ASSESSED" || medicareStatus == "MEDICARE_PENDED") ? string.Empty : SetErrorText(patientClaimInteractiveResponse);

                        //}
                        //catch (Exception e)
                        //{

                        //}
                        medicareStatus = (string.IsNullOrWhiteSpace(medicareStatus)) ? "ERROR" : medicareStatus;
                        InvoiceMedicareAssoc medicareAssoc = await SetInvoiceMedicareAssoc(invoice_Details_Id, (long)invoiceDetailView.AppointmentDetails.PatientDetailsId, (short)ClaimType.PCI, transactionId, medicareStatus, errorText, baseHttpRequestContext, minorId, patientClaimInteractiveType);
                        if (medicareAssoc.Id > 0)
                        {
                            if(invoiceDetailIdObject.EftDetails is not null && string.IsNullOrWhiteSpace(origTransactionId))
                            {
                                InvoicePatientEftAssoc invoicePatientEftDB = await _invoiceDAL.FetchInvoicePatientEftAssoc(invoiceDetailIdObject.Id, baseHttpRequestContext.OrgId);

                                if (invoicePatientEftDB is not null)
                                {
                                    invoicePatientEftDB.StatusId = (short)Status.Deleted;
                                    invoicePatientEftDB.ModifiedDate = DateTime.UtcNow;
                                    invoicePatientEftDB.ModifiedBy = baseHttpRequestContext.UserId;
                                    await _invoiceDAL.UpdateInvoicePatientEftAssoc(invoicePatientEftDB);
                                }
                                invoiceDetailIdObject.EftDetails.Id = default(long);
                                invoiceDetailIdObject.EftDetails.OrgId = baseHttpRequestContext.OrgId;
                                invoiceDetailIdObject.EftDetails.ModifiedDate = DateTime.UtcNow;
                                invoiceDetailIdObject.EftDetails.CreatedDate = DateTime.UtcNow;
                                invoiceDetailIdObject.EftDetails.ModifiedBy = baseHttpRequestContext.UserId;
                                invoiceDetailIdObject.EftDetails.StatusId = (short)Status.Active;
                                invoiceDetailIdObject.EftDetails.InvoiceDetailsId = invoice_Details_Id;
                                await _invoiceDAL.AddInvoicePatientEftAssoc(invoiceDetailIdObject.EftDetails);
                            }
                            if (!string.IsNullOrWhiteSpace(medicareAssoc.ErrorText))
                            {
                                apiResp.Errors.Add(medicareAssoc.ErrorText);
                            }
                            int rows = await _invoiceDAL.UpdateInvoiceMedicareAssocRange(lstMedicareAssocs);
                            apiResp.Result = medicareAssoc;
                            if (!string.IsNullOrWhiteSpace(medicareAssoc.ErrorText))
                                await StoreMedicareExceptionMessage(baseHttpRequestContext.OrgCode, medicareAssoc.Id);//TODO:Send message to Service Bus for MedicareException report
                        }
                    }
                    return apiResp;

                }

            }
            else
            {
                apiResponse.Errors.Add("Claims can be raised only for Invoices (non voided).");

            }

            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            return apiResponse;
        }

        public Shared.Models.Medicare.ReferralType SetReferralDetails(ReferralDetailInfo referralDetail, string serviceTypeCode)
        {
            Shared.Models.Medicare.ReferralType referral = null;

            if (referralDetail is not null)
            {
                ProviderType provider = new()
                {
                    ProviderNumber = referralDetail.ReferringProviderInfo.ProviderNumber
                };
                referral = new();
                referral.Provider = provider;
                referral.IssueDate = (DateTime)referralDetail.IssueDate;
                referral.TypeCode = serviceTypeCode; // will always be S as D and P out of scope for now
                if (referralDetail.ReferralTypeId == (short)Shared.Models.Enum.ReferralType.Standard)
                {
                    referral.PeriodCode = "S";
                }
                else if (referralDetail.ReferralTypeId == (short)Shared.Models.Enum.ReferralType.Non_Standard)
                {
                    referral.PeriodCode = "N";
                    if (referralDetail.ExpiryDate is not null)
                        referral.Period = MonthDifference((DateTime)referralDetail.ExpiryDate, (DateTime)referralDetail.IssueDate).ToString();
                    else
                    {
                        return null;
                    }
                }
                else
                    referral.PeriodCode = "I";

            }
            return referral;
        }

        private string SetErrorText(PatientClaimInteractiveResponseType patientClaimInteractiveResponse)
        {
            string assessmentCode = string.Empty;
            string errortext = string.Empty;
            if (patientClaimInteractiveResponse.ClaimAssessment.Error is not null && !string.IsNullOrWhiteSpace(patientClaimInteractiveResponse.ClaimAssessment.Error.Text))
                return patientClaimInteractiveResponse.ClaimAssessment.Error.Text;
            patientClaimInteractiveResponse.ClaimAssessment.MedicalEvent?.ToList().ForEach(medEvent =>
            {
                medEvent.Service.ToList().ForEach(service =>
                {
                    if (service is not null && service.Error is not null && !string.IsNullOrWhiteSpace(service.Error.Text))
                    {
                        if (patientClaimInteractiveResponse.Status == "MEDICARE_PENDABLE" || (service.AssessmentCode == "UNACCEPTABLE_ERROR" && (patientClaimInteractiveResponse.Status == "MEDICARE_REJECTED")))
                            errortext = errortext + "ItemNum " + service.ItemNumber + ":" + " Error code:" + service.Error.Code + "-" + service.Error.Text;
                    }
                });
            });
            return errortext;
        }

        public ApiResponse<dynamic> ValidatePatientDetails(InvoiceDetailView invoiceDetail, ClaimantPatientDetailInfo claimantPatientDetailInfo, DateTime todayDate, string serviceTypeCode, DateTime? dateOfAppointment, short claimTypeId)
        {
            ApiResponse<dynamic> apiResponse = new();
            if (claimantPatientDetailInfo is not null)
            {
                if (claimTypeId != (short)ClaimType.OEC)
                {
                    bool patientMinor = CheckPatientMinor((DateTime)claimantPatientDetailInfo.DateofBirth, todayDate);
                    AccountHolderParentGuardianAssocInfo accountHolderGuardian = null;
                    if (patientMinor)
                    {
                        accountHolderGuardian = claimantPatientDetailInfo.AccountHolderParentGuardianAssocInfo;
                        if (accountHolderGuardian is null || string.IsNullOrWhiteSpace(accountHolderGuardian.MedicareCardIrn) || string.IsNullOrWhiteSpace(accountHolderGuardian.MedicareCardNumber) || accountHolderGuardian.DateOfBirth is null)
                        {
                            apiResponse.Errors.Add("Parent/Guardian Details (First Name,Last Name,Date of Birth,Medicare Details )is mandatory if patient is minor.");
                            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                        }
                    }
                }


                short accountHolderTypes = (short)AccountHolderTypes.Medicare;
                if (claimTypeId == (short)ClaimType.DVA)
                    accountHolderTypes = (short)AccountHolderTypes.DVA;

                AccountHolderAssocInfo accountHolderMedicare = (claimantPatientDetailInfo.AccountHolderAssocInfos is null) ? null : claimantPatientDetailInfo.AccountHolderAssocInfos.Where(x => x.AccountHolderType == accountHolderTypes).FirstOrDefault();
                if (claimTypeId != (short)ClaimType.OEC && claimTypeId!=(short)ClaimType.ECF && (accountHolderMedicare is null || string.IsNullOrWhiteSpace(accountHolderMedicare.AccountNumber) || string.IsNullOrWhiteSpace(accountHolderMedicare.AccountNumber)))
                {
                    apiResponse.Errors.Add("Patient Medicare/DVA Details is mandatory.");
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                }
                else if (claimTypeId == (short)ClaimType.DVA)
                {
                    if(accountHolderMedicare.ColourId is not null && accountHolderMedicare.ColourId == (short)AccountHolderDvaColor.White)
                    {
                        if (accountHolderMedicare.AcceptedDisabilityInd == true && string.IsNullOrWhiteSpace(accountHolderMedicare.AcceptedDisabilityCode)) 
                        {
                            apiResponse.Errors.Add("Accepted Disability Code is mandatory if Accepted Disability Ind is set.");
                            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                        }
                    }
                }
                if (claimTypeId == (short)ClaimType.ECF)
                    if (claimantPatientDetailInfo is null || claimantPatientDetailInfo.AccountHolderHealthfundAssocs is null || claimantPatientDetailInfo.AccountHolderHealthfundAssocs.Count == 0)
                    {
                        apiResponse.Errors.Add("Health Fund Details are mandatory for claim type ECF.");
                    }
                if (claimTypeId == (short)ClaimType.ECM)
                    if (accountHolderMedicare is null || string.IsNullOrWhiteSpace(accountHolderMedicare.AccountNumber) || string.IsNullOrWhiteSpace(accountHolderMedicare.AccountNumber))
                    {
                        apiResponse.Errors.Add("Patient Medicare Details are mandatory for claim type ECM.");
                    }
                if (claimTypeId != (short)ClaimType.OEC && serviceTypeCode == Constants.CLAIMTYPE_SPECIALIST && invoiceDetail.AppointmentDetails.ReferralDetail is not null && invoiceDetail.AppointmentDetails.ReferralDetail.IssueDate is not null)
                {
                    DateTime issueDate = (DateTime)invoiceDetail.AppointmentDetails.ReferralDetail.IssueDate;
                    if (issueDate.CompareTo((DateTime)claimantPatientDetailInfo.DateofBirth) < 0)
                    {
                        apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                        apiResponse.Errors.Add("Referral Issue Date Must not be before Patient Date of Birth.");
                    }
                }
                //if (serviceTypeCode == Constants.CLAIMTYPE_SPECIALIST && string.IsNullOrWhiteSpace(invoiceDetail.ReferralOverrideCode))
                //{
                //    if (claimantPatientDetailInfo.ReferralDetail is null || claimantPatientDetailInfo.ReferralDetail.IssueDate is null || claimantPatientDetailInfo.ReferralDetail.ReferralTypeId is null || (string.IsNullOrWhiteSpace(claimantPatientDetailInfo.ReferralDetail.ReferringProviderInfo.ProviderNumber)))
                //    {
                //        apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                //        apiResponse.Errors.Add("Referral Details including Issue Date,Expiry Date(for Non Standard referrals),Referral Type and Referral Provider's Provider No. is mandatory.");
                //    }
                //    else if(claimantPatientDetailInfo.ReferralDetail.IssueDate is not null && claimantPatientDetailInfo.DateofBirth is not null)
                //    {
                //        DateTime issueDate = (DateTime)claimantPatientDetailInfo.ReferralDetail.IssueDate;
                //        if(issueDate.CompareTo((DateTime)claimantPatientDetailInfo.DateofBirth) < 0)
                //        {
                //            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                //            apiResponse.Errors.Add("Referral Issue Date Must not be before Patient Date of Birth.");
                //        }
                //    }
                //    if (claimantPatientDetailInfo.ReferralDetail is not null && claimantPatientDetailInfo.ReferralDetail.IssueDate is not null && dateOfAppointment is not null)
                //    {
                //        DateTime issueDate = (DateTime)claimantPatientDetailInfo.ReferralDetail.IssueDate;
                //        if (issueDate.CompareTo((DateTime)dateOfAppointment) > 0)
                //        {
                //            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                //            apiResponse.Errors.Add("Medical Event Date must not be before Referral Issue Date.");
                //        }
                //    }

                //}
                if (dateOfAppointment is not null && claimantPatientDetailInfo.DateofBirth is not null)
                {
                    if (((DateTime)dateOfAppointment).CompareTo((DateTime)claimantPatientDetailInfo.DateofBirth) < 0)
                    {
                        apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                        apiResponse.Errors.Add("Medical Event Date must not be before Patient Date of Birth.");
                    }
                }

                if((claimTypeId == (short)ClaimType.PCI || claimTypeId == (short)ClaimType.Bulk_Bill || claimTypeId == (short)ClaimType.DVA) && claimantPatientDetailInfo.PatientAddress is not null && (string.IsNullOrWhiteSpace(claimantPatientDetailInfo.PatientAddress.AddressLine1) || string.IsNullOrWhiteSpace(claimantPatientDetailInfo.PatientAddress.Suburb) || string.IsNullOrWhiteSpace(claimantPatientDetailInfo.PatientAddress.PostCode)))
                {
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Errors.Add("Claimant Address details supplied are incomplete. When a claimant residential address is supplied, Claimant Address Line 1, Locality and Postcode are required.");
                }

            }
            else
            {
                apiResponse.Errors.Add("Patient Details cannot be fetched.");
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;

            }
            return apiResponse;
        }


        public ApiResponse<dynamic> ValidateInvoiceForClaim(InvoiceDetailView invoiceDetailView, DateTime todayDate, string serviceTypeCode, BaseHttpRequestContext baseHttpRequestContext, short claimType)
        {
            ApiResponse<dynamic> apiResponse = new();
            string referringProviderNumber = string.Empty;
            if (claimType == (short)ClaimType.PCI)
            {
                if (!(invoiceDetailView.AccountHolderTypeId == (short)AccountHolderTypes.Patient && invoiceDetailView.BillingScheduleTypeId == (short)BillingSchedules.Private) && !(invoiceDetailView.AccountHolderTypeId == (short)AccountHolderTypes.ParentOrGuardian && invoiceDetailView.BillingScheduleTypeId == (short)BillingSchedules.Private))
                {
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Errors.Add("The AccountHolder/Billing Schedule is not consistent with the claim being raised.");
                }
            }
            else if (claimType == (short)ClaimType.Bulk_Bill)
            {
                if (!(invoiceDetailView.AccountHolderTypeId == (short)AccountHolderTypes.Medicare && invoiceDetailView.BillingScheduleTypeId == (short)BillingSchedules.Bulk_Bill))
                {
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Errors.Add("The AccountHolder/Billing Schedule is not consistent with the claim being raised.");
                }
            }
            else if (claimType == (short)ClaimType.IMC)
            {
                if (!(invoiceDetailView.AccountHolderTypeId == (short)AccountHolderTypes.Health_Fund && (invoiceDetailView.BillingScheduleTypeId == (short)BillingSchedules.No_Gap || invoiceDetailView.BillingScheduleTypeId == (short)BillingSchedules.Known_Gap)) && !(invoiceDetailView.AccountHolderTypeId == (short)AccountHolderTypes.Patient && invoiceDetailView.BillingScheduleTypeId == (short)BillingSchedules.Private))
                {
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Errors.Add("The AccountHolder/Billing Schedule is not consistent with the claim being raised.");
                }
            }
            //else if (claimType == (short)ClaimType.OEC)
            //{
            //    if (!(invoiceDetailView.AccountHolderTypeId == (short)AccountHolderTypes.Health_Fund && (invoiceDetailView.BillingScheduleTypeId == (short)BillingSchedules.No_Gap || invoiceDetailView.BillingScheduleTypeId == (short)BillingSchedules.Known_Gap)))
            //    {
            //        apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            //        apiResponse.Errors.Add("The AccountHolder/Billing Schedule is not consistent with the Online Eligibility Check.");
            //    }
            //}

            else if (claimType == (short)ClaimType.DVA)
            {
                if (!(invoiceDetailView.AccountHolderTypeId == (short)AccountHolderTypes.DVA && invoiceDetailView.BillingScheduleTypeId == (short)BillingSchedules.DVA))
                {
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Errors.Add("The AccountHolder/Billing Schedule is not consistent with the claim being raised.");
                }
            }
            if (invoiceDetailView.ProviderDetails is null || invoiceDetailView.ProviderDetails.ProviderTypeId is null)
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;

                apiResponse.Errors.Add("Provider Details is mandatory.");
            }
            if (claimType != (short)ClaimType.OEC && claimType != (short)ClaimType.ECF &&  claimType != (short)ClaimType.ECM)
            {
                if (invoiceDetailView.AppointmentDetails is null || invoiceDetailView.AppointmentDetails.DateOfAppointment is null)
                {
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Errors.Add("Appointment Details including Date Of Appointment is mandatory.");
                }
                else
                {
                    DateTime appointmentDate = (DateTime)invoiceDetailView.AppointmentDetails.DateOfAppointment;
                    appointmentDate = appointmentDate.Add((TimeSpan)invoiceDetailView.AppointmentDetails.StartTime);

                    if (todayDate.CompareTo(appointmentDate) < 0)
                    {
                        apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                        apiResponse.Errors.Add("Medical Event Date Must not be a date in the future.");
                    }
                    else if (todayDate.CompareTo(invoiceDetailView.AppointmentDetails.DateOfAppointment) > 0)
                    {
                        bool isValid = ValidateMedicalEventDate((DateTime)invoiceDetailView.AppointmentDetails.DateOfAppointment, todayDate);
                        if (!isValid)
                        {
                            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                            apiResponse.Errors.Add("Medical Event Date must be less than two years before the date the request is received.");
                        }
                    }


                    if (serviceTypeCode == Constants.CLAIMTYPE_SPECIALIST && string.IsNullOrWhiteSpace(invoiceDetailView.ReferralOverrideCode))
                    {
                        if (invoiceDetailView.AppointmentDetails.ReferralDetail is null || invoiceDetailView.AppointmentDetails.ReferralDetail.IssueDate is null || invoiceDetailView.AppointmentDetails.ReferralDetail.ReferralTypeId is null || (string.IsNullOrWhiteSpace(invoiceDetailView.AppointmentDetails.ReferralDetail.ReferringProviderInfo.ProviderNumber)))
                        {
                            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                            apiResponse.Errors.Add("Referral Details including Issue Date,Expiry Date(for Non Standard referrals),Referral Type and Referral Provider's Provider No. is mandatory.");
                        }
                        else
                        {
                            referringProviderNumber = invoiceDetailView.AppointmentDetails.ReferralDetail.ReferringProviderInfo.ProviderNumber;

                        }

                        if (invoiceDetailView.AppointmentDetails.ReferralDetail is not null && invoiceDetailView.AppointmentDetails.ReferralDetail.IssueDate is not null && invoiceDetailView.AppointmentDetails.DateOfAppointment is not null)
                        {
                            DateTime issueDate = (DateTime)invoiceDetailView.AppointmentDetails.ReferralDetail.IssueDate;
                            if (issueDate.CompareTo((DateTime)invoiceDetailView.AppointmentDetails.DateOfAppointment) > 0)
                            {
                                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                                apiResponse.Errors.Add("Medical Event Date must not be before Referral Issue Date.");
                            }
                        }

                    }
                }
            }
            else
            {
                DateTime? dateOfService = null;
                if (invoiceDetailView.AppointmentDetails is not null && invoiceDetailView.AppointmentDetails.DateOfAppointment is not null)
                {
                    dateOfService = invoiceDetailView.AppointmentDetails.DateOfAppointment;
                }
                else if (invoiceDetailView.DateOfService is not null)
                {
                    dateOfService = invoiceDetailView.DateOfService;
                }
                else
                {
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Errors.Add("Date Of Service is mandatory.");
                }
                if (dateOfService is not null)
                {
                    if (todayDate.CompareTo(dateOfService) > 0)
                    {
                        TimeSpan diff = (TimeSpan)(todayDate - dateOfService);
                        if (diff.Days > 7)
                            apiResponse.Errors.Add("Date of Service must not be more than 7 days prior to the date request is received.");

                    }
                    if (invoiceDetailView.InvoiceAdditionalDetails is not null && invoiceDetailView.InvoiceAdditionalDetails.AccidentDate is not null && dateOfService < invoiceDetailView.InvoiceAdditionalDetails.AccidentDate)
                    {
                        apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                        apiResponse.Errors.Add("The Date Of Service supplied must not before the Accident Date.");
                    }
                }
            }

            string serviceProviderNumber = string.Empty;
            if (invoiceDetailView.ProviderDetails is not null)
            {
                serviceProviderNumber = invoiceDetailView.ProviderDetails.LstUserCompanyInfo.Where(x => x.UserCompanyId == invoiceDetailView.CompanyDetailsId).Select(x => x.ProviderNumber).FirstOrDefault();
            }

            if (string.IsNullOrWhiteSpace(serviceProviderNumber))
            {
                apiResponse.Errors.Add("Service Provider number is mandatory.");
            }
            else if (!string.IsNullOrWhiteSpace(referringProviderNumber) && serviceProviderNumber.Equals(referringProviderNumber))
            {
                apiResponse.Errors.Add("Referring Provider Number must not be the same as the Servicing Provider Number.");
            }

            if(invoiceDetailView.PayeeProviderId is not null && invoiceDetailView.PayeeCompanyDetailsId is not null)
            {
                string payeeProviderNUmber = invoiceDetailView.PayeeProviderDetails?.LstUserCompanyInfo?.Where(x => x.UserCompanyId == invoiceDetailView.PayeeCompanyDetailsId).Select(x => x.ProviderNumber).FirstOrDefault();
                if (string.IsNullOrWhiteSpace(payeeProviderNUmber))
                {
                    apiResponse.Errors.Add("Payee Provider number is mandatory if Payee Provider details are entered.");
                }
            }
            if (invoiceDetailView.InvoiceEpisodeItemAssocs is null || invoiceDetailView.InvoiceEpisodeItemAssocs.Count == 0 || invoiceDetailView.InvoiceEpisodeItemAssocs.Where(x => x.EpisodeTypeId == (short)EpisodeTypes.MBS && x.Fee < 1 && x.StatusId == (short)Status.Active && (x.ClaimStatusId == null || x.ClaimStatusId != (short)ClaimStatus.Complete)).Count() > 0)
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Errors.Add("At least one item in the Medical Event must be an MBS item with a charge of 100(i.e. $1) or more.");
                apiResponse.Errors.Add("Minimum charge amount is 100 (i.e. $1.00).");

            }
            else
            {
                List<InvoiceEpisodeItemViewAssoc> lstItems = invoiceDetailView.InvoiceEpisodeItemAssocs.Where(x => x.EpisodeTypeId == (short)EpisodeTypes.MBS && x.StatusId == (short)Status.Active).ToList();
                lstItems.ForEach(item =>
                {

                    if(item.EpisodeTypeId==(short)EpisodeTypes.MBS && item.StatusId==(short)Status.Active && item.Quantity!=null && item.Quantity >99 && claimType!=(short)ClaimType.IMC && claimType != (short)ClaimType.OEC && claimType != (short)ClaimType.ECF)
                    {
                        apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                        apiResponse.Errors.Add($"Invalid value of {item.Quantity} supplied for Field Quantity/ServiceQuantity.The value supplied must be numeric (1-99).");
                    }
                    List<InvoiceEpisodeItemsIndicatorViewAssoc> lstIndicator = item.InvoiceEpisodeItemsIndicatorAssocs?.ToList();
                    if (lstIndicator is not null && lstIndicator.Count > 0)
                    {
                        if (lstIndicator.Where(x => x.InvoiceItemIndicatorTypeId == (short)InvoiceItemIndicatorType.Not_Duplicate && x.Value == "Y" && x.StatusId == (short)Status.Active).Any())
                        {
                            if (string.IsNullOrWhiteSpace(item.Notes) && invoiceDetailView.AppointmentDetails.StartTime is null)
                            {
                                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                                apiResponse.Errors.Add("Additional information required. If Duplicate Service Override Indicator is set to Y (Not Duplicate) then Medical Event Time or additional information in service text is required to support the reason for the override.");
                            }
                        }
                        if (lstIndicator.Where(x => x.InvoiceItemIndicatorTypeId == (short)InvoiceItemIndicatorType.Not_Multiple && x.Value == "Y" && x.StatusId == (short)Status.Active).Any())
                        {
                            if (string.IsNullOrWhiteSpace(item.Notes))
                            {
                                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                                apiResponse.Errors.Add("If Multiple Procedure Override Indicator is set to Y (Not Multiple) then service text providing the reason for the override must be supplied.");
                            }
                        }
                        if (lstIndicator.Where(x => x.InvoiceItemIndicatorTypeId == (short)InvoiceItemIndicatorType.Self_Deemed_Code).Any() && serviceTypeCode == Constants.CLAIMTYPE_GENERAL)
                        {
                            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                            apiResponse.Errors.Add("The details in this claim are inconsistent with the service called. A General Service does not require a Referral Type Code, Referral Override Code or a Self Deemed Code.");

                        }
                    }

                });
            }
            return apiResponse;
        }

        private bool ValidateMedicalEventDate(DateTime dateOfAppointment, DateTime todayDate)
        {
            if ((todayDate.Year - dateOfAppointment.Year) < 2) return true;

            if (todayDate.Year - dateOfAppointment.Year == 2)
            {
                if (todayDate.Month > dateOfAppointment.Month) return false;
                if (todayDate.Month < dateOfAppointment.Month) return true;
                if (todayDate.Month == dateOfAppointment.Month)
                {
                    if (todayDate.Day < dateOfAppointment.Day) return true;
                    return false;
                }
            }
            if (todayDate.Year - dateOfAppointment.Year > 2) return false;

            return true;
        }

        private PatientClaimInteractiveType SetReferralDetails(PatientClaimInteractiveType patientClaimInteractiveType, ClaimantPatientDetailInfo claimantPatientDetailInfo)
        {
            Shared.Models.Medicare.ReferralType referral = new();
            ProviderType provider = new()
            {
                ProviderNumber = claimantPatientDetailInfo.ReferralDetail.ReferringProviderInfo.ProviderNumber
            };
            referral.Provider = provider;
            referral.IssueDate = (DateTime)claimantPatientDetailInfo.ReferralDetail.IssueDate;

            if (claimantPatientDetailInfo.ReferralDetail.ReferralTypeId == (short)Shared.Models.Enum.ReferralType.Standard)
            {
                referral.PeriodCode = "S";
            }
            else if (claimantPatientDetailInfo.ReferralDetail.ReferralTypeId == (short)Shared.Models.Enum.ReferralType.Non_Standard)
            {
                referral.PeriodCode = "N";
                if (claimantPatientDetailInfo.ReferralDetail.ExpiryDate is not null)
                    referral.Period = MonthDifference((DateTime)claimantPatientDetailInfo.ReferralDetail.ExpiryDate, (DateTime)claimantPatientDetailInfo.ReferralDetail.IssueDate).ToString();
                else
                {
                    return patientClaimInteractiveType;
                }
            }
            else
                referral.PeriodCode = "I";
            if (referral is not null && referral.PeriodCode is not null && referral.Provider is not null)
            {
                patientClaimInteractiveType.Referral = referral;
            }
            return patientClaimInteractiveType;
        }
        public static int MonthDifference(DateTime lValue, DateTime rValue)
        {
            var yearDifferenceInMonths = (lValue.Year - rValue.Year) * 12;
            var monthDifference = lValue.Month - rValue.Month;

            return yearDifferenceInMonths + monthDifference +
                (lValue.Day > rValue.Day
                    ? 1 : 0); // If end day is greater than start day, add 1 to round up the partial month
        }
        public bool CheckPatientMinor(DateTime dateofBirth, DateTime todayDate)
        {
            if ((todayDate.Year - dateofBirth.Year) > 14) return false;

            if (todayDate.Year - dateofBirth.Year == 14)
            {
                if (todayDate.Month > dateofBirth.Month) return false;
                if (todayDate.Month < dateofBirth.Month) return true;
                if (todayDate.Month == dateofBirth.Month)
                {
                    if (todayDate.Day >= dateofBirth.Day) return false;
                    return true;
                }
            }
            if ((todayDate.Year - dateofBirth.Year) < 14) return true;

            return true;
        }

        public async Task<List<CompanyDetailInfo>> GetCompanyByIds(List<int> lstCompanyIds, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<List<CompanyDetailInfo>> apiResponseCompany = new();
            int ps = lstCompanyIds.Count();
            string filter = "{CompanyDetailId:[" + string.Join(",", lstCompanyIds) + "]}";
            string endpoint = "/company/company_details_info?pn = 1 & ps =" + ps;
            string encodedFilter = Base64EncoderHelper.EncodeQueryModelFilterParam(filter);
            string companySeviceUrl = _appSettings.ApiUrls["CompanyServiceUrl"] + endpoint + "&f=" + encodedFilter;
            RestClient restClientCompany = new RestClient(companySeviceUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            apiResponseCompany = await restClientCompany.GetAsync<ApiResponse<List<CompanyDetailInfo>>>(companySeviceUrl);

            return (apiResponseCompany == null || apiResponseCompany.Result == null) ? null : apiResponseCompany.Result;
        }

        private PatientClaimInteractiveType SetMedicalEvent(PatientClaimInteractiveType patientClaimInteractiveType, InvoiceDetailView invoiceDetailView, string serviceTypeCode, string timeZone)
        {
            List<PCIMedicalEventType> lstMedicalEvent = new();
            List<PCIServiceType> lstService = new();
            PCIServiceType service;

            int serviceCounter = 0;
            int totalServiceCounter = 0;
            int totalMedicalEventServiceCounter = 0;

            int medicalEventCounter = 1;
            PCIMedicalEventType medicalEvent = new();
            medicalEvent.MedicalEventDate = (DateTime)invoiceDetailView.AppointmentDetails.DateOfAppointment;
            TimeSpan startTime = (TimeSpan)invoiceDetailView.AppointmentDetails.StartTime;
            DateTime appointmentDate = (DateTime)invoiceDetailView.AppointmentDetails.DateOfAppointment;
            appointmentDate = appointmentDate.Add(startTime);
         
            // DateTime? appointmentDateTZ = ConvertTime(appointmentDate, orgDetails);
            DateTime? appointmentDateTZ = DateTimeConversion.SetTimeZoneToDateTIme(appointmentDate, timeZone);

            if (appointmentDateTZ is not null)
            {
                //TimeSpan? utcOffSet = GetUtcOffset((DateTime)appointmentDateTZ, orgDetails);
                TimeSpan? utcOffSet = DateTimeConversion.GetUtcOffset((DateTime)appointmentDateTZ, timeZone);
                if (utcOffSet is not null)
                {
                    DateTimeOffset dtOffset = new DateTimeOffset((DateTime)appointmentDateTZ, (TimeSpan)utcOffSet);
                    string mTime = dtOffset.ToString("HH:mm:sszzz");
                    medicalEvent.MedicalEventTime = mTime;
                }


                // 
            }


            medicalEvent.Id = medicalEventCounter.ToString().PadLeft(2, '0');
            medicalEvent.InvoiceDetailsId = invoiceDetailView.Id;

            invoiceDetailView.InvoiceEpisodeItemAssocs.ToList().ForEach(item =>
            {
                service = new();

                if (item.EpisodeTypeId == (short)EpisodeTypes.MBS)
                {
                    totalMedicalEventServiceCounter++;
                    serviceCounter++;
                    totalServiceCounter++;
                    if (totalMedicalEventServiceCounter > 14)
                    {
                        medicalEvent.Service = lstService;
                        lstService = new();
                        lstMedicalEvent.Add(medicalEvent);
                        totalMedicalEventServiceCounter = 1;
                        medicalEvent = new();
                        //serviceCounter = 0;
                        medicalEventCounter++;
                        medicalEvent.Id = medicalEventCounter.ToString().PadLeft(2, '0');
                        medicalEvent.MedicalEventDate = (DateTime)invoiceDetailView.AppointmentDetails.DateOfAppointment;
                        medicalEvent.InvoiceDetailsId = item.InvoiceDetailsId;
                    }
                    service.Id = serviceCounter.ToString().PadLeft(4, '0');
                    service.InvoiceEpisodeItemAssocsId = item.Id;
                    service.ItemNumber = item.ItemNumber.ToString();
                    if(item.Quantity!=null && item.Quantity > 1)
                    {
                        service.FieldQuantity = item.Quantity.ToString();
                    }
                    string strAmount = (item.Fee).ToString();
                    if (patientClaimInteractiveType.AccountPaidInd == "N"){
                        if(item.BenefitPaid!=null && item.BenefitPaid >0)
                            service.PatientContribAmount = item.BenefitPaid.ToString().Replace(".", null);                        
                    }
                    if (strAmount.IndexOf(".") != -1)
                    {
                        strAmount = strAmount.Replace(".", null);
                    }
                    if (!string.IsNullOrWhiteSpace(item.Notes))
                        service.Text = item.Notes;
                    if (!string.IsNullOrWhiteSpace(item.TimeDuration))
                    {
                        service.TimeDuration = item.TimeDuration.PadLeft(3,'0');
                    }
                    if (item?.InvoiceEpisodeItemsIndicatorAssocs?.Count > 0)
                    {
                        item.InvoiceEpisodeItemsIndicatorAssocs?.Where(x => x.StatusId == (short)Status.Active).ToList().ForEach(indicator =>
                        {
                            service = SetItemIndicators(service, indicator);
                        });
                    }

                    service.ChargeAmount = strAmount;
                    lstService.Add(service);

                }
            });
            if (lstService is not null && lstService.Count > 0)
            {
                medicalEvent.Service = lstService;
                lstMedicalEvent.Add(medicalEvent);
            }
            if (lstMedicalEvent is not null && lstMedicalEvent.Count > 0)
            {
                patientClaimInteractiveType.MedicalEvent = lstMedicalEvent;
            }
            return patientClaimInteractiveType;
            //startTime = (TimeSpan)invoiceDetailView.AppointmentDetails.StartTime;
            //endTime = (TimeSpan)invoiceDetailView.AppointmentDetails.EndTime;
        }

       
        private PCIServiceType SetItemIndicators(PCIServiceType service, InvoiceEpisodeItemsIndicatorViewAssoc indicator)
        {
            switch (indicator.InvoiceItemIndicatorTypeId)
            {
                case (short)Shared.Models.Enum.InvoiceItemIndicatorType.Restrictive_Override:
                    {
                        service.RestrictiveOverrideCode = indicator.Value;
                        return service;

                    }
                case (short)Shared.Models.Enum.InvoiceItemIndicatorType.Self_Deemed_Code:
                    {
                        service.SelfDeemedCode = indicator.Value;
                        return service;

                    }
                case (short)Shared.Models.Enum.InvoiceItemIndicatorType.Not_Duplicate:
                    {
                        if (indicator.Value == "Y")
                            service.DuplicateServiceOverrideInd = indicator.Value;
                        return service;

                    }
                case (short)Shared.Models.Enum.InvoiceItemIndicatorType.Not_Normal_Aftercare:
                    {
                        if (indicator.Value == "Y")
                            service.AftercareOverrideInd = indicator.Value;
                        return service;

                    }
                case (short)Shared.Models.Enum.InvoiceItemIndicatorType.Not_Multiple:
                    {
                        if (indicator.Value == "Y")
                            service.MultipleProcedureOverrideInd = indicator.Value;
                        return service;

                    }
                default:
                    return service;
            }
        }
        private ServiceType SetItemIndicators(ServiceType service, InvoiceEpisodeItemsIndicatorViewAssoc indicator)
        {
            switch (indicator.InvoiceItemIndicatorTypeId)
            {
                case (short)Shared.Models.Enum.InvoiceItemIndicatorType.Restrictive_Override:
                    {
                        service.RestrictiveOverrideCode = indicator.Value;
                        return service;

                    }
                case (short)Shared.Models.Enum.InvoiceItemIndicatorType.Self_Deemed_Code:
                    {
                        service.SelfDeemedCode = indicator.Value;
                        return service;

                    }
                case (short)Shared.Models.Enum.InvoiceItemIndicatorType.Not_Duplicate:
                    {
                        if (indicator.Value == "Y")
                            service.DuplicateServiceOverrideInd = indicator.Value;
                        return service;

                    }
                case (short)Shared.Models.Enum.InvoiceItemIndicatorType.Not_Normal_Aftercare:
                    {
                        if (indicator.Value == "Y")
                            service.AftercareOverrideInd = indicator.Value;
                        return service;

                    }
                case (short)Shared.Models.Enum.InvoiceItemIndicatorType.Not_Multiple:
                    {
                        if (indicator.Value == "Y")
                            service.MultipleProcedureOverrideInd = indicator.Value;
                        return service;

                    }
                default:
                    return service;
            }
        }

        private DVAMedicare.ReferralType SetDVAReferralDetails(ReferralDetailInfo referralDetail, string serviceTypeCode)
        {
            DVAMedicare.ReferralType referral = null;

            if (referralDetail is not null)
            {
                DVAMedicare.ProviderType provider = new()
                {
                    ProviderNumber = referralDetail.ReferringProviderInfo.ProviderNumber
                };
                referral = new();
                referral.Provider = provider;
                referral.IssueDate = (DateTime)referralDetail.IssueDate;
                referral.TypeCode = serviceTypeCode;
                if (referralDetail.ReferralTypeId == (short)Shared.Models.Enum.ReferralType.Standard)
                {
                    referral.PeriodCode = "S";
                }
                else if (referralDetail.ReferralTypeId == (short)Shared.Models.Enum.ReferralType.Non_Standard)
                {
                    referral.PeriodCode = "N";
                    if (referralDetail.ExpiryDate is not null)
                        referral.Period = MonthDifference((DateTime)referralDetail.ExpiryDate, (DateTime)referralDetail.IssueDate).ToString();
                    else
                    {
                        return null;
                    }
                }
                else
                    referral.PeriodCode = "I";

            }
            return referral;
        }

        private DVAMedicare.VeteranPatientType SetDVAMedicarePatientType(ClaimantPatientDetailInfo claimantPatientDetailInfo)
        {
            DVAMedicare.VeteranPatientType patient = new();
            DVAMedicare.IdentityType identity = new();
            DVAMedicare.VeteranMembershipType medicare = new();
            if (claimantPatientDetailInfo is not null)
            {
                identity.DateOfBirth = claimantPatientDetailInfo.DateofBirth;//DateTimeOffset/2010 - 11 - 04 "2010-11-04";
                identity.GivenName = claimantPatientDetailInfo.FirstName.Trim();
                identity.FamilyName = claimantPatientDetailInfo.SurName.Trim();
                identity.Sex = SetGender(claimantPatientDetailInfo.GenderId);
                AccountHolderAssocInfo accountHolderMedicare = (claimantPatientDetailInfo.AccountHolderAssocInfos is null) ? null : claimantPatientDetailInfo.AccountHolderAssocInfos.Where(x => x.AccountHolderType == (short)AccountHolderTypes.DVA).FirstOrDefault();
                if (accountHolderMedicare is not null)
                {
                    medicare.VeteranNumber = accountHolderMedicare.AccountNumber;
                }
                if (identity is not null && medicare is not null)
                {
                    patient.Identity = identity;
                    patient.VeteranMembership = medicare;
                }

                if (claimantPatientDetailInfo.PatientAddress is not null)
                {
                    DVAMedicare.AddressType residentialAddres = new();       
                    
                    residentialAddres.Locality = claimantPatientDetailInfo.PatientAddress.Suburb;
                    residentialAddres.Postcode = claimantPatientDetailInfo.PatientAddress.PostCode;
                    patient.ResidentialAddress = residentialAddres;


                }
            }
            return patient;

        }

        private DVAMedicare.DVAServiceType SetDVAItemIndicators(DVAMedicare.DVAServiceType service, InvoiceEpisodeItemsIndicatorViewAssoc indicator)
        {
            switch (indicator.InvoiceItemIndicatorTypeId)
            {
                case (short)Shared.Models.Enum.InvoiceItemIndicatorType.Restrictive_Override:
                    {
                        service.RestrictiveOverrideCode = indicator.Value;
                        return service;

                    }
                case (short)Shared.Models.Enum.InvoiceItemIndicatorType.Self_Deemed_Code:
                    {
                        service.SelfDeemedCode = indicator.Value;
                        return service;

                    }
                case (short)Shared.Models.Enum.InvoiceItemIndicatorType.Not_Duplicate:
                    {
                        if (indicator.Value == "Y")
                            service.DuplicateServiceOverrideInd = indicator.Value;
                        return service;

                    }
                case (short)Shared.Models.Enum.InvoiceItemIndicatorType.Not_Normal_Aftercare:
                    {
                        if (indicator.Value == "Y")
                            service.AftercareOverrideInd = indicator.Value;
                        return service;

                    }
                case (short)Shared.Models.Enum.InvoiceItemIndicatorType.Not_Multiple:
                    {
                        if (indicator.Value == "Y")
                            service.MultipleProcedureOverrideInd = indicator.Value;
                        return service;

                    }
                default:
                    return service;
            }
        }

        private string SetGender(short? genderId)
        {
            switch (genderId)
            {
                case (short)Shared.Models.Enum.Gender.Male:
                    {

                        return "1";

                    }
                case (short)Shared.Models.Enum.Gender.Female:
                    {
                        return "2";

                    }
                case (short)Shared.Models.Enum.Gender.Other:
                    {

                        return "3";

                    }
                case (short)Shared.Models.Enum.Gender.Unknown:
                    {

                        return "9";

                    }

                default:
                    return "9";
            }
        }

        private PatientClaimInteractiveType SetPatientClaimantInfo(PatientClaimInteractiveType patientClaimInteractiveType, ClaimantPatientDetailInfo claimantPatientDetailInfo, AccountHolderAssocInfo accountolderMedicare, bool patientMinor,InvoiceDetailIdObject invoiceDetailIdObject=null)
        {
            MedicarePatientType patient = new();
            IdentityType identity = new();
            MembershipType medicare = new();
            ClaimantType claimant = new();
            Capstone2.Shared.Models.Medicare.AddressType residentialAddres = new();
            ContactType contactDetails = new();
            identity.DateOfBirth = claimantPatientDetailInfo.DateofBirth;//DateTimeOffset/2010 - 11 - 04 "2010-11-04";
            identity.GivenName = claimantPatientDetailInfo.FirstName.Trim();
            identity.FamilyName = claimantPatientDetailInfo.SurName.Trim();

            medicare.MemberNumber = accountolderMedicare.AccountNumber;
            medicare.MemberRefNumber = accountolderMedicare.AccountSubNumber;
            patient.Identity = identity;
            patient.Medicare = medicare;
            patientClaimInteractiveType.Patient = patient;
            if (invoiceDetailIdObject?.EftDetails is not null)
            {
                BankAccountType eftDetails = _mapper.Map<InvoicePatientEftAssoc, BankAccountType>(invoiceDetailIdObject?.EftDetails);
                claimant.EftDetails = eftDetails;
            }
            if (!patientMinor)
            {
                claimant.Identity = identity;
                claimant.Medicare = medicare;

                if(claimantPatientDetailInfo.PatientAddress is not null)
                {
                    residentialAddres.AddressLineOne = claimantPatientDetailInfo.PatientAddress.AddressLine1;
                    residentialAddres.AddressLineTwo = string.IsNullOrWhiteSpace(claimantPatientDetailInfo.PatientAddress.AddressLine2)?null:claimantPatientDetailInfo.PatientAddress.AddressLine2;
                    residentialAddres.Locality = claimantPatientDetailInfo.PatientAddress.Suburb;
                    residentialAddres.Postcode = claimantPatientDetailInfo.PatientAddress.PostCode;
                    claimant.ResidentialAddress = residentialAddres;


                }
                if (!string.IsNullOrWhiteSpace(claimantPatientDetailInfo.Mobile))
                {
                    contactDetails.PhoneNumber = claimantPatientDetailInfo.Mobile;
                    claimant.ContactDetails = contactDetails;
                }
                else if (!string.IsNullOrWhiteSpace(claimantPatientDetailInfo.HomeContact))
                {

                    contactDetails.PhoneNumber = claimantPatientDetailInfo.HomeContact;
                }
                else if (!string.IsNullOrWhiteSpace(claimantPatientDetailInfo.WorkContact))
                {
                    contactDetails.PhoneNumber = claimantPatientDetailInfo.WorkContact;
                    claimant.ContactDetails = contactDetails;

                }
            }
            else
            {
                IdentityType identityClaimant = new();
                MembershipType medicareClaimant = new();

                if (claimantPatientDetailInfo.AccountHolderParentGuardianAssocInfo is not null)
                {
                    identityClaimant.DateOfBirth = claimantPatientDetailInfo.AccountHolderParentGuardianAssocInfo.DateOfBirth;
                    identityClaimant.FamilyName = claimantPatientDetailInfo.AccountHolderParentGuardianAssocInfo.LastName.Trim();
                    identityClaimant.GivenName = claimantPatientDetailInfo.AccountHolderParentGuardianAssocInfo.FirstName.Trim();

                    medicareClaimant.MemberNumber = claimantPatientDetailInfo.AccountHolderParentGuardianAssocInfo.MedicareCardNumber;
                    medicareClaimant.MemberRefNumber = claimantPatientDetailInfo.AccountHolderParentGuardianAssocInfo.MedicareCardIrn;

                    claimant.Identity = identityClaimant;
                    claimant.Medicare = medicareClaimant;

                    if(claimantPatientDetailInfo.AccountHolderAddress is not null && claimantPatientDetailInfo.AccountHolderAddress.AccountHolderType == (short)AccountHolderTypes.ParentOrGuardian)
                    {
                        residentialAddres.AddressLineOne = claimantPatientDetailInfo.AccountHolderAddress.AddressLine1;
                        residentialAddres.AddressLineTwo = string.IsNullOrWhiteSpace(claimantPatientDetailInfo.AccountHolderAddress.AddressLine2) ? null : claimantPatientDetailInfo.AccountHolderAddress.AddressLine2;
                        residentialAddres.Locality = claimantPatientDetailInfo.AccountHolderAddress.Suburb;
                        residentialAddres.Postcode = claimantPatientDetailInfo.AccountHolderAddress.PostCode;
                        claimant.ResidentialAddress = residentialAddres;
                    }
                }

               
            }
            patientClaimInteractiveType.Claimant = claimant;

            return patientClaimInteractiveType;
        }

        private async Task<ApiResponse<dynamic>> CallMedicareServicePCI(PatientClaimInteractiveRequestType patientClaimInteractiveRequestType, string serviceTypeCode, BaseHttpRequestContext baseHttpRequestContext, string origTransactionId, string minorId = null)
        {
            ApiResponse<dynamic> apiResponsePCI = new();
            string medicareSeviceUrl = string.Empty;
            string endpoint = "/medicare/SubmitClaim/{0}";
            Dictionary<string, string> headers = new();
            //{
            //    { "TransactionId", origTransactionId }
            //};
            if (!string.IsNullOrWhiteSpace(minorId))
            {
                headers.Add("MinorId", minorId);
            }
            if (!string.IsNullOrWhiteSpace(origTransactionId))
            {
                headers.Add("TransactionId", origTransactionId);
            }
            if (serviceTypeCode == Constants.CLAIMTYPE_SPECIALIST)
                medicareSeviceUrl = _appSettings.ApiUrls["MedicareServiceUrl"] + string.Format(endpoint, "specialist");
            else
                medicareSeviceUrl = _appSettings.ApiUrls["MedicareServiceUrl"] + string.Format(endpoint, "general");
            RestClient restClientMedicare;
            //if (string.IsNullOrWhiteSpace(origTransactionId))
            //    restClientMedicare = new RestClient(medicareSeviceUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            //else
            restClientMedicare = new RestClient(medicareSeviceUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken, headers);

            apiResponsePCI = await restClientMedicare.PostAsync<ApiResponse<dynamic>>(medicareSeviceUrl, patientClaimInteractiveRequestType);
            return apiResponsePCI;
        }

        private DateTime? GetDateFromOrganisation(OrganisationView orgDetails, bool isWindows)
        {
            string timeZone = (isWindows) ? orgDetails.WindowsTimeZoneData : orgDetails.LinuxTimeZoneData;

            var CompanyDateTimeNow = DateTimeConversion.ConvertTimeFromUtc(DateTime.UtcNow, timeZone);

            return CompanyDateTimeNow;
        }

        public async Task<ClaimantPatientDetailInfo> FetchClaimantPatientInfo(long patientId, BaseHttpRequestContext baseHttpRequestContext, string serviceTypeCode)
        {
            ApiResponse<ClaimantPatientDetailInfo> apiResponsePatient = new();
            string endpoint = string.Empty;
            //if(Constants.CLAIMTYPE_SPECIALIST==serviceTypeCode)
            //    endpoint = string.Format("/patient/{0}/referral_account_holder_info", patientId);
            //else
            endpoint = string.Format("/patient/{0}/account_holder_info", patientId);
            string patientSeviceUrl = _appSettings.ApiUrls["PatientServiceUrl"] + endpoint;
            RestClient restClientPatient = new RestClient(patientSeviceUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            apiResponsePatient = await restClientPatient.GetAsync<ApiResponse<ClaimantPatientDetailInfo>>(patientSeviceUrl);
            return (apiResponsePatient == null || apiResponsePatient.Result == null) ? null : apiResponsePatient.Result;
        }
        public async Task<OrganisationView> FetchMasterCompanyDetails(BaseHttpRequestContext baseHttpRequestContext)
        {

            ApiResponse<OrganisationView> apiResponseCompany = new();
            string endpoint = string.Format("/company/Organisation/{0}", baseHttpRequestContext.OrgId);
            string companySeviceUrl = _appSettings.ApiUrls["CompanyServiceUrl"] + endpoint;
            RestClient restClientCompany = new RestClient(companySeviceUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            apiResponseCompany = await restClientCompany.GetAsync<ApiResponse<OrganisationView>>(companySeviceUrl);
            return (apiResponseCompany == null || apiResponseCompany.Result == null) ? null : apiResponseCompany.Result;

        }
        private string FetchServiceType(short? providerTypeId)
        {
            switch (providerTypeId)
            {
                case (short)Shared.Models.Enum.ProviderType.General_Practitioner:
                    {
                        return Constants.CLAIMTYPE_GENERAL;

                    }
                case (short)Shared.Models.Enum.ProviderType.Specialist_and_consultant_physician:
                    {
                        return Constants.CLAIMTYPE_SPECIALIST;

                    }

                default:
                    return Constants.CLAIMTYPE_GENERAL;
            }
        }
        /// <summary>
        /// Method to generate a SDD Request
        /// </summary>
        /// <param name="id"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<dynamic>> SubmitSDD(InvoiceDetailIdObject invoiceDetailIdObject, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<dynamic> apiResponseSDD = new();

            bool isWindows = System.Runtime.InteropServices.RuntimeInformation
                                                  .IsOSPlatform(OSPlatform.Windows);

            string timeZone = string.Empty;

            //OrganisationView orgDetails = await FetchMasterCompanyDetails(baseHttpRequestContext);
            //string timeZone = (isWindows) ? orgDetails.WindowsTimeZoneData : orgDetails.LinuxTimeZoneData;
            //DateTime todayDate = (DateTime)GetDateFromOrganisation(orgDetails, isWindows);
            long invoice_Details_Id = invoiceDetailIdObject.Id;
            string reasonCode = invoiceDetailIdObject.ReasonCode;
            InvoiceMedicareAssoc medicareAssoc = await _invoiceDAL.GetLatestMedicareTransactionForClaimType(invoice_Details_Id, (short)ClaimType.PCI, baseHttpRequestContext.OrgId);
            if (!string.IsNullOrWhiteSpace(reasonCode) && medicareAssoc is not null && medicareAssoc.InvoiceMedicareStatus == "MEDICARE_ASSESSED" && medicareAssoc.CreatedDate > DateTime.UtcNow.AddHours(-24))
            {
                InvoiceDetailView invoiceDetailView = null;
                ApiResponse<InvoiceDetailView> apiResponseInv = await _invoiceBAL.GetInvoiceDetail(invoice_Details_Id, 0, baseHttpRequestContext);
                if (apiResponseInv is not null && apiResponseInv.StatusCode == StatusCodes.Status200OK && apiResponseInv.Result is not null)
                {
                    invoiceDetailView = apiResponseInv.Result;

                }
                if (invoiceDetailView is not null && invoiceDetailView.AppointmentDetails is not null && invoiceDetailView.AppointmentDetails.PatientDetailsId is not null)
                {
                    //
                    List<CompanyDetailInfo> lstCompany = await GetCompanyByIds(new List<int> { (int)invoiceDetailView.CompanyDetailsId }, baseHttpRequestContext);

                    if (lstCompany is not null)
                    {
                        short? timeZoneId = (lstCompany is not null && lstCompany.Count > 0) ? lstCompany.FirstOrDefault().TimeZoneId : null;
                        if (timeZoneId is not null)
                        {
                            timeZone = (isWindows) ? EnumExtensions.GetDescription((WindowsTimeZone)(timeZoneId)) : EnumExtensions.GetDescription((LinuxTimeZone)(timeZoneId));
                        }
                        else
                        {
                            OrganisationView orgDetails = await FetchMasterCompanyDetails(baseHttpRequestContext);
                            timeZone = (isWindows) ? orgDetails.WindowsTimeZoneData : orgDetails.LinuxTimeZoneData;
                        }

                    }
                    if (string.IsNullOrWhiteSpace(timeZone))
                    {
                        apiResponseSDD.StatusCode = StatusCodes.Status400BadRequest;
                        apiResponseSDD.Result = null;
                        apiResponseSDD.Message = "Timezone could not be fetched correctly.";
                        return apiResponseSDD;
                    }

                    DateTime todayDate = (DateTime)DateTimeConversion.ConvertTimeFromUtc(DateTime.UtcNow, timeZone);
                    ClaimantPatientDetailInfo claimantPatientDetailInfo = await FetchClaimantPatientInfo((long)invoiceDetailView.AppointmentDetails.PatientDetailsId, baseHttpRequestContext, Constants.CLAIMTYPE_GENERAL);
                    apiResponseSDD = ValidatePatientDetails(invoiceDetailView, claimantPatientDetailInfo, todayDate, Constants.CLAIMTYPE_GENERAL, null, (short)ClaimType.PCI);
                    if (!(apiResponseSDD is not null && apiResponseSDD.Errors is not null && apiResponseSDD.Errors.Count > 0))
                    {
                        SameDayDeleteRequestType sameDayDeleteRequestType = PrepareSDDBody(claimantPatientDetailInfo, reasonCode);
                        string medicareSeviceUrl = _appSettings.ApiUrls["MedicareServiceUrl"];
                        string endpoint = "/medicare/SubmitSameDayDeleteClaim";
                        Dictionary<string, string> headers = new()
                        {
                            { "TransactionId", medicareAssoc.TransactionId },
                            { "MinorId",medicareAssoc.MinorId}
                        };
                        RestClient restClientMedicare = new RestClient(medicareSeviceUrl + endpoint, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken, headers);
                        //RestClient restClientMedicare = new RestClient(medicareSeviceUrl + endpoint, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);

                        apiResponseSDD = await restClientMedicare.PostAsync<ApiResponse<dynamic>>(medicareSeviceUrl + endpoint, sameDayDeleteRequestType);
                        string errorText = string.Empty;
                        if (apiResponseSDD is not null)
                        {
                            if (apiResponseSDD.Result is not null)
                            {
                                Object respObj = ConvertMedicareReponse(apiResponseSDD.Result, (short)ClaimType.SDD);
                                if (respObj != null && "servicemessagestype" == respObj.GetType().Name.ToLower())
                                {
                                    ServiceMessagesType serviceMessageType = (ServiceMessagesType)respObj;
                                    if (serviceMessageType is not null && serviceMessageType.ServiceMessage is not null)
                                    {
                                        serviceMessageType.ServiceMessage?.ToList().ForEach(msg =>
                                        {
                                            errorText = errorText + msg.Code + ":" + msg.Reason;
                                        });
                                    }


                                }
                                else if (respObj != null && "samedaydeleteresponsetype" == respObj.GetType().Name.ToLower())
                                {
                                    SameDayDeleteResponseType sameDayResponse = (SameDayDeleteResponseType)respObj;
                                    if (sameDayResponse is not null && sameDayResponse.Status == "SUCCESS")
                                    {
                                        await UpdateInvoiceMedicareAssoctoDeleted(baseHttpRequestContext, medicareAssoc);
                                    }
                                }

                                else if (respObj != null && "medicarejsonerrorresponse" == respObj.GetType().Name.ToLower())
                                {
                                    MedicareJsonErrorResponse errorResponse = (MedicareJsonErrorResponse)respObj;
                                    if (errorResponse is not null && !string.IsNullOrWhiteSpace(errorResponse.Message))
                                    {
                                        errorText = errorText + errorResponse.Message;
                                    }
                                }
                            }
                            string medicarestatus = (apiResponseSDD.StatusCode == StatusCodes.Status200OK) ? "SUCCESS" : "ERROR";
                            InvoiceMedicareAssoc medicareAssoc_sdd = await SetInvoiceMedicareAssoc(invoice_Details_Id, invoiceDetailView.AppointmentDetails.PatientDetailsId, (short)ClaimType.SDD, medicareAssoc.TransactionId, medicarestatus, errorText, baseHttpRequestContext, medicareAssoc.MinorId);
                        
                            if (medicareAssoc_sdd != null && !string.IsNullOrWhiteSpace(medicareAssoc_sdd.ErrorText))
                            {
                                await StoreMedicareExceptionMessage(baseHttpRequestContext.OrgCode, medicareAssoc_sdd.Id);

                            }
                            apiResponseSDD.Result = medicareAssoc_sdd;
                            return apiResponseSDD;

                        }
                    }


                }


            }

            apiResponseSDD.StatusCode = StatusCodes.Status400BadRequest;
            apiResponseSDD.Errors.Add("Claim cannot be deleted with the details provided.");

            return apiResponseSDD;
        }

        private async Task UpdateInvoiceMedicareAssoctoDeleted(BaseHttpRequestContext baseHttpRequestContext, InvoiceMedicareAssoc medicareAssoc)
        {
            medicareAssoc.StatusId = (short)Status.Deleted;
            medicareAssoc.ModifiedBy = baseHttpRequestContext.UserId;
            medicareAssoc.ModifiedDate = DateTime.UtcNow;
            await _invoiceDAL.UpdateInvoiceMedicareAssoc(medicareAssoc);
        }
        private async Task<int> SetInvoiceMedicareAssoc
            (InvoiceDetailView invoiceDetailView, string minorId, string facilityId, short claimTypeId, string serviceProviderNumber, string serviceTypeCode,
            BaseHttpRequestContext baseHttpRequestContext, List<BBSMedicalEventType> lstMedicalEvent,string payeeProviderNumber ,bool resubmit =false)
        {
            List<InvoiceMedicareAssoc> lstMedicareAssoc = new();
          
            List<InvoiceMedicareAssoc> lstDelete = await _invoiceDAL.GetActiveMedicareTransactionForClaimType(invoiceDetailView.Id, claimTypeId, baseHttpRequestContext.OrgId);
            lstDelete.ForEach(medicareAssocDelete =>
            {
                medicareAssocDelete.StatusId = (short)Status.Deleted;
                medicareAssocDelete.ModifiedBy = baseHttpRequestContext.UserId;
                medicareAssocDelete.ModifiedDate = DateTime.UtcNow;
            });

            foreach (BBSMedicalEventType medEvent in lstMedicalEvent)
            {
                InvoiceMedicareAssoc medicareAssoc = new();
                medicareAssoc.InvoiceDetailsId = invoiceDetailView.Id;
                medicareAssoc.OrgId = baseHttpRequestContext.OrgId;
                medicareAssoc.PatientDetailsId = invoiceDetailView.AppointmentDetails.PatientDetailsId;
                medicareAssoc.IsInPatient = invoiceDetailView.IsInPatient;
                medicareAssoc.FacilityId = facilityId;
                medicareAssoc.MinorId = minorId;
                medicareAssoc.ServiceProviderNumber = serviceProviderNumber;
                medicareAssoc.PayeeProviderNumber = string.IsNullOrWhiteSpace(payeeProviderNumber)? serviceProviderNumber : payeeProviderNumber;
                medicareAssoc.ServiceTypeCode = serviceTypeCode;
                medicareAssoc.CreatedDate = DateTime.UtcNow;
                medicareAssoc.ModifiedDate = DateTime.UtcNow; ;
                medicareAssoc.ModifiedBy = baseHttpRequestContext.UserId;
                medicareAssoc.StatusId = (short)Status.Active;
                medicareAssoc.ClaimTypeId = claimTypeId;
                medicareAssoc.ClaimStatusId = (short)ClaimStatus.Ready_For_Pickup;
                medicareAssoc.ClaimRequestBody = JsonConvert.SerializeObject(medEvent);

                string MedicareNo = medEvent.Patient?.Medicare?.MemberNumber;
                bool isValid = MedicareValidityCheck(MedicareNo);//TO check valid, member card
                if (!isValid || string.IsNullOrWhiteSpace(MedicareNo))
                {
                    medicareAssoc.ErrorText = Constants.INVALID_MEDICARE_NUMBER_400;
                    medicareAssoc.ClaimStatusId = (short)ClaimStatus.Error;
                    medicareAssoc.InvoiceMedicareStatus = ClaimStatus.Error.ToString();
                }

                List<InvoiceItemsMedicareAssoc> lstItemsMedicareAssocs = new();
                medEvent.Service?.ToList().ForEach(service =>
                {
                    InvoiceItemsMedicareAssoc itemMedicareAssoc = new();
                    itemMedicareAssoc.OrgId = baseHttpRequestContext.OrgId;
                    itemMedicareAssoc.CreatedDate = DateTime.UtcNow;
                    itemMedicareAssoc.ModifiedDate = DateTime.UtcNow; ;
                    itemMedicareAssoc.ModifiedBy = baseHttpRequestContext.UserId;
                    itemMedicareAssoc.StatusId = (short)Status.Active;
                    itemMedicareAssoc.InvoiceEpisodeItemAssocsId = service.InvoiceEpisodeItemAssocsId;
                    itemMedicareAssoc.ServiceId = service.Id;
                    itemMedicareAssoc.ItemNumber = service.ItemNumber;
                    lstItemsMedicareAssocs.Add(itemMedicareAssoc);
                });
                medicareAssoc.InvoiceItemsMedicareAssocs = lstItemsMedicareAssocs;
                lstMedicareAssoc.Add(medicareAssoc);
            }

            if (lstDelete is not null && lstDelete.Count > 0)
                await _invoiceDAL.UpdateInvoiceMedicareAssocRange(lstDelete);
            int rows = await _invoiceDAL.AddInvoiceMedicareAssocRange(lstMedicareAssoc);
            if (resubmit && lstDelete is not null && lstDelete.Count >0)
            {

                await StoreInvoiceClaimedStatusMessage(baseHttpRequestContext.OrgCode, lstDelete.Select(x => x.Id).ToList(), claimTypeId);
            }
            return rows;

        }

        private async Task<int> SetInvoiceDVAMedicareAssoc(InvoiceDetailView invoiceDetailView, string minorId, string facilityId, short claimTypeId, string serviceProviderNumber, string serviceTypeCode,
            BaseHttpRequestContext baseHttpRequestContext, List<DVAMedicare.DVAMedicalEventType> lstDVAMedicalEvent,string payeeProviderNumber,bool resubmit=false)
        {
            List<InvoiceMedicareAssoc> lstMedicareAssoc = new();

            List<InvoiceMedicareAssoc> lstDelete = await _invoiceDAL.GetActiveMedicareTransactionForClaimType(invoiceDetailView.Id, claimTypeId, baseHttpRequestContext.OrgId);
            lstDelete.ForEach(medicareAssocDelete =>
            {
                medicareAssocDelete.StatusId = (short)Status.Deleted;
                medicareAssocDelete.ModifiedBy = baseHttpRequestContext.UserId;
                medicareAssocDelete.ModifiedDate = DateTime.UtcNow;
            });
            foreach (DVAMedicare.DVAMedicalEventType medEvent in lstDVAMedicalEvent)
            {
                InvoiceMedicareAssoc medicareAssoc = new();
                medicareAssoc.InvoiceDetailsId = invoiceDetailView.Id;
                medicareAssoc.OrgId = baseHttpRequestContext.OrgId;
                medicareAssoc.PatientDetailsId = invoiceDetailView.AppointmentDetails.PatientDetailsId;
                medicareAssoc.IsInPatient = invoiceDetailView.IsInPatient;
                medicareAssoc.FacilityId = facilityId;
                medicareAssoc.MinorId = minorId;
                medicareAssoc.ServiceProviderNumber = serviceProviderNumber;
                medicareAssoc.PayeeProviderNumber = string.IsNullOrWhiteSpace(payeeProviderNumber) ? serviceProviderNumber : payeeProviderNumber; 
                medicareAssoc.ServiceTypeCode = serviceTypeCode;
                medicareAssoc.CreatedDate = DateTime.UtcNow;
                medicareAssoc.ModifiedDate = DateTime.UtcNow; ;
                medicareAssoc.ModifiedBy = baseHttpRequestContext.UserId;
                medicareAssoc.StatusId = (short)Status.Active;
                medicareAssoc.ClaimTypeId = claimTypeId;
                medicareAssoc.ClaimStatusId = (short)ClaimStatus.Ready_For_Pickup;
                medicareAssoc.ClaimRequestBody = JsonConvert.SerializeObject(medEvent);

                List<InvoiceItemsMedicareAssoc> lstItemsMedicareAssocs = new();
                medEvent.Service?.ToList().ForEach(service =>
                {
                    InvoiceItemsMedicareAssoc itemMedicareAssoc = new();
                    itemMedicareAssoc.OrgId = baseHttpRequestContext.OrgId;
                    itemMedicareAssoc.CreatedDate = DateTime.UtcNow;
                    itemMedicareAssoc.ModifiedDate = DateTime.UtcNow; ;
                    itemMedicareAssoc.ModifiedBy = baseHttpRequestContext.UserId;
                    itemMedicareAssoc.StatusId = (short)Status.Active;
                    itemMedicareAssoc.InvoiceEpisodeItemAssocsId = service.InvoiceEpisodeItemAssocsId;
                    itemMedicareAssoc.ServiceId = service.Id;
                    itemMedicareAssoc.ItemNumber = service.ItemNumber;
                    lstItemsMedicareAssocs.Add(itemMedicareAssoc);
                });
                medicareAssoc.InvoiceItemsMedicareAssocs = lstItemsMedicareAssocs;
                lstMedicareAssoc.Add(medicareAssoc);
            }
            if (lstDelete is not null && lstDelete.Count > 0)
                await _invoiceDAL.UpdateInvoiceMedicareAssocRange(lstDelete);
            int rows = await _invoiceDAL.AddInvoiceMedicareAssocRange(lstMedicareAssoc); 
            if (resubmit && lstDelete is not null && lstDelete.Count > 0)
            {
                await StoreInvoiceClaimedStatusMessage(baseHttpRequestContext.OrgCode, lstDelete.Select(x => x.Id).ToList(), claimTypeId);
            }
            return rows;

        }
        public static bool MedicareValidityCheck(string medicareNumber)
        {
            if (!(medicareNumber?.Length == 10) || !medicareNumber.All(char.IsDigit))
                return false;

            var medArray = medicareNumber.Select(c => (int)char.GetNumericValue(c)).ToArray();
            if (medArray[9] == 0)
            {
                return false;
            }
            int checkSum = medArray.Zip(new[] { 1, 3, 7, 9, 1, 3, 7, 9 }, (m, d) => m * d).Sum() % 10;
            if (checkSum == medArray[8])
            {
                return true;
            }
            return false;
        }

        private async Task<InvoiceMedicareAssoc> SetInvoiceMedicareAssoc(long invoice_Details_Id, long? patient_id, short claimTypeId, string transactionId, string medicarestatus, string errorText, BaseHttpRequestContext baseHttpRequestContext, string minorId = null, PatientClaimInteractiveType patientClaimInteractiveType = null)
        {
            InvoiceMedicareAssoc medicareAssoc = new();
            if (claimTypeId == (short)ClaimType.PCI && patientClaimInteractiveType is not null)
            {
                patientClaimInteractiveType.MedicalEvent?.ToList().ForEach(medEvent =>
                {
                    medicareAssoc = new();
                    medicareAssoc.InvoiceDetailsId = invoice_Details_Id;
                    medicareAssoc.OrgId = baseHttpRequestContext.OrgId;
                    medicareAssoc.PatientDetailsId = patient_id;
                    medicareAssoc.InvoiceMedicareStatus = medicarestatus;
                    medicareAssoc.CreatedDate = DateTime.UtcNow;
                    medicareAssoc.ModifiedDate = DateTime.UtcNow; ;
                    medicareAssoc.ModifiedBy = baseHttpRequestContext.UserId;
                    medicareAssoc.StatusId = (short)Status.Active;
                    medicareAssoc.ClaimTypeId = claimTypeId;
                    medicareAssoc.TransactionId = transactionId;
                    medicareAssoc.ErrorText = errorText;
                    medicareAssoc.MinorId = minorId;
                    if (medEvent.InvoiceDetailsId == invoice_Details_Id)
                    {
                        medicareAssoc.MedicalEventId = medEvent.Id;
                    }
                    List<InvoiceItemsMedicareAssoc> lstItemsMedicareAssocs = new();
                    medEvent.Service?.ToList().ForEach(service =>
                    {
                        InvoiceItemsMedicareAssoc itemMedicareAssoc = new();
                        itemMedicareAssoc.OrgId = baseHttpRequestContext.OrgId;
                        itemMedicareAssoc.CreatedDate = DateTime.UtcNow;
                        itemMedicareAssoc.ModifiedDate = DateTime.UtcNow; ;
                        itemMedicareAssoc.ModifiedBy = baseHttpRequestContext.UserId;
                        itemMedicareAssoc.StatusId = (short)Status.Active;
                        itemMedicareAssoc.InvoiceEpisodeItemAssocsId = service.InvoiceEpisodeItemAssocsId;
                        itemMedicareAssoc.ServiceId = service.Id;
                        itemMedicareAssoc.ItemNumber = service.ItemNumber;
                        lstItemsMedicareAssocs.Add(itemMedicareAssoc);
                    });
                    medicareAssoc.InvoiceItemsMedicareAssocs = lstItemsMedicareAssocs;
                });
            }
            else
            {

                medicareAssoc.InvoiceDetailsId = invoice_Details_Id;
                medicareAssoc.OrgId = baseHttpRequestContext.OrgId;
                medicareAssoc.InvoiceMedicareStatus = medicarestatus;
                medicareAssoc.PatientDetailsId = patient_id;
                medicareAssoc.CreatedDate = DateTime.UtcNow;
                medicareAssoc.ModifiedDate = DateTime.UtcNow; ;
                medicareAssoc.ModifiedBy = baseHttpRequestContext.UserId;
                medicareAssoc.StatusId = (short)Status.Active;
                medicareAssoc.ClaimTypeId = claimTypeId;
                medicareAssoc.TransactionId = transactionId;
                medicareAssoc.ErrorText = errorText;
                medicareAssoc.MinorId = minorId;
            }



            return await _invoiceDAL.AddInvoiceMedicareAssoc(medicareAssoc);
        }

        private SameDayDeleteRequestType PrepareSDDBody(ClaimantPatientDetailInfo claimantPatientDetailInfo, string reasonCode)
        {
            SameDayDeleteRequestType sameDayDeleteRequestType = new();

            SameDayDeleteType sameDayDelete = new();
            MedicarePatientType patient = SetMedicarePatientType(claimantPatientDetailInfo,(short)ClaimType.SDD);
            sameDayDelete.Patient = patient;
            sameDayDelete.ReasonCode = reasonCode;
            sameDayDeleteRequestType.SameDayDelete = sameDayDelete;
            return sameDayDeleteRequestType;

        }

        private MedicarePatientType SetMedicarePatientType(ClaimantPatientDetailInfo claimantPatientDetailInfo,short claimTypeId=0)
        {


            MedicarePatientType patient = new();
            IdentityType identity = new();
            MembershipType medicare = new();

            if (claimantPatientDetailInfo is not null)
            {
                if(claimTypeId!=(short)ClaimType.SDD)
                    identity.DateOfBirth = claimantPatientDetailInfo.DateofBirth;//DateTimeOffset/2010 - 11 - 04 "2010-11-04";
                identity.GivenName = claimantPatientDetailInfo.FirstName.Trim();
                identity.FamilyName = claimantPatientDetailInfo.SurName.Trim();
                AccountHolderAssocInfo accountHolderMedicare = (claimantPatientDetailInfo.AccountHolderAssocInfos is null) ? null : claimantPatientDetailInfo.AccountHolderAssocInfos.Where(x => x.AccountHolderType == (short)AccountHolderTypes.Medicare).FirstOrDefault();
                if (accountHolderMedicare is not null)
                {
                    medicare.MemberNumber = accountHolderMedicare.AccountNumber;
                    medicare.MemberRefNumber = accountHolderMedicare.AccountSubNumber;
                }
                if (identity is not null && medicare is not null)
                {
                    patient.Identity = identity;
                    patient.Medicare = medicare;
                }

            }
            return patient;

        }
        /// <summary>
        /// method for PCI resubmit
        /// </summary>
        /// <param name="invoiceDetailObj"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<dynamic>> ResubmitPCI(InvoiceDetailIdObject invoiceDetailObj, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<dynamic> apiResponsePCI = new();

            bool isWindows = System.Runtime.InteropServices.RuntimeInformation
                                                  .IsOSPlatform(OSPlatform.Windows);
            OrganisationView orgDetails = await FetchMasterCompanyDetails(baseHttpRequestContext);
            string timeZone = (isWindows) ? orgDetails.WindowsTimeZoneData : orgDetails.LinuxTimeZoneData;
            DateTime todayDate = (DateTime)GetDateFromOrganisation(orgDetails, isWindows);
            long invoice_Details_Id = invoiceDetailObj.Id;
            InvoiceMedicareAssoc medicareAssoc = await _invoiceDAL.GetLatestMedicareTransactionForClaimType(invoice_Details_Id, (short)ClaimType.PCI, baseHttpRequestContext.OrgId);
            if (medicareAssoc is not null && medicareAssoc.InvoiceMedicareStatus == "MEDICARE_PENDABLE" && medicareAssoc.CreatedDate > DateTime.UtcNow.AddHours(-1))
            {
                InvoicePatientEftAssoc invoicePatientEftAssoc = await _invoiceDAL.FetchInvoicePatientEftAssoc(invoiceDetailObj.Id, baseHttpRequestContext.OrgId);
                if (invoicePatientEftAssoc is not null) invoiceDetailObj.EftDetails = invoicePatientEftAssoc;
                return await SubmitPCI(invoiceDetailObj, baseHttpRequestContext, medicareAssoc.TransactionId);

            }
            apiResponsePCI.StatusCode = StatusCodes.Status400BadRequest;
            apiResponsePCI.StatusCode = StatusCodes.Status400BadRequest;
            apiResponsePCI.Errors.Add("Claim cannot be re submitted with the details provided.");
            return apiResponsePCI;
        }

        public object ConvertMedicareReponse(string response, short claimTypeId = 0)
        {
            if (response.ToLower().Contains("highestseverity"))
            {
                return JsonConvert.DeserializeObject<ServiceMessagesType>(response);
            }

            else if (response.ToLower().Contains("claimassessment"))
            {
                return JsonConvert.DeserializeObject<PatientClaimInteractiveResponseType>(response);

            }
            else if (response.ToLower().Contains("status") && claimTypeId == (short)ClaimType.SDD)
            {
                return JsonConvert.DeserializeObject<SameDayDeleteResponseType>(response);
            }
            else if (response.ToLower().Contains("dhsein") && response.ToLower().Contains("codetype"))
            {
                return JsonConvert.DeserializeObject<MedicareJsonErrorResponse>(response);
            }
            return response;
        }
        private async Task StoreMedicareExceptionMessage(string orgCode, long medicareAssocId)
        {
            PaymentRequestDataModel paymentRequestDataModel = new PaymentRequestDataModel
            {
                OrgName = orgCode,
                PropertyType = MedicareRequestType.MEDICARE_EXCEPTION,//(int)EODRequestDataType.EODReport,
                PropertyId = new long[1] { medicareAssocId },

                PropertyValue = ""
            };
            Dictionary<string, string> userPros = new Dictionary<string, string>
                        {
                            { "RequestType","MedicareException"},
                            { "to",_configuration["AzureAD:ASBSubNameMedicare"]}
                        };
            byte[] jsonContentByteArr = Encoding.GetEncoding("iso-8859-1").GetBytes(JsonConvert.SerializeObject(paymentRequestDataModel));
           await _asbMessageSenderHelper.MessageSenderTopic(jsonContentByteArr, userPros, _configuration["AzureAD:AzSBConnStringMedicare"], _configuration["AzureAD:ASBTopicMedicare"]);
        }
        /// <summary>
        /// Validating the invoice before raising medicare claims
        /// </summary>
        /// <param name="invoiceDetailObj"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<dynamic>> ValidateInvoiceForClaims(InvoiceDetailView invoiceDetailView, short claimTypeId, DateTime todayDate, BaseHttpRequestContext baseHttpRequestContext)
        {

            ApiResponse<dynamic> apiResponse = new();
            string serviceProviderNumber = string.Empty;

            if (invoiceDetailView is not null && invoiceDetailView.InvoiceTypeId == (short)InvoiceType.Invoice && invoiceDetailView.InvoiceStatusId != (short)InvoiceStatus.Void)
            {

                ProviderType serviceProvider = new();
                string serviceTypeCode = string.Empty;
                // short claimypeId = (short)invoiceDetailObj.ClaimTypeId;

                short? providerTypeId = (invoiceDetailView.ProviderDetails is not null) ? invoiceDetailView.ProviderDetails.ProviderTypeId : null;
                if (providerTypeId is not null && providerTypeId > default(short))
                {
                    serviceTypeCode = FetchServiceType(providerTypeId);

                }
                if (claimTypeId > default(short))
                    apiResponse = ValidateInvoiceForClaim(invoiceDetailView, todayDate, serviceTypeCode, baseHttpRequestContext, claimTypeId);
                else
                    apiResponse.Errors.Add("Account Holder and Billing Schedule is not set properly.");
                if (apiResponse is not null && apiResponse.Errors is not null && apiResponse.Errors.Count > 0)
                {
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Result = null;
                    apiResponse.Message = "Failure";

                }
                else
                {
                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    apiResponse.Result = invoiceDetailView.Id;
                    apiResponse.Message = "Success";
                }
            }
            return apiResponse;
        }
        private async Task StoreInvoiceClaimedStatusMessage(string orgCode, List<long> invMedicareAssocIds, int propertyType)
        {
            InvoiceConversionRequestDataModel invoiceConversionRequestDataModel = new InvoiceConversionRequestDataModel
            {
                OrgName = orgCode,
                PropertyType = propertyType,
                PropertyId = invMedicareAssocIds.Select(x => x).ToArray(),
                PaymentDate = DateTime.UtcNow
            };
            Dictionary<string, string> userPros = new Dictionary<string, string>
                        {
                            { "RequestType","UpdateClaimedStatusId"},
                            { "to",_configuration["AzureAD:ASBSubNameClaimStatus"]}
                        };
            byte[] jsonContentByteArr = Encoding.GetEncoding("iso-8859-1").GetBytes(JsonConvert.SerializeObject(invoiceConversionRequestDataModel));
            await _asbMessageSenderHelper.MessageSenderTopic(jsonContentByteArr, userPros, _configuration["AzureAD:AzSBConnStringMedicare"], _configuration["AzureAD:ASBTopicMedicare"]);
        }
        public async Task<ApiResponse<dynamic>> ReProcessInvoiceForClaims(ClaimRequestObject claimRequestObj, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<dynamic> apiResponse = new();
            if (claimRequestObj is null || claimRequestObj.Id.Length == 0)
            {
                apiResponse.Errors.Add("Claims cannot be reprocessed with the data provided.");
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = null;
                apiResponse.Message = "Failure";
                return apiResponse;
            }

            List<long> invoiceDetailsIds = new List<long>();
          
            foreach (var claimId in claimRequestObj.Id)
            {
                var claimResponse = await ProcessInvoiceForClaims(new InvoiceDetailIdObject { Id = claimId }, baseHttpRequestContext,true);
              
                if ((claimResponse.Message is not null && claimResponse.Message.Contains("Failure", StringComparison.OrdinalIgnoreCase)) || (claimResponse.Errors is not null && claimResponse.Errors.Count > 0))
                {
                    apiResponse.Errors.Add($"InvoiceDetailId:{claimId} {claimResponse.Errors.FirstOrDefault()}");          
                }
                //else
                //{
                //    //TODO:Push Message Into Queue for Update Status of existing items
                //    invoiceDetailsIds.Add(claimId);
                //}
            }
         
            //if (invoiceDetailsIds.Count > 0)
            //{
            //    await StoreInvoiceClaimedStatusMessage(baseHttpRequestContext.OrgCode, invoiceDetailsIds, (short)ClaimType.Bulk_Bill);
            //}

            if (apiResponse.Errors.Count > 0)
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = null;
                apiResponse.Message = apiResponse.Errors.Count == claimRequestObj.Id.Length ? "Failure" : "Partial Failure";
                return apiResponse;
            }
            apiResponse.StatusCode = StatusCodes.Status200OK;
            return apiResponse;
        }

        public async Task<ApiResponse<dynamic>> ProcessInvoiceForClaims(InvoiceDetailIdObject invoiceDetailObj, BaseHttpRequestContext baseHttpRequestContext,bool resubmit =false)
        {
            ApiResponse<dynamic> apiResponse = new();
            if (invoiceDetailObj is null || invoiceDetailObj.Id <= default(long))
            {
                apiResponse.Errors.Add("Claims cannot be raised with the data provided.");
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = null;
                apiResponse.Message = "Failure";
                return apiResponse;
            }
            ApiResponse<InvoiceDetailView> apiResponseInv = await _invoiceBAL.GetInvoiceDetail(invoiceDetailObj.Id, 0, baseHttpRequestContext, false);//TODO:false ->Ignore Processed Claim Items
            if (apiResponseInv is not null && apiResponseInv.StatusCode == StatusCodes.Status200OK && apiResponseInv.Result is not null)
            {
                bool isWindows = System.Runtime.InteropServices.RuntimeInformation.IsOSPlatform(OSPlatform.Windows);
                InvoiceDetailView invoiceDetailView = apiResponseInv.Result;
                short claimTypeId = default(short);
                if (invoiceDetailView is not null && invoiceDetailView.InvoiceTypeId == (short)InvoiceType.Invoice && invoiceDetailView.InvoiceStatusId != (short)InvoiceStatus.Void)
                {
                    string serviceTypeCode = string.Empty;
                    short? providerTypeId = (invoiceDetailView.ProviderDetails is not null) ? invoiceDetailView.ProviderDetails.ProviderTypeId : null;
                    if (providerTypeId is not null && providerTypeId > default(short))
                    {
                        serviceTypeCode = FetchServiceType(providerTypeId);
                    }

                    if (invoiceDetailView.AccountHolderTypeId == (short)AccountHolderTypes.Medicare && invoiceDetailView.BillingScheduleTypeId == (short)BillingSchedules.Bulk_Bill)
                    {
                        claimTypeId = (short)ClaimType.Bulk_Bill;
                    }
                    else if (invoiceDetailView.AccountHolderTypeId == (short)AccountHolderTypes.DVA && invoiceDetailView.BillingScheduleTypeId == (short)BillingSchedules.DVA)
                    {
                        claimTypeId = (short)ClaimType.DVA;
                    }
                    else if (invoiceDetailView.AccountHolderTypeId == (short)AccountHolderTypes.Health_Fund && (invoiceDetailView.BillingScheduleTypeId == (short)BillingSchedules.Known_Gap || invoiceDetailView.BillingScheduleTypeId == (short)BillingSchedules.No_Gap))
                    {
                        claimTypeId = (short)ClaimType.IMC;
                    }
                    ProviderType serviceProvider = new();

                    string serviceProviderNumber = invoiceDetailView.ProviderDetails.LstUserCompanyInfo.Where(x => x.UserCompanyId == invoiceDetailView.CompanyDetailsId).Select(x => x.ProviderNumber).FirstOrDefault();
                    string payeeProviderNumber = string.Empty; 
                    if (!string.IsNullOrWhiteSpace(serviceProviderNumber))
                    {
                        serviceProvider.ProviderNumber = serviceProviderNumber;

                        if (invoiceDetailView.PayeeProviderId is not null && invoiceDetailView.PayeeProviderId > default(long) && invoiceDetailView.PayeeProviderDetails is not null)
                        {
                            payeeProviderNumber = invoiceDetailView.PayeeProviderDetails?.LstUserCompanyInfo?.Where(x => x.UserCompanyId == invoiceDetailView.PayeeCompanyDetailsId).Select(x => x.ProviderNumber).FirstOrDefault();
                            if (!string.IsNullOrWhiteSpace(payeeProviderNumber))
                            {
                                if (!payeeProviderNumber.Equals(serviceProviderNumber))
                                {
                                    ProviderType payeeProvider = new();
                                    payeeProvider.ProviderNumber = payeeProviderNumber;
                                }
                                else
                                {
                                    payeeProviderNumber = serviceProviderNumber;
                                }
                            }
                            else
                            {
                                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                                apiResponse.Result = null;
                                apiResponse.Message = "Failure";
                                apiResponse.Errors.Add("Payee Provider number is mandatory if a payee provider is selected.");
                                return apiResponse;

                            }
                        }
                        else
                        {
                            payeeProviderNumber = serviceProviderNumber;
                        }
                    }
                    else
                    {
                        apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                        apiResponse.Result = null;
                        apiResponse.Message = "Failure";
                        apiResponse.Errors.Add("Service Provider number is mandatory.");
                        return apiResponse;
                    }
                    string inPatientFacilityId = string.Empty;
                    CompanyDetailInfo companyDetailInfo = null;
                    List<int> companyIds = new List<int> { (int)invoiceDetailView.CompanyDetailsId };
                    if (invoiceDetailView.InvoiceAdditionalDetails is not null && invoiceDetailView.InvoiceAdditionalDetails.InPatientCompanyDetailsId is not null)
                        companyIds.Add((int)invoiceDetailView.InvoiceAdditionalDetails.InPatientCompanyDetailsId);
                    List<CompanyDetailInfo> lstCompany = await GetCompanyByIds(companyIds, baseHttpRequestContext);
                    string timeZone = string.Empty;
                    string minorId = string.Empty;

                    if (lstCompany is not null)
                    {
                        companyDetailInfo = (lstCompany is not null && lstCompany.Count > 0) ? lstCompany.Where(x => x.Id == invoiceDetailView.CompanyDetailsId).FirstOrDefault() : null;
                        short? timeZoneId = (companyDetailInfo is null) ? null : companyDetailInfo.TimeZoneId;
                        //short? timeZoneId = (lstCompany is not null && lstCompany.Count > 0) ? lstCompany.FirstOrDefault().TimeZoneId : null;
                        if (timeZoneId is not null)
                        {
                            timeZone = (isWindows) ? EnumExtensions.GetDescription((WindowsTimeZone)(timeZoneId)) : EnumExtensions.GetDescription((LinuxTimeZone)(timeZoneId));
                        }
                        else
                        {
                            OrganisationView orgDetails = await FetchMasterCompanyDetails(baseHttpRequestContext);
                            timeZone = (isWindows) ? orgDetails.WindowsTimeZoneData : orgDetails.LinuxTimeZoneData;
                        }
                        minorId = (companyDetailInfo is null) ? null : companyDetailInfo.MinorId;
                        if (invoiceDetailView.InvoiceAdditionalDetails is not null && invoiceDetailView.InvoiceAdditionalDetails.InPatientCompanyDetailsId is not null)
                        {
                            inPatientFacilityId = lstCompany.Where(x => x.Id == invoiceDetailView.InvoiceAdditionalDetails.InPatientCompanyDetailsId).FirstOrDefault().FacilityId;
                        }

                    }
                    if (string.IsNullOrWhiteSpace(minorId))
                    {
                        apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                        apiResponse.Result = null;
                        apiResponse.Message = "Failure";
                        apiResponse.Errors.Add("MinorId is mandatory for In patient claims.");
                        return apiResponse;
                    }
                    if (string.IsNullOrWhiteSpace(timeZone))
                    {
                        apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                        apiResponse.Result = null;
                        apiResponse.Message = "Timezone could not be fetched correctly.";
                        return apiResponse;
                    }

                    DateTime todayDate = (DateTime)DateTimeConversion.ConvertTimeFromUtc(DateTime.UtcNow, timeZone);
                    apiResponse = ValidateInvoiceForClaim(invoiceDetailView, todayDate, serviceTypeCode, baseHttpRequestContext, claimTypeId);

                    

                    string facilityId = string.Empty;
                    if (invoiceDetailView.IsInPatient || claimTypeId == (short)ClaimType.IMC)
                    {
                        facilityId = (string.IsNullOrWhiteSpace(inPatientFacilityId)) ? ((companyDetailInfo != null) ? companyDetailInfo.FacilityId : string.Empty) : inPatientFacilityId;

                        //if (claimTypeId == (short)ClaimType.IMC)
                        //{
                        //CompanyDetailInfo companyDetailInfo = (lstCompany is not null && lstCompany.Count > 0) ? lstCompany.FirstOrDefault() : null;

                        //facilityId = (invoiceDetailView.InvoiceAdditionalDetails is null || string.IsNullOrWhiteSpace(invoiceDetailView.InvoiceAdditionalDetails.FacilityId)) ? ((companyDetailInfo is not null) ? companyDetailInfo.FacilityId : null) : invoiceDetailView.InvoiceAdditionalDetails.FacilityId;

                        //}
                        //else
                        //{
                        //    facilityId = (lstCompany is not null && lstCompany.Count > 0) ? lstCompany.FirstOrDefault().FacilityId : null;

                        //}
                        if (string.IsNullOrWhiteSpace(facilityId))
                        {
                            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                            apiResponse.Result = null;
                            apiResponse.Message = "Failure";
                            apiResponse.Errors.Add("Faciltity Id is mandatory for In patient claims.");
                            return apiResponse;
                        }
                    }

                    ClaimantPatientDetailInfo claimantPatientDetailInfo = await FetchClaimantPatientInfo((long)invoiceDetailView.AppointmentDetails.PatientDetailsId, baseHttpRequestContext, serviceTypeCode);
                    ApiResponse<dynamic> apiResponsePatient = ValidatePatientDetails(invoiceDetailView, claimantPatientDetailInfo, todayDate, serviceTypeCode, invoiceDetailView.AppointmentDetails.DateOfAppointment, claimTypeId);
                    if (apiResponsePatient is not null && apiResponsePatient.Errors is not null && apiResponsePatient.Errors.Count > 0)
                    {
                        apiResponse.Errors.AddRange(apiResponsePatient.Errors);
                    }
                   
                    if (apiResponse is not null && (apiResponse.Errors is null || apiResponse.Errors.Count == 0))
                    {
                        InvoiceMedicareAssoc medicareAssoc = null;
                      
                        if (claimTypeId == (short)ClaimType.Bulk_Bill)
                        {
                            await PrepareBulkBillBody(invoiceDetailView, minorId, facilityId, serviceTypeCode, claimantPatientDetailInfo, todayDate, timeZone, baseHttpRequestContext, claimTypeId, serviceProviderNumber,payeeProviderNumber ,resubmit);
                            apiResponse.StatusCode = StatusCodes.Status200OK;
                            apiResponse.Result = invoiceDetailView.Id;
                            apiResponse.Message = "Success";
                        }
                        else if (claimTypeId == (short)ClaimType.DVA)
                        {
                            await PrepareDVABulkBillBody(invoiceDetailView, minorId, facilityId, serviceTypeCode, claimantPatientDetailInfo, todayDate, timeZone, baseHttpRequestContext, claimTypeId, serviceProviderNumber,payeeProviderNumber,resubmit);
                            apiResponse.StatusCode = StatusCodes.Status200OK;
                            apiResponse.Result = invoiceDetailView.Id;
                            apiResponse.Message = "Success";
                        }

                        else
                        {
                            apiResponse.Errors.Add("Claims cannot be raised with the data provided.");
                            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                            apiResponse.Result = null;
                            apiResponse.Message = "Failure";
                            return apiResponse;
                        }

                    }
                }
            }
           
            return apiResponse;
        }
        private async Task<int> PrepareBulkBillBody(InvoiceDetailView invoiceDetailView, string minorId, string facilityId, string serviceTypeCode, ClaimantPatientDetailInfo claimantPatientDetailInfo, DateTime todayDate, string timeZone, BaseHttpRequestContext baseHttpRequestContext, short claimTypeId, string serviceProviderNumber, string payeeProviderNumber,bool resubmit = false)
        {
           
            List<BBSMedicalEventType> lstBBSMedicalEvent = PopulateBBSMedicalEventType(invoiceDetailView, claimantPatientDetailInfo, todayDate, timeZone, serviceTypeCode);
            return await SetInvoiceMedicareAssoc(invoiceDetailView, minorId, facilityId, claimTypeId, serviceProviderNumber, serviceTypeCode, baseHttpRequestContext, lstBBSMedicalEvent,payeeProviderNumber ,resubmit);
        }
        private List<BBSMedicalEventType> PopulateBBSMedicalEventType(InvoiceDetailView invoiceDetailView, ClaimantPatientDetailInfo claimantPatientDetailInfo, DateTime todayDate, string timeZone, string serviceTypeCode)
        {
            BBSMedicalEventType bBSMedicalEvent = new();
            bBSMedicalEvent.AuthorisationDate = todayDate;
            bBSMedicalEvent.CreateDateTime = ConvertDateTimeZone(todayDate, timeZone);
            bBSMedicalEvent.MedicalEventDate = (DateTime)invoiceDetailView.AppointmentDetails.DateOfAppointment;
            bBSMedicalEvent.InvoiceDetailsId = invoiceDetailView.Id;
            bBSMedicalEvent.ReferralOverrideCode = invoiceDetailView.ReferralOverrideCode;
            bBSMedicalEvent.SubmissionAuthorityInd = "Y";
            if (serviceTypeCode != Constants.CLAIMTYPE_GENERAL && string.IsNullOrWhiteSpace(bBSMedicalEvent.ReferralOverrideCode))
            {
                bBSMedicalEvent.Referral = SetReferralDetails(invoiceDetailView.AppointmentDetails.ReferralDetail, serviceTypeCode);
            }
            bBSMedicalEvent.Patient = SetMedicarePatientType(claimantPatientDetailInfo);
            DateTime appointmentDate = (DateTime)invoiceDetailView.AppointmentDetails.DateOfAppointment;
            TimeSpan? startTime = invoiceDetailView.AppointmentDetails.StartTime;
            if (startTime is not null)
            {
                appointmentDate = appointmentDate.Add((TimeSpan)startTime);
                DateTime? appointmentDateTZ = DateTimeConversion.SetTimeZoneToDateTIme(appointmentDate, timeZone);
                if (appointmentDateTZ is not null)
                {
                    TimeSpan? utcOffSet = DateTimeConversion.GetUtcOffset((DateTime)appointmentDateTZ, timeZone);
                    if (utcOffSet is not null)
                    {
                        DateTimeOffset dtOffset = new DateTimeOffset((DateTime)appointmentDateTZ, (TimeSpan)utcOffSet);
                        string mTime = dtOffset.ToString("HH:mm:sszzz");
                        bBSMedicalEvent.MedicalEventTime = mTime;
                    }
                }
            }
            List<BBSMedicalEventType> lstMedicalEvent = new();
            List<ServiceType> lstService = new();
            int totalMedicalEventServiceCounter = 0;
            invoiceDetailView.InvoiceEpisodeItemAssocs.ToList().ForEach(item =>
            {
                ServiceType service = new();

                if (item.EpisodeTypeId == (short)EpisodeTypes.MBS && item.StatusId==(short)Status.Active && (item.ClaimStatusId==null|| item.ClaimStatusId!=(short)ClaimStatus.Complete))
                {
                    totalMedicalEventServiceCounter++;

                    if (totalMedicalEventServiceCounter > 14)
                    {
                        bBSMedicalEvent.Service = lstService;
                        lstService = new();
                        lstMedicalEvent.Add(bBSMedicalEvent);
                        totalMedicalEventServiceCounter = 1;
                        bBSMedicalEvent = bBSMedicalEvent.Clone();
                        bBSMedicalEvent.Service = null;

                    }
                    service.InvoiceEpisodeItemAssocsId = item.Id;
                    service.ItemNumber = item.ItemNumber.ToString();
                    if (item.Quantity != null && item.Quantity > 1)
                    {
                        service.FieldQuantity = item.Quantity.ToString();
                    }
                    string strAmount = (item.Fee != null) ? (item.Fee).ToString() : "0";
                    if (strAmount.IndexOf(".") != -1)
                    {
                        strAmount = strAmount.Replace(".", null);
                    }
                    if (!string.IsNullOrWhiteSpace(item.Notes))
                        service.Text = item.Notes;
                    if (!string.IsNullOrWhiteSpace(item.TimeDuration))
                    {
                        service.TimeDuration = item.TimeDuration.PadLeft(3, '0');
                    }
                    if (item?.InvoiceEpisodeItemsIndicatorAssocs?.Count > 0)
                    {
                        item.InvoiceEpisodeItemsIndicatorAssocs?.Where(x => x.StatusId == (short)Status.Active).ToList().ForEach(indicator =>
                        {
                            service = SetItemIndicators(service, indicator);
                        });
                    }

                    service.ChargeAmount = strAmount;
                    lstService.Add(service);

                }
            });
            if (lstService is not null && lstService.Count > 0)
            {
                bBSMedicalEvent.Service = lstService;
                lstMedicalEvent.Add(bBSMedicalEvent);
            }

            return lstMedicalEvent;

        }

        private async Task<int> PrepareDVABulkBillBody(InvoiceDetailView invoiceDetailView, string minorId, string facilityId, string serviceTypeCode, ClaimantPatientDetailInfo claimantPatientDetailInfo, DateTime todayDate, string timeZone, BaseHttpRequestContext baseHttpRequestContext, short claimTypeId, string serviceProviderNumber,string payeeProviderNumber,bool resubmit=false)
        {
            List<DVAMedicare.DVAMedicalEventType> lstDVAMedicalEvent = await PopulateDVAMedicalEventType(invoiceDetailView, claimantPatientDetailInfo, todayDate, timeZone, serviceTypeCode);
            return await SetInvoiceDVAMedicareAssoc(invoiceDetailView, minorId, facilityId, claimTypeId, serviceProviderNumber, serviceTypeCode, baseHttpRequestContext, lstDVAMedicalEvent,payeeProviderNumber,resubmit);
        }
        private async Task<List<DVAMedicare.DVAMedicalEventType>> PopulateDVAMedicalEventType(InvoiceDetailView invoiceDetailView, ClaimantPatientDetailInfo claimantPatientDetailInfo, DateTime todayDate, string timeZone, string serviceTypeCode)
        {
            DVAMedicare.DVAMedicalEventType dvaMedicalEvent = new DVAMedicare.DVAMedicalEventType();
            dvaMedicalEvent.AuthorisationDate = todayDate;
            dvaMedicalEvent.CreateDateTime = ConvertDateTimeZone(todayDate, timeZone);
            dvaMedicalEvent.MedicalEventDate = (DateTime)invoiceDetailView.AppointmentDetails.DateOfAppointment;
            dvaMedicalEvent.InvoiceDetailsId = invoiceDetailView.Id;
            dvaMedicalEvent.ReferralOverrideCode = invoiceDetailView.ReferralOverrideCode;
            dvaMedicalEvent.SubmissionAuthorityInd = "Y";
            long accountRef = await _invoiceDAL.GetNextVal("[Invoice].[SeqDVAAccountRefId]");
            string accountReferance =  (accountRef >default(long)) ? accountRef.ToString() : "DVA";

            if (serviceTypeCode != Constants.CLAIMTYPE_GENERAL && string.IsNullOrWhiteSpace(dvaMedicalEvent.ReferralOverrideCode))
            {
                dvaMedicalEvent.Referral = SetDVAReferralDetails(invoiceDetailView.AppointmentDetails.ReferralDetail, serviceTypeCode);
            }
            dvaMedicalEvent.Patient = SetDVAMedicarePatientType(claimantPatientDetailInfo);

            dvaMedicalEvent.AcceptedDisability = SetAcceptedDisability(claimantPatientDetailInfo);
            DateTime appointmentDate = (DateTime)invoiceDetailView.AppointmentDetails.DateOfAppointment;
            TimeSpan? startTime = invoiceDetailView.AppointmentDetails.StartTime;
            if (startTime is not null)
            {
                appointmentDate = appointmentDate.Add((TimeSpan)startTime);
                DateTime? appointmentDateTZ = DateTimeConversion.SetTimeZoneToDateTIme(appointmentDate, timeZone);
                if (appointmentDateTZ is not null)
                {
                    TimeSpan? utcOffSet = DateTimeConversion.GetUtcOffset((DateTime)appointmentDateTZ, timeZone);
                    if (utcOffSet is not null)
                    {
                        DateTimeOffset dtOffset = new DateTimeOffset((DateTime)appointmentDateTZ, (TimeSpan)utcOffSet);
                        string mTime = dtOffset.ToString("HH:mm:sszzz");
                        dvaMedicalEvent.MedicalEventTime = mTime;
                    }
                }
            }
            List<DVAMedicare.DVAMedicalEventType> lstDVAMedicalEvent = new();
            List<DVAMedicare.DVAServiceType> lstService = new();
            int totalMedicalEventServiceCounter = 0;
            invoiceDetailView.InvoiceEpisodeItemAssocs.ToList().ForEach(item =>
            {
                DVAMedicare.DVAServiceType service = new();

                if (item.EpisodeTypeId == (short)EpisodeTypes.MBS && item.StatusId == (short)Status.Active && (item.ClaimStatusId == null || item.ClaimStatusId != (short)ClaimStatus.Complete))
                {
                    totalMedicalEventServiceCounter++;

                    if (totalMedicalEventServiceCounter > 14)
                    {
                        dvaMedicalEvent.Service = lstService;
                        lstService = new();
                        lstDVAMedicalEvent.Add(dvaMedicalEvent);
                        totalMedicalEventServiceCounter = 1;
                        dvaMedicalEvent = dvaMedicalEvent.Clone();
                        dvaMedicalEvent.Service = null;

                    }

                    service.AccountReferenceNumber = accountReferance;
                    service.InvoiceEpisodeItemAssocsId = item.Id;
                    service.ItemNumber = item.ItemNumber.ToString();
                    if (item.Quantity != null && item.Quantity > 1)
                    {
                        service.FieldQuantity = item.Quantity.ToString();
                    }
                    if (!service.ItemNumber.Equals("KM", StringComparison.OrdinalIgnoreCase))
                    {
                        string strAmount = (item.Fee != null) ? (item.Fee).ToString() : "0";
                        if (strAmount.IndexOf(".") != -1)
                        {
                            strAmount = strAmount.Replace(".", null);
                        }
                        service.ChargeAmount = strAmount;

                        if (!string.IsNullOrWhiteSpace(item.Notes))
                            service.Text = item.Notes;
                        if (!string.IsNullOrWhiteSpace(item.TimeDuration))
                        {
                            service.TimeDuration = item.TimeDuration.PadLeft(3, '0');
                        }
                        if (item?.InvoiceEpisodeItemsIndicatorAssocs?.Count > 0)
                        {
                            item.InvoiceEpisodeItemsIndicatorAssocs?.Where(x => x.StatusId == (short)Status.Active).ToList().ForEach(indicator =>
                            {
                                service = SetDVAItemIndicators(service, indicator);
                            });
                        }
                    }
                    else if (!dvaMedicalEvent.TreatmentLocationCode.Equals("R", StringComparison.OrdinalIgnoreCase))
                    {
                        service.DistanceKilometres = 0;//TODO
                    }

                    lstService.Add(service);

                }
            });
            if (lstService is not null && lstService.Count > 0)
            {
                dvaMedicalEvent.Service = lstService;
                lstDVAMedicalEvent.Add(dvaMedicalEvent);
            }

            return lstDVAMedicalEvent;
        }

        private DVAMedicare.AcceptedDisabilityType SetAcceptedDisability(ClaimantPatientDetailInfo claimantPatientDetailInfo)
        {
            
            if (claimantPatientDetailInfo is not null)
            {
                AccountHolderAssocInfo accountHolderDVA = (claimantPatientDetailInfo.AccountHolderAssocInfos is null) ? null : claimantPatientDetailInfo.AccountHolderAssocInfos.Where(x => x.AccountHolderType == (short)AccountHolderTypes.DVA).FirstOrDefault();
                if(accountHolderDVA?.ColourId is not null && accountHolderDVA?.ColourId == (short)AccountHolderDvaColor.White)
                {
                    if (accountHolderDVA.AcceptedDisabilityInd == true)
                    {
                        DVAMedicare.AcceptedDisabilityType acceptedDisability = new();
                        acceptedDisability.Ind = "Y";
                        acceptedDisability.Code = accountHolderDVA.AcceptedDisabilityCode;
                        return acceptedDisability;
                    }
                }

            }
            return null;
        }

        private DateTimeOffset ConvertDateTimeZone(DateTimeOffset date, string timeZone)
        {
            var localTimeZone = TimeZoneInfo.FindSystemTimeZoneById(timeZone);
            var convertedDateTimeOffSet = TimeZoneInfo.ConvertTime(date, localTimeZone);
            var localDateTimeZone = new DateTimeOffset(date.DateTime, convertedDateTimeOffSet.Offset);
            return localDateTimeZone;
        }

        private async Task<ApiResponse<dynamic>> ValidateInPatientClaim(BaseHttpRequestContext baseHttpRequestContext, InvoiceDetailView invoiceDetailView)
        {
            ApiResponse<dynamic> apiResponse = new ApiResponse<dynamic>();
            if (invoiceDetailView.IsInPatient)
            {
                List<CompanyDetailInfo> lstCompany = await GetCompanyByIds(new List<int> { (int)invoiceDetailView.CompanyDetailsId }, baseHttpRequestContext);
                string facilityId = (lstCompany is not null && lstCompany.Count > 0) ? lstCompany.FirstOrDefault().FacilityId : null;
                if (string.IsNullOrWhiteSpace(facilityId))
                {
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Result = null;
                    apiResponse.Message = "Failure";
                    apiResponse.Errors.Add("Faciltity Id is mandatory for In patient claims.");
                }
            }
            return apiResponse;
        }

        private async Task<InvoiceMedicareAssoc> AddToClaimsQueue(InvoiceDetailView invoiceDetailView, short claimTypeId, BaseHttpRequestContext baseHttpRequestContext)
        {
            InvoiceMedicareAssoc medicareAssoc = new();
            medicareAssoc.InvoiceDetailsId = invoiceDetailView.Id;
            medicareAssoc.OrgId = invoiceDetailView.OrgId;
            medicareAssoc.CreatedDate = DateTime.UtcNow;
            medicareAssoc.ModifiedBy = baseHttpRequestContext.OrgId;
            medicareAssoc.ClaimTypeId = claimTypeId;
            medicareAssoc.PatientDetailsId = invoiceDetailView.AppointmentDetails.PatientDetailsId;
            medicareAssoc.StatusId = (short)Status.Active;
            medicareAssoc.IsInPatient = invoiceDetailView.IsInPatient;

            return await _invoiceDAL.AddInvoiceMedicareAssoc(medicareAssoc);
        }
    }
}