﻿using AutoMapper;
using Capstone2.Framework.Business.Common;
using Capstone2.Framework.RestApi;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.Framework.RestApi.Utility;
using Capstone2.RestServices.Invoice.Common;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.Invoice.Interfaces;
using Capstone2.RestServices.Invoice.Models;
using Capstone2.RestServices.Invoice.Utility;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Transactions;
using InvoiceDetailView = Capstone2.RestServices.Invoice.Models.InvoiceDetailView;

namespace Capstone2.RestServices.Invoice.Services
{
    public class InvoiceBAL : IInvoiceBAL
    {
        public readonly IInvoiceDAL _invoiceDAL;
        public IMapper _mapper;
        public readonly AppSettings _appSettings;
        private readonly ILogger<InvoiceBAL> _logger;
        private IASBMessageSenderHelper _asbMessageSenderHelper;
        private readonly IConfiguration _configuration;

        public InvoiceBAL(IInvoiceDAL invoiceDAL, IMapper mapper, IOptions<AppSettings> appSettings, ILogger<InvoiceBAL> logger, IConfiguration configuration, IASBMessageSenderHelper asbMessageSenderHelper)
        {
            _invoiceDAL = invoiceDAL;
            _mapper = mapper;
            _appSettings = appSettings.Value;
            _logger = logger;
            _configuration = configuration;
            _asbMessageSenderHelper = asbMessageSenderHelper;
        }
        /// <summary>
        /// Method to add new Invoice Summary
        /// </summary>
        /// <param name="invoiceSummary"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long>> AddInvoiceSummary(InvoiceSummary invoiceSummary, BaseHttpRequestContext baseHttpRequestContext)
        {
            int orgId = baseHttpRequestContext.OrgId;
            long loggedInUser = baseHttpRequestContext.UserId;
            long summaryId = default(long);
            ApiResponse<long> apiResponse = new();
            decimal totalAmount = default(decimal);
            decimal owing = default(decimal);

            if (invoiceSummary is not null)
            {
                long? appointmentDetailsId = invoiceSummary.InvoiceDetails?.FirstOrDefault(x => x.AppointmentDetailsId != null)?.AppointmentDetailsId;
                invoiceSummary.ModifiedBy = loggedInUser;
                invoiceSummary.OrgId = orgId;
                invoiceSummary.CreatedDate = DateTime.UtcNow;
                if (invoiceSummary.InvoiceTypeId == default(short))
                {
                    invoiceSummary.InvoiceTypeId = (short)InvoiceType.Estimate;
                    invoiceSummary.InvoiceStatusId = (short)InvoiceStatus.Valid;
                }
                if (invoiceSummary.InvoiceStatusId == default(short))
                {
                    invoiceSummary.InvoiceStatusId = (short)InvoiceStatus.Valid;
                }
                if (invoiceSummary.FinanceTypeId == default(short))
                {
                    invoiceSummary.FinanceTypeId = (short)FinanceType.Estimate;
                }
                if (invoiceSummary.InvoiceDetails is not null)
                {
                    short financeTypeId = invoiceSummary.FinanceTypeId;
                    if (financeTypeId != (short)FinanceType.Template)
                    {
                        invoiceSummary.RecordId = await SetRecordId(2);
                    }
                    List<InvoiceDetail> lstInvoiceDetails = invoiceSummary.InvoiceDetails.ToList();
                    foreach (var invoice in lstInvoiceDetails)
                    //lstInvoiceDetails.ForEach(invoice =>
                    {
                        invoice.CreatedDate = DateTime.UtcNow;
                        invoice.OrgId = orgId;
                        invoice.ModifiedBy = loggedInUser;
                        invoice.Deposit = invoice.Deposit is null ? 0 : invoice.Deposit;
                        invoice.TotalAmount = invoice.TotalAmount is null ? 0 : invoice.TotalAmount;
                        invoice.Owing = invoice.Owing is null ? 0 : invoice.Owing;
                        invoice.ExcessAmount = (invoice.ExcessAmount is null) ? default(decimal) : invoice.ExcessAmount;
                        invoice.ExcessOwing = (invoice.ExcessAmount is null) ? default(decimal) : invoice.ExcessAmount;
                        if (invoice.MsrId is null || invoice.MsrId == default(short))
                        {
                            invoice.MsrId = (short)MSR.MSR_Off;
                        }
                        if (invoice.InvoiceTypeId == default(short))
                        {
                            invoice.InvoiceTypeId = (short)InvoiceType.Estimate;
                        }
                        if (invoice.InvoiceTypeId == default(short))
                        {
                            invoice.InvoiceTypeId = (short)InvoiceType.Estimate;
                        }
                        else if (invoice.InvoiceTypeId == (short)InvoiceType.Invoice)
                        {
                            invoice.Deposit = 0;
                            invoice.DateInvoiced = DateTime.UtcNow;
                            invoice.InvoiceStatusId = (short)InvoiceStatus.Unpaid;
                        }
                        if (financeTypeId == (short)FinanceType.Template)
                        {
                            invoice.AppointmentDetailsId = null;
                            invoice.DateOfService = null;
                            invoice.IsServiceDate = false;
                            invoice.InvoiceTypeId = (short)InvoiceType.Estimate;
                            invoice.InvoiceStatusId = (short)InvoiceStatus.Valid;
                        }
                        else
                        {
                            invoice.RecordId = await SetRecordId(1);
                        }
                        if (invoice.InvoiceAdditionalDetails is not null)
                        {
                            invoice.InvoiceAdditionalDetails.CreatedDate = DateTime.UtcNow;
                            invoice.InvoiceAdditionalDetails.OrgId = orgId;
                            invoice.InvoiceAdditionalDetails.ModifiedBy = loggedInUser;
                            invoice.OrgId = orgId;
                        }
                        if (invoice.InvoiceEpisodeItemAssocs is not null)
                        {

                            invoice.InvoiceEpisodeItemAssocs.ToList().ForEach(item =>
                            {
                                item.Notes = !string.IsNullOrWhiteSpace(item.Notes) ? item.Notes.Trim() : string.Empty;
                                if (item.EpisodeTypeId != (short)EpisodeTypes.MBS && item.EpisodeTypeId != (short)EpisodeTypes.Accommodation)
                                {
                                    item.Medicare = default(decimal);
                                    item.Fund = default(decimal);
                                    item.Rebate = default(decimal);
                                }
                                invoice.TotalAmount += item.Fee;
                                //saving just the fee exc gst component
                                item.Fee = CommonFinanceCalculator.Rounding((decimal)(item.Fee - item.Gst), 2);

                                item.CreatedDate = DateTime.UtcNow;
                                item.CreatedBy = loggedInUser;
                                item.OrgId = orgId;
                                item.ModifiedBy = loggedInUser;
                                item.StatusId = (short)Status.Active;
                                item.AdjustmentTypeId = null;
                                item.InvoiceEpisodeItemAssocsId = null;

                                if (item.InvoiceEpisodeItemsIndicatorAssocs is not null)
                                {
                                    item.InvoiceEpisodeItemsIndicatorAssocs.ToList().ForEach(indicator =>
                                    {
                                        indicator.CreatedDate = DateTime.UtcNow;
                                        indicator.CreatedBy = loggedInUser;
                                        indicator.OrgId = orgId;
                                        indicator.ModifiedBy = loggedInUser;
                                        indicator.StatusId = (short)Status.Active;
                                    });
                                }
                            });
                        }
                        invoice.Owing = invoice.TotalAmount - invoice.Deposit;
                        //});
                    }
                }
                if (invoiceSummary.FinanceTypeId == (short)FinanceType.Estimate && invoiceSummary.InvoiceTypeId == (short)InvoiceType.Estimate)
                {
                    short estimateValidity = await FetchEstimateValidity(baseHttpRequestContext);
                    using (var transaction = new TransactionScope(TransactionScopeOption.Required, TransactionScopeAsyncFlowOption.Enabled))
                    {
                        invoiceSummary = await _invoiceDAL.AddInvoiceSummary(invoiceSummary);
                        summaryId = invoiceSummary.Id;
                        if (summaryId > default(short))
                        {
                            InvoiceSummaryExpiryDetail invoiceSummaryExpiryDetail = new()
                            {
                                InvoiceSummaryId = summaryId,
                                OrgId = orgId,
                                ModifiedBy = loggedInUser,
                                CreatedDate = DateTime.UtcNow,
                                ExpiryDate = DateTime.UtcNow.AddDays(estimateValidity)
                            };
                            await _invoiceDAL.AddInvoiceSummaryExpiryDetail(invoiceSummaryExpiryDetail);
                            transaction.Complete();

                        }
                    }
                    List<long> invoiceDetailIds = new List<long>();
                    invoiceSummary.InvoiceDetails.ToList().ForEach(d =>
                    {
                        invoiceDetailIds.Add(d.Id);
                    });
                    await StoreInvoiceDetailMessage(baseHttpRequestContext.OrgCode, invoiceDetailIds, DateTime.Now, (int)InvoiceRequestType.InvoiceAdded, appointmentDetailsId);

                }
                else
                {

                    invoiceSummary = await _invoiceDAL.AddInvoiceSummary(invoiceSummary);
                    summaryId = invoiceSummary.Id;
                    List<long> invoiceDetailIds = new List<long>();
                    invoiceSummary.InvoiceDetails.ToList().ForEach(d =>
                    {
                        invoiceDetailIds.Add(d.Id);
                    });
                    await StoreInvoiceDetailMessage(baseHttpRequestContext.OrgCode, invoiceDetailIds, DateTime.Now, (int)InvoiceRequestType.InvoiceAdded, appointmentDetailsId);
                }
                await StoreInvoiceSummaryMessage(baseHttpRequestContext.OrgCode, summaryId, (int)InvoiceRequestType.InvoiceSummaryStatus);

                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Result = summaryId;
                apiResponse.Message = "Success";
                return apiResponse;
            }


            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            apiResponse.Result = default(long);
            apiResponse.Message = "Failure";
            apiResponse.Errors.Add("New Invoice Summary cannot be added at this time.");
            return apiResponse;
        }
        private async Task<short> FetchEstimateValidity(BaseHttpRequestContext baseHttpRequestContext)
        {

            ApiResponse<short> apiResponseCompany = new();
            string endpoint = string.Format("/company/Organisation/{0}/estimate_validity", baseHttpRequestContext.OrgId);
            string companySeviceUrl = _appSettings.ApiUrls["CompanyServiceUrl"] + endpoint;
            RestClient restClientCompany = new RestClient(companySeviceUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            apiResponseCompany = await restClientCompany.GetAsync<ApiResponse<short>>(companySeviceUrl);
            return (apiResponseCompany == null) ? default(short) : apiResponseCompany.Result;

        }
        /// <summary>
        /// Method to fetch an invoice summary and all its invoices
        /// </summary>
        /// <param name="id"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<InvoiceSummaryView>> GetInvoiceSummary(long id, BaseHttpRequestContext baseHttpRequestContext)
        {
            int orgId = baseHttpRequestContext.OrgId;
            ApiResponse<InvoiceSummaryView> apiResponse = new();
            InvoiceSummary invoiceSummary = await _invoiceDAL.GetInvoiceSummaryWithInvoices(id, orgId);
            InvoiceSummaryView invoiceSummaryView = _mapper.Map<InvoiceSummary, InvoiceSummaryView>(invoiceSummary);
            if (invoiceSummaryView is null)
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = null;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("Invoice not found.");
            }
            else
            {
                if (invoiceSummaryView.InvoiceDetails is not null)
                {
                    List<long> mbsItemNumbers = new();
                    List<ListMbsData> lstMbsData = new();
                    invoiceSummaryView.InvoiceDetails?.ToList().ForEach(x =>
                       mbsItemNumbers.AddRange(x.InvoiceEpisodeItemAssocs.Where(x => x.EpisodeTypeId == (short)EpisodeTypes.MBS && x.AdjustmentTypeId == null && x.ItemNumber > default(long)).Select(x => x.ItemNumber).ToList()));
                    if (mbsItemNumbers is not null && mbsItemNumbers.Any())
                        lstMbsData = await FetchMBSItemDetails(mbsItemNumbers, baseHttpRequestContext);

                    List<long> customItemIds = new();
                    List<ListEpisodeItemDetail> lstCustomData = new();
                    invoiceSummaryView.InvoiceDetails?.ToList().ForEach(x =>
                       customItemIds.AddRange(x.InvoiceEpisodeItemAssocs.Where(x => x.EpisodeTypeId != (short)EpisodeTypes.MBS && x.AdjustmentTypeId == null && x.ItemNumber > default(long)).Select(x => x.ItemDetailsId).ToList()));
                    if (customItemIds is not null && customItemIds.Any())
                        lstCustomData = await FetchCustomItemDetails(customItemIds, baseHttpRequestContext);


                    List<InvoiceDetailView> lstInvoice = invoiceSummaryView.InvoiceDetails.ToList();
                    List<long> lstAppointmentIds = lstInvoice.Where(x => x.AppointmentDetailsId != null && x.AppointmentDetailsId > 0).Select(x => (long)x.AppointmentDetailsId).ToList();
                    List<long> lstProviderIds = lstInvoice.Where(x => x.ProviderId != null && x.ProviderId > 0).Select(x => (long)x.ProviderId).ToList();
                    lstProviderIds.AddRange(lstInvoice.Where(x => x.PayeeProviderId != null && x.PayeeProviderId > 0).Select(x => (long)x.PayeeProviderId).ToList());
                    //List<long> lstCustomItemIds = new();
                    //var lstDictionaryCustomItems = new List<KeyValuePair<long, long>>();
                    List<UserInfoView> lstProviderDetails = await FetchProviderDetails(lstProviderIds, baseHttpRequestContext);
                    if (lstProviderDetails is not null && lstProviderDetails.Any())
                    {
                        lstInvoice.Where(invoice => invoice.ProviderId != null && lstProviderIds.Contains((long)invoice.ProviderId)).ToList()
                            .ForEach(x =>
                            {
                                x.ProviderDetails = lstProviderDetails.Where(a => a.Id == x.ProviderId).FirstOrDefault();
                            });
                        lstInvoice.Where(invoice => invoice.PayeeProviderId != null && lstProviderIds.Contains((long)invoice.PayeeProviderId)).ToList()
                            .ForEach(x =>
                            {
                                x.PayeeProviderDetails = lstProviderDetails.Where(a => a.Id == x.PayeeProviderId).FirstOrDefault();
                            });
                    }
                    if (lstAppointmentIds is not null && lstAppointmentIds.Count > 0)
                    {
                        List<AppointmentView> lstAppointmentDetails = await FetchAppointmentDetailsWithReferrals(lstAppointmentIds, baseHttpRequestContext);
                        if (lstAppointmentDetails is not null && lstAppointmentDetails.Any())
                        {
                            lstInvoice.Where(invoice => invoice.AppointmentDetailsId != null && lstAppointmentIds.Contains((long)invoice.AppointmentDetailsId)).ToList()
                                .ForEach(x =>
                                {
                                    x.AppointmentDetails = lstAppointmentDetails.Where(a => a.Id == x.AppointmentDetailsId).FirstOrDefault();

                                    //if (appDetails is not null)
                                    //{
                                    //    x.AppointmentDetails = _mapper.Map<AppointmentList, AppointmentView>(appDetails);
                                    //}
                                });
                        }

                    }
                    foreach (InvoiceDetailView invoice in lstInvoice)
                    {       //will be done by frontend
                        if (invoice.InvoiceEpisodeItemAssocs is not null)
                        {
                            List<InvoiceEpisodeItemViewAssoc> lstItems = invoice.InvoiceEpisodeItemAssocs.ToList();
                            if (lstItems is not null && lstItems.Count > 0)
                            {

                                //List<long> lstMbsItems = lstItems.Where(x => x.AdjustmentTypeId == null && x.ItemNumber > default(long) && x.EpisodeTypeId == (short)EpisodeTypes.MBS).Select(x => x.ItemNumber).ToList();
                                //List<ListMbsData> lstMbsData = await FetchMBSItemDetails(lstMbsItems, baseHttpRequestContext);
                                lstItems.ForEach(item =>
                                {
                                    if (item.InvoiceEpisodeItemAssocsId is not null && item.InvoiceEpisodeItemAssocsId > 0)
                                    {
                                        InvoiceEpisodeItemViewAssoc parentEntry = lstItems.Where(x => x.Id == item.InvoiceEpisodeItemAssocsId).FirstOrDefault();
                                        if (parentEntry is not null)
                                        {
                                            parentEntry.Quantity -= item.Quantity;
                                            parentEntry.Fee -= item.Fee;
                                            parentEntry.Oop -= item.Oop;
                                            parentEntry.Gst -= item.Gst;
                                        }

                                    }
                                    if (item.EpisodeTypeId == (short)EpisodeTypes.MBS && item.ItemNumber > default(long) && lstMbsData is not null && lstMbsData.Any())
                                    {
                                        ListMbsData mbsData = lstMbsData.Where(x => x.ItemNum == item.ItemNumber).FirstOrDefault();

                                        ListEpisodeItemDetail episodeItemDetail = _mapper.Map<ListMbsData, ListEpisodeItemDetail>(mbsData);
                                        item.EpisodeItemDetail = episodeItemDetail;
                                    }
                                    if (item.EpisodeTypeId != (short)EpisodeTypes.MBS && item.ItemDetailsId > default(long) && lstCustomData is not null && lstCustomData.Any())
                                    {
                                        item.EpisodeItemDetail = lstCustomData.Where(x => x.Id == item.ItemDetailsId).FirstOrDefault();
                                    }
                                    if (item.InvoiceEpisodeItemsIndicatorAssocs is not null && item.InvoiceEpisodeItemsIndicatorAssocs.Count > 0)
                                    {

                                        item.InvoiceEpisodeItemsIndicatorAssocs = item.InvoiceEpisodeItemsIndicatorAssocs.Where(x => x.StatusId == (short)Status.Active).ToList();
                                        if (item.InvoiceEpisodeItemsIndicatorAssocs is not null && item.InvoiceEpisodeItemsIndicatorAssocs.Any())
                                        {
                                            item.InvoiceEpisodeItemsIndicatorAssocs.ToList().ForEach(indicator =>
                                            {
                                                indicator.InvoiceItemIndicatorType = (indicator.InvoiceItemIndicatorTypeId is null) ? string.Empty : EnumExtensions.GetDescription((InvoiceItemIndicatorType)indicator.InvoiceItemIndicatorTypeId);
                                            });
                                        }
                                    }

                                });
                                lstItems.RemoveAll(x => (x.InvoiceEpisodeItemAssocsId != null && x.StatusId == (short)Status.Active && x.AdjustmentTypeId != null && x.AdjustmentTypeId == (short)AdjustmentType.Refund) || x.Quantity == 0);
                                invoice.InvoiceEpisodeItemAssocs = lstItems;
                            }
                        }

                        //    lstDictionaryCustomItems = lstItems.Where(item => item.EpisodeTypeId != (short)EpisodeTypes.MBS).Select(x => new KeyValuePair<long, long>(x.Id, x.ItemDetailsId)).ToList();
                        //    if (lstDictionaryCustomItems is not null && lstDictionaryCustomItems.Any())
                        //    {
                        //        List<ListEpisodeItemDetail> lstCustomItemInfo = await FetchCustomItemDetails(lstDictionaryCustomItems.Select(x => x.Value).ToList(), baseHttpRequestContext);
                        //        if(lstCustomItemInfo is not null && lstCustomItemInfo.Count > 0)
                        //        {
                        //            lstItems.Where(item => lstDictionaryCustomItems.Select(kv => kv.Key).ToList().Contains(item.Id)).ToList()
                        //                .ForEach(customItem =>
                        //                {
                        //                    customItem.ItemDescription = lstCustomItemInfo.Where(x => x.Id == customItem.ItemDetailsId).Select(x => x.Description).FirstOrDefault();
                        //                });

                        //        }

                        //    }

                        //}
                        //Fetching the userinfo 

                        //if (invoice.ProviderId!=null && invoice.ProviderId > default(long))
                        //    {
                        //    ApiResponse<UserInfoView> apiResponseUser = await FetchUserInfo(baseHttpRequestContext, (long)invoice.ProviderId);
                        //    if (apiResponseUser is not null && apiResponseUser.StatusCode == StatusCodes.Status200OK)
                        //    {
                        //        invoice.ProviderDetails = apiResponseUser.Result;
                        //    }
                        //}
                        //Fetching the userinfo 

                        if (invoice.CompanyDetailsId != null && invoice.CompanyDetailsId > default(int))
                        {
                            //invoice.CompanyName =  await FetchCompanyDetails(baseHttpRequestContext, invoice.CompanyDetailsId);

                        }
                    };
                }

                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";
                apiResponse.Result = invoiceSummaryView;
            }

            return apiResponse;
        }

        public async Task<List<UserInfoView>> FetchProviderDetails(List<long> lstProviderIds, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<List<UserInfoView>> apiResponseUsers = new();
            int ps = lstProviderIds.Count();
            string filter = "{UserId:[" + string.Join(",", lstProviderIds) + "]}";
            string encodedFilter = Base64EncoderHelper.EncodeQueryModelFilterParam(filter);
            // string param = "Ids=" + string.Join(",", lstProviderIds) ;
            string userSeviceUrl = _appSettings.ApiUrls["UserServiceUrl"] + "/user/user_detailinfo/usercompanyassocs?pn=1&ps=" + ps + "&f=" + encodedFilter;

            RestClient restClientUser = new RestClient(userSeviceUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            apiResponseUsers = await restClientUser.GetAsync<ApiResponse<List<UserInfoView>>>(userSeviceUrl);
            if (apiResponseUsers.StatusCode == StatusCodes.Status200OK && apiResponseUsers.Result is not null)
            {
                return apiResponseUsers.Result;

            }
            return null;
        }

        public async Task<List<AppointmentList>> FetchAppointmentDetails(List<long> lstAppointmentIds, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<QueryResultList<AppointmentList>> apiResponseAppointments = new();
            int ps = lstAppointmentIds.Count();
            string filter = "{AppointmentId:[" + string.Join(",", lstAppointmentIds) + "]}";
            string encodedFilter = Base64EncoderHelper.EncodeQueryModelFilterParam(filter);
            string appointmentSeviceUrl = _appSettings.ApiUrls["AppointmentServiceUrl"] + "/appointment/appointment_details?pn=1&ps=" + ps + "&f=" + encodedFilter;

            RestClient restClientAppointment = new RestClient(appointmentSeviceUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            apiResponseAppointments = await restClientAppointment.GetAsync<ApiResponse<QueryResultList<AppointmentList>>>(appointmentSeviceUrl);
            if (apiResponseAppointments.StatusCode == StatusCodes.Status200OK && apiResponseAppointments.Result is not null && apiResponseAppointments.Result.CurrentCount > 0)
            {
                return apiResponseAppointments.Result.ItemRecords.ToList();

            }
            return null;
        }
        public async Task<List<AppointmentView>> FetchAppointmentDetailsWithReferrals(List<long> lstAppointmentIds, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<QueryResultList<AppointmentView>> apiResponseAppointments = new();
            int ps = lstAppointmentIds.Count();
            string filter = "{AppointmentId:[" + string.Join(",", lstAppointmentIds) + "]}";
            string encodedFilter = Base64EncoderHelper.EncodeQueryModelFilterParam(filter);
            string appointmentSeviceUrl = _appSettings.ApiUrls["AppointmentServiceUrl"] + "/appointment/appointment_details_info_referral?pn=1&ps=" + ps + "&f=" + encodedFilter;

            RestClient restClientAppointment = new RestClient(appointmentSeviceUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            apiResponseAppointments = await restClientAppointment.GetAsync<ApiResponse<QueryResultList<AppointmentView>>>(appointmentSeviceUrl);
            if (apiResponseAppointments.StatusCode == StatusCodes.Status200OK && apiResponseAppointments.Result is not null && apiResponseAppointments.Result.CurrentCount > 0)
            {
                return apiResponseAppointments.Result.ItemRecords.ToList();

            }
            return null;
        }
        //private async Task<string> FetchCompanyDetails(BaseHttpRequestContext baseHttpRequestContext, int? companyDetailsId)
        //{
        //    ApiResponse<InputGetCompany> apiResponseCompany = new();

        //    string companySeviceUrl = _appSettings.ApiUrls["CompanyServiceUrl"] + "/company/company_details/" + companyDetailsId;
        //    RestClient restClientCompany = new RestClient(companySeviceUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);

        //    return await restClientUser.GetAsync<ApiResponse<UserInfoView>>(companySeviceUrl);
        //}

        private async Task<List<ListEpisodeItemDetail>> FetchCustomItemDetails(List<long> listItemIds, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<QueryResultList<ListEpisodeItemDetail>> apiResponseItems = new();
            int ps = listItemIds.Count();
            string filter = "{ EpisodeTypeId:[],SearchAll: true,EpisodeCateogoriesIdLvl2:[],EpisodeCateogoriesIdLvl1:[],EpisodeCateogoriesIdLvl3:[],ItemId:[" + string.Join(",", listItemIds) + "]}";
            string encodedFilter = Base64EncoderHelper.EncodeQueryModelFilterParam(filter);//Base64EncoderHelper.EncodeBase64(filter).Replace("+", "-").Replace("/", "_");
            string utilityServiceUrl = _appSettings.ApiUrls["UtilityServiceUrl"] + "/utility/episode_itemdetails?pn=1&ps=" + ps + "&f=" + encodedFilter;
            RestClient restClientUtlity = new RestClient(utilityServiceUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            apiResponseItems = await restClientUtlity.GetAsync<ApiResponse<QueryResultList<ListEpisodeItemDetail>>>(utilityServiceUrl);
            if (apiResponseItems != null && apiResponseItems.StatusCode == StatusCodes.Status200OK && apiResponseItems.Result is not null && apiResponseItems.Result.CurrentCount > 0)
            {
                return apiResponseItems.Result.ItemRecords.ToList();

            }
            return null;
        }

        private async Task<List<ListMbsData>> FetchMBSItemDetails(List<long> listItemIds, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<QueryResultList<ListMbsData>> apiResponseItems = new();
            string filter = "{ListMbsItemsIncDel:[" + string.Join(",", listItemIds) + "]}";
            string encodedFilter = Base64EncoderHelper.EncodeQueryModelFilterParam(filter);
            string medicalScheduleApiUrl = _appSettings.ApiUrls["MedicalScheduleServiceUrl"] + "/MedicalSchedule/mbs?pn=1&ps=100&soo=Asc&f=" + encodedFilter;
            RestClient restClient = new RestClient(medicalScheduleApiUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            var medicalScheduleMbsApiResponse = await restClient.GetAsync<ApiResponse<QueryResultList<ListMbsData>>>(medicalScheduleApiUrl);
            if (medicalScheduleMbsApiResponse != null && medicalScheduleMbsApiResponse.StatusCode == StatusCodes.Status200OK && medicalScheduleMbsApiResponse.Result is not null && medicalScheduleMbsApiResponse.Result.CurrentCount > 0)
            {
                return medicalScheduleMbsApiResponse.Result.ItemRecords.ToList();
            }
            return null;
        }
        private async Task<List<MedicalContractorBandAssocInfo>> FetchAccommodationItemDetails(List<long> listItemIds, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<QueryResultList<ListMbsData>> apiResponseItems = new();
            string Ids = string.Join(",", listItemIds);
            string utilityApiUrl = _appSettings.ApiUrls["UtilityServiceUrl"] + "/utility/medical_contractor/medicalcontractorbandassocsinfo?Ids=" + Ids;
            RestClient restClient = new RestClient(utilityApiUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            var utilityServiceApiResponse = await restClient.GetAsync<ApiResponse<List<MedicalContractorBandAssocInfo>>>(utilityApiUrl);
            if (utilityServiceApiResponse != null && utilityServiceApiResponse.StatusCode == StatusCodes.Status200OK && utilityServiceApiResponse.Result is not null && utilityServiceApiResponse.Result.Count > 0)
            {
                return utilityServiceApiResponse.Result;
            }
            return null;
        }

        /// <summary>
        /// Method to fetch UserInfo
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="userDetailsId"></param>
        /// <returns></returns>
        //private async Task<ApiResponse<UserInfoView>> FetchUserInfo(BaseHttpRequestContext baseHttpRequestContext, long userDetailsId)
        //{
        //    string userSeviceUrl = _appSettings.ApiUrls["UserServiceUrl"] + "/user/user_detailinfo/" + userDetailsId;
        //    RestClient restClientUser = new RestClient(userSeviceUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
        //    return await restClientUser.GetAsync<ApiResponse<UserInfoView>>(userSeviceUrl);

        //}
        /// <summary>
        /// Method to update 
        /// </summary>
        /// <param name="id"></param>
        /// <param name="invoiceSummary"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> EditInvoiceSummary(long id, InvoiceSummaryInput invoiceSummaryInput, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<long?> apiResponse = new();

            int orgId = baseHttpRequestContext.OrgId;
            InvoiceSummary invoiceSummaryDB = await _invoiceDAL.GetInvoiceSummaryWithInvoices(id, orgId);
            if (invoiceSummaryDB is not null && invoiceSummaryInput is not null)
            {
                if (invoiceSummaryInput.ChangeFlag == "U")
                {
                    long? appointmentDetailsId = null;
                    List<InvoiceDetail> lstInvoiceDB = (invoiceSummaryDB.InvoiceDetails is null) ? null : invoiceSummaryDB.InvoiceDetails.ToList();

                    if (invoiceSummaryInput.InvoiceDetails?.FirstOrDefault(x => x.AppointmentDetailsId != null) != null)
                        appointmentDetailsId = invoiceSummaryInput.InvoiceDetails?.FirstOrDefault(x => x.AppointmentDetailsId != null)?.AppointmentDetailsId;
                    else if (lstInvoiceDB != null)
                        appointmentDetailsId = lstInvoiceDB.FirstOrDefault(x => x.AppointmentDetailsId != null)?.AppointmentDetailsId;

                    List<InvoiceDetailInput> lstInvoiceInput = (invoiceSummaryInput.InvoiceDetails is null) ? null : invoiceSummaryInput.InvoiceDetails.ToList();
                    invoiceSummaryDB.InvoiceDetails = null;
                    InvoiceSummary invoiceSummary = _mapper.Map<InvoiceSummaryInput, InvoiceSummary>(invoiceSummaryInput);
                    short financeTypeId = invoiceSummary.FinanceTypeId;
                    lstInvoiceInput.ForEach(x =>
                    {
                        x.InvoiceSummaryId = id;
                        if (x.MsrId is null || x.MsrId == default(short))
                        {
                            x.MsrId = (short)MSR.MSR_Off;
                        }
                        //x.Deposit = x.Deposit is null ? 0 : x.Deposit;
                        //x.TotalAmount = x.TotalAmount is null ? 0 : x.TotalAmount;
                        //x.Owing = x.Owing is null ? 0 : x.Owing;

                        if (financeTypeId == (short)FinanceType.Template)
                        {
                            x.AppointmentDetailsId = null;
                            x.DateOfService = null;
                            x.IsServiceDate = false;
                            x.InvoiceTypeId = (short)InvoiceType.Estimate;
                            x.InvoiceStatusId = (short)InvoiceStatus.Valid;
                        }

                    });
                    invoiceSummary.InvoiceDetails = await EditInvoiceDetails(lstInvoiceDB, lstInvoiceInput, baseHttpRequestContext);
                    invoiceSummary.OrgId = baseHttpRequestContext.OrgId;
                    invoiceSummary.CreatedDate = invoiceSummaryDB.CreatedDate;
                    invoiceSummary.ModifiedBy = baseHttpRequestContext.UserId;
                    invoiceSummary.ModifiedDate = DateTime.UtcNow;
                    invoiceSummary.RecordId = invoiceSummaryDB.RecordId;
                    invoiceSummary.FinanceTypeId = invoiceSummaryDB.FinanceTypeId;
                    if (invoiceSummary.InvoiceTypeId == (short)InvoiceType.Estimate && lstInvoiceInput.Where(x => x.InvoiceTypeId == (short)InvoiceType.Invoice).Any())
                    {
                        invoiceSummary.InvoiceTypeId = (short)InvoiceType.Invoice;
                    }
                    if (invoiceSummary.FinanceTypeId == default(short))
                    {
                        invoiceSummary.FinanceTypeId = (short)FinanceType.Estimate;
                    }

                    using (var transaction = new TransactionScope(TransactionScopeOption.Required, TransactionScopeAsyncFlowOption.Enabled))
                    {
                        invoiceSummary = await _invoiceDAL.UpdateInvoiceSummary(invoiceSummary);
                        int rows = invoiceSummary.AffectedRows;
                        if (rows > 0)
                        {
                            if (invoiceSummary.FinanceTypeId != (short)FinanceType.Template && invoiceSummary.InvoiceStatusId == (short)InvoiceStatus.Valid && invoiceSummary.InvoiceTypeId == (short)InvoiceType.Estimate && lstInvoiceInput.Where(x => x.InvoiceTypeId != (short)InvoiceType.Estimate).Any())
                            {
                                await _invoiceDAL.DeleteInvoiceSummaryExpiryDetail(invoiceSummary.Id, baseHttpRequestContext.OrgId);
                            }
                            transaction.Complete();
                            //apiResponse.StatusCode = StatusCodes.Status200OK;
                            //apiResponse.Result = id;
                            //apiResponse.Message = "Success";
                            //return apiResponse;

                        }
                    }
                    List<long> invoiceDetailIds = new List<long>();
                    invoiceSummary.InvoiceDetails.ToList().ForEach(d =>
                    {
                        invoiceDetailIds.Add(d.Id);
                    });

                    await StoreInvoiceSummaryMessage(baseHttpRequestContext.OrgCode, invoiceSummary.Id, (int)InvoiceRequestType.InvoiceSummaryStatus);

                    await StoreInvoiceDetailMessage(baseHttpRequestContext.OrgCode, invoiceDetailIds, DateTime.Now, (int)InvoiceRequestType.InvoiceUpdated, appointmentDetailsId);

                }
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Result = id;
                apiResponse.Message = "Success";
                return apiResponse;

            }
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            apiResponse.Result = null;
            apiResponse.Message = "Failure";
            apiResponse.Errors.Add("Invoice cannot be updated at this time.");
            return apiResponse;
        }

        private async Task<List<InvoiceDetail>> EditInvoiceDetails(List<InvoiceDetail> lstInvoiceDB, List<InvoiceDetailInput> lstInvoiceInput, BaseHttpRequestContext baseHttpRequestContext, bool isTemplate = false)
        {
            List<InvoiceDetail> lstInvoiceAddUpd = new();
            foreach (var invoiceInput in lstInvoiceInput)
            {
                if (invoiceInput.InvoiceTypeId == (short)InvoiceType.Invoice)
                {
                    invoiceInput.Deposit = 0;
                }

                if (invoiceInput.Id > 0)
                {
                    InvoiceDetail invoiceDB = lstInvoiceDB.Find(x => x.Id == invoiceInput.Id);

                    if (invoiceInput.ChangeFlag == "U")
                    {
                        InvoiceDetail invoice = _mapper.Map<InvoiceDetailInput, InvoiceDetail>(invoiceInput);
                        invoice.ModifiedBy = baseHttpRequestContext.UserId;
                        invoice.OrgId = baseHttpRequestContext.OrgId;
                        invoice.CreatedDate = invoiceDB.CreatedDate;
                        invoice.RecordId = invoiceDB.RecordId;
                        invoice.ModifiedDate = DateTime.UtcNow;
                        invoice.Deposit = (invoiceDB.Deposit is null) ? 0 : invoiceDB.Deposit;
                        invoice.DateInvoiced = invoiceDB.DateInvoiced;
                        List<InvoiceEpisodeItemAssoc> lstItemDB = invoiceDB.InvoiceEpisodeItemAssocs is null ? null : invoiceDB.InvoiceEpisodeItemAssocs.ToList();
                        List<InvoiceEpisodeItemInputAssoc> lstItemInput = invoiceInput.InvoiceEpisodeItemAssocs is null ? null : invoiceInput.InvoiceEpisodeItemAssocs.ToList();
                        decimal? totalAmountDB = invoiceDB.TotalAmount is null ? 0 : invoiceDB.TotalAmount;
                        decimal? owingDB = invoiceDB.Owing is null ? 0 : invoiceDB.Owing;

                        lstItemInput.ForEach(x =>
                        {
                            x.InvoiceDetailsId = invoiceInput.Id;
                            x.Notes = !string.IsNullOrWhiteSpace(x.Notes) ? x.Notes.Trim() : string.Empty;
                            //saving just the fee exc gst component
                            x.Fee = CommonFinanceCalculator.Rounding((decimal)(x.Fee - x.Gst), 2);
                        });
                        List<KeyValuePair<long, decimal?>> lstItemFeeDB = lstItemDB.Where(x => x.StatusId == (short)Status.Active && (x.AdjustmentTypeId == null || x.AdjustmentTypeId != (short)AdjustmentType.Refund)).Select(x => new KeyValuePair<long, decimal?>(x.Id, x.Fee + x.Gst)).ToList();
                        lstItemFeeDB.AddRange(lstItemDB.Where(x => x.StatusId == (short)Status.Active && (x.AdjustmentTypeId != null && x.AdjustmentTypeId == (short)AdjustmentType.Refund)).Select(x => new KeyValuePair<long, decimal?>(x.Id, (x.Fee + x.Gst) * -1)).ToList());
                        if (lstItemInput != null && lstItemInput.Count > 0)
                            invoice.InvoiceEpisodeItemAssocs = EditInvoiceItems(lstItemDB, lstItemInput, baseHttpRequestContext);
                        if (invoice.InvoiceEpisodeItemAssocs is not null && invoice.InvoiceEpisodeItemAssocs.Count > 0)
                        {
                            invoice.TotalAmount = ComputeTotalAmount(invoice.InvoiceEpisodeItemAssocs.ToList(), lstItemFeeDB);
                            invoice.Owing = ComputeOwingAmount(invoice.TotalAmount, totalAmountDB, owingDB);
                        }
                        else
                        {
                            invoice.Deposit = (invoiceDB.Deposit is null) ? 0 : invoiceDB.Deposit;
                            invoice.TotalAmount = (invoiceDB.TotalAmount is null) ? 0 : invoiceDB.TotalAmount;
                            invoice.Owing = (invoiceDB.Owing is null) ? 0 : invoiceDB.Owing;

                        }
                        //Updating InvoiceAdditional Details
                        if (invoiceInput.InvoiceAdditionalDetails is not null)
                        {
                            invoiceInput.InvoiceAdditionalDetails.InvoiceDetailsId = invoiceInput.Id;
                            InvoiceAdditionalDetail invoiceAdditionalDetailDB = invoiceDB.InvoiceAdditionalDetails is null ? null : invoiceDB.InvoiceAdditionalDetails;
                            invoice.InvoiceAdditionalDetails = EditInvoiceAdditionalDetails(invoiceInput.InvoiceAdditionalDetails, invoiceAdditionalDetailDB, baseHttpRequestContext);

                        }

                        if (invoice.ExcessAmount != null && invoice.ExcessAmount > 0)
                        {
                            if (invoiceDB.ExcessAmount == null || invoiceDB.ExcessAmount == 0)
                            {
                                invoice.ExcessOwing = invoice.ExcessAmount;
                            }
                            else if (invoiceDB.ExcessAmount > invoice.ExcessAmount)
                            {
                                var difference = invoiceDB.ExcessAmount - invoice.ExcessAmount;
                                invoice.ExcessOwing = invoiceDB.ExcessOwing - difference;
                            }
                            else if (invoice.ExcessAmount > invoiceDB.ExcessAmount)
                            {
                                var difference = invoice.ExcessAmount - invoiceDB.ExcessAmount;
                                invoice.ExcessOwing = invoiceDB.ExcessOwing + difference;
                            }
                        }
                        else
                        {

                            if (invoiceDB.ExcessAmount == null || invoiceDB.ExcessAmount == 0)
                            {
                                invoice.ExcessOwing = invoice.ExcessAmount;
                            }
                            else
                            {
                                var difference = invoiceDB.ExcessAmount;
                                invoice.ExcessOwing = invoiceDB.ExcessOwing - difference;
                            }
                        }

                        if (invoice.InvoiceTypeId == (short)InvoiceType.Invoice)
                        {
                            invoice.InvoiceStatusId = (invoice.Owing > 0) ? (short)InvoiceStatus.Unpaid : (short)InvoiceStatus.Paid;
                            if (invoiceDB.InvoiceTypeId == (short)InvoiceType.Estimate)
                                invoice.DateInvoiced = DateTime.UtcNow;
                        }
                        else if (invoice.InvoiceTypeId == (short)InvoiceType.Estimate)
                        {
                            invoice.InvoiceStatusId = (invoice.Deposit > 0) ? (short)InvoiceStatus.Deposit : (short)InvoiceStatus.Valid;
                        }
                        lstInvoiceAddUpd.Add(invoice);
                    }
                    else if (invoiceInput.ChangeFlag == "D")
                    {
                        InvoiceDetail invoice = _mapper.Map<InvoiceDetailInput, InvoiceDetail>(invoiceInput);
                        invoice.ModifiedBy = baseHttpRequestContext.UserId;
                        invoice.OrgId = baseHttpRequestContext.OrgId;
                        invoice.CreatedDate = invoiceDB.CreatedDate;
                        invoice.RecordId = invoiceDB.RecordId;
                        invoice.ModifiedDate = DateTime.UtcNow;
                        invoice.InvoiceStatusId = (short)InvoiceStatus.Void;
                        invoice.Deposit = (invoiceDB.Deposit is null) ? 0 : invoiceDB.Deposit;
                        invoice.TotalAmount = (invoiceDB.TotalAmount is null) ? 0 : invoiceDB.TotalAmount;
                        invoice.Owing = (invoiceDB.Owing is null) ? 0 : invoiceDB.Owing;
                        invoice.DateInvoiced = invoiceDB.DateInvoiced;

                        invoice.InvoiceEpisodeItemAssocs.ToList().ForEach(x =>
                        {
                            x.InvoiceDetailsId = invoiceInput.Id;
                            x.Fee = CommonFinanceCalculator.Rounding((decimal)(x.Fee - x.Gst), 2);
                            x.StatusId = (short)Status.Active;
                            x.ModifiedBy = baseHttpRequestContext.UserId;
                            x.ModifiedDate = DateTime.UtcNow;
                            x.OrgId = baseHttpRequestContext.OrgId;
                            x.InvoiceEpisodeItemsIndicatorAssocs = null;
                        });
                        invoice.InvoiceAdditionalDetails = null;
                        lstInvoiceAddUpd.Add(invoice);

                    }
                    lstInvoiceDB.Remove(invoiceDB);

                }
                else if (invoiceInput.ChangeFlag == "C")
                {

                    InvoiceDetail invoice = _mapper.Map<InvoiceDetailInput, InvoiceDetail>(invoiceInput);
                    invoice.ModifiedBy = baseHttpRequestContext.UserId;
                    invoice.OrgId = baseHttpRequestContext.OrgId;
                    invoice.CreatedDate = DateTime.UtcNow;
                    invoice.TotalAmount = (invoice.TotalAmount is null) ? 0 : invoice.TotalAmount;
                    invoice.Owing = (invoice.Owing is null) ? 0 : invoice.Owing;
                    invoice.Deposit = (invoice.Deposit is null) ? 0 : invoice.Deposit;
                    invoice.ExcessAmount = (invoice.ExcessAmount is null) ? default(decimal) : invoice.ExcessAmount;
                    invoice.ExcessOwing = (invoice.ExcessAmount is null) ? default(decimal) : invoice.ExcessAmount;


                    if (isTemplate)
                    {
                        invoice.RecordId = null;
                    }
                    else
                        invoice.RecordId = await SetRecordId(1);
                    List<InvoiceEpisodeItemInputAssoc> lstItemInput = invoiceInput.InvoiceEpisodeItemAssocs is null ? null : invoiceInput.InvoiceEpisodeItemAssocs.ToList();
                    lstItemInput.ForEach(x =>
                    {
                        x.ChangeFlag = "C";
                        invoice.TotalAmount += x.Fee;

                        x.Notes = !string.IsNullOrWhiteSpace(x.Notes) ? x.Notes.Trim() : string.Empty;
                        //saving just the fee exc gst component
                        x.Fee = CommonFinanceCalculator.Rounding((decimal)(x.Fee - x.Gst), 2);
                        if (x.InvoiceEpisodeItemsIndicatorAssocs is not null)
                        {
                            x.InvoiceEpisodeItemsIndicatorAssocs.ToList().ForEach(indicator =>
                            {
                                indicator.CreatedDate = DateTime.UtcNow;
                                indicator.CreatedBy = baseHttpRequestContext.UserId;
                                indicator.OrgId = baseHttpRequestContext.OrgId;
                                indicator.ModifiedBy = baseHttpRequestContext.UserId;
                                indicator.StatusId = (short)Status.Active;
                            });
                        }
                    });
                    invoice.InvoiceEpisodeItemAssocs = EditInvoiceItems(null, lstItemInput, baseHttpRequestContext);
                    invoice.Owing = invoice.TotalAmount - invoice.Deposit;
                    if (invoice.InvoiceTypeId == (short)InvoiceType.Invoice)
                    {
                        invoice.DateInvoiced = DateTime.UtcNow;
                        invoice.InvoiceStatusId = (invoice.Owing > 0) ? (short)InvoiceStatus.Unpaid : (short)InvoiceStatus.Paid;
                    }
                    else if (invoice.InvoiceTypeId == (short)InvoiceType.Estimate)
                    {
                        invoice.InvoiceStatusId = (invoice.Deposit > 0) ? (short)InvoiceStatus.Deposit : (short)InvoiceStatus.Valid;
                    }
                    if (invoiceInput.InvoiceAdditionalDetails is not null)
                    {
                        // invoiceInput.InvoiceAdditionalDetails.InvoiceDetailsId = invoiceInput.Id;
                        invoice.InvoiceAdditionalDetails = EditInvoiceAdditionalDetails(invoiceInput.InvoiceAdditionalDetails, null, baseHttpRequestContext);

                    }
                    lstInvoiceAddUpd.Add(invoice);

                }
            }
            if (lstInvoiceDB is not null)
            {
                foreach (var invoice in lstInvoiceDB)
                {
                    invoice.ModifiedBy = baseHttpRequestContext.UserId;
                    invoice.ModifiedDate = DateTime.UtcNow;
                    invoice.InvoiceStatusId = (short)InvoiceStatus.Deleted;
                }
                lstInvoiceAddUpd = lstInvoiceAddUpd.Concat(lstInvoiceDB).ToList();
            }
            return lstInvoiceAddUpd;
        }

        private decimal? ComputeOwingAmount(decimal? totalAmountNew, decimal? totalAmountDB, decimal? owingDB)
        {
            decimal totalAmtDiff = (decimal)(totalAmountNew - totalAmountDB);
            return CommonFinanceCalculator.Rounding((decimal)(owingDB + totalAmtDiff), 2);
        }

        private decimal ComputeTotalAmount(List<InvoiceEpisodeItemAssoc> invoiceEpisodeItemAssocs, List<KeyValuePair<long, decimal?>> lstItemFeeDB)
        {
            decimal totalAmount = 0;
            totalAmount += (decimal)invoiceEpisodeItemAssocs.Where(item => item.Id == 0).Select(x => x.Gst + x.Fee).Sum();
            List<long> itemsIdsUpdated = invoiceEpisodeItemAssocs.Where(x => x.Id > 0).Select(x => x.Id).ToList();
            List<long> itemsIdUnchanged = lstItemFeeDB.Select(x => x.Key).ToList().Except(itemsIdsUpdated).ToList();
            List<long> itemsIdAdjustmnt = lstItemFeeDB.Select(x => x.Key).ToList().Except(itemsIdsUpdated).ToList();

            totalAmount += (decimal)invoiceEpisodeItemAssocs.Where(x => x.Id > 0 && x.StatusId == (short)Status.Active).Select(x => x.Gst + x.Fee).Sum();
            if (itemsIdUnchanged.Count > 0)
                totalAmount += (decimal)lstItemFeeDB.Where(x => itemsIdUnchanged.Contains(x.Key)).Select(x => x.Value).Sum();

            return CommonFinanceCalculator.Rounding(totalAmount, 2);

        }

        private InvoiceAdditionalDetail EditInvoiceAdditionalDetails(InvoiceAdditionalDetailInput invoiceAdditionalDetailInput, InvoiceAdditionalDetail invoiceAdditionalDetailDB, BaseHttpRequestContext baseHttpRequestContext)
        {
            if (invoiceAdditionalDetailInput != null && invoiceAdditionalDetailInput.ChangeFlag == "U" && invoiceAdditionalDetailDB is not null)
            {
                invoiceAdditionalDetailInput.CreatedDate = invoiceAdditionalDetailDB.CreatedDate;
                invoiceAdditionalDetailInput.ModifiedDate = DateTime.UtcNow;
                invoiceAdditionalDetailInput.OrgId = invoiceAdditionalDetailDB.OrgId;
                invoiceAdditionalDetailInput.ModifiedBy = baseHttpRequestContext.UserId;
                invoiceAdditionalDetailInput.InvoiceDetailsId = invoiceAdditionalDetailDB.InvoiceDetailsId;

            }
            else if (invoiceAdditionalDetailInput != null && (invoiceAdditionalDetailInput.ChangeFlag == "C" || invoiceAdditionalDetailDB is null))
            {
                invoiceAdditionalDetailInput.CreatedDate = DateTime.UtcNow;
                invoiceAdditionalDetailInput.ModifiedDate = DateTime.UtcNow;
                invoiceAdditionalDetailInput.OrgId = baseHttpRequestContext.OrgId;
                invoiceAdditionalDetailInput.ModifiedBy = baseHttpRequestContext.UserId;
            }
            else
            {
                return null;
            }
            if (invoiceAdditionalDetailInput is not null)
                return _mapper.Map<InvoiceAdditionalDetailInput, InvoiceAdditionalDetail>(invoiceAdditionalDetailInput);
            return null;
        }

        private ICollection<InvoiceEpisodeItemAssoc> EditInvoiceItems(List<InvoiceEpisodeItemAssoc> lstItemDB, List<InvoiceEpisodeItemInputAssoc> lstItemInput, BaseHttpRequestContext baseHttpRequestContext)
        {
            List<InvoiceEpisodeItemAssoc> lstAddUpdItem = new();
            foreach (var itemInput in lstItemInput)
            {
                if (itemInput.Id > 0)
                {
                    var existingobj = lstItemDB.FirstOrDefault(x => x.Id == itemInput.Id);
                    var adjustmentobj = lstItemDB.FirstOrDefault(x => x.InvoiceEpisodeItemAssocsId != null && x.InvoiceEpisodeItemAssocsId == itemInput.Id);

                    if (itemInput.ChangeFlag == "U")
                    {

                        InvoiceEpisodeItemAssoc item = _mapper.Map<InvoiceEpisodeItemInputAssoc, InvoiceEpisodeItemAssoc>(itemInput);
                        InvoiceEpisodeItemAssoc itemDB = lstItemDB.Find(x => x.Id == itemInput.Id);
                        if (adjustmentobj is not null)
                        {
                            item.Quantity = (short?)(item.Quantity + adjustmentobj.Quantity);
                            item.Oop = item.Oop + adjustmentobj.Oop;
                            item.Gst = item.Gst + adjustmentobj.Gst;
                            item.Fee = item.Fee + adjustmentobj.Fee;


                        }
                        if (item.EpisodeTypeId != (short)EpisodeTypes.MBS && item.EpisodeTypeId != (short)EpisodeTypes.Accommodation)
                        {
                            item.Medicare = default(decimal);
                            item.Fund = default(decimal);
                            item.Rebate = default(decimal);
                        }
                        item.ModifiedBy = baseHttpRequestContext.UserId;
                        item.OrgId = baseHttpRequestContext.OrgId;
                        item.CreatedDate = itemDB.CreatedDate;
                        item.ModifiedDate = DateTime.UtcNow;
                        item.StatusId = (short)Status.Active;
                        item.AdjustmentTypeId = null;
                        item.InvoiceEpisodeItemAssocsId = null;
                        if ((item.InvoiceEpisodeItemsIndicatorAssocs is not null && item.InvoiceEpisodeItemsIndicatorAssocs.Any()) || (itemDB.InvoiceEpisodeItemsIndicatorAssocs is not null && itemDB.InvoiceEpisodeItemsIndicatorAssocs.Any()))
                        {
                            item.InvoiceEpisodeItemsIndicatorAssocs = EditInvoiceItemIndicators(itemDB.InvoiceEpisodeItemsIndicatorAssocs, item.InvoiceEpisodeItemsIndicatorAssocs, baseHttpRequestContext);
                        }

                        lstAddUpdItem.Add(item);
                    }
                    lstItemDB.Remove(existingobj);

                }

                else if (itemInput.ChangeFlag == "C")
                {
                    InvoiceEpisodeItemAssoc item = _mapper.Map<InvoiceEpisodeItemInputAssoc, InvoiceEpisodeItemAssoc>(itemInput);
                    if (item.EpisodeTypeId != (short)EpisodeTypes.MBS && item.EpisodeTypeId != (short)EpisodeTypes.Accommodation)
                    {
                        item.Medicare = default(decimal);
                        item.Fund = default(decimal);
                        item.Rebate = default(decimal);
                    }
                    item.OrgId = baseHttpRequestContext.OrgId;
                    item.CreatedDate = DateTime.UtcNow;
                    item.CreatedBy = baseHttpRequestContext.UserId;
                    item.StatusId = (short)Status.Active;
                    if (item.InvoiceEpisodeItemsIndicatorAssocs is not null)
                    {
                        item.InvoiceEpisodeItemsIndicatorAssocs.ToList().ForEach(indicator =>
                        {
                            indicator.CreatedDate = DateTime.UtcNow;
                            indicator.CreatedBy = baseHttpRequestContext.UserId;
                            indicator.OrgId = baseHttpRequestContext.OrgId;
                            indicator.ModifiedBy = baseHttpRequestContext.UserId;
                            indicator.StatusId = (short)Status.Active;
                        });
                    }
                    lstAddUpdItem.Add(item);

                }

            }
            if (lstItemDB is not null)
            {//Edit or delete of Adjust row in invoiceepisodeitemassoc table should not happen
                lstItemDB.RemoveAll(x => x.AdjustmentTypeId != null);
                foreach (var item in lstItemDB)
                {   //Edit or delete of Adjust row in invoiceepisodeitemassoc table should not happen
                    if (item is not null && item.AdjustmentTypeId == null)
                    {
                        item.ModifiedBy = baseHttpRequestContext.UserId;
                        item.ModifiedDate = DateTime.UtcNow;
                        item.StatusId = (short)Status.Deleted;
                    }

                }
                lstAddUpdItem = lstAddUpdItem.Concat(lstItemDB).ToList();
            }

            return lstAddUpdItem;
        }

        private ICollection<InvoiceEpisodeItemsIndicatorAssoc> EditInvoiceItemIndicators(ICollection<InvoiceEpisodeItemsIndicatorAssoc> invoiceEpisodeItemsIndicatorAssocsDB, ICollection<InvoiceEpisodeItemsIndicatorAssoc> invoiceEpisodeItemsIndicatorAssocsInput, BaseHttpRequestContext baseHttpRequestContext)
        {
            List<InvoiceEpisodeItemsIndicatorAssoc> lstAddUpdItemInd = new();
            InvoiceEpisodeItemsIndicatorAssoc existingObj = null;
            invoiceEpisodeItemsIndicatorAssocsInput.ToList().ForEach(indicator =>
            {

                existingObj = invoiceEpisodeItemsIndicatorAssocsDB.FirstOrDefault(x => x.InvoiceItemIndicatorTypeId == indicator.InvoiceItemIndicatorTypeId);

                if (existingObj != null)
                {
                    if (existingObj.StatusId == (short)Status.Deleted || existingObj.Value != indicator.Value)
                    {
                        indicator.StatusId = (short)Status.Active;
                        indicator.ModifiedBy = baseHttpRequestContext.UserId;
                        indicator.CreatedBy = existingObj.CreatedBy;
                        indicator.CreatedDate = existingObj.CreatedDate;
                        indicator.OrgId = existingObj.OrgId;
                        indicator.Id = existingObj.Id;
                        indicator.ModifiedDate = DateTime.UtcNow;
                        lstAddUpdItemInd.Add(indicator);
                    }

                    invoiceEpisodeItemsIndicatorAssocsDB.Remove(existingObj);
                }
                else
                {
                    indicator.StatusId = (short)Status.Active;
                    indicator.ModifiedBy = baseHttpRequestContext.UserId;
                    indicator.CreatedBy = baseHttpRequestContext.UserId;
                    indicator.CreatedDate = DateTime.UtcNow;
                    indicator.OrgId = baseHttpRequestContext.OrgId;
                    lstAddUpdItemInd.Add(indicator);
                }

            });
            if (invoiceEpisodeItemsIndicatorAssocsDB is not null && invoiceEpisodeItemsIndicatorAssocsDB.Count > 0)
            {
                foreach (var indicator in invoiceEpisodeItemsIndicatorAssocsDB)
                {
                    indicator.ModifiedBy = baseHttpRequestContext.UserId;
                    indicator.ModifiedDate = DateTime.UtcNow;
                    indicator.StatusId = (short)Status.Deleted;
                }
                lstAddUpdItemInd = lstAddUpdItemInd.Concat(invoiceEpisodeItemsIndicatorAssocsDB).ToList();
            }
            return lstAddUpdItemInd;
        }

        /// <summary>
        /// Method to list Estimate summaries of a patient
        /// </summary>
        /// <param name="queryModel"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<QueryResultList<ListInvoiceSummary>>> ListInvoiceSummary(QueryModel queryModel, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<QueryResultList<ListInvoiceSummary>> apiResponse = new();
            int orgId = baseHttpRequestContext.OrgId;
            InvoiceSummaryFilterModel filterModel = PrepareFilterParameters(queryModel.Filter);
            QueryResultList<ListInvoiceSummary> invoiceSummaryQueryList = await _invoiceDAL.ListInvoiceSummary(orgId, queryModel, filterModel);
            List<int?> lstCompanyIds = new();
            if (invoiceSummaryQueryList is not null && invoiceSummaryQueryList.ItemRecords is not null && invoiceSummaryQueryList.ItemRecords.Any())
            {
                List<KeyValuePair<long, long>> kvAppointments = new();
                invoiceSummaryQueryList.ItemRecords.ToList().ForEach(x =>
               {
                   decimal estimated = default(decimal);
                   decimal invoiced = default(decimal); ;
                   decimal receipted = default(decimal);
                   decimal deposited = default(decimal);
                   decimal totalAmt = (decimal)(x.InvoiceDetails?.ToList().Where(x => x.TotalAmount != null).Sum(x => x.TotalAmount).GetValueOrDefault());
                   x.TotalAmount = totalAmt;
                   decimal totalOwing = (decimal)(x.InvoiceDetails?.ToList().Where(x => x.Owing != null).Sum(x => x.Owing).GetValueOrDefault());
                   x.TotalReceipted = totalAmt - totalOwing;
                   var groupByTypeTotAmt = from invoice in x.InvoiceDetails
                                           where invoice.TotalAmount != null && invoice.TotalAmount != 0
                                           group invoice by invoice.InvoiceTypeId into invoiceGroup
                                           select new KeyValuePair<short, decimal>(invoiceGroup.Key, invoiceGroup.Sum(s => (decimal)s.TotalAmount));
                   if (groupByTypeTotAmt is not null)
                   {
                       estimated = groupByTypeTotAmt.Where(x => x.Key == (short)InvoiceType.Estimate).FirstOrDefault().Value;
                       invoiced = groupByTypeTotAmt.Where(x => x.Key == (short)InvoiceType.Invoice).FirstOrDefault().Value;

                   }
                   var groupByTypeOwing = from invoice in x.InvoiceDetails
                                          where invoice.Owing != null && invoice.Owing != 0
                                          group invoice by invoice.InvoiceTypeId into invoiceGroup
                                          select new KeyValuePair<short, decimal>(invoiceGroup.Key, invoiceGroup.Sum(s => (decimal)s.Owing));
                   if (groupByTypeOwing is not null)
                   {
                       deposited = estimated - groupByTypeOwing.Where(x => x.Key == (short)InvoiceType.Estimate).FirstOrDefault().Value;
                       receipted = invoiced - groupByTypeOwing.Where(x => x.Key == (short)InvoiceType.Invoice).FirstOrDefault().Value;

                   }
                   x.Receipted = receipted;
                   x.Deposited = deposited;
                   x.Estimated = estimated;
                   x.Invoiced = invoiced;
                   InvoiceDetail firstActiveChild = x.InvoiceDetails?.ToList().Where(x => x.InvoiceStatusId != (short)InvoiceStatus.Deleted && x.InvoiceStatusId != (short)InvoiceStatus.Void).FirstOrDefault();
                   if (firstActiveChild is not null)
                       x.Number = $"{x.RecordId} - {firstActiveChild.RecordId}";
                   else
                       x.Number = x.RecordId.ToString();
                   if (firstActiveChild != null && firstActiveChild.AppointmentDetailsId is not null)
                   {
                       kvAppointments.Add(new((long)firstActiveChild.AppointmentDetailsId, x.Id));

                   }
                   else
                   {
                       x.DateOfService = firstActiveChild?.DateOfService;
                   }

                   x.EntityIds = x.InvoiceDetails.Where(x => x.CompanyDetailsId != null).Select(x => x.CompanyDetailsId).Distinct().ToList();
                   lstCompanyIds.AddRange(x.EntityIds);
                   // for the entity details

                   // x.EntityDetails = await GetCompanyByIds(x.InvoiceDetails.Where(x => x.CompanyDetailsId != null).Select(x => x.CompanyDetailsId).ToList(), baseHttpRequestContext);
               });
                List<CompanyDetailInfo> companyDetailInfos = await GetCompanyByIds(lstCompanyIds, baseHttpRequestContext);

                List<AppointmentView> appointmentInfos = await GetAppointmentViewByIds(kvAppointments.Select(x => x.Key).ToList(), baseHttpRequestContext);
                if (companyDetailInfos is not null && companyDetailInfos.Any())
                {
                    invoiceSummaryQueryList.ItemRecords.ToList().ForEach(x =>
                    {
                        x.EntityIds?.ForEach(cId =>
                        {
                            CompanyDetailInfo companyDetailInfo = companyDetailInfos.Where(company => company.Id == cId).FirstOrDefault();
                            if (companyDetailInfo is not null) x.EntityDetails.Add(companyDetailInfo);
                        });
                        if (kvAppointments is not null && kvAppointments.Any() && appointmentInfos is not null && appointmentInfos.Any())
                        {
                            long appointmentId = (long)kvAppointments?.Where(k => k.Value == x.Id).Select(k => k.Key).FirstOrDefault();
                            if (appointmentId > default(long))
                            {
                                AppointmentView appointment = appointmentInfos.Where(a => a.Id == appointmentId).FirstOrDefault();
                                if (appointment is not null)
                                {
                                    x.Details = appointment.Notes;
                                    x.DateOfService = appointment.DateOfAppointment;

                                }
                            }
                        }

                    });

                    //if(kvAppointments is not null && kvAppointments.Any())
                    //{
                    //    List<AppointmentView> appointmentInfos = await GetAppointmentViewByIds(kvAppointments.Select(x=>x.Key).ToList(), baseHttpRequestContext);
                    //    if (appointmentInfos is not null && appointmentInfos.Any())
                    //    {
                    //        appointmentInfos.ForEach(appointment =>
                    //        {
                    //           long summaryID = kvAppointments.Where(x => x.Key == appointment.Id).Select(x => x.Value).FirstOrDefault();
                    //           if (summaryID > default(long))
                    //            {
                    //                invoiceSummaryQueryList.ItemRecords.Where(x => x.Id == summaryID).FirstOrDefault().Details = appointment.Notes;
                    //            }
                    //        });
                    //    }
                    //}

                }
            }
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result = invoiceSummaryQueryList;
            return apiResponse;
        }
        private async Task<List<AppointmentView>> GetAppointmentViewByIds(List<long> lstAppointmentIds, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<List<AppointmentView>> apiResponseAppointment = new();

            if (lstAppointmentIds is not null && lstAppointmentIds.Any())
            {
                lstAppointmentIds = lstAppointmentIds.Distinct().ToList();
                int ps = lstAppointmentIds.Count();
                string filter = "{AppointmentId:[" + string.Join(",", lstAppointmentIds) + "]}";
                string endpoint = "/appointment/appointment_details_view?pn = 1 & ps =" + ps;
                string encodedFilter = Base64EncoderHelper.EncodeQueryModelFilterParam(filter);
                string appointmentSeviceUrl = _appSettings.ApiUrls["AppointmentServiceUrl"] + endpoint + "&f=" + encodedFilter;
                RestClient restClientAppointment = new RestClient(appointmentSeviceUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
                apiResponseAppointment = await restClientAppointment.GetAsync<ApiResponse<List<AppointmentView>>>(appointmentSeviceUrl);

                return (apiResponseAppointment == null || apiResponseAppointment.Result == null) ? null : apiResponseAppointment.Result;
            }
            return null;
        }
        private async Task<List<CompanyDetailInfo>> GetCompanyByIds(List<int?> lstCompanyIds, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<List<CompanyDetailInfo>> apiResponseCompany = new();
            int ps = lstCompanyIds.Count();
            string filter = "{CompanyDetailId:[" + string.Join(",", lstCompanyIds) + "]}";
            string endpoint = "/company/company_details_info?pn = 1 & ps =" + ps;
            string encodedFilter = Base64EncoderHelper.EncodeQueryModelFilterParam(filter);
            string companySeviceUrl = _appSettings.ApiUrls["CompanyServiceUrl"] + endpoint + "&f=" + encodedFilter;
            RestClient restClientCompany = new RestClient(companySeviceUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            apiResponseCompany = await restClientCompany.GetAsync<ApiResponse<List<CompanyDetailInfo>>>(companySeviceUrl);

            return (apiResponseCompany == null || apiResponseCompany.Result == null) ? null : apiResponseCompany.Result;
        }
        private InvoiceSummaryFilterModel PrepareFilterParameters(string filter)
        {
            if (!(String.IsNullOrEmpty(filter)))
            {
                return JsonConvert.DeserializeObject<InvoiceSummaryFilterModel>(filter);
            }
            else
            {
                return null;
            }
        }
        /// <summary>
        /// Method for partial update of Invoice Summary table  - eg Expired to Valid Status
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="id"></param>
        /// <param name="invoiceSummaryUpdateView"></param>
        /// <returns></returns>
        public async Task<ApiResponse<string>> PartialUpdateInvoiceSummary(BaseHttpRequestContext baseHttpRequestContext, long id, InvoiceSummaryUpdateView invoiceSummaryUpdateView)
        {
            ApiResponse<string> apiResponse = new();

            InvoiceSummary invoiceSummaryDB = await _invoiceDAL.GetInvoiceSummaryFromId(baseHttpRequestContext.OrgId, id);
            if (invoiceSummaryDB is not null)
            {


                Dictionary<string, object> propertyDictionary = (Dictionary<string, object>)GetFilledProperties(invoiceSummaryUpdateView);
                if (invoiceSummaryDB.InvoiceStatusId == (short)InvoiceStatus.Expired && invoiceSummaryUpdateView.InvoiceStatusId == (short)InvoiceStatus.Valid)
                {
                    short estimateValidity = await FetchEstimateValidity(baseHttpRequestContext);

                    using (var transaction = new TransactionScope(TransactionScopeOption.Required, TransactionScopeAsyncFlowOption.Enabled))
                    {
                        int rows1 = await _invoiceDAL.PartialUpdateInvoiceSummary(propertyDictionary, baseHttpRequestContext.UserId, invoiceSummaryDB);
                        if (rows1 > 0)
                        {
                            InvoiceSummaryExpiryDetail invoiceSummaryExpiryDetail = new()
                            {
                                InvoiceSummaryId = invoiceSummaryDB.Id,
                                OrgId = baseHttpRequestContext.OrgId,
                                ModifiedBy = baseHttpRequestContext.UserId,
                                CreatedDate = DateTime.UtcNow,
                                ExpiryDate = DateTime.UtcNow.AddDays(estimateValidity)
                            };
                            await _invoiceDAL.AddInvoiceSummaryExpiryDetail(invoiceSummaryExpiryDetail);
                            transaction.Complete();
                            apiResponse.StatusCode = StatusCodes.Status200OK;
                            apiResponse.Result = "Successfully updated";
                            apiResponse.Message = "Success";
                            return apiResponse;
                        }
                    }
                }
                else
                {
                    int rows = await _invoiceDAL.PartialUpdateInvoiceSummary(propertyDictionary, baseHttpRequestContext.UserId, invoiceSummaryDB);
                    if (rows > 0)
                    {
                        apiResponse.Result = "Successfully updated";
                        apiResponse.StatusCode = StatusCodes.Status200OK;
                        return apiResponse;
                    }
                }


            }
            apiResponse.Errors.Add("Invoice Summary cannot be updated.");
            apiResponse.Message = "Failure";
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            return apiResponse;
        }
        private IDictionary GetFilledProperties(InvoiceSummaryUpdateView invoiceSummaryUpdateView)
        {
            Dictionary<string, object> propertyDictionary = new();
            foreach (PropertyInfo property in invoiceSummaryUpdateView.GetType().GetProperties())
            {
                var value = property.GetValue(invoiceSummaryUpdateView);
                if (value is not null)
                    propertyDictionary.Add(property.Name, property.GetValue(invoiceSummaryUpdateView));
            }
            return propertyDictionary;
        }
        private IDictionary GetFilledProperties(InvoiceMedicareAssocUpdateView medicareAssocUpdateView)
        {
            Dictionary<string, object> propertyDictionary = new();
            foreach (PropertyInfo property in medicareAssocUpdateView.GetType().GetProperties())
            {
                var value = property.GetValue(medicareAssocUpdateView);
                if (value is not null)
                    propertyDictionary.Add(property.Name, property.GetValue(medicareAssocUpdateView));
            }
            return propertyDictionary;
        }
        /// <summary>
        /// Methopd to Update Invoice Detail (geenration of invoice etc)
        /// </summary>
        /// <param name="id"></param>
        /// <param name="invoiceDetail"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<InvoiceDetail>> EditInvoiceDetail(long id, InvoiceDetailInput invoiceDetail, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<InvoiceDetail> apiResponse = new();
            bool invConversion = false;
            int orgId = baseHttpRequestContext.OrgId;
            InvoiceDetail invoiceDetailDB = await _invoiceDAL.GetInvoiceDetailsWithInvoiceItems(id, orgId, true);
            //logger.LogError(" EditInvoiceDetail :");
            long? appointmentDetailsId = null;
            if (invoiceDetailDB is not null)
            {
                if (invoiceDetail is not null && invoiceDetail.ChangeFlag == "U")
                {
                    appointmentDetailsId = invoiceDetailDB.AppointmentDetailsId;
                    bool statusChange = false;
                    InvoiceDetail invoice = _mapper.Map<InvoiceDetailInput, InvoiceDetail>(invoiceDetail);
                    if (invoice.MsrId is null || invoice.MsrId == default(short))
                    {
                        invoice.MsrId = (short)MSR.MSR_Off;
                    }
                    invoice.ModifiedBy = baseHttpRequestContext.UserId;
                    invoice.OrgId = baseHttpRequestContext.OrgId;
                    invoice.CreatedDate = invoiceDetailDB.CreatedDate;
                    invoice.RecordId = invoiceDetailDB.RecordId;
                    invoice.ModifiedDate = DateTime.UtcNow;
                    if (invoice.DateInvoiced is null && invoice.InvoiceTypeId == (short)InvoiceType.Invoice) invoice.DateInvoiced = invoiceDetailDB.DateInvoiced;
                    decimal? totalAmountDB = invoiceDetailDB.TotalAmount is null ? 0 : invoiceDetailDB.TotalAmount;
                    decimal? owingDB = invoiceDetailDB.Owing is null ? 0 : invoiceDetailDB.Owing;
                    decimal? depositDB = (invoiceDetailDB.Deposit is null) ? 0 : invoiceDetailDB.Deposit;
                    if (invoiceDetailDB.InvoiceTypeId != invoiceDetail.InvoiceTypeId && invoiceDetail.InvoiceTypeId == (short)InvoiceType.Invoice)
                    {
                        statusChange = true;
                        invoice.DateInvoiced = DateTime.UtcNow;
                        invConversion = true;
                    }
                    List<InvoiceEpisodeItemAssoc> lstItemDB = invoiceDetailDB.InvoiceEpisodeItemAssocs is null ? null : invoiceDetailDB.InvoiceEpisodeItemAssocs.ToList();
                    List<KeyValuePair<long, decimal?>> lstItemFeeDB = lstItemDB.Where(x => x.StatusId == (short)Status.Active && (x.AdjustmentTypeId == null || x.AdjustmentTypeId != (short)AdjustmentType.Refund)).Select(x => new KeyValuePair<long, decimal?>(x.Id, x.Fee + x.Gst)).ToList();
                    lstItemFeeDB.AddRange(lstItemDB.Where(x => x.StatusId == (short)Status.Active && (x.AdjustmentTypeId != null && x.AdjustmentTypeId == (short)AdjustmentType.Refund)).Select(x => new KeyValuePair<long, decimal?>(x.Id, (x.Fee + x.Gst) * -1)).ToList());
                    List<InvoiceEpisodeItemInputAssoc> lstItemInput = invoiceDetail.InvoiceEpisodeItemAssocs is null ? null : invoiceDetail.InvoiceEpisodeItemAssocs.ToList();
                    lstItemInput.ForEach(x =>
                    {
                        x.InvoiceDetailsId = invoiceDetail.Id;
                        x.Notes = !string.IsNullOrWhiteSpace(x.Notes) ? x.Notes.Trim() : string.Empty;
                        //saving just the fee exc gst component
                        x.Fee = CommonFinanceCalculator.Rounding((decimal)(x.Fee - x.Gst), 2);
                    });
                    if (lstItemInput != null && lstItemInput.Count > 0)
                        invoice.InvoiceEpisodeItemAssocs = EditInvoiceItems(lstItemDB, lstItemInput, baseHttpRequestContext);
                    if (invoice.InvoiceEpisodeItemAssocs is not null && invoice.InvoiceEpisodeItemAssocs.Count > 0)
                    {
                        invoice.TotalAmount = ComputeTotalAmount(invoice.InvoiceEpisodeItemAssocs.ToList(), lstItemFeeDB);
                        invoice.Owing = ComputeOwingAmount(invoice.TotalAmount, totalAmountDB, owingDB);
                    }
                    else
                    {
                        invoice.Deposit = depositDB;
                        invoice.TotalAmount = totalAmountDB;
                        invoice.Owing = owingDB;

                    }

                    if (invoiceDetail.InvoiceAdditionalDetails is not null)
                    {
                        invoice.InvoiceAdditionalDetails = EditInvoiceAdditionalDetails(invoiceDetail.InvoiceAdditionalDetails, invoiceDetailDB.InvoiceAdditionalDetails, baseHttpRequestContext);
                    }
                    if (invoice.ExcessAmount != null && invoice.ExcessAmount > 0)
                    {
                        if (invoiceDetailDB.ExcessAmount == null || invoiceDetailDB.ExcessAmount == 0)
                        {
                            invoice.ExcessOwing = invoice.ExcessAmount;
                        }
                        else if (invoiceDetailDB.ExcessAmount > invoice.ExcessAmount)
                        {
                            var difference = invoiceDetailDB.ExcessAmount - invoice.ExcessAmount;
                            invoice.ExcessOwing = invoiceDetailDB.ExcessOwing - difference;
                        }
                        else if (invoice.ExcessAmount > invoiceDetailDB.ExcessAmount)
                        {
                            var difference = invoice.ExcessAmount - invoiceDetailDB.ExcessAmount;
                            invoice.ExcessOwing = invoiceDetailDB.ExcessOwing + difference;
                        }
                    }
                    else
                    {

                        if (invoiceDetailDB.ExcessAmount == null || invoiceDetailDB.ExcessAmount == 0)
                        {
                            invoice.ExcessOwing = invoice.ExcessAmount;
                        }
                        else
                        {
                            var difference = invoiceDetailDB.ExcessAmount;
                            invoice.ExcessOwing = invoiceDetailDB.ExcessOwing - difference;
                        }
                    }

                    if (/*invoice.InvoiceTypeId!=invoiceDetailDB.InvoiceTypeId && */invoice.InvoiceTypeId == (short)InvoiceType.Invoice)
                    {
                        invoice.Deposit = 0;
                        if (invoice.InvoiceTypeId == (short)InvoiceType.Invoice)
                        {
                            invoice.InvoiceStatusId = (invoice.Owing > 0) ? (short)InvoiceStatus.Unpaid : (short)InvoiceStatus.Paid;
                        }
                        else if (invoice.InvoiceTypeId == (short)InvoiceType.Estimate)
                        {
                            invoice.InvoiceStatusId = (invoice.Deposit > 0) ? (short)InvoiceStatus.Deposit : (short)InvoiceStatus.Valid;
                        }
                        if (!((invoice.AppointmentDetailsId != null && invoice.AppointmentDetailsId > default(long)) || (invoice.DateOfService != null && invoice.DateOfService > default(DateTime))))
                        {
                            apiResponse.Errors.Add("Date Of Service or Appointment is required for an invoice.");
                            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                            apiResponse.Result = null;
                            apiResponse.Message = "Failure";

                            return apiResponse;
                        }
                        if (invoice.DateOfService != null && invoice.DateOfService > default(DateTime))
                        {
                            bool isWindows = System.Runtime.InteropServices.RuntimeInformation
                                                        .IsOSPlatform(OSPlatform.Windows);
                            OrganisationView orgDetails = await FetchMasterCompanyDetails(baseHttpRequestContext);
                            short timeZoneId = (orgDetails is null || orgDetails.TimeZoneMasterId <= 0) ? default(short) : orgDetails.TimeZoneMasterId;
                            if (timeZoneId > 0)
                            {
                                DateTime? CompanyDateTimeNow = GetDateFromOrganisation(orgDetails, isWindows);
                                if (CompanyDateTimeNow is not null && CompanyDateTimeNow > default(DateTime))
                                {
                                    if (invoice.DateOfService > CompanyDateTimeNow)
                                    {
                                        apiResponse.Errors.Add("Date Of Service cannot be in the future.");
                                        apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                                        apiResponse.Result = null;
                                        apiResponse.Message = "Failure";

                                        return apiResponse;
                                    }
                                }
                            }
                        }




                    }
                    InvoiceSummary invoiceSummary = await _invoiceDAL.GetInvoiceSummaryFromId(orgId, invoiceDetail.InvoiceSummaryId);

                    if (invoiceSummary != null && invoiceSummary.InvoiceTypeId == (short)InvoiceType.Estimate && statusChange)
                    {
                        invoiceSummary.InvoiceTypeId = (short)InvoiceType.Invoice;
                        invoiceSummary.ModifiedBy = baseHttpRequestContext.UserId;
                        invoiceSummary.ModifiedDate = DateTime.UtcNow;
                        invoiceSummary.InvoiceDetails.Add(invoice);
                        invoiceSummary = await _invoiceDAL.UpdateInvoiceSummary(invoiceSummary);
                        int rows = invoiceSummary.AffectedRows;
                        List<long> invoiceDetailIds = new List<long>();
                        invoiceSummary.InvoiceDetails.ToList().ForEach(d =>
                        {
                            invoiceDetailIds.Add(d.Id);
                        });
                        appointmentDetailsId = invoiceSummary.InvoiceDetails.FirstOrDefault(x => x.AppointmentDetailsId != null)?.AppointmentDetailsId;
                        await StoreInvoiceDetailMessage(baseHttpRequestContext.OrgCode, invoiceDetailIds, DateTime.Now, (int)InvoiceRequestType.InvoiceUpdated, appointmentDetailsId);
                        if (rows > 0)
                        {
                            await _invoiceDAL.DeleteInvoiceSummaryExpiryDetail(invoiceDetail.InvoiceSummaryId, baseHttpRequestContext.OrgId);
                            if (invConversion)
                            {
                                await AddInvoiceConversionPayments(invoice.Id, baseHttpRequestContext);
                                List<long> invoiceIds = new List<long>();
                                invoiceIds.Add(id);
                                bool isWindows = System.Runtime.InteropServices.RuntimeInformation
                                                        .IsOSPlatform(OSPlatform.Windows);
                                OrganisationView orgDetails = await FetchMasterCompanyDetails(baseHttpRequestContext);
                                short timeZoneId = (orgDetails is null || orgDetails.TimeZoneMasterId <= 0) ? default(short) : orgDetails.TimeZoneMasterId;
                                if (timeZoneId > 0)
                                {
                                    DateTime? CompanyDateTimeNow = GetDateFromOrganisation(orgDetails, isWindows);
                                    if (CompanyDateTimeNow is not null && CompanyDateTimeNow > default(DateTime))
                                    {
                                        DateTime baseDate = (DateTime)CompanyDateTimeNow;
                                        DateTime paymentDate = baseDate.Date;
                                        await StoreInvoiceDetailMessage(baseHttpRequestContext.OrgCode, invoiceIds, paymentDate, (int)InvoiceConversionRequestDataType.InvoiceConversion);
                                    }
                                }
                            }
                        }
                    }
                    //if(invoiceDetailDB.InvoiceTypeId==(short)InvoiceType.Estimate && invoice.InvoiceTypeId == (short)InvoiceType.Invoice)
                    //{
                    //    using (var transaction = new TransactionScope(TransactionScopeOption.Required, TransactionScopeAsyncFlowOption.Enabled))
                    //    {
                    //        int rows = await _invoiceDAL.UpdateInvoiceDetail(invoice);

                    //        if (rows > 0)
                    //        {

                    //            await _invoiceDAL.DeleteInvoiceSummaryExpiryDetail(invoice.InvoiceSummaryId, baseHttpRequestContext.OrgId);

                    //            transaction.Complete();
                    //                                        }
                    //    }
                    //}
                    else
                    {
                        _logger.LogError("inside rows > 0 invConversion:" + invConversion);

                        invoice = await _invoiceDAL.UpdateInvoiceDetail(invoice);
                        List<long> invoiceDetailIds = new List<long>();
                        invoiceDetailIds.Add(invoice.Id);
                        appointmentDetailsId = invoice?.AppointmentDetailsId;
                        await StoreInvoiceDetailMessage(baseHttpRequestContext.OrgCode, invoiceDetailIds, DateTime.Now, (int)InvoiceRequestType.InvoiceUpdated, appointmentDetailsId);
                        int rows = invoice.Id > 0 ? 1 : 0;
                        if (rows > 0)
                        {
                            _logger.LogError("inside rows > 0 invConversion:" + invConversion);
                            if (invConversion)
                            {
                                await AddInvoiceConversionPayments(invoice.Id, baseHttpRequestContext);
                                List<long> invoiceIds = new List<long>();
                                invoiceIds.Add(id);
                                bool isWindows = System.Runtime.InteropServices.RuntimeInformation
                                                        .IsOSPlatform(OSPlatform.Windows);
                                OrganisationView orgDetails = await FetchMasterCompanyDetails(baseHttpRequestContext);
                                short timeZoneId = (orgDetails is null || orgDetails.TimeZoneMasterId <= 0) ? default(short) : orgDetails.TimeZoneMasterId;
                                if (timeZoneId > 0)
                                {
                                    DateTime? CompanyDateTimeNow = GetDateFromOrganisation(orgDetails, isWindows);
                                    if (CompanyDateTimeNow is not null && CompanyDateTimeNow > default(DateTime))
                                    {
                                        _logger.LogError(" before   invConversion:");

                                        DateTime baseDate = (DateTime)CompanyDateTimeNow;
                                        DateTime paymentDate = baseDate.Date;
                                        await StoreInvoiceDetailMessage(baseHttpRequestContext.OrgCode, invoiceIds, paymentDate, (int)InvoiceConversionRequestDataType.InvoiceConversion);
                                    }
                                }
                            }

                        }

                    }
                    await StoreInvoiceSummaryMessage(baseHttpRequestContext.OrgCode, invoiceSummary.Id, (int)InvoiceRequestType.InvoiceSummaryStatus);

                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    apiResponse.Result = invoice;
                    apiResponse.Message = "Success";
                }

                apiResponse.StatusCode = StatusCodes.Status200OK;
                //apiResponse.Result = invoiceDetail;
                apiResponse.Message = "Success";
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = null;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("Invoice cannot be updated at this time.");
            }

            return apiResponse;
        }

        private async Task AddInvoiceConversionPayments(long id, BaseHttpRequestContext baseHttpRequestContext)
        {
            InvoiceDetailIdObject invObj = new()
            {
                Id = id
            };
            string endpoint = string.Format("/invoicepayment/payment_details/invoice_conversion", baseHttpRequestContext.OrgId);
            string paymentSeviceUrl = _appSettings.ApiUrls["InvoicePaymentServiceUrl"] + endpoint;
            RestClient restClientPayment = new RestClient(paymentSeviceUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            await restClientPayment.PostAsync<ApiResponse<string>>(paymentSeviceUrl, invObj);
        }
        private async Task StoreInvoiceSummaryMessage(string orgCode, long invoiceSummaryId, int propertyType)
        {
            InvoiceConversionRequestDataModel invoiceConversionRequestDataModel = new InvoiceConversionRequestDataModel
            {
                OrgName = orgCode,
                PropertyType = propertyType,
                PropertyId = new[] { invoiceSummaryId }
            };
            Dictionary<string, string> userPros = new Dictionary<string, string>
                        {
                            { "RequestType","InvoiceSummary"},
                            { "to",_configuration["AzureAD:ASBSubNameInvoiceSummary"]}
                        };
            byte[] jsonContentByteArr = Encoding.GetEncoding("iso-8859-1").GetBytes(JsonConvert.SerializeObject(invoiceConversionRequestDataModel));
            await _asbMessageSenderHelper.MessageSenderTopic(jsonContentByteArr, userPros, _configuration["AzureAD:AzSBConnStringInvoice"], _configuration["AzureAD:ASBTopicInvoice"]);
        }
        private async Task StoreInvoiceDetailMessage(string orgCode, List<long> invoiceIds, DateTime paymentDate, int propertyType, long? appointmentDetailsId = null)
        {
            _logger.LogError("StoreInvoiceDetailMessage invoiceIDs: " + string.Join(',', invoiceIds));
            _logger.LogError("StoreInvoiceDetailMessage propertyParentId: " + appointmentDetailsId);
            _logger.LogError("StoreInvoiceDetailMessage propertyType: " + propertyType);
            _logger.LogError("StoreInvoiceDetailMessage PaymentDate: " + paymentDate);

            InvoiceConversionRequestDataModel invoiceConversionRequestDataModel = new InvoiceConversionRequestDataModel
            {
                OrgName = orgCode,
                PropertyType = propertyType,
                PropertyParentId = appointmentDetailsId,
                PropertyId = invoiceIds.Select(x => x).ToArray(),
                PaymentDate = paymentDate
            };
            Dictionary<string, string> userPros = new Dictionary<string, string>
                        {
                            { "RequestType","InvoiceConversion"},
                            { "to",_configuration["AzureAD:ASBSubNameInvoice"]}
                        };
            byte[] jsonContentByteArr = Encoding.GetEncoding("iso-8859-1").GetBytes(JsonConvert.SerializeObject(invoiceConversionRequestDataModel));
            await _asbMessageSenderHelper.MessageSenderTopic(jsonContentByteArr, userPros, _configuration["AzureAD:AzSBConnStringInvoice"], _configuration["AzureAD:ASBTopicInvoice"]);
        }
        private async Task StoreSplitPaymentDetailMessage(string orgCode, long paymentId, int propertyType)
        {
            PaymentRequestDataModel paymentRequestDataModel = new PaymentRequestDataModel
            {
                OrgName = orgCode,
                PropertyType = propertyType,
                PropertyId = new[] { paymentId },
                PropertyValue = ""
            };
            Dictionary<string, string> userPros = new Dictionary<string, string>
                        {
                            { "RequestType","SplitPayment"},
                            { "to",_configuration["AzureAD:ASBSubNameInvoicePayment"]}
                        };
            byte[] jsonContentByteArr = Encoding.GetEncoding("iso-8859-1").GetBytes(JsonConvert.SerializeObject(paymentRequestDataModel));
            await _asbMessageSenderHelper.MessageSenderTopic(jsonContentByteArr, userPros, _configuration["AzureAD:AzSBConnStringPayment"], _configuration["AzureAD:ASBTopicPayment"]);
        }
        private async Task AddInvoicePayments(long id, List<PaymentDetail> lstPayments, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<string> apiResponsePayment = new();

            string endpoint = "/invoicepayment/" + id + "/payment_details";
            string paymentSeviceUrl = _appSettings.ApiUrls["InvoicePaymentServiceUrl"] + endpoint;
            RestClient restClientPayment = new RestClient(paymentSeviceUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            await restClientPayment.PostAsync<ApiResponse<string>>(paymentSeviceUrl, lstPayments);

        }
        private async Task<ApiResponse<long>> AddInvoicePaymentAdjustments(long id, PaymentDetail payment, BaseHttpRequestContext baseHttpRequestContext, long? patientId)
        {
            ApiResponse<long> apiResponsePayment = new();

            if (patientId == null)
            {
                patientId = 0;
            }
            string endpoint = "/invoicepayment/" + id + "/payment_details_adjustment/" + patientId;
            string paymentSeviceUrl = _appSettings.ApiUrls["InvoicePaymentServiceUrl"] + endpoint;
            RestClient restClientPayment = new RestClient(paymentSeviceUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            apiResponsePayment = await restClientPayment.PostAsync<ApiResponse<long>>(paymentSeviceUrl, payment);
            return apiResponsePayment;
        }
        private async Task<ApiResponse<PaymentDetail>> FetchInvoicePaymentDetails(long paymentDetailsId, long invoiceDetailsId, BaseHttpRequestContext baseHttpRequestContext, bool splitup = false)
        {
            ApiResponse<PaymentDetail> apiResponsePayment = new();

            string endpoint = "/invoicepayment/" + invoiceDetailsId + "/payment_details/" + paymentDetailsId;
            if (splitup)
                endpoint = "/invoicepayment/" + invoiceDetailsId + "/payment_details_splitup/" + paymentDetailsId;
            string paymentSeviceUrl = _appSettings.ApiUrls["InvoicePaymentServiceUrl"] + endpoint;
            RestClient restClientPayment = new RestClient(paymentSeviceUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            apiResponsePayment = await restClientPayment.GetAsync<ApiResponse<PaymentDetail>>(paymentSeviceUrl);
            return apiResponsePayment;
        }
        private DateTime? GetDateFromOrganisation(OrganisationView orgDetails, bool isWindows)
        {
            string timeZone = (isWindows) ? orgDetails.WindowsTimeZoneData : orgDetails.LinuxTimeZoneData;

            var CompanyDateTimeNow = DateTimeConversion.ConvertTimeFromUtc(DateTime.UtcNow, timeZone);

            return CompanyDateTimeNow;
        }
        private async Task<OrganisationView> FetchMasterCompanyDetails(BaseHttpRequestContext baseHttpRequestContext)
        {

            ApiResponse<OrganisationView> apiResponseCompany = new();
            string endpoint = string.Format("/company/Organisation/{0}", baseHttpRequestContext.OrgId);
            string companySeviceUrl = _appSettings.ApiUrls["CompanyServiceUrl"] + endpoint;
            RestClient restClientCompany = new RestClient(companySeviceUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            apiResponseCompany = await restClientCompany.GetAsync<ApiResponse<OrganisationView>>(companySeviceUrl);
            return (apiResponseCompany == null) ? null : apiResponseCompany.Result;

        }
        /// <summary>
        /// Method to add a new Invoice .If the invoice is not part of an existing EstimateSummary then a new InvoiceSummary table entry will also be added
        /// </summary>
        /// <param name="invoiceDetail"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<InvoiceDetail>> AddInvoiceDetail(InvoiceDetail invoiceDetail, BaseHttpRequestContext baseHttpRequestContext)
        {
            long invoiceId = default(long);
            ApiResponse<InvoiceDetail> apiResponse = new();
            int orgId = baseHttpRequestContext.OrgId;
            long loggedInUser = baseHttpRequestContext.UserId;
            bool isSaved = false;
            long? appointmentDetailsId = null;
            if (invoiceDetail is not null && invoiceDetail.InvoiceSummaryId > default(long))
            {
                appointmentDetailsId = invoiceDetail.AppointmentDetailsId;
                invoiceDetail.ModifiedBy = loggedInUser;
                invoiceDetail.OrgId = orgId;
                invoiceDetail.CreatedDate = DateTime.UtcNow;
                invoiceDetail.RecordId = await SetRecordId(1);
                invoiceDetail.TotalAmount = (invoiceDetail.TotalAmount is null) ? default(decimal) : invoiceDetail.TotalAmount;
                invoiceDetail.Owing = (invoiceDetail.Owing is null) ? default(decimal) : invoiceDetail.Owing;
                invoiceDetail.Deposit = (invoiceDetail.Deposit is null) ? default(decimal) : invoiceDetail.Deposit;
                invoiceDetail.ExcessAmount = (invoiceDetail.ExcessAmount is null) ? default(decimal) : invoiceDetail.ExcessAmount;
                invoiceDetail.ExcessOwing = (invoiceDetail.ExcessAmount is null) ? default(decimal) : invoiceDetail.ExcessAmount;

                //if(invoiceDetail.InvoiceSummaryId<= default(long))
                //{
                //    InvoiceSummary invoiceSummary = new InvoiceSummary
                //    {
                //        OrgId = orgId,
                //        CreatedDate = DateTime.UtcNow,
                //        ModifiedBy = loggedInUser,
                //        InvoiceStatusId  = (short)InvoiceStatus.Valid,
                //        InvoiceTypeId = invoiceDetail.InvoiceTypeId
                //    };
                //}
                if (invoiceDetail.MsrId is null || invoiceDetail.MsrId == default(short))
                {
                    invoiceDetail.MsrId = (short)MSR.MSR_Off;
                }
                if (invoiceDetail.InvoiceTypeId == default(short))
                {
                    invoiceDetail.InvoiceTypeId = (short)InvoiceType.Estimate;
                }
                if (invoiceDetail.InvoiceStatusId == default(short))
                {
                    invoiceDetail.InvoiceStatusId = (short)InvoiceStatus.Valid;
                }
                if (invoiceDetail.InvoiceAdditionalDetails is not null)
                {
                    invoiceDetail.InvoiceAdditionalDetails.CreatedDate = DateTime.UtcNow;
                    invoiceDetail.InvoiceAdditionalDetails.OrgId = orgId;
                    invoiceDetail.InvoiceAdditionalDetails.ModifiedBy = loggedInUser;
                }
                if (invoiceDetail.InvoiceEpisodeItemAssocs is not null)
                {

                    invoiceDetail.InvoiceEpisodeItemAssocs.ToList().ForEach(item =>
                    {
                        item.Notes = !string.IsNullOrWhiteSpace(item.Notes) ? item.Notes.Trim() : string.Empty;

                        if (item.EpisodeTypeId != (short)EpisodeTypes.MBS && item.EpisodeTypeId != (short)EpisodeTypes.Accommodation)
                        {
                            item.Medicare = default(decimal);
                            item.Fund = default(decimal);
                            item.Rebate = default(decimal);
                        }
                        invoiceDetail.TotalAmount += item.Fee;

                        //saving just the fee exc gst component
                        item.Fee = CommonFinanceCalculator.Rounding((decimal)(item.Fee - item.Gst), 2);

                        item.CreatedDate = DateTime.UtcNow;
                        item.CreatedBy = loggedInUser;
                        item.OrgId = orgId;
                        item.ModifiedBy = loggedInUser;
                        item.StatusId = (short)Status.Active;
                        item.AdjustmentTypeId = null;
                        item.InvoiceEpisodeItemAssocsId = null;
                        if (item.InvoiceEpisodeItemsIndicatorAssocs is not null)
                        {
                            item.InvoiceEpisodeItemsIndicatorAssocs.ToList().ForEach(indicator =>
                            {
                                indicator.CreatedDate = DateTime.UtcNow;
                                indicator.CreatedBy = loggedInUser;
                                indicator.OrgId = orgId;
                                indicator.ModifiedBy = loggedInUser;
                                indicator.StatusId = (short)Status.Active;
                            });
                        }
                    });
                }
                invoiceDetail.Owing = invoiceDetail.TotalAmount;

                if (invoiceDetail.InvoiceTypeId == (short)InvoiceType.Invoice)
                {
                    invoiceDetail.InvoiceStatusId = (invoiceDetail.Owing > 0) ? (short)InvoiceStatus.Unpaid : (short)InvoiceStatus.Paid;
                    invoiceDetail.DateInvoiced = DateTime.UtcNow;
                    invoiceDetail.Deposit = 0;
                    if (!((invoiceDetail.AppointmentDetailsId != null && invoiceDetail.AppointmentDetailsId > default(long)) || (invoiceDetail.DateOfService != null && invoiceDetail.DateOfService > default(DateTime))))
                    {
                        apiResponse.Errors.Add("Date Of Service or Appointment is required for an invoice.");
                        apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                        apiResponse.Result = null;
                        apiResponse.Message = "Failure";

                        return apiResponse;
                    }
                    if (invoiceDetail.DateOfService != null && invoiceDetail.DateOfService > default(DateTime))
                    {
                        OrganisationView orgDetails = await FetchMasterCompanyDetails(baseHttpRequestContext);
                        short timeZoneId = (orgDetails is null || orgDetails.TimeZoneMasterId <= 0) ? default(short) : orgDetails.TimeZoneMasterId;
                        if (timeZoneId > 0)
                        {
                            bool isWindows = System.Runtime.InteropServices.RuntimeInformation
                                                           .IsOSPlatform(OSPlatform.Windows);
                            DateTime? CompanyDateTimeNow = GetDateFromOrganisation(orgDetails, isWindows);
                            if (CompanyDateTimeNow is not null && CompanyDateTimeNow > default(DateTime))
                            {
                                if (invoiceDetail.DateOfService > CompanyDateTimeNow)
                                {
                                    apiResponse.Errors.Add("Date Of Service cannot be in the future.");
                                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                                    apiResponse.Result = null;
                                    apiResponse.Message = "Failure";

                                    return apiResponse;
                                }
                            }
                        }
                    }
                    InvoiceSummary invoiceSummary = await _invoiceDAL.GetInvoiceSummaryFromId(orgId, invoiceDetail.InvoiceSummaryId);
                    if (invoiceSummary != null && invoiceSummary.InvoiceTypeId == (short)InvoiceType.Estimate && invoiceDetail.InvoiceTypeId == (short)InvoiceType.Invoice)
                    {
                        invoiceSummary.InvoiceTypeId = (short)InvoiceType.Invoice;
                        invoiceSummary.ModifiedBy = loggedInUser;
                        invoiceSummary.ModifiedDate = DateTime.UtcNow;
                        invoiceSummary.InvoiceDetails.Add(invoiceDetail);
                        invoiceSummary = await _invoiceDAL.UpdateInvoiceSummary(invoiceSummary);
                        List<long> invoiceDetailIds = new List<long>();
                        invoiceSummary.InvoiceDetails.ToList().ForEach(d =>
                        {
                            invoiceDetailIds.Add(d.Id);
                        });
                        appointmentDetailsId = invoiceSummary.InvoiceDetails.FirstOrDefault(x => x.AppointmentDetailsId != null)?.AppointmentDetailsId;
                        await StoreInvoiceDetailMessage(baseHttpRequestContext.OrgCode, invoiceDetailIds, DateTime.Now, (int)InvoiceRequestType.InvoiceUpdated, appointmentDetailsId);
                        int rows = invoiceSummary.AffectedRows;
                        isSaved = true;
                        if (rows > 0)
                        {
                            invoiceId = invoiceSummary.InvoiceDetails.FirstOrDefault().Id;
                            await _invoiceDAL.DeleteInvoiceSummaryExpiryDetail(invoiceDetail.InvoiceSummaryId, baseHttpRequestContext.OrgId);
                        }
                    }
                }
                else if (invoiceDetail.InvoiceTypeId == (short)InvoiceType.Estimate)
                {
                    invoiceDetail.InvoiceStatusId = (invoiceDetail.Deposit > 0) ? (short)InvoiceStatus.Deposit : (short)InvoiceStatus.Valid;
                }
                if (!isSaved)
                {
                    invoiceDetail = await _invoiceDAL.AddInvoiceDetail(invoiceDetail);
                    List<long> invoiceDetailIds = new List<long>();
                    invoiceDetailIds.Add(invoiceDetail.Id);
                    appointmentDetailsId = invoiceDetail.AppointmentDetailsId;
                    await StoreInvoiceDetailMessage(baseHttpRequestContext.OrgCode, invoiceDetailIds, DateTime.Now, (int)InvoiceRequestType.InvoiceAdded, appointmentDetailsId);

                }
                await StoreInvoiceSummaryMessage(baseHttpRequestContext.OrgCode, invoiceDetail.InvoiceSummaryId, (int)InvoiceRequestType.InvoiceSummaryStatus);

                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Result = invoiceDetail;
                apiResponse.Message = "Success";
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Invoice cannot be created with the data provided.");
                apiResponse.Message = "Failure";
            }
            return apiResponse;
        }
        private async Task<long> SetRecordId(short recordIdType)
        {
            switch (recordIdType)
            {
                case 1:
                    {
                        return await _invoiceDAL.GetNextVal("[Invoice].[SeqInvoiceID]");

                    }
                case 2:
                    {
                        return await _invoiceDAL.GetNextVal("[Invoice].[SeqInvoiceSummaryID]");

                    }

                default:
                    return default(long);
            }

        }

        /// <summary>
        /// Method to Add a new Estimate Template
        /// </summary>
        /// <param name="invoiceTemplate"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long>> AddInvoiceTemplate(InvoiceTemplate invoiceTemplate, BaseHttpRequestContext baseHttpRequestContext)
        {
            int orgId = baseHttpRequestContext.OrgId;
            long loggedInUser = baseHttpRequestContext.UserId;
            ApiResponse<long> apiResponse = new();
            if (invoiceTemplate is not null)
            {
                if (!string.IsNullOrEmpty(invoiceTemplate.Name))
                {
                    bool isDuplicate = await _invoiceDAL.CheckTemplateName(invoiceTemplate.Name, orgId);
                    if (isDuplicate)
                    {
                        apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                        apiResponse.Result = default(long);
                        apiResponse.Message = "Failure";
                        apiResponse.Errors.Add("Template Name is already in use.");
                        return apiResponse;
                    }
                }
                invoiceTemplate.StatusId = (short)Status.Active;
                invoiceTemplate.ModifiedBy = loggedInUser;
                invoiceTemplate.OrgId = orgId;
                invoiceTemplate.CreatedDate = DateTime.UtcNow;
                if (invoiceTemplate.InvoiceSummaries is not null)
                {
                    InvoiceSummary invoiceSummary = invoiceTemplate.InvoiceSummaries;
                    invoiceSummary.ModifiedBy = loggedInUser;
                    invoiceSummary.OrgId = orgId;
                    invoiceSummary.CreatedDate = DateTime.UtcNow;

                    invoiceSummary.InvoiceTypeId = (short)InvoiceType.Estimate;
                    invoiceSummary.FinanceTypeId = (short)FinanceType.Template;

                    invoiceSummary.InvoiceStatusId = (short)InvoiceStatus.Valid;
                    invoiceSummary.PatientDetailsId = null;

                    if (invoiceSummary.InvoiceDetails is not null)
                    {
                        List<InvoiceDetail> lstInvoiceDetails = invoiceSummary.InvoiceDetails.ToList();
                        lstInvoiceDetails.ForEach(invoice =>
                        {
                            invoice.CreatedDate = DateTime.UtcNow;
                            invoice.OrgId = orgId;
                            invoice.ModifiedBy = loggedInUser;
                            invoice.InvoiceTypeId = (short)InvoiceType.Estimate;
                            invoice.InvoiceStatusId = (short)InvoiceStatus.Valid;
                            invoice.AppointmentDetailsId = null;
                            invoice.DateOfService = null;
                            invoice.IsServiceDate = false;
                            invoice.TotalAmount = default(decimal);
                            invoice.Owing = default(decimal);
                            invoice.Deposit = default(decimal);
                            invoice.KnownGapMax = 0;
                            invoice.ExcessAmount = 0;
                            invoice.ExcessOwing = 0;
                            invoice.HealthFundParticipantId = null;
                            if (invoice.MsrId is null || invoice.MsrId == default(short))
                            {
                                invoice.MsrId = (short)MSR.MSR_Off;
                            }

                            if (invoice.InvoiceEpisodeItemAssocs is not null)
                            {

                                invoice.InvoiceEpisodeItemAssocs.ToList().ForEach(item =>
                                {
                                    item.Notes = !string.IsNullOrWhiteSpace(item.Notes) ? item.Notes.Trim() : string.Empty;

                                    if (item.EpisodeTypeId != (short)EpisodeTypes.MBS && item.EpisodeTypeId != (short)EpisodeTypes.Accommodation)
                                    {
                                        item.Medicare = default(decimal);
                                        item.Fund = default(decimal);
                                        item.Rebate = default(decimal);
                                    }

                                    invoice.TotalAmount += item.Fee;

                                    //saving just the fee exc gst component
                                    item.Fee = CommonFinanceCalculator.Rounding((decimal)(item.Fee - item.Gst), 2);

                                    item.CreatedDate = DateTime.UtcNow;
                                    item.CreatedBy = loggedInUser;
                                    item.OrgId = orgId;
                                    item.ModifiedBy = loggedInUser;
                                    item.StatusId = (short)Status.Active;
                                });
                                invoice.Owing = invoice.TotalAmount;
                            }
                            if (invoice.InvoiceAdditionalDetails is not null)
                            {
                                invoice.InvoiceAdditionalDetails.OrgId = orgId;
                                invoice.InvoiceAdditionalDetails.ModifiedBy = loggedInUser;
                                invoice.InvoiceAdditionalDetails.CreatedDate = DateTime.UtcNow;

                            }


                        });
                    }
                }


                long templateId = await _invoiceDAL.AddInvoiceTemplate(invoiceTemplate);

                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Result = templateId;
                apiResponse.Message = "Success";
                return apiResponse;


            }


            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            apiResponse.Result = default(long);
            apiResponse.Message = "Failure";
            apiResponse.Errors.Add("New Estimate Template cannot be added at this time.");
            return apiResponse;
        }
        /// <summary>
        /// Method to fetch Estimate Template
        /// </summary>
        /// <param name="id"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<InvoiceTemplateView>> FetchInvoiceTemplate(long id, BaseHttpRequestContext baseHttpRequestContext)
        {
            int orgId = baseHttpRequestContext.OrgId;
            ApiResponse<InvoiceTemplateView> apiResponse = new();
            InvoiceTemplate invoiceTemplate = await _invoiceDAL.GetInvoiceTemplateWithInvoiceSummary(id, orgId);
            if (invoiceTemplate is not null)
            {
                InvoiceTemplateView invoiceTemplateView = _mapper.Map<InvoiceTemplate, InvoiceTemplateView>(invoiceTemplate);
                if (invoiceTemplate.InvoiceSummaries is not null)
                {
                    ApiResponse<InvoiceSummaryView> apiResponseSummary = await GetInvoiceSummary(invoiceTemplate.InvoiceSummaries.Id, baseHttpRequestContext);
                    if (apiResponseSummary is not null && apiResponseSummary.StatusCode == StatusCodes.Status200OK)
                    {
                        invoiceTemplateView.InvoiceSummaries = apiResponseSummary.Result;
                        apiResponse.StatusCode = StatusCodes.Status200OK;
                        apiResponse.Result = invoiceTemplateView;
                        apiResponse.Message = "Success";
                        return apiResponse;
                    }

                }
            }
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            apiResponse.Result = null;
            apiResponse.Message = "Failure";
            apiResponse.Errors.Add("Estimate Template cannot be fetched at this time.");
            return apiResponse;
        }
        /// <summary>
        /// ethod to list estimate templates
        /// </summary>
        /// <param name="queryModel"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<QueryResultList<InvoiceTemplate>>> ListInvoiceTemplate(QueryModel queryModel, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<QueryResultList<InvoiceTemplate>> apiResponse = new();
            int orgId = baseHttpRequestContext.OrgId;
            //InvoiceSummaryFilterModel filterModel = PrepareFilterParameters(queryModel.Filter);
            QueryResultList<InvoiceTemplate> invoiceTemplateQueryList = await _invoiceDAL.ListInvoiceTemplate(orgId, queryModel);
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result = invoiceTemplateQueryList;
            return apiResponse;
        }
        /// <summary>
        /// Method to rename a template (partial update)
        /// </summary>
        /// <param name="id"></param>
        /// <param name="invoiceTemplateUpdateView"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long>> PartialUpdateInvoiceTemplate(long id, InvoiceTemplateUpdateView invoiceTemplateUpdateView, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<long> apiResponse = new();
            if (invoiceTemplateUpdateView is not null)
            {
                InvoiceTemplate invoiceTemplateDB = await _invoiceDAL.GetInvoiceTemplateFromId(id, baseHttpRequestContext.OrgId);
                if (invoiceTemplateDB is not null)
                {
                    if (invoiceTemplateDB.Name != invoiceTemplateUpdateView.Name && !(await _invoiceDAL.CheckTemplateName(invoiceTemplateUpdateView.Name, baseHttpRequestContext.OrgId)))
                    {
                        Dictionary<string, object> propertyDictionary = (Dictionary<string, object>)GetFilledProperties(invoiceTemplateUpdateView);
                        int rows = await _invoiceDAL.PartialUpdateInvoiceTemplate(propertyDictionary, baseHttpRequestContext.UserId, invoiceTemplateDB);
                        if (rows > 0)
                        {
                            apiResponse.Message = "Successfully updated";
                            apiResponse.StatusCode = StatusCodes.Status200OK;
                            apiResponse.Result = id;
                            return apiResponse;
                        }
                    }

                }
            }
            apiResponse.Message = "Failure";
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            apiResponse.Result = default(long);
            apiResponse.Errors.Add("Estimate template cannot be updated at this time.");
            return apiResponse;
        }

        private IDictionary GetFilledProperties(InvoiceTemplateUpdateView invoiceTemplateUpdateView)
        {
            Dictionary<string, object> propertyDictionary = new();
            foreach (PropertyInfo property in invoiceTemplateUpdateView.GetType().GetProperties())
            {
                var value = property.GetValue(invoiceTemplateUpdateView);
                if (value is not null)
                    propertyDictionary.Add(property.Name, property.GetValue(invoiceTemplateUpdateView));
            }
            return propertyDictionary;
        }
        /// <summary>
        /// Method to check if the template name is duplicate
        /// </summary>
        /// <param name="search_term"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<ApiResponse<bool>> CheckTemplateName(string search_term, int orgId)
        {
            bool nameBool = await _invoiceDAL.CheckTemplateName(search_term, orgId);
            var apiResponse = new ApiResponse<bool>
            {
                Result = nameBool,
                Message = "Success",
                StatusCode = StatusCodes.Status200OK
            };
            return apiResponse;
        }
        /// <summary>
        /// Method to update Invoice Template
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="id"></param>
        /// <param name="invoiceTemplateInput"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> UpdateInvoiceTemplate(BaseHttpRequestContext baseHttpRequestContext, long id, InvoiceTemplateInput invoiceTemplateInput)
        {
            ApiResponse<long?> apiResponse = new();

            int orgId = baseHttpRequestContext.OrgId;
            InvoiceTemplate invoiceTemplateDB = await _invoiceDAL.GetInvoiceTemplateWithInvoices(id, orgId);
            if (invoiceTemplateDB is not null && invoiceTemplateInput is not null)
            {
                invoiceTemplateInput.CreatedDate = invoiceTemplateDB.CreatedDate;
                invoiceTemplateInput.OrgId = invoiceTemplateDB.OrgId;
                invoiceTemplateInput.ModifiedBy = baseHttpRequestContext.UserId;
                invoiceTemplateInput.ModifiedDate = DateTime.UtcNow;
                invoiceTemplateInput.StatusId = invoiceTemplateDB.StatusId;
                invoiceTemplateInput.DeleteReason = invoiceTemplateDB.DeleteReason;
                invoiceTemplateInput.Name = invoiceTemplateDB.Name;
                InvoiceTemplate invoiceTemplate = _mapper.Map<InvoiceTemplateInput, InvoiceTemplate>(invoiceTemplateInput);

                if (invoiceTemplateInput.InvoiceSummaries is not null)
                {
                    InvoiceSummaryInput invoiceSummaryInput = invoiceTemplateInput.InvoiceSummaries;
                    invoiceTemplateInput.InvoiceSummaries = null;
                    InvoiceSummary invoiceSummaryDB = invoiceTemplateDB.InvoiceSummaries;
                    invoiceTemplateDB.InvoiceSummaries = null;
                    invoiceSummaryInput.CreatedDate = invoiceSummaryDB.CreatedDate;
                    invoiceSummaryInput.OrgId = invoiceSummaryDB.OrgId;
                    invoiceSummaryInput.ModifiedBy = baseHttpRequestContext.UserId;
                    invoiceSummaryInput.ModifiedDate = DateTime.UtcNow;
                    invoiceSummaryInput.RecordId = null;
                    invoiceSummaryInput.InvoiceTemplatesId = invoiceSummaryDB.InvoiceTemplatesId;
                    invoiceSummaryInput.InvoiceTypeId = invoiceSummaryDB.InvoiceTypeId;
                    invoiceSummaryInput.InvoiceStatusId = invoiceSummaryDB.InvoiceStatusId;

                    List<InvoiceDetail> lstInvoiceDB = (invoiceSummaryDB.InvoiceDetails is null) ? null : invoiceSummaryDB.InvoiceDetails.ToList();
                    List<InvoiceDetailInput> lstInvoiceInput = (invoiceSummaryInput.InvoiceDetails is null) ? null : invoiceSummaryInput.InvoiceDetails.ToList();

                    InvoiceSummary invoiceSummary = _mapper.Map<InvoiceSummaryInput, InvoiceSummary>(invoiceSummaryInput);
                    short financeTypeId = invoiceSummary.FinanceTypeId;

                    lstInvoiceInput.ForEach(x =>
                    {
                        x.InvoiceSummaryId = id;
                        if (x.MsrId is null || x.MsrId == default(short))
                        {
                            x.MsrId = (short)MSR.MSR_Off;
                        }
                        if (financeTypeId == (short)FinanceType.Template)
                        {
                            x.AppointmentDetailsId = null;
                            x.DateOfService = null;
                            x.IsServiceDate = false;
                            x.InvoiceTypeId = (short)InvoiceType.Estimate;
                            x.InvoiceStatusId = (short)InvoiceStatus.Valid;
                            x.KnownGapMax = 0;
                            x.ExcessAmount = 0;
                            x.ExcessOwing = 0;
                            x.HealthFundParticipantId = null;
                        }
                        x.Deposit = 0;

                    });
                    invoiceSummary.InvoiceDetails = await EditInvoiceDetails(lstInvoiceDB, lstInvoiceInput, baseHttpRequestContext, true);
                    invoiceTemplate.InvoiceSummaries = invoiceSummary;

                    await _invoiceDAL.UpdateInvoiceTemplate(invoiceTemplate);
                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    apiResponse.Message = "Success";
                    apiResponse.Result = id;
                    return apiResponse;
                }



            }
            apiResponse.Message = "Failure";
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            apiResponse.Result = default(long);
            apiResponse.Errors.Add("Estimate template cannot be updated at this time.");
            return apiResponse;
        }
        /// <summary>
        /// Method to delete a estimate template
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="id"></param>
        /// <param name="deleteObject"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> DeleteInvoiceTemplate(BaseHttpRequestContext baseHttpRequestContext, long id, DeleteObject deleteObject)
        {
            int orgId = baseHttpRequestContext.OrgId;
            long loggedInUser = baseHttpRequestContext.UserId;
            ApiResponse<long?> apiResponse = new();
            InvoiceTemplate invoiceTemplate = await _invoiceDAL.GetInvoiceTemplateFromId(id, orgId);
            if (invoiceTemplate is not null && deleteObject is not null)
            {

                invoiceTemplate.ModifiedBy = baseHttpRequestContext.UserId;
                invoiceTemplate.ModifiedDate = DateTime.UtcNow;
                invoiceTemplate.StatusId = (short)Status.Deleted;
                invoiceTemplate.DeleteReason = deleteObject.DeleteReason;

                await _invoiceDAL.UpdateInvoiceTemplate(invoiceTemplate);
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";
                apiResponse.Result = id;
                return apiResponse;
            }
            apiResponse.Message = "Failure";
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            apiResponse.Result = default(long);
            apiResponse.Errors.Add("Estimate template cannot be updated at this time.");
            return apiResponse;
        }

        public async Task<ApiResponse<long?>> OverrrideInvoiceTemplate(BaseHttpRequestContext baseHttpRequestContext, long id, InvoiceTemplate invoiceTemplate)
        {
            ApiResponse<long?> apiResponse = new();
            int orgId = baseHttpRequestContext.OrgId;
            InvoiceTemplate invoiceTemplateDB = await _invoiceDAL.GetInvoiceTemplateWithInvoices(id, orgId);

            if (invoiceTemplateDB is not null && invoiceTemplate is not null)
            {
                invoiceTemplate.CreatedDate = invoiceTemplateDB.CreatedDate;
                invoiceTemplate.OrgId = invoiceTemplateDB.OrgId;
                invoiceTemplate.ModifiedBy = baseHttpRequestContext.UserId;
                invoiceTemplate.ModifiedDate = DateTime.UtcNow;
                invoiceTemplate.StatusId = invoiceTemplateDB.StatusId;
                invoiceTemplate.DeleteReason = invoiceTemplateDB.DeleteReason;
                invoiceTemplate.Id = id;
                if (invoiceTemplate.InvoiceSummaries is not null)
                {
                    InvoiceSummary invoiceSummary = invoiceTemplate.InvoiceSummaries;

                    InvoiceSummary invoiceSummaryDB = invoiceTemplateDB.InvoiceSummaries;
                    invoiceSummary.CreatedDate = invoiceSummaryDB.CreatedDate;
                    invoiceSummary.OrgId = invoiceSummaryDB.OrgId;
                    invoiceSummary.ModifiedBy = baseHttpRequestContext.UserId;
                    invoiceSummary.ModifiedDate = DateTime.UtcNow;
                    invoiceSummary.RecordId = null;
                    invoiceSummary.InvoiceTemplatesId = invoiceSummaryDB.InvoiceTemplatesId;
                    invoiceSummary.InvoiceTypeId = invoiceSummaryDB.InvoiceTypeId;
                    invoiceSummary.InvoiceStatusId = invoiceSummaryDB.InvoiceStatusId;
                    invoiceSummary.PatientDetailsId = null;
                    invoiceSummary.FinanceTypeId = (short)FinanceType.Template;
                    invoiceSummary.Id = invoiceSummaryDB.Id;
                    invoiceSummary.InvoiceTemplatesId = invoiceSummaryDB.InvoiceTemplatesId;
                    if (invoiceSummary.InvoiceDetails is not null)
                    {
                        List<InvoiceDetail> lstInvoiceDetails = invoiceSummary.InvoiceDetails.ToList();
                        lstInvoiceDetails.ForEach(invoice =>
                        {
                            invoice.InvoiceSummaryId = invoiceSummary.Id;
                            invoice.CreatedDate = DateTime.Now;
                            invoice.OrgId = orgId;
                            invoice.ModifiedBy = baseHttpRequestContext.UserId;
                            invoice.InvoiceTypeId = (short)InvoiceType.Estimate;
                            invoice.InvoiceStatusId = (short)InvoiceStatus.Valid;
                            invoice.AppointmentDetailsId = null;
                            invoice.DateOfService = null;
                            invoice.IsServiceDate = false;
                            invoice.Deposit = default(decimal);
                            invoice.Owing = default(decimal);
                            invoice.TotalAmount = default(decimal);
                            //invoice.InvoiceAdditionalDetails = null;
                            if (invoice.MsrId is null || invoice.MsrId == default(short))
                            {
                                invoice.MsrId = (short)MSR.MSR_Off;
                            }

                            if (invoice.InvoiceEpisodeItemAssocs is not null)
                            {

                                invoice.InvoiceEpisodeItemAssocs.ToList().ForEach(item =>
                                {
                                    item.Notes = !string.IsNullOrWhiteSpace(item.Notes) ? item.Notes.Trim() : string.Empty;

                                    if (item.EpisodeTypeId != (short)EpisodeTypes.MBS)
                                    {
                                        item.Medicare = default(decimal);
                                        item.Fund = default(decimal);
                                        item.Rebate = default(decimal);
                                    }
                                    //saving just the fee exc gst component
                                    invoice.TotalAmount += item.Fee;

                                    item.Fee = CommonFinanceCalculator.Rounding((decimal)(item.Fee - item.Gst), 2);

                                    item.CreatedDate = DateTime.UtcNow;
                                    item.CreatedBy = baseHttpRequestContext.UserId;
                                    item.OrgId = orgId;
                                    item.ModifiedBy = baseHttpRequestContext.UserId;
                                    item.StatusId = (short)Status.Active;
                                });
                                invoice.Owing = invoice.TotalAmount;
                            }
                            if (invoice.InvoiceAdditionalDetails is not null)
                            {
                                //InvoiceAdditionalDetail invoiceAdditionalDetailDB = invoiceSummaryDB.InvoiceDetails?.ToList().Where(x=>x.Id==invoice.Id).Select(x=>x.InvoiceAdditionalDetails).FirstOrDefault();
                                //if(invoiceAdditionalDetailDB is not null)
                                //{
                                //    //invoice.InvoiceAdditionalDetails.Id = invoiceAdditionalDetailDB.Id;
                                //    invoice.InvoiceAdditionalDetails.CreatedDate = invoiceAdditionalDetailDB.CreatedDate;                                   

                                //}
                                invoice.InvoiceAdditionalDetails.CreatedDate = DateTime.UtcNow;
                                invoice.InvoiceAdditionalDetails.ModifiedBy = baseHttpRequestContext.UserId;
                                invoice.InvoiceAdditionalDetails.ModifiedDate = DateTime.UtcNow;
                                invoice.InvoiceAdditionalDetails.OrgId = baseHttpRequestContext.OrgId;

                            }
                        });
                        if (invoiceSummaryDB.InvoiceDetails is not null)
                        {
                            List<InvoiceDetail> lstInvoiceDetailsDB = invoiceSummaryDB.InvoiceDetails.ToList();
                            lstInvoiceDetailsDB.ForEach(invoice =>
                            {
                                invoice.ModifiedBy = baseHttpRequestContext.UserId;
                                invoice.InvoiceStatusId = (short)InvoiceStatus.Deleted;
                                invoice.ModifiedDate = DateTime.UtcNow;

                                if (invoice.InvoiceEpisodeItemAssocs is not null)
                                {
                                    invoice.InvoiceEpisodeItemAssocs.ToList().ForEach(item =>
                                    {
                                        item.ModifiedDate = DateTime.UtcNow;
                                        item.ModifiedBy = baseHttpRequestContext.UserId;
                                        item.StatusId = (short)Status.Deleted;
                                    });
                                }
                                invoiceSummary.InvoiceDetails.Add(invoice);
                            });



                        }
                    }
                    await _invoiceDAL.UpdateInvoiceTemplate(invoiceTemplate);
                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    apiResponse.Message = "Success";
                    apiResponse.Result = id;
                    return apiResponse;
                }
            }

            apiResponse.Message = "Failure";
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            apiResponse.Result = default(long);
            apiResponse.Errors.Add("Estimate template cannot be overridden at this time.");
            return apiResponse;
        }
        /// <summary>
        /// Method to partially update InvoiceDetail
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="id"></param>
        /// <param name="invoiceDetailUpdate"></param>
        /// <returns></returns>
        public async Task<ApiResponse<string>> PartialUpdateInvoiceDetail(BaseHttpRequestContext baseHttpRequestContext, long id, InvoiceDetailUpdate invoiceDetailUpdate)
        {
            ApiResponse<string> apiResponse = new();

            InvoiceDetail invoiceDetailDB = await _invoiceDAL.GetInvoiceDetailsFromId(id, baseHttpRequestContext.OrgId);
            if (invoiceDetailDB is not null && invoiceDetailUpdate is not null)
            {
                short invoiceStatusId = default(short);
                Dictionary<string, object> propertyDictionary = new();
                if (invoiceDetailUpdate.TotalDepositPaid != 0)
                {
                    decimal deposit = (decimal)((invoiceDetailDB.Deposit != null) ? invoiceDetailDB.Deposit + invoiceDetailUpdate.TotalDepositPaid : invoiceDetailUpdate.TotalDepositPaid);
                    if (invoiceDetailDB.InvoiceTypeId == (short)InvoiceType.Estimate)
                    {
                        propertyDictionary.Add("Deposit", deposit);

                        if (deposit > 0)
                            invoiceStatusId = (short)InvoiceStatus.Deposit;
                        else
                            invoiceStatusId = (short)InvoiceStatus.Valid;
                        propertyDictionary.Add("InvoiceStatusId", invoiceStatusId);

                    }
                    else
                    {
                        propertyDictionary.Add("Deposit", default(decimal));

                    }
                }
                if (invoiceDetailUpdate.TotalAmoutPaid > 0)
                {
                    invoiceDetailDB.TotalAmount = (invoiceDetailDB.TotalAmount is null) ? 0 : invoiceDetailDB.TotalAmount;
                    decimal? owing = (invoiceDetailDB.Owing != null) ? invoiceDetailDB.Owing - invoiceDetailUpdate.TotalAmoutPaid : invoiceDetailDB.TotalAmount - invoiceDetailUpdate.TotalAmoutPaid;
                    propertyDictionary.Add("Owing", owing);

                    if (invoiceDetailDB.InvoiceTypeId == (short)InvoiceType.Invoice)
                        if (owing != null && owing <= 0)
                            invoiceStatusId = (short)InvoiceStatus.Paid;
                        else
                            invoiceStatusId = (short)InvoiceStatus.Unpaid;
                    //else if (invoiceDetailDB.InvoiceTypeId == (short)InvoiceType.Estimate) { 

                    //}
                    //   invoiceStatusId = (short)InvoiceStatus.Deposit;
                    if (!propertyDictionary.ContainsKey("InvoiceStatusId"))
                    {
                        propertyDictionary.Add("InvoiceStatusId", invoiceStatusId);
                    }


                }
                else if (invoiceDetailUpdate.TotalAmoutPaid < 0)
                {
                    invoiceDetailDB.TotalAmount = (invoiceDetailDB.TotalAmount is null) ? 0 : invoiceDetailDB.TotalAmount;
                    decimal? owing = (invoiceDetailDB.Owing != null) ? invoiceDetailDB.Owing - invoiceDetailUpdate.TotalAmoutPaid : invoiceDetailDB.TotalAmount - invoiceDetailUpdate.TotalAmoutPaid;
                    propertyDictionary.Add("Owing", owing);

                    if (invoiceDetailDB.InvoiceTypeId == (short)InvoiceType.Invoice)
                        if (owing != null && owing <= 0)
                            invoiceStatusId = (short)InvoiceStatus.Paid;
                        else
                            invoiceStatusId = (short)InvoiceStatus.Unpaid;
                    //else if (invoiceDetailDB.InvoiceTypeId == (short)InvoiceType.Estimate) { 

                    //}
                    //   invoiceStatusId = (short)InvoiceStatus.Deposit;
                    if (!propertyDictionary.ContainsKey("InvoiceStatusId"))
                    {
                        propertyDictionary.Add("InvoiceStatusId", invoiceStatusId);
                    }


                }
                if (invoiceDetailUpdate.TotalInvoiceAmount != null)
                {
                    propertyDictionary.Add("TotalAmount", invoiceDetailUpdate.TotalInvoiceAmount);

                }
                if (invoiceDetailUpdate.Owing != null)
                {
                    propertyDictionary.Add("Owing", invoiceDetailUpdate.Owing);
                    if (!propertyDictionary.ContainsKey("InvoiceStatusId"))
                    {
                        if (invoiceDetailDB.InvoiceTypeId == (short)InvoiceType.Invoice)
                            if (invoiceDetailUpdate.Owing != null && invoiceDetailUpdate.Owing <= 0)
                                invoiceStatusId = (short)InvoiceStatus.Paid;
                            else
                                invoiceStatusId = (short)InvoiceStatus.Unpaid;
                        propertyDictionary.Add("InvoiceStatusId", invoiceStatusId);
                    }
                }
                if (invoiceDetailUpdate.TotalExcessAmountPaid != null)
                {

                    decimal? excessOwing = (invoiceDetailDB.ExcessOwing != null) ? invoiceDetailDB.ExcessOwing - invoiceDetailUpdate.TotalExcessAmountPaid : invoiceDetailDB.ExcessOwing;
                    propertyDictionary.Add("ExcessOwing", excessOwing);
                }


                int rows = await _invoiceDAL.PartialUpdateInvoiceDetail(propertyDictionary, baseHttpRequestContext.UserId, invoiceDetailDB);
                List<long> invoiceDetailIds = new List<long>();
                invoiceDetailIds.Add(id);
                await StoreInvoiceDetailMessage(baseHttpRequestContext.OrgCode, invoiceDetailIds, DateTime.Now, (int)InvoiceRequestType.InvoiceUpdated);
                await StoreInvoiceSummaryMessage(baseHttpRequestContext.OrgCode, invoiceDetailDB.InvoiceSummaryId, (int)InvoiceRequestType.InvoiceSummaryStatus);
                if (rows > 0)
                {
                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    apiResponse.Result = "Successfully updated";
                    apiResponse.Message = "Success";
                    return apiResponse;
                }
            }

            apiResponse.Errors.Add("Invoice/Estimate cannot be updated.");
            apiResponse.Message = "Failure";
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            return apiResponse;
        }

        public async Task<ApiResponse<InvoiceDetailView>> GetInvoiceDetail(long id, long summary_id, BaseHttpRequestContext baseHttpRequestContext, bool isIncludeClaimedItem)
        {
            int orgId = baseHttpRequestContext.OrgId;
            ApiResponse<InvoiceDetailView> apiResponse = new();
            InvoiceDetail invoiceDetail = await _invoiceDAL.GetInvoiceDetailsWithInvoiceItems(id, orgId, isIncludeClaimedItem);
            InvoiceDetailView invoiceDetailView = _mapper.Map<InvoiceDetail, InvoiceDetailView>(invoiceDetail);
            if (invoiceDetailView is null)
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = null;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("Invoice not found.");
            }
            else
            {
                if (invoiceDetailView.AppointmentDetailsId is not null && invoiceDetailView.AppointmentDetailsId > default(long))
                {
                    List<long> lstAppointmentIds = new() { (long)invoiceDetailView.AppointmentDetailsId };
                    List<AppointmentView> lstAppointmentDetails = await FetchAppointmentDetailsWithReferrals(lstAppointmentIds, baseHttpRequestContext);
                    if (lstAppointmentIds is not null && lstAppointmentIds.Count > 0)
                    {

                        invoiceDetailView.AppointmentDetails = lstAppointmentDetails.Where(a => a.Id == invoiceDetailView.AppointmentDetailsId).FirstOrDefault();

                        //if (appDetails is not null)
                        //{
                        //    invoiceDetailView.AppointmentDetails = _mapper.Map<AppointmentList, AppointmentView>(appDetails);
                        //}


                    }
                }

                List<ListMbsData> lstMbsData = new();
                List<long> mbsItemNumbers = invoiceDetailView.InvoiceEpisodeItemAssocs.Where(x => x.EpisodeTypeId == (short)EpisodeTypes.MBS && x.AdjustmentTypeId == null && x.ItemNumber > default(long)).Select(x => x.ItemNumber).ToList();
                if (mbsItemNumbers is not null && mbsItemNumbers.Any())
                    lstMbsData = await FetchMBSItemDetails(mbsItemNumbers, baseHttpRequestContext);

                List<long> customItemIds = invoiceDetailView.InvoiceEpisodeItemAssocs.Where(x => x.EpisodeTypeId != (short)EpisodeTypes.MBS && x.AdjustmentTypeId == null && x.ItemNumber > default(long)).Select(x => x.ItemDetailsId).ToList();
                List<ListEpisodeItemDetail> lstCustomData = new();
                if (customItemIds is not null && customItemIds.Any())
                    lstCustomData = await FetchCustomItemDetails(customItemIds, baseHttpRequestContext);
                List<long> lstProviderIds = new();
                if (invoiceDetailView.ProviderId is not null && invoiceDetailView.ProviderId > default(long))
                {
                    lstProviderIds.Add((long)invoiceDetailView.ProviderId);

                }
                if (invoiceDetailView.PayeeProviderId is not null && invoiceDetailView.PayeeProviderId > default(long))
                {
                    lstProviderIds.Add((long)invoiceDetailView.PayeeProviderId);

                }
                if (lstProviderIds is not null && lstProviderIds.Count > 0)
                {
                    List<UserInfoView> lstProviderDetails = await FetchProviderDetails(lstProviderIds, baseHttpRequestContext);
                    if (lstProviderDetails is not null && lstProviderDetails.Any())
                    {
                        invoiceDetailView.ProviderDetails = (invoiceDetailView.ProviderId is not null) ? lstProviderDetails.Where(a => a.Id == invoiceDetailView.ProviderId).FirstOrDefault() : null;
                        invoiceDetailView.PayeeProviderDetails = (invoiceDetailView.PayeeProviderId is not null) ? lstProviderDetails.Where(a => a.Id == invoiceDetailView.PayeeProviderId).FirstOrDefault() : null;

                    }
                }

                if (invoiceDetailView.InvoiceEpisodeItemAssocs is not null)
                {
                    List<InvoiceEpisodeItemViewAssoc> lstItems = invoiceDetailView.InvoiceEpisodeItemAssocs.ToList();
                    if (lstItems is not null && lstItems.Count > 0)
                    {
                        lstItems.ForEach(item =>
                        {
                            if (item.InvoiceEpisodeItemAssocsId is not null && item.InvoiceEpisodeItemAssocsId > 0)
                            {
                                InvoiceEpisodeItemViewAssoc parentEntry = lstItems.Where(x => x.Id == item.InvoiceEpisodeItemAssocsId).FirstOrDefault();
                                if (parentEntry is not null)
                                {
                                    parentEntry.Quantity -= item.Quantity;
                                    parentEntry.Fee -= item.Fee;
                                    parentEntry.Oop -= item.Oop;
                                    parentEntry.Gst -= item.Gst;
                                }
                            }

                            if (item.EpisodeTypeId == (short)EpisodeTypes.MBS && item.ItemNumber > default(long) && lstMbsData is not null && lstMbsData.Any())
                            {
                                ListMbsData mbsData = lstMbsData.Where(x => x.ItemNum == item.ItemNumber).FirstOrDefault();

                                ListEpisodeItemDetail episodeItemDetail = _mapper.Map<ListMbsData, ListEpisodeItemDetail>(mbsData);
                                item.EpisodeItemDetail = episodeItemDetail;
                            }
                            if (item.EpisodeTypeId != (short)EpisodeTypes.MBS && item.ItemDetailsId > default(long) && lstCustomData is not null && lstCustomData.Any())
                            {
                                item.EpisodeItemDetail = lstCustomData.Where(x => x.Id == item.ItemDetailsId).FirstOrDefault();
                            }
                            if (item.InvoiceEpisodeItemsIndicatorAssocs is not null && item.InvoiceEpisodeItemsIndicatorAssocs.Count > 0)
                            {

                                item.InvoiceEpisodeItemsIndicatorAssocs = item.InvoiceEpisodeItemsIndicatorAssocs.Where(x => x.StatusId == (short)Status.Active).ToList();
                                if (item.InvoiceEpisodeItemsIndicatorAssocs is not null && item.InvoiceEpisodeItemsIndicatorAssocs.Any())
                                {
                                    item.InvoiceEpisodeItemsIndicatorAssocs.ToList().ForEach(indicator =>
                                    {
                                        indicator.InvoiceItemIndicatorType = (indicator.InvoiceItemIndicatorTypeId is null) ? string.Empty : EnumExtensions.GetDescription((InvoiceItemIndicatorType)indicator.InvoiceItemIndicatorTypeId);
                                    });
                                }
                            }

                        });
                        lstItems.RemoveAll(x => (x.InvoiceEpisodeItemAssocsId != null && x.StatusId == (short)Status.Active && x.AdjustmentTypeId != null && x.AdjustmentTypeId == (short)AdjustmentType.Refund) || x.Quantity == 0);
                        invoiceDetailView.InvoiceEpisodeItemAssocs = lstItems;

                    }
                }
                apiResponse.Result = invoiceDetailView;
                apiResponse.StatusCode = StatusCodes.Status200OK;
            }
            return apiResponse;
        }

        public async Task<ApiResponse<InvoiceTemplateTagsData>> GetInvoiceSummaryTagData(long id, BaseHttpRequestContext baseHttpRequestContext)
        {
            int orgId = baseHttpRequestContext.OrgId;
            ApiResponse<InvoiceTemplateTagsData> apiResponse = new ApiResponse<InvoiceTemplateTagsData>();
            InvoiceSummary invoiceSummary = await _invoiceDAL.GetInvoiceSummaryWithInvoices(id, orgId);
            InvoiceSummaryView invoiceSummaryView = _mapper.Map<InvoiceSummary, InvoiceSummaryView>(invoiceSummary);

            if (invoiceSummaryView is null)
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = null;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("Invoice not found.");
            }
            else
            {
                string templateName = string.Empty;
                long? firstChildAppId = null;
                if (invoiceSummaryView.InvoiceTemplatesId is not null)
                {
                    InvoiceTemplate invoiceTemplate = await _invoiceDAL.GetInvoiceTemplateFromId((long)invoiceSummaryView.InvoiceTemplatesId, baseHttpRequestContext.OrgId);
                    templateName = invoiceTemplate?.Name;
                }

                if (invoiceSummaryView.InvoiceDetails is not null)
                {
                    firstChildAppId = invoiceSummary.InvoiceDetails?.First().AppointmentDetailsId;
                    var invoiceTemplateTagsData = new InvoiceTemplateTagsData();
                    invoiceTemplateTagsData.invoiceSummaryTagDatas = new List<InvoiceSummaryTagData>();
                    List<InvoiceDetailView> InvoiceDetailView = invoiceSummaryView.InvoiceDetails.Where(x => x.InvoiceStatusId != (short)InvoiceStatus.Void && x.InvoiceStatusId != (short)InvoiceStatus.Deleted).ToList();

                    if (InvoiceDetailView.Any())
                    {
                        //FirstServiceProvider
                        List<long> lstProviderIds = InvoiceDetailView.Where(x => x.ProviderId != null && x.ProviderId > 0).Select(x => (long)x.ProviderId).ToList();
                        lstProviderIds.AddRange(InvoiceDetailView.Where(x => x.PayeeProviderId != null && x.PayeeProviderId > 0).Select(x => (long)x.PayeeProviderId).ToList());
                        List<UserInfoView> lstProviderDetails = await FetchProviderDetails(lstProviderIds, baseHttpRequestContext);

                        //Get CompanyDetails Info
                        List<int> lstCompanyIds = InvoiceDetailView.Where(x => x.CompanyDetailsId != null && x.CompanyDetailsId > 0).Select(x => (int)x.CompanyDetailsId).ToList();
                        var companyDetails = await ListCompanyDetailInfo(lstCompanyIds, baseHttpRequestContext);

                        List<long> lstAppointmentIds = InvoiceDetailView.Where(x => x.AppointmentDetailsId != null && x.AppointmentDetailsId > 0).Select(x => (long)x.AppointmentDetailsId).ToList();
                        List<AppointmentList> lstAppointmentDetails = await FetchAppointmentDetails(lstAppointmentIds, baseHttpRequestContext);
                        invoiceTemplateTagsData.AdmissionDate = null;
                        invoiceTemplateTagsData.AppointmentDate = null;
                        if (firstChildAppId is not null && lstAppointmentDetails is not null && lstAppointmentDetails.Any())
                        {
                            templateName = lstAppointmentDetails?.Where(x => x.Id == firstChildAppId).Select(x => x.Notes).FirstOrDefault();
                            var AppointmentDate = (lstAppointmentDetails != null && lstAppointmentDetails.Any(a => a.Id == firstChildAppId)) ? lstAppointmentDetails.FirstOrDefault(a => a.Id == firstChildAppId).DateOfAppointment : null;
                            var appointmentTime = (lstAppointmentDetails != null && lstAppointmentDetails.Any(a => a.Id == firstChildAppId)) ? lstAppointmentDetails.FirstOrDefault(a => a.Id == firstChildAppId).StartTime : null;
                            invoiceTemplateTagsData.AppointmentDate = AppointmentDate.HasValue ? AppointmentDate.Value.Add(appointmentTime.Value) : null;
                            invoiceTemplateTagsData.AdmissionDate = lstAppointmentDetails?.Where(x => x.Id == firstChildAppId).Select(x => x.AdmissionDate).FirstOrDefault();

                        }
                        //FirstAccountHolder
                        invoiceTemplateTagsData.AccountHolder = EnumExtensions.GetDescription((AccountHolderTypes)InvoiceDetailView.FirstOrDefault().AccountHolderTypeId);

                        OrganisationView orgDetails = await FetchMasterCompanyDetails(baseHttpRequestContext);
                        bool isWindows = System.Runtime.InteropServices.RuntimeInformation
                                                 .IsOSPlatform(OSPlatform.Windows);
                        string timeZone = (isWindows) ? orgDetails.WindowsTimeZoneData : orgDetails.LinuxTimeZoneData;

                        invoiceTemplateTagsData.ServiceProvider = lstProviderDetails.Any() ? lstProviderDetails.FirstOrDefault().FirstName + " " + lstProviderDetails.FirstOrDefault().SurName : " ";

                        invoiceTemplateTagsData.EstimateNumber = invoiceSummary.RecordId.HasValue ? invoiceSummary.RecordId.Value : 0;
                        invoiceTemplateTagsData.EstimateDate = DateTimeConversion.SetTimeZoneToDateTIme(invoiceSummary.CreatedDate, timeZone);
                        //invoiceTemplateTagsData.AppointmentDate = null;
                        //invoiceTemplateTagsData.AdmissionDate = null;
                        invoiceTemplateTagsData.ProcedureDetails = string.IsNullOrWhiteSpace(templateName) ? null : templateName; //(string.IsNullOrWhiteSpace(templateName) || templateName?.Length <= 40) ? templateName : templateName.Substring(0, 37) + "...";
                        //invoiceTemplateTagsData.AdmissionDate = null;
                        invoiceTemplateTagsData.InvoiceTypeId = invoiceSummary.InvoiceTypeId;

                        InvoiceDetailView.ForEach(x =>
                        {
                            var providerName = lstProviderDetails.Any() ? lstProviderDetails.Where(p => p.Id == x.ProviderId).Select(s => s.FirstName + " " + s.SurName + " ").FirstOrDefault() : null;
                            var company = companyDetails.Where(c => c.Id == x.CompanyDetailsId).Select(s => s.Name).FirstOrDefault();
                            var companyInfo = companyDetails.Where(c => c.Id == x.CompanyDetailsId).FirstOrDefault();
                            //invoiceTemplateTagsData.ServiceProvider = !String.IsNullOrEmpty(company) ? company : providerName;
                            if (companyInfo != null && companyInfo.CategoryTypeId == (short)ICCategoryType.Hospital && companyInfo.CompanyTypeId == (short)CompanyType.InternalCompany)
                                providerName = company;
                            else
                                providerName = string.IsNullOrEmpty(providerName) ? company : providerName + " - " + company;
                            invoiceTemplateTagsData.invoiceSummaryTagDatas.Add(new InvoiceSummaryTagData
                            {
         

                                Description = providerName,//string.Format("{0} {1}", providerName, string.IsNullOrEmpty(providerName) ? company : " - " + company),
                                OopValue = x.InvoiceEpisodeItemAssocs.ToList().Sum(i => i.Oop),
                                FeeValue = x.InvoiceEpisodeItemAssocs.ToList().Sum(i => i.Fee + i.Gst),
                                MedicareValue = x.InvoiceEpisodeItemAssocs.ToList().Sum(i => i.Medicare),
                                HealthFund = x.InvoiceEpisodeItemAssocs.ToList().Sum(i => i.Fund),
                                BalanceDue = x.Owing.HasValue ? x.Owing.Value : 0,
                            });
                        });
                    }
                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    apiResponse.Message = "Success";
                    apiResponse.Result = invoiceTemplateTagsData;
                }
            }

            return apiResponse;
        }

        private async Task<List<CompanyDetailInfo>> ListCompanyDetailInfo(List<int> companyIds, BaseHttpRequestContext baseHttpRequestContext)
        {
            string filter = "{ CompanyDetailId:[" + string.Join(",", companyIds) + "]}";
            string encodedFilter = Base64EncoderHelper.EncodeQueryModelFilterParam(filter);
            List<CompanyDetailInfo> companyDetailInfos = new List<CompanyDetailInfo>();
            string companySeviceUrl = _appSettings.ApiUrls["CompanyServiceUrl"] + "/company/company_details_info?f=" + encodedFilter;
            RestClient restClientCompany = new RestClient(companySeviceUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);

            var data = await restClientCompany.GetAsync<ApiResponse<List<CompanyDetailInfo>>>(companySeviceUrl);
            return data.Result;

        }

        public async Task<ApiResponse<InvoiceTagsDetails>> GetInvoiceDetailedSummaryTags(long id, BaseHttpRequestContext baseHttpRequestContext)
        {
            int orgId = baseHttpRequestContext.OrgId;
            ApiResponse<InvoiceTagsDetails> apiResponse = new ApiResponse<InvoiceTagsDetails>();
            InvoiceTagsDetails invoiceTagsDetails = new InvoiceTagsDetails();
            invoiceTagsDetails.InvoiceTagsDetail = new List<InvoiceTemplateTagsData>();
            InvoiceSummary invoiceSummary = await _invoiceDAL.GetInvoiceSummaryWithInvoices(id, orgId);
            InvoiceSummaryView invoiceSummaryView = _mapper.Map<InvoiceSummary, InvoiceSummaryView>(invoiceSummary);

            if (invoiceSummaryView is null)
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = null;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("Invoice not found.");
                return apiResponse;
            }
            else
            {
                string templateName = string.Empty;
                long? firstChildAppId = null;
                if (invoiceSummaryView.InvoiceTemplatesId is not null)
                {
                    InvoiceTemplate invoiceTemplate = await _invoiceDAL.GetInvoiceTemplateFromId((long)invoiceSummaryView.InvoiceTemplatesId, baseHttpRequestContext.OrgId);
                    templateName = invoiceTemplate?.Name;
                }
                if (invoiceSummaryView.InvoiceDetails is not null)
                {
                    firstChildAppId = invoiceSummary.InvoiceDetails?.First().AppointmentDetailsId;

                    List<InvoiceDetailView> InvoiceDetailView = invoiceSummaryView.InvoiceDetails.Where(x => x.InvoiceStatusId != (short)InvoiceStatus.Void && x.InvoiceStatusId != (short)InvoiceStatus.Deleted).ToList();

                    if (InvoiceDetailView.Any())
                    {
                        //FirstServiceProvider
                        List<long> lstProviderIds = InvoiceDetailView.Where(x => x.ProviderId != null && x.ProviderId > 0).Select(x => (long)x.ProviderId).ToList();
                        List<UserInfoView> lstProviderDetails = await FetchProviderDetails(lstProviderIds, baseHttpRequestContext);

                        //Get CompanyDetails Info
                        List<int> lstCompanyIds = InvoiceDetailView.Where(x => x.CompanyDetailsId != null && x.CompanyDetailsId > 0).Select(x => (int)x.CompanyDetailsId).ToList();
                        var companyDetails = await ListCompanyDetailInfo(lstCompanyIds, baseHttpRequestContext);

                        List<long> lstAppointmentIds = InvoiceDetailView.Where(x => x.AppointmentDetailsId != null && x.AppointmentDetailsId > 0).Select(x => (long)x.AppointmentDetailsId).ToList();
                        List<AppointmentList> lstAppointmentDetails = await FetchAppointmentDetails(lstAppointmentIds, baseHttpRequestContext);
                        if (firstChildAppId is not null && lstAppointmentDetails is not null && lstAppointmentDetails.Any())
                        {
                            templateName = lstAppointmentDetails?.Where(x => x.Id == firstChildAppId).Select(x => x.Notes).FirstOrDefault();
                        }
                        var invoiceTemplateTagsData = new InvoiceTemplateTagsData();
                        //FirstAccountHolder
                        invoiceTemplateTagsData.AccountHolder = EnumExtensions.GetDescription((AccountHolderTypes)InvoiceDetailView.FirstOrDefault().AccountHolderTypeId);

                        List<long> customeEstimateNumbers = new List<long>();
                        List<long> mbsEstimateNumbers = new List<long>();
                        List<long> accommodationEstimateNumbers = new();
                        InvoiceDetailView.ForEach(x =>
                        {
                            customeEstimateNumbers.AddRange(x.InvoiceEpisodeItemAssocs.Where(item => (item.EpisodeTypeId == (short)EpisodeTypes.Custom_Product || item.EpisodeTypeId == (short)EpisodeTypes.Custom_Procedure || item.EpisodeTypeId == (short)EpisodeTypes.Custom_Surgical)).Select(a => a.ItemDetailsId));
                            mbsEstimateNumbers.AddRange(x.InvoiceEpisodeItemAssocs.Where(item => item.EpisodeTypeId == (short)EpisodeTypes.MBS).Select(a => a.ItemNumber));
                            accommodationEstimateNumbers.AddRange(x.InvoiceEpisodeItemAssocs.Where(item => item.EpisodeTypeId == (short)EpisodeTypes.Accommodation).Select(a => a.ItemDetailsId));
                        });

                        var customeEstimateitemsDetails = await FetchCustomItemDetails(customeEstimateNumbers, baseHttpRequestContext);
                        var mbsItemsDetails = await FetchMBSItemDetails(mbsEstimateNumbers, baseHttpRequestContext);
                        var accomItemsDetails = await FetchAccommodationItemDetails(accommodationEstimateNumbers, baseHttpRequestContext);

                        OrganisationView orgDetails = await FetchMasterCompanyDetails(baseHttpRequestContext);
                        bool isWindows = System.Runtime.InteropServices.RuntimeInformation
                                                 .IsOSPlatform(OSPlatform.Windows);
                        string timeZone = (isWindows) ? orgDetails.WindowsTimeZoneData : orgDetails.LinuxTimeZoneData;

                        invoiceTemplateTagsData.ServiceProvider = lstProviderDetails.Any() ? lstProviderDetails.FirstOrDefault().FirstName + " " + lstProviderDetails.FirstOrDefault().SurName : " ";
                        invoiceTemplateTagsData.EstimateNumber = invoiceSummary.RecordId.HasValue ? invoiceSummary.RecordId.Value : 0;
                        invoiceTemplateTagsData.EstimateDate = DateTimeConversion.SetTimeZoneToDateTIme(invoiceSummary.CreatedDate, timeZone);
                        invoiceTemplateTagsData.AppointmentDate = null;
                        invoiceTemplateTagsData.AdmissionDate = null;
                        invoiceTemplateTagsData.ProcedureDetails = (!string.IsNullOrWhiteSpace(templateName)) ? templateName : null;// templateName.Substring(0, 37) + "..."; ;
                        invoiceTemplateTagsData.InvoiceTypeId = invoiceSummary.InvoiceTypeId;

                        InvoiceDetailView.ForEach(x =>
                        {
                            InvoiceTemplateTagsData invoiceTemplateTagsData = new InvoiceTemplateTagsData();
                            invoiceTemplateTagsData.invoiceSummaryTagDatas = new List<InvoiceSummaryTagData>();
                            var providerName = lstProviderDetails != null ? lstProviderDetails.Where(p => p.Id == x.ProviderId).Select(s => s.FirstName + " " + s.SurName + " ").FirstOrDefault() : null;
                            var company = companyDetails.Where(c => c.Id == x.CompanyDetailsId).Select(s => s.Name).FirstOrDefault();
                            var companyInfo = companyDetails.Where(c => c.Id == x.CompanyDetailsId).FirstOrDefault();
                            if(companyInfo!=null && companyInfo.CategoryTypeId==(short)ICCategoryType.Hospital && companyInfo.CompanyTypeId==(short)CompanyType.InternalCompany)
                                invoiceTemplateTagsData.ServiceProvider = !String.IsNullOrEmpty(company) ? company : providerName;
                            else
                                invoiceTemplateTagsData.ServiceProvider = String.IsNullOrEmpty(providerName) ? company : providerName;

                            invoiceTemplateTagsData.EstimateNumber = invoiceSummary.RecordId.HasValue ? invoiceSummary.RecordId.Value : 0;
                            invoiceTemplateTagsData.EstimateDate = DateTimeConversion.SetTimeZoneToDateTIme(invoiceSummary.CreatedDate, timeZone);
                            var AppointmentDate = (lstAppointmentDetails != null && lstAppointmentDetails.Any(a => a.Id == x.AppointmentDetailsId)) ? lstAppointmentDetails.FirstOrDefault(a => a.Id == x.AppointmentDetailsId).DateOfAppointment : null;
                            var appointmentTime = (lstAppointmentDetails != null && lstAppointmentDetails.Any(a => a.Id == x.AppointmentDetailsId)) ? lstAppointmentDetails.FirstOrDefault(a => a.Id == x.AppointmentDetailsId).StartTime : null;
                            invoiceTemplateTagsData.AppointmentDate = AppointmentDate.HasValue ? AppointmentDate.Value.Add(appointmentTime.Value) : null;
                            invoiceTemplateTagsData.AdmissionDate = (lstAppointmentDetails != null && lstAppointmentDetails.Any(a => a.Id == x.AppointmentDetailsId)) ? lstAppointmentDetails.FirstOrDefault(a => a.Id == x.AppointmentDetailsId).AdmissionDate : null;
                            invoiceTemplateTagsData.ProcedureDetails = string.IsNullOrWhiteSpace(templateName) ? null : templateName; //(string.IsNullOrWhiteSpace(templateName) || templateName?.Length <= 40) ? templateName : templateName.Substring(0, 37) + "..."; ;
                            invoiceTemplateTagsData.AppointmentDetailsId = x.AppointmentDetailsId;
                            invoiceTemplateTagsData.InvoiceTypeId = x.InvoiceTypeId;

                            x.InvoiceEpisodeItemAssocs.ToList().ForEach(i =>
                            {
                                string description = string.Empty;
                                if (i.EpisodeTypeId == (short)EpisodeTypes.Accommodation)
                                {
                                    if (accomItemsDetails != null && accomItemsDetails.Count > 0)
                                    {
                                        MedicalContractorBandAssocInfo bandAssocInfo = accomItemsDetails.Where(c => c.Id == i.ItemDetailsId).FirstOrDefault();
                                        if (bandAssocInfo != null)
                                        {
                                            description = bandAssocInfo.Description;
                                            if (bandAssocInfo.MedicalContBandTypeId == (short)MedicalContBandType.Accommodation_Day_Stay)
                                            {
                                                description = "(Day) " + description;
                                            }
                                            else if (bandAssocInfo.MedicalContBandTypeId == (short)MedicalContBandType.Accommodation_Overnight)
                                            {
                                                description = "(Overnight) " + description;
                                            }

                                        }
                                    }

                                }
                                else if (i.EpisodeTypeId != (short)EpisodeTypes.MBS)
                                    description = (customeEstimateitemsDetails != null && customeEstimateitemsDetails.Any(c => c.Id == i.ItemDetailsId)) ? customeEstimateitemsDetails.Where(c => c.Id == i.ItemDetailsId).FirstOrDefault().Description : string.Empty;
                                else
                                    description = (mbsItemsDetails != null && mbsItemsDetails.Any(c => c.ItemNum == i.ItemNumber)) ? mbsItemsDetails.Where(c => c.ItemNum == i.ItemNumber).FirstOrDefault().Description : string.Empty;

                                if (i.ItemNumber == default(long) && i.AdjustmentTypeId != null && i.AdjustmentTypeId == (short)AdjustmentType.Write_off)
                                {
                                    description = EnumExtensions.GetDescription((AdjustmentType)AdjustmentType.Write_off);
                                    i.Fee = (i.Fee is not null && i.Fee.HasValue) ? i.Fee * -1 : 0;
                                }

                                InvoiceSummaryTagData invoiceSummary = new InvoiceSummaryTagData()
                                {
                                    Description = description.Length > 30 ? description.Substring(0, 30) + "..." : description,
                                    OopValue = i.Oop,
                                    FeeValue = i.Fee + i.Gst,
                                    MedicareValue = i.Medicare,
                                    HealthFund = i.Fund,
                                    BalanceDue = x.Owing.HasValue ? x.Owing.Value : 0,
                                    Quantity = i.Quantity,
                                    ItemNumner = i.ItemNumber
                                };

                                invoiceTemplateTagsData.invoiceSummaryTagDatas.Add(invoiceSummary);
                            });

                            invoiceTagsDetails.InvoiceTagsDetail.Add(invoiceTemplateTagsData);
                        });
                    }

                }
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";
                apiResponse.Result = invoiceTagsDetails;
                return apiResponse;
            }
        }

        public async Task<ApiResponse<InvoiceDepositTagsDetails>> GetInvoiceDepositTagsDetails(long id, BaseHttpRequestContext baseHttpRequestContext)
        {
            int orgId = baseHttpRequestContext.OrgId;
            ApiResponse<InvoiceDepositTagsDetails> apiResponse = new ApiResponse<InvoiceDepositTagsDetails>();
            InvoiceDepositTagsDetails invoiceDepositTagsDetails = new InvoiceDepositTagsDetails();
            invoiceDepositTagsDetails.PaymentDetailsTagsDatas = new List<PaymentDetailsTagsData>();
            var invoiceResponse = await GetInvoiceDetail(id, 0, baseHttpRequestContext, true);

            if (invoiceResponse == null)
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failed";
                apiResponse.Errors.Add("Invoice Details not found");
                return apiResponse;
            }
            var invoiceDetail = invoiceResponse.Result;

            List<long> lstAppointmentIds = new List<long>();
            if (invoiceDetail.AppointmentDetailsId.HasValue)
                lstAppointmentIds.Add(invoiceDetail.AppointmentDetailsId.Value);

            List<AppointmentList> lstAppointmentDetails = await FetchAppointmentDetails(lstAppointmentIds, baseHttpRequestContext);

            if (lstAppointmentDetails != null && lstAppointmentDetails.Count > 0)
            {
                var appointment = lstAppointmentDetails.FirstOrDefault();
                invoiceDepositTagsDetails.AppointmentDetails = invoiceDetail.AppointmentDetailsId;
                invoiceDepositTagsDetails.AppointmentDate = appointment.DateOfAppointment.HasValue ? appointment.DateOfAppointment.Value : null;
                invoiceDepositTagsDetails.AppointmentTime = appointment.StartTime.HasValue ? appointment.StartTime.Value : null;
                invoiceDepositTagsDetails.AppointmentServiceProvider = appointment.UserDetailsInfo.FirstName + " " + appointment.UserDetailsInfo.SurName;
                invoiceDepositTagsDetails.AppointmentInternalEntity = appointment.CompanyDetailInfo != null ? appointment.CompanyDetailInfo.Name : "";
            }
            if (invoiceDetail.InvoiceAdditionalDetails is not null && invoiceDetail.InvoiceAdditionalDetails.InPatientCompanyDetailsId is not null)
            {
                invoiceDepositTagsDetails.InPatientCompanyDetailsId = invoiceDetail.InvoiceAdditionalDetails.InPatientCompanyDetailsId;
            }
            invoiceDepositTagsDetails.EstimateNumber = invoiceDetail.RecordId.HasValue ? invoiceDetail.RecordId.Value : 0;
            invoiceDepositTagsDetails.BalanceOutstanding = invoiceDetail.Owing.HasValue ? invoiceDetail.Owing.Value : 0;
            invoiceDepositTagsDetails.CompanyDetailsId = invoiceDetail.CompanyDetailsId.HasValue ? invoiceDetail.CompanyDetailsId.Value : null;
            invoiceDepositTagsDetails.UserDetailsId = invoiceDetail.ProviderId.HasValue ? invoiceDetail.ProviderId.Value : null;
            invoiceDepositTagsDetails.TotalEstimate = invoiceDetail.TotalAmount.HasValue ? invoiceDetail.TotalAmount.Value : 0;
            //Get Invoice Payment Details
            ApiResponse<QueryResultList<ListPaymentDetail>> paymentDetails = new ApiResponse<QueryResultList<ListPaymentDetail>>();
            string filter = "{ InvoiceDetailsId:[" + invoiceDetail.Id + "]}";
            string encodedFilter = Base64EncoderHelper.EncodeQueryModelFilterParam(filter);
            string InvoicePaymentServiceUrl = _appSettings.ApiUrls["InvoicePaymentServiceUrl"] + "/invoicepayment/payment_details?pn=1&ps=100&sot=id&soo=desc&f=" + encodedFilter;
            RestClient restClientPayment = new RestClient(InvoicePaymentServiceUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            paymentDetails = await restClientPayment.GetAsync<ApiResponse<QueryResultList<ListPaymentDetail>>>(InvoicePaymentServiceUrl);

            if (paymentDetails == null || paymentDetails.Result.ItemRecords == null)
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failed";
                apiResponse.Errors.Add("payment Details not found");
                return apiResponse;
            }

            if (paymentDetails != null && paymentDetails.Result.ItemRecords != null && paymentDetails.Result.ItemRecords.Count > 0)
            {
                var paymentList = paymentDetails.Result.ItemRecords.ToList();
                var refundAmount = default(decimal);
                paymentList.ForEach(x =>
                {
                    if (x.StatusId == (short)Status.Active)
                    {
                        if (x.PaymentTypeId == (short)AdjustmentType.Refund && x.DRAmount != null)
                            refundAmount = refundAmount + (decimal)x.DRAmount;

                        PaymentDetailsTagsData detailsTagsData = new PaymentDetailsTagsData()
                        {
                            AmountPaid = x.CRAmount.HasValue ? x.CRAmount.Value : (x.DRAmount.HasValue ? x.DRAmount.Value * -1 : 0),
                            DatePaid = x.PaymentDate,
                            PaymentType = x.PaymentMethod,
                            PaymentDetailsID = x.Id,
                        };

                        invoiceDepositTagsDetails.PaymentDetailsTagsDatas.Add(detailsTagsData);
                    }

                });
                invoiceDepositTagsDetails.TotalRefund = refundAmount;
            }

            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result = invoiceDepositTagsDetails;
            return apiResponse;
        }
        /// <summary>
        /// Method for invoice adjustment
        /// </summary>
        /// <param name="id"></param>
        /// <param name="inputPaymentAdjustmentDetail"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long>> InvoiceAdjustment(long id, InputPaymentAdjustmentDetail inputPaymentAdjustmentDetail, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<long> apiResponse = new();

            if (inputPaymentAdjustmentDetail is not null)
            {
                if (inputPaymentAdjustmentDetail.AdjustmentTypeId == (short)AdjustmentType.Refund)
                {
                    InvoiceDetail invoiceDetail = await _invoiceDAL.GetInvoiceDetailsFromId(id, baseHttpRequestContext.OrgId);

                    return await ProcessRefund(invoiceDetail, inputPaymentAdjustmentDetail, baseHttpRequestContext, inputPaymentAdjustmentDetail.PatientDetailsId);
                }
                else if (inputPaymentAdjustmentDetail.AdjustmentTypeId == (short)AdjustmentType.Write_off)
                {
                    return await ProcessWriteOff(id, inputPaymentAdjustmentDetail, baseHttpRequestContext, inputPaymentAdjustmentDetail.PatientDetailsId);

                }
            }
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            apiResponse.Result = default(long);
            apiResponse.Errors.Add("Invoice Adjustment cannot be performed at this time.");
            return apiResponse;
        }

        private async Task<ApiResponse<long>> ProcessWriteOff(long invoicedetailsId, InputPaymentAdjustmentDetail inputPaymentAdjustmentDetail, BaseHttpRequestContext baseHttpRequestContext, long? patientId)
        {
            ApiResponse<long> apiResponse = new();
            decimal totalFee = 0;
            decimal totalGst = 0;
            decimal newOwingAmt = 0;
            decimal newTotalAmtPaid = 0;
            decimal totalFeePaid = 0;
            decimal totalGstPaid = 0;
            InvoiceDetail invoiceDetail = await _invoiceDAL.GetInvoiceDetailsWithOnlyInvoiceItems(invoicedetailsId, baseHttpRequestContext.OrgId);

            if (invoiceDetail is not null)
            {
                decimal totalAmountOrig = (invoiceDetail.TotalAmount == null) ? 0 : (decimal)invoiceDetail.TotalAmount;
                decimal owingOrig = (invoiceDetail.Owing == null) ? 0 : (decimal)invoiceDetail.Owing;
                decimal totalAmountPaidOrig = totalAmountOrig - owingOrig;
                invoiceDetail.InvoiceEpisodeItemAssocs?.ToList().ForEach(item =>
                {
                    if (item.AdjustmentTypeId is not null && item.AdjustmentTypeId == (short)AdjustmentType.Refund)
                    {
                        totalFee -= (item.Fee == null) ? 0 : (decimal)item.Fee;
                        totalGst -= (item.Gst == null) ? 0 : (decimal)item.Gst;
                        totalGstPaid -= (item.GstPaid == null) ? 0 : (decimal)item.GstPaid;
                        totalFeePaid -= (item.BenefitPaid == null) ? 0 : (decimal)item.BenefitPaid;//inc Gst
                    }
                    else
                    {
                        totalFee += (item.Fee == null) ? 0 : (decimal)item.Fee;
                        totalGst += (item.Gst == null) ? 0 : (decimal)item.Gst;
                        totalGstPaid += (item.GstPaid == null) ? 0 : (decimal)item.GstPaid;
                        totalFeePaid += (item.BenefitPaid == null) ? 0 : (decimal)item.BenefitPaid;//inc Gst
                    }
                });

                decimal totalFeeWriteOff = owingOrig;
                newOwingAmt = owingOrig - totalFeeWriteOff;
                if (totalFeeWriteOff > 0)
                {
                    DateTime? CompanyDateTimeNow = null;
                    bool isWindows = System.Runtime.InteropServices.RuntimeInformation
                                                        .IsOSPlatform(OSPlatform.Windows);
                    OrganisationView orgDetails = await FetchMasterCompanyDetails(baseHttpRequestContext);
                    short timeZoneId = (orgDetails is null || orgDetails.TimeZoneMasterId <= 0) ? default(short) : orgDetails.TimeZoneMasterId;
                    if (timeZoneId > 0)
                    {
                        CompanyDateTimeNow = GetDateFromOrganisation(orgDetails, isWindows);
                    }
                    InvoiceEpisodeItemAssoc itemAssoc = new();

                    itemAssoc.OrgId = baseHttpRequestContext.OrgId;
                    itemAssoc.CreatedDate = DateTime.UtcNow;
                    itemAssoc.CreatedBy = baseHttpRequestContext.UserId;
                    itemAssoc.ModifiedBy = baseHttpRequestContext.UserId;
                    itemAssoc.StatusId = (short)Status.Active;
                    itemAssoc.AdjustmentTypeId = (short)AdjustmentType.Write_off;
                    itemAssoc.Gst = totalGst - totalGstPaid;
                    itemAssoc.Fee = totalFeeWriteOff - itemAssoc.Gst;
                    itemAssoc.InvoiceDetailsId = invoicedetailsId;

                    itemAssoc.Oop = 0;
                    itemAssoc.Quantity = 1;
                    PaymentDetail payment = new();
                    if (inputPaymentAdjustmentDetail.PaymentDetail is not null && inputPaymentAdjustmentDetail.PaymentDetail is not null)
                    {
                        inputPaymentAdjustmentDetail.PaymentDetail.InvoiceDetailsId = invoicedetailsId;
                        inputPaymentAdjustmentDetail.PaymentDetail.OrgId = baseHttpRequestContext.OrgId;
                        inputPaymentAdjustmentDetail.PaymentDetail.CreatedDate = DateTime.UtcNow;
                        inputPaymentAdjustmentDetail.PaymentDetail.CreatedBy = baseHttpRequestContext.UserId;
                        inputPaymentAdjustmentDetail.PaymentDetail.ModifiedBy = baseHttpRequestContext.UserId;
                        inputPaymentAdjustmentDetail.PaymentDetail.StatusId = (short)Status.Active;
                        inputPaymentAdjustmentDetail.PaymentDetail.PaymentTypeId = (short)AdjustmentType.Write_off;
                        inputPaymentAdjustmentDetail.PaymentDetail.DRAmount = inputPaymentAdjustmentDetail.PaymentDetail.DRAmount;
                        inputPaymentAdjustmentDetail.PaymentDetail.PaymentDate = (CompanyDateTimeNow is null) ? DateTime.Now.Date : CompanyDateTimeNow.Value.Date;
                        inputPaymentAdjustmentDetail.PaymentDetail.Reason = (inputPaymentAdjustmentDetail != null && inputPaymentAdjustmentDetail.PaymentDetail != null) ? inputPaymentAdjustmentDetail.PaymentDetail.Reason : string.Empty;
                        payment = inputPaymentAdjustmentDetail.PaymentDetail;
                    }
                    else
                    {

                        payment.InvoiceDetailsId = invoicedetailsId;
                        payment.OrgId = baseHttpRequestContext.OrgId;
                        payment.CreatedDate = DateTime.UtcNow;
                        payment.CreatedBy = baseHttpRequestContext.UserId;
                        payment.ModifiedBy = baseHttpRequestContext.UserId;
                        payment.StatusId = (short)Status.Active;
                        payment.PaymentTypeId = (short)AdjustmentType.Write_off;
                        payment.DRAmount = owingOrig;
                        payment.PaymentDate = (CompanyDateTimeNow is null) ? DateTime.Now.Date : CompanyDateTimeNow.Value.Date;


                    }
                    ApiResponse<long> apiRespPayment = await AddInvoicePaymentAdjustments(invoicedetailsId, payment, baseHttpRequestContext, patientId);
                    long? paymentDetailsId = null;
                    if (apiRespPayment is not null && apiRespPayment.StatusCode == StatusCodes.Status200OK && apiRespPayment.Result > 0)
                    {
                        //List<long> invoiceDetailIds = new List<long>();
                        //invoiceDetailIds.Add(invoicedetailsId);
                        //await StoreInvoiceDetailMessage(baseHttpRequestContext.OrgCode, invoiceDetailIds, DateTime.Now, (int)InvoiceRequestType.InvoiceUpdated);
                        paymentDetailsId = apiRespPayment.Result;
                        itemAssoc.PaymentDetailsId = paymentDetailsId;
                        List<InvoiceEpisodeItemAssoc> lstItems = new()
                        {
                            itemAssoc
                        };



                        int rows = await _invoiceDAL.AddInvoiceEpisodeItemAssocRange(lstItems);
                        if (rows > 0)
                        {
                            InvoiceDetailUpdate invoiceDetailUpdate = new()
                            {
                                TotalAmoutPaid = 0,
                                Owing = newOwingAmt,
                                TotalDepositPaid = 0
                            };



                            ApiResponse<string> apiResp = await PartialUpdateInvoiceDetail(baseHttpRequestContext, invoicedetailsId, invoiceDetailUpdate);
                            if (apiResp is not null && apiResp.StatusCode == StatusCodes.Status200OK)
                            {
                                List<long> invoiceIds = new List<long>();
                                invoiceIds.Add(invoicedetailsId);
                                if (CompanyDateTimeNow is not null && CompanyDateTimeNow > default(DateTime))
                                {
                                    DateTime baseDate = (DateTime)CompanyDateTimeNow;
                                    DateTime paymentDate = baseDate.Date;
                                    await StoreInvoiceDetailMessage(baseHttpRequestContext.OrgCode, invoiceIds, paymentDate, 2);
                                }


                                apiResponse.StatusCode = StatusCodes.Status200OK;
                                apiResponse.Result = invoicedetailsId;
                                return apiResponse;
                            }
                            else
                            {
                                if (apiResponse is not null)
                                    apiResponse.Errors = apiResp.Errors;
                            }
                        }
                    }
                    else
                    {
                        apiResponse = apiRespPayment;

                    }


                }

            }


            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            apiResponse.Message = "Failure";
            apiResponse.Result = default(long);
            apiResponse.Errors.Add("WriteOff cannot be processed.");
            return apiResponse;


        }

        private async Task<ApiResponse<long>> ProcessRefund(InvoiceDetail invoiceDetail, InputPaymentAdjustmentDetail inputPaymentAdjustmentDetail, BaseHttpRequestContext baseHttpRequestContext, long? patientId)
        {
            ApiResponse<long> apiResponse = new();
            long id = invoiceDetail.Id;
            decimal totalFee = 0;
            decimal totalGst = 0;
            decimal newOwingAmt = 0;
            decimal newTotalAmtPaid = 0;
            if (inputPaymentAdjustmentDetail.InvoiceEpisodeItemAssocs is not null)
            {



                long paymentDetailsId = default(long);

                if (inputPaymentAdjustmentDetail.PaymentDetail is not null && inputPaymentAdjustmentDetail.PaymentDetail.DRAmount is not null && inputPaymentAdjustmentDetail.PaymentDetail.DRAmount > 0)
                {
                    inputPaymentAdjustmentDetail.PaymentDetail.InvoiceDetailsId = id;
                    inputPaymentAdjustmentDetail.PaymentDetail.OrgId = baseHttpRequestContext.OrgId;
                    inputPaymentAdjustmentDetail.PaymentDetail.InvoiceDetailsId = id;
                    inputPaymentAdjustmentDetail.PaymentDetail.StatusId = (short)Status.Active;
                    inputPaymentAdjustmentDetail.PaymentDetail.CreatedBy = baseHttpRequestContext.UserId;
                    inputPaymentAdjustmentDetail.PaymentDetail.ModifiedBy = baseHttpRequestContext.UserId;
                    inputPaymentAdjustmentDetail.PaymentDetail.CreatedDate = DateTime.UtcNow;
                    ApiResponse<long> apiRespPayment = await AddInvoicePaymentAdjustments(id, inputPaymentAdjustmentDetail.PaymentDetail, baseHttpRequestContext, patientId);
                    //List<long> invoiceDetailIds = new List<long>();
                    //invoiceDetailIds.Add(id);
                    //await StoreInvoiceDetailMessage(baseHttpRequestContext.OrgCode, invoiceDetailIds, DateTime.Now, (int)InvoiceRequestType.InvoiceUpdated);
                    if (apiRespPayment is not null && apiRespPayment.StatusCode == StatusCodes.Status200OK && apiRespPayment.Result > 0)
                    {
                        paymentDetailsId = apiRespPayment.Result;
                    }
                    else
                    {
                        apiResponse = apiRespPayment;
                        apiResponse.Errors.Add("Refund cannot be processed.");
                        return apiResponse;
                    }
                }
                inputPaymentAdjustmentDetail.InvoiceEpisodeItemAssocs.ToList().ForEach(x =>
                {
                    x.OrgId = baseHttpRequestContext.OrgId;
                    x.CreatedDate = DateTime.UtcNow;
                    x.CreatedBy = baseHttpRequestContext.UserId;
                    x.ModifiedBy = baseHttpRequestContext.UserId;
                    x.StatusId = (short)Status.Active;
                    x.AdjustmentTypeId = (short)AdjustmentType.Refund;
                    x.Fee = CommonFinanceCalculator.Rounding((decimal)(x.Fee - x.Gst), 2);
                    x.InvoiceDetailsId = id;
                    totalFee += ((x.Fee == null) ? 0 : (decimal)x.Fee);
                    totalGst += (x.Gst == null) ? 0 : (decimal)x.Gst;
                    x.PaymentDetailsId = (paymentDetailsId == default(long)) ? null : paymentDetailsId;
                });

                decimal totalAmountOrig = (invoiceDetail.TotalAmount == null) ? 0 : (decimal)invoiceDetail.TotalAmount;
                decimal owingOrig = (invoiceDetail.Owing == null) ? 0 : (decimal)invoiceDetail.Owing;
                decimal totalAmountPaidOrig = totalAmountOrig - owingOrig;
                decimal depositOrig = (invoiceDetail.Deposit == null) ? 0 : (decimal)invoiceDetail.Deposit;
                decimal refund = 0;



                decimal newTotalAmt = totalAmountOrig - totalGst - totalFee;

                if (inputPaymentAdjustmentDetail.PaymentDetail is not null)
                {
                    refund = (inputPaymentAdjustmentDetail.PaymentDetail.DRAmount == null) ? 0 : (decimal)inputPaymentAdjustmentDetail.PaymentDetail.DRAmount;
                    newTotalAmtPaid = totalAmountPaidOrig - refund;
                    newOwingAmt = newTotalAmt - newTotalAmtPaid;

                }
                InvoiceDetailUpdate invoiceDetailUpdate = new()
                {
                    TotalAmoutPaid = 0,
                    Owing = newOwingAmt,
                    TotalDepositPaid = 0,
                    TotalInvoiceAmount = newTotalAmt
                };
                if (invoiceDetail.InvoiceTypeId == (short)InvoiceType.Estimate && refund > 0)
                {
                    invoiceDetailUpdate.TotalDepositPaid = refund * -1;
                }

                int rows = await _invoiceDAL.AddInvoiceEpisodeItemAssocRange(inputPaymentAdjustmentDetail.InvoiceEpisodeItemAssocs.ToList());
                if (rows > 0 || (rows == 0 && invoiceDetail.InvoiceTypeId == (short)InvoiceType.Estimate))
                {

                    ApiResponse<string> apiResp = await PartialUpdateInvoiceDetail(baseHttpRequestContext, id, invoiceDetailUpdate);

                    if (apiResp is not null && apiResp.StatusCode == StatusCodes.Status200OK)
                    {
                        invoiceDetail.TotalAmount = newTotalAmt;
                        invoiceDetail.Owing = newOwingAmt;


                        apiResponse.StatusCode = StatusCodes.Status200OK;
                        apiResponse.Result = id;

                        if (refund == 0)
                        {
                            List<long> invoiceIds = new List<long>();
                            invoiceIds.Add(id);
                            bool isWindows = System.Runtime.InteropServices.RuntimeInformation
                                                    .IsOSPlatform(OSPlatform.Windows);
                            OrganisationView orgDetails = await FetchMasterCompanyDetails(baseHttpRequestContext);
                            short timeZoneId = (orgDetails is null || orgDetails.TimeZoneMasterId <= 0) ? default(short) : orgDetails.TimeZoneMasterId;
                            if (timeZoneId > 0)
                            {
                                DateTime? CompanyDateTimeNow = GetDateFromOrganisation(orgDetails, isWindows);
                                if (CompanyDateTimeNow is not null && CompanyDateTimeNow > default(DateTime))
                                {
                                    DateTime baseDate = (DateTime)CompanyDateTimeNow;
                                    DateTime paymentDate = baseDate.Date;
                                    await StoreInvoiceDetailMessage(baseHttpRequestContext.OrgCode, invoiceIds, paymentDate, 2);
                                }
                            }
                        }

                        if (invoiceDetail.InvoiceTypeId == (short)InvoiceType.Invoice && paymentDetailsId > 0)
                        {
                            await StoreSplitPaymentDetailMessage(baseHttpRequestContext.OrgCode, paymentDetailsId, (int)EODRequestDataType.SplitPayment_Adjustment);
                        }
                        return apiResponse;
                    }
                }



            }
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            apiResponse.Result = default(long);
            apiResponse.Errors.Add("Refund cannot be processed.");
            return apiResponse;

        }
        /// <summary>
        /// Method to fetch the details of adjustment 
        /// </summary>
        /// <param name="id"></param>
        /// <param name="paymentDetailsId"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<InputPaymentAdjustmentDetail>> FetchInvoiceAdjustment(long id, long paymentDetailsId, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<InputPaymentAdjustmentDetail> apiResponse = new();
            List<InvoiceEpisodeItemAssoc> lstItems = await _invoiceDAL.FetchInvoiceEpisodeItemAssocsFromPaymentDetailsId(paymentDetailsId, id, baseHttpRequestContext.OrgId);
            InputPaymentAdjustmentDetail inputPaymentAdjustmentDetail = new();
            inputPaymentAdjustmentDetail.InvoiceEpisodeItemAssocs = lstItems;

            ApiResponse<PaymentDetail> apiRespPayment = await FetchInvoicePaymentDetails(paymentDetailsId, id, baseHttpRequestContext, true);
            if (apiRespPayment is not null && apiRespPayment.StatusCode == StatusCodes.Status200OK)
            {
                inputPaymentAdjustmentDetail.PaymentDetail = apiRespPayment.Result;
                inputPaymentAdjustmentDetail.AdjustmentTypeId = (inputPaymentAdjustmentDetail.PaymentDetail is not null) ? inputPaymentAdjustmentDetail.PaymentDetail.PaymentTypeId : default(short);
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";
            }
            else
            {
                apiResponse.Message = "Failure";
                if (apiRespPayment is not null && apiRespPayment.Errors is not null)
                    apiResponse.Errors = apiRespPayment.Errors;
                apiResponse.Errors.Add("Adjustment Details cannot be fetched at this time.");
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            }
            apiResponse.Result = inputPaymentAdjustmentDetail;
            return apiResponse;

        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<InvoiceDepositTagsDetails>> GeInvoiceCustomBillingTagData(long id, BaseHttpRequestContext baseHttpRequestContext)
        {
            int orgId = baseHttpRequestContext.OrgId;
            ApiResponse<InvoiceDepositTagsDetails> apiResponse = new ApiResponse<InvoiceDepositTagsDetails>();
            InvoiceDepositTagsDetails invoiceDepositTagsDetails = new InvoiceDepositTagsDetails();
            invoiceDepositTagsDetails.CustomBillingItemDetails = new List<CustomBillingItemDetails>();
            var invoiceResponse = await GetInvoiceDetail(id, 0, baseHttpRequestContext, true);

            if (invoiceResponse == null)
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failed";
                apiResponse.Errors.Add("Invoice Details not found");
                return apiResponse;
            }
            var invoiceDetail = invoiceResponse.Result;
            ApiResponse<QueryResultList<ListPaymentDetail>> paymentDetails = new ApiResponse<QueryResultList<ListPaymentDetail>>();
            string filter = "{ InvoiceDetailsId:[" + invoiceDetail.Id + "]}";
            string encodedFilter = Base64EncoderHelper.EncodeQueryModelFilterParam(filter);
            string InvoicePaymentServiceUrl = _appSettings.ApiUrls["InvoicePaymentServiceUrl"] + "/invoicepayment/payment_details?pn=1&ps=100&sot=id&soo=desc&f=" + encodedFilter;
            RestClient restClientPayment = new RestClient(InvoicePaymentServiceUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            paymentDetails = await restClientPayment.GetAsync<ApiResponse<QueryResultList<ListPaymentDetail>>>(InvoicePaymentServiceUrl);
            if (paymentDetails != null && paymentDetails.Result.ItemRecords != null)
            {
                var paymentList = paymentDetails.Result.ItemRecords.ToList();
                if (paymentList.Any())
                {
                    invoiceDepositTagsDetails.ReceiptedAmount = paymentList.Where(x => x.StatusId == (short)Status.Active).FirstOrDefault().CRAmount.HasValue ? paymentList.FirstOrDefault().CRAmount.Value : 0;
                    invoiceDepositTagsDetails.TotalEstimate = paymentList.Where(p => p.CRAmount.HasValue && p.StatusId == (short)Status.Active).Sum(x => x.CRAmount.Value);//##TotalAmountPaid##
                    invoiceDepositTagsDetails.BalanceOutstanding = invoiceDetail.Owing.HasValue ? invoiceDetail.Owing.Value : 0;
                    invoiceDepositTagsDetails.TotalRefund = paymentList.Where(p => p.PaymentTypeId == (short)AdjustmentType.Refund && p.DRAmount != null).Sum(x => x.DRAmount.Value);
                }
            }

            DateTime? dateOfService = default;

            if (invoiceDetail.InvoiceAdditionalDetails is not null && invoiceDetail.InvoiceAdditionalDetails.InPatientCompanyDetailsId is not null)
            {
                invoiceDepositTagsDetails.InPatientCompanyDetailsId = invoiceDetail.InvoiceAdditionalDetails.InPatientCompanyDetailsId;
            }
            if (invoiceDetail.AppointmentDetailsId.HasValue)
            {
                List<long> lstAppointmentIds = new() { (long)invoiceDetail.AppointmentDetailsId };
                List<AppointmentView> lstAppointmentDetails = await FetchAppointmentDetailsWithReferrals(lstAppointmentIds, baseHttpRequestContext);

                if (lstAppointmentDetails != null && lstAppointmentDetails.Count > 0)
                {
                    var appointment = lstAppointmentDetails.FirstOrDefault();
                    invoiceDepositTagsDetails.AppointmentDetails = invoiceDetail.AppointmentDetailsId;
                    invoiceDepositTagsDetails.AppointmentDate = appointment.DateOfAppointment.HasValue ? appointment.DateOfAppointment.Value : null;
                    invoiceDepositTagsDetails.AppointmentTime = appointment.StartTime.HasValue ? appointment.StartTime.Value : null;
                    dateOfService = appointment.DateOfAppointment.HasValue ? appointment.DateOfAppointment.Value : null;

                    if (invoiceDetail.AppointmentDetails.ReferralDetail != null)
                    {
                        invoiceDepositTagsDetails.ReferralProviderName = invoiceDetail.AppointmentDetails.ReferralDetail.ReferringProviderInfo.FirstName + " " + invoiceDetail.AppointmentDetails.ReferralDetail.ReferringProviderInfo.SurName;
                        invoiceDepositTagsDetails.ReferralDate = invoiceDetail.AppointmentDetails.ReferralDetail.IssueDate;

                        if (invoiceDetail.AppointmentDetails.ReferralDetail.IssueDate.HasValue && invoiceDetail.AppointmentDetails.ReferralDetail.ExpiryDate.HasValue)
                            invoiceDepositTagsDetails.ReferralPeriod = invoiceDetail.AppointmentDetails.ReferralDetail.IssueDate.Value.Date.ToString("dd-MM-yyyy") + " To " + invoiceDetail.AppointmentDetails.ReferralDetail.ExpiryDate.Value.Date.ToString("dd-MM-yyyy");
                        else
                            invoiceDepositTagsDetails.ReferralPeriod = string.Empty;
                        invoiceDepositTagsDetails.ReferringProviderNumber = invoiceDetail.AppointmentDetails.ReferralDetail.ReferringProviderInfo.ProviderNumber;
                        invoiceDepositTagsDetails.ReferringProviderCompanyId = invoiceDetail.AppointmentDetails.ReferralDetail.ReferringProviderInfo.CompanyId;
                    }
                }
            }

            if (invoiceDetail.ProviderDetails != null)
            {
                invoiceDepositTagsDetails.UserDetailsId = invoiceDetail.ProviderId.HasValue ? invoiceDetail.ProviderId.Value : null;
                invoiceDepositTagsDetails.ServiceProvider = invoiceDetail.ProviderDetails.FirstName + " " + invoiceDetail.ProviderDetails.SurName;
                invoiceDepositTagsDetails.ServiceProviderNumber = invoiceDetail.ProviderDetails.LstUserCompanyInfo != null ? invoiceDetail.ProviderDetails.LstUserCompanyInfo.Where(c => c.IsDefault == true).FirstOrDefault().ProviderNumber : "";
            }
            invoiceDepositTagsDetails.EstimateNumber = invoiceDetail.RecordId.HasValue ? invoiceDetail.RecordId.Value : 0;
            invoiceDepositTagsDetails.BalanceOutstanding = invoiceDetail.Owing.HasValue ? invoiceDetail.Owing.Value : 0;
            invoiceDepositTagsDetails.CompanyDetailsId = invoiceDetail.CompanyDetailsId.HasValue ? invoiceDetail.CompanyDetailsId.Value : null;
            invoiceDepositTagsDetails.EstimateDate = invoiceDetail.DateInvoiced;
            invoiceDepositTagsDetails.InvoiceStatus = invoiceDetail.InvoiceStatusId;
            invoiceDepositTagsDetails.AccountHolderType = invoiceDetail.AccountHolderTypeId.HasValue ? invoiceDetail.AccountHolderTypeId.Value : 0;
            invoiceDepositTagsDetails.ExcessAmount = invoiceDetail.ExcessAmount.HasValue ? invoiceDetail.ExcessAmount.Value : 0;
            invoiceDepositTagsDetails.ExcessOwing = invoiceDetail.ExcessOwing.HasValue ? invoiceDetail.ExcessOwing.Value : 0;
            invoiceDepositTagsDetails.IsInPatient = invoiceDetail.IsInPatient;
            invoiceDepositTagsDetails.HealthFundMembershipNo = invoiceDetail.HealthFundMembershipNo;
            invoiceDepositTagsDetails.HealthFundParticipantId = invoiceDetail.HealthFundParticipantId;
            //Get Invoice ItemDetails
            List<long> customeEstimateNumbers = new List<long>();
            List<long> mbsEstimateNumbers = new List<long>();
            List<long> accommodationEstimateNumbers = new();
            if (invoiceDetail.InvoiceEpisodeItemAssocs != null)
            {
                customeEstimateNumbers.AddRange(invoiceDetail.InvoiceEpisodeItemAssocs.Where(item => (item.EpisodeTypeId == (short)EpisodeTypes.Custom_Product || item.EpisodeTypeId == (short)EpisodeTypes.Custom_Procedure || item.EpisodeTypeId == (short)EpisodeTypes.Custom_Surgical)).Select(a => a.ItemDetailsId));
                mbsEstimateNumbers.AddRange(invoiceDetail.InvoiceEpisodeItemAssocs.Where(item => item.EpisodeTypeId == (short)EpisodeTypes.MBS).Select(a => a.ItemNumber));
                accommodationEstimateNumbers.AddRange(invoiceDetail.InvoiceEpisodeItemAssocs.Where(item => item.EpisodeTypeId == (short)EpisodeTypes.Accommodation).Select(a => a.ItemDetailsId));
            }

            var customeEstimateitemsDetails = await FetchCustomItemDetails(customeEstimateNumbers, baseHttpRequestContext);
            var mbsItemsDetails = await FetchMBSItemDetails(mbsEstimateNumbers, baseHttpRequestContext);
            var accomItemsDetails = await FetchAccommodationItemDetails(accommodationEstimateNumbers, baseHttpRequestContext);
            if (invoiceDetail.InvoiceEpisodeItemAssocs != null)
            {

                var paymentList = invoiceDetail.InvoiceEpisodeItemAssocs.ToList();
                paymentList.ForEach(x =>
                {
                    string description = string.Empty;
                    if (x.EpisodeTypeId == (short)EpisodeTypes.Accommodation)
                    {
                        if (accomItemsDetails != null && accomItemsDetails.Count > 0)
                        {
                            MedicalContractorBandAssocInfo bandAssocInfo = accomItemsDetails.Where(c => c.Id == x.ItemDetailsId).FirstOrDefault();
                            if (bandAssocInfo != null)
                            {
                                description = bandAssocInfo.Description;
                                if (bandAssocInfo.MedicalContBandTypeId == (short)MedicalContBandType.Accommodation_Day_Stay)
                                {
                                    description = "(Day) " + description;
                                }
                                else if (bandAssocInfo.MedicalContBandTypeId == (short)MedicalContBandType.Accommodation_Overnight)
                                {
                                    description = "(Overnight) " + description;
                                }

                            }
                        }
                    }
                    else if (x.EpisodeTypeId != (short)EpisodeTypes.MBS)
                        description = (customeEstimateitemsDetails != null && customeEstimateitemsDetails.Any(c => c.Id == x.ItemDetailsId)) ? customeEstimateitemsDetails.Where(c => c.Id == x.ItemDetailsId).FirstOrDefault().Description : string.Empty;
                    else
                        description = (mbsItemsDetails != null && mbsItemsDetails.Any(c => c.ItemNum == x.ItemNumber)) ? mbsItemsDetails.Where(c => c.ItemNum == x.ItemNumber).FirstOrDefault().Description : string.Empty;
                    if (x.ItemNumber == default(long) && x.AdjustmentTypeId != null && x.AdjustmentTypeId == (short)AdjustmentType.Write_off)
                    {
                        description = EnumExtensions.GetDescription((AdjustmentType)AdjustmentType.Write_off);
                        x.Fee = (x.Fee is not null && x.Fee.HasValue) ? x.Fee * -1 : 0;
                        x.Gst = (x.Gst is not null && x.Gst.HasValue) ? x.Gst * -1 : 0;

                    }
                    string serviceType = string.Empty;
                    serviceType = string.Join("-", x.InvoiceEpisodeItemsIndicatorAssocs.Select(s => s.InvoiceItemIndicatorType));
                    string[] arr = { serviceType, x.Notes };
                    CustomBillingItemDetails itemDetails = new CustomBillingItemDetails()
                    {
                        DateOfService = dateOfService.HasValue ? dateOfService.Value : invoiceDetail.DateOfService.Value,
                        Description = description = description.Length > 40 ? description.Substring(0, 40) + "..." : description,
                        Fee = x.Fee.HasValue ? x.Fee.Value : 0,
                        GST = x.Gst.HasValue ? x.Gst.Value : 0,
                        ItemNumber = x.ItemNumber,
                        Note = string.Join(":", arr.Where(a => !string.IsNullOrWhiteSpace(a))),


                    };

                    itemDetails.Total = itemDetails.Fee + itemDetails.GST;
                    invoiceDepositTagsDetails.CustomBillingItemDetails.Add(itemDetails);
                });
            }

            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result = invoiceDepositTagsDetails;
            return apiResponse;
        }

        /// <summary>
        /// Method to list invoice summaries of a patient appointments
        /// </summary>
        /// <param name="queryModel"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<List<ListInvoiceDetails>>> GetAppointmentInvoiceList(QueryModel queryModel, long appointmentDetailsId, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<List<ListInvoiceDetails>> apiResponse = new();
            int orgId = baseHttpRequestContext.OrgId;
            InvoiceSummaryFilterModel filterModel = PrepareFilterParameters(queryModel.Filter);
            List<ListInvoiceDetails> invoiceDetailsList = null;
            if (filterModel == null)
            {
                filterModel = new InvoiceSummaryFilterModel();
                filterModel.AppointmentDetailsId = appointmentDetailsId;
                invoiceDetailsList = await _invoiceDAL.GetAppointmentInvoiceList(orgId, queryModel, filterModel);

            }
            else
            {
                //filterModel = new InvoiceSummaryFilterModel();
                filterModel.AppointmentDetailsId = appointmentDetailsId;
                invoiceDetailsList = await _invoiceDAL.ListInvoicesForAppointment(orgId, appointmentDetailsId, filterModel);
            }
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result = invoiceDetailsList;
            return apiResponse;
        }

        public async Task<ApiResponse<string>> PartialUpdateInvoiceMedicareAssoc(BaseHttpRequestContext baseHttpRequestContext, long id, InvoiceMedicareAssocUpdateView invoiceMedicareAssocUpdateView)
        {
            ApiResponse<string> apiResponse = new();

            InvoiceMedicareAssoc invoiceMedicareAssocDB = await _invoiceDAL.GetInvoiceMedicareAssocsFromId(id, baseHttpRequestContext.OrgId);
            if (invoiceMedicareAssocDB is not null)
            {

                Dictionary<string, object> propertyDictionary = (Dictionary<string, object>)GetFilledProperties(invoiceMedicareAssocUpdateView);

                int rows1 = await _invoiceDAL.PartialUpdateInvoiceMedicareAssoc(propertyDictionary, baseHttpRequestContext.UserId, invoiceMedicareAssocDB);
                if (rows1 > 0)
                {

                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    apiResponse.Result = "Successfully updated";
                    apiResponse.Message = "Success";
                    return apiResponse;
                }

            }
            apiResponse.Errors.Add("InvoiceMedicareAssoc cannot be updated usign the data provided.");
            apiResponse.Message = "Failure";
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            return apiResponse;
        }

        public async Task<ApiResponse<List<FinanceSummaryGroupView>>> GetFinanceSummary(long patientDetailsId, BaseHttpRequestContext baseHttpRequestContext, QueryModel queryModel)
        {
            ApiResponse<List<FinanceSummaryGroupView>> apiResponse = new();
            FinanceSummaryFilter filter = null;
            if (queryModel is not null && !string.IsNullOrWhiteSpace(queryModel.Filter))
                filter = PrepareFinanceSummaryFilterParameter(queryModel.Filter);
            List<FinanceSummaryGroupView> financeGroups = await _invoiceDAL.GetInvoiceSummaryForPatient(patientDetailsId, queryModel.SearchTerm, baseHttpRequestContext.OrgId, filter);


            apiResponse.Result = financeGroups;
            apiResponse.Message = "Success";
            apiResponse.StatusCode = StatusCodes.Status200OK;
            return apiResponse;
        }

        private FinanceSummaryFilter PrepareFinanceSummaryFilterParameter(string filter)
        {

            return JsonConvert.DeserializeObject<FinanceSummaryFilter>(filter);

        }

        public async Task<ApiResponse<InvoiceDetailUpdate>> GetInvoiceDetailInfoForPayment(long id, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<InvoiceDetailUpdate> apiResponse = new();

            InvoiceDetailUpdate invoiceDetailUpdate = await _invoiceDAL.GetInvoiceDetailInfoForPayment(id, baseHttpRequestContext.OrgId);
            if (invoiceDetailUpdate != null)
            {
                apiResponse.Result = invoiceDetailUpdate;
                apiResponse.StatusCode = StatusCodes.Status200OK;
                return apiResponse;
            }
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            apiResponse.Errors.Add("GetInvoiceDetailInfoForPayment cannot be fetched with the data provided.");
            return apiResponse;
        }
    }
}
