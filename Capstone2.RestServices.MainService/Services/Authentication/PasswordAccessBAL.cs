﻿using Capstone2.Framework.Business.Common;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.Authentication.Interfaces;
using Capstone2.RestServices.Authentication.Models;
using Capstone2.Shared.Models.Communication;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.StackExchangeRedis;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using System.Transactions;

namespace Capstone2.RestServices.Authentication.Services
{
    public class PasswordAccessBAL : IPasswordAccessBAL
    {
        public readonly AppSettings _appSettings;
        private readonly IPasswordAccessDAL _PasswordAccessDAL;
        private readonly IOffsiteAccessBAL _offsiteAccessBAL;
        private readonly IAuthTokenBAL _authTokenBAL;
        private readonly IAuthTokenDAL _authTokenDAL;
        private readonly ILogger<PasswordAccessBAL> _logger;
        private IDistributedCacheHelper _redisCache;

        public PasswordAccessBAL(IPasswordAccessDAL passwordAccessDAL, IOptions<AppSettings> appSettings, IOffsiteAccessBAL offsiteAccessBAL, IAuthTokenBAL authTokenBAL, IAuthTokenDAL authTokenDAL, ILogger<PasswordAccessBAL> logger, IDistributedCacheHelper cache)
        {
            _PasswordAccessDAL = passwordAccessDAL;
            _appSettings = appSettings.Value;
            _offsiteAccessBAL = offsiteAccessBAL;
            _authTokenBAL = authTokenBAL;
            _authTokenDAL = authTokenDAL;
            _logger = logger;
            _redisCache = cache;
        }
        public async Task<ApiResponse<LoginResponse>> LoginBAL(string ClientIpAddress, List<LoginRequest> listLoginRequest, int OrgId, string orgCode)
        {
            ApiResponse<LoginResponse> response = new ApiResponse<LoginResponse>();
            if (listLoginRequest != null && listLoginRequest.Count == 1 && "EMAIL_PASSWORD".Equals(listLoginRequest[0].Type.ToUpper()))
                response = await CheckCredentials(ClientIpAddress, listLoginRequest[0], OrgId, orgCode);
            else if (listLoginRequest != null && listLoginRequest.Count > 1)
                response = await _offsiteAccessBAL.ValidateTwoFactorAuth(ClientIpAddress, listLoginRequest, OrgId, orgCode);
            else
            {
                response.StatusCode = StatusCodes.Status400BadRequest;
                response.Errors.Add("Invalid Request");
            }
            return response;
        }
        private async Task<ApiResponse<LoginResponse>> CheckCredentials(string clientIP, LoginRequest loginRequest, int OrgId, string orgCode)
        {            
            ApiResponse<LoginResponse> response = new ApiResponse<LoginResponse>();
            response.Result = new LoginResponse();

            var dbResponse = await _PasswordAccessDAL.GetloginData(loginRequest.Identifier, OrgId);
            if (dbResponse == null || dbResponse.OrgId == 0)
            {
                response.StatusCode = StatusCodes.Status400BadRequest;
                response.Message = "Unauthenticated";
                response.Errors.Add("Not a Valid Username or Password");
                return response;
            }
            if (dbResponse == null || dbResponse.UserStatusId == (int)UserStatus.AccountLocked)
            {
                response.StatusCode = StatusCodes.Status400BadRequest;
                response.Message = "Unauthenticated";
                response.Errors.Add("The account is locked.Please contact the administrator.");
                return response;
            }
            if ((dbResponse.UserStatusId == (int)UserStatus.AccountSuspended))
            {
                if (dbResponse.AccountSuspendedDate > DateTime.UtcNow.AddMinutes(-30))
                {
                    response.StatusCode = StatusCodes.Status400BadRequest;
                    response.Message = "Unauthenticated";
                    response.Errors.Add("The account is temporarily suspended.Please try again later.");
                    return response;
                }
            }
            if ((dbResponse.UserStatusId == (int)UserStatus.Archived))
            {
                response.StatusCode = StatusCodes.Status400BadRequest;
                response.Message = "Unauthenticated";
                response.Errors.Add("The account is Archived.The user cannot be logged in.");
                return response;
            }
            if ((dbResponse.UserStatusId == (int)UserStatus.InActive))
            {
                response.StatusCode = StatusCodes.Status400BadRequest;
                response.Message = "Unauthenticated";
                response.Errors.Add("The account is Inactive.The user cannot be logged in.");
                return response;
            }
            if ((dbResponse.UserStatusId == (int)UserStatus.PendingRegistration))
            {
                response.StatusCode = StatusCodes.Status400BadRequest;
                response.Message = "Unauthenticated";
                response.Errors.Add("The account is not Activated.The user cannot be logged in.");
                return response;
            }
            if (dbResponse == null || dbResponse.OrgId == 0)
            {
                response.StatusCode = StatusCodes.Status400BadRequest;
                response.Message = "Unauthenticated";
                response.Errors.Add("Not a Valid Username or Password.");
                return response;
            }
            if (dbResponse.OrgIpAddress == null)
            {
                response.StatusCode = StatusCodes.Status400BadRequest;
                response.Message = "Unauthenticated";
                response.Errors.Add("No Ip is Whitelisted for your Organisation.please contact Admin.");
                return response;
            }
            var ipList = dbResponse.OrgIpAddress.Split(',').ToList();
            if (dbResponse.UserIpaddress != null)
            {
                var useripList = dbResponse.UserIpaddress.Split(',').ToList();
                ipList = ipList.Concat(useripList).ToList();
            }

            string enteredPassword = Base64EncoderHelper.DecodeBase64(loginRequest.FactorValue);
            bool Validpassword = false;
            if (!string.IsNullOrWhiteSpace(enteredPassword))
            {
                Validpassword = BcryptHashingHelper.VerifyHashedPassword(enteredPassword, dbResponse.FactorValue);
            }

            if (Validpassword == true)
            {
                int expire_second = Convert.ToInt32(_appSettings.TokenExpiresIn);
                if (ipList.Count > 0 && ipList.Contains(clientIP))
                {
                    response.StatusCode = StatusCodes.Status200OK;
                    response.Message = "Success";
                    response.Result.IdleTimeOut = (short)dbResponse.IdleTimeOut;
                    response.Result.Token = _authTokenBAL.GenerateAuthToken(dbResponse.UserId, dbResponse.OrgId, dbResponse.UserRoleId.Value, orgCode);
                    response.Result.OffSiteAccess = dbResponse.IsOffsiteAccess;
                    using (var transaction = new TransactionScope(TransactionScopeOption.Required, TransactionScopeAsyncFlowOption.Enabled))
                    {
                        await _authTokenDAL.InvalidateExistingTokens(dbResponse.UserId, dbResponse.OrgId, (short)TokenType.Login);
                        await _authTokenDAL.AddTokentoDB(dbResponse.UserId, dbResponse.OrgId, response.Result.Token, expire_second, (short)TokenType.Login);
                        await _authTokenDAL.AddLoginLogtoDB(dbResponse.UserId, dbResponse.OrgId, clientIP);
                        await _PasswordAccessDAL.UpdateLoginAttempts(dbResponse.UserId, dbResponse.OrgId, 0, false);
                        transaction.Complete();
                    }
                    string UserTokenKey = "_token_" + orgCode + "_" + dbResponse.OrgId + "_" + dbResponse.UserId;
                    await _redisCache.SetIntoCache(response.Result.Token,UserTokenKey);


                }
                else if (dbResponse.IsOffsiteAccess == true)
                {
                    if (!(string.IsNullOrWhiteSpace(loginRequest.RememberMeKey)))
                    {
                        short? remMeExpiryDays = dbResponse.RememberMeTokenExpiry;
                        List<string> remMeKeys = await _PasswordAccessDAL.FetchUserAccessTokens(dbResponse.UserId, dbResponse.OrgId, remMeExpiryDays);
                        if (remMeKeys != null && remMeKeys.Contains(loginRequest.RememberMeKey))
                        {
                            response.StatusCode = StatusCodes.Status200OK;
                            response.Message = "Success";
                            response.Result.IdleTimeOut = (short)dbResponse.IdleTimeOut;
                            response.Result.Token = _authTokenBAL.GenerateAuthToken(dbResponse.UserId, dbResponse.OrgId, dbResponse.UserRoleId.Value, orgCode);
                            response.Result.OffSiteAccess = dbResponse.IsOffsiteAccess;
                            using (var transaction = new TransactionScope(TransactionScopeOption.Required, TransactionScopeAsyncFlowOption.Enabled))
                            {
                                await _authTokenDAL.InvalidateExistingTokens(dbResponse.UserId, dbResponse.OrgId, (short)TokenType.Login);
                                await _authTokenDAL.AddTokentoDB(dbResponse.UserId, dbResponse.OrgId, response.Result.Token, expire_second, (short)TokenType.Login);
                                await _authTokenDAL.AddLoginLogtoDB(dbResponse.UserId, dbResponse.OrgId, clientIP);
                                await _PasswordAccessDAL.UpdateLoginAttempts(dbResponse.UserId, dbResponse.OrgId, 0, false);
                                transaction.Complete();
                            }

                            string UserTokenKey = "_token_" + orgCode + "_" + dbResponse.OrgId +"_"+dbResponse.UserId;
                            await _redisCache.SetIntoCache(response.Result.Token,UserTokenKey);

                            return response;
                        }
                    }

                    var factorsForUser = await _PasswordAccessDAL.Get2FactorData(dbResponse.UserId);
                    var emailFactor = factorsForUser.Where(factor => factor.IdentifierType == (int)IdentifierType.Email_Pin).FirstOrDefault();
                    if (!(emailFactor is null))
                    {
                        response.Result.Email = FactorModifier(emailFactor.IdentifierValue);
                    }
                    var mobileFactor = factorsForUser.Where(factor => factor.IdentifierType == (int)IdentifierType.Mobile_Pin).FirstOrDefault();

                    if (!(mobileFactor is null))
                    {
                        response.Result.Mobile = FactorModifier(mobileFactor.IdentifierValue);
                    }
                    response.StatusCode = StatusCodes.Status200OK;
                    response.Result.OffSiteAccess = dbResponse.IsOffsiteAccess;
                    response.Message = "Next factor needed";
                    await _PasswordAccessDAL.UpdateLoginAttempts(dbResponse.UserId, dbResponse.OrgId, 0, false);                    

                }
                else
                {
                    response.StatusCode = StatusCodes.Status400BadRequest;
                    response.Message = "Unauthenticated";
                    response.Errors.Add("User is not allowed Offsite Access, Please contact Admin.");
                }
            }
            else
            {
                long userId = dbResponse.UserId;
                short? userLoginAttempts = (short?)(dbResponse.UserLoginAttempts + 1);
                bool accountLocked = false;
                response.StatusCode = StatusCodes.Status400BadRequest;
                response.Message = "Unauthenticated";
                int pendingAttempts = (int)(3 - userLoginAttempts);
                response.Errors.Add("Not a Valid Username or Password.You have " + pendingAttempts + " attempts left.");
                if (userLoginAttempts >= 3)
                {
                    accountLocked = true;
                    response.Errors.Add("Account is Locked after 3 unsuccessful login attempts.Please contact Administrator.");
                }
                await _PasswordAccessDAL.UpdateLoginAttempts(userId, dbResponse.OrgId, userLoginAttempts, accountLocked);

            }

            //if (response.StatusCode == StatusCodes.Status200OK)
            //{
            //    await _PasswordAccessDAL.UpdateLoginAttempts(dbResponse.UserId, dbResponse.OrgId, 0, false);
            //}
            return response;
        }

        private string FactorModifier(string inputString)
        {
            StringBuilder modifiedString = new StringBuilder(inputString, 50);
            modifiedString.Remove(3, 4);
            modifiedString.Insert(3, "****");
            return modifiedString.ToString();
        }
        public async Task<ApiResponse<string>> ResetPasswordLink(ResetLinkRequest resetLinkRequest, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<string> apiResponse = new ApiResponse<string>();
            var dbResponse = await _PasswordAccessDAL.GetloginData(resetLinkRequest.Identifier, baseHttpRequestContext.OrgId);
            if (dbResponse is null)
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Unauthenticated";
                apiResponse.Errors.Add("Not a Valid Username or Password");
                return apiResponse;
            }
            if (dbResponse.UserStatusId == (int)UserStatus.AccountLocked)
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Unauthenticated";
                apiResponse.Errors.Add("The account is locked.Please contact the administrator.");
                return apiResponse;
            }
            apiResponse = await SendResetPasswordEmail(resetLinkRequest, dbResponse, baseHttpRequestContext);

            return apiResponse;
        }

        private async Task<ApiResponse<string>> SendResetPasswordEmail(ResetLinkRequest resetLinkRequest, LoginDBResponse dbResponse, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<string> apiResponse = new ApiResponse<string>();
            int orgId = dbResponse.OrgId;
            Int64 userId = dbResponse.UserId;
            EmailRequest emailRequest = await _offsiteAccessBAL.CreateNewEmailRequestMsg("ResetPassword", userId, "");
            if (!(emailRequest is null))
            {
                emailRequest.ToEmailAddress = resetLinkRequest.Identifier;

                apiResponse = await _offsiteAccessBAL.CreateEmailRequest(emailRequest, baseHttpRequestContext);
                if (apiResponse.StatusCode == StatusCodes.Status200OK || apiResponse.StatusCode == StatusCodes.Status202Accepted)
                {
                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    apiResponse.Message = "Success";
                    apiResponse.Result = "Success";
                    return apiResponse;
                }
            }
            return apiResponse;
        }

        public async Task<ApiResponse<LoginResponse>> ClientIdLogin(LoginRequest loginRequest, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<LoginResponse> response = new ApiResponse<LoginResponse>();
            if (loginRequest != null  && "CLIENT_ID".Equals(loginRequest.Type.ToUpper()))
            {
                response = await CheckClientIdCredentials(loginRequest, baseHttpRequestContext);
            }
            else
            {
                response.StatusCode = StatusCodes.Status400BadRequest;
                response.Errors.Add("Invalid Request");
            }
            return response;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="SystemAdminEmail"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<ClientSecreatView>> ClientIdAndSecretLogin(string SystemAdminEmail, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<ClientSecreatView> apiResponse = new();
            var clientSecretView=await _PasswordAccessDAL.ClientIdAndSecretLogin(SystemAdminEmail);
            if (clientSecretView == null)
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failed to fetch";
                apiResponse.Errors.Add("Invalid System email id.");
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";
                apiResponse.Result = clientSecretView;
            }
            return apiResponse;
        }
        private async Task<ApiResponse<LoginResponse>> CheckClientIdCredentials(LoginRequest loginRequest, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<LoginResponse> apiResponse = new();
            var dbResponse = await _PasswordAccessDAL.GetClientIdloginData(loginRequest, baseHttpRequestContext.OrgId);
            if (dbResponse != null)
            {
                string enteredPassword = loginRequest.FactorValue;
                bool Validpassword = (enteredPassword == dbResponse.FactorValue) ? true : false;
                if (Validpassword)
                {
                    int expire_second = Convert.ToInt32(_appSettings.ClientIdTokenExpiresIn);
                    apiResponse.Result = new LoginResponse();

                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    apiResponse.Message = "Success";
                    apiResponse.Result.IdleTimeOut = (short)dbResponse.IdleTimeOut;
                    apiResponse.Result.Token = _authTokenBAL.GenerateClientIdAuthToken(dbResponse.UserId, dbResponse.OrgId, dbResponse.UserRoleId.Value, baseHttpRequestContext.OrgCode);
                    await _authTokenDAL.AddTokentoDB(dbResponse.UserId, dbResponse.OrgId, apiResponse.Result.Token, expire_second, (short)TokenType.Login);
                    await _authTokenDAL.AddLoginLogtoDB(dbResponse.UserId, dbResponse.OrgId, string.Empty);
                    string UserTokenKey = "_token_" + baseHttpRequestContext.OrgCode + "_" + dbResponse.OrgId + "_" + dbResponse.UserId;

                    try
                    {
                        await _redisCache.SetIntoCache(apiResponse.Result.Token, UserTokenKey);
                    }
                    catch (Exception cacheEx)
                    {
                        _logger.LogWarning(cacheEx, $"Failed to set token in cache for key: {UserTokenKey}");
                        // Continue execution even if cache fails
                    }

                }
                else
                {
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Message = "Unauthenticated";
                    apiResponse.Errors.Add("Invalid UserId/Password.");
                }
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Unauthenticated";
                apiResponse.Errors.Add("Invalid UserId/Password.");
            }
            return apiResponse;
        }
    }
}

