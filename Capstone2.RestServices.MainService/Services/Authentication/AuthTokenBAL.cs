﻿using Capstone2.Framework.Business.Common;
using Capstone2.Framework.RestApi.Context;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.Framework.RestApi.Utility;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.Authentication.Interfaces;
using Capstone2.RestServices.Authentication.Models;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.StackExchangeRedis;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using System;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Authentication.Services
{
    public class AuthTokenBAL : IAuthTokenBAL
    {
        public readonly AppSettings _appSettings;
        private readonly IPasswordAccessDAL _PasswordAccessDAL;
        private readonly IAuthTokenDAL _authTokenDAL;
        private IJwtSecurityTokenHelper _jwtSecurityTokenHelper;
        private IDistributedCacheHelper _redisCache;

        public AuthTokenBAL(IPasswordAccessDAL passwordAccessDAL, IOptions<AppSettings> appSettings, IAuthTokenDAL authTokenDAL, IJwtSecurityTokenHelper jwtSecurityTokenHelper, IDistributedCacheHelper redisCache)
        {
            _PasswordAccessDAL = passwordAccessDAL;
            _appSettings = appSettings.Value;
            _authTokenDAL = authTokenDAL;
            _jwtSecurityTokenHelper = jwtSecurityTokenHelper;
            _redisCache = redisCache;
        }

        public ApiResponse<string> GenerateAnonymousToken(int orgId, string orgCode)
        {
            var apiResponse = new ApiResponse<string>();
            var token = GenerateAnonymousTokenByOrg(orgId, TenantConnectionService.GetFilterDomainName(orgCode));
            apiResponse.Result = token;
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            return apiResponse;
        }

        public async Task<ApiResponse<string>> DeleteToken(long userId)
        {
            var apiResponse = new ApiResponse<string>();
            await _authTokenDAL.DeleteTokenForUser(userId);
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            return apiResponse;
        }

        public async Task<ApiResponse<LoginResponse>> RefreshTokenBAL(long userId, int orgId, int roleId)
        {
            ApiResponse<LoginResponse> response = new ApiResponse<LoginResponse>();
            response.Result = new LoginResponse();
            response.StatusCode = StatusCodes.Status200OK;
            response.Message = "Success";
            var org = await _PasswordAccessDAL.GetOrganisation(orgId);
            response.Result.IdleTimeOut = (short)org.IdleTimeOut;
            response.Result.Token = GenerateAuthToken(userId, orgId, roleId, org.OrgCode);
            int expire_second = Convert.ToInt32(_appSettings.TokenExpiresIn);
            await _authTokenDAL.AddTokentoDB(userId, orgId, response.Result.Token, expire_second, (short)TokenType.Login);
            string UserTokenKey = "_token_" + org.OrgCode+ "_" + orgId + "_" + userId;
            await _redisCache.SetIntoCache(response.Result.Token,UserTokenKey);

            return response;
        }

        /// <summary>
        /// Method to generate Interservice token
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="orgId"></param>
        /// <param name="roleId"></param>
        /// <returns></returns>
        private string GenerateInterServiceToken(long userId, int orgId, int roleId, string orgCode)
        {
            try
            {
                var jwtInterServiceKey = _jwtSecurityTokenHelper.GetJWTSecureKey(isInterServiceKey: true).Result.ToString();
                var key = Encoding.ASCII.GetBytes(jwtInterServiceKey);
                int expire_second = Convert.ToInt32(_appSettings.InterserviceTokenExpiresIn);
                var tokenHandeler = new JwtSecurityTokenHandler();
                var tokenDescriptor = new SecurityTokenDescriptor
                {
                    Subject = new ClaimsIdentity(new Claim[] {
                    new Claim("uid", userId.ToString()),
                    new Claim("oid", orgId.ToString()),
                    new Claim("rid", roleId.ToString()),
                    new Claim("ocode", orgCode),
                }),
                    Expires = DateTime.UtcNow.AddSeconds(expire_second),
                    Issuer = _appSettings.Issuer,
                    Audience = _appSettings.Audience,
                    SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
                };

                var token = tokenHandeler.CreateToken(tokenDescriptor);
                var FinalToken = tokenHandeler.WriteToken(token);
                return FinalToken;
            }
            catch (Exception Ex)
            {
                return null;
            }
        }

        public string GenerateAuthToken(long userId, int orgId, int roleId, string orgCode)
        {
            try
            {
                var jwtKey = _jwtSecurityTokenHelper.GetJWTSecureKey().Result.ToString();
                var key = Encoding.ASCII.GetBytes(jwtKey);
                int expire_second = Convert.ToInt32(_appSettings.TokenExpiresIn);
                var tokenHandeler = new JwtSecurityTokenHandler();
                var tokenDescriptor = new SecurityTokenDescriptor
                {
                    Subject = new ClaimsIdentity(new Claim[] {
                    new Claim("uid", userId.ToString()),
                    new Claim("oid", orgId.ToString()),
                    new Claim("rid", roleId.ToString()),
                    new Claim("ocode", orgCode),
                }),
                    Expires = DateTime.UtcNow.AddSeconds(expire_second),
                    Issuer = _appSettings.Issuer,
                    Audience = _appSettings.Audience,
                    SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
                };

                var token = tokenHandeler.CreateToken(tokenDescriptor);
                var FinalToken = tokenHandeler.WriteToken(token);
                return FinalToken;
            }
            catch (Exception Ex)
            {
                return null;
            }
        }
        public string GenerateClientIdAuthToken(long userId, int orgId, int roleId, string orgCode)
        {
            try
            {
                var jwtKey = _jwtSecurityTokenHelper.GetJWTSecureKey().Result.ToString();
                var key = Encoding.ASCII.GetBytes(jwtKey);
                int expire_second = Convert.ToInt32(_appSettings.ClientIdTokenExpiresIn);
                var tokenHandeler = new JwtSecurityTokenHandler();
                var tokenDescriptor = new SecurityTokenDescriptor
                {
                    Subject = new ClaimsIdentity(new Claim[] {
                    new Claim("uid", userId.ToString()),
                    new Claim("oid", orgId.ToString()),
                    new Claim("rid", roleId.ToString()),
                    new Claim("ocode", orgCode),
                }),
                    Expires = DateTime.UtcNow.AddSeconds(expire_second),
                    Issuer = _appSettings.Issuer,
                    Audience = _appSettings.Audience,
                    SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
                };

                var token = tokenHandeler.CreateToken(tokenDescriptor);
                var FinalToken = tokenHandeler.WriteToken(token);
                return FinalToken;
            }
            catch (Exception Ex)
            {
                return null;
            }
        }
        private string GenerateAnonymousTokenByOrg(int orgId, string orgCode)
        {
            try
            {
                var jwtKey = _jwtSecurityTokenHelper.GetJWTSecureKey().Result.ToString();
                var key = Encoding.ASCII.GetBytes(jwtKey);
                int expire_second = Convert.ToInt32(_appSettings.AnonymousTokenExpiresIn);
                var tokenHandeler = new JwtSecurityTokenHandler();
                var tokenDescriptor = new SecurityTokenDescriptor
                {
                    Subject = new ClaimsIdentity(new Claim[] {
                    new Claim("oid", orgId.ToString()),
                    new Claim("ocode", orgCode)
                }),
                    Expires = DateTime.UtcNow.AddSeconds(expire_second),
                    Issuer = _appSettings.Issuer,
                    Audience = _appSettings.Audience,
                    SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
                };

                var token = tokenHandeler.CreateToken(tokenDescriptor);
                var FinalToken = tokenHandeler.WriteToken(token);
                return FinalToken;
            }
            catch (Exception Ex)
            {
                return null;
            }
        }

        public async Task<ApiResponse<AuthContext>> ValidateJWTTokenAsync(string token)
        {
            var apiresponse = new ApiResponse<AuthContext>();
            apiresponse.Result = new AuthContext();
            if (token == null)
                return apiresponse;

            var tokenHandler = new JwtSecurityTokenHandler();
            var jwtKey = _jwtSecurityTokenHelper.GetJWTSecureKey().Result.ToString();
            var key = Encoding.ASCII.GetBytes(jwtKey);
            try
            {
                ClaimsPrincipal principal = tokenHandler.ValidateToken(token, new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidateIssuer = true,
                    ValidateAudience = true,
                    ValidateActor = false,
                    ValidateLifetime = true,
                    ValidIssuer = _appSettings.Issuer,
                    ValidAudience = _appSettings.Audience,
                    // set clockskew to zero so tokens expire exactly at token expiration time (instead of 5 minutes later)
                    ClockSkew = TimeSpan.Zero
                }, out SecurityToken validatedToken);

                var jwtToken = (JwtSecurityToken)validatedToken;

                var response = new AuthContext()
                {
                    IsAuthenticated = true,
                    Oid = jwtToken.Claims.First(c => c.Type == "oid").Value,
                    Rid = jwtToken.Claims.First(c => c.Type == "rid").Value,
                    Uid = jwtToken.Claims.First(c => c.Type == "uid").Value,
                    Ocode = jwtToken.Claims.First(c => c.Type == "ocode").Value
                };
                var userid = jwtToken.Claims.First(c => c.Type == "uid").Value;
                var orgid = jwtToken.Claims.First(c => c.Type == "oid").Value;
                var roleid = jwtToken.Claims.First(c => c.Type == "rid").Value;
                var orgCode = jwtToken.Claims.First(c => c.Type == "ocode").Value;

                string UserTokenKey = "_token_" + orgCode + "_" + orgid + "_" + userid;

                string tokenFromCache = null;
                try
                {
                    tokenFromCache = await _redisCache.GetFromCache<string>(UserTokenKey);
                }
                catch (Exception cacheEx)
                {
                    _logger.LogWarning(cacheEx, $"Failed to get token from cache for key: {UserTokenKey}");
                }

                if (tokenFromCache == null || tokenFromCache != token)
                {
                    try
                    {

                        var userToken = _authTokenDAL.GetTokenForUser(Convert.ToInt64(userid), Convert.ToInt32(orgid), token);
                        if (userToken is null)
                        {
                            apiresponse.StatusCode = StatusCodes.Status401Unauthorized;
                            apiresponse.Errors.Add("Not valid Token for Auth Service.");
                            return apiresponse;
                        }
                    }
                    catch (Exception ex)
                    {
                        apiresponse.StatusCode = StatusCodes.Status418ImATeapot;
                        apiresponse.Errors.Add("Fetch Token from DB Failed with Exception");
                        apiresponse.Errors.Add("StackTrace" + ex.StackTrace + "Message: " + ex.Message);
                        apiresponse.Errors.Add("InnerException" + ex.InnerException);
                        apiresponse.Errors.Add("Security Key" + key);
                        return apiresponse;

                    }

                    try
                    {
                        await _redisCache.SetIntoCache(token, UserTokenKey);
                    }
                    catch (Exception cacheEx)
                    {
                        _logger.LogWarning(cacheEx, $"Failed to set token in cache for key: {UserTokenKey}");
                        // Continue execution even if cache fails
                    }
                }

                response.InterServiceToken = GenerateInterServiceToken(Convert.ToInt64(userid), Convert.ToInt32(orgid), Convert.ToInt32(roleid), orgCode);

                // return true from JWT token if validation successful
                apiresponse.StatusCode = StatusCodes.Status200OK;
                apiresponse.Result = response;
                return apiresponse;
            }
            catch (Exception ex)
            {
                // return null if validation fails
                apiresponse.StatusCode = StatusCodes.Status401Unauthorized;
                apiresponse.Errors.Add("Not valid Token");
                apiresponse.Errors.Add(ex.Message);
                apiresponse.Errors.Add(ex.StackTrace);

                return apiresponse;
            }
        }

        public ApiResponse<AuthContext> ValidateAnonymousTokenAsync(string token)
        {
            var apiresponse = new ApiResponse<AuthContext>();
            apiresponse.Result = new AuthContext();

            if (token == null)
                return apiresponse;

            var tokenHandler = new JwtSecurityTokenHandler();
            var jwtKey = _jwtSecurityTokenHelper.GetJWTSecureKey().Result.ToString();
            var key = Encoding.ASCII.GetBytes(jwtKey);
            try
            {
                ClaimsPrincipal principal = tokenHandler.ValidateToken(token, new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidateIssuer = true,
                    ValidateAudience = true,
                    ValidateActor = false,
                    ValidateLifetime = true,
                    ValidIssuer = _appSettings.Issuer,
                    ValidAudience = _appSettings.Audience,
                    // set clockskew to zero so tokens expire exactly at token expiration time (instead of 5 minutes later)
                    ClockSkew = TimeSpan.Zero
                }, out SecurityToken validatedToken);

                var jwtToken = (JwtSecurityToken)validatedToken;
                var orgid = jwtToken.Claims.First(c => c.Type == "oid").Value;
                var orgCode = jwtToken.Claims.First(c => c.Type == "ocode").Value;
                var response = new AuthContext()
                {
                    IsAuthenticated = true,
                    Oid = jwtToken.Claims.First(c => c.Type == "oid").Value,
                    Ocode = orgCode,
                    InterServiceToken = GenerateInterServiceToken(0, Convert.ToInt32(orgid), 0, orgCode)

                };
                // return true from JWT token if validation successful
                apiresponse.StatusCode = StatusCodes.Status200OK;
                apiresponse.Result = response;
                return apiresponse;
            }
            catch (Exception ex)
            {
                // return null if validation fails
                apiresponse.StatusCode = StatusCodes.Status401Unauthorized;
                apiresponse.Errors.Add("Not valid Anonymous Token");
                apiresponse.Errors.Add(ex.Message);
                apiresponse.Errors.Add(ex.StackTrace);

                return apiresponse;
            }
        }

        public async Task<ApiResponse<string>> DeleteToken(BaseHttpRequestContext baseHttpRequestContext, short tokenType)
        {
            var apiResponse = new ApiResponse<string>();
            long userId = baseHttpRequestContext.UserId;
            int orgId = baseHttpRequestContext.OrgId;
            string orgCode = baseHttpRequestContext.OrgCode;

            await _authTokenDAL.DeleteToken(userId, orgId, tokenType);
            await _authTokenDAL.UpdateLogoutLogtoDB(userId, orgId);
            string UserTokenKey = "_token_" + orgCode + "_" + orgId + "_" + userId;
            await _redisCache.RemoveCache(UserTokenKey);
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            return apiResponse;
        }

        public ApiResponse<AuthContext> ValidateInterServiceToken(string token)
        {
            var apiresponse = new ApiResponse<AuthContext>();
            apiresponse.Result = new AuthContext();
            if (token == null)
                return apiresponse;

            var tokenHandler = new JwtSecurityTokenHandler();
            var jwtInterServiceKey = _jwtSecurityTokenHelper.GetJWTSecureKey(isInterServiceKey: true).Result.ToString();
            var key = Encoding.ASCII.GetBytes(jwtInterServiceKey);
            try
            {
                ClaimsPrincipal principal = tokenHandler.ValidateToken(token, new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidateIssuer = true,
                    ValidateAudience = true,
                    ValidateActor = false,
                    ValidateLifetime = true,
                    ValidIssuer = _appSettings.Issuer,
                    ValidAudience = _appSettings.Audience,
                    // set clockskew to zero so tokens expire exactly at token expiration time (instead of 5 minutes later)
                    ClockSkew = TimeSpan.Zero
                }, out SecurityToken validatedToken);

                var jwtToken = (JwtSecurityToken)validatedToken;
                var orgid = jwtToken.Claims.First(c => c.Type == "oid").Value;
                var response = new AuthContext()
                {
                    IsAuthenticated = true,
                    Oid = jwtToken.Claims.First(c => c.Type == "oid").Value,
                    Uid = jwtToken.Claims.First(c => c.Type == "uid").Value,
                    Ocode = jwtToken.Claims.First(c => c.Type == "ocode").Value,
                    Rid = jwtToken.Claims.First(c => c.Type == "rid").Value,
                    //  InterServiceToken = GenerateInterServiceToken(0, Convert.ToInt32(orgid), 0, jwtToken.Claims.First(c => c.Type == "ocode").Value)
                    InterServiceToken = token
                };
                // return true from JWT token if validation successful
                apiresponse.StatusCode = StatusCodes.Status200OK;
                apiresponse.Result = response;
                return apiresponse;
            }
            catch (Exception ex)
            {
                // return null if validation fails
                apiresponse.StatusCode = StatusCodes.Status401Unauthorized;
                apiresponse.Errors.Add("Not valid Token");
                return apiresponse;
            }
        }
    }
}

