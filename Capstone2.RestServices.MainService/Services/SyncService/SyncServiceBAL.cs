﻿using AutoMapper;
using Azure.Core;
using Azure.Messaging.WebPubSub;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.SyncService.Interfaces;
using Capstone2.RestServices.SyncService.Models;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using System;
using System.Threading.Tasks;
using System.Web;

namespace Capstone2.RestServices.SyncService.Services
{
    public class SyncServiceBAL : ISyncServiceBAL
    {
        public readonly ISyncServiceDAL _appointmentDetailDAL;
        public IMapper _mapper;
        public readonly AppSettings _appSettings;
        public IConfiguration _configuration { get; }

        public SyncServiceBAL(ISyncServiceDAL appointmentDetailDAL, IMapper mapper, IOptions<AppSettings> appSettings, IConfiguration configuration)
        {
            _appointmentDetailDAL = appointmentDetailDAL;
            _mapper = mapper;
            _appSettings = appSettings.Value;
            _configuration = configuration;
        }

        public async Task<ApiResponse<PubSubConnectionInfo>> GetClientPUBSUBToken(string ScreenName, string orgCode)
        {
            ApiResponse<PubSubConnectionInfo> apiResponse = new ApiResponse<PubSubConnectionInfo>();
            string hubName = orgCode + ScreenName;
            var connectionString = _configuration.GetConnectionString("AzurePubSubConnection");
            var serverClient = new WebPubSubServiceClient(connectionString, hubName);
            var token = serverClient.GetClientAccessUri(TimeSpan.FromHours(1));

            PubSubConnectionInfo connectionInfo = new PubSubConnectionInfo()
            {
                CompleteAccessUrl = token.ToString(),
                AccessToken = HttpUtility.ParseQueryString(token.ToString()).Get("access_token"),
                HubUrl = token.AbsolutePath,
                ScreenName = ScreenName,
                HubName = hubName
            };


            apiResponse.Result = connectionInfo;
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            return apiResponse;
        }

        public ApiResponse<string> SendDataToHubGeneric(PubSubGenericResponse hubData, string orgCode)
        {
            if (hubData is not null)
            {
                var connectionString = _configuration.GetConnectionString("AzurePubSubConnection");
                var hubName = hubData.HubName;
                var serverClient = new WebPubSubServiceClient(connectionString, hubName);

                var response = serverClient.SendToAll(RequestContent.Create(new
                {
                    Data = hubData.AppointmentList,
                    hubName = hubName,
                    NotificationCount = hubData.NotificationCount,
                    UserId = hubData.UserId
                }), ContentType.ApplicationJson);

                return new ApiResponse<string>()
                {
                    Message = "Data sent successfully",
                    StatusCode = response.Status,
                    Result = "Success"
                };

            }

            return new ApiResponse<string>()
            {
                Message = "Send Data to Hub Failed.",
                StatusCode = StatusCodes.Status400BadRequest,
                Result = "Failed"
            };
        }
    }
}
    