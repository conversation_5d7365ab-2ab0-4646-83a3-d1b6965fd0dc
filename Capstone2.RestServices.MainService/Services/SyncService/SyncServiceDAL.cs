﻿using Capstone2.RestServices.SyncService.Context;
using Capstone2.RestServices.SyncService.Interfaces;
using Capstone2.RestServices.SyncService.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;

namespace Capstone2.RestServices.SyncService.Services
{
    public class SyncServiceDAL : ISyncServiceDAL
    {
        public readonly ReadOnlySyncServiceDBContext _readOnlyDbContext;
        public readonly UpdatableSyncServiceDBContext _updatableDBContext;

        public SyncServiceDAL(ReadOnlySyncServiceDBContext readOnlyDbContext, UpdatableSyncServiceDBContext updatableDBContext)
        {
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
        }
    }
}
