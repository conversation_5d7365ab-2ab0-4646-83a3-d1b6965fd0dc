﻿using Capstone2.RestServices.Snapforms.Interfaces;
using Capstone2.RestServices.Snapforms.Models;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Snapforms.Services
{
    public class SnapformsConfigBAL : ISnapformsConfigBAL
    {
        private readonly ISnapformsConfigDAL _snapformsConfigDAL;
        public SnapformsConfigBAL(ISnapformsConfigDAL snapformsConfigDAL)
        {
            _snapformsConfigDAL = snapformsConfigDAL;
        }

        public async Task<SnapformsConfig> GetSnapformsConfigs(int orgId)
        {
            return await _snapformsConfigDAL.GetSnapformsConfigs(orgId);
        }
    }
}
