﻿using Capstone2.Framework.Business.Common;
using Capstone2.Framework.RestApi;
using Capstone2.RestServices.Snapforms.Interfaces;
using Capstone2.RestServices.Snapforms.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using static Capstone2.RestServices.Snapforms.Common.ConstantsHelper;

namespace Capstone2.RestServices.Snapforms.Services
{
    public class HelperService : IHelperService
    {
        private readonly SnapformsAPISettings _snapformsApiSetting;
        private readonly IConfiguration _configuration;
        private IDistributedCacheHelper _redisCache;
        private readonly ISnapformsConfigBAL _snapformsConfigBAL;
        public HelperService(IOptions<SnapformsAPISettings> options, IConfiguration configuration, IDistributedCacheHelper cache, ISnapformsConfigBAL snapformsConfigBAL)
        {
            _snapformsApiSetting = options.Value;
            _configuration = configuration;
            _redisCache = cache;
            _snapformsConfigBAL = snapformsConfigBAL;
        }

        /// <summary>
        /// Get Snapform headers for requesting forms API
        /// </summary>
        /// <returns>Header key/Value</returns>
        public async Task<Dictionary<string, string>> GetSnapformSecurityHeader(int orgId, string orgCode)
        {
            string cachedSnapformsAuthTokenKey = $"{_configuration["AppSettings:CachedSnapformsAuthTokenKey"]}_{orgId}_{orgCode}";
            BearerToken authBearerToken = await _redisCache.GetFromCache<BearerToken>(cachedSnapformsAuthTokenKey);
            if (authBearerToken == null || authBearerToken.token_expire_at <= DateTime.UtcNow)
            {
                authBearerToken = await GetSnapformBearerToken(orgId).ConfigureAwait(false);
                authBearerToken.token_generated_at = DateTime.UtcNow;
                authBearerToken.token_expire_at = DateTime.UtcNow.AddSeconds(authBearerToken.expires_in).AddMinutes(-15);
                await _redisCache.SetIntoCache(authBearerToken, cachedSnapformsAuthTokenKey).ConfigureAwait(false);
            }

            return new Dictionary<string, string> { { "Authorization", $"{authBearerToken.token_type} {authBearerToken.access_token}" } };
        }

        /// <summary>
        /// Generate OAuth2 authentication bearer token from Snapform
        /// </summary>
        /// <returns></returns>
        private async Task<BearerToken> GetSnapformBearerToken(int orgId)
        {
            var orgSnapformConfig = await _snapformsConfigBAL.GetSnapformsConfigs(orgId);
            var headerKeys = new Dictionary<string, string>
            {
                { "client_id", orgSnapformConfig?.ClientID },
                { "client_secret", orgSnapformConfig?.ClientSecret },
                { "username", orgSnapformConfig?.UserName },
                { "password", orgSnapformConfig?.Password },
                { "grant_type", _snapformsApiSetting.Grant_Type }
            };

            string resourceUri = $"{_snapformsApiSetting.BaseApiUrl}/{GetEndpointByKey(_snapformsApiSetting.APIEndPoints, nameof(TypeofSnapformsAPI.AuthToken))}";
            var restClient = new RestClient(resourceUri, headerKeys);
            return await restClient.PostAsync<BearerToken>(resourceUri, headerKeys, "application/x-www-form-urlencoded").ConfigureAwait(false);
        }

    }
}
