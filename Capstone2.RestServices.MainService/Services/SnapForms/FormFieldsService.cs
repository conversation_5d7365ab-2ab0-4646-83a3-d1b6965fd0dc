﻿using Capstone2.Framework.RestApi;
using Capstone2.RestServices.Snapforms.Interfaces;
using Capstone2.RestServices.Snapforms.Models;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using static Capstone2.RestServices.Snapforms.Common.ConstantsHelper;

namespace Capstone2.RestServices.Snapforms.Services
{
    public class FormFieldsService : IFormFieldsService
    {
        private readonly SnapformsAPISettings _snapformsApiSetting;
        private readonly IHelperService _helperService;
        public FormFieldsService(IOptions<SnapformsAPISettings> options, IHelperService helperService)
        {
            _snapformsApiSetting = options.Value;
            _helperService = helperService;
        }

        /// <summary>
        /// Search request for form fields
        /// </summary>
        /// <param name="orgId">orgId</param>
        /// <param name="orgCode">orgCode</param>
        /// <returns>Lists all accessible forms</returns>
        public async Task<List<FormFieldDetails>> GetAllForms(int orgId, string orgCode)
        {
            string requestUrl = $"{_snapformsApiSetting.BaseApiUrl}/{GetEndpointByKey(_snapformsApiSetting.APIEndPoints, TypeofSnapformsAPI.AllForms.ToString())}";
            Dictionary<string, string> headerKeys = await _helperService.GetSnapformSecurityHeader(orgId, orgCode);

            RestClient restClient = new RestClient(requestUrl, headerKeys);
            var response = await restClient.GetAsync<List<FormFieldDetails>>(requestUrl, null);
            return response;
        }

        /// <summary>
        /// This API returns all the fields for a particular form
        /// </summary>
        /// <param name="orgId">orgId</param>
        /// <param name="orgCode">orgCode</param>
        /// <param name="formName">formName</param>
        /// <returns>Return form fields  - type, name, label and required</returns>
        public async Task<List<ViewFormFields>> GetViewFormFields(int orgId, string orgCode, string formSlug)
        {
            string requestUrl = string.Format($"{_snapformsApiSetting.BaseApiUrl}/{GetEndpointByKey(_snapformsApiSetting.APIEndPoints, TypeofSnapformsAPI.ViewFormFields.ToString())}", formSlug);
            Dictionary<string, string> headerKeys = await _helperService.GetSnapformSecurityHeader(orgId, orgCode);

            RestClient restClient = new RestClient(requestUrl, headerKeys);
            var response = await restClient.GetAsync<List<ViewFormFields>>(requestUrl, null);
            return response;
        }

        /// <summary>
        /// Search request by slug and response_id
        /// </summary>
        /// <param name="orgId">orgId</param>
        /// <param name="orgCode">orgCode</param>
        /// <param name="formSlug">Replace {{formSlug}} with your unique Form identifier</param>
        /// <param name="responseId">Replace {{response_id}} with the internal Response identifier</param>
        /// <returns>Retrieve a Form Response using a particular response identifier</returns>
        public async Task<SingleResponse> GetSingleResponse(int orgId, string orgCode, string formSlug, string responseId)
        {
            string requestUrl = string.Format($"{_snapformsApiSetting.BaseApiUrl}/{GetEndpointByKey(_snapformsApiSetting.APIEndPoints, TypeofSnapformsAPI.SingleResponse.ToString())}", formSlug, responseId);
            Dictionary<string, string> headerKeys = await _helperService.GetSnapformSecurityHeader(orgId, orgCode);

            RestClient restClient = new RestClient(requestUrl, headerKeys);
            var response = await restClient.GetAsync<SingleResponse>(requestUrl, null);
            return response;
        }

        /// <summary>
        /// Search request by search, field, date_from, date_to, date_modified_from and date_modified_to
        /// </summary>
        /// <param name="orgId">orgId</param>
        /// <param name="orgCode">orgCode</param>
        /// <param name="formSlug">formSlug</param>
        /// <param name="paramsRequest">search, field, date_from, date_to, date_modified_from and date_modified_to</param>
        /// <returns>Return forms details</returns>
        public async Task<SearchResponse> GetSearchResponse(int orgId, string orgCode, string formSlug, SearchParamFilterModel paramsRequest)
        {
            var properties = from pop in paramsRequest.GetType().GetProperties()
                             where pop.GetValue(paramsRequest, null) != null
                             select pop.Name + "=" + HttpUtility.UrlEncode(pop.GetValue(paramsRequest, null).ToString());
            string queryString = string.Join("&", properties.ToArray());
            string requestUrl = string.Format($"{_snapformsApiSetting.BaseApiUrl}/{GetEndpointByKey(_snapformsApiSetting.APIEndPoints, TypeofSnapformsAPI.SearchResponses.ToString())}?{queryString}", formSlug);
            Dictionary<string, string> headerKeys = await _helperService.GetSnapformSecurityHeader(orgId, orgCode);

            RestClient restClient = new RestClient(requestUrl, headerKeys);
            var response = await restClient.GetAsync<SearchResponse>(requestUrl, null);
            return response;
        }

        /// <summary>
        /// Post form data
        /// </summary>
        /// <param name="orgId">orgId</param>
        /// <param name="orgCode">orgCode</param>
        /// <param name="formSlug">formSlug</param>
        /// <param name="paramsRequest">paramsRequest Object</param>
        /// <returns></returns>
        public async Task<Response> PostSingleResponse(int orgId, string orgCode, string formSlug, FormDataWrapper paramsRequest)
        {
            string requestUrl = string.Format($"{_snapformsApiSetting.BaseApiUrl}/{GetEndpointByKey(_snapformsApiSetting.APIEndPoints, TypeofSnapformsAPI.Post_SingleResponse.ToString())}", formSlug);
            Dictionary<string, string> headerKeys = await _helperService.GetSnapformSecurityHeader(orgId, orgCode);
            var formDataModel = new FormDataModel { formdata = paramsRequest.FormData };
            RestClient restClient = new RestClient(requestUrl, headerKeys);
            var response = await restClient.PostAsync<Response>(requestUrl, formDataModel);
            return response;
        }

        /// <summary>
        /// Delete by formSlug and response_id
        /// </summary>
        /// <param name="orgId">orgId</param>
        /// <param name="orgCode">orgCode</param>
        /// <param name="formSlug">Replace {{formSlug}} with your unique Form identifier</param>
        /// <param name="responseId">Replace {{response_id}} with the internal Response identifier</param>
        /// <returns>Retrieve a Form Response using a particular response identifier</returns>
        public async Task<Response> DeleteSingleResponse(int orgId, string orgCode, string formSlug, string responseId)
        {
            string requestUrl = string.Format($"{_snapformsApiSetting.BaseApiUrl}/{GetEndpointByKey(_snapformsApiSetting.APIEndPoints, TypeofSnapformsAPI.Delete_SingleResponse.ToString())}", formSlug, responseId);
            Dictionary<string, string> headerKeys = await _helperService.GetSnapformSecurityHeader(orgId, orgCode);

            RestClient restClient = new RestClient(requestUrl, headerKeys);
            var response = await restClient.DeleteAsync<Response>(requestUrl, null);
            return response;
        }

        /// <summary>
        /// Search request by slug and response_id
        /// </summary>
        /// <param name="orgId">orgId</param>
        /// <param name="orgCode">orgCode</param>
        /// <param name="formSlug">Replace {{formSlug}} with your unique Form identifier</param>
        /// <param name="responseId">Replace {{response_id}} with the internal Response identifier</param>
        /// <returns>Retrieve a temporary PDF url that can be used to download the PDF with the response</returns>
        public async Task<PDFUrl> GetPDFURL(int orgId, string orgCode, string formSlug, string responseId)
        {
            string requestUrl = string.Format($"{_snapformsApiSetting.BaseApiUrl}/{GetEndpointByKey(_snapformsApiSetting.APIEndPoints, TypeofSnapformsAPI.PDFUrl.ToString())}", formSlug, responseId);
            Dictionary<string, string> headerKeys = await _helperService.GetSnapformSecurityHeader(orgId, orgCode);

            RestClient restClient = new RestClient(requestUrl, headerKeys);
            var response = await restClient.GetAsync<PDFUrl>(requestUrl, null);
            return response;
        }

        /// <summary>
        /// Search View Logs by from_date and to_date
        /// </summary>
        /// <param name="orgId">orgId</param>
        /// <param name="orgCode">orgCode</param>
        /// <param name="fromDate">fromDate</param>
        /// <param name="toDate">toDate</param>
        /// <param name="pageNumber">pageNumber</param>
        /// <param name="pageSize">pageSize</param>
        /// <returns>Return View Logs</returns>
        public async Task<ViewLogs> GetViewLogs(int orgId, string orgCode, string fromDate, string toDate, int pageNumber, int pageSize)
        {
            string requestUrl = string.Format($"{_snapformsApiSetting.BaseApiUrl}/{GetEndpointByKey(_snapformsApiSetting.APIEndPoints, TypeofSnapformsAPI.ViewLogs.ToString())}", fromDate, toDate, pageSize, pageNumber);
            Dictionary<string, string> headerKeys = await _helperService.GetSnapformSecurityHeader(orgId, orgCode);

            RestClient restClient = new RestClient(requestUrl, headerKeys);
            var response = await restClient.GetAsync<ViewLogs>(requestUrl, null);
            return response;
        }

    }
}
