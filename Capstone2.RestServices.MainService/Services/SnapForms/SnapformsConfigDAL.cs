﻿using Capstone2.RestServices.Snapforms.Context;
using Capstone2.RestServices.Snapforms.Interfaces;
using Capstone2.RestServices.Snapforms.Models;
using Microsoft.EntityFrameworkCore;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Snapforms.Services
{
    public class SnapformsConfigDAL : ISnapformsConfigDAL
    {
        public readonly ReadOnlySnapformsDBContext _readOnlyDbContext;
        public readonly UpdatableSnapformsDBContext _updatableDBContext;
        public SnapformsConfigDAL(ReadOnlySnapformsDBContext readOnlyDbContext, UpdatableSnapformsDBContext updatableDBContext)
        {
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
        }

        public async Task<SnapformsConfig> GetSnapformsConfigs(int orgId)
        {
            return await _readOnlyDbContext.SnapformsConfigs.FirstOrDefaultAsync(x => x.OrgId == orgId && x.Status == true);
        }
    }
}
