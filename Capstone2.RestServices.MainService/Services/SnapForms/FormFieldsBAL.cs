﻿using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.Snapforms.Interfaces;
using Capstone2.RestServices.Snapforms.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Snapforms.Services
{
    public class FormFieldsBAL : IFormFieldsBAL
    {
        public IFormFieldsService _formFieldsService;
        public FormFieldsBAL(IFormFieldsService formFieldsService)
        {
            _formFieldsService = formFieldsService;
        }

        public async Task<ApiResponse<List<FormFieldDetails>>> GetAllForms(BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<List<FormFieldDetails>> apiResponse = new ApiResponse<List<FormFieldDetails>>();
            List<FormFieldDetails> formFieldList = await _formFieldsService.GetAllForms(baseHttpRequestContext.OrgId, baseHttpRequestContext.OrgCode);
            apiResponse.Result = formFieldList;
            apiResponse.Message = "Success";
            apiResponse.StatusCode = StatusCodes.Status200OK;
            return apiResponse;
        }
        public async Task<ApiResponse<List<ViewFormFields>>> GetViewFormFields(string formSlug, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<List<ViewFormFields>> apiResponse = new ApiResponse<List<ViewFormFields>>();
            List<ViewFormFields> response = await _formFieldsService.GetViewFormFields(baseHttpRequestContext.OrgId, baseHttpRequestContext.OrgCode, formSlug);
            apiResponse.Result = response;
            apiResponse.Message = "Success";
            apiResponse.StatusCode = StatusCodes.Status200OK;
            return apiResponse;
        }

        public async Task<ApiResponse<SingleResponse>> GetSingleResponse(string formSlug, string responseId, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<SingleResponse> apiResponse = new ApiResponse<SingleResponse>();
            SingleResponse response = await _formFieldsService.GetSingleResponse(baseHttpRequestContext.OrgId, baseHttpRequestContext.OrgCode, formSlug, responseId);
            apiResponse.Result = response;
            apiResponse.Message = "Success";
            apiResponse.StatusCode = StatusCodes.Status200OK;
            return apiResponse;
        }

        public async Task<ApiResponse<SearchResponse>> GetSearchResponse(string formSlug, QueryModel queryModel, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<SearchResponse> apiResponse = new ApiResponse<SearchResponse>();
            var filterModel = PrepareFilterParameters<SearchParamFilterModel>(queryModel.Filter);
            if (!string.IsNullOrWhiteSpace(filterModel.date_from))
                filterModel.date_from = Convert.ToDateTime(filterModel.date_from).ToString("yyyy-MM-dd 00:00:00");
            if (!string.IsNullOrWhiteSpace(filterModel.date_to))
                filterModel.date_to = Convert.ToDateTime(filterModel.date_to).ToString("yyyy-MM-dd 23:59:00");
            filterModel.limit = Convert.ToString(queryModel.PageSize);
            SearchResponse response = await _formFieldsService.GetSearchResponse(baseHttpRequestContext.OrgId, baseHttpRequestContext.OrgCode, formSlug, filterModel);
            apiResponse.Result = response;
            apiResponse.Message = "Success";
            apiResponse.StatusCode = StatusCodes.Status200OK;
            return apiResponse;
        }

        public async Task<ApiResponse<Response>> PostSingleResponse(string formSlug, FormDataWrapper paramsRequest, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<Response> apiResponse = new ApiResponse<Response>();
            Response response = await _formFieldsService.PostSingleResponse(baseHttpRequestContext.OrgId, baseHttpRequestContext.OrgCode, formSlug, paramsRequest);
            apiResponse.Result = response;
            apiResponse.Message = "Success";
            apiResponse.StatusCode = StatusCodes.Status200OK;
            return apiResponse;
        }

        public async Task<ApiResponse<Response>> DeleteSingleResponse(string formSlug, string responseId, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<Response> apiResponse = new ApiResponse<Response>();
            Response response = await _formFieldsService.DeleteSingleResponse(baseHttpRequestContext.OrgId, baseHttpRequestContext.OrgCode, formSlug, responseId);
            apiResponse.Result = response;
            apiResponse.Message = "Success";
            apiResponse.StatusCode = StatusCodes.Status200OK;
            return apiResponse;
        }

        public async Task<ApiResponse<PDFUrl>> GetPDFURL(string formSlug, string responseId, bool isFile, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<PDFUrl> apiResponse = new ApiResponse<PDFUrl>();
            PDFUrl response = await _formFieldsService.GetPDFURL(baseHttpRequestContext.OrgId, baseHttpRequestContext.OrgCode, formSlug, responseId);
            if (isFile)
                response.blob = await GetDownloadFileFromSource(response.url);
            apiResponse.Result = response;
            apiResponse.Message = "Success";
            apiResponse.StatusCode = StatusCodes.Status200OK;
            return apiResponse;
        }

        private async Task<byte[]> GetDownloadFileFromSource(string fileUrl)
        {
            using (HttpClient client = new HttpClient())
            {
                HttpResponseMessage response = await client.GetAsync(fileUrl);
                response.EnsureSuccessStatusCode();
                return await response.Content.ReadAsByteArrayAsync();
            }
        }

        public async Task<ApiResponse<ViewLogs>> GetViewLogs(QueryModel queryModel, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<ViewLogs> apiResponse = new ApiResponse<ViewLogs>();
            var filterModel = PrepareFilterParameters<LogsParamFilterModel>(queryModel.Filter);
            ViewLogs response = await _formFieldsService.GetViewLogs(baseHttpRequestContext.OrgId, baseHttpRequestContext.OrgCode, filterModel.fromdate, filterModel.todate, queryModel.PageNumber, queryModel.PageSize);
            apiResponse.Result = response;
            apiResponse.Message = "Success";
            apiResponse.StatusCode = StatusCodes.Status200OK;
            return apiResponse;
        }
        private T PrepareFilterParameters<T>(string filter) where T : class
        {
            if (!string.IsNullOrEmpty(filter))
            {
                return JsonConvert.DeserializeObject<T>(filter);
            }
            else
            {
                return null;
            }
        }

    }
}
