﻿using Capstone2.RestServices.SSRSReport.Context;
using Capstone2.RestServices.SSRSReport.Interfaces;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
//using System.Data.SqlClient;
using Microsoft.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using Capstone2.RestServices.SSRSReport.Model;
using Capstone2.Framework.RestApi.Context;

namespace Capstone2.RestServices.SSRSReport.Services
{
    public class SSRSReportDAL : ISSRSReportDAL
    {
        public readonly ReadOnlySSRSReportDBContext _readOnlyDbContext;
        public readonly UpdatableSSRSReportDBContext _updatableDBContext;
        public readonly ReadOnlyDBMasterContext _readMasterOnlyDbContext;

        public SSRSReportDAL(ReadOnlySSRSReportDBContext readOnlyDbContext, UpdatableSSRSReportDBContext updatableDBContext, ReadOnlyDBMasterContext readMasterOnlyDbContext)
        {
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
            _readMasterOnlyDbContext = readMasterOnlyDbContext;
        }

        public async Task<long> AddReportFilter(UserReportFilter userReportFilter)
        {
            try
            {
                await _updatableDBContext.UserReportFilter.AddAsync(userReportFilter);
                await _updatableDBContext.SaveChangesAsync();
                return (long)userReportFilter.ID;
            }
            catch (Exception)
            {

                throw;
            }
        }

        public async Task<long> UpdateReportFilter(UserReportFilter userReportFilter)
        {
            _updatableDBContext.UserReportFilter.Update(userReportFilter);
            await _updatableDBContext.SaveChangesAsync();
            return (long)userReportFilter.ID;
        }

        public string GetReportNameByReportId(ReportRequestFilter userReportFilter)
        {
            return _readMasterOnlyDbContext.ReportDetails.Where(d => d.ID == userReportFilter.reportId).Select(d=>d.ReportPath).FirstOrDefault();
        }

        public async Task<UserReportFilter> GetUserReportFilter(UserReportFilter userReportFilter)
        {
            return await _readOnlyDbContext.UserReportFilter.Where(d => d.UserId == userReportFilter.UserId && d.ReportId == userReportFilter.ReportId).FirstOrDefaultAsync();
        }
        public async Task<List<DataSet>> ListAppointmentDayDetails(ReportRequestFilter reportRequestFilter)
        {
   
            List<DataSet> dataSets = new List<DataSet>();
            var reportDetail = _readMasterOnlyDbContext.ReportDetailStoredProcs.AsNoTracking()
                                .Where(d => d.ReportId == reportRequestFilter.reportId)
                                .ToList();
            foreach (var item in reportDetail)
            {
                List<SqlParameter> paramsList = new List<SqlParameter>();
                paramsList.Add(new SqlParameter("@ReportId", reportRequestFilter.reportId));
                paramsList.Add(new SqlParameter("@UserId", reportRequestFilter.userId));
                if(reportRequestFilter.reportId == 8 && item.StoredProcName == "SSRS.rpt_MbsListReport")
                {
                    var masterdata = await ExecuteMasterDBSP(item.StoredProcName, paramsList);
                    masterdata.DataSetName = item.ReportDataSetName;
                    dataSets.Add(masterdata);

                }
                else { 
                    var ds = await ExecuteStoredProcedure(item.StoredProcName, paramsList);
                    ds.DataSetName = item.ReportDataSetName;
                    dataSets.Add(ds);
                }

            }
            return dataSets;
            
          
        }
        private async Task<DataSet> ExecuteStoredProcedure(string storedProcedureName, IEnumerable<SqlParameter> parameters)
        {
            var dt = new DataSet();
            using (var conn = new SqlConnection(_readOnlyDbContext.Database.GetDbConnection().ConnectionString))
            {
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandText = storedProcedureName;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandTimeout = 1800;
                    foreach (var parameter in parameters)
                    {
                        cmd.Parameters.Add(parameter);
                    }

                    using (var adapter = new SqlDataAdapter(cmd))
                    {
                        adapter.Fill(dt);
                    }
                }
            }

            return dt;

        }

        private async Task<DataSet> ExecuteMasterDBSP(string storedProcedureName, IEnumerable<SqlParameter> parameters)
        {
            var dt = new DataSet();
            using (var conn = new SqlConnection(_readMasterOnlyDbContext.Database.GetDbConnection().ConnectionString))
            {
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandText = storedProcedureName;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandTimeout = 1800;
                    foreach (var parameter in parameters)
                    {
                        cmd.Parameters.Add(parameter);
                    }

                    using (var adapter = new SqlDataAdapter(cmd))
                    {
                        adapter.Fill(dt);
                    }
                }
            }

            return dt;

        }


    }
}
