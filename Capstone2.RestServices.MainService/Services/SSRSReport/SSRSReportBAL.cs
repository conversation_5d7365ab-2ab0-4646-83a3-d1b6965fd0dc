﻿    using BoldReports.Web;
using Capstone2.RestServices.SSRSReport.Interfaces;
using Capstone2.RestServices.SSRSReport.Model;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.RestServices.SSRSReport.Services
{
    public class SSRSReportBAL : ISSRSReportBAL
    {
        public ISSRSReportDAL _ssrsReportDAL;

        public SSRSReportBAL(ISSRSReportDAL ssrsReportDAL)
        {
            _ssrsReportDAL = ssrsReportDAL;
        }

        public async Task<ApiResponse<long>> AddReportFilter(UserReportFilter userReportFilter)
        {
            ApiResponse<long> apiResponse = new ApiResponse<long>();
            long id = 0;
            userReportFilter.ModifiedBy = userReportFilter.UserId;
            var reportFilter = await _ssrsReportDAL.GetUserReportFilter(userReportFilter);
            if (reportFilter == null)
            {
                id = await _ssrsReportDAL.AddReportFilter(userReportFilter);
            }
            else
            {
                reportFilter.FilterData = userReportFilter.FilterData;
                id = await _ssrsReportDAL.UpdateReportFilter(reportFilter);
            }
            if (id > 0)
            {
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";
                apiResponse.Result = id;
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Errors.Add("Report Filter cannot be created at this time.");
            }

            return apiResponse;
        }

        public string GetReportNameByReportId(ReportRequestFilter reportRequestFilter)
        {
            return  _ssrsReportDAL.GetReportNameByReportId(reportRequestFilter);
        }

        public async Task<ReportDataSourceCollection> ListAppointmentDayDetails(ReportRequestFilter reportRequestFilter)
        {
            ReportDataSourceCollection reportDataSource = new ReportDataSourceCollection();
            var dataSet = await _ssrsReportDAL.ListAppointmentDayDetails(reportRequestFilter);
            foreach (var ds in dataSet)
            {
                ReportDataSource reportDS = new ReportDataSource();
                reportDS.Name = ds.DataSetName;
                reportDS.Value = ds;
                reportDataSource.Add(reportDS);
            }
            return reportDataSource;
        }
    }
}
