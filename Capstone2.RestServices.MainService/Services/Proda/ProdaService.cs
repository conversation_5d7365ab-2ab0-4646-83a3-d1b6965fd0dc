﻿using Capstone2.Framework.RestApi;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.Proda.Interfaces;
using Capstone2.RestServices.Proda.Models;
using Capstone2.RestServices.Proda.Utility;
using Capstone2.Shared.Models.Communication;
using Capstone2.Shared.Models.Medicare;
using Capstone2.Shared.Models.Proda;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Proda.Services
{
    public class ProdaService : IProdaService
    {
        private readonly ILogger<ProdaService> _logger;
        private readonly AppSettings _appSettings;
        private readonly PRODASettings _prodaSettings;
        private readonly IProdaCertificateHelper _certificateHelper;
        private readonly IProdaKeyVaultHelper _keyVaultHelper;
        private readonly IProdaConfigsBAL _prodaConfigsBAL;
        private readonly IRequestLogsBAL _requestLogsBAL;
        private readonly ISubscriberDetailsBAL _subscriberDetailsBAL;
        public ProdaService(ILogger<ProdaService> logger, IOptions<AppSettings> appSettings, IOptions<PRODASettings> prodaSettings, IProdaKeyVaultHelper keyVaultHelper, IProdaCertificateHelper certificateHelper, IProdaConfigsBAL prodaConfigsBAL, IRequestLogsBAL requestLogsBAL, ISubscriberDetailsBAL subscriberDetailsBAL)
        {
            _logger = logger;
            _appSettings = appSettings.Value;
            _prodaSettings = prodaSettings.Value;
            _keyVaultHelper = keyVaultHelper;
            _certificateHelper = certificateHelper;
            _prodaConfigsBAL = prodaConfigsBAL;
            _requestLogsBAL = requestLogsBAL;
            _subscriberDetailsBAL = subscriberDetailsBAL;
        }

        /// <summary>
        /// Proda Request for device activate
        /// </summary>
        /// <returns></returns>
        public async Task<ApiResponse<dynamic>> ActivateDevice(BaseHttpRequestContext baseHttpRequestContext)
        {
            var apiResponse = new ApiResponse<dynamic>();
            byte[] rsaCertificateBytes = _certificateHelper.GenerateRSACertificateBytes(_prodaSettings.ProdaDeviceName);

            //Save into Azure KV
            await _keyVaultHelper.GetSetCertificateKey(_prodaSettings.KVCertificateName, _prodaSettings.KVTenantId, _prodaSettings.KVClientId, _prodaSettings.KVClientSecret, _prodaSettings.KVCertificateUri, rsaCertificateBytes);

            string publicKey = _certificateHelper.GeneratePublicKey(_certificateHelper.GetRSACertificate(rsaCertificateBytes), _prodaSettings.ProdaDeviceName, _prodaSettings.ProdaDeviceActivationCode, _prodaSettings.ProdaOrganisationRA);

            apiResponse = await RequestProdaService(publicKey, baseHttpRequestContext.OrgId, baseHttpRequestContext.UserId);

            return apiResponse;
        }

        /// <summary>
        /// Proda Request for device token
        /// </summary>
        /// <param name="isRefreshPublicKey"></param>
        /// <returns></returns>
        public async Task<ApiResponse<dynamic>> GetAuthenticationToken(BaseHttpRequestContext baseHttpRequestContext, bool isRefreshPublicKey = true)
        {
            var apiResponse = new ApiResponse<dynamic>();
            ProdaConfigs prodaConfig = await _prodaConfigsBAL.GetProdaConfigs();
            if (prodaConfig != null && prodaConfig.DeviceExpiry != null)
                await SendDeviceExpiryNotification(prodaConfig, baseHttpRequestContext);
            if (prodaConfig != null && prodaConfig.KeyExpiry.Value.Date >= DateTime.UtcNow.Date && (prodaConfig.AccessTokenExpiry != null && (prodaConfig.AccessTokenExpiry.Value - DateTime.UtcNow).TotalMinutes > 0) && !string.IsNullOrWhiteSpace(prodaConfig.AccessToken))
            {
                apiResponse.Result = prodaConfig.AccessToken;
            }
            else if (prodaConfig != null && prodaConfig.KeyExpiry.Value.Date > DateTime.UtcNow.Date && (prodaConfig.AccessTokenExpiry == null || (prodaConfig.AccessTokenExpiry.Value - DateTime.UtcNow).TotalMinutes <= 0))
            {
                //Get private certificate from Azure KV
                byte[] rsaCertificateBytes = await _keyVaultHelper.GetSetCertificateKey(_prodaSettings.KVCertificateName, _prodaSettings.KVTenantId, _prodaSettings.KVClientId, _prodaSettings.KVClientSecret, _prodaSettings.KVCertificateUri);
                apiResponse = await ProdaAuthenticationToken(rsaCertificateBytes, prodaConfig, baseHttpRequestContext.OrgId, baseHttpRequestContext.UserId);
                if (apiResponse.StatusCode == StatusCodes.Status200OK && !(apiResponse.Result is null))
                {
                    JObject jresult = JObject.Parse(apiResponse.Result);
                    string token = (string)jresult["access_token"];
                    apiResponse.Result = token;
                }
            }
            else if (prodaConfig != null && prodaConfig.KeyExpiry.Value.Date <= DateTime.UtcNow.Date && ( string.IsNullOrWhiteSpace(prodaConfig.AccessToken) ||    prodaConfig.AccessTokenExpiry == null || (prodaConfig.AccessTokenExpiry.Value - DateTime.UtcNow).TotalMinutes <= 0))
            {
                //Get private certificate from Azure KV
                byte[] rsaCertificateBytes = await _keyVaultHelper.GetSetCertificateKey(_prodaSettings.KVCertificateName, _prodaSettings.KVTenantId, _prodaSettings.KVClientId, _prodaSettings.KVClientSecret, _prodaSettings.KVCertificateUri);
                apiResponse = await ProdaAuthenticationToken(rsaCertificateBytes, prodaConfig, baseHttpRequestContext.OrgId, baseHttpRequestContext.UserId);
                if (apiResponse.StatusCode == StatusCodes.Status200OK && !(apiResponse.Result is null))
                {
                    JObject jresult = JObject.Parse(apiResponse.Result);
                    string token = (string)jresult["access_token"];
                    apiResponse.Result = token;
                }
            }
            else if (isRefreshPublicKey && prodaConfig != null && prodaConfig.KeyExpiry.Value.Date < DateTime.UtcNow.Date)
            {
                apiResponse.Result = await RefreshPublicKey(baseHttpRequestContext);
            }
           

            return apiResponse;
        }

        /// <summary>
        /// Proda Request for device refresh
        /// </summary>
        /// <returns></returns>
        public async Task<ApiResponse<dynamic>> RefreshPublicKey(BaseHttpRequestContext baseHttpRequestContext)
        {
            var apiResponse = new ApiResponse<dynamic>();
            apiResponse = await GetAuthenticationToken(baseHttpRequestContext, isRefreshPublicKey: false);
            byte[] rsaCertificateBytes = _certificateHelper.GenerateRSACertificateBytes(_prodaSettings.ProdaDeviceName);

            //Save into Azure KV
            await _keyVaultHelper.GetSetCertificateKey(_prodaSettings.KVCertificateName, _prodaSettings.KVTenantId, _prodaSettings.KVClientId, _prodaSettings.KVClientSecret, _prodaSettings.KVCertificateUri, rsaCertificateBytes);

            string publicKey = _certificateHelper.GeneratePublicKey(_certificateHelper.GetRSACertificate(rsaCertificateBytes), _prodaSettings.ProdaDeviceName, _prodaSettings.ProdaDeviceActivationCode, _prodaSettings.ProdaOrganisationRA);
            var jsonKey = JObject.Parse(publicKey);
            string keyToken = Convert.ToString(jsonKey["key"]);

            apiResponse = await RequestProdaService(keyToken, baseHttpRequestContext.OrgId, baseHttpRequestContext.UserId, accessToken: apiResponse.Result);

            return apiResponse;
        }

        /// <summary>
        /// Proda Request for device activation/refresh
        /// </summary>
        /// <param name="publicKey"></param>
        /// <param name="accessToken"></param>
        /// <returns></returns>
        private async Task<ApiResponse<dynamic>> RequestProdaService(string publicKey, int orgId, long userId, string accessToken = "")
        {
            var apiResponse = new ApiResponse<dynamic>();
            RequestLogs requestLog = new RequestLogs();
            try
            {
                requestLog.CorrelationId = System.Guid.NewGuid();
                requestLog.MessageId = System.Guid.NewGuid();
                requestLog.Request = publicKey;
                requestLog.OrgId = orgId;
                requestLog.ModifiedBy = userId;

                Dictionary<string, string> headerKeys = new Dictionary<string, string>()
                {
                    {"dhs-auditIdType", _prodaSettings.ProdaAuditType},
                    {"dhs-subjectId", _prodaSettings.ProdaDeviceName},
                    {"dhs-productId", _prodaSettings.ProdaApplicationName},
                    {"dhs-auditId", _prodaSettings.ProdaOrganisationRA},
                    {"dhs-messageId", $"{"urn:uuid:"}{requestLog.MessageId.ToString()}"},
                    {"dhs-correlationId", $"{"uuid:"}{requestLog.CorrelationId.ToString()}"},
                    {"dhs-subjectIdType", _prodaSettings.ProdaSubjectType }
                };

                string baseUrl = string.Format(_prodaSettings.ProdaDeviceActivationUrl, _prodaSettings.ProdaDeviceName);
                if (!string.IsNullOrWhiteSpace(accessToken))
                {
                    headerKeys.Add("Authorization", $"{"Bearer "}{accessToken}");
                    baseUrl = string.Format(_prodaSettings.ProdaDeviceKeyRefreshUrl, _prodaSettings.ProdaOrganisationRA, _prodaSettings.ProdaDeviceName);
                }

                requestLog.ServiceName = baseUrl;
                requestLog.ID = await _requestLogsBAL.AddRequestLogs(requestLog);

                RestClient restClient = new RestClient(baseUrl, headerKeys);
                var response = await restClient.PutAsync<HttpResponseMessage>(baseUrl, publicKey, "application/proda-json");
                string result = await response.Content.ReadAsStringAsync();
                apiResponse.Result = result;
                apiResponse.StatusCode = (int)response.StatusCode;
                if (response.IsSuccessStatusCode)
                {
                    JObject jObjResult = JObject.Parse(result);
                    DateTime keyExpiry = (DateTime)jObjResult["keyExpiry"];
                    keyExpiry = keyExpiry.ToUniversalTime().AddDays(-2);
                    DateTime deviceExpiry = (DateTime)jObjResult["deviceExpiry"];
                    deviceExpiry = deviceExpiry.ToUniversalTime().AddDays(-10);
                    string deviceName = (string)jObjResult["deviceName"];

                    ProdaConfigs prodaConfig = new ProdaConfigs()
                    {
                        DeviceName = deviceName,
                        DeviceExpiry = (DateTime)deviceExpiry,
                        KeyExpiry = (DateTime)keyExpiry,
                    };

                    await _prodaConfigsBAL.UpsertProdaConfigs(prodaConfig);
                }
                requestLog.Response = result;
                await _requestLogsBAL.UpdateRequestLogs(requestLog);
                return apiResponse;
            }
            catch (Exception ex)
            {
                apiResponse.Errors.Add("Proda Activate service has failed"); 
                requestLog.Response = ex.Message;
                await _requestLogsBAL.UpdateRequestLogs(requestLog);
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                return apiResponse;
            }
        }

        /// <summary>
        /// Get Proda Authentication Token
        /// </summary>
        /// <param name="certificate"></param>
        /// <param name="prodaConfig"></param>
        /// <returns>return proda access token</returns>
        private async Task<ApiResponse<dynamic>> ProdaAuthenticationToken(byte[] certificate, ProdaConfigs prodaConfig, int orgId, long userId)
        {
            var apiResponse = new ApiResponse<dynamic>();
            RequestLogs requestLog = null;
            try
            {
                var prodaAud = _prodaSettings.ProdaAccessTokenAudience;
                var assertionKey = _certificateHelper.GenerateEncryptedToken(certificate, _prodaSettings.ProdaOrganisationRA, _prodaSettings.ProdaDeviceName, prodaAud);

                string authUrl = _prodaSettings.ProdaAuthEndPoint;
                string requestBody = "client_id=" + _prodaSettings.ProdaClientId + "&grant_type=" + _prodaSettings.GrantType + "&assertion=" + assertionKey;

                requestLog = new RequestLogs()
                {
                    CorrelationId = System.Guid.NewGuid(),
                    MessageId = System.Guid.NewGuid(),
                    Request = requestBody,
                    ServiceName = authUrl,
                    OrgId = orgId,
                    ModifiedBy = userId
                };

                requestLog.ID = await _requestLogsBAL.AddRequestLogs(requestLog);

                RestClient restClient = new RestClient(authUrl, new Dictionary<string, string>());
                var response = await restClient.PostAsync<HttpResponseMessage>(authUrl, requestBody, "application/proda-form");
                string content = await response.Content.ReadAsStringAsync();
                apiResponse.Result = content;
                apiResponse.StatusCode = (int)response.StatusCode;
                if (response.IsSuccessStatusCode)
                {
                    JObject jsonResp = JObject.Parse(content);
                    string accessToken = (string)jsonResp["access_token"];
                    int tokenExpirey = (int)jsonResp["expires_in"];

                    prodaConfig.AccessToken = (string)jsonResp["access_token"];
                    prodaConfig.AccessTokenExpiry = DateTime.UtcNow.AddSeconds(tokenExpirey).AddSeconds(-5 * 60);
                    await _prodaConfigsBAL.UpdateProdaConfigs(prodaConfig);
                }

                requestLog.Response = content;
                await _requestLogsBAL.UpdateRequestLogs(requestLog);
                return apiResponse;
            }
            catch (Exception ex)
            {
                apiResponse.Errors.Add("Get Proda Authentication Token Failed"); 
                requestLog.Response = ex.Message;
                await _requestLogsBAL.UpdateRequestLogs(requestLog);
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                return apiResponse;
            }

        }

        /// <summary>
        /// This method used for device expiry updation
        /// </summary>
        /// <param name="prodaConfig"></param>
        /// <returns></returns>
        public async Task<ApiResponse<string>> EditProdaConfig(ProdaConfigsModel prodaConfig)
        {
            var apiResponse = new ApiResponse<string>();
            var dbProdaConfig = await _prodaConfigsBAL.GetProdaConfigs();
            if (dbProdaConfig != null && prodaConfig != null && prodaConfig.DeviceExpiry != null)
            {
                dbProdaConfig.DeviceExpiry = prodaConfig.DeviceExpiry;
                await _prodaConfigsBAL.UpdateProdaConfigs(dbProdaConfig);
                apiResponse.Result = dbProdaConfig.ID.ToString();
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";
                return apiResponse;
            }

            apiResponse.StatusCode = StatusCodes.Status304NotModified;
            apiResponse.Message = "Failed";
            return apiResponse;
        }

        /// <summary>
        /// Get Medicare Authentication Token
        /// </summary>
        /// <param name="baseHttpRequestContext">baseHttpRequestContext</param>
        /// <returns></returns>
        public async Task<ApiResponse<dynamic>> GetMedicareAuthToken(BaseHttpRequestContext baseHttpRequestContext, string minorId)
        {
            var apiResponse = new ApiResponse<dynamic>();
            ProdaConfigs prodaConfig = await _prodaConfigsBAL.GetProdaConfigs();
            if (prodaConfig != null && prodaConfig.KeyExpiry.Value.Date <= DateTime.UtcNow.Date)
                await RefreshPublicKey(baseHttpRequestContext);
            //if (prodaConfig != null && prodaConfig.DeviceExpiry != null)
            //    await SendDeviceExpiryNotification(prodaConfig, baseHttpRequestContext);
            //Get private certificate from Azure KV
            byte[] rsaCertificateBytes = await _keyVaultHelper.GetSetCertificateKey(_prodaSettings.KVCertificateName, _prodaSettings.KVTenantId, _prodaSettings.KVClientId, _prodaSettings.KVClientSecret, _prodaSettings.KVCertificateUri);
            apiResponse = await RequestMedicareAuthenticationToken(rsaCertificateBytes, prodaConfig, baseHttpRequestContext.OrgId, baseHttpRequestContext.UserId, minorId);
            return apiResponse;
        }

        /// <summary>
        /// Request for Medicare token
        /// </summary>
        /// <param name="certificate">certificate</param>
        /// <param name="prodaConfig">prodaConfig</param>
        /// <param name="orgId">orgId</param>
        /// <param name="userId">userId</param>
        /// <returns>return medicare access token</returns>
        private async Task<ApiResponse<dynamic>> RequestMedicareAuthenticationToken(byte[] certificate, ProdaConfigs prodaConfig, int orgId, long userId, string minorId)
        {
            var apiResponse = new ApiResponse<dynamic>();
            RequestLogs requestLog = null;
            try
            {
                var prodaAudience = _prodaSettings.ProdaAccessTokenAudience; var medicareAudience = _prodaSettings.MedicareTokenAudience;
                var assertionKey = _certificateHelper.GenerateEncryptedToken(certificate, _prodaSettings.ProdaOrganisationRA, _prodaSettings.ProdaDeviceName, prodaAudience, medicareAudience);
                string authUrl = _prodaSettings.ProdaAuthEndPoint;
                string requestBody = "client_id=" + _prodaSettings.ProdaClientId + "&grant_type=" + _prodaSettings.GrantType + "&assertion=" + assertionKey;
                requestLog = new RequestLogs()
                {
                    CorrelationId = System.Guid.NewGuid(),
                    MessageId = System.Guid.NewGuid(),
                    Request = requestBody,
                    ServiceName = authUrl,
                    OrgId = orgId,
                    ModifiedBy = userId
                };
                requestLog.ID = await _requestLogsBAL.AddRequestLogs(requestLog);
                RestClient restClient = new RestClient(authUrl, new Dictionary<string, string>());
                var response = await restClient.PostAsync<HttpResponseMessage>(authUrl, requestBody, "application/proda-form");
                string content = await response.Content.ReadAsStringAsync();
                apiResponse.Result = content;
                apiResponse.StatusCode = (int)response.StatusCode;
                if (response.IsSuccessStatusCode)
                {
                    JObject jsonResp = JObject.Parse(content);
                    string accessToken = (string)jsonResp["access_token"];
                    int tokenExpirey = (int)jsonResp["expires_in"];
                    SubscriberDetails subscriberDetails = new SubscriberDetails();
                    subscriberDetails.OrgId = orgId;
                    subscriberDetails.MinorId = minorId;
                    subscriberDetails.AccessToken = accessToken;
                    subscriberDetails.AccessTokenExpiry = DateTime.UtcNow.AddSeconds(tokenExpirey).AddSeconds(-5 * 60);
                    await _subscriberDetailsBAL.UpsertSubscriberDetails(subscriberDetails, userId);
                }
                requestLog.Response = content;
                await _requestLogsBAL.UpdateRequestLogs(requestLog);
                return apiResponse;
            }
            catch (Exception ex)
            {
                apiResponse.Errors.Add("Get Medicare Authentication Token Failed"); requestLog.Response = ex.Message;
                await _requestLogsBAL.UpdateRequestLogs(requestLog);
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                return apiResponse;
            }
        }

        /// <summary>
        /// Send notification for device expiry
        /// </summary>
        /// <param name="prodaConfig"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        private async Task SendDeviceExpiryNotification(ProdaConfigs prodaConfig, BaseHttpRequestContext baseHttpRequestContext)
        {
            TimeSpan timeSpan = (prodaConfig.DeviceExpiry.Value.Date - DateTime.UtcNow.Date);
          
            if (timeSpan.Days <= Convert.ToInt32(_prodaSettings.NotificationBeforeInDays))
            {
                _logger.LogInformation($"SendDeviceExpiryNotification before email");

                var subject = "Proda Device " + prodaConfig.DeviceName + " to be Expired .";
                string htmlContent = "<br><br><table border=\"2\"><tr><th>Device Name</th><th>Expiry Date</th></tr><td>" + prodaConfig.DeviceName + "</td><td>" + prodaConfig.DeviceExpiry.Value.ToShortDateString() + "</td></tr></table>";
                htmlContent = htmlContent + "<p><p>Please login to the PRODA Account and Extend B2B device.</p></p>";
                var plainTextContent = "Device Name " + prodaConfig.DeviceName + "will be expired on" + prodaConfig.DeviceExpiry.Value.ToString("dd/MM/yyyy") + "\n" + "Kindly go to your PRODA Account and Extend B2B device.";

                EmailRequest emailRequest = new EmailRequest();
                emailRequest.ToEmailAddress = _prodaSettings.ProdaNotificationMailTo;
                emailRequest.Subject = subject;
                emailRequest.HtmlContent = htmlContent;
                emailRequest.PlainTextContent = plainTextContent;

                string emailAPiUrl = _appSettings.ApiUrls["CommunicationServiceUrl"] + "/Email/SendEmail";
                RestClient restClient = new RestClient(emailAPiUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
                await restClient.PostAsync<ApiResponse<string>>(emailAPiUrl, emailRequest);
            }
        }

    }
}
