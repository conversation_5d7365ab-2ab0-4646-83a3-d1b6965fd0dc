﻿using Capstone2.RestServices.Proda.Interfaces;
using Capstone2.Shared.Models.Proda;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Proda.Services
{
    public class ProdaConfigsBAL : IProdaConfigsBAL
    {
        private readonly IProdaConfigsDAL _prodaConfigsDAL;
        public ProdaConfigsBAL(IProdaConfigsDAL prodaConfigsDAL)
        {
            _prodaConfigsDAL = prodaConfigsDAL;
        }

        public async Task<int> UpsertProdaConfigs(ProdaConfigs prodaConfigs)
        {
            return await _prodaConfigsDAL.UpsertProdaConfigs(prodaConfigs);
        }

        public async Task<int> UpdateProdaConfigs(ProdaConfigs prodaConfigs)
        {
            return await _prodaConfigsDAL.UpdateProdaConfigs(prodaConfigs);
        }

        public async Task<ProdaConfigs> GetProdaConfigs()
        {
            return await _prodaConfigsDAL.GetProdaConfigs();
        }

        public async Task<int> SaveCertificateThumbPrint(ProdaConfigs prodaConfigs)
        {
            return await _prodaConfigsDAL.SaveCertificateThumbPrint(prodaConfigs);
        }
    }
}
