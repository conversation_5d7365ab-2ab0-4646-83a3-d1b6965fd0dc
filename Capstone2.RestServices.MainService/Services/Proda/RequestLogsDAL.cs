﻿using Capstone2.Framework.RestApi.Context;
using Capstone2.RestServices.Proda.Interfaces;
using Capstone2.Shared.Models.Proda;
using Microsoft.EntityFrameworkCore;
using System;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Proda.Services
{
    public class RequestLogsDAL : IRequestLogsDAL
    {
        public readonly ReadOnlyDBMasterContext _readOnlyDbContext;
        public readonly UpdatableDBMasterContext _updatableDBContext;
        public RequestLogsDAL(ReadOnlyDBMasterContext readOnlyDbContext, UpdatableDBMasterContext updatableDBContext)
        {
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
        }

        public async Task<Guid> AddRequestLogs(RequestLogs requestLog)
        {
            try
            {
                requestLog.CreatedDate = DateTime.UtcNow;
                await _updatableDBContext.RequestLogs.AddAsync(requestLog);
                await _updatableDBContext.SaveChangesAsync();
                return requestLog.ID;
            }
            catch (Exception e)
            {
                requestLog.CreatedDate = DateTime.UtcNow;
                await _updatableDBContext.RequestLogs.AddAsync(requestLog);
                await _updatableDBContext.SaveChangesAsync();
                return requestLog.ID;
            }
        }

        public async Task<Guid> UpdateRequestLogs(RequestLogs requestLog)
        {
            requestLog.ModifiedDate = DateTime.UtcNow;
            _updatableDBContext.RequestLogs.Update(requestLog);
            await _updatableDBContext.SaveChangesAsync();
            return requestLog.ID;
        }

        public async Task<RequestLogs> GetRequestLogs(Guid id)
        {
            return await _readOnlyDbContext.RequestLogs.FirstOrDefaultAsync(x => x.ID == id);
        }

    }
}
