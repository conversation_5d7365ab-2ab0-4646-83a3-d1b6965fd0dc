﻿using Capstone2.Framework.RestApi.Context;
using Capstone2.RestServices.Master.Context;
using Capstone2.RestServices.Proda.Interfaces;
using Capstone2.Shared.Models.Proda;
using Microsoft.EntityFrameworkCore;
using System;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Proda.Services
{
    public class ProdaConfigsDAL : IProdaConfigsDAL
    {
        public readonly ReadOnlyDBMasterContext _readOnlyDbContext;
        public readonly UpdatableDBMasterContext _updatableDBContext;
        public ProdaConfigsDAL(ReadOnlyDBMasterContext readOnlyDbContext, UpdatableDBMasterContext updatableDBContext)
        {
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
        }

        public async Task<int> UpsertProdaConfigs(ProdaConfigs prodaConfigs)
        {
            var config = await _readOnlyDbContext.ProdaConfigs.FirstOrDefaultAsync(x => x.Status == true);
            if (config != null)
            {
                config.DeviceName = prodaConfigs.DeviceName;
                config.DeviceExpiry = prodaConfigs.DeviceExpiry;
                config.KeyExpiry = prodaConfigs.KeyExpiry;
                config.AccessToken = prodaConfigs.AccessToken != null ? prodaConfigs.AccessToken : config.AccessToken;
                config.AccessTokenExpiry = (prodaConfigs.AccessTokenExpiry != null && prodaConfigs.AccessTokenExpiry != DateTime.MinValue) ? prodaConfigs.AccessTokenExpiry : config.AccessTokenExpiry;
                config.Status = true;
                config.ModifiedDate = DateTime.UtcNow;
                _updatableDBContext.ProdaConfigs.Update(config);
            }
            else
            {
                prodaConfigs.Status = true;
                prodaConfigs.CreatedDate = DateTime.UtcNow;
                await _updatableDBContext.ProdaConfigs.AddAsync(prodaConfigs);
            }
            await _updatableDBContext.SaveChangesAsync();
            return prodaConfigs.ID;
        }

        public async Task<int> UpdateProdaConfigs(ProdaConfigs prodaConfigs)
        {
            _updatableDBContext.ProdaConfigs.Update(prodaConfigs);
            await _updatableDBContext.SaveChangesAsync();
            return prodaConfigs.ID;
        }

        public async Task<ProdaConfigs> GetProdaConfigs()
        {
            return await _readOnlyDbContext.ProdaConfigs.FirstOrDefaultAsync(x => x.Status == true);
        }

        public async Task<int> SaveCertificateThumbPrint(ProdaConfigs prodaConfigs)
        {
            var config = await _readOnlyDbContext.ProdaConfigs.FirstOrDefaultAsync(x => x.DeviceName == prodaConfigs.DeviceName && x.Status == true);
            if (config != null)
            {
                config.CertificateThumbPrint = prodaConfigs.CertificateThumbPrint; //TODO
                config.ModifiedDate = DateTime.UtcNow;
                _updatableDBContext.ProdaConfigs.Update(config);
                await _updatableDBContext.SaveChangesAsync();

                return config.ID;
            }

            return 0;
        }

    }
}
