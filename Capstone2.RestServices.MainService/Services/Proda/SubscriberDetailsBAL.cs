﻿using Capstone2.RestServices.Proda.Interfaces;
using Capstone2.Shared.Models.Medicare;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Proda.Services
{
    public class SubscriberDetailsBAL: ISubscriberDetailsBAL
    {
        private readonly ISubscriberDetailsDAL _subscriberDetailsDAL;
        public SubscriberDetailsBAL(ISubscriberDetailsDAL subscriberDetailsDAL)
        {
            _subscriberDetailsDAL = subscriberDetailsDAL;
        }
        public async Task<SubscriberDetails> GetSubscriberDetails(int orgId)
        {
            return await _subscriberDetailsDAL.GetSubscriberDetails(orgId);
        }

        public async Task UpdateSubscriberDetails(SubscriberDetails subscriberDetails)
        {
            await _subscriberDetailsDAL.UpdateSubscriberDetails(subscriberDetails);
        }
        public async Task UpsertSubscriberDetails(SubscriberDetails subscriberDetails, long userId)
        {
            await _subscriberDetailsDAL.UpsertSubscriberDetails(subscriberDetails, userId);            
        }
    }
}
