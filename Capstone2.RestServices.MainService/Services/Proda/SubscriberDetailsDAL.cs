﻿using Capstone2.Framework.RestApi.Context;
using Capstone2.RestServices.Master.Context;
using Capstone2.RestServices.Proda.Context;
using Capstone2.RestServices.Proda.Interfaces;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Medicare;
using Microsoft.EntityFrameworkCore;
using System;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Proda.Services
{
    public class SubscriberDetailsDAL : ISubscriberDetailsDAL
    {
        public readonly ReadOnlyProdaDBContext _readOnlyDbContext;
        public readonly ReadOnlyDBMasterContext _readOnlyDbMasterContext;
        public readonly UpdatableDBMasterContext _updatableDBMasterContext;
        public SubscriberDetailsDAL(ReadOnlyDBMasterContext readOnlyDbMasterContext, UpdatableDBMasterContext updatableDBMasterContext, ReadOnlyProdaDBContext readOnlyDbContext)
        {
            _readOnlyDbMasterContext = readOnlyDbMasterContext;
            _updatableDBMasterContext = updatableDBMasterContext;
            _readOnlyDbContext = readOnlyDbContext;
        }

        public async Task<SubscriberDetails> GetSubscriberDetails(int orgId)
        {
            return await _readOnlyDbMasterContext.SubscriberDetails.FirstOrDefaultAsync(x => x.OrgId == orgId);
        }

        public async Task UpdateSubscriberDetails(SubscriberDetails subscriberDetails)
        {
            var config = await _readOnlyDbMasterContext.SubscriberDetails.FirstOrDefaultAsync(x => x.OrgId == subscriberDetails.OrgId);
            if (config != null)
            {
                config.AccessToken = subscriberDetails.AccessToken != null ? subscriberDetails.AccessToken : config.AccessToken;
                config.AccessTokenExpiry = (subscriberDetails.AccessTokenExpiry != null && subscriberDetails.AccessTokenExpiry != DateTime.MinValue) ? subscriberDetails.AccessTokenExpiry : config.AccessTokenExpiry;
                config.ModifiedDate = DateTime.UtcNow;
                _updatableDBMasterContext.SubscriberDetails.Update(config);
                await _updatableDBMasterContext.SaveChangesAsync();
            }
        }

        public async Task UpsertSubscriberDetails(SubscriberDetails subscriberDetails, long userId)
        {
            var userCompanyAssoc = await _readOnlyDbContext.UserCompanyAssocs.FirstOrDefaultAsync(x => x.OrgId == subscriberDetails.OrgId && x.UserDetailsId == userId && x.CompanyTypeId == (short)CompanyType.InternalCompany && x.StatusId.Value == (short)Status.Active && x.IsDefault == true);
            if (userCompanyAssoc != null)
            {
                var dbSubscriber = await _readOnlyDbMasterContext.SubscriberDetails.FirstOrDefaultAsync(x => x.OrgId == subscriberDetails.OrgId && x.MinorId == subscriberDetails.MinorId);
                if (dbSubscriber != null)
                {
                    dbSubscriber.AccessToken = subscriberDetails.AccessToken != null ? subscriberDetails.AccessToken : dbSubscriber.AccessToken;
                    dbSubscriber.AccessTokenExpiry = (subscriberDetails.AccessTokenExpiry != null && subscriberDetails.AccessTokenExpiry != DateTime.MinValue) ? subscriberDetails.AccessTokenExpiry : dbSubscriber.AccessTokenExpiry;
                    dbSubscriber.ModifiedDate = DateTime.UtcNow;
                    _updatableDBMasterContext.SubscriberDetails.Update(dbSubscriber);
                }
                else
                {
                    subscriberDetails.CompanyID = userCompanyAssoc.CompanyId;
                    subscriberDetails.CreatedDate = DateTime.UtcNow; subscriberDetails.ModifiedDate = DateTime.UtcNow;
                    _updatableDBMasterContext.SubscriberDetails.Add(subscriberDetails);
                }
                await _updatableDBMasterContext.SaveChangesAsync();
            }
        }

    }
}
