﻿using AutoMapper;
using Capstone2.Framework.Business.Common;
using Capstone2.Framework.RestApi;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.Framework.RestApi.Utility;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.Hcp.Interfaces;
using Capstone2.RestServices.Hcp.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace Capstone2.RestServices.Hcp.Services
{
    public class HcpBAL : IHcpBAL
    {
        public readonly IHcpDAL _hcpDAL;
        public IMapper _mapper;
        public readonly AppSettings _appSettings;
        private readonly ILogger<HcpBAL> _logger;
        private IASBMessageSenderHelper _asbMessageSenderHelper;
        private readonly IConfiguration _configuration;
        public HcpBAL(IHcpDAL hcpDAL, IMapper mapper, IOptions<AppSettings> appSettings, ILogger<HcpBAL> logger, IConfiguration configuration, IASBMessageSenderHelper asbMessageSenderHelper)
        {
            _hcpDAL = hcpDAL;
            _mapper = mapper;
            _appSettings = appSettings.Value;
            _logger = logger;
            this._asbMessageSenderHelper = asbMessageSenderHelper;
            this._configuration = configuration;
        }
        public async Task<ApiResponse<Hcpsummary>> EditHCPSummary(long id,Hcpsummary appointmentHcpsummaryInput, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<Hcpsummary> apiResponse = new();
            int orgId = baseHttpRequestContext.OrgId;
            long loggedInUserId = baseHttpRequestContext.UserId;
            if (appointmentHcpsummaryInput is not null)
            {
                Hcpsummary appointmentHcpsummaryDB = await _hcpDAL.GetAppointmentHCPSummaryFromId(id, orgId);
                appointmentHcpsummaryInput.OrgId = baseHttpRequestContext.OrgId;
                if (!string.IsNullOrWhiteSpace(appointmentHcpsummaryInput.InsurerMembershipIdentifier))
                {
                    appointmentHcpsummaryInput.InsurerMembershipIdentifier = (appointmentHcpsummaryInput.InsurerMembershipIdentifier.Trim().Length > 15) ? appointmentHcpsummaryInput.InsurerMembershipIdentifier.Trim().Substring(0, 15) : appointmentHcpsummaryInput.InsurerMembershipIdentifier.Trim();
                }

                if (appointmentHcpsummaryDB is not null)
                {
                    appointmentHcpsummaryInput.Id = appointmentHcpsummaryDB.Id;
                    appointmentHcpsummaryInput.CreatedBy = appointmentHcpsummaryDB.CreatedBy;
                    appointmentHcpsummaryInput.CreatedDate = appointmentHcpsummaryDB.CreatedDate;
                    appointmentHcpsummaryInput.EpisodeIdentifierId = appointmentHcpsummaryDB.EpisodeIdentifierId;
                    appointmentHcpsummaryInput.AppointmentDetailsId = appointmentHcpsummaryDB.AppointmentDetailsId;
                    appointmentHcpsummaryInput.PatientDetailsId = appointmentHcpsummaryDB.PatientDetailsId;

                    if (string.IsNullOrWhiteSpace(appointmentHcpsummaryInput.PatientSurName))
                        appointmentHcpsummaryInput.PatientSurName = appointmentHcpsummaryDB.PatientSurName;
                    if (string.IsNullOrWhiteSpace(appointmentHcpsummaryInput.PatientFirstName))
                        appointmentHcpsummaryInput.PatientFirstName = appointmentHcpsummaryDB.PatientFirstName;
                    if (appointmentHcpsummaryInput.PatientDOB is null)
                        appointmentHcpsummaryInput.PatientDOB = appointmentHcpsummaryDB.PatientDOB;
                    if (appointmentHcpsummaryInput.PatientRecordID is null || appointmentHcpsummaryInput.PatientRecordID <=default(long))
                        appointmentHcpsummaryInput.PatientRecordID = appointmentHcpsummaryDB.PatientRecordID;

                    if (appointmentHcpsummaryInput.FileDetailsId is null || appointmentHcpsummaryInput.FileDetailsId <= default(long))
                        appointmentHcpsummaryInput.IsConsentDetailsCompleted = false;
                    else
                        appointmentHcpsummaryInput.IsConsentDetailsCompleted = true;

                    appointmentHcpsummaryInput.AdmissionDate = (appointmentHcpsummaryInput.AdmissionDate is null && appointmentHcpsummaryDB.AdmissionDate is not null) ? appointmentHcpsummaryDB.AdmissionDate : appointmentHcpsummaryInput.AdmissionDate;

                }
                appointmentHcpsummaryInput.ModifiedDate = DateTime.UtcNow;
                appointmentHcpsummaryInput.ModifiedBy = loggedInUserId;
                appointmentHcpsummaryInput.StatusId = (short)Status.Active;
                
                appointmentHcpsummaryInput.HcpotherServicesAssocs = EditHcpotherServicesAssocs(appointmentHcpsummaryDB.HcpotherServicesAssocs, appointmentHcpsummaryInput.HcpotherServicesAssocs, baseHttpRequestContext);
                appointmentHcpsummaryInput.HcptheatreAndMbsdetailsAssocs = EditHcptheatreAndMbsdetailsAssocs(appointmentHcpsummaryDB.HcptheatreAndMbsdetailsAssocs, appointmentHcpsummaryInput.HcptheatreAndMbsdetailsAssocs, baseHttpRequestContext);
                if (appointmentHcpsummaryInput.HcptheatreAndMbsdetailsAssocs is not null && appointmentHcpsummaryInput.HcptheatreAndMbsdetailsAssocs.Count > 0)
                {
                    appointmentHcpsummaryInput.IsTheatreDetailsCompleted = (appointmentHcpsummaryInput.HcptheatreAndMbsdetailsAssocs.Where(x => x.StatusId == (short)Status.Active).Count() > 0) ? true : false;
                }
                appointmentHcpsummaryInput.HcpproceduresAssocs = EditHcpproceduresAssocs(appointmentHcpsummaryDB.HcpproceduresAssocs, appointmentHcpsummaryInput.HcpproceduresAssocs, baseHttpRequestContext);
                //if (appointmentHcpsummaryInput.HcpproceduresAssocs is not null && appointmentHcpsummaryInput.HcpproceduresAssocs.Count > 0)
                //{
                //    int countActive  = appointmentHcpsummaryInput.HcpproceduresAssocs.Where(x => x.StatusId == (short)Status.Active ).Count();
                //    if(countActive > 0)
                //    {
                //        int principalCount = appointmentHcpsummaryInput.HcpproceduresAssocs.Where(x => x.StatusId == (short)Status.Active && x.PrinipalProcedure == true).Count();
                //        if(principalCount==0)
                //            apiResponse.Errors.Add("One Procedure Code should be selected as Principal Procedure Code.");

                //    }
                //}
                if (appointmentHcpsummaryInput.HcphospitalAccommodationAssocs is not null && appointmentHcpsummaryInput.HcphospitalAccommodationAssocs.Count > 0)
                {
                    appointmentHcpsummaryInput.DischargeDate = appointmentHcpsummaryInput.HcphospitalAccommodationAssocs.Where(x => x.SeparationDate.HasValue).Max(x => x.SeparationDate.Value);
                    appointmentHcpsummaryInput.AdmissionDate = appointmentHcpsummaryInput.HcphospitalAccommodationAssocs.Where(x => x.AdmissionDate.HasValue).Min(x => x.AdmissionDate.Value);

                }
                appointmentHcpsummaryInput.HcphospitalAccommodationAssocs = EditHcphospitalAccommodationAssocs(appointmentHcpsummaryDB.HcphospitalAccommodationAssocs, appointmentHcpsummaryInput.HcphospitalAccommodationAssocs, baseHttpRequestContext);
                
                appointmentHcpsummaryInput.HcpTheatreTimeAssocs = EditHcpTheatreTimeAssocs(appointmentHcpsummaryDB.HcpTheatreTimeAssocs, appointmentHcpsummaryInput.HcpTheatreTimeAssocs, baseHttpRequestContext);
                if (appointmentHcpsummaryInput.HcpTheatreTimeAssocs is not null && appointmentHcpsummaryInput.HcpTheatreTimeAssocs.Count > 0)
                {
                    if (appointmentHcpsummaryInput.AdmissionDate != null && appointmentHcpsummaryInput.DischargeDate != null)
                        appointmentHcpsummaryInput.IsDateTimeDetailsCompleted = (appointmentHcpsummaryInput.HcpTheatreTimeAssocs.Where(x => x.StatusId == (short)Status.Active).Count() > 0) ? true : false;
                    else
                        appointmentHcpsummaryInput.IsDateTimeDetailsCompleted = false;
                }
                

                appointmentHcpsummaryInput.HcpdocumentAssocs = EditHcpdocumentAssocs(appointmentHcpsummaryDB.HcpdocumentAssocs, appointmentHcpsummaryInput.HcpdocumentAssocs, baseHttpRequestContext);

                if (appointmentHcpsummaryInput.HcpsameDayAccommodationDetails is not null)
                {
                    appointmentHcpsummaryInput.HcpsameDayAccommodationDetails.ModifiedBy = loggedInUserId;
                    appointmentHcpsummaryInput.HcpsameDayAccommodationDetails.ModifiedDate = DateTime.UtcNow;
                    appointmentHcpsummaryInput.HcpsameDayAccommodationDetails.StatusId = (short)Status.Active;
                    if (appointmentHcpsummaryDB.HcpsameDayAccommodationDetails is not null)
                    {
                        appointmentHcpsummaryInput.HcpsameDayAccommodationDetails.Id = appointmentHcpsummaryDB.HcpsameDayAccommodationDetails.Id;
                        appointmentHcpsummaryInput.HcpsameDayAccommodationDetails.CreatedBy = appointmentHcpsummaryDB.HcpsameDayAccommodationDetails.CreatedBy;
                        appointmentHcpsummaryInput.HcpsameDayAccommodationDetails.CreatedDate = appointmentHcpsummaryDB.HcpsameDayAccommodationDetails.CreatedDate;
                        appointmentHcpsummaryInput.HcpsameDayAccommodationDetails.OrgId = appointmentHcpsummaryDB.HcpsameDayAccommodationDetails.OrgId;
                        appointmentHcpsummaryInput.HcpsameDayAccommodationDetails.HcpsummaryId = appointmentHcpsummaryDB.HcpsameDayAccommodationDetails.HcpsummaryId;
                    }
                    else
                    {
                        appointmentHcpsummaryInput.HcpsameDayAccommodationDetails.CreatedBy = loggedInUserId;
                        appointmentHcpsummaryInput.HcpsameDayAccommodationDetails.CreatedDate = DateTime.UtcNow;
                        appointmentHcpsummaryInput.HcpsameDayAccommodationDetails.OrgId = orgId;
                        appointmentHcpsummaryInput.HcpsameDayAccommodationDetails.HcpsummaryId = id;
                    }
                    
                }
                if (appointmentHcpsummaryInput.HcpcertificateDetails is not null)
                {
                    appointmentHcpsummaryInput.HcpcertificateDetails.StatusId =  (short)Status.Active ;
                    if (appointmentHcpsummaryDB.HcpcertificateDetails is not null)
                    {
                        appointmentHcpsummaryInput.HcpcertificateDetails.Id = appointmentHcpsummaryDB.HcpcertificateDetails.Id;
                        appointmentHcpsummaryInput.HcpcertificateDetails.CreatedBy = appointmentHcpsummaryDB.HcpcertificateDetails.CreatedBy;
                        appointmentHcpsummaryInput.HcpcertificateDetails.CreatedDate = appointmentHcpsummaryDB.HcpcertificateDetails.CreatedDate;
                        appointmentHcpsummaryInput.HcpcertificateDetails.OrgId = appointmentHcpsummaryDB.HcpcertificateDetails.OrgId;
                        appointmentHcpsummaryInput.HcpcertificateDetails.HcpsummaryId = appointmentHcpsummaryDB.HcpcertificateDetails.HcpsummaryId;
                    }
                    else
                    {
                        appointmentHcpsummaryInput.HcpcertificateDetails.CreatedBy = loggedInUserId;
                        appointmentHcpsummaryInput.HcpcertificateDetails.CreatedDate = DateTime.UtcNow;
                        appointmentHcpsummaryInput.HcpcertificateDetails.OrgId = orgId;
                        appointmentHcpsummaryInput.HcpcertificateDetails.HcpsummaryId = id;
                    }

                    appointmentHcpsummaryInput.IsCertificateDetailsCompleted = (appointmentHcpsummaryInput.HcpcertificateDetails.IsCertificateUploaded == null) ? false : (bool)appointmentHcpsummaryInput.HcpcertificateDetails.IsCertificateUploaded;
                }
                else
                {
                    appointmentHcpsummaryInput.IsCertificateDetailsCompleted = true;
                    if (appointmentHcpsummaryDB.HcpcertificateDetails is not null)
                    {
                        appointmentHcpsummaryDB.HcpcertificateDetails.StatusId = (short)Status.Deleted;
                        appointmentHcpsummaryInput.HcpcertificateDetails = appointmentHcpsummaryDB.HcpcertificateDetails;
                    }
                }
                if (appointmentHcpsummaryInput.HcpotherDetails is not null)
                {
                    appointmentHcpsummaryInput.HcpotherDetails.ModifiedBy = loggedInUserId;
                    appointmentHcpsummaryInput.HcpotherDetails.ModifiedDate = DateTime.UtcNow;
                    appointmentHcpsummaryInput.HcpotherDetails.StatusId = (short)Status.Active;
                    appointmentHcpsummaryInput.HcpotherDetails.UnplannedTheatreVisit = (appointmentHcpsummaryInput.HcpotherDetails.UnplannedTheatreVisit is null) ? false : appointmentHcpsummaryInput.HcpotherDetails.UnplannedTheatreVisit;

                    appointmentHcpsummaryInput.HcpotherDetails.BirthWeight = string.IsNullOrWhiteSpace(appointmentHcpsummaryInput.HcpotherDetails.BirthWeight) ? "0" : appointmentHcpsummaryInput.HcpotherDetails.BirthWeight;
                    appointmentHcpsummaryInput.HcpotherDetails.HospitalTransferredFromProviderNo = (appointmentHcpsummaryInput.HcpotherDetails.HospitalTransferredFromProviderNo is null) ? string.Empty : appointmentHcpsummaryInput.HcpotherDetails.HospitalTransferredFromProviderNo;
                    appointmentHcpsummaryInput.HcpotherDetails.HospitalTransferredToProviderNo = (appointmentHcpsummaryInput.HcpotherDetails.HospitalTransferredToProviderNo is null) ? string.Empty : appointmentHcpsummaryInput.HcpotherDetails.HospitalTransferredToProviderNo;
                    appointmentHcpsummaryInput.HcpotherDetails.TotalLeaveDays = string.IsNullOrWhiteSpace(appointmentHcpsummaryInput.HcpotherDetails.TotalLeaveDays) ? "0" : appointmentHcpsummaryInput.HcpotherDetails.TotalLeaveDays;
                    appointmentHcpsummaryInput.HcpotherDetails.Icuhours = string.IsNullOrWhiteSpace(appointmentHcpsummaryInput.HcpotherDetails.Icuhours) ? "0" : appointmentHcpsummaryInput.HcpotherDetails.Icuhours;
                    appointmentHcpsummaryInput.HcpotherDetails.PsychiatricCareDays = string.IsNullOrWhiteSpace(appointmentHcpsummaryInput.HcpotherDetails.PsychiatricCareDays) ? "0" : appointmentHcpsummaryInput.HcpotherDetails.PsychiatricCareDays;
                    appointmentHcpsummaryInput.HcpotherDetails.Mvhours = string.IsNullOrWhiteSpace(appointmentHcpsummaryInput.HcpotherDetails.Mvhours) ? "0" : appointmentHcpsummaryInput.HcpotherDetails.Mvhours;
                    appointmentHcpsummaryInput.HcpotherDetails.AgeInDays = string.IsNullOrWhiteSpace(appointmentHcpsummaryInput.HcpotherDetails.AgeInDays) ? "0" : appointmentHcpsummaryInput.HcpotherDetails.AgeInDays;

                    if (appointmentHcpsummaryDB.HcpotherDetails is not null)
                    {
                        appointmentHcpsummaryInput.HcpotherDetails.Id = appointmentHcpsummaryDB.HcpotherDetails.Id;
                        appointmentHcpsummaryInput.HcpotherDetails.CreatedBy = appointmentHcpsummaryDB.HcpotherDetails.CreatedBy;
                        appointmentHcpsummaryInput.HcpotherDetails.CreatedDate = appointmentHcpsummaryDB.HcpotherDetails.CreatedDate;
                        appointmentHcpsummaryInput.HcpotherDetails.OrgId = appointmentHcpsummaryDB.HcpotherDetails.OrgId;
                        appointmentHcpsummaryInput.HcpotherDetails.HcpsummaryId = appointmentHcpsummaryDB.HcpotherDetails.HcpsummaryId;
                    }
                    else
                    {
                        appointmentHcpsummaryInput.HcpotherDetails.CreatedBy = loggedInUserId;
                        appointmentHcpsummaryInput.HcpotherDetails.CreatedDate = DateTime.UtcNow;
                        appointmentHcpsummaryInput.HcpotherDetails.OrgId = orgId;
                        appointmentHcpsummaryInput.HcpotherDetails.HcpsummaryId = id;
                    }

                }
                if (appointmentHcpsummaryInput.HcpdiagnosesDetails is not null)
                {
                    appointmentHcpsummaryInput.HcpdiagnosesDetails.ModifiedBy = loggedInUserId;
                    appointmentHcpsummaryInput.HcpdiagnosesDetails.ModifiedDate = DateTime.UtcNow;
                    if (appointmentHcpsummaryDB.HcpdiagnosesDetails is not null)
                    {
                        appointmentHcpsummaryInput.HcpdiagnosesDetails.Id = appointmentHcpsummaryDB.HcpdiagnosesDetails.Id;
                        appointmentHcpsummaryInput.HcpdiagnosesDetails.CreatedBy = appointmentHcpsummaryDB.HcpdiagnosesDetails.CreatedBy;
                        appointmentHcpsummaryInput.HcpdiagnosesDetails.CreatedDate = appointmentHcpsummaryDB.HcpdiagnosesDetails.CreatedDate;
                        appointmentHcpsummaryInput.HcpdiagnosesDetails.OrgId = appointmentHcpsummaryDB.HcpdiagnosesDetails.OrgId;
                        appointmentHcpsummaryInput.HcpdiagnosesDetails.HcpsummaryId = appointmentHcpsummaryDB.HcpdiagnosesDetails.HcpsummaryId;

                    }
                    else
                    {
                        appointmentHcpsummaryInput.HcpdiagnosesDetails.CreatedBy = loggedInUserId;
                        appointmentHcpsummaryInput.HcpdiagnosesDetails.CreatedDate = DateTime.UtcNow;
                        appointmentHcpsummaryInput.HcpdiagnosesDetails.OrgId = orgId;
                        appointmentHcpsummaryInput.HcpdiagnosesDetails.HcpsummaryId = id;
                    }
                    if (appointmentHcpsummaryInput.HcpdiagnosesDetails.HcpdiagnosesAssocs is not null)
                    {
                        appointmentHcpsummaryInput.HcpdiagnosesDetails.HcpdiagnosesAssocs = EditHcpdiagnosesAssocs(appointmentHcpsummaryDB?.HcpdiagnosesDetails?.HcpdiagnosesAssocs, appointmentHcpsummaryInput.HcpdiagnosesDetails.HcpdiagnosesAssocs, baseHttpRequestContext);


                        if (appointmentHcpsummaryInput.HcpdiagnosesDetails.HcpdiagnosesAssocs is not null && appointmentHcpsummaryInput.HcpdiagnosesDetails.HcpdiagnosesAssocs.Count > 0)
                        {
                            appointmentHcpsummaryInput.IsClinicalCodingDetailsCompleted = (appointmentHcpsummaryInput.HcpdiagnosesDetails.HcpdiagnosesAssocs.Where(x => x.StatusId == (short)Status.Active && x.PrinipalDiagnosis==true).Count() ==1) ? true : false;
                            if (string.IsNullOrWhiteSpace(appointmentHcpsummaryInput.HcpdiagnosesDetails.Drg) || string.IsNullOrWhiteSpace(appointmentHcpsummaryInput.HcpdiagnosesDetails.Ardrgversion)) appointmentHcpsummaryInput.IsClinicalCodingDetailsCompleted = false;
                            //if (!appointmentHcpsummaryInput.IsClinicalCodingDetailsCompleted)
                            //{
                            //    apiResponse.Errors.Add("One Diagnoses Code should be selected as Principal Diagnoses Code.");                                
                            //}
                        }
                    }

                }
                if(apiResponse.Errors is null || apiResponse.Errors.Count == 0)
                {
                    int rows = await _hcpDAL.UpdateAppointmentHCPSummaryAsync(appointmentHcpsummaryInput);
                    if (rows > 0)
                    {
                        apiResponse.StatusCode = StatusCodes.Status200OK;
                        apiResponse.Result = appointmentHcpsummaryInput;
                        apiResponse.Message = "Success";
                        return apiResponse;
                    }
                }
                    

            }
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            apiResponse.Result = null;
            apiResponse.Message = "Failure";
            apiResponse.Errors.Add("Error while updating Appointment HCP Summary Details.");
            return apiResponse;
        }

        private ICollection<HcpTheatreTimeAssoc> EditHcpTheatreTimeAssocs(ICollection<HcpTheatreTimeAssoc> hcpTheatreTimeAssocsDB, ICollection<HcpTheatreTimeAssoc> hcpTheatreTimeAssocsInput, BaseHttpRequestContext baseHttpRequestContext)
        {
            List<HcpTheatreTimeAssoc> lstAddUpdItem = new();
            foreach (var itemInput in hcpTheatreTimeAssocsInput)
            {
                if (itemInput.Id > 0)
                {
                    var existingobj = hcpTheatreTimeAssocsDB.FirstOrDefault(x => x.Id == itemInput.Id);
                    itemInput.CreatedBy = existingobj.CreatedBy;
                    itemInput.OrgId = existingobj.OrgId;
                    itemInput.CreatedDate = existingobj.CreatedDate;
                    hcpTheatreTimeAssocsDB.Remove(existingobj);
                }
                else
                {
                    itemInput.CreatedDate = DateTime.UtcNow;
                    itemInput.CreatedBy = baseHttpRequestContext.UserId;
                    itemInput.OrgId = baseHttpRequestContext.OrgId;
                }
                itemInput.StatusId = (short)Status.Active;
                itemInput.ModifiedBy = baseHttpRequestContext.UserId;
                itemInput.ModifiedDate = DateTime.UtcNow;
                lstAddUpdItem.Add(itemInput);

            }
            if (hcpTheatreTimeAssocsDB is not null)
            {

                foreach (var item in hcpTheatreTimeAssocsDB)
                {   //Edit or delete of Adjust row in invoiceepisodeitemassoc table should not happen
                    if (item is not null)
                    {
                        item.ModifiedBy = baseHttpRequestContext.UserId;
                        item.ModifiedDate = DateTime.UtcNow;
                        item.StatusId = (short)Status.Deleted;
                    }

                }
                lstAddUpdItem = lstAddUpdItem.Concat(hcpTheatreTimeAssocsDB).ToList();
            }

            return lstAddUpdItem;
        }

        private ICollection<HcphospitalAccommodationAssoc> EditHcphospitalAccommodationAssocs(ICollection<HcphospitalAccommodationAssoc> hcphospitalAccommodationAssocsDB, ICollection<HcphospitalAccommodationAssoc> hcphospitalAccommodationAssocsInput, BaseHttpRequestContext baseHttpRequestContext)
        {
            List<HcphospitalAccommodationAssoc> lstAddUpdItem = new();
            foreach (var itemInput in hcphospitalAccommodationAssocsInput)
            {
                if (itemInput.Id > 0)
                {
                    var existingobj = hcphospitalAccommodationAssocsDB.FirstOrDefault(x => x.Id == itemInput.Id);
                    itemInput.CreatedBy = existingobj.CreatedBy;
                    itemInput.OrgId = existingobj.OrgId;
                    itemInput.CreatedDate = existingobj.CreatedDate;
                    hcphospitalAccommodationAssocsDB.Remove(existingobj);
                }
                else
                {
                    itemInput.CreatedDate = DateTime.UtcNow;                    
                    itemInput.CreatedBy = baseHttpRequestContext.UserId;
                    itemInput.OrgId = baseHttpRequestContext.OrgId;
                }
                itemInput.StatusId = (short)Status.Active;
                itemInput.ModifiedBy = baseHttpRequestContext.UserId;
                itemInput.ModifiedDate = DateTime.UtcNow;
                lstAddUpdItem.Add(itemInput);

            }
            if (hcphospitalAccommodationAssocsDB is not null)
            {
                
                foreach (var item in hcphospitalAccommodationAssocsDB)
                {   //Edit or delete of Adjust row in invoiceepisodeitemassoc table should not happen
                    if (item is not null )
                    {
                        item.ModifiedBy = baseHttpRequestContext.UserId;
                        item.ModifiedDate = DateTime.UtcNow;
                        item.StatusId = (short)Status.Deleted;
                    }

                }
                lstAddUpdItem = lstAddUpdItem.Concat(hcphospitalAccommodationAssocsDB).ToList();
            }

            return lstAddUpdItem;
        }
        private ICollection<HcpcertificateAssoc> EditHcpcertificateAssocs(ICollection<HcpcertificateAssoc> hcpcertificateAssocDB, ICollection<HcpcertificateAssoc> hcpcertificateAssocInput, BaseHttpRequestContext baseHttpRequestContext)
        {   
            List<HcpcertificateAssoc> lstAddUpdItem = new();
            if (hcpcertificateAssocInput is null) hcpcertificateAssocInput = new List<HcpcertificateAssoc>();
            foreach (var itemInput in hcpcertificateAssocInput)
            {
                if (itemInput.Id > 0)
                {
                    var existingobj = hcpcertificateAssocDB.FirstOrDefault(x => x.Id == itemInput.Id);
                    itemInput.CreatedBy = existingobj.CreatedBy;
                    itemInput.OrgId = existingobj.OrgId;
                    
                    itemInput.CreatedDate = existingobj.CreatedDate;
                    hcpcertificateAssocDB.Remove(existingobj);
                }
                else
                {
                    itemInput.CreatedDate = DateTime.UtcNow;
                    itemInput.CreatedBy = baseHttpRequestContext.UserId;
                    itemInput.OrgId = baseHttpRequestContext.OrgId;
                }
                itemInput.StatusId = (short)Status.Active;
                itemInput.ModifiedBy = baseHttpRequestContext.UserId;
                itemInput.ModifiedDate = DateTime.UtcNow;
                lstAddUpdItem.Add(itemInput);
            }
            if (hcpcertificateAssocDB is not null)
            {

                foreach (var item in hcpcertificateAssocDB)
                {   
                    if (item is not null)
                    {
                        item.ModifiedBy = baseHttpRequestContext.UserId;
                        item.ModifiedDate = DateTime.UtcNow;
                        item.StatusId = (short)Status.Deleted;
                    }

                }
                lstAddUpdItem = lstAddUpdItem.Concat(hcpcertificateAssocDB).ToList();
            }

            return lstAddUpdItem;
        }


        private ICollection<HcpdiagnosesAssoc> EditHcpdiagnosesAssocs(ICollection<HcpdiagnosesAssoc> hcpdiagnosesAssocDB, ICollection<HcpdiagnosesAssoc> HcpdiagnosesAssocInput, BaseHttpRequestContext baseHttpRequestContext)
        {
            List<HcpdiagnosesAssoc> lstAddUpdItem = new();
            foreach (var itemInput in HcpdiagnosesAssocInput)
            {
                if (itemInput.Id > 0)
                {
                    var existingobj = hcpdiagnosesAssocDB.FirstOrDefault(x => x.Id == itemInput.Id);
                    itemInput.CreatedBy = existingobj.CreatedBy;
                    itemInput.OrgId = existingobj.OrgId;
                    itemInput.CreatedDate = existingobj.CreatedDate;
                    hcpdiagnosesAssocDB.Remove(existingobj);
                }
                else
                {
                    itemInput.CreatedDate = DateTime.UtcNow;
                    itemInput.CreatedBy = baseHttpRequestContext.UserId;
                    itemInput.OrgId = baseHttpRequestContext.OrgId;
                }
                itemInput.StatusId = (short)Status.Active;
                itemInput.ModifiedBy = baseHttpRequestContext.UserId;
                itemInput.ModifiedDate = DateTime.UtcNow;
                lstAddUpdItem.Add(itemInput);
            }
            if (hcpdiagnosesAssocDB is not null)
            {

                foreach (var item in hcpdiagnosesAssocDB)
                {
                    if (item is not null)
                    {
                        item.ModifiedBy = baseHttpRequestContext.UserId;
                        item.ModifiedDate = DateTime.UtcNow;
                        item.StatusId = (short)Status.Deleted;
                    }

                }
                lstAddUpdItem = lstAddUpdItem.Concat(hcpdiagnosesAssocDB).ToList();
            }

            return lstAddUpdItem;
        }
        private ICollection<HcpproceduresAssoc> EditHcpproceduresAssocs(ICollection<HcpproceduresAssoc> hcpproceduresAssocDB, ICollection<HcpproceduresAssoc> hcpproceduresAssocInput, BaseHttpRequestContext baseHttpRequestContext)
        {
            List<HcpproceduresAssoc> lstAddUpdItem = new();
            foreach (var itemInput in hcpproceduresAssocInput)
            {
                if (itemInput.Id > 0)
                {
                    var existingobj = hcpproceduresAssocDB.FirstOrDefault(x => x.Id == itemInput.Id);
                    itemInput.CreatedBy = existingobj.CreatedBy;
                    itemInput.OrgId = existingobj.OrgId;
                  
                    itemInput.CreatedDate = existingobj.CreatedDate;
                   
                    hcpproceduresAssocDB.Remove(existingobj);
                }
                else
                {
                    itemInput.CreatedDate = DateTime.UtcNow;
                    itemInput.CreatedBy = baseHttpRequestContext.UserId;
                    itemInput.OrgId = baseHttpRequestContext.OrgId;
                }
                itemInput.StatusId = (short)Status.Active;
                itemInput.ModifiedBy = baseHttpRequestContext.UserId;
                itemInput.ModifiedDate = DateTime.UtcNow;
                lstAddUpdItem.Add(itemInput);
            }
            if (hcpproceduresAssocDB is not null)
            {

                foreach (var item in hcpproceduresAssocDB)
                {   
                    if (item is not null)
                    {
                        item.ModifiedBy = baseHttpRequestContext.UserId;
                        item.ModifiedDate = DateTime.UtcNow;
                        item.StatusId = (short)Status.Deleted;
                    }

                }
                lstAddUpdItem = lstAddUpdItem.Concat(hcpproceduresAssocDB).ToList();
            }

            return lstAddUpdItem;
        }
        private ICollection<HcptheatreAndMbsdetailsAssoc> EditHcptheatreAndMbsdetailsAssocs(ICollection<HcptheatreAndMbsdetailsAssoc> hcptheatreAndMbsdetailsAssocDB, ICollection<HcptheatreAndMbsdetailsAssoc> hcptheatreAndMbsdetailsAssocInput, BaseHttpRequestContext baseHttpRequestContext)
        {
            List<HcptheatreAndMbsdetailsAssoc> lstAddUpdItem = new();
            foreach (var itemInput in hcptheatreAndMbsdetailsAssocInput)
            {
                if (itemInput.Id > 0)
                {
                    var existingobj = hcptheatreAndMbsdetailsAssocDB.FirstOrDefault(x => x.Id == itemInput.Id);
                    itemInput.CreatedBy = existingobj.CreatedBy;
                    itemInput.OrgId = existingobj.OrgId;
                    
                    itemInput.CreatedDate = existingobj.CreatedDate;
                  
                    hcptheatreAndMbsdetailsAssocDB.Remove(existingobj);
                }
                else
                {
                    itemInput.CreatedDate = DateTime.UtcNow;
                    itemInput.CreatedBy = baseHttpRequestContext.UserId;
                    itemInput.OrgId = baseHttpRequestContext.OrgId;
                }
                itemInput.StatusId = (short)Status.Active;
                itemInput.ModifiedBy = baseHttpRequestContext.UserId;
                itemInput.ModifiedDate = DateTime.UtcNow;
                lstAddUpdItem.Add(itemInput);
            }
            if (hcptheatreAndMbsdetailsAssocDB is not null)
            {

                foreach (var item in hcptheatreAndMbsdetailsAssocDB)
                {
                    if (item is not null)
                    {
                        item.ModifiedBy = baseHttpRequestContext.UserId;
                        item.ModifiedDate = DateTime.UtcNow;
                        item.StatusId = (short)Status.Deleted;
                    }

                }
                lstAddUpdItem = lstAddUpdItem.Concat(hcptheatreAndMbsdetailsAssocDB).ToList();
            }

            return lstAddUpdItem;
        }


        private ICollection<HcpotherServicesAssoc> EditHcpotherServicesAssocs(ICollection<HcpotherServicesAssoc> hcpotherServicesAssocDB, ICollection<HcpotherServicesAssoc> hcpotherServicesAssocInput, BaseHttpRequestContext baseHttpRequestContext)
        {
            List<HcpotherServicesAssoc> lstAddUpdItem = new();
            foreach (var itemInput in hcpotherServicesAssocInput)
            {
                if (itemInput.Id > 0)
                {
                    var existingobj = hcpotherServicesAssocDB.FirstOrDefault(x => x.Id == itemInput.Id);
                    itemInput.CreatedBy = existingobj.CreatedBy;
                    itemInput.OrgId = existingobj.OrgId;
                  
                    itemInput.CreatedDate = existingobj.CreatedDate;
                  
                    hcpotherServicesAssocDB.Remove(existingobj);
                }
                else
                {
                    itemInput.CreatedDate = DateTime.UtcNow;
                    itemInput.CreatedBy = baseHttpRequestContext.UserId;
                    itemInput.OrgId = baseHttpRequestContext.OrgId;
                }
                itemInput.StatusId = (short)Status.Active;
                itemInput.ModifiedBy = baseHttpRequestContext.UserId;
                itemInput.ModifiedDate = DateTime.UtcNow;
                lstAddUpdItem.Add(itemInput);
            }
            if (hcpotherServicesAssocDB is not null)
            {

                foreach (var item in hcpotherServicesAssocDB)
                {
                    if (item is not null)
                    {
                        item.ModifiedBy = baseHttpRequestContext.UserId;
                        item.ModifiedDate = DateTime.UtcNow;
                        item.StatusId = (short)Status.Deleted;
                    }

                }
                lstAddUpdItem = lstAddUpdItem.Concat(hcpotherServicesAssocDB).ToList();
            }

            return lstAddUpdItem;
        }
        private ICollection<HcpdocumentAssoc> EditHcpdocumentAssocs(ICollection<HcpdocumentAssoc> hcpdocumentAssocDB, ICollection<HcpdocumentAssoc> hcpdocumentAssocInput, BaseHttpRequestContext baseHttpRequestContext)
        {
            List<HcpdocumentAssoc> lstAddUpdItem = new();
            foreach (var itemInput in hcpdocumentAssocInput)
            {
                if (itemInput.Id > 0)
                {
                    var existingobj = hcpdocumentAssocDB.FirstOrDefault(x => x.Id == itemInput.Id);
                    itemInput.CreatedBy = existingobj.CreatedBy;
                    itemInput.OrgId = existingobj.OrgId;

                    itemInput.CreatedDate = existingobj.CreatedDate;

                    hcpdocumentAssocDB.Remove(existingobj);
                }
                else
                {
                    itemInput.CreatedDate = DateTime.UtcNow;
                    itemInput.CreatedBy = baseHttpRequestContext.UserId;
                    itemInput.OrgId = baseHttpRequestContext.OrgId;
                }
                itemInput.StatusId = (short)Status.Active;
                itemInput.ModifiedBy = baseHttpRequestContext.UserId;
                itemInput.ModifiedDate = DateTime.UtcNow;
                lstAddUpdItem.Add(itemInput);
            }
            if (hcpdocumentAssocDB is not null)
            {

                foreach (var item in hcpdocumentAssocDB)
                {
                    if (item is not null)
                    {
                        item.ModifiedBy = baseHttpRequestContext.UserId;
                        item.ModifiedDate = DateTime.UtcNow;
                        item.StatusId = (short)Status.Deleted;
                    }

                }
                lstAddUpdItem = lstAddUpdItem.Concat(hcpdocumentAssocDB).ToList();
            }

            return lstAddUpdItem;
        }
        /// <summary>
        /// Method to fetchAppointmentHCPSummaryFromId
        /// </summary>
        /// <param name="id"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<HcpsummaryViewInfo>> GetHCPSummaryFromId(long id, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<HcpsummaryViewInfo> apiResponse = new();
            Hcpsummary hcpSummary = await _hcpDAL.GetAppointmentHCPSummaryFromId(id, baseHttpRequestContext.OrgId);
            HcpsummaryViewInfo hcpView = null;
            if (hcpSummary != null)
            {
                hcpView = _mapper.Map<Hcpsummary, HcpsummaryViewInfo>(hcpSummary);
                List<long> listFileDetailsId = new List<long>();
                if (hcpSummary.HcpdocumentAssocs is not null)
                {
                    listFileDetailsId.AddRange(hcpSummary.HcpdocumentAssocs.Where(x => x.StatusId == (short)Status.Active).Select(x => x.FileDetailsId).ToList());
                }
                if (hcpView.FileDetailsId is not null)
                {

                    listFileDetailsId.Add((long)hcpView.FileDetailsId);

                }
                if (listFileDetailsId.Count > 0)
                {
                    string stringFileDetailsId = string.Join(",", listFileDetailsId);
                    var token = baseHttpRequestContext.BearerToken;
                    string interServiceToken = baseHttpRequestContext.InterServiceToken;
                    string fileAPiUrl = _appSettings.ApiUrls["FileServiceUrl"] + "/file/file_details_list?" + "stringFileDetailsId" + "=" + HttpUtility.UrlEncode(stringFileDetailsId);
                    RestClient restClient = new RestClient(fileAPiUrl, null, token, interServiceToken);
                    var fileApiResponse = await restClient.GetAsync<ApiResponse<List<FileDetailsOutputForId>>>(fileAPiUrl, null);
                    if (fileApiResponse.StatusCode == StatusCodes.Status200OK)
                    {
                        var fileResponse = fileApiResponse.Result;
                        var filedata = fileResponse.Find(x => x.FileDetail.Id == hcpView.FileDetailsId);
                        hcpView.FileDetailsOutput = filedata;
                        if (hcpView.HcpdocumentAssocs is not null)
                        {
                            hcpView.HcpdocumentAssocs.ToList().ForEach(doc =>
                            {
                                var filedata = fileResponse.Find(x => x.FileDetail.Id == doc.FileDetailsId);
                                doc.FileDetailsOutput = filedata;
                            });
                        }
                    }
                }

            }
        

        apiResponse.Result = hcpView;
        apiResponse.StatusCode = StatusCodes.Status200OK;
        apiResponse.Message = "Success";
        return apiResponse;
        }

        public async Task<ApiResponse<QueryResultList<HcpSummaryView>>> ListHCPSummary(QueryModel queryModel, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<QueryResultList<HcpSummaryView>> apiResponse = new();
            HcpSummaryFilter filterModel = PrepareFilterParameters(queryModel.Filter);
            if (filterModel is null)
            {
                apiResponse.Errors.Add("Filter is needed to get the list of hcp records");
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failure";
                return apiResponse;
            }
            QueryResultList<HcpSummaryView> queryList = await _hcpDAL.ListHCPSummary(baseHttpRequestContext.OrgId, queryModel, filterModel);

            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result = queryList;

            return apiResponse;
        }

        private HcpSummaryFilter PrepareFilterParameters(string filter)
        {
            if (!(String.IsNullOrEmpty(filter)))
            {
                return JsonConvert.DeserializeObject<HcpSummaryFilter>(filter);
            }
            else
            {
                return null;
            }
        }
        /// <summary>
        /// Method to add a new HCpClaim
        /// </summary>
        /// <param name="hcpClaim"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<HcpClaim>> AddHcpClaim(HcpClaim hcpClaim, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<HcpClaim> apiResponse = new();
            long loggedinUserId = baseHttpRequestContext.UserId;
            hcpClaim.OrgId = baseHttpRequestContext.OrgId;
            hcpClaim.CreatedDate = DateTime.UtcNow;
            hcpClaim.ModifiedDate = DateTime.UtcNow;
            hcpClaim.ModifiedBy = loggedinUserId;
            hcpClaim.CreatedBy = loggedinUserId;
            hcpClaim.RecordCount = (short)hcpClaim.HcpSummaryAssocs.Count;
            hcpClaim.Hcpversion = _appSettings.HCPVersion;//"1100";
            hcpClaim.Icdversion = _appSettings.ICDVersion; //"1012";
            hcpClaim.DiskReferanceNo = (await _hcpDAL.GetNextVal("[Hcp].[SeqDiskReferanceNumber]")).ToString();
            hcpClaim.ResubmittedFlag = false;
            hcpClaim.TestFlag = _appSettings.HCPTestFlag;
            hcpClaim.ClaimStatusId = (short)HcpStatus.Pending;
            if (hcpClaim.ExtractType == (short)HcpExtractType.PHDB) hcpClaim.InsurerIdentifier = string.Empty;
            hcpClaim.HcpSummaryAssocs?.ToList().ForEach(x =>
            {
                x.OrgId = baseHttpRequestContext.OrgId;
                x.CreatedDate = DateTime.UtcNow;
                x.ModifiedBy = loggedinUserId;
                x.CreatedBy = loggedinUserId;
                x.StatusId = (short)Status.Active;
            });

            hcpClaim = await _hcpDAL.AddHcpClaim(hcpClaim);
            if(hcpClaim.Id > 0)
            {   short propertyType = (hcpClaim.ExtractType==(short)HcpExtractType.HCP)?(short)AppointmentRequestDataType.HCP_Extract_Create:(short)AppointmentRequestDataType.PHDB_Extract_Create;
                await StoreHcpMessage(baseHttpRequestContext.OrgCode, hcpClaim.Id, propertyType);
            }
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Result = hcpClaim;
            apiResponse.Message = "Success";
            return apiResponse;

        }


        private async Task StoreHcpMessage(string orgCode, long propertyId, short propertyType)
        {
            PaymentRequestDataModel updateRequestDataModel = new PaymentRequestDataModel
            {
                OrgName = orgCode,
                PropertyType = propertyType,
                PropertyId = new long[] { propertyId },
            };
            Dictionary<string, string> userPros = null;
            if (propertyType == (short)AppointmentRequestDataType.HCP_Extract_Create || propertyType == (short)AppointmentRequestDataType.PHDB_Extract_Create)
            {
                userPros = new Dictionary<string, string>
                        {
                            { "RequestType","HcpExtract"},
                            { "to",_configuration["AzureAD:ASBSubNameHCP"]}
                        };


            }
            
            byte[] jsonContentByteArr = Encoding.GetEncoding("iso-8859-1").GetBytes(JsonConvert.SerializeObject(updateRequestDataModel));
            await _asbMessageSenderHelper.MessageSenderTopic(jsonContentByteArr, userPros, _configuration["AzureAD:AzSBConnStringAppointment"], _configuration["AzureAD:ASBTopicAppointment"]);
        }
        public async Task<OrganisationView> FetchMasterCompanyDetails(BaseHttpRequestContext baseHttpRequestContext)
        {

            ApiResponse<OrganisationView> apiResponseCompany = new();
            string endpoint = string.Format("/company/Organisation/{0}", baseHttpRequestContext.OrgId);
            string companySeviceUrl = _appSettings.ApiUrls["CompanyServiceUrl"] + endpoint;
            RestClient restClientCompany = new RestClient(companySeviceUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            apiResponseCompany = await restClientCompany.GetAsync<ApiResponse<OrganisationView>>(companySeviceUrl);
            return (apiResponseCompany == null || apiResponseCompany.Result == null) ? null : apiResponseCompany.Result;

        }
        public async Task<ApiResponse<QueryResultList<HcpClaimView>>> ListHCPExtractClaims(QueryModel queryModel, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<QueryResultList<HcpClaimView>> apiResponse = new();
            HcpSummaryFilter filterModel = PrepareFilterParameters(queryModel.Filter);
            List<FileDetailsOutputForId> fileResponse = new();

            if (filterModel is null)
            {
                apiResponse.Errors.Add("Filter is needed to get the list of hcp records");
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failure";
                return apiResponse;
            }
            else
            {
                if(filterModel.StartDate!=null || filterModel.EndDate != null)
                {
                    bool isWindows = System.Runtime.InteropServices.RuntimeInformation
                                                 .IsOSPlatform(OSPlatform.Windows);
                    OrganisationView orgDetails = await FetchMasterCompanyDetails(baseHttpRequestContext);
                    string timeZone = (isWindows) ? orgDetails.WindowsTimeZoneData : orgDetails.LinuxTimeZoneData;
                    if(filterModel.StartDate != null)
                        filterModel.StartDate = (DateTime)DateTimeConversion.ConvertTimeToUtc((DateTime)filterModel.StartDate, timeZone);
                    if (filterModel.EndDate != null)
                        filterModel.EndDate = (DateTime)DateTimeConversion.ConvertTimeToUtc((DateTime)filterModel.EndDate, timeZone);
                }
            }
            QueryResultList<HcpClaimView> queryList = await _hcpDAL.ListHCPExtractClaims(baseHttpRequestContext.OrgId, queryModel, filterModel);
            List<long> listFileDetailsId = new();
            if (queryList != null && queryList.ItemRecords is not null)
            {
                listFileDetailsId = queryList.ItemRecords.Where(x => x.FileDetailsId != null).Select(x => (long)x.FileDetailsId).ToList();
                if (listFileDetailsId.Count > 0)
                {
                    string stringFileDetailsId = string.Join(",", listFileDetailsId);
                    var token = baseHttpRequestContext.BearerToken;
                    string interServiceToken = baseHttpRequestContext.InterServiceToken;
                    string fileAPiUrl = _appSettings.ApiUrls["FileServiceUrl"] + "/file/file_details_list?" + "stringFileDetailsId" + "=" + HttpUtility.UrlEncode(stringFileDetailsId);
                    RestClient restClient = new RestClient(fileAPiUrl, null, token, interServiceToken);
                    var fileApiResponse = await restClient.GetAsync<ApiResponse<List<FileDetailsOutputForId>>>(fileAPiUrl, null);
                    if (fileApiResponse.StatusCode == StatusCodes.Status200OK)
                    {
                        fileResponse = fileApiResponse.Result;
                    }
                    else
                    {
                        apiResponse.StatusCode = fileApiResponse.StatusCode;
                        apiResponse.Errors = fileApiResponse.Errors;
                        apiResponse.Message = fileApiResponse.Message;
                        return apiResponse;
                    }

                    foreach (var item in queryList.ItemRecords)
                    {
                        if (item.FileDetailsId != null)
                        {
                            var filedata = fileResponse.Find(x => x.FileDetail.Id == item.FileDetailsId);
                            item.FileDetailsOutput = filedata;
                        }
                    }
                }

            }
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result = queryList;

            return apiResponse;
        }

        public async Task<ApiResponse<Hcpsummary>> SaveAndValidateHCPSummary(long id, HcpsummaryViewInfo hcpsummaryViewInfo, BaseHttpRequestContext baseHttpRequestContext)
        {

            ApiResponse<Hcpsummary> apiResponse = new();
            bool validPrincipalDiagnosisCode = false;
            if(hcpsummaryViewInfo is not null)
            {
                Hcpsummary hcpSummary = _mapper.Map<HcpsummaryViewInfo, Hcpsummary>(hcpsummaryViewInfo);
                if(hcpSummary.HcpdiagnosesDetails is not null && hcpSummary.HcpdiagnosesDetails.HcpdiagnosesAssocs is not null && hcpSummary.HcpdiagnosesDetails.HcpdiagnosesAssocs.Count > 0)
                {
                    if(hcpSummary.HcpdiagnosesDetails.HcpdiagnosesAssocs.Where(x=>x.PrinipalDiagnosis==true && x.StatusId!=(short)Status.Inactive && x.StatusId != (short)Status.Deleted).Count() == 1)
                    {
                        validPrincipalDiagnosisCode = true;
                    }
                }
                decimal maxAmt = 1000000.00M;
                if (hcpSummary.HcphospitalAccommodationAssocs is not null && hcpSummary.HcphospitalAccommodationAssocs.Count > 0)
                {
                    if((hcpSummary.HcphospitalAccommodationAssocs.Where(x=>x.AmountCharged!=null ).Select(x => (decimal)x.AmountCharged).Sum()) > maxAmt)
                    {
                        apiResponse.Errors.Add("Maximum Charge Amount Exceeded For Accommodation Details.");

                    }
                }
                if (hcpSummary.HcptheatreAndMbsdetailsAssocs is not null && hcpSummary.HcptheatreAndMbsdetailsAssocs.Count > 0)
                {
                    if ((hcpSummary.HcptheatreAndMbsdetailsAssocs.Where(x => x.AmountCharged != null).Select(x => (decimal)x.AmountCharged).Sum()) > maxAmt)
                    {
                        apiResponse.Errors.Add("Maximum Charge Amount Exceeded For Mbs Theatre Details.");

                    }
                }
                if (hcpSummary.HcpotherServicesAssocs is not null && hcpSummary.HcpotherServicesAssocs.Count > 0)
                {
                    if ((hcpSummary.HcpotherServicesAssocs.Where(x => x.AmountCharged != null).Select(x => (decimal)x.AmountCharged).Sum()) > maxAmt)
                    {
                        apiResponse.Errors.Add("Maximum Charge Amount Exceeded For Other Services.");

                    }
                }
                if (!validPrincipalDiagnosisCode)
                    apiResponse.Errors.Add("One principalDiagnosis code should be present");

                if(apiResponse.Errors is null || apiResponse.Errors.Count == 0)
                {
                    hcpSummary.HCPStatusId = (short)HcpStatus.Completed;
                    hcpSummary.PHDBStatusId = (short)HcpStatus.Completed;
                    apiResponse = await EditHCPSummary(id, hcpSummary, baseHttpRequestContext);
                }
            }
            return apiResponse;


        }
        private IDictionary GetFilledProperties(HcpSummaryUpdateView hcpSummaryUpdateView)
        {
            Dictionary<string, object> propertyDictionary = new();
            foreach (PropertyInfo property in hcpSummaryUpdateView.GetType().GetProperties())
            {
                var value = property.GetValue(hcpSummaryUpdateView);
                if (value is not null)
                    propertyDictionary.Add(property.Name, property.GetValue(hcpSummaryUpdateView));
            }
            return propertyDictionary;
        }
        /// <summary>
        /// Method to partially update hcpsummary -hcpStatus
        /// </summary>
        /// <param name="hcpSummaryUpdateView"></param>
        /// <param name="id"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<string>> PartialUpdateHcpSummary(HcpSummaryUpdateView hcpSummaryUpdateView, long id, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<string> apiResponse = new();
            int orgId = baseHttpRequestContext.OrgId;
            if (hcpSummaryUpdateView is not null)
            {
                Hcpsummary appointmentHcpsummaryDB = await _hcpDAL.GetAppointmentHCPSummaryDetailsFromId(id, orgId);
                if (appointmentHcpsummaryDB is not null)
                {
                    Dictionary<string, object> propertyDictionary = (Dictionary<string, object>)GetFilledProperties(hcpSummaryUpdateView);
                    int rows = await _hcpDAL.PartialUpdateHcpSummary(propertyDictionary, baseHttpRequestContext.UserId, appointmentHcpsummaryDB);
                    if(rows > 0)
                    {
                        apiResponse.StatusCode = StatusCodes.Status200OK;
                        apiResponse.Result = "Successfully updated";
                        apiResponse.Message = "Success";
                        return apiResponse;
                    }
                }

            }
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            apiResponse.Result = "Not successful";
            apiResponse.Message = "Failure";
            return apiResponse;
        }

        public async Task<ApiResponse<string>> PartialUpdateHcpClaim(HcpClaimUpdateView hcpClaimUpdateView, long id, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<string> apiResponse = new();
            int orgId = baseHttpRequestContext.OrgId;
            if (hcpClaimUpdateView is not null)
            {
                int rows = 0;
                HcpClaim hcpClaimDb = await _hcpDAL.GetHcpClaimFromId(id, orgId);
                if (hcpClaimDb is not null)
                {
                    Dictionary<string, object> propertyDictionary = (Dictionary<string, object>)GetFilledProperties(hcpClaimUpdateView);
                    if (hcpClaimUpdateView.ClaimStatusId is null || hcpClaimDb.HcpSummaryAssocs is null )
                    {
                        hcpClaimDb.HcpSummaryAssocs = null;
                        rows = await _hcpDAL.PartialUpdateHcpClaim(propertyDictionary, baseHttpRequestContext.UserId, hcpClaimDb);

                    }
                    else
                    {
                         rows = await _hcpDAL.PartialUpdateHcpClaimStatus(hcpClaimUpdateView, id, hcpClaimDb.HcpSummaryAssocs.Select(x=>x.HcpsummaryId).ToList(),baseHttpRequestContext.OrgId, baseHttpRequestContext.UserId, hcpClaimDb.ExtractType);
                    }
                    if (rows > 0)
                    {
                        apiResponse.StatusCode = StatusCodes.Status200OK;
                        apiResponse.Result = "Successfully updated";
                        apiResponse.Message = "Success";
                        return apiResponse;
                    }
                }

            }
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            apiResponse.Result = "Not successful";
            apiResponse.Message = "Failure";
            return apiResponse;
        }

        private Dictionary<string, object> GetFilledProperties(HcpClaimUpdateView hcpClaimUpdateView)
        {
            Dictionary<string, object> propertyDictionary = new();
            foreach (PropertyInfo property in hcpClaimUpdateView.GetType().GetProperties())
            {
                var value = property.GetValue(hcpClaimUpdateView);
                if (value is not null)
                    propertyDictionary.Add(property.Name, property.GetValue(hcpClaimUpdateView));
            }
            return propertyDictionary;
        }
    }
}
