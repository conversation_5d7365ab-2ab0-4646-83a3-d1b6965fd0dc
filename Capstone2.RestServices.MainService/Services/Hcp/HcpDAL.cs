﻿using Capstone2.RestServices.Hcp.Context;
using Capstone2.RestServices.Hcp.Interfaces;
using Capstone2.RestServices.Hcp.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Globalization;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Hcp.Services
{
    public class HcpDAL : IHcpDAL
    {
        public readonly ReadOnlyHcpDBContext _readOnlyDbContext;
        public readonly UpdatableHcpDBContext _updatableDBContext;

        public HcpDAL(ReadOnlyHcpDBContext readOnlyDbContext, UpdatableHcpDBContext updatableDBContext)
        {
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
        }

        public Task<int> UpdateAppointmentHCPSummaryAsync(Hcpsummary appointmentHcpsummary)
        {
            _updatableDBContext.HcpSummary.Update(appointmentHcpsummary);
            return _updatableDBContext.SaveChangesAsync();
        }


        public async Task<Hcpsummary> GetAppointmentHCPSummaryFromId(long id, int orgId)
        {
            return await _readOnlyDbContext.HcpSummary
                .Include(x => x.HcphospitalAccommodationAssocs.Where(x => x.StatusId == (short)Status.Active))
                .Include(x => x.HcpotherDetails)
               .Include(x => x.HcpcertificateDetails)
                .Include(x => x.HcpproceduresAssocs.Where(x => x.StatusId == (short)Status.Active))
                .Include(x => x.HcpsameDayAccommodationDetails)
                .Include(x => x.HcptheatreAndMbsdetailsAssocs.Where(x => x.StatusId == (short)Status.Active))
                .Include(x => x.HcpotherServicesAssocs.Where(x => x.StatusId == (short)Status.Active))
                //.Include(x => x.HcpcertificateAssocs.Where(x => x.StatusId == (short)Status.Active))
                .Include(x => x.HcptheatreAndMbsdetailsAssocs.Where(x => x.StatusId == (short)Status.Active))
                .Include(x => x.HcpTheatreTimeAssocs.Where(x => x.StatusId == (short)Status.Active))
                .Include(x => x.HcpdocumentAssocs.Where(x => x.StatusId == (short)Status.Active))
                .Include(x => x.HcpdiagnosesDetails).ThenInclude(diag => diag.HcpdiagnosesAssocs.Where(diag => diag.StatusId == (short)Status.Active)).DefaultIfEmpty()
                .Where(hcp => hcp.Id == id && hcp.OrgId == orgId).AsNoTracking().FirstOrDefaultAsync();


            //return await( from hcp in _readOnlyDbContext.AppointmentHcpSummary
            //              .Include(hcp => hcp.HcphospitalAccommodationAssocs)//.Where(x => x.StatusId == (short)Status.Active)//.AsNoTracking()
            //   .Where(hcp => hcp.Id == id && hcp.OrgId == orgId)//.AsNoTracking()
            //    select hcp).FirstOrDefaultAsync();
        }

        public async Task<QueryResultList<HcpSummaryView>> ListHCPSummary(int orgId, QueryModel queryModel, HcpSummaryFilter filterModel)
        {
            List<SqlParameter> paramsList = new List<SqlParameter>();
            var limit = queryModel.PageSize;
            var offset = (queryModel.PageNumber - 1) * queryModel.PageSize;

            paramsList.Add(new SqlParameter("@OrgId", orgId));
            paramsList.Add(new SqlParameter("@offset", offset));
            paramsList.Add(new SqlParameter("@limit", limit));

            if (!(filterModel is null))
            {
                if (!string.IsNullOrWhiteSpace(filterModel.HealthFund))
                {
                    paramsList.Add(new SqlParameter("@HealthFund", filterModel.HealthFund));
                }
                if (filterModel.StartDate != null)
                {
                    paramsList.Add(new SqlParameter("@StartDate", filterModel.StartDate));
                }
                if (filterModel.EndDate != null)
                {
                    paramsList.Add(new SqlParameter("@EndDate", filterModel.EndDate));
                }
                if (filterModel.HCPSummaryId is not null && filterModel.HCPSummaryId.Count > 0)
                    paramsList.Add(new SqlParameter("@hcpSummaryId", string.Join<long?>(",", filterModel.HCPSummaryId)));
                if (filterModel.HCPStatusId is not null && filterModel.HCPStatusId.Count > 0)
                    paramsList.Add(new SqlParameter("@hcpStatusId", string.Join<short?>(",", filterModel.HCPStatusId)));
            }
            if (!(queryModel.SortOrder is null))
            {
                paramsList.Add(new SqlParameter("@sortOrder", queryModel.SortOrder));

            }
            if (!(queryModel.SortTerm is null))
            {
                paramsList.Add(new SqlParameter("@sortTerm", queryModel.SortTerm));
            }


            var response = await ExecuteStoredProcedure("[Hcp].[ListHCPSummary]", paramsList);
            response.PageNumber = queryModel.PageNumber;
            response.PageSize = queryModel.PageSize;
            return response;
        }
        private async Task<QueryResultList<HcpSummaryView>> ExecuteStoredProcedure(string storedProcedureName, IEnumerable<SqlParameter> parameters)
        {
            var dbConnection = (_readOnlyDbContext.Database.GetDbConnection());
            using (dbConnection)
            {
                using (var cmd = dbConnection.CreateCommand())
                {
                    cmd.CommandText = storedProcedureName;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandTimeout = 900;
                    foreach (var parameter in parameters)
                    {
                        cmd.Parameters.Add(parameter);
                    }
                    await dbConnection.OpenAsync().ConfigureAwait(false);
                    var reader = await cmd.ExecuteReaderAsync();

                    return SqlDatoToJson(reader);
                }
            }
        }
        private QueryResultList<HcpSummaryView> SqlDatoToJson(DbDataReader dataReader)
        {
            var dataTable = new DataTable();
            List<HcpSummaryView> convList = new List<HcpSummaryView>();
            dataTable.Load(dataReader);

            DateTime validValue;
            convList = (from rw in dataTable.AsEnumerable()
                        select new HcpSummaryView
                        {
                            Id = Convert.ToInt64(rw["Id"]),
                            OrgId = Convert.ToInt32(rw["OrgId"]),
                            PatientRecordID = rw["PatientRecordID"] == DBNull.Value ? null : Convert.ToInt64(rw["PatientRecordID"]),

                            Appointment = rw["Appointment"] == DBNull.Value ? null : Convert.ToString(rw["Appointment"]),
                            AppointmentDetailsId = rw["AppointmentDetailsId"] == DBNull.Value ? 0 : Convert.ToInt64(rw["AppointmentDetailsId"]),

                            InsurerIdentifier = rw["InsurerIdentifier"] == DBNull.Value ? null : Convert.ToString(rw["InsurerIdentifier"]),
                            PatientFirstName = rw["PatientFirstName"] == DBNull.Value ? null : Convert.ToString(rw["PatientFirstName"]),
                            PatientSurName = rw["PatientSurName"] == DBNull.Value ? null : Convert.ToString(rw["PatientSurName"]),
                            // PatientDOB = rw["PatientDOB"] == DBNull.Value ? null : DateTime.TryParse(Convert.ToString(rw["PatientDOB"]), out validValue) ? validValue : (DateTime?)null,
                            IsCertificateDetailsCompleted = rw["IsCertificateDetailsCompleted"] == DBNull.Value ? false : Convert.ToBoolean(rw["IsCertificateDetailsCompleted"]),
                            IsClinicalCodingDetailsCompleted = rw["IsClinicalCodingDetailsCompleted"] == DBNull.Value ? false : Convert.ToBoolean(rw["IsClinicalCodingDetailsCompleted"]),
                            IsConsentDetailsCompleted = rw["IsConsentDetailsCompleted"] == DBNull.Value ? false : Convert.ToBoolean(rw["IsConsentDetailsCompleted"]),
                            IsTheatreDetailsCompleted = rw["IsTheatreDetailsCompleted"] == DBNull.Value ? false : Convert.ToBoolean(rw["IsTheatreDetailsCompleted"]),
                            IsDateTimeDetailsCompleted = rw["IsDateTimeDetailsCompleted"] == DBNull.Value ? false : Convert.ToBoolean(rw["IsDateTimeDetailsCompleted"]),
                            AdmissionDate = rw["AdmissionDate"] == DBNull.Value ? null : DateTime.TryParse(Convert.ToString(rw["AdmissionDate"]), out validValue) ? validValue : (DateTime?)null,
                            DischargeDate = rw["DischargeDate"] == DBNull.Value ? null : DateTime.TryParse(Convert.ToString(rw["DischargeDate"]), out validValue) ? validValue : (DateTime?)null,
                            HCPStatusId = rw["HCPStatusId"] == DBNull.Value ? (short)HcpStatus.Pending : Convert.ToInt16(rw["HCPStatusId"]),
                            PHDBStatusId = rw["PHDBStatusId"] == DBNull.Value ? (short)HcpStatus.Pending : Convert.ToInt16(rw["PHDBStatusId"]),
                            LastModifiedUser = rw["LastModifiedUser"] == DBNull.Value ? null : JsonConvert.DeserializeObject<UserDetailInfo>(Convert.ToString(rw["LastModifiedUser"])),
                            CreatedUser = rw["CreatedUser"] == DBNull.Value ? null : JsonConvert.DeserializeObject<UserDetailInfo>(Convert.ToString(rw["CreatedUser"])),
                            CreatedDate = rw["CreatedDate"] == DBNull.Value ? null : DateTime.TryParse(Convert.ToString(rw["CreatedDate"]), out validValue) ? validValue : (DateTime?)null,
                            ModifiedDate = rw["ModifiedDate"] == DBNull.Value ? null : DateTime.TryParse(Convert.ToString(rw["ModifiedDate"]), out validValue) ? validValue : (DateTime?)null


                        }).ToList();

            var dataTable2 = new DataTable();
            dataTable2.Load(dataReader);
            var Total = int.Parse(JsonConvert.SerializeObject(dataTable2.Rows[0][0]));
            var respose = new QueryResultList<HcpSummaryView>()
            {
                ItemRecords = convList,
                CurrentCount = convList.Count(),
                TotalCount = Total,
                PageNumber = 0,
                PageSize = 0
            };
            return respose;
        }

        /// <summary>
        /// Method to fetch the next value for a sequence
        /// </summary>
        /// <param name="sequenceName"></param>
        /// <returns></returns>
        public async Task<long> GetNextVal(string sequenceName)
        {
            var p = new SqlParameter("@result", System.Data.SqlDbType.BigInt);
            p.Direction = System.Data.ParameterDirection.Output;
            await _updatableDBContext.Database.ExecuteSqlRawAsync("set @result = next value for " + sequenceName, p);
            return (long)p.Value;
        }

        public async Task<HcpClaim> AddHcpClaim(HcpClaim hcpClaim)
        {

            await _updatableDBContext.HcpClaim.AddAsync(hcpClaim);
            await _updatableDBContext.SaveChangesAsync();
            return hcpClaim;

        }

        public async  Task<QueryResultList<HcpClaimView>> ListHCPExtractClaims(int orgId, QueryModel queryModel, HcpSummaryFilter filterModel)
        {
            var query = from HC in _readOnlyDbContext.HcpClaim.Include(x=>x.HcpSummaryAssocs)
                        //join HS in _readOnlyDbContext.HcpSummaryAssocs on HC.Id equals HS.HcpClaimId into JHS
                        select new HcpClaimView()
                        {
                            Id = HC.Id,
                            DatePrepared = HC.DatePrepared,
                            DiskReferanceNo = HC.DiskReferanceNo,
                            ExtractType = HC.ExtractType,
                            InsurerIdentifier = HC.InsurerIdentifier,
                            HospitalProviderNumber = HC.HospitalProviderNumber,
                            FileDetailsId = HC.FileDetailsId,
                            HcpSummaryAssocs = HC.HcpSummaryAssocs,
                            RecordCount = HC.RecordCount,
                            ModifiedDate=HC.ModifiedDate,
                            CreatedDate=HC.CreatedDate,
                            ClaimStatusId=HC.ClaimStatusId
                        };

            if(filterModel is not null)
            {
                if(filterModel.StartDate is not null)
                {
                    query = query.Where(x => x.ModifiedDate >= filterModel.StartDate);
                }
                if (filterModel.EndDate is not null)
                {
                    query = query.Where(x => x.ModifiedDate <= filterModel.EndDate);
                }
                if (!string.IsNullOrEmpty(filterModel.HealthFund))
                {
                    query = query.Where(x => x.InsurerIdentifier == filterModel.HealthFund);
                }
            }

            List<HcpClaimView> paginatedList = await CreatePaginateList(query, queryModel);
            QueryResultList<HcpClaimView> queryList = new QueryResultList<HcpClaimView>();
            if (paginatedList != null)
            {

                queryList.ItemRecords = paginatedList;
                queryList.PageNumber = queryModel.PageNumber;
                queryList.PageSize = queryModel.PageSize;
                queryList.CurrentCount = paginatedList.Count();

            }
            else
            {
                queryList.ItemRecords = null;
                queryList.CurrentCount = 0;

            }
            queryList.TotalCount = query.Count();
            queryList.PageNumber = queryModel.PageNumber;
            queryList.PageSize = queryModel.PageSize;
            return queryList;
        }

        private async Task<List<HcpClaimView>> CreatePaginateList(IQueryable<HcpClaimView> query, QueryModel queryModel)
        {
            if (queryModel.PageNumber > 0 && queryModel.PageSize > 0)
            {
                if (query.Any())
                {

                    List<HcpClaimView> paginatedList = await query.Skip((queryModel.PageNumber - 1) * queryModel.PageSize)
                 .Take(queryModel.PageSize).ToListAsync();

                    return paginatedList;

                }

            }
            return null;
        }

        public async Task<Hcpsummary> GetAppointmentHCPSummaryDetailsFromId(long id, int orgId)
        {
            return await _readOnlyDbContext.HcpSummary.AsNoTracking().Where(x => x.Id == id && x.OrgId == orgId).FirstOrDefaultAsync();
        }

        public async Task<int> PartialUpdateHcpSummary(Dictionary<string, object> nameValuePairProperties, long userId, Hcpsummary appointmentHcpsummaryDB)
        {
             _updatableDBContext.HcpSummary.Attach(appointmentHcpsummaryDB);
            var currentValues = _updatableDBContext.Entry(appointmentHcpsummaryDB).CurrentValues;
            var type = appointmentHcpsummaryDB.GetType();
            PropertyInfo[] p = type.GetProperties();
            foreach (string key in nameValuePairProperties.Keys)
            {
                if (p.ToList().Where(p => p.Name.ToLower() == key.ToLower()).Any())
                {
                    var value = nameValuePairProperties[key];

                    currentValues[key] = Convert.ChangeType(value, currentValues[key].GetType(), CultureInfo.InvariantCulture);

                }
            }
            if (_updatableDBContext.Entry(appointmentHcpsummaryDB).State == EntityState.Modified)
            {
                appointmentHcpsummaryDB.ModifiedDate = DateTime.UtcNow;
                appointmentHcpsummaryDB.ModifiedBy = userId;
            }
            return await _updatableDBContext.SaveChangesAsync();
        }

        public async Task<HcpClaim> GetHcpClaimFromId(long id, int orgId)
        {
            return await _readOnlyDbContext.HcpClaim.AsNoTracking().Where(x => x.Id == id && x.OrgId == orgId).Include(x=>x.HcpSummaryAssocs).FirstOrDefaultAsync();
        }

        public async Task<int> PartialUpdateHcpClaim(Dictionary<string, object> nameValuePairProperties, long userId, HcpClaim hcpClaimDb)
        {
            _updatableDBContext.HcpClaim.Attach(hcpClaimDb);
            var currentValues = _updatableDBContext.Entry(hcpClaimDb).CurrentValues;
            var type = hcpClaimDb.GetType();
            PropertyInfo[] p = type.GetProperties();
            foreach (string key in nameValuePairProperties.Keys)
            {
                if (p.ToList().Where(p => p.Name.ToLower() == key.ToLower()).Any())
                {
                    var value = nameValuePairProperties[key];

                    currentValues[key] = Convert.ChangeType(value, currentValues[key].GetType(), CultureInfo.InvariantCulture);

                }
            }
            if (_updatableDBContext.Entry(hcpClaimDb).State == EntityState.Modified)
            {
                hcpClaimDb.ModifiedDate = DateTime.UtcNow;
                hcpClaimDb.ModifiedBy = userId;
            }
            return await _updatableDBContext.SaveChangesAsync();
        }

        public async Task<int> PartialUpdateHcpClaimStatus(HcpClaimUpdateView hcpClaimUpdateView, long id, List<long> listIds,int orgId,long userId,short extractType)
        {
            var p0 = new SqlParameter("@idsList", string.Join<long>(",", listIds));
            var p1 = new SqlParameter("@claimStatusId", hcpClaimUpdateView.ClaimStatusId);
            var p2 = new SqlParameter("@modifiedDate", DateTime.UtcNow);
            var p3 = new SqlParameter("@modifiedBy", userId);
            var p4 = new SqlParameter("@id", id); 

            int rows = await _updatableDBContext.Database.ExecuteSqlRawAsync(@"UPDATE Hcp.HcpClaim SET ClaimStatusId = @claimStatusId,
                                                                                                   
                                                                                                    ModifiedDate = @modifiedDate,
                                                                                                    ModifiedBy = @modifiedBy
                                                                        WHERE ID =@id", p1, p2, p3, p4);

            if(extractType==(short)HcpExtractType.HCP)
                rows = await _updatableDBContext.Database.ExecuteSqlRawAsync(@"UPDATE Hcp.HcpSummary SET HcpStatusId = @claimStatusId,
                                                                                                   
                                                                                                        ModifiedDate = @modifiedDate,
                                                                                                        ModifiedBy = @modifiedBy
                                                                             WHERE ID in ( Select [VALUE] as Id  FROM string_split(@idsList,','))", p1, p2, p3,p0);
            else
                rows = await _updatableDBContext.Database.ExecuteSqlRawAsync(@"UPDATE Hcp.HcpSummary SET PHDBStatusId = @claimStatusId,
                                                                                                   
                                                                                                        ModifiedDate = @modifiedDate,
                                                                                                        ModifiedBy = @modifiedBy
                                                                             WHERE ID in ( Select [VALUE] as Id  FROM string_split(@idsList,','))", p1, p2, p3, p0);
            return rows;
        }
    }

}
