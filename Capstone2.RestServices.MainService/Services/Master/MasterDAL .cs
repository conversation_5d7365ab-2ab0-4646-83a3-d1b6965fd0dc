﻿using Capstone2.RestServices.Master.Context;
using Capstone2.RestServices.Master.Interfaces;
using Capstone2.RestServices.Master.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Enum;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Master.Services
{
    public class MasterDAL : IMasterDAL
    {
        public ReadOnlyMasterDBContext _readOnlyDbContext;
        public UpdatableMasterDBContext _updatableDBContext;

        public MasterDAL(ReadOnlyMasterDBContext readOnlyDbContext, UpdatableMasterDBContext updatableDBContext)
        {
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
        }

        public async Task<List<CountryMaster>> GetCountriesDAL()
        {
            return await _readOnlyDbContext.CountryMaster.ToListAsync();
        }

        public async Task<List<StateMaster>> GetStatesDAL()
        {
            return await _readOnlyDbContext.StateMaster.ToListAsync();
        }

        public async Task<List<LanguageMaster>> GetLangaguesDAL()
        {
            return await _readOnlyDbContext.LanguageMaster.OrderBy(p => p.LanguageName).ToListAsync();
        }

        public async Task<List<TimeZoneMaster>> GetTimeZonesDAL()
        {
            return await _readOnlyDbContext.TimeZoneMaster.OrderBy(p => p.TimeZoneName).ToListAsync();
        }
        public async Task<List<NationalityMaster>> GetNationalitiesDAL()
        {
            return await _readOnlyDbContext.NationalityMaster.OrderBy(p => p.Nationality).ToListAsync();
        }

        public async Task<List<SystemConstant>> GetSystemConstantsDAL()
        {
            return await _readOnlyDbContext.SystemConstant.Where(p => p.Status.Equals(true)).ToListAsync();
        }

        public async Task<List<DataFieldsView>> GetDataFielsDAL()
        {
            return await (from DF in _readOnlyDbContext.DataFields
                          where DF.StatusId == (short)Status.Active
                          orderby DF.Id ascending
                          select new DataFieldsView()
                          {
                              Id = DF.Id,
                              DataFieldName = DF.DataFieldName,
                              TemplateFieldName = DF.TemplateFieldName,
                              DataFieldTypeId = DF.DataFieldTypeId,
                              DataFieldTypeName = ((DataFieldType)DF.DataFieldTypeId).GetDescription(),
                              DynamicQuery = DF.DynamicQuery,
                              DummyValues = DF.DummyValues,
                              StatusId = DF.StatusId,
                              ModifiedDate = DF.ModifiedDate,
                              TemplateTypeIds = DF.TemplateTypeIds

                          }).ToListAsync();
        }

        public async Task<List<RolesCategory>> GetRolesCategoriesDAL()
        {
            return await _readOnlyDbContext.RolesCategories.Where(p => p.Status.Equals(true)).ToListAsync();
        }

        public async Task<List<RolesPermission>> GetRolesPermissionsDAL()
        {
            try
            {
                return await _readOnlyDbContext.RolesPermissions.Where(p => p.Status.Equals(true) && p.IsHidden.Equals(false)).ToListAsync();
            }
            catch (Exception e)
            {
                return await _readOnlyDbContext.RolesPermissions.Where(p => p.Status.Equals(true) && p.IsHidden.Equals(false)).ToListAsync();
            }
        }

        public async Task<List<MedicareParticipants>> GetMedicareParticipants()
        {
            return await _readOnlyDbContext.ParticipantDetails.Where(p => p.StatusId.Equals((short)Status.Active)).ToListAsync();
        }

        public async Task<int> AddMedicareParticipants(ICollection<MedicareParticipants> participants)
        {
            _updatableDBContext.ParticipantDetails.AddRange(participants);
            return await _updatableDBContext.SaveChangesAsync();
        }

        public async Task<int> UpdateAccountHolderData(ICollection<MedicareParticipants> participants)
        {
            _updatableDBContext.ParticipantDetails.UpdateRange(participants);
            return await _updatableDBContext.SaveChangesAsync();
        }
        /// <summary>
        /// Method to fetch the MedicareCosntants
        /// </summary>
        /// <returns></returns>
        public async Task<List<MedicareConstant>> GeMedicareConstants()
        {
            return await _readOnlyDbContext.MedicareConstants.Where(p => p.Status.Equals(true)).ToListAsync();
        }

        /// <summary>
        /// Method to fetch the MedicareErrors
        /// </summary>
        /// <returns></returns>
        public async Task<List<MedicareErrors>> GetMedicareErrors()
        {
            return await _readOnlyDbContext.MedicareErrors.ToListAsync();
        }

        /// <summary>
        /// Method to fetch the ApiPermissions
        /// </summary>
        /// <returns></returns>
        public async Task<List<ApiPermissionsAssoc>> GetApiPermissionsDAL()
        {
            return await _readOnlyDbContext.ApiPermissionsAssocs.ToListAsync();
        }

      /// <summary>
      /// Method to fetch list of presentingillnesscodes
      /// </summary>
      /// <returns></returns>

        public async Task<List<PresentingIllnessCode>> GetPresentingIllnessCodes()
        {
            return await _readOnlyDbContext.PresentingIllnessCodes.ToListAsync();
        }

        public async Task<List<MedicalContractorBand>> GetMedContDAL()
        {
            return await _readOnlyDbContext.MedicalContractorBand.OrderBy(p => p.Id).ToListAsync();
        }

        public async Task<MedicareParticipants> GetMedicareParticipantsById(int mpId)
        {
            return await _readOnlyDbContext.ParticipantDetails.Where(p => p.Id == mpId).FirstOrDefaultAsync();
        }

        public async Task<List<MedicareParticipants>> GetParticipantsWithFilter(QueryModel queryModel, MedParticipantsFilterModel filterModel)
        {
           /* var query = (from PD in _readOnlyDbContext.ParticipantDetails
                        select new MedicareParticipants());*/


            if (!(filterModel is null))
            {
                if (filterModel.StatusId != null && filterModel.StatusId.Any())
                {

                    // query = query.Where(x => filterModel.StatusId.Contains(x.StatusId));
                    return await _readOnlyDbContext.ParticipantDetails.Where(p => filterModel.StatusId.Contains(p.StatusId)).ToListAsync();

                }

            }

            return null;
          //  List<MedicareParticipants> paginatedList = await query.ToListAsync();
          //  return paginatedList;
        }


        public async Task<List<MedicareParticipantsInfo>> GetMedicareParticipantsByParticipantIds(List<string> lstParticipantId)
        {
            return await _readOnlyDbContext.ParticipantDetails.Where(p => lstParticipantId.Contains(p.ParticipantId)).OrderBy(x => x.StatusId)
                .Select(x => new MedicareParticipantsInfo()
                {
                    Id = x.Id,
                    ParticipantId = x.ParticipantId,
                    ParticipantName = x.ParticipantName,
                    StatusId = x.StatusId

                }).ToListAsync();
        }

    }
}
