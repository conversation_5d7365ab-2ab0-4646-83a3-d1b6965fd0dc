﻿using Capstone2.RestServices.Master.Context;
using Capstone2.RestServices.Master.Interfaces;
using Capstone2.RestServices.Master.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Master.Services
{
    public class HealthFundDAL : IHealthFundDAL
    {
        public ReadOnlyMasterDBContext _readOnlyDbContext;
        public UpdatableMasterDBContext _updatableDBContext;

        public HealthFundDAL(ReadOnlyMasterDBContext readOnlyDbContext, UpdatableMasterDBContext updatableDBContext)
        {
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
        }

        /// <summary>
        /// Method to add Health Fund
        /// </summary>
        /// <param name="inputHealthFund"></param>
        /// <returns></returns>
        public async Task<int> AddHFGroupDAL(HealthFundGroup inputHealthFund)
        {
            await _updatableDBContext.HealthFundGroups.AddAsync(inputHealthFund);
            int rows = await _updatableDBContext.SaveChangesAsync();
            return rows > 0 ? inputHealthFund.Id : 0;
        }

        /// <summary>
        /// Method to create a new GroupToHealthFundAssoc
        /// </summary>
        /// <param name="grpToHealthFundAssoc"></param>
        /// <returns></returns>
        public async Task<int> AddGroupToHFAssocsDAL(List<GroupToHealthFundAssoc> grpToHealthFundAssoc)
        {
            await _updatableDBContext.GroupToHealthFundAssocs.AddRangeAsync(grpToHealthFundAssoc);
            return await _updatableDBContext.SaveChangesAsync();
        }


        public async Task<List<HealthFundGroup>> GetHealthFundGroupsDAL()
        {


            List<HealthFundGroup> healthQuery = await (from HG in _readOnlyDbContext.HealthFundGroups
                                                       where HG.StatusId == (short)Status.Active
                                                      select new HealthFundGroup()
                                                      {
                                                          Id = HG.Id,
                                                          Name = HG.Name,
                                                          StatusId = HG.StatusId,
                                                          ModifiedDate = HG.ModifiedDate,
                                                          ModifiedBy = HG.ModifiedBy,
                                                          GroupToHealthFundAssocs = (from assoc in _readOnlyDbContext.GroupToHealthFundAssocs.Where(x => x.HealthFundGroupsId == HG.Id && x.StatusId == (short)Status.Active)
                                                                                                                          select assoc).ToList()
                                                      }).ToListAsync();

            return healthQuery;

        }

        public async Task<HealthFundGroup> GetHFForEdit(int hfId)
        {
            HealthFundGroup healthFundGroup = await (from HG in _readOnlyDbContext.HealthFundGroups
                                                     where HG.Id == hfId    
                                                     select new HealthFundGroup()
                                                     {
                                                         Id = HG.Id,
                                                         Name = HG.Name,
                                                         StatusId = HG.StatusId,
                                                         ModifiedDate = HG.ModifiedDate,
                                                         ModifiedBy = HG.ModifiedBy
                                                        
                                                     }).FirstOrDefaultAsync();
            return healthFundGroup;
        }

        public async Task<HealthFundGroup> GetHFForDelete(int hfId)
        {
            HealthFundGroup healthFundGroup = await (from HG in _readOnlyDbContext.HealthFundGroups
                                                     where HG.Id == hfId && HG.StatusId == (short)Status.Active
                                                     select new HealthFundGroup()
                                                     {
                                                         Id = HG.Id,
                                                         Name = HG.Name,
                                                         StatusId = HG.StatusId,
                                                         ModifiedDate = HG.ModifiedDate,
                                                         ModifiedBy = HG.ModifiedBy,
                                                         GroupToHealthFundAssocs = (from assoc in _readOnlyDbContext.GroupToHealthFundAssocs.Where(x => x.HealthFundGroupsId == HG.Id && x.StatusId == (short)Status.Active)
                                                                                    select assoc).ToList()
                                                     }).FirstOrDefaultAsync();
            return healthFundGroup;
        }


        public async Task<int> EditHf(HealthFundGroup inputHealthFund)
        {
            _updatableDBContext.HealthFundGroups.Update(inputHealthFund);
            return await _updatableDBContext.SaveChangesAsync();
        }

        public async  Task<List<GroupToHealthFundAssoc>> GetHFBAssocByMultipleIds(List<int> assocIds)
        {
            return await _readOnlyDbContext.GroupToHealthFundAssocs
              .Where(s => assocIds.Contains(s.Id) && s.StatusId == (short)Status.Active)
              .ToListAsync();
        }

        public async Task<int> EditGroupHealthFundAssocs(List<GroupToHealthFundAssoc> dbAssocs)
        {
            _updatableDBContext.GroupToHealthFundAssocs.UpdateRange(dbAssocs);
            return await _updatableDBContext.SaveChangesAsync();
        }

        public async Task<QueryResultList<HealthFundDataList>> GetHealthFundDataDAL(QueryModel queryModel, HealthFundInput filterModel)
        {
            List<HealthFundDataList> paginatedList = new();
            int totalCount = 0;
           
            HFDataParticipantIdAssoc query = await (from HFP in _readOnlyDbContext.HFDataParticipantIdAssocs
                                                    where HFP.EffectiveDate <= filterModel.DateofService.Date && HFP.ParticipantId == filterModel.HFName 
                                                    && HFP.StatusId == (short)Status.Active && (HFP.StateId == null || HFP.StateId == filterModel.StateId)
                                                    orderby HFP.EffectiveDate descending
                                                    select HFP).FirstOrDefaultAsync();

            if (query is not null)
            {
                IQueryable<HealthFundDataList> healthFundList = from HFD in _readOnlyDbContext.HealthFundData
                                                                where HFD.EffectiveDate == query.EffectiveDate && HFD.HFUploadsId == query.HFUploadsId
                                                                && filterModel.ItemNums.Contains(HFD.ItemNum)
                                                                select new HealthFundDataList
                                                                {
                                                                    Id = HFD.Id,
                                                                    ItemNum = HFD.ItemNum,
                                                                    KnownGap = HFD.KnownGap,
                                                                    KnownGapPercentage = HFD.KnownGapPercentage,
                                                                    NoGap = HFD.NoGap,
                                                                    NoGapPercentage = HFD.NoGapPercentage,
                                                                    StateId = HFD.StateId,
                                                                    EffectiveDate = HFD.EffectiveDate,
                                                                    HealthFundGroupsId = HFD.HealthFundGroupsId
                                                                };
                totalCount = healthFundList.Count();

                 paginatedList = await CreatePaginateList(healthFundList, queryModel);
            }

            QueryResultList<HealthFundDataList> queryList = new QueryResultList<HealthFundDataList>();
            if (totalCount > 0)
            {

                queryList.ItemRecords = paginatedList;
                queryList.PageNumber = queryModel.PageNumber;
                queryList.PageSize = queryModel.PageSize;
                queryList.CurrentCount = paginatedList.Count();

            }
            else
            {
                queryList.ItemRecords = null;
                queryList.CurrentCount = 0;

            }
            queryList.TotalCount = totalCount;
            queryList.PageNumber = queryModel.PageNumber;
            queryList.PageSize = queryModel.PageSize;
            return queryList;

        }

        private async Task<List<HealthFundDataList>> CreatePaginateList(IQueryable<HealthFundDataList> hfQuery, QueryModel queryModel)
        {
            if (queryModel.PageNumber > 0 && queryModel.PageSize > 0)
            {
                if (hfQuery.Any())
                {

                    List<HealthFundDataList> paginatedList = await hfQuery.Skip((queryModel.PageNumber - 1) * queryModel.PageSize)
                 .Take(queryModel.PageSize).ToListAsync();

                    return paginatedList;

                }

            }
            return null;
        }

        /// <summary>
        /// Method to check if the Health Fund Group name exists in the database
        /// </summary>
        /// <param name="search_term"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<bool> CheckHFGroupNameDAL(string search_term)
        {
            var name = await _readOnlyDbContext.HealthFundGroups
                   .Where(p => p.Name.Equals(search_term) && p.StatusId == (short)Status.Active).FirstOrDefaultAsync();
            if (name == null)
                return false;
            else
                return true;
        }

        /// <summary>
        /// Method to check if the Health Fund exists in the database
        /// </summary>
        /// <param name="search_term"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<bool> CheckHFNameDAL(string search_term)
        {
            var name = await _readOnlyDbContext.GroupToHealthFundAssocs
                   .Where(p => p.ParticipantId.Equals(search_term) && p.StatusId == (short)Status.Active).FirstOrDefaultAsync();
            if (name == null)
                return false;
            else
                return true;
        }


    }
}
