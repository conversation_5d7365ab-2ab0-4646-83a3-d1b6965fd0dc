﻿using Capstone2.Framework.Business.Common;
using Capstone2.Framework.RestApi;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.Master.Common;
using Capstone2.RestServices.Master.Context;
using Capstone2.RestServices.Master.Interfaces;
using Capstone2.RestServices.Master.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Master.Services
{
    public class MasterBAL : IMasterBAL
    {
        public ReadOnlyMasterDBContext _ReadOnlyDbContext;
        public IMasterDAL _MasterDAL;
        private IDistributedCacheHelper _redisCache;
        private readonly ILogger<MasterBAL> _logger;
        public readonly AppSettings _appSettings;

        public MasterBAL(ReadOnlyMasterDBContext readOnlyDbContext, IDistributedCacheHelper cache, IMasterDAL masterDAL, ILogger<MasterBAL> logger, IOptions<AppSettings> appSettings)
        {
            _ReadOnlyDbContext = readOnlyDbContext;
            _MasterDAL = masterDAL;
            _redisCache = cache;
            _logger = logger;
            _appSettings = appSettings.Value;
        }

        public async Task<ApiResponse<List<CountryMaster>>> GetCountries(string search_term)
        {
            List<CountryMaster> responseData = await _redisCache.GetFromCache<List<CountryMaster>>($"{CachedKeys.Tbl_CountryMaster}");
            if (responseData == null)
            {
                responseData = await _MasterDAL.GetCountriesDAL();
                await _redisCache.SetIntoCache(responseData, $"{CachedKeys.Tbl_CountryMaster}");
            }

            if (!string.IsNullOrWhiteSpace(search_term))
                responseData = responseData.Where(p => p.CountryName.StartsWith(search_term, StringComparison.OrdinalIgnoreCase)).ToList();

            var apiResponse = new ApiResponse<List<CountryMaster>>
            {
                Result = responseData,
                Message = "Success",
                StatusCode = StatusCodes.Status200OK
            };
            return apiResponse;
        }

        public async Task<ApiResponse<List<StateMaster>>> GetStatesBAL(string search_term, short countryId)
        {
            List<StateMaster> responseData = await _redisCache.GetFromCache<List<StateMaster>>($"{CachedKeys.Tbl_StateMaster}");
            if (responseData == null)
            {
                responseData = await _MasterDAL.GetStatesDAL();
                await _redisCache.SetIntoCache(responseData, $"{CachedKeys.Tbl_StateMaster}");
            }

            if (search_term == null)
                responseData = responseData.Where(p => p.CountryID == countryId).ToList();
            else
                responseData = responseData.Where(p => p.CountryID == countryId).Where(p => p.StateFullName.StartsWith(search_term, StringComparison.OrdinalIgnoreCase) || p.StateShortName.StartsWith(search_term, StringComparison.OrdinalIgnoreCase)).ToList();

            var apiResponse = new ApiResponse<List<StateMaster>>
            {
                Result = responseData,
                Message = "Success",
                StatusCode = StatusCodes.Status200OK
            };
            return apiResponse;
        }

        public async Task<ApiResponse<List<LanguageMaster>>> GetLanguagesBAL(QueryModel queryModel)
        {
            List<LanguageMaster> responseData = await _redisCache.GetFromCache<List<LanguageMaster>>($"{CachedKeys.Tbl_LanguageMaster}");
            if (responseData == null)
            {
                responseData = await _MasterDAL.GetLangaguesDAL();
                await _redisCache.SetIntoCache(responseData, $"{CachedKeys.Tbl_LanguageMaster}");
            }

            if (queryModel.SearchTerm == null)
                responseData = responseData.OrderBy(p => p.LanguageName).ToList();
            else if (queryModel.SortOrder == "asc")
                responseData = responseData.Where(p => p.LanguageName.StartsWith(queryModel.SearchTerm, StringComparison.OrdinalIgnoreCase)).OrderBy(p => p.LanguageName).ToList();
            else
                responseData = responseData.Where(p => p.LanguageName.StartsWith(queryModel.SearchTerm, StringComparison.OrdinalIgnoreCase)).OrderByDescending(p => p.LanguageName).ToList();

            var apiResponse = new ApiResponse<List<LanguageMaster>>
            {
                Result = responseData,
                Message = "Success",
                StatusCode = StatusCodes.Status200OK
            };
            return apiResponse;
        }

        public async Task<ApiResponse<List<TimeZoneMaster>>> GetTimeZonesBAL(QueryModel queryModel)
        {
            List<TimeZoneMaster> responseData = await _redisCache.GetFromCache<List<TimeZoneMaster>>($"{CachedKeys.Tbl_TimeZoneMaster}");
            if (responseData == null)
            {
                responseData = await _MasterDAL.GetTimeZonesDAL();
                await _redisCache.SetIntoCache(responseData, $"{CachedKeys.Tbl_TimeZoneMaster}");
            }

            if (queryModel.SearchTerm == null)
                responseData = responseData.OrderBy(p => p.TimeZoneName).ToList();
            else if (queryModel.SortOrder == "asc")
                responseData = responseData.Where(p => p.TimeZoneName.StartsWith(queryModel.SearchTerm, StringComparison.OrdinalIgnoreCase)).OrderBy(p => p.TimeZoneName).ToList();
            else
                responseData = responseData.Where(p => p.TimeZoneName.StartsWith(queryModel.SearchTerm, StringComparison.OrdinalIgnoreCase)).OrderByDescending(p => p.TimeZoneName).ToList();

            var apiResponse = new ApiResponse<List<TimeZoneMaster>>
            {
                Result = responseData,
                Message = "Success",
                StatusCode = StatusCodes.Status200OK
            };
            return apiResponse;
        }

        public async Task<ApiResponse<List<NationalityMaster>>> GetNationalitiesBAL(QueryModel queryModel)
        {
            List<NationalityMaster> responseData = await _redisCache.GetFromCache<List<NationalityMaster>>($"{CachedKeys.Tbl_NationalityMaster}");
            if (responseData == null)
            {
                responseData = await _MasterDAL.GetNationalitiesDAL();
                await _redisCache.SetIntoCache(responseData, $"{CachedKeys.Tbl_NationalityMaster}");
            }

            if (queryModel.SearchTerm == null)
                responseData = responseData.OrderBy(p => p.Nationality).ToList();
            else if (queryModel.SortOrder == "asc")
                responseData = responseData.Where(p => p.Nationality.StartsWith(queryModel.SearchTerm, StringComparison.OrdinalIgnoreCase)).OrderBy(p => p.Nationality).ToList();
            else
                responseData = responseData.Where(p => p.Nationality.StartsWith(queryModel.SearchTerm, StringComparison.OrdinalIgnoreCase)).OrderByDescending(p => p.Nationality).ToList();

            var apiResponse = new ApiResponse<List<NationalityMaster>>
            {
                Result = responseData,
                Message = "Success",
                StatusCode = StatusCodes.Status200OK
            };
            return apiResponse;
        }

        public async Task<ApiResponse<List<SystemConstant>>> GetSystemConstantsBAL(string type)
        {
            List<SystemConstant> responseData = await _redisCache.GetFromCache<List<SystemConstant>>($"{CachedKeys.Tbl_SystemConstant}");
            if (responseData == null)
            {
                responseData = await _MasterDAL.GetSystemConstantsDAL();
                await _redisCache.SetIntoCache(responseData, $"{CachedKeys.Tbl_SystemConstant}");
            }

            if (!string.IsNullOrWhiteSpace(type))
                responseData = responseData.Where(p => p.Type.StartsWith(type, StringComparison.OrdinalIgnoreCase) && p.Status.Equals(true)).ToList();

            var apiResponse = new ApiResponse<List<SystemConstant>>
            {
                Result = responseData,
                Message = "Success",
                StatusCode = StatusCodes.Status200OK
            };
            return apiResponse;
        }

        public async Task<ApiResponse<List<DataFieldsView>>> GetDataFielsBAL(QueryModel queryModel)
        {
            DataFieldsFilter filterModel = PrepareFilterParameters(queryModel.Filter);
            List<DataFieldsView> responseData = await _redisCache.GetFromCache<List<DataFieldsView>>($"{CachedKeys.Tbl_DataFields}");

            if (responseData == null)
            {
                responseData = await _MasterDAL.GetDataFielsDAL();
                await _redisCache.SetIntoCache(responseData, $"{CachedKeys.Tbl_DataFields}");
            }

            if (filterModel != null && filterModel.TemplateTypeId != "")
            {
                responseData = responseData.Where(x => !string.IsNullOrEmpty(x.TemplateTypeIds) && x.TemplateTypeIds.Split(',').Contains(filterModel.TemplateTypeId)).ToList();
            }
            responseData = responseData.Where(x => x.DataFieldName.Contains(!string.IsNullOrWhiteSpace(queryModel.SearchTerm) ? queryModel.SearchTerm : x.DataFieldName, StringComparison.OrdinalIgnoreCase)).ToList();
            var apiResponse = new ApiResponse<List<DataFieldsView>>
            {
                Result = responseData,
                Message = "Success",
                StatusCode = StatusCodes.Status200OK
            };
            return apiResponse;
        }

        private DataFieldsFilter PrepareFilterParameters(string filter)
        {
            if (!(String.IsNullOrEmpty(filter)))
            {
                return JsonConvert.DeserializeObject<DataFieldsFilter>(filter);
            }
            else
            {
                return null;
            }
        }
        private MedicareErrorsFilter PrepareMedicareErrorsFilterParameters(string filter)
        {
            if (!(String.IsNullOrEmpty(filter)))
            {
                return JsonConvert.DeserializeObject<MedicareErrorsFilter>(filter);
            }
            else
            {
                return null;
            }
        }
        public async Task<ApiResponse<List<RolesCategory>>> GetRolesCategoriesBAL()
        {
            List<RolesCategory> responseData = null;
            //await _redisCache.GetFromCache<List<RolesCategory>>($"{CachedKeys.Tbl_RolesCategories}");
            if (responseData == null)
            {
                responseData = await _MasterDAL.GetRolesCategoriesDAL();
                //await _redisCache.SetIntoCache(responseData, $"{CachedKeys.Tbl_RolesCategories}");
            }

            responseData = responseData.ToList();

            var apiResponse = new ApiResponse<List<RolesCategory>>
            {
                Result = responseData,
                Message = "Success",
                StatusCode = StatusCodes.Status200OK
            };
            return apiResponse;
        }

        public async Task<ApiResponse<List<RolesPermission>>> GetRolesPermissionsBAL()
        {
            List<RolesPermission> responseData = null;
            //await _redisCache.GetFromCache<List<RolesPermission>>($"{CachedKeys.Tbl_RolesPermissions}");
            if (responseData == null)
            {
                responseData = await _MasterDAL.GetRolesPermissionsDAL();
                //await _redisCache.SetIntoCache(responseData, $"{CachedKeys.Tbl_RolesPermissions}");
            }

            responseData = responseData.ToList();

            var apiResponse = new ApiResponse<List<RolesPermission>>
            {
                Result = responseData,
                Message = "Success",
                StatusCode = StatusCodes.Status200OK
            };
            return apiResponse;
        }

        /// <summary>
        /// Get MedicareParticipants
        /// </summary>
        /// <returns></returns>
        public async Task<ApiResponse<List<MedicareParticipants>>> GetMedicareParticipants(QueryModel queryModel)
        {
            List<MedicareParticipants> responseData = null;

            if (responseData == null && queryModel.Filter is null)
            {
                responseData = await _MasterDAL.GetMedicareParticipants();
            }
            else
            {
                MedParticipantsFilterModel filterModel = FilterParametersForParticipants(queryModel.Filter);
                responseData = await _MasterDAL.GetParticipantsWithFilter(queryModel, filterModel);
            }

            responseData = responseData.ToList();

            var apiResponse = new ApiResponse<List<MedicareParticipants>>
            {
                Result = responseData,
                Message = "Success",
                StatusCode = StatusCodes.Status200OK
            };
            return apiResponse;
        }


        private MedParticipantsFilterModel FilterParametersForParticipants(string filter)
        {
            if (!(String.IsNullOrEmpty(filter)))
            {
                return JsonConvert.DeserializeObject<MedParticipantsFilterModel>(filter);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// Add MedicareParticipants
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="participants"></param>
        /// <returns></returns>
        public async Task<ApiResponse<int>> AddMedicareParticipants(BaseHttpRequestContext baseHttpRequestContext, ICollection<MedicareParticipants> participants)
        {
            var orgId = baseHttpRequestContext.OrgId;
            var userId = baseHttpRequestContext.UserId;

            participants.ToList().ForEach(a => { a.OrgId = orgId; a.CreatedBy = userId; a.CreatedDate = DateTime.UtcNow; a.StatusId = (short)Status.Active; });

            var response = await _MasterDAL.AddMedicareParticipants(participants);

            var apiResponse = new ApiResponse<int>
            {
                Result = response,
                Message = "Success",
                StatusCode = StatusCodes.Status200OK
            };
            return apiResponse;
        }

        /// <summary>
        /// Update MedicareParticipants
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="participants"></param>
        /// <returns></returns>
        public async Task<ApiResponse<int>> UpdateMedicareParticipants(BaseHttpRequestContext baseHttpRequestContext, ICollection<MedicareParticipants> participants)
        {
            var orgId = baseHttpRequestContext.OrgId;
            var userId = baseHttpRequestContext.UserId;

            var payload = await MapMedicareParticipants(participants, orgId, userId);
            var response = await _MasterDAL.UpdateAccountHolderData(payload);

            var apiResponse = new ApiResponse<int>
            {
                Result = response,
                Message = "Success",
                StatusCode = StatusCodes.Status200OK
            };
            return apiResponse;
        }

        private async Task<ICollection<MedicareParticipants>> MapMedicareParticipants(ICollection<MedicareParticipants> participants, int orgId, long userId)
        {
            List<MedicareParticipants> addControlList = new();

            var removeUserList = await _MasterDAL.GetMedicareParticipants();

            foreach (var control in participants)
            {
                if (control.Id > 0)
                {
                    var existingobj = removeUserList.FirstOrDefault(x => x.Id == control.Id);
                    removeUserList.Remove(existingobj);
                    control.ModifiedDate = DateTime.UtcNow;
                    control.ModifiedBy = userId;
                    control.StatusId = (short)Status.Active;
                    addControlList.Add(control);
                }
                else
                {
                    var userAssocDB = removeUserList.Where(x => x.ParticipantId == control.ParticipantId).FirstOrDefault();
                    if (userAssocDB is null)
                    {
                        control.CreatedDate = DateTime.UtcNow;
                        control.CreatedBy = userId;
                        control.StatusId = (short)Status.Active;
                        addControlList.Add(control);
                    }
                    else
                    {
                        if (userAssocDB.StatusId == (short)Status.Inactive)
                        {
                            userAssocDB.StatusId = (short)Status.Active;
                            userAssocDB.ModifiedDate = DateTime.UtcNow;
                            userAssocDB.ModifiedBy = userId;
                            addControlList.Add(userAssocDB);
                        }
                        removeUserList.Remove(userAssocDB);
                    }
                }
            }

            foreach (var userAssoc in removeUserList.Where(x => x.StatusId == (short)Status.Active))
            {
                userAssoc.StatusId = (short)Status.Inactive;
                userAssoc.ModifiedDate = DateTime.UtcNow;
                userAssoc.ModifiedBy = userId;
            }

            addControlList = addControlList.Concat(removeUserList).ToList();
            return addControlList;
        }

        /// <summary>
        /// Remove cached data from Redis cache using cackedKy
        /// </summary>
        /// <param name="cachekey"></param>
        /// <returns></returns>
        public async Task RemoveCache(string cachekey)
        {
            await _redisCache.RemoveCache(cachekey);
        }

        public async Task<ApiResponse<List<MedicareConstant>>> GeMedicareConstants(string type)
        {
            List<MedicareConstant> responseData = await _redisCache.GetFromCache<List<MedicareConstant>>($"{CachedKeys.Tbl_MedicareConstants}");
            if (responseData == null || responseData.Count == 0)
            {
                responseData = await _MasterDAL.GeMedicareConstants();
                await _redisCache.SetIntoCache(responseData, $"{CachedKeys.Tbl_MedicareConstants}");
            }

            if (!string.IsNullOrWhiteSpace(type))
                responseData = responseData.Where(p => p.Type.StartsWith(type, StringComparison.OrdinalIgnoreCase) && p.Status.Equals(true)).ToList();

            var apiResponse = new ApiResponse<List<MedicareConstant>>
            {
                Result = responseData,
                Message = "Success",
                StatusCode = StatusCodes.Status200OK
            };
            return apiResponse;
        }

        /// <summary>
        /// Get MedicareErrors
        /// </summary>
        /// <returns></returns>
        public async Task<ApiResponse<List<MedicareErrors>>> GetMedicareErrors(short type, QueryModel queryModel)
        {
            MedicareErrorsFilter filterModel = PrepareMedicareErrorsFilterParameters(queryModel.Filter);
            List<MedicareErrors> responseData = await _redisCache.GetFromCache<List<MedicareErrors>>($"{CachedKeys.Tbl_MedicareErrors}");
            if (responseData == null || responseData.Count == 0)
            {
                responseData = await _MasterDAL.GetMedicareErrors();
                await _redisCache.SetIntoCache(responseData, $"{CachedKeys.Tbl_MedicareErrors}");
            }
            if (type != null)
                responseData = responseData.Where(p => p.ErrorTypeId == type).ToList();
            if (filterModel != null && filterModel.ErrorCodes.Any())
            {
                responseData = responseData.Where(x => filterModel.ErrorCodes.Contains(x.ErrorCode)).ToList();
            }
            responseData = responseData.Where(x => x.ErrorDescription.Contains(!string.IsNullOrWhiteSpace(queryModel.SearchTerm) ? queryModel.SearchTerm : x.ErrorDescription, StringComparison.OrdinalIgnoreCase)).ToList();

            var apiResponse = new ApiResponse<List<MedicareErrors>>
            {
                Result = responseData,
                Message = "Success",
                StatusCode = StatusCodes.Status200OK
            };
            return apiResponse;
        }

        /// <summary>
        /// Get apipermissionsassocs
        /// </summary>
        /// <returns></returns>
        public async Task<ApiResponse<List<ApiPermissionsAssoc>>> GetApiPermissionsBAL(QueryModel queryModel)
        {
            List<ApiPermissionsAssoc> responseData = await _redisCache.GetFromCache<List<ApiPermissionsAssoc>>($"{CachedKeys.Tbl_ApiPermissionsAssocs}");
            if (responseData == null || responseData.Count == 0)
            {
                responseData = await _MasterDAL.GetApiPermissionsDAL();
                await _redisCache.SetIntoCache(responseData, $"{CachedKeys.Tbl_ApiPermissionsAssocs}");
            }

            var apiResponse = new ApiResponse<List<ApiPermissionsAssoc>>
            {
                Result = responseData,
                Message = "Success",
                StatusCode = StatusCodes.Status200OK
            };
            return apiResponse;
        }
        /// <summary>
        /// Method to list presentingillnesscodes
        /// </summary>
        /// <param name="search_term"></param>
        /// <returns></returns>
        public async Task<ApiResponse<List<PresentingIllnessCode>>> ListPresentingIllnessCodes(string searchTerm)
        {
            List<PresentingIllnessCode> responseData = await _redisCache.GetFromCache<List<PresentingIllnessCode>>($"{CachedKeys.Tbl_PresentingIllnessCodes}");
            if (responseData == null)
            {
                responseData = await _MasterDAL.GetPresentingIllnessCodes();
                await _redisCache.SetIntoCache(responseData, $"{CachedKeys.Tbl_PresentingIllnessCodes}");
            }

            if (!string.IsNullOrWhiteSpace(searchTerm))
                responseData = responseData.Where(p => p.Description.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) || p.Code.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)).ToList();

            var apiResponse = new ApiResponse<List<PresentingIllnessCode>>
            {
                Result = responseData,
                Message = "Success",
                StatusCode = StatusCodes.Status200OK
            };
            return apiResponse;
        }
        /// <summary>
        /// method to fech presenting illness record based on code
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        public async Task<ApiResponse<PresentingIllnessCode>> GetPresentingIllnessCodes(string code)
        {
            ApiResponse<PresentingIllnessCode> apiResponse = new();
            List<PresentingIllnessCode> responseData = await _redisCache.GetFromCache<List<PresentingIllnessCode>>($"{CachedKeys.Tbl_PresentingIllnessCodes}");
            if (responseData == null)
            {
                responseData = await _MasterDAL.GetPresentingIllnessCodes();
                await _redisCache.SetIntoCache(responseData, $"{CachedKeys.Tbl_PresentingIllnessCodes}");
            }
            if (!string.IsNullOrWhiteSpace(code))
            {
                PresentingIllnessCode presentingIllnessCode = responseData.Where(p => p.Code == code).FirstOrDefault();
                if (presentingIllnessCode is not null)
                {
                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    apiResponse.Result = presentingIllnessCode;
                    apiResponse.Message = "Success";
                    return apiResponse;
                }

            }
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            apiResponse.Result = null;
            apiResponse.Message = "Failure";
            apiResponse.Errors.Add("Data cannot be fetched with the request.");

            return apiResponse;

        }

        /// <summary>
        /// method to fech language by Id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<LanguageMaster>> GetLanguagesByIdBAL(long id)
        {
            List<LanguageMaster> responseData = await _redisCache.GetFromCache<List<LanguageMaster>>($"{CachedKeys.Tbl_LanguageMaster}");
            if (responseData == null)
            {
                responseData = await _MasterDAL.GetLangaguesDAL();
                await _redisCache.SetIntoCache(responseData, $"{CachedKeys.Tbl_LanguageMaster}");
            }

            LanguageMaster responseDataObj = responseData.Where(p => p.Id == id).FirstOrDefault();
            var apiResponse = new ApiResponse<LanguageMaster>
            {
                Result = responseDataObj,
                Message = "Success",
                StatusCode = StatusCodes.Status200OK
            };
            return apiResponse;
        }

        public async Task<ApiResponse<List<MedicalContractorBand>>> GetMedContBandBAL(QueryModel queryModel)
        {
            List<MedicalContractorBand> responseData = await _redisCache.GetFromCache<List<MedicalContractorBand>>($"{CachedKeys.Tbl_MedicalContractorBand}");
            if (responseData == null)
            {
                responseData = await _MasterDAL.GetMedContDAL();
                await _redisCache.SetIntoCache(responseData, $"{CachedKeys.Tbl_MedicalContractorBand}");
            }

            var apiResponse = new ApiResponse<List<MedicalContractorBand>>
            {
                Result = responseData,
                Message = "Success",
                StatusCode = StatusCodes.Status200OK
            };
            return apiResponse;
        }

        public async Task<ApiResponse<MedicareParticipants>> GetMedParticipantsById(int id)
        {
            MedicareParticipants responseData = null;
            if (responseData == null)
            {
                responseData = await _MasterDAL.GetMedicareParticipantsById(id);
            }

            var apiResponse = new ApiResponse<MedicareParticipants>
            {
                Result = responseData,
                Message = "Success",
                StatusCode = StatusCodes.Status200OK
            };
            return apiResponse;

        }

        public async Task<ApiResponse<List<SystemConstant>>> GetAllSystemConstantsBAL()
        {
            List<SystemConstant> responseData = await _redisCache.GetFromCache<List<SystemConstant>>($"{CachedKeys.Tbl_SystemConstant}");
            if (responseData == null)
            {
                responseData = await _MasterDAL.GetSystemConstantsDAL();
                await _redisCache.SetIntoCache(responseData, $"{CachedKeys.Tbl_SystemConstant}");
            }

            var apiResponse = new ApiResponse<List<SystemConstant>>
            {
                Result = responseData,
                Message = "Success",
                StatusCode = StatusCodes.Status200OK
            };
            return apiResponse;
        }

        public async Task<ApiResponse<List<MedicareConstant>>> GetAllMedicareConstants()
        {
            List<MedicareConstant> responseData = await _redisCache.GetFromCache<List<MedicareConstant>>($"{CachedKeys.Tbl_MedicareConstants}");
            if (responseData == null || responseData.Count == 0)
            {
                responseData = await _MasterDAL.GeMedicareConstants();
                await _redisCache.SetIntoCache(responseData, $"{CachedKeys.Tbl_MedicareConstants}");
            }


            var apiResponse = new ApiResponse<List<MedicareConstant>>
            {
                Result = responseData,
                Message = "Success",
                StatusCode = StatusCodes.Status200OK
            };
            return apiResponse;
        }

        public async Task<ApiResponse<string>> GetAddressBAL(BaseHttpRequestContext baseHttpRequestContext, string searchtearm)
        {
            ApiResponse<string> apiResponse = new();
            string searchTerm = searchtearm;
            string baseUrl = _appSettings.AddressFinderUrl + "/autocomplete/";
            Uri uri = new Uri(baseHttpRequestContext.Referer);
            string hostName = uri.Host;


            // Query parameters
            string parameter1 = "q";
            string value1 = searchTerm;
            string parameter2 = "format";
            string value2 = "json";
            string parameter3 = "key";
            string value3 = _appSettings.AddressFinderKey;
            string parameter4 = "domain";
            string value4 = hostName;

            // Create a UriBuilder
            var uriBuilder = new UriBuilder(baseUrl);

            // Parse existing query parameters (if any)
            var query = Uri.EscapeUriString(uriBuilder.Query);
            if (query.Length > 1) // Check if there are existing parameters
            {
                query = query.Substring(1); // Remove the leading '?'
            }

            // Add or update query parameters
            query = AddOrUpdateParameter(query, parameter1, value1);
            query = AddOrUpdateParameter(query, parameter2, value2);
            query = AddOrUpdateParameter(query, parameter3, value3);
            query = AddOrUpdateParameter(query, parameter4, value4);

            // Set the updated query back to the UriBuilder
            uriBuilder.Query = query;

            // Get the final URL
            string finalUrl = uriBuilder.ToString();

            //Add headers
            Dictionary<string, string> headers = new Dictionary<string, string>();

            // Add key-value pairs to the dictionary
            headers.Add("Authorization", _appSettings.AddressFinderAuth);

            headers.Add("Referer", hostName);


            string addressFinderApiUrl = finalUrl;
            RestClient restClient = new RestClient(addressFinderApiUrl, headers);
            var httpResponse = await restClient.GetAsync<dynamic>(addressFinderApiUrl);
            /* apiResponse.Result = new { completions = new List<dynamic>(), paid = false, demo = true, success = false };
             apiResponse.Result = AssignValues(apiResponse.Result, httpResponse);   */
            apiResponse.Result = JsonConvert.SerializeObject(httpResponse, Formatting.Indented);
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            return apiResponse;

        }

        // Helper method to add or update a query parameter
        public string AddOrUpdateParameter(string queryString, string key, string value)
        {
            var keyValue = $"{Uri.EscapeDataString(key)}={Uri.EscapeDataString(value)}";

            // Check if the key already exists in the query string
            if (queryString.Contains(key))
            {
                // Update the existing value
                var regex = new System.Text.RegularExpressions.Regex($"{Uri.EscapeDataString(key)}=[^&]*");
                queryString = regex.Replace(queryString, keyValue);
            }
            else
            {
                // Add the new key-value pair
                if (queryString.Length > 0)
                {
                    queryString += "&";
                }
                queryString += keyValue;
            }

            return queryString;
        }


        public async Task<ApiResponse<string>> GetAddressByIdBAL(BaseHttpRequestContext baseHttpRequestContext, string addId)
        {
            ApiResponse<string> apiResponse = new();
            string baseUrl = _appSettings.AddressFinderUrl + "/metadata/";
            Uri uri = new Uri(baseHttpRequestContext.Referer);
            string hostName = uri.Host;

            // Query parameters
            string parameter1 = "id";
            string value1 = addId;
            string parameter2 = "format";
            string value2 = "json";
            string parameter3 = "key";
            string value3 = _appSettings.AddressFinderKey;
            string parameter4 = "domain";
            string value4 = hostName;

            // Create a UriBuilder
            var uriBuilder = new UriBuilder(baseUrl);

            // Parse existing query parameters (if any)
            var query = Uri.EscapeUriString(uriBuilder.Query);
            if (query.Length > 1) // Check if there are existing parameters
            {
                query = query.Substring(1); // Remove the leading '?'
            }

            // Add or update query parameters
            query = AddOrUpdateParameter(query, parameter1, value1);
            query = AddOrUpdateParameter(query, parameter2, value2);
            query = AddOrUpdateParameter(query, parameter3, value3);
            query = AddOrUpdateParameter(query, parameter4, value4);

            // Set the updated query back to the UriBuilder
            uriBuilder.Query = query;

            // Get the final URL
            string finalUrl = uriBuilder.ToString();

            //Add headers
            Dictionary<string, string> headers = new Dictionary<string, string>();

            // Add key-value pairs to the dictionary
            headers.Add("Authorization", _appSettings.AddressFinderAuth);
            headers.Add("Referer", hostName);


            string addressFinderApiUrl = finalUrl;
            RestClient restClient = new RestClient(addressFinderApiUrl, headers);
            var httpResponse = await restClient.GetAsync<dynamic>(addressFinderApiUrl);
            /* apiResponse.Result = new { completions = new List<dynamic>(), paid = false, demo = true, success = false };
             apiResponse.Result = AssignValues(apiResponse.Result, httpResponse);   */
            apiResponse.Result = JsonConvert.SerializeObject(httpResponse, Formatting.Indented);
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            return apiResponse;

        }

        public async Task<ApiResponse<List<MedicareParticipantsInfo>>> GetMedicareParticipantsByParticipantIds(string strParticipantIds)
        {
            var apiResponse = new ApiResponse<List<MedicareParticipantsInfo>>();
            if (!string.IsNullOrWhiteSpace(strParticipantIds))
            {
                var listParticintId = strParticipantIds.Split(',').ToList();
                List<MedicareParticipantsInfo> lstMP = await _MasterDAL.GetMedicareParticipantsByParticipantIds(listParticintId);
                apiResponse.Result = lstMP;
                apiResponse.StatusCode = StatusCodes.Status200OK;
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = null;
                apiResponse.Errors.Add("Medicare Participants cannot be fetched at this time.");
            }
            return apiResponse;
        }
    }

}