﻿using Capstone2.Framework.Business.Common;
using Capstone2.Framework.RestApi;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.Master.Context;
using Capstone2.RestServices.Master.Interfaces;
using Capstone2.RestServices.Master.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Transactions;

namespace Capstone2.RestServices.Master.Services
{
    public class HealthFundBAL : IHealthFundBAL
    {
        public ReadOnlyMasterDBContext _ReadOnlyDbContext;
        public IHealthFundDAL _healthFundDAL;
        private IDistributedCacheHelper _redisCache;
        private readonly ILogger<MasterBAL> _logger;
        public readonly AppSettings _appSettings;

        public HealthFundBAL(ReadOnlyMasterDBContext readOnlyDbContext, IDistributedCacheHelper cache, IHealthFundDAL healthFundDAL, ILogger<MasterBAL> logger, IOptions<AppSettings> appSettings)
        {
            _ReadOnlyDbContext = readOnlyDbContext;
            _healthFundDAL = healthFundDAL;
            _redisCache = cache;
            _logger = logger;
            _appSettings = appSettings.Value;
        }

        /// <summary>
        /// Method to save a entry in HealthFundGroup
        /// </summary>
        /// <param name="inputhealthFund"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<int?>> AddHFGroup(HealthFundGroup inputhealthFund, BaseHttpRequestContext baseHttpRequestContext)
        {
           // var orgId = baseHttpRequestContext.OrgId;
            var loggedInUser = baseHttpRequestContext.UserId;

            inputhealthFund.ModifiedBy = loggedInUser;
            inputhealthFund.ModifiedDate = DateTime.UtcNow;
            inputhealthFund.StatusId = (short)Status.Active;
           


            var returnid = await _healthFundDAL.AddHFGroupDAL(inputhealthFund);
            if (returnid > 0)
            {
                ApiResponse<int?> apiResponse = new()
                {
                    StatusCode = StatusCodes.Status200OK,
                    Message = "Success",
                    Result = returnid
                };
                return apiResponse;
            }
            else
            {
                ApiResponse<int?> apiResponse = new()
                {
                    StatusCode = StatusCodes.Status400BadRequest,
                    Message = "Failure",
                    Result = null
                };
                return apiResponse;
            }
        }

        public async Task<ApiResponse<int?>> AddGroupToHFAssocsBAL(int groupId, List<GroupToHealthFundAssoc> inputHFAssocs, BaseHttpRequestContext baseHttpRequestContext)

        {
            ApiResponse<int?> apiResposne = new();
            var userId = baseHttpRequestContext.UserId;
            var orgId = baseHttpRequestContext.OrgId;
    
           
            foreach (var HfAssoc in inputHFAssocs)
            {

                HfAssoc.ModifiedBy = userId;
                HfAssoc.StatusId = (short) Status.Active;
                HfAssoc.HealthFundGroupsId = groupId;
                HfAssoc.ModifiedDate = DateTime.UtcNow;
            }

                int id = await _healthFundDAL.AddGroupToHFAssocsDAL(inputHFAssocs);

            if (id > 0)
            {
                apiResposne.StatusCode = StatusCodes.Status200OK;
                apiResposne.Result = id;
                apiResposne.Message = "Success";
            }
            else
            {
                apiResposne.StatusCode = StatusCodes.Status400BadRequest;
                apiResposne.Result = null;
                apiResposne.Message = "Failure";
                apiResposne.Errors.Add("GroupToHealthFundAssocs cannot be added at this time.Please try again later.");
            }
            return apiResposne;
        }

        public async Task<ApiResponse<QueryResultList<HealthFundGroup>>> GetHealthFundGroupsBAL(BaseHttpRequestContext baseHttpRequestContext, QueryModel queryModel)
        {
            ApiResponse<QueryResultList<HealthFundGroup>> apiResponse = new();
            apiResponse.Result = new();
            List<HealthFundGroup> groupList = await _healthFundDAL.GetHealthFundGroupsDAL();

            if(groupList is null ) {

                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "List of Health Fund Group not found";
                apiResponse.Result.PageNumber = queryModel.PageNumber;
                apiResponse.Result.PageSize = queryModel.PageSize;
                apiResponse.Result.TotalCount = 0;
                apiResponse.Result.ItemRecords = null;
                return apiResponse;


            }

            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result.PageNumber = queryModel.PageNumber;
            apiResponse.Result.PageSize = queryModel.PageSize;
            apiResponse.Result.TotalCount = groupList.Count;
            apiResponse.Result.ItemRecords = groupList;
            return apiResponse;

        }

        public async Task<ApiResponse<int?>> EditHealthFundGroupBAL(int hfId, HealthFundGroup inputHealthFund, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<int?> apiResponse = new();
            var userId = baseHttpRequestContext.UserId;
            var orgId = baseHttpRequestContext.OrgId;

            var hfFromDB = await _healthFundDAL.GetHFForEdit(hfId);
         
            if (hfFromDB == null)
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failed";
                apiResponse.Errors.Add("Medical Contractor not found");
                return apiResponse;
            }

            inputHealthFund.ModifiedBy = userId;
            inputHealthFund.ModifiedDate = DateTime.UtcNow;
            inputHealthFund.Id = hfFromDB.Id;

            int returnId = await _healthFundDAL.EditHf(inputHealthFund);

            if (returnId > 0)
            {
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Result = returnId;
                apiResponse.Message = "Success";
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = null;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("HealthfundGroup cannot be Edited at this time.Please try again later.");
            }

           return apiResponse;

        }

        public async Task<ApiResponse<int?>> DeleteHFGroupBAL(int hfId, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<int?> apiResponse = new();
            var userId = baseHttpRequestContext.UserId;
            var orgId = baseHttpRequestContext.OrgId;

            var hfFromDB = await _healthFundDAL.GetHFForDelete(hfId);

            if (hfFromDB == null)
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failed";
                apiResponse.Errors.Add("Active Health Fund Group Not found");
                return apiResponse;
            }

            hfFromDB.ModifiedBy = userId;
            hfFromDB.ModifiedDate = DateTime.UtcNow;
            hfFromDB.StatusId = (short)Status.Deleted;

            foreach (var assoc in hfFromDB.GroupToHealthFundAssocs)
            {
                assoc.StatusId = (short)Status.Deleted;
                assoc.ModifiedBy = userId;
                assoc.ModifiedDate = DateTime.UtcNow;
            }

           // int returnId = await _healthFundDAL.EditHf(hfFromDB);

            using (var transaction = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
            {
                int returnId = await _healthFundDAL.EditHf(hfFromDB);
                int groupreturnid = await _healthFundDAL.EditGroupHealthFundAssocs(hfFromDB.GroupToHealthFundAssocs.ToList());

                transaction.Complete();
                apiResponse.Result = returnId;
               
            }



            if (apiResponse.Result > 0)
            {
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = null;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("HealthfundGroup cannot be Deleted at this time.Please try again later.");
            }

            return apiResponse;

        }

        public async Task<ApiResponse<int?>> DeleteGHFAssocsBAL(int id, string ghfassocIds, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<int?> apiResponse = new();
            var userId = baseHttpRequestContext.UserId;
            var orgId = baseHttpRequestContext.OrgId;

            List<int> assocIds = ghfassocIds.Split(',').ToList().Select(s => int.Parse(s)).ToList();

            var dbAssocs = await _healthFundDAL.GetHFBAssocByMultipleIds(assocIds);
            if(dbAssocs is null || dbAssocs.Count <= 0)
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("No Active Records with these Ids");
                return apiResponse;
            }

            foreach (var assoc in dbAssocs)
            {
                assoc.StatusId = (short)Status.Deleted;
                assoc.ModifiedBy = userId;
                assoc.ModifiedDate = DateTime.UtcNow;
            }

            int returnid = await _healthFundDAL.EditGroupHealthFundAssocs(dbAssocs);

            if (returnid > 0)
            {
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Result = returnid;
                apiResponse.Message = "Success";
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = null;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("GroupHealthFundAssoc cannot be Deleted at this time.Please try again later.");
            }
            return apiResponse;

        }

        public async Task<ApiResponse<HealthFundGroup>> GetHealthFundGroupByIdBAL(int id, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<HealthFundGroup> apiResponse = new();
            apiResponse.Result = new();
            HealthFundGroup healthFund = await _healthFundDAL.GetHFForDelete(id);

            if (healthFund is null)
            {

                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("HealthFund cannot be Fetched at this time.Please try again later.");
                return apiResponse;


            }

            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result = healthFund;
            return apiResponse;
        }

       public async Task<ApiResponse<QueryResultList<HealthFundDataList>>> GetHealthFundDataBAL(BaseHttpRequestContext baseHttpRequestContext, QueryModel queryModel)
        {
            ApiResponse<QueryResultList<HealthFundDataList>> apiResponse = new();

            HealthFundInput filterModel = FilterParametersForHFdata(queryModel.Filter);
            if (filterModel is null)
            {
                apiResponse.Errors.Add("Filter is needed to get the list of Health Fund Data");
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failure";
                return apiResponse;
            }

            QueryResultList<HealthFundDataList> queryList = await _healthFundDAL.GetHealthFundDataDAL(queryModel, filterModel);

            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result = queryList;

            return apiResponse;
        }

        private HealthFundInput FilterParametersForHFdata(string filter)
        {
            if (!(String.IsNullOrEmpty(filter)))
            {
                return JsonConvert.DeserializeObject<HealthFundInput>(filter);
            }
            else
            {
                return null;
            }
        }

        public async Task<ApiResponse<bool>> CheckHFGroupNameBAL(string search_term, BaseHttpRequestContext baseHttpRequestContext)
        {
            bool nameBool = await _healthFundDAL.CheckHFGroupNameDAL(search_term);
            var apiResponse = new ApiResponse<bool>
            {
                Result = nameBool,
                Message = "Success",
                StatusCode = StatusCodes.Status200OK
            };
            return apiResponse;
        }

        public async Task<ApiResponse<bool>> CheckHFNameBAL(string search_term, BaseHttpRequestContext baseHttpRequestContext)
        {
            bool nameBool = await _healthFundDAL.CheckHFNameDAL(search_term);
            var apiResponse = new ApiResponse<bool>
            {
                Result = nameBool,
                Message = "Success",
                StatusCode = StatusCodes.Status200OK
            };
            return apiResponse;
        }


    }

}