﻿using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.InvoicePayment.Context;
using Capstone2.RestServices.InvoicePayment.Interfaces;
using Capstone2.RestServices.InvoicePayment.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Enum;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;

namespace Capstone2.RestServices.InvoicePayment.Services
{
    public class PaymentDAL : IPaymentDAL
    {
        public readonly ReadOnlyInvoicePaymentDBContext _readOnlyDbContext;
        public UpdatableInvoicePaymentDBContext _updatableDBContext;

        public PaymentDAL(ReadOnlyInvoicePaymentDBContext readOnlyDbContext, UpdatableInvoicePaymentDBContext updatableDBContext)
        {
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
        }
        /// <summary>
        /// Method to add payments to PaymentDetails table
        /// </summary>
        /// <param name="paymentDetailColl"></param>
        /// <returns></returns>
        public async Task<int> AddPaymentDetailsRange(ICollection<PaymentDetail> paymentDetailColl)
        {
            _updatableDBContext.PaymentDetails.AddRange(paymentDetailColl);
           return await _updatableDBContext.SaveChangesAsync();
        }

        ///// <summary>
        ///// Method to update payments to PaymentDetails table
        ///// </summary>
        ///// <param name="paymentDetailColl"></param>
        ///// <returns></returns>
        //public async Task<int> UpdatePaymentDetailsRange(ICollection<PaymentDetail> paymentDetailColl)
        //{
        //    _updatableDBContext.PaymentDetails.UpdateRange(paymentDetailColl);
        //    return await _updatableDBContext.SaveChangesAsync();
        //}

        /// <summary>
        /// Method to get payment from PaymentDetails table
        /// </summary>
        /// <param name="paymentDetailId"></param>
        /// <returns></returns>
        public async Task<PaymentDetail> GetPaymentDetails(long? paymentDetailId, bool splitUp = false )
        {   
            if(!splitUp)
                return await _readOnlyDbContext.PaymentDetails.Where(x => x.Id == paymentDetailId).FirstOrDefaultAsync();
            else
            {
                return await _readOnlyDbContext.PaymentDetails.Where(x => x.Id == paymentDetailId).Include(x=>x.PaymentInvoiceEpisodeItemsAssocs).FirstOrDefaultAsync();

            }
        }
        /// <summary>
        /// Method to get payment credit from payments id
        /// </summary>
        /// <param name="paymentDetailId"></param>
        /// <returns></returns>
        public async Task<PatientCredit> GetPatientCreditFromPaymentId(long? paymentDetailId,int orgId)
        {
         
            return await _readOnlyDbContext.PatientCredit.Where(x => x.PaymentDetailsId == paymentDetailId && x.OrgId==orgId).FirstOrDefaultAsync();

            
        }
        /// <summary>
        /// Method to do partial update
        /// </summary>
        /// <param name="id"></param>
        /// <param name="loggedInUser"></param>
        /// <returns></returns>
        public async Task<int> PartialUpdateInvoicePayments(long? loggedInUser, PaymentDetailView dbPaymentDetail)
        {
            var p0 = new SqlParameter("@markForReview", dbPaymentDetail.MarkForReview);
            var p1 = (dbPaymentDetail.MarkForReview == true) ? new SqlParameter("@markForReviewId", dbPaymentDetail.MarkForReviewId) : new SqlParameter("@markForReviewId", DBNull.Value);
            var p2 = new SqlParameter("@id", dbPaymentDetail.PaymentDetailsId);
            var p3 = new SqlParameter("@reconcile", dbPaymentDetail.Reconcile);
            var p4 = (dbPaymentDetail.Reconcile == true)? new SqlParameter("@bankDate", dbPaymentDetail.BankDate): new SqlParameter("@bankDate", DBNull.Value);
            var p5 = new SqlParameter("@modifiedDate", DateTime.UtcNow);
            var p6 = new SqlParameter("@modifiedBy", loggedInUser);
            int rows =await _updatableDBContext.Database.ExecuteSqlRawAsync(@"UPDATE InvoicePayment.PaymentDetails SET MarkForReview = @markForReview , 
                                                                                                    MarkForReviewId = @markForReviewId,                                                                                                  
                                                                                                    Reconcile = @reconcile,
                                                                                                    BankDate = @bankDate,
                                                                                                    ModifiedDate = @modifiedDate,
                                                                                                    ModifiedBy = @modifiedBy
                                                                        WHERE ID = @id" , p0, p1, p3, p4, p5, p6, p2);
            return rows;
        }

        /// <summary>
        /// Method to do reconcile and bankdate update
        /// </summary>
        /// <param name="id"></param>
        /// <param name="loggedInUser"></param>
        /// <returns></returns>
        public async Task<int> UpdateReconcilePayments(long? loggedInUser, ReconcilePaymentDetail reconcilePaymentDetail)
        {
            //var ids = string.Join(',', reconcilePaymentDetail.Ids.Select(x => x));
            var p0 = new SqlParameter("@idsList", string.Join<long>(",", reconcilePaymentDetail.Ids));
            var p1 = new SqlParameter("@bankDate", reconcilePaymentDetail.BankDate);
            var p2 = new SqlParameter("@modifiedDate", DateTime.UtcNow);
            var p3 = new SqlParameter("@modifiedBy", loggedInUser);
            int rows = await _updatableDBContext.Database.ExecuteSqlRawAsync(@"UPDATE InvoicePayment.PaymentDetails SET Reconcile = 1,
                                                                                                    BankDate = @bankDate,
                                                                                                    ModifiedDate = @modifiedDate,
                                                                                                    ModifiedBy = @modifiedBy
                                                                        WHERE ID in ( Select [VALUE] as Id  FROM string_split(@idsList,','))", p1,p2,p3,p0);
            return rows;
        }

        /// <summary>
        /// Method to fetch the list of payments made against an invoice
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="invoiceDetailsId"></param>
        /// <returns></returns>
        public async Task<List<PaymentDetail>> FetchPaymentsForInvoice(int orgId, long invoiceDetailsId)
        {
            return await (from payment in _readOnlyDbContext.PaymentDetails
             where payment.OrgId == orgId && payment.StatusId == (short)Status.Active && payment.InvoiceDetailsId == invoiceDetailsId select payment).ToListAsync();
        }

        /// <summary>
        /// Method to list all the payments based on filter
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="queryModel"></param>
        /// <param name="filterModel"></param>
        /// <returns></returns>
        public async Task<QueryResultList<ListPaymentDetail>> ListPaymentTransactions(int orgId, QueryModel queryModel, PaymentDetailFilterModel filterModel)
        {
            IQueryable<ListPaymentDetail> paymentQuery = from payment in _readOnlyDbContext.PaymentDetails
                                                     where payment.OrgId == orgId  && payment.PaymentTypeId != (short)PaymentType.Invoice_Conversion
                                                     select new ListPaymentDetail
                                                     {
                                                         Id = payment.Id,
                                                         OrgId = payment.OrgId,
                                                         InvoiceDetailsId = payment.InvoiceDetailsId,
                                                         PaymentDate = payment.PaymentDate,
                                                         PaymentTypeId = payment.PaymentTypeId,
                                                         PaymentMethodTypeId = payment.PaymentMethodTypeId,
                                                         //Amount = payment.Amount,
                                                         CRAmount = payment.CRAmount,
                                                         DRAmount = payment.DRAmount,
                                                         Surcharge = payment.Surcharge,
                                                         IsSurchargeInc = payment.IsSurchargeInc,
                                                         Reason = payment.Reason,
                                                         ReferanceNo = payment.ReferanceNo,
                                                         BankDate = payment.BankDate,
                                                         CreatedDate = payment.CreatedDate,
                                                         ModifiedDate = payment.ModifiedDate,
                                                         ModifiedBy = payment.ModifiedBy,
                                                         CreatedBy = payment.CreatedBy,
                                                         StatusId = payment.StatusId,
                                                         DeletedBy = payment.DeletedBy,
                                                         DeleteReason = payment.DeleteReason,
                                                         IsExcess = payment.IsExcess
                                                         
                                                     };


            if (filterModel != null)
            {
                if (filterModel.InvoiceDetailsId != null && filterModel.InvoiceDetailsId.Count>0)
                {
                    paymentQuery = paymentQuery.Where(x => filterModel.InvoiceDetailsId.Contains(x.InvoiceDetailsId));
                }

            }
            paymentQuery = SortPayments(paymentQuery, queryModel.SortTerm, queryModel.SortOrder);

            QueryResultList<ListPaymentDetail> paginatedList = await PaginatedResultListForInvoicePayments(paymentQuery, queryModel);

            return paginatedList;
        }

        private async Task<QueryResultList<ListPaymentDetail>> PaginatedResultListForInvoicePayments(IQueryable<ListPaymentDetail> paymentQuery, QueryModel queryModel)
        {
            QueryResultList<ListPaymentDetail> queryList = new QueryResultList<ListPaymentDetail>();
            List<ListPaymentDetail> paginatedList = null;
            if (queryModel.PageNumber > 0 && queryModel.PageSize > 0)
            {
                if (paymentQuery.Any())
                {
                    paginatedList = await paymentQuery.Skip((queryModel.PageNumber - 1) * queryModel.PageSize)
                 .Take(queryModel.PageSize).ToListAsync();
                }
            }

            if (paginatedList != null)
            {
                queryList.ItemRecords = paginatedList;
                queryList.PageNumber = queryModel.PageNumber;
                queryList.PageSize = queryModel.PageSize;
                queryList.CurrentCount = paginatedList.Count();
            }
            else
            {
                queryList.ItemRecords = null;
                queryList.CurrentCount = 0;
            }
            queryList.TotalCount = paymentQuery.Count();
            queryList.PageNumber = queryModel.PageNumber;
            queryList.PageSize = queryModel.PageSize;

            return queryList;
        }

        private IQueryable<ListPaymentDetail> SortPayments(IQueryable<ListPaymentDetail> paymentQuery, string sortTerm, string sortOrder)
        {
            sortTerm = (sortTerm is null) ? string.Empty : sortTerm;
            sortOrder = (sortOrder is null) ? string.Empty : sortOrder;
            switch (sortTerm.ToLower())
            {

                case "id":

                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            paymentQuery = paymentQuery.OrderBy(x => x.Id);
                        }
                        else
                        {

                            paymentQuery = paymentQuery.OrderByDescending(x => x.Id);

                        }
                        break;
                    }
                case "modifieddate":

                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            paymentQuery = paymentQuery.OrderBy(x => x.ModifiedDate);
                        }
                        else
                        {

                            paymentQuery = paymentQuery.OrderByDescending(x => x.ModifiedDate);

                        }
                        break;
                    }
                case "paymentdate":

                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            paymentQuery = paymentQuery.OrderBy(x => x.PaymentDate);
                        }
                        else
                        {

                            paymentQuery = paymentQuery.OrderByDescending(x => x.PaymentDate);

                        }
                        break;
                    }
                default:
                    paymentQuery = paymentQuery.OrderByDescending(x => x.Id);

                    break;
            }
            return paymentQuery;
        }

        public async Task<List<PaymentDetail>> FetchPaymentFromInvoiceIdAndType(long invoice_id, int orgId, short paymentTypeId)
        {
            return await _readOnlyDbContext.PaymentDetails.Where(x => x.InvoiceDetailsId == invoice_id && x.OrgId == orgId && x.StatusId == (short)Status.Active && x.PaymentTypeId == paymentTypeId).ToListAsync();
        }
        public async Task<List<PaymentDetail>> FetchPaymentFromInvoiceIdAndType(long invoice_id, int orgId, List<short> paymentTypeIds)
        {
            return await _readOnlyDbContext.PaymentDetails.Where(x => x.InvoiceDetailsId == invoice_id && x.OrgId == orgId && x.StatusId == (short)Status.Active && paymentTypeIds.Contains(x.PaymentTypeId)).ToListAsync();
        }
        public async Task<int> AddPaymentDetails(PaymentDetail paymentDetail)
        {
            await _updatableDBContext.PaymentDetails.AddAsync(paymentDetail);
            return await _updatableDBContext.SaveChangesAsync();
        }

        public async Task<int>  UpdatePaymentDetails(PaymentDetail paymentDetail)
        {
            _updatableDBContext.PaymentDetails.Update(paymentDetail);
            return await _updatableDBContext.SaveChangesAsync();
        }
        public async Task<int> UpdatePatientCreditDetails(PatientCredit patientCredit)
        {
            _updatableDBContext.PatientCredit.Update(patientCredit);
            return await _updatableDBContext.SaveChangesAsync();
        }
        public async Task<long> AddPatientCredit(PatientCredit patientCredit)
        {
            await _updatableDBContext.PatientCredit.AddAsync(patientCredit);
            return await _updatableDBContext.SaveChangesAsync();
        }

        /// <summary>
        /// Method to add payments to PaymentDetails table
        /// </summary>
        /// <param name="paymentDetailColl"></param>
        /// <returns></returns>
        public async Task<int> AddPatinetCreditRange(List<PatientCredit> patientCredit)
        {
            _updatableDBContext.PatientCredit.AddRange(patientCredit);
            return await _updatableDBContext.SaveChangesAsync();
        }

        public async Task<decimal?> GetPatinetCreditTotalDAL(long patient_id, int orgId,int? companyDetailsId =null)
        {

            decimal? sumCreditAmount =await  _readOnlyDbContext.PatientCredit
                                     .Where(pc => pc.PatientDetailsId == patient_id && (companyDetailsId==null || pc.CompanyDetailsId== companyDetailsId) && pc.OrgId == orgId && pc.StatusId == (short)Status.Active
                                     ).SumAsync(pc => pc.CreditAmount);

            return sumCreditAmount;
        }

        public async Task<List<PaymentDetail>> AddPaymentDetailsForAdd(ICollection<PaymentDetail> paymentDetailColl)
        {
            _updatableDBContext.PaymentDetails.AddRange(paymentDetailColl);
             await _updatableDBContext.SaveChangesAsync();
            return (List<PaymentDetail>)paymentDetailColl;
        }
    }
}
