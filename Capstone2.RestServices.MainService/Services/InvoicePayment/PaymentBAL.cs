﻿using Capstone2.Framework.Business.Common;
using Capstone2.Framework.RestApi;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.Framework.RestApi.Utility;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.InvoicePayment.Interfaces;
using Capstone2.RestServices.InvoicePayment.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Transactions;
using ListPaymentDetail = Capstone2.RestServices.InvoicePayment.Models.ListPaymentDetail;

namespace Capstone2.RestServices.InvoicePayment.Services
{
    public class PaymentBAL : IPaymentBAL
    {
        public IPaymentDAL _paymentDAL;
        public readonly AppSettings _appSettings;
        private IASBMessageSenderHelper _asbMessageSenderHelper;
        private readonly IConfiguration _configuration;
        public PaymentBAL(IPaymentDAL paymentDAL, IOptions<AppSettings> appSettings, IConfiguration configuration, IASBMessageSenderHelper asbMessageSenderHelper)
        {
            this._paymentDAL = paymentDAL;
            this._appSettings = appSettings.Value;
            this._asbMessageSenderHelper = asbMessageSenderHelper;
            this._configuration = configuration;
        }

        /// <summary>
        /// Method to add payments for an invoice
        /// </summary>
        /// <param name="inputPaymentDetail"></param>
        /// <param name="invoicedetail_id"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<string>> AddInvoicePayments(List<PaymentDetail> lstPaymentDetail, long invoicedetail_id, BaseHttpRequestContext baseHttpRequestContext,long patient_id)
        {
            ApiResponse<string> apiResponse = new ApiResponse<string>();
            OrganisationView orgDetails = await FetchMasterCompanyDetails(baseHttpRequestContext);
            short timeZoneId = (orgDetails is null || orgDetails.TimeZoneMasterId <= 0) ? default(short) : orgDetails.TimeZoneMasterId;
            DateTime? companyDateTimeNow = new();
            List<PatientCredit> pateintCreditList = new();
            int creditRows = 0;

            bool isWindows = System.Runtime.InteropServices.RuntimeInformation
                                                 .IsOSPlatform(OSPlatform.Windows);
            if (timeZoneId > 0)
            {
                companyDateTimeNow = GetDateFromOrganisation(orgDetails,isWindows);
            }


            if (lstPaymentDetail is not null && lstPaymentDetail.Count > 0)
            {
                bool isError = false;
                decimal totalPayment = 0;
                decimal totalCreditDebit = 0;
                lstPaymentDetail.ForEach(payment =>
                {
                    if (companyDateTimeNow is not null && companyDateTimeNow > default(DateTime))
                    {
                        if (payment.PaymentDate > companyDateTimeNow)
                        {
                            apiResponse.Errors.Add("Payment Date cannot be in the future.");
                            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                            apiResponse.Result = null;
                            apiResponse.Message = "Failure";
                            isError = true;
                        }
                    }
                    if (payment.CRAmount != null)
                        totalPayment += (decimal)payment.CRAmount;
                    if(payment.PaymentMethodTypeId==(short)PaymentMethodType.Patient_Credit && payment.PaymentTypeId!=(short)PaymentType.Credit)
                        totalCreditDebit += (decimal)payment.CRAmount;
                    payment.OrgId = baseHttpRequestContext.OrgId;
                    payment.InvoiceDetailsId = invoicedetail_id;
                    payment.StatusId = (short)Status.Active;
                    payment.CreatedBy = baseHttpRequestContext.UserId;
                    payment.ModifiedBy = baseHttpRequestContext.UserId;
                    payment.CreatedDate = DateTime.UtcNow;
                    payment.InvoiceDetailsId = invoicedetail_id;
                    if (!payment.IsSurchargeInc)
                    {
                        payment.Surcharge = default(decimal);
                    }

                });

                int? companydetailsId = null;

                if (!isError && totalPayment > 0)
                {
                    ApiResponse<InvoiceDetailUpdate> apiResponseInvoice = await FetchInvoiceInfo(invoicedetail_id, baseHttpRequestContext);
                    if (apiResponseInvoice != null && apiResponseInvoice.StatusCode == StatusCodes.Status200OK)
                    {
                        InvoiceDetailUpdate invoiceDetailUpdate = apiResponseInvoice.Result;
                        if (invoiceDetailUpdate != null)
                        {
                            companydetailsId = invoiceDetailUpdate.CompanyDetailsId;
                            if (totalCreditDebit > 0)
                            {
                                decimal? totalCreditAvailable = await _paymentDAL.GetPatinetCreditTotalDAL(patient_id, baseHttpRequestContext.OrgId, companydetailsId);
                                if (totalCreditAvailable < totalCreditDebit)
                                {
                                    isError = true;
                                    apiResponse.Errors.Add("Not enough patient credits available.");
                                }

                            }
                            if (!isError && (invoiceDetailUpdate.TotalInvoiceAmount-invoiceDetailUpdate.TotalAmoutPaid) < totalPayment)
                            {
                                isError = true;
                                apiResponse.Errors.Add("Mismatch with payment amount and owing amount.");
                            }
                            //else
                            //{
                            //    isError = false;
                            //}
                           
                        }
                        else
                        {
                            isError = true;
                        }
                    }
                    else
                    {
                        isError = true;
                    }
                }
                if (!isError)
                {
                    

                    bool paymentComplete = false;
                    using (var transaction = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
                    {
                        int rows = await _paymentDAL.AddPaymentDetailsRange(lstPaymentDetail);
                      
                        if (rows > 0)
                        {
                            foreach (PaymentDetail Payment in lstPaymentDetail)
                            {

                                if (Payment.PaymentMethodTypeId == (short)PaymentMethodType.Patient_Credit)
                                {
                                    PatientCredit newCredit = new();
                                    newCredit.PatientDetailsId = patient_id;
                                    newCredit.OrgId = Payment.OrgId;
                                    newCredit.PaymentDetailsId = Payment.Id;
                                    newCredit.BankDate = Payment.BankDate;
                                    newCredit.StatusId = (short)Status.Active;
                                    newCredit.ModifiedBy = baseHttpRequestContext.UserId;
                                    newCredit.CreditAmount = Payment.CRAmount * -1;
                                    newCredit.CompanyDetailsId = companydetailsId;
                                    pateintCreditList.Add(newCredit);
                                }

                            }

                            if (pateintCreditList.Count > 0) { 

                            creditRows = await _paymentDAL.AddPatinetCreditRange(pateintCreditList);

                            }

                            ApiResponse<string> apiResposneInv = await UpdateInvoice(lstPaymentDetail, invoicedetail_id, baseHttpRequestContext);
                                if(apiResposneInv!=null && apiResposneInv.StatusCode==StatusCodes.Status200OK)
                                    paymentComplete = true;
                                else
                                {
                                    apiResponse.Errors = apiResposneInv.Errors;
                                }
                        }
                        if(paymentComplete)
                            transaction.Complete();
                    }

                      //  int rows = await _paymentDAL.AddPaymentDetailsRange(lstPaymentDetail);
                    if (paymentComplete )
                    {   
                       // await UpdateInvoice(lstPaymentDetail, invoicedetail_id, baseHttpRequestContext);
                       List<PaymentDetail> listToEoDReport = lstPaymentDetail.Where(y =>  y.PaymentMethodTypeId != (short)PaymentMethodType.Patient_Credit).ToList();
                        await StorePaymentDetailMessage(baseHttpRequestContext.OrgCode, listToEoDReport);//TODO:Send message to Service Bus for EOD report
                        await StorePaymentDetailMessageForDepositReport(baseHttpRequestContext.OrgCode, lstPaymentDetail, (int)EODRequestDataType.DepositReport);//TODO:Send message to Service Bus for Deposit report

                        List<PaymentDetail> lstInvPayments = (lstPaymentDetail == null || lstPaymentDetail.Count==0)?null: lstPaymentDetail.Where(x => x.PaymentTypeId != (short)PaymentType.Deposit).ToList();
                        if(lstInvPayments!=null && lstInvPayments.Count >0)
                            await StoreSplitPaymentDetailMessage(baseHttpRequestContext.OrgCode, lstInvPayments);//TODO:Send message to Service Bus for Split Payments-invoicepayment

                        if (creditRows > 0)
                        {
                            // Add the Credit Row to Deposit Report with positive value
                            List<PaymentDetail> listToCreditReport = lstPaymentDetail.Where(y => y.PaymentMethodTypeId == (short)PaymentMethodType.Patient_Credit).ToList();
                            await StorePaymentDetailMessageForDepositReport(baseHttpRequestContext.OrgCode, listToCreditReport, (int)EODRequestDataType.patient_Credit_Substract);//TODO:Send message to Service Bus for Deposit report

                        }


                        apiResponse.StatusCode = StatusCodes.Status200OK;
                        apiResponse.Result = "Success";
                        return apiResponse;
                    }
                    else
                    {
                        apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                        apiResponse.Result = "Failure";
                        apiResponse.Errors.Add("Payment cannot be added at this time.");
                        return apiResponse;
                    }
                }

            }
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            apiResponse.Errors.Add("Payments cannot be added at this time.");
            return apiResponse;
        }
        /// <summary>
        /// Method to add payments for an invoice
        /// </summary>
        /// <param name="inputPaymentDetail"></param>
        /// <param name="invoicedetail_id"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long>> AddInvoiceAdjustmentPayments(PaymentDetail paymentDetail, long invoicedetail_id, BaseHttpRequestContext baseHttpRequestContext, long patient_id)
        {
            ApiResponse<long> apiResponse = new ApiResponse<long>();
            OrganisationView orgDetails = await FetchMasterCompanyDetails(baseHttpRequestContext);
            PatientCredit newCredit = new();
            int rows = 0;
            long creditRows = 0;
            short timeZoneId = (orgDetails is null || orgDetails.TimeZoneMasterId <= 0) ? default(short) : orgDetails.TimeZoneMasterId;
            DateTime? companyDateTimeNow = new();
            bool isWindows = System.Runtime.InteropServices.RuntimeInformation
                                                 .IsOSPlatform(OSPlatform.Windows);
            if (timeZoneId > 0)
            {
                companyDateTimeNow = GetDateFromOrganisation(orgDetails, isWindows);
            }
            if (paymentDetail is not null && Enum.IsDefined(typeof(AdjustmentType), (int)paymentDetail.PaymentTypeId))
            {

                if (companyDateTimeNow is not null && companyDateTimeNow > default(DateTime))
                {
                    if (paymentDetail.PaymentDate > companyDateTimeNow)
                    {
                        apiResponse.Errors.Add("Payment Date cannot be in the future.");
                        apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                        apiResponse.Result = default(long);
                        apiResponse.Message = "Failure";
                        return apiResponse;
                    }
                }
                paymentDetail.OrgId = baseHttpRequestContext.OrgId;
                paymentDetail.InvoiceDetailsId = invoicedetail_id;
                paymentDetail.StatusId = (short)Status.Active;
                paymentDetail.CreatedBy = baseHttpRequestContext.UserId;
                paymentDetail.ModifiedBy = baseHttpRequestContext.UserId;
                paymentDetail.CreatedDate = DateTime.UtcNow;
                paymentDetail.InvoiceDetailsId = invoicedetail_id;
                if (!paymentDetail.IsSurchargeInc)
                {
                    paymentDetail.Surcharge = default(decimal);
                }
                if (paymentDetail.PaymentMethodTypeId == (short)PaymentMethodType.Patient_Credit)
                {
                    newCredit.PatientDetailsId = patient_id;
                    newCredit.OrgId = paymentDetail.OrgId;
                    newCredit.BankDate = paymentDetail.BankDate;
                    newCredit.StatusId = (short)Status.Active;
                    newCredit.ModifiedBy = baseHttpRequestContext.UserId;
                    newCredit.CreditAmount = paymentDetail.DRAmount;
                    ApiResponse<InvoiceDetailUpdate> apiResponseInvoice = await FetchInvoiceInfo(invoicedetail_id, baseHttpRequestContext);
                    if (apiResponseInvoice != null && apiResponseInvoice.StatusCode == StatusCodes.Status200OK)
                    {
                        InvoiceDetailUpdate invoiceDetailUpdate = apiResponseInvoice.Result;
                        if (invoiceDetailUpdate != null)
                        {
                            newCredit.CompanyDetailsId= invoiceDetailUpdate.CompanyDetailsId;
                        }
                    }
                }

                using (var transaction = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
                {
                    rows = await _paymentDAL.AddPaymentDetails(paymentDetail);

                    if (rows > 0 && paymentDetail.PaymentMethodTypeId == (short)PaymentMethodType.Patient_Credit)
                    {
                        newCredit.PaymentDetailsId = paymentDetail.Id;

                        creditRows = await _paymentDAL.AddPatientCredit(newCredit);
                    }
                    transaction.Complete();
                }
                
                if (rows > 0)
                {
                    List<PaymentDetail> paymentDetails = new()
                    {
                        paymentDetail
                    };
                    if (paymentDetail.PaymentTypeId != (short)AdjustmentType.Write_off && paymentDetail.PaymentMethodTypeId != (short)PaymentMethodType.Patient_Credit)
                    {
                        await StorePaymentDetailMessage(baseHttpRequestContext.OrgCode, paymentDetails);//TODO:Send message to Service Bus for EOD report
                        await StorePaymentDetailMessageForDepositReport(baseHttpRequestContext.OrgCode, paymentDetails, (int)EODRequestDataType.DepositReport);//TODO:Send message to Service Bus for Deposit report

                    }
                    else if(paymentDetail.PaymentTypeId != (short)AdjustmentType.Write_off && paymentDetail.PaymentMethodTypeId == (short)PaymentMethodType.Patient_Credit){

                        await StorePaymentDetailMessageForDepositReport(baseHttpRequestContext.OrgCode, paymentDetails, (int)EODRequestDataType.DepositReport);//TODO:Send message to Service Bus for Deposit report

                    }

                    if(creditRows > 0)
                    {
                        // Add the Credit Row to Deposit Report with positive value
                        await StorePaymentDetailMessageForDepositReport(baseHttpRequestContext.OrgCode, paymentDetails, (int)EODRequestDataType.Patient_Credit_add);//TODO:Send message to Service Bus for Deposit report

                    }


                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    apiResponse.Result = paymentDetail.Id;
                    return apiResponse;
                }
                

            }
            apiResponse.Result = default(long);
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            apiResponse.Errors.Add("Invoice Adjustment cannot be added at this time.");
            return apiResponse;
        }
        private async Task StorePaymentDetailMessage(string orgCode, List<PaymentDetail> lstPaymentDetail,bool isEodAdd  =true)
        {
            PaymentRequestDataModel paymentRequestDataModel = new PaymentRequestDataModel
            {
                OrgName = orgCode,
                PropertyType = (isEodAdd)?(int)EODRequestDataType.EODReport : (int)EODRequestDataType.PaymentDetailUpdate,
                PropertyId = lstPaymentDetail.Select(x => x.Id).ToArray(),
                PropertyValue = ""
            };
            Dictionary<string, string> userPros = new Dictionary<string, string>
                        {
                            { "RequestType","EODReport"},
                            { "to",_configuration["AzureAD:ASBSubNameEODReport"]}
                        };
            byte[] jsonContentByteArr = Encoding.GetEncoding("iso-8859-1").GetBytes(JsonConvert.SerializeObject(paymentRequestDataModel));
            await _asbMessageSenderHelper.MessageSenderTopic(jsonContentByteArr, userPros, _configuration["AzureAD:AzSBConnStringPayment"], _configuration["AzureAD:ASBTopicPayment"]);

        }
        private async Task StorePaymentDetailMessageForDepositReport(string orgCode, List<PaymentDetail> lstPaymentDetail,int propertyType)
        {
            PaymentRequestDataModel paymentRequestDataModel = new PaymentRequestDataModel
            {
                OrgName = orgCode,
                PropertyType = propertyType,
                PropertyId = lstPaymentDetail.Select(x => x.Id).ToArray(),
                PropertyValue = ""
            };
            Dictionary<string, string> userPros = new Dictionary<string, string>
                        {
                            { "RequestType","DepositReport"},
                            { "to",_configuration["AzureAD:ASBSubNameDepositReport"]}
                        };
            byte[] jsonContentByteArr = Encoding.GetEncoding("iso-8859-1").GetBytes(JsonConvert.SerializeObject(paymentRequestDataModel));
            await _asbMessageSenderHelper.MessageSenderTopic(jsonContentByteArr, userPros, _configuration["AzureAD:AzSBConnStringPayment"], _configuration["AzureAD:ASBTopicPayment"]);

        }
        private async Task StoreSplitPaymentDetailMessage(string orgCode, List<PaymentDetail> lstPaymentDetail)
        {
            PaymentRequestDataModel paymentRequestDataModel = new PaymentRequestDataModel
            {
                OrgName = orgCode,
                PropertyType =(int)EODRequestDataType.SplitPayment,
                PropertyId = lstPaymentDetail.Select(x => x.Id).ToArray(),
                PropertyValue = ""
            };
            Dictionary<string, string> userPros = new Dictionary<string, string>
                        {
                            { "RequestType","SplitPayment"},
                            { "to",_configuration["AzureAD:ASBSubNameInvoicePayment"]}
                        };
            byte[] jsonContentByteArr = Encoding.GetEncoding("iso-8859-1").GetBytes(JsonConvert.SerializeObject(paymentRequestDataModel));
            await _asbMessageSenderHelper.MessageSenderTopic(jsonContentByteArr, userPros, _configuration["AzureAD:AzSBConnStringPayment"], _configuration["AzureAD:ASBTopicPayment"]);
        }
        ///// <summary>
        ///// Method to edit payments for an invoice
        ///// </summary>
        ///// <param name="inputPaymentDetail"></param>
        ///// <param name="invoicedetail_id"></param>
        ///// <param name="baseHttpRequestContext"></param>
        ///// <returns></returns>
        //public async Task<ApiResponse<string>> EditInvoicePayments(List<PaymentDetail> lstPaymentDetail, long invoicedetail_id, BaseHttpRequestContext baseHttpRequestContext)
        //{
        //    ApiResponse<string> apiResponse = new ApiResponse<string>();
        //    OrganisationView orgDetails = await FetchMasterCompanyDetails(baseHttpRequestContext);
        //    short timeZoneId = (orgDetails is null || orgDetails.TimeZoneMasterId <= 0) ? default(short) : orgDetails.TimeZoneMasterId;
        //    DateTime? companyDateTimeNow = new();
        //    if (timeZoneId > 0)
        //    {
        //        companyDateTimeNow = GetDateFromOrganisation(orgDetails);

        //    }
        //    if (lstPaymentDetail is not null && lstPaymentDetail.Count > 0)
        //    {
        //        bool isError = false;
        //        lstPaymentDetail.ForEach(async payment =>
        //        {
        //            PaymentDetail paymentDetailDB = await _paymentDAL.GetPaymentDetails(payment.Id);
        //            if (companyDateTimeNow is not null && companyDateTimeNow > default(DateTime))
        //            {
        //                if (payment.PaymentDate > companyDateTimeNow)
        //                {
        //                    apiResponse.Errors.Add("Payment Date cannot be in the future.");
        //                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
        //                    apiResponse.Result = null;
        //                    apiResponse.Message = "Failure";
        //                    isError = true;
        //                }
        //            }
        //            payment.OrgId = baseHttpRequestContext.OrgId;
        //            payment.InvoiceDetailsId = invoicedetail_id;
        //            payment.ModifiedBy = baseHttpRequestContext.UserId;
        //            payment.CreatedDate = paymentDetailDB.CreatedDate;
        //            payment.CreatedBy = paymentDetailDB.CreatedBy;
        //            payment.ModifiedDate = paymentDetailDB.ModifiedDate;
        //            if(payment.Surcharge is null)
        //            {
        //                payment.Surcharge = paymentDetailDB.Surcharge;
        //            }

        //        });
        //        if (!isError)
        //        {
        //            int rows = await _paymentDAL.UpdatePaymentDetailsRange(lstPaymentDetail);
        //            if (rows > 0)
        //            {
        //                await UpdateInvoice(lstPaymentDetail, invoicedetail_id, baseHttpRequestContext);

        //                apiResponse.StatusCode = StatusCodes.Status200OK;
        //                apiResponse.Result = "Success";
        //                return apiResponse;
        //            }
        //        }

        //    }
        //    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
        //    apiResponse.Errors.Add("Payments cannot be added at this time.");
        //    return apiResponse;
        //}

        private IDictionary GetFilledProperties(PaymentDetailView paymentDetailView)
        {
            Dictionary<string, object> propertyDictionary = new();
            foreach (PropertyInfo property in paymentDetailView.GetType().GetProperties())
            {
                var value = property.GetValue(paymentDetailView);
                if (value is not null)
                    propertyDictionary.Add(property.Name, property.GetValue(paymentDetailView));
            }
            return propertyDictionary;
        }
        /// <summary>
        /// Method to do partial update
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="id"></param>
        /// <param name="patientUpdateView"></param>
        /// <returns></returns>

        public async Task<ApiResponse<string>> PartialUpdateInvoicePayments(BaseHttpRequestContext baseHttpRequestContext, List<PaymentDetailView> paymentDetailViews)
        {
            ApiResponse<string> apiResponse = new();
            if (paymentDetailViews is not null)
            {
                foreach (PaymentDetailView paymentDetail in paymentDetailViews)
                {
                    if (paymentDetail is not null)
                    {
                        //Dictionary<string, object> propertyDictionary = (Dictionary<string, object>)GetFilledProperties(paymentDetail);
                        await _paymentDAL.PartialUpdateInvoicePayments(baseHttpRequestContext.UserId, paymentDetail);

                    }
                }
                apiResponse.Result = "Successfully updated";
                apiResponse.StatusCode = StatusCodes.Status200OK;

                //Action:Send message to Service Bus for Bulk update EOD report
                long[] requestIds = paymentDetailViews.Select(x => x.PaymentDetailsId.Value).ToArray();
                if (requestIds.Length > 0)
                    await StorePaymentDetailBulkMessage(baseHttpRequestContext.OrgCode, requestIds);

                return apiResponse;
            }

            apiResponse.Errors.Add("InvoicePayment cannot be updated.");
            apiResponse.Message = "Failure";
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            return apiResponse;
        }

        private async Task StorePaymentDetailBulkMessage(string orgCode, long[] requestIds)
        {
            PaymentRequestDataModel paymentRequestDataModel = new PaymentRequestDataModel
            {
                OrgName = orgCode,
                PropertyType = (int)EODRequestDataType.BulkUpdate,
                PropertyId = requestIds,
                PropertyValue = ""
            };
            Dictionary<string, string> userPros = new Dictionary<string, string>
                        {
                            { "RequestType","Bulk Update EODReport"},
                            { "to",_configuration["AzureAD:ASBSubNameBulkUpdateEOD"]}
                        };
            byte[] jsonContentByteArr = Encoding.GetEncoding("iso-8859-1").GetBytes(JsonConvert.SerializeObject(paymentRequestDataModel));
            await _asbMessageSenderHelper.MessageSenderTopic(jsonContentByteArr, userPros, _configuration["AzureAD:AzSBConnStringPayment"], _configuration["AzureAD:ASBTopicPayment"]);
        }

        /// <summary>
        /// Method to do reconcile and bankdate update
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="id"></param>
        /// <param name="patientUpdateView"></param>
        /// <returns></returns>

        public async Task<ApiResponse<string>> UpdateReconcilePayments(BaseHttpRequestContext baseHttpRequestContext, ReconcilePaymentDetail reconcilePaymentDetail)
        {
            ApiResponse<string> apiResponse = new();
            if (reconcilePaymentDetail is not null)
            {

                //Dictionary<string, object> propertyDictionary = (Dictionary<string, object>)GetFilledProperties(paymentDetail);
                await _paymentDAL.UpdateReconcilePayments(baseHttpRequestContext.UserId, reconcilePaymentDetail);
                apiResponse.Result = "Successfully updated";
                apiResponse.StatusCode = StatusCodes.Status200OK;

                //Action:Send message to Service Bus for Bulk update EOD report
                long[] requestIds = reconcilePaymentDetail.Ids.ToArray();
                if (requestIds.Length > 0)
                    await StorePaymentDetailBulkMessage(baseHttpRequestContext.OrgCode, requestIds);

                return apiResponse;
            }

            apiResponse.Errors.Add("InvoicePayment cannot be updated.");
            apiResponse.Message = "Failure";
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            return apiResponse;
        }

        private DateTime? GetDateFromOrganisation(OrganisationView orgDetails, bool isWindows)
        {
            //var CompanyDateTimeNow = DateTimeConversion.ConvertTimeFromUtc(DateTime.UtcNow, orgDetails.WindowsTimeZoneData);
            //if (CompanyDateTimeNow is null)
            //{
            //    CompanyDateTimeNow = DateTimeConversion.ConvertTimeFromUtc(DateTime.UtcNow, orgDetails.LinuxTimeZoneData);
            //}

            //return CompanyDateTimeNow;
            string timeZone = (isWindows) ? orgDetails.WindowsTimeZoneData : orgDetails.LinuxTimeZoneData;

            var CompanyDateTimeNow = DateTimeConversion.ConvertTimeFromUtc(DateTime.UtcNow, timeZone);

            return CompanyDateTimeNow;
        }
        private async Task<OrganisationView> FetchMasterCompanyDetails(BaseHttpRequestContext baseHttpRequestContext)
        {

            ApiResponse<OrganisationView> apiResponseCompany = new();
            string endpoint = string.Format("/company/Organisation/{0}", baseHttpRequestContext.OrgId);
            string companySeviceUrl = _appSettings.ApiUrls["CompanyServiceUrl"] + endpoint;
            RestClient restClientCompany = new RestClient(companySeviceUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            apiResponseCompany = await restClientCompany.GetAsync<ApiResponse<OrganisationView>>(companySeviceUrl);
            return (apiResponseCompany == null || apiResponseCompany.Result == null) ? null : apiResponseCompany.Result;

        }
        private async Task<ApiResponse<string>> UpdateInvoice(List<PaymentDetail> lstPaymentDetail, long invoiceDetailsId, BaseHttpRequestContext baseHttpRequestContext)
        {
            InvoiceDetailUpdate invoiceDetailUpdate = new();
            bool isInvoice = false;
            lstPaymentDetail.ForEach(payment =>
            {
                if(payment.CRAmount is not null)
                {
                    invoiceDetailUpdate.TotalAmoutPaid += (decimal)payment.CRAmount;
                    if (payment.PaymentTypeId == (short)PaymentType.Deposit)
                    {
                        invoiceDetailUpdate.TotalDepositPaid += (decimal)payment.CRAmount; ;
                    }
                }
                if(payment.CRAmount is not null && payment.IsExcess == true)
                {
                
                    invoiceDetailUpdate.TotalExcessAmountPaid = (decimal)((invoiceDetailUpdate.TotalExcessAmountPaid != null) ? invoiceDetailUpdate.TotalExcessAmountPaid + (decimal)payment.CRAmount : (decimal)payment.CRAmount);
                }
                

            });

            string invoiceSeviceUrl = _appSettings.ApiUrls["InvoiceServiceUrl"] + "/invoice/invoice_details/patch/" + invoiceDetailsId;
            RestClient restClientInvoice = new RestClient(invoiceSeviceUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            return await restClientInvoice.PutAsync<ApiResponse<string>>(invoiceSeviceUrl, invoiceDetailUpdate);

        }
        private async Task<ApiResponse<InvoiceDetailUpdate>> FetchInvoiceInfo(long invoiceDetailsId, BaseHttpRequestContext baseHttpRequestContext)
        {
            InvoiceDetailUpdate invoiceDetailUpdate = new();
            

            string invoiceSeviceUrl = _appSettings.ApiUrls["InvoiceServiceUrl"] + "/invoice/invoice_details_info/" + invoiceDetailsId;
            RestClient restClientInvoice = new RestClient(invoiceSeviceUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            return await restClientInvoice.GetAsync<ApiResponse<InvoiceDetailUpdate>>(invoiceSeviceUrl, invoiceDetailUpdate);

        }

        /// <summary>
        /// Method to list all the payments against an invoice
        /// </summary>
        /// <param name="queryModel"></param>
        /// <param name="invoicedetail_id"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<QueryResultList<ListPaymentDetail>>> ListPaymentTransactions(QueryModel queryModel, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<QueryResultList<ListPaymentDetail>> apiResponse = new();
            int orgId = baseHttpRequestContext.OrgId;
            PaymentDetailFilterModel filterModel = PrepareFilterParameters(queryModel.Filter);
            QueryResultList<ListPaymentDetail> paymentQueryList = await _paymentDAL.ListPaymentTransactions(orgId, queryModel, filterModel);

            if (paymentQueryList is not null && paymentQueryList.CurrentCount > 0 && paymentQueryList.ItemRecords is not null)
            {
                List<ListPaymentDetail> lstPayments = paymentQueryList.ItemRecords.ToList();
                List<long?> lstUserIds = lstPayments.Where(x => x.ModifiedBy != null).Select(x => x.ModifiedBy).ToList();
                lstUserIds.AddRange(lstPayments.Where(x => x.CreatedBy != null).Select(x => x.CreatedBy).ToList());
                List<UserInfoView> lstUserInfoView = await FetchUserDetails(lstUserIds, baseHttpRequestContext);
                if (lstUserInfoView is not null && lstUserInfoView.Any())
                {
                    lstPayments.Where(payment => payment.ModifiedBy != null).ToList()
                        .ForEach(payment =>
                        {
                            if (payment.PaymentTypeId > default(short))
                                if (Enum.IsDefined(typeof(PaymentType),(int) payment.PaymentTypeId))
                                    payment.PaymentType = EnumExtensions.GetDescription((PaymentType)payment.PaymentTypeId);
                                else if (Enum.IsDefined(typeof(AdjustmentType),(int) payment.PaymentTypeId))
                                    payment.PaymentType = EnumExtensions.GetDescription((AdjustmentType)payment.PaymentTypeId);

                           // payment.PaymentType = (payment.CRAmount is not null && payment.CRAmount >= 0)? EnumExtensions.GetDescription((PaymentType)payment.PaymentTypeId) : EnumExtensions.GetDescription((AdjustmentType)payment.PaymentTypeId);
                            payment.PaymentMethod =(payment.PaymentMethodTypeId ==null)?string.Empty:EnumExtensions.GetDescription((PaymentMethodType)payment.PaymentMethodTypeId);
                            payment.ModifiedUser = (payment.ModifiedBy is null) ? null : lstUserInfoView.Where(a => a.Id == payment.ModifiedBy).FirstOrDefault();
                            payment.CreatedUser = (payment.CreatedBy is null) ? null : lstUserInfoView.Where(a => a.Id == payment.CreatedBy).FirstOrDefault();

                        });
                }
            }
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result = paymentQueryList;
            return apiResponse;
        }

        private PaymentDetailFilterModel PrepareFilterParameters(string filter)
        {
            if (!(string.IsNullOrEmpty(filter)))
            {
                return JsonConvert.DeserializeObject<PaymentDetailFilterModel>(filter);
            }
            else
            {
                return null;
            }
        }

        private async Task<List<UserInfoView>> FetchUserDetails(List<long?> lstUserIds, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<List<UserInfoView>> apiResponseUsers = new();
            string param = "Ids=" + string.Join(",", lstUserIds);
            string userSeviceUrl = _appSettings.ApiUrls["UserServiceUrl"] + "/user/user_detailinfo?" + param;

            RestClient restClientUser = new RestClient(userSeviceUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            apiResponseUsers = await restClientUser.GetAsync<ApiResponse<List<UserInfoView>>>(userSeviceUrl);
            if (apiResponseUsers.StatusCode == StatusCodes.Status200OK && apiResponseUsers.Result is not null)
            {
                return apiResponseUsers.Result;

            }
            return null;
        }
        public async Task<ApiResponse<string>> AddUpdateInvoicePaymentsOnConversion(InvoiceDetailIdObject invoiceObj, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<string> apiResponse = new();
            List<short> paymentTypeIds = new()
            {
                (short)PaymentType.Deposit,
                (short)AdjustmentType.Refund
            };
            List<PaymentDetail> lstPaymentDetails = await _paymentDAL.FetchPaymentFromInvoiceIdAndType(invoiceObj.Id, baseHttpRequestContext.OrgId, paymentTypeIds);

            OrganisationView orgDetails = await FetchMasterCompanyDetails(baseHttpRequestContext);
            bool isWindows = System.Runtime.InteropServices.RuntimeInformation
                                                  .IsOSPlatform(OSPlatform.Windows);
            short timeZoneId = (orgDetails is null || orgDetails.TimeZoneMasterId <= 0) ? default(short) : orgDetails.TimeZoneMasterId;
            DateTime? companyDateTimeNow = GetDateFromOrganisation(orgDetails, isWindows);
            List<PaymentDetail> lstAddPayment = new();
            if (companyDateTimeNow is not null)
            {
                decimal totalFee = 0;
                if (lstPaymentDetails is not null)
                {
                    lstPaymentDetails.ForEach(x =>
                    {
                        if(x.CRAmount!=null && x.CRAmount > 0)
                        {
                            //PaymentDetail crPayment = new();
                            //crPayment.OrgId = baseHttpRequestContext.OrgId;
                            //crPayment.InvoiceDetailsId = invoiceObj.Id;
                            //crPayment.StatusId = (short)Status.Active;
                            //crPayment.CreatedBy = baseHttpRequestContext.UserId;
                            //crPayment.ModifiedBy = baseHttpRequestContext.UserId;
                            //crPayment.CreatedDate = DateTime.UtcNow;
                            //crPayment.PaymentTypeId = (short)PaymentType.Invoice_Conversion;
                            //crPayment.CRAmount = x.CRAmount;
                            //crPayment.PaymentDate = (DateTime)companyDateTimeNow;
                            //lstAddPayment.Add(crPayment);
                            totalFee+=(decimal) x.CRAmount;
                           
                        }
                        if(x.DRAmount!=null && x.DRAmount > 0)
                        {
                            totalFee -= (decimal)x.DRAmount;
                        }
                       
                    });
                    PaymentDetail crPayment = new();
                    crPayment.OrgId = baseHttpRequestContext.OrgId;
                    crPayment.InvoiceDetailsId = invoiceObj.Id;
                    crPayment.StatusId = (short)Status.Active;
                    crPayment.CreatedBy = baseHttpRequestContext.UserId;
                    crPayment.ModifiedBy = baseHttpRequestContext.UserId;
                    crPayment.CreatedDate = DateTime.UtcNow;
                    crPayment.PaymentTypeId = (short)PaymentType.Invoice_Conversion;
                    crPayment.CRAmount = totalFee;
                    crPayment.PaymentDate = (DateTime)companyDateTimeNow;
                    lstAddPayment.Add(crPayment);

                    PaymentDetail drPayment = crPayment.Clone();
                    drPayment.DRAmount = drPayment.CRAmount;
                    drPayment.CRAmount = null;
                    lstAddPayment.Add(drPayment);
                }

                if (lstAddPayment is not null && lstAddPayment.Count > 0)
                {
                    int rows = await _paymentDAL.AddPaymentDetailsRange(lstAddPayment);
                    if(rows > 0)
                    {
                        List<PaymentDetail> lstInvPayments = (lstAddPayment == null || lstAddPayment.Count == 0) ? null : lstAddPayment.Where(x => x.PaymentTypeId != (short)PaymentType.Deposit).ToList();

                        if (lstInvPayments != null && lstInvPayments.Count > 0)
                            await StoreSplitPaymentDetailMessage(baseHttpRequestContext.OrgCode, lstInvPayments);//Send message to Service Bus for Split Payments-invoicepayment
                    }
                    string ids = string.Join(",", lstAddPayment.Select(x => x.Id).ToList()) + "]}";
                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    apiResponse.Message = "Success";
                    apiResponse.Result = ids;
                    return apiResponse;
                }
            }
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            apiResponse.Errors.Add("Invoice Conversion Payment cannot be added.");
            apiResponse.Message = "Failure";
            apiResponse.Result = null;
            return apiResponse;

        }
        /// <summary>
        /// Method to fetch payment details
        /// </summary>
        /// <param name="id"></param>
        /// <param name="invoicedetail_id"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<PaymentDetail>> GetInvoicePaymentDetail(long id, long invoicedetail_id, BaseHttpRequestContext baseHttpRequestContext,bool splitUp=false)
        {
            ApiResponse<PaymentDetail> apiResponse = new();
            PaymentDetail paymentDetail = await _paymentDAL.GetPaymentDetails(id,splitUp);
            if(paymentDetail is not null)
            {
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failure";
            }
            apiResponse.Result = paymentDetail;
            return apiResponse;
        }
        /// <summary>
        /// Method to update payment detail except CRAmount and DRAmount
        /// </summary>
        /// <param name="paymentDetail"></param>
        /// <param name="id"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<PaymentDetail>> EditInvoicePayment(PaymentDetail paymentDetail, long id, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<PaymentDetail> apiResponse = new();
            int rows = 0;
            PaymentDetail paymentDetailDB = await _paymentDAL.GetPaymentDetails(id, false);
            if(paymentDetailDB is not null && paymentDetail is not null)
            {
                if(paymentDetailDB.PaymentMethodTypeId!= paymentDetail.PaymentMethodTypeId && (paymentDetailDB.PaymentMethodTypeId==(short)PaymentMethodType.Patient_Credit || paymentDetail.PaymentMethodTypeId == (short)PaymentMethodType.Patient_Credit ))
                {
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Message = "Failure";
                    apiResponse.Errors.Add("Payment Detail cannot be updated to/from Patient Credit Type.");
                    return apiResponse;
                }
                paymentDetail.CRAmount = paymentDetailDB.CRAmount;
                paymentDetail.DRAmount = paymentDetailDB.DRAmount;
                paymentDetail.CreatedBy = paymentDetailDB.CreatedBy;
                paymentDetail.CreatedDate = paymentDetailDB.CreatedDate;
                paymentDetail.OrgId = paymentDetailDB.OrgId;
                paymentDetail.ModifiedDate = DateTime.UtcNow;
                paymentDetail.ModifiedBy = baseHttpRequestContext.UserId;
                paymentDetail.Id = id;
                paymentDetail.TransactionId = paymentDetailDB.TransactionId;
                paymentDetail.Reconcile = paymentDetailDB.Reconcile;
                paymentDetail.BankDate = (paymentDetail.BankDate is null) ? paymentDetailDB.BankDate : paymentDetail.BankDate;
                paymentDetail.InvoiceDetailsId = paymentDetailDB.InvoiceDetailsId;
                paymentDetail.MarkForReview = paymentDetailDB.MarkForReview;
                paymentDetail.MarkForReviewId = paymentDetailDB.MarkForReviewId;
                paymentDetail.StatusId = (short)Status.Active;
                paymentDetail.PaymentTypeId = paymentDetailDB.PaymentTypeId;
                //paymentDetail.Reason = paymentDetailDB.Reason;
                if (!paymentDetail.IsSurchargeInc)
                {
                    paymentDetail.Surcharge = default(decimal);
                }
                bool paymentComplete = false;
                using (var transaction = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
                {
                    rows = await _paymentDAL.UpdatePaymentDetails(paymentDetail);

                    if (rows > 0)
                    {
                        if (paymentDetail.IsExcess != paymentDetailDB.IsExcess)
                        {
                            ApiResponse<string> apiResposneInv = await UpdateInvoiceForPaymentEdit(paymentDetail, paymentDetailDB, paymentDetailDB.InvoiceDetailsId, baseHttpRequestContext);
                            if (apiResposneInv != null && apiResposneInv.StatusCode == StatusCodes.Status200OK)
                            {
                                paymentComplete = true;
                            }
                            else
                            {
                                apiResponse.Errors = apiResposneInv.Errors;
                            }
                        }
                        else
                        {
                            paymentComplete = true;
                        }
                    }

                    transaction.Complete();


                }


                if (paymentComplete)
                {
                    await StorePaymentDetailMessage(baseHttpRequestContext.OrgCode, new List<PaymentDetail>() { paymentDetail }, false);//TODO:Send message to Service Bus for EOD report
                    await StorePaymentDetailMessageForDepositReport(baseHttpRequestContext.OrgCode, new List<PaymentDetail>() { paymentDetail }, (int)EODRequestDataType.PaymentDetailUpdate);
                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    apiResponse.Message = "Success";
                    apiResponse.Result = paymentDetail;
                    return apiResponse;
                }

                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("Payment Detail cannot be updated now.");
                return apiResponse;
            }


            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            apiResponse.Message = "Failure";
            apiResponse.Errors.Add("Payment Detail cannot be updated now.");
            return apiResponse;

        }

        public async  Task<ApiResponse<long?>> DeleteInvoicePayment(BaseHttpRequestContext baseHttpRequestContext, long id, DeleteObject deleteObject,TransferObject transferObject)
        {
            int orgId = baseHttpRequestContext.OrgId;
            long loggedInUser = baseHttpRequestContext.UserId;
            ApiResponse<long?> apiResponse = new();
            PatientCredit patientCreditDB = null;
            PaymentDetail paymentDetailDB = await _paymentDAL.GetPaymentDetails(id, true);
            List<short> lstValidPaymentTypes = new List<short>()
                                                {
                                                    (short)PaymentType.Deposit,
                                                    (short)PaymentType.Invoice
                                                };
            if(paymentDetailDB==null || paymentDetailDB .StatusId==(short)Status.Deleted || !lstValidPaymentTypes.Contains(paymentDetailDB.PaymentTypeId) || paymentDetailDB.CRAmount==null || paymentDetailDB.CRAmount==0 || (deleteObject is not null && paymentDetailDB.BankDate != null))
            {
                apiResponse.Message = "Failure";
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = default(long);
                apiResponse.Errors.Add("Payment Transaction cannot be deleted for the selected transaction.");
                return apiResponse;
            }
            long paymentCreatedUser = (paymentDetailDB.CreatedBy==null)?0:(long)paymentDetailDB.CreatedBy;

            if(paymentCreatedUser > default(long))
            {
                List<int> systemRoles = new List<int> { 6, 7 };
                int roleId = await GetRoleId(paymentCreatedUser, baseHttpRequestContext);
                if(roleId ==0 || systemRoles.Contains(roleId))
                {
                    apiResponse.Message = "Failure";
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Result = default(long);
                    apiResponse.Errors.Add("Payment Transaction cannot be deleted for the selected transaction.");
                    return apiResponse;
                }
            }

            if (paymentDetailDB is not null && ((deleteObject is not null && !string.IsNullOrWhiteSpace(deleteObject.DeleteReason)) /*|| (transferObject is not null && transferObject.PatientDetailsId > 0 )*/))
            {

                paymentDetailDB.ModifiedBy = baseHttpRequestContext.UserId;
                paymentDetailDB.ModifiedDate = DateTime.UtcNow;
                paymentDetailDB.StatusId = (short)Status.Deleted;
                paymentDetailDB.DeleteReason = (deleteObject is not null) ? deleteObject.DeleteReason : transferObject.DeleteReason;
                paymentDetailDB.DeletedBy = loggedInUser;
                paymentDetailDB.DeletedDate = DateTime.UtcNow;

                if(paymentDetailDB.PaymentMethodTypeId==(short)PaymentMethodType.Patient_Credit) {

                    patientCreditDB = await _paymentDAL.GetPatientCreditFromPaymentId(paymentDetailDB.Id, baseHttpRequestContext.OrgId);
                    if(patientCreditDB is not null)
                    {
                        patientCreditDB.StatusId = (short)Status.Deleted;
                        patientCreditDB.ModifiedBy = loggedInUser;
                        patientCreditDB.ModifiedDate = DateTime.UtcNow;

                    }
                    else
                    {
                        apiResponse.Message = "Failure";
                        apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                        apiResponse.Result = default(long);
                        apiResponse.Errors.Add("Payment cannot be updated at this time.");
                        return apiResponse;
                    }


                }


                paymentDetailDB.PaymentInvoiceEpisodeItemsAssocs?.ToList().ForEach(x =>
                {
                    x.StatusId = (short)Status.Deleted;
                    x.ModifiedBy = loggedInUser;
                    x.ModifiedDate = DateTime.UtcNow;
                });
                bool paymentComplete = false;
                long creditRows =  0;
                using (var transaction = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
                {
                    int rows = await _paymentDAL.UpdatePaymentDetails(paymentDetailDB);

                    if (rows > 0)
                    {
                        if(patientCreditDB is not null)
                        {
                             creditRows = await _paymentDAL.UpdatePatientCreditDetails(patientCreditDB);
                        }
                        ApiResponse<string> apiResposneInv = await UpdateInvoiceForPaymentDelete(paymentDetailDB, paymentDetailDB.InvoiceDetailsId, baseHttpRequestContext);
                        if (apiResposneInv != null && apiResposneInv.StatusCode == StatusCodes.Status200OK)
                            paymentComplete = true;
                        else
                        {
                            apiResponse.Errors = apiResposneInv.Errors;
                        }
                    }
                    if (paymentComplete)
                        transaction.Complete();
                }

                if (paymentComplete)
                {
                     await StorePaymentDetailMessages(baseHttpRequestContext.OrgCode, id,(short)EODRequestDataType.Payment_Delete);//TODO:Send message to Service Bus for Split Payments-invoicepayment
                    

                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    apiResponse.Result = id;
                    return apiResponse;
                }

              
            }
            apiResponse.Message = "Failure";
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            apiResponse.Result = default(long);
            apiResponse.Errors.Add("Payment cannot be updated at this time.");
            return apiResponse;
        }

        private async Task<int> GetRoleId(long paymentCreatedUser, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<int> apiResponseAuth = new();
           
            string endpoint = "/auth/users/roleId/"+ paymentCreatedUser;
         
            string authSeviceUrl = _appSettings.ApiUrls["AuthServiceUrl"] + endpoint ;
            RestClient restClientAuth = new RestClient(authSeviceUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            apiResponseAuth = await restClientAuth.GetAsync<ApiResponse<int>>(authSeviceUrl);

            return (apiResponseAuth == null || apiResponseAuth.StatusCode!=StatusCodes.Status200OK) ? 0 : apiResponseAuth.Result;
        }

        private async Task StorePaymentDetailMessages(string orgCode, long id, short propertyType)
        {
            PaymentRequestDataModel paymentRequestDataModelForPayment = new PaymentRequestDataModel
            {
                OrgName = orgCode,
                PropertyType = propertyType,
                PropertyId = new long[] { id },
                PropertyValue = ""
            };
            Dictionary<string, string> paymentPros = new Dictionary<string, string>
                        {
                            { "RequestType","PaymentDelete"},
                            { "to",_configuration["AzureAD:ASBSubNameInvoicePayment"]}
                        };

            Dictionary<string, string> reportPros = new Dictionary<string, string>
                        {
                            { "RequestType","EODReport"},
                            { "to",_configuration["AzureAD:ASBSubNameEODReport"]}
                        };
            byte[] jsonContentByteArr = Encoding.GetEncoding("iso-8859-1").GetBytes(JsonConvert.SerializeObject(paymentRequestDataModelForPayment));

            if (propertyType == (short)EODRequestDataType.Payment_Delete)
            {
                await _asbMessageSenderHelper.MessageSenderTopic(jsonContentByteArr, paymentPros, _configuration["AzureAD:AzSBConnStringPayment"], _configuration["AzureAD:ASBTopicPayment"]);

                await _asbMessageSenderHelper.MessageSenderTopic(jsonContentByteArr, reportPros, _configuration["AzureAD:AzSBConnStringPayment"], _configuration["AzureAD:ASBTopicPayment"]);

            }

        }

        private async Task<ApiResponse<string>> UpdateInvoiceForPaymentDelete(PaymentDetail paymentDetail, long invoiceDetailsId, BaseHttpRequestContext baseHttpRequestContext)
        {
            InvoiceDetailUpdate invoiceDetailUpdate = new();
            bool isInvoice = false;
           
            if (paymentDetail.CRAmount is not null && paymentDetail.StatusId==(short)Status.Deleted)
            {
                invoiceDetailUpdate.TotalAmoutPaid = (decimal)paymentDetail.CRAmount*-1;
                if (paymentDetail.PaymentTypeId == (short)PaymentType.Deposit)
                {
                    invoiceDetailUpdate.TotalDepositPaid =   (decimal)paymentDetail.CRAmount*-1 ;
                }

                if(paymentDetail.IsExcess == true)
                {
                    invoiceDetailUpdate.TotalExcessAmountPaid = (decimal)paymentDetail.CRAmount * -1;
                }
            }


           

            string invoiceSeviceUrl = _appSettings.ApiUrls["InvoiceServiceUrl"] + "/invoice/invoice_details/patch/" + invoiceDetailsId;
            RestClient restClientInvoice = new RestClient(invoiceSeviceUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            return await restClientInvoice.PutAsync<ApiResponse<string>>(invoiceSeviceUrl, invoiceDetailUpdate);

        }

        public async Task<ApiResponse<decimal?>> GetPatinetCreditTotalBAL(long patient_id, BaseHttpRequestContext baseHttpRequestContext, string strCompanyDetailsId =null)
        {
            ApiResponse<decimal?> apiResponse = new();
            int? companyDetailsid = null;
            if (!string.IsNullOrWhiteSpace(strCompanyDetailsId)){
                companyDetailsid = int.Parse(strCompanyDetailsId);
            }
             decimal? totalCredit = await _paymentDAL.GetPatinetCreditTotalDAL(patient_id, baseHttpRequestContext.OrgId, companyDetailsid);

            if (totalCredit != null)
            {
                apiResponse.Result = totalCredit;
                apiResponse.StatusCode = StatusCodes.Status200OK;
                return apiResponse;
            }
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            apiResponse.Errors.Add("Patient Credit Total cannot be fetched with the data provided.");
            return apiResponse;

        }

        private async Task<ApiResponse<string>> UpdateInvoiceForPaymentEdit(PaymentDetail paymentDetail, PaymentDetail paymentDetailDB, long invoiceDetailsId, BaseHttpRequestContext baseHttpRequestContext)
        {
            InvoiceDetailUpdate invoiceDetailUpdate = new();

            if (paymentDetailDB.IsExcess == true && paymentDetail.IsExcess == false)
            {

                invoiceDetailUpdate.TotalAmoutPaid = 0;
                invoiceDetailUpdate.TotalDepositPaid = 0;
                invoiceDetailUpdate.TotalExcessAmountPaid = (decimal)paymentDetail.CRAmount * -1;
            }
            else if (paymentDetailDB.IsExcess == false && paymentDetail.IsExcess == true)
            {

                invoiceDetailUpdate.TotalAmoutPaid = 0;
                invoiceDetailUpdate.TotalDepositPaid = 0;
                invoiceDetailUpdate.TotalExcessAmountPaid = (decimal)paymentDetail.CRAmount;

            }

            string invoiceSeviceUrl = _appSettings.ApiUrls["InvoiceServiceUrl"] + "/invoice/invoice_details/patch/" + invoiceDetailsId;
            RestClient restClientInvoice = new RestClient(invoiceSeviceUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            return await restClientInvoice.PutAsync<ApiResponse<string>>(invoiceSeviceUrl, invoiceDetailUpdate);

        }

    }
}
