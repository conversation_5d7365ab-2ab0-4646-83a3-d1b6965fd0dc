﻿using Capstone2.RestServices.MedicalSchedule.Context;
using Capstone2.RestServices.MedicalSchedule.Interfaces;
using Capstone2.RestServices.MedicalSchedule.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Capstone2.RestServices.MedicalSchedule.Services
{
    public class UploaderDAL : IUploaderDAL
    {
        public readonly ReadOnlyMedicalScheduleDBContext _readOnlyDbContext;
        public readonly UpdatableMedicalScheduleDBContext _updatableDBContext;
        public UploaderDAL(ReadOnlyMedicalScheduleDBContext readOnlyDbContext, UpdatableMedicalScheduleDBContext updatableDBContext)
        {
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
        }

        /// <summary>
        /// Method to add medicare data to Upload table
        /// </summary>
        /// <param name="inputUpload"></param>
        /// <returns></returns>
        public async Task<long> AddUploadAsync(Upload inputUpload)
        {
            await _updatableDBContext.Uploads.AddAsync(inputUpload);
            int rows = await _updatableDBContext.SaveChangesAsync();
            if (rows > 0)
                return inputUpload.Id;
            return 0;
        }


        /// <summary>
        /// Method to retrieve list of letter templates 
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        public async Task<QueryResultList<UploadView>> ListUploadDAL(int orgId, QueryModel queryModel, UploaderFilter filterModel)

        {
            var medicareQuery = from MU in _readOnlyDbContext.Uploads
                                join UD in _readOnlyDbContext.UserDetails on MU.ModifiedBy equals UD.Id
                                where MU.OrgId == orgId
                                select new UploadView
                                {
                                    Id = MU.Id,
                                    OrgId = MU.OrgId,
                                    FileName = MU.FileName,
                                    FileDetailsId = MU.FileDetailsId,
                                    UploaderTypeId = MU.UploaderTypeId,
                                    UploaderStatusId = MU.UploaderStatusId,
                                    ErrorDetails = MU.ErrorDetails,
                                    CreatedDate = MU.CreatedDate,
                                    ModifiedDate = MU.ModifiedDate,
                                    ModifiedBy = MU.ModifiedBy,
                                    Modifier = new UserDetails
                                    {
                                        Id = UD.Id,
                                        OrgId = UD.OrgId,
                                        FirstName = UD.FirstName,
                                        SurName = UD.SurName,
                                        TitleId = UD.TitleId
                                    },

                                };

            if (filterModel is not null)
            {
                if (filterModel.UploaderTypeId is not null && filterModel.UploaderTypeId.Any())
                {
                    medicareQuery = medicareQuery.Where(x => filterModel.UploaderTypeId.Contains(x.UploaderTypeId));
                }
            }



            if (!(string.IsNullOrWhiteSpace(queryModel.SortOrder)) && !(string.IsNullOrWhiteSpace(queryModel.SortTerm)))
            {
                medicareQuery = SortUpload(medicareQuery, queryModel.SortTerm, queryModel.SortOrder);
            }

            var paginatedList = await CreatePaginatedListAsync(medicareQuery, queryModel);
            QueryResultList<UploadView> queryList = new QueryResultList<UploadView>();
            if (paginatedList != null)
            {
                queryList.ItemRecords = paginatedList;
                queryList.CurrentCount = paginatedList.Count();
            }
            else
            {
                queryList.ItemRecords = null;
                queryList.CurrentCount = 0;
            }
            queryList.TotalCount = medicareQuery.Count();
            queryList.PageNumber = queryModel.PageNumber;
            queryList.PageSize = queryModel.PageSize;

            return queryList;
        }

        private async Task<List<UploadView>> CreatePaginatedListAsync(IQueryable<UploadView> medicareQuery, QueryModel queryModel)
        {
            if (queryModel.PageNumber > 0 && queryModel.PageSize > 0)
            {
                if (medicareQuery.Any())
                {
                    List<UploadView> paginatedList = await medicareQuery.Skip((queryModel.PageNumber - 1) * queryModel.PageSize)
                 .Take(queryModel.PageSize).ToListAsync();

                    return paginatedList;
                }
            }
            return null;
        }

        private IQueryable<UploadView> SortUpload(IQueryable<UploadView> medicareQuery, string sortTerm, string sortOrder)
        {
            switch (sortTerm.ToLower())
            {
                case "id":
                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            medicareQuery = medicareQuery.OrderBy(x => x.Id);
                        }
                        else
                        {
                            medicareQuery = medicareQuery.OrderByDescending(x => x.Id);
                        }
                        break;
                    }
                case "ModifiedDate":
                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            medicareQuery = medicareQuery.OrderBy(x => x.ModifiedDate);
                        }
                        else
                        {
                            medicareQuery = medicareQuery.OrderByDescending(x => x.ModifiedDate);
                        }
                        break;
                    }
                default:
                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            medicareQuery = medicareQuery.OrderBy(x => x.ModifiedDate);
                        }
                        else
                        {
                            medicareQuery = medicareQuery.OrderByDescending(x => x.ModifiedDate);
                        }
                        break;
                    }
            }

            return medicareQuery;
        }

        public async Task<Upload> GetLatestUploadDAL(Upload inputUpload)

        {
            var medicareQuery = await (from UP in _readOnlyDbContext.Uploads
                                       where UP.UploaderTypeId == inputUpload.UploaderTypeId
                                       select UP).OrderByDescending(m => m.Id).FirstOrDefaultAsync();

            return medicareQuery;

        }

        public async Task<long> AddHFUploadAsync(HFUpload inputHFUpload)
        {
            await _updatableDBContext.HFUploads.AddAsync(inputHFUpload);
            int rows = await _updatableDBContext.SaveChangesAsync();
            if (rows > 0)
                return inputHFUpload.Id;
            return 0;
        }

        public async Task<QueryResultList<HFUploadView>> ListHFUploadDAL(int orgId, QueryModel queryModel, HFUploaderFilter filterModel)
        {
            var hfQuery = from MU in _readOnlyDbContext.HFUploads
                                join UD in _readOnlyDbContext.UserDetails on MU.ModifiedBy equals UD.Id
                                where MU.OrgId == orgId
                                select new HFUploadView
                                {
                                    Id = MU.Id,
                                    OrgId = MU.OrgId,
                                    FileName = MU.FileName,
                                    FileDetailsId = MU.FileDetailsId,
                                    StateId = MU.StateId,
                                    EffectiveDate = MU.EffectiveDate,
                                    HealthFundGroupsId = MU.HealthFundGroupsId,
                                    UploaderStatusId = MU.UploaderStatusId,
                                    ErrorDetails = MU.ErrorDetails,
                                    CreatedDate = MU.CreatedDate,
                                    ModifiedDate = MU.ModifiedDate,
                                    ModifiedBy = MU.ModifiedBy,
                                    Modifier = new UserDetails
                                    {
                                        Id = UD.Id,
                                        OrgId = UD.OrgId,
                                        FirstName = UD.FirstName,
                                        SurName = UD.SurName,
                                        TitleId = UD.TitleId
                                    },

                                };

            if (filterModel is not null)
            {
                if (filterModel.StateId is not null)
                {
                    hfQuery = hfQuery.Where(x => x.StateId == filterModel.StateId);
                }
                if (filterModel.HealthFundGroupsId is not null)
                {
                    hfQuery = hfQuery.Where(x => x.HealthFundGroupsId == filterModel.HealthFundGroupsId);
                }
            }

            if (!(string.IsNullOrWhiteSpace(queryModel.SortOrder)) && !(string.IsNullOrWhiteSpace(queryModel.SortTerm)))
            {
                hfQuery = SortHFUpload(hfQuery, queryModel.SortTerm, queryModel.SortOrder);
            }

            List<HFUploadView> paginatedList = await CreateHFPaginatedListAsync(hfQuery, queryModel);
            QueryResultList<HFUploadView> queryList = new QueryResultList<HFUploadView>();
            if (paginatedList != null)
            {
                queryList.ItemRecords = paginatedList;
                queryList.CurrentCount = paginatedList.Count();
            }
            else
            {
                queryList.ItemRecords = null;
                queryList.CurrentCount = 0;
            }
            queryList.TotalCount = hfQuery.Count();
            queryList.PageNumber = queryModel.PageNumber;
            queryList.PageSize = queryModel.PageSize;

            return queryList;

        }

        private IQueryable<HFUploadView> SortHFUpload(IQueryable<HFUploadView> hfQuery, string sortTerm, string sortOrder)
        {
            switch (sortTerm.ToLower())
            {
                case "id":
                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            hfQuery = hfQuery.OrderBy(x => x.Id);
                        }
                        else
                        {
                            hfQuery = hfQuery.OrderByDescending(x => x.Id);
                        }
                        break;
                    }
                case "ModifiedDate":
                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            hfQuery = hfQuery.OrderBy(x => x.ModifiedDate);
                        }
                        else
                        {
                            hfQuery = hfQuery.OrderByDescending(x => x.ModifiedDate);
                        }
                        break;
                    }
                default:
                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            hfQuery = hfQuery.OrderBy(x => x.ModifiedDate);
                        }
                        else
                        {
                            hfQuery = hfQuery.OrderByDescending(x => x.ModifiedDate);
                        }
                        break;
                    }
            }

            return hfQuery;
        }

        private async Task<List<HFUploadView>> CreateHFPaginatedListAsync(IQueryable<HFUploadView> hfQuery, QueryModel queryModel)
        {
            if (queryModel.PageNumber > 0 && queryModel.PageSize > 0)
            {
                if (hfQuery.Any())
                {
                    List<HFUploadView> paginatedList = await hfQuery.Skip((queryModel.PageNumber - 1) * queryModel.PageSize)
                 .Take(queryModel.PageSize).ToListAsync();

                    return paginatedList;
                }
            }
            return null;
        }


    }

}
