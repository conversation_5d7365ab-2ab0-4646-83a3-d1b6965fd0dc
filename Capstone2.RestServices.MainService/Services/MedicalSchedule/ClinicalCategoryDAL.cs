﻿using Capstone2.RestServices.Master.Context;
using Capstone2.RestServices.MedicalSchedule.Context;
using Capstone2.RestServices.MedicalSchedule.Interfaces;
using Capstone2.RestServices.MedicalSchedule.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Capstone2.RestServices.MedicalSchedule.Services
{
    public class ClinicalCategoryDAL : IClinicalCategoryDAL
    {
        public readonly ReadOnlyMedicalScheduleDBContext _readOnlyDbContext;
        public readonly UpdatableMedicalScheduleDBContext _updatableDBContext;
        public readonly ReadOnlyMasterDBContext _readMasterDbContext;
        public ClinicalCategoryDAL(ReadOnlyMedicalScheduleDBContext readOnlyDbContext, UpdatableMedicalScheduleDBContext updatableDBContext, ReadOnlyMasterDBContext readMasterDbContext)
        {
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
            _readMasterDbContext = readMasterDbContext;
        }
        public async Task<List<ClinicalCategory>> GetClinicalCategoryDataListDAL()
        {
            return await _readMasterDbContext.ClinicalCategories.Where(x => x.StatusId == (short)Status.Active).ToListAsync();
        }
        /// <summary>
        /// Method to list ClinicalCategory Data Items
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        public async Task<QueryResultList<ClinicalCategory>> ListClinicalCategoryDataDAL(int orgId, QueryModel queryModel, List<ClinicalCategory> responseData, ClinicalCategoryFilter filterModel)
        {
           IEnumerable<ClinicalCategory> clinicalCategoryCollection = from CC in responseData
                                                                 where CC.StatusId == (short)Status.Active
                                                                 select CC;


            //IQueryable<ListMbsData> mbsItemQuery = mbsItemCollection.AsQueryable<ListMbsData>();
            if (filterModel != null)
            {
   
                if (filterModel.MbsLvl1Ids is not null && filterModel.MbsLvl1Ids.Any())
                {
                    clinicalCategoryCollection = clinicalCategoryCollection.Where(x => filterModel.MbsLvl1Ids.Contains(x.Id));
                }
            }
                if (!(string.IsNullOrWhiteSpace(queryModel.SearchTerm)))
            {
                clinicalCategoryCollection = SearchClinicalCategoryItems(clinicalCategoryCollection, queryModel.SearchTerm);

            }

            if (!(string.IsNullOrWhiteSpace(queryModel.SortOrder)) && !(string.IsNullOrWhiteSpace(queryModel.SortTerm)))
            {
                clinicalCategoryCollection = SortClinicalCategoryItems(clinicalCategoryCollection, queryModel.SortTerm, queryModel.SortOrder);
            }
            QueryResultList<ClinicalCategory> paginatedList = await PaginatedResultListForItems(clinicalCategoryCollection, queryModel);

            return paginatedList;
        }

        private IEnumerable<ClinicalCategory> SearchClinicalCategoryItems(IEnumerable<ClinicalCategory> clinicalCategoryCollection, string searchTerm)
        {
            return clinicalCategoryCollection.Where(s => s.Name.ToLower().Contains(searchTerm.ToLower()));
        }

        private async Task<QueryResultList<ClinicalCategory>> PaginatedResultListForItems(IEnumerable<ClinicalCategory> clinicalCategoryCollection, QueryModel queryModel)
        {
            QueryResultList<ClinicalCategory> queryList = new QueryResultList<ClinicalCategory>();
            List<ClinicalCategory> paginatedList = null;
            if (queryModel.PageNumber > 0 && queryModel.PageSize > 0)
            {
                if (clinicalCategoryCollection.Any())
                {
                    paginatedList = clinicalCategoryCollection.Skip((queryModel.PageNumber - 1) * queryModel.PageSize)
                 .Take(queryModel.PageSize).ToList();
                }
            }

            if (paginatedList != null)
            {
                queryList.ItemRecords = paginatedList;
                queryList.PageNumber = queryModel.PageNumber;
                queryList.PageSize = queryModel.PageSize;
                queryList.CurrentCount = paginatedList.Count();
            }
            else
            {
                queryList.ItemRecords = null;
                queryList.CurrentCount = 0;
            }
            queryList.TotalCount = clinicalCategoryCollection.Count();
            queryList.PageNumber = queryModel.PageNumber;
            queryList.PageSize = queryModel.PageSize;

            return queryList;
        }

        private IEnumerable<ClinicalCategory> SortClinicalCategoryItems(IEnumerable<ClinicalCategory> clinicalCategoryCollection, string sortTerm, string sortOrder)
        {
            switch (sortTerm.ToLower())
            {

                case "id":

                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            clinicalCategoryCollection = clinicalCategoryCollection.OrderBy(x => x.Id);
                        }
                        else
                        {

                            clinicalCategoryCollection = clinicalCategoryCollection.OrderByDescending(x => x.Id);

                        }
                        break;
                    }
                case "name":

                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            clinicalCategoryCollection = clinicalCategoryCollection.OrderBy(x => x.Name);
                        }
                        else
                        {

                            clinicalCategoryCollection = clinicalCategoryCollection.OrderByDescending(x => x.Name);

                        }
                        break;
                    }
                default:
                    clinicalCategoryCollection = clinicalCategoryCollection.OrderByDescending(x => x.Id);

                    break;
            }
            return clinicalCategoryCollection;
        }
    }

}
