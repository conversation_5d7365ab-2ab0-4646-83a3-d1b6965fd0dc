﻿using AutoMapper;
using Capstone2.Framework.Business.Common;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.Master.Common;

using Capstone2.RestServices.MedicalSchedule.Interfaces;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;

namespace Capstone2.RestServices.MedicalSchedule.Services
{
    public class TheatreBandingDataBAL : ITheatreBandingDataBAL
    {
        public IMapper _mapper;
        public readonly AppSettings _appSettings;
        public readonly ITheatreBandingDataDAL _theatreBindingDataDAL;
        private readonly HttpClient client;
        private IDistributedCacheHelper _redisCache;

        public TheatreBandingDataBAL(IMapper mapper, IOptions<AppSettings> appSettings, ITheatreBandingDataDAL theatreBindingDataDAL, IDistributedCacheHelper redisCache)
        {
            _mapper = mapper;
            _appSettings = appSettings.Value;
            _theatreBindingDataDAL = theatreBindingDataDAL;
            _redisCache = redisCache;
        }

        /// <summary>
        /// Get List of TheatreBinding data
        /// </summary>
        /// <returns></returns>
        public async Task<ApiResponse<QueryResultList<ListTheatreBandingData>>> GetTheatreBandingDataListBAL(BaseHttpRequestContext baseHttpRequestContext, QueryModel queryModel)
        {
            var orgId = baseHttpRequestContext.OrgId;
            List<TheatreBandingData> responseData = await _redisCache.GetFromCache<List<TheatreBandingData>>($"{CachedKeys.Tbl_TheatreBandingData}");
            QueryResultList<ListTheatreBandingData> theatreBandingDataList = null;
            if (responseData == null)
            {
                responseData = await _theatreBindingDataDAL.GetTheatreBandingDataListDAL();
                await _redisCache.SetIntoCache(responseData, $"{CachedKeys.Tbl_TheatreBandingData}");
            }
            theatreBandingDataList = await _theatreBindingDataDAL.ListTheatreBandingDataDAL(orgId, queryModel, responseData);

            var apiResponse = new ApiResponse<QueryResultList<ListTheatreBandingData>>
            {
                Result = theatreBandingDataList,
                Message = "Success",
                StatusCode = StatusCodes.Status200OK
            };
            return apiResponse;
        }
    }
}
