﻿using AutoMapper;
using Capstone2.Framework.Business.Common;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.Master.Common;
using Capstone2.RestServices.MedicalSchedule.Interfaces;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;

namespace Capstone2.RestServices.MedicalSchedule.Services
{
    public class MbsClinicalCategoryDataBAL : IMbsClinicalCategoryDataBAL
    {
        public IMapper _mapper;
        public readonly AppSettings _appSettings;
        public readonly IMbsClinicalCategoryDataDAL _mbsClinicalCategoryDataDAL;
        private readonly HttpClient client;
        private IDistributedCacheHelper _redisCache;

        public MbsClinicalCategoryDataBAL(IMapper mapper, IOptions<AppSettings> appSettings, IMbsClinicalCategoryDataDAL mbsClinicalCategoryDataDAL, IDistributedCacheHelper redisCache)
        {
            _mapper = mapper;
            _appSettings = appSettings.Value;
            _mbsClinicalCategoryDataDAL = mbsClinicalCategoryDataDAL;
            _redisCache = redisCache;
        }

        /// <summary>
        /// Get List of MbsClinicalCategory data
        /// </summary>
        /// <returns></returns>
        public async Task<ApiResponse<QueryResultList<MbsClinicalCategoryData>>> GetMbsClinicalCategoryDataListBAL(BaseHttpRequestContext baseHttpRequestContext, QueryModel queryModel)
        {
            var orgId = baseHttpRequestContext.OrgId;
            List<MbsClinicalCategoryData> responseData = await _redisCache.GetFromCache<List<MbsClinicalCategoryData>>($"{CachedKeys.Tbl_MbsClinicalCategoryData}");
            QueryResultList<MbsClinicalCategoryData> mbsClinicalCategoryDataList = null;
            if (responseData == null)
            {
                responseData = await _mbsClinicalCategoryDataDAL.GetMbsClinicalCategoryDataListDAL();
                await _redisCache.SetIntoCache(responseData, $"{CachedKeys.Tbl_MbsClinicalCategoryData}");
            }
            mbsClinicalCategoryDataList = await _mbsClinicalCategoryDataDAL.ListMbsClinicalCategoryDataDAL(orgId, queryModel, responseData);

            var apiResponse = new ApiResponse<QueryResultList<MbsClinicalCategoryData>>
            {
                Result = mbsClinicalCategoryDataList,
                Message = "Success",
                StatusCode = StatusCodes.Status200OK
            };
            return apiResponse;
        }

        public async Task<ApiResponse<List<long>>> GetItemNumsList(BaseHttpRequestContext baseHttpRequestContext, short clincalCategoryId)
        {
            List<MbsClinicalCategoryData> mbsClinicalCategoryDataList = await _redisCache.GetFromCache<List<MbsClinicalCategoryData>>($"{CachedKeys.Tbl_MbsClinicalCategoryData}");
            if (mbsClinicalCategoryDataList == null)
            {
                mbsClinicalCategoryDataList = await _mbsClinicalCategoryDataDAL.GetMbsClinicalCategoryDataListDAL();
                await _redisCache.SetIntoCache(mbsClinicalCategoryDataList, $"{CachedKeys.Tbl_MbsClinicalCategoryData}");
            }
            List<long> ListofchildMBS = (from MCC in mbsClinicalCategoryDataList
                                         where MCC.StatusId == (short)Status.Active && (MCC.ClinicalCategoryId == clincalCategoryId)
                                         select MCC.ItemNum).ToList();
            var apiResponse = new ApiResponse<List<long>>
            {
                Result = ListofchildMBS,
                Message = "Success",
                StatusCode = StatusCodes.Status200OK
            };
            return apiResponse;
        }
    }
}
