﻿using Capstone2.Framework.Business.Common;
using Capstone2.RestServices.MedicalSchedule.Context;
using Capstone2.RestServices.MedicalSchedule.Interfaces;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Data.SqlClient;
using Capstone2.RestServices.Master.Context;
namespace Capstone2.RestServices.MedicalSchedule.Services
{
    public class MbsDAL : IMbsDAL
    {
        public readonly ReadOnlyMedicalScheduleDBContext _readOnlyDbContext;
        public readonly UpdatableMedicalScheduleDBContext _updatableDBContext;
        public readonly ReadOnlyMasterDBContext _readMasterDbContext;
        private IDistributedCacheHelper _redisCache;
        public MbsDAL(ReadOnlyMedicalScheduleDBContext readOnlyDbContext, UpdatableMedicalScheduleDBContext updatableDBContext, ReadOnlyMasterDBContext readMasterDbContext, IDistributedCacheHelper redisCache)
        {
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
            _readMasterDbContext = readMasterDbContext;
            _redisCache = redisCache;
        }

        public async Task<string> GetOrgcode(int orgId)
        {
            var orgDetails = await _readOnlyDbContext.Organisation
                  .Where(p => p.Id == orgId).FirstOrDefaultAsync();
            return orgDetails.OrgCode;
        }

        public async Task<FileDetails> GetFileDetails(int orgId, long Id)
        {
            return await _readOnlyDbContext.FileDetails
                    .Where(p => p.Id == Id).FirstOrDefaultAsync();
        }

        public async Task<FileRepository> GetFileRepositoryById(long Id)
        {
            return await _readOnlyDbContext.FileRepository
                    .Where(p => p.Id == Id).FirstOrDefaultAsync();
        }

        public async Task<List<MbsData>> GetMbsDataListDAL()
        {
            return await _readMasterDbContext.MbsData.ToListAsync();
        }
        
        public async Task<List<MbsClinicalCategoryData>> GetMbsClinicalCategoryDataListDAL()
        {
            return await _readMasterDbContext.MbsClinicalCategoryData.ToListAsync();
        }

        public async Task<List<ClinicalCategory>> GetClinicalCategoryDataListDAL()
        {
            return await _readMasterDbContext.ClinicalCategories.ToListAsync();
        }
        public async Task<List<DvaData>> GetDvaDataListDAL()
        {
            return await _readMasterDbContext.DvaData.ToListAsync();
        }
        public async Task<List<TheatreBandingData>> GetTheatreBandingDataListDAL()
        {
            return await _readMasterDbContext.TheatreBandingData.ToListAsync();
        }
        public async Task<MbsDataView> GetMbsDataDAL(long itemnum, int orgId, List<MbsData> mbsDataList, List<MbsClinicalCategoryData> mbsClinicalCategoryDataList, List<ClinicalCategory> clinicalCategoryDataList, List<DvaData> dvaDataList, List<TheatreBandingData> theatreBandingDataList)
        {
            var masterResult =  (from MBS in mbsDataList
                                      where MBS.ItemNum == itemnum && MBS.StatusId == (short)Status.Active
                                      join DVA in dvaDataList on new { MBS.ItemNum, MBS.StatusId } equals new { DVA.ItemNum, DVA.StatusId } into JC
                                      from DVA in JC.DefaultIfEmpty()
                                      join MCC in mbsClinicalCategoryDataList on new { MBS.ItemNum, MBS.StatusId } equals new { MCC.ItemNum, MCC.StatusId } into JC1
                                      from MCC in JC1.DefaultIfEmpty()
                                      //join CC in clinicalCategoryDataList on new { MCC.ClinicalCategoryId, MCC.StatusId } equals new { ClinicalCategoryId = CC.Id, CC.StatusId } into JC2
                                      //from CC in JC2.DefaultIfEmpty()
                                      join TBD in theatreBandingDataList on new { MBS.ItemNum, MBS.StatusId } equals new { TBD.ItemNum, TBD.StatusId } into JC3
                                      from TBD in JC3.DefaultIfEmpty()

                                      select new MbsDataView
                                      {
                                          Id = MBS.Id,
                                          ItemNum = MBS.ItemNum,
                                          Description = MBS.Description,
                                          ScheduleFee = MBS.ScheduleFee,
                                          Benefit75 = MBS.Benefit75,
                                          Benefit85 = MBS.Benefit85,
                                          Benefit100 = MBS.Benefit100,
                                          Group =MBS.Group,
                                          SubGroup=MBS.SubGroup,
                                          FeeType = MBS.FeeType,
                                          Category = MBS.Category,
                                          ClinicalCategoryId = (MCC == null) ? null : MCC.ClinicalCategoryId,
                                          ClinicalCategoryName = (MCC == null) ? null : (from CC in clinicalCategoryDataList where CC.Id==MCC.ClinicalCategoryId select CC.Name).FirstOrDefault(),
                                          DvaData = new DvaView
                                          {
                                              Id = DVA == null ? 0 : DVA.Id,
                                              LMOFee = DVA == null ? null : DVA.LMOFee,
                                              RMFSInHospitalFee = DVA == null ? null : DVA.RMFSInHospitalFee,
                                              RMFSOutOfHospitalFee = DVA == null ? null : DVA.RMFSOutOfHospitalFee

                                          },
                                          TheatreData = new TheatreView
                                          {
                                              Id = TBD == null ? 0 : TBD.Id,
                                              Band = TBD == null ? null : TBD.Band,
                                              PatientClassOvernight = TBD == null ? null : TBD.PatientClassOvernight,
                                              PatientClassSameDay = TBD == null ? null : TBD.PatientClassSameDay
                                          }
                                      }).FirstOrDefault();

            return masterResult;

        }

        /// <summary>
        /// Method to list Mbs Data Items
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="queryModel"></param>
        /// <param name="filterModel"></param>
        /// <returns></returns>
        public async Task<QueryResultList<ListMbsData>> ListMbsDataDAL(int orgId, QueryModel queryModel, List<MbsData> responseData, List<long> listofChildMbs, MbsDataFilterModel filterModel)
        {
            IEnumerable<ListMbsData> mbsItemCollection = null;

            if (listofChildMbs is not null)
            {
                mbsItemCollection = responseData.Where(d => d.StatusId == (short)Status.Active && listofChildMbs.Contains(d.ItemNum)).Select(data => new ListMbsData
                {
                    Id = data.Id,
                    ItemNum = data.ItemNum,
                    Description = data.Description
                });
            }
            else if (filterModel is not null && filterModel.ListMbsItemsOnSearch is not null && filterModel.ListMbsItemsOnSearch.Any()&& ( filterModel.SearchAll is  null || filterModel.SearchAll==false))
            {
                mbsItemCollection = responseData.Where(d => d.StatusId == (short)Status.Active && filterModel.ListMbsItemsOnSearch.Contains(d.ItemNum)).Select(data => new ListMbsData
                {
                    Id = data.Id,
                    ItemNum = data.ItemNum,
                    Description = data.Description
                });
            }
            else if (filterModel is not null && filterModel.ListMbsItemsIncDel is not null && filterModel.ListMbsItemsIncDel.Any() && (filterModel.SearchAll is null || filterModel.SearchAll == false))
            {
                mbsItemCollection = responseData.Where(d => filterModel.ListMbsItemsIncDel.Contains(d.ItemNum)).Select(data => new ListMbsData
                {
                    Id = data.Id,
                    ItemNum = data.ItemNum,
                    Description = data.Description,
                    StatusId=data.StatusId
                });
            }
            else
            {
                mbsItemCollection = responseData.Where(d => d.StatusId == (short)Status.Active).Select(data => new ListMbsData
                {
                    Id = data.Id,
                    ItemNum = data.ItemNum,
                    Description = data.Description
                });
            }
            //IQueryable<ListMbsData> mbsItemQuery = mbsItemCollection.AsQueryable<ListMbsData>();
            if(filterModel != null)
            {
                if (filterModel.ListMbsItems is not null && filterModel.ListMbsItems.Any())
                {
                    mbsItemCollection = mbsItemCollection.Where(x => filterModel.ListMbsItems.Contains(x.ItemNum));
                }
            }
            if (!(string.IsNullOrWhiteSpace(queryModel.SearchTerm)) && (filterModel.ListMbsItems is  null || filterModel.ListMbsItems.Count() == 0))
            {
                mbsItemCollection = SearchMbsItems(mbsItemCollection, queryModel.SearchTerm);
                List<long> lstItemNum = filterModel.ListMbsItemsOnSearch.Except(mbsItemCollection.Select(x => x.ItemNum).ToList()).ToList();
                if(lstItemNum is not null && lstItemNum.Count > 0)
                {
                    IEnumerable<ListMbsData> mbsItemCollection2 = null;
                    mbsItemCollection2 = responseData.Where(d => d.StatusId == (short)Status.Active && lstItemNum.Contains(d.ItemNum)).Select(data => new ListMbsData
                    {
                        Id = data.Id,
                        ItemNum = data.ItemNum,
                        Description = data.Description
                    });
                    if (mbsItemCollection2 is not null && mbsItemCollection2.Count() > 0)
                        mbsItemCollection = mbsItemCollection.Concat(mbsItemCollection2);
                }
            }

            if (!(string.IsNullOrWhiteSpace(queryModel.SortOrder)) && !(string.IsNullOrWhiteSpace(queryModel.SortTerm)))
            {
                mbsItemCollection = SortMbsItems(mbsItemCollection, queryModel.SortTerm, queryModel.SortOrder);
            }
            QueryResultList<ListMbsData> paginatedList = await PaginatedResultListForItems(mbsItemCollection, queryModel);

            return paginatedList;
        }

        private IEnumerable<ListMbsData> SearchMbsItems(IEnumerable<ListMbsData> mbsItemCollection, string searchTerm)
        {
            return mbsItemCollection.Where(s => s.Description.ToLower().Contains(searchTerm.ToLower()) || s.ItemNum.ToString() == searchTerm);
        }

        private async Task<QueryResultList<ListMbsData>> PaginatedResultListForItems(IEnumerable<ListMbsData> mbsItemCollection, QueryModel queryModel)
        {
            QueryResultList<ListMbsData> queryList = new QueryResultList<ListMbsData>();
            List<ListMbsData> paginatedList = null;
            if (queryModel.PageNumber > 0 && queryModel.PageSize > 0)
            {
                if (mbsItemCollection.Any())
                {
                    paginatedList = mbsItemCollection.Skip((queryModel.PageNumber - 1) * queryModel.PageSize)
                 .Take(queryModel.PageSize).ToList();
                }
            }

            if (paginatedList != null)
            {
                queryList.ItemRecords = paginatedList;
                queryList.PageNumber = queryModel.PageNumber;
                queryList.PageSize = queryModel.PageSize;
                queryList.CurrentCount = paginatedList.Count();
            }
            else
            {
                queryList.ItemRecords = null;
                queryList.CurrentCount = 0;
            }
            queryList.TotalCount = mbsItemCollection.Count();
            queryList.PageNumber = queryModel.PageNumber;
            queryList.PageSize = queryModel.PageSize;

            return queryList;
        }

        private IEnumerable<ListMbsData> SortMbsItems(IEnumerable<ListMbsData> mbsItemCollection, string sortTerm, string sortOrder)
        {
            switch (sortTerm.ToLower())
            {

                case "id":

                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            mbsItemCollection = mbsItemCollection.OrderBy(x => x.Id);
                        }
                        else
                        {

                            mbsItemCollection = mbsItemCollection.OrderByDescending(x => x.Id);

                        }
                        break;
                    }
                case "itemnum":

                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            mbsItemCollection = mbsItemCollection.OrderBy(x => x.ItemNum);
                        }
                        else
                        {

                            mbsItemCollection = mbsItemCollection.OrderByDescending(x => x.ItemNum);

                        }
                        break;
                    }


                default:
                    mbsItemCollection = mbsItemCollection.OrderBy(x => x.ItemNum);

                    break;
            }
            return mbsItemCollection;
        }

        public async Task<List<MbsDataView>> ListMbsDataBAL(QueryModel queryModel, MbsDataFilterModel filterModel, int orgId, List<MbsData> mbsDataList, List<MbsClinicalCategoryData> mbsClinicalCategoryDataList, List<ClinicalCategory> clinicalCategoryDataList, List<DvaData> dvaDataList, List<TheatreBandingData> theatreBandingDataList)
        {
            return (from MBS in mbsDataList
                                where filterModel.ListMbsItems.Contains(MBS.ItemNum) && MBS.StatusId == (short)Status.Active
                                join DVA in dvaDataList on new { MBS.ItemNum, MBS.StatusId } equals new { DVA.ItemNum, DVA.StatusId } into JC
                                from DVA in JC.DefaultIfEmpty()
                                //join MCC in mbsClinicalCategoryDataList on new { MBS.ItemNum, MBS.StatusId } equals new { MCC.ItemNum, MCC.StatusId } into JC1
                                //from MCC in JC1.DefaultIfEmpty()
                                //join CC in clinicalCategoryDataList on new { MCC.ClinicalCategoryId, MCC.StatusId } equals new { ClinicalCategoryId = CC.Id, CC.StatusId } into JC2
                                //from CC in JC2.DefaultIfEmpty()
                                join TBD in theatreBandingDataList on new { MBS.ItemNum, MBS.StatusId } equals new { TBD.ItemNum, TBD.StatusId } into JC3
                                from TBD in JC3.DefaultIfEmpty()

                                select new MbsDataView
                                {
                                    Id = MBS.Id,
                                    ItemNum = MBS.ItemNum,
                                    Description = MBS.Description,
                                    ScheduleFee = MBS.ScheduleFee,
                                    Benefit75 = MBS.Benefit75,
                                    Benefit85 = MBS.Benefit85,
                                    Benefit100 = MBS.Benefit100,
                                    Group = MBS.Group,
                                    SubGroup = MBS.SubGroup,
                                    Category = MBS.Category,
                                    FeeType = MBS.FeeType,
                                    //ClinicalCategoryId = (CC == null) ? null : CC.Id,
                                    //ClinicalCategoryName = (CC == null) ? null : CC.Name,
                                    DvaData = new DvaView
                                    {
                                        Id = DVA == null ? 0 : DVA.Id,
                                        LMOFee = DVA == null ? null : DVA.LMOFee,
                                        RMFSInHospitalFee = DVA == null ? null : DVA.RMFSInHospitalFee,
                                        RMFSOutOfHospitalFee = DVA == null ? null : DVA.RMFSOutOfHospitalFee

                                    },
                                    TheatreData = new TheatreView
                                    {
                                        Id = TBD == null ? 0 : TBD.Id,
                                        Band = TBD == null ? null : TBD.Band,
                                        PatientClassOvernight = TBD == null ? null : TBD.PatientClassOvernight,
                                        PatientClassSameDay = TBD == null ? null : TBD.PatientClassSameDay
                                    }
                                }).ToList();

           
        }
    }

}
