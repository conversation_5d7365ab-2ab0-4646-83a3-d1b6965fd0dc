﻿using AutoMapper;
using Azure;
using Azure.Messaging.EventGrid;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.MedicalSchedule.Interfaces;
using Capstone2.RestServices.MedicalSchedule.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Net.Http;
using System.Threading.Tasks;

namespace Capstone2.RestServices.MedicalSchedule.Services
{
    public class UploaderBAL : IUploaderBAL
    {
        public readonly IUploaderDAL _uploaderDAL;
        public IMapper _mapper;
        public readonly AppSettings _appSettings;
        public readonly IMbsDAL _mbsDAL;
        private readonly HttpClient client;


        public UploaderBAL(IUploaderDAL uploaderDAL, IMapper mapper, IOptions<AppSettings> appSettings, IMbsDAL mbsDAL)
        {
            _uploaderDAL = uploaderDAL;
            _mapper = mapper;
            _appSettings = appSettings.Value;
            _mbsDAL = mbsDAL;
        }

        /// <summary>
        /// Method to save a entry in Upload
        /// </summary>
        /// <param name="inputUpload"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> AddUploadBAL(Upload inputUpload, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<long?> apiResponse = new();

            int orgId = baseHttpRequestContext.OrgId;
            long loggedInUser = baseHttpRequestContext.UserId;
            inputUpload.ModifiedBy = loggedInUser;
            inputUpload.OrgId = orgId;
            inputUpload.UploaderStatusId = (short)UploaderStatus.In_Progress;
            inputUpload.ModifiedDate = DateTime.UtcNow;

            if(inputUpload.UploaderTypeId == (short)UploaderType.MBS_Schedule && (inputUpload.FileName.ToLower().Contains("mbs") == false))
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Not a valid File.Filename must Contain MBS";
                apiResponse.Result = null;
                return apiResponse;

            }


            if (baseHttpRequestContext.OrgCode != "adactin")
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Only adactin organisation is allowed to upload medical documents";
                apiResponse.Result = null;
                return apiResponse;
            }
            // check the status of previous upload of same uploadertype

            var latestUpload =await _uploaderDAL.GetLatestUploadDAL(inputUpload);
            if(latestUpload != null && latestUpload !=null && latestUpload.UploaderStatusId == (short)UploaderStatus.In_Progress)
            {

                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Previous Upload of the Type " + (UploaderType)inputUpload.UploaderTypeId + " Still In Progress.So Next File can not be uploaded";
                apiResponse.Result = null;
                return apiResponse;


            }


            long id = await _uploaderDAL.AddUploadAsync(inputUpload);
            if (id > 0)
            {
                var azureStatus = await  SendAzureFunctionRequest(inputUpload, baseHttpRequestContext.OrgCode);

                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success And Azure function send status"+ azureStatus;
                apiResponse.Result = id;
                return apiResponse;

            }      
            else
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failure";
                apiResponse.Result = null;
                return apiResponse;
            }
        }



        public async Task<int>  SendAzureFunctionRequest(Upload addedUpload, string orgCode)
        {
            var fileFromDB = await _mbsDAL.GetFileDetails(addedUpload.OrgId, (long)addedUpload.FileDetailsId);
            var RepoDetails = await _mbsDAL.GetFileRepositoryById(fileFromDB.FileRepositoryId);
            //var orgCode = await _mbsDAL.GetOrgcode(addedUpload.OrgId);

            //Get the Url of the file 
            var foldername = ((FileModuleType)RepoDetails.FileModuleTypeId).ToString().ToLower();
            string fileUrl = RepoDetails.Path + "/" + orgCode + "/" + foldername + "/" + fileFromDB.FileName;

            //send request to Azure Function 
            //send event to function 
            EventGridPublisherClient client = new EventGridPublisherClient(
                                                    new Uri(_appSettings.EventGridEndpoint),
                                                    new AzureKeyCredential(_appSettings.EventGridKey));

            EventData data = new()
            {
                UploadsId = addedUpload.Id,
                Url = fileUrl,
                UploaderTypeId = addedUpload.UploaderTypeId,
                OrgId = addedUpload.OrgId,
                OrgCode = orgCode

            };

            EventGridEvent newMessage = new("Uploader", "FileUploaded", "dataversion", data, null);
            var response = await client.SendEventAsync(newMessage);

            return response.Status;

        }


        /// <summary>
        /// Method to generate a paginated list of Medicare Uploads based on search criteria
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        public async Task<ApiResponse<QueryResultList<UploadView>>> ListUploadBAL(BaseHttpRequestContext baseHttpRequestContext, QueryModel queryModel)
        {
            ApiResponse<QueryResultList<UploadView>> apiResponse = new();
            int orgId = baseHttpRequestContext.OrgId;
            long loggedInUser = baseHttpRequestContext.UserId;

            if (baseHttpRequestContext.OrgCode != "adactin")
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Only adactin organisation is allowed to view medical documents";
                apiResponse.Result = null;
                return apiResponse;
            }

            UploaderFilter filterModel = PrepareFilterParameters(queryModel.Filter);


            var queryList = await _uploaderDAL.ListUploadDAL(orgId, queryModel, filterModel);
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result = queryList;
            return apiResponse;
        }

        private UploaderFilter PrepareFilterParameters(string filter)
        {
            if (!(String.IsNullOrEmpty(filter)))
            {
                return JsonConvert.DeserializeObject<UploaderFilter>(filter);
            }
            else
            {
                return null;
            }
        }

        private HFUploaderFilter PrepareHFFilterParameters(string filter)
        {
            if (!(String.IsNullOrEmpty(filter)))
            {
                return JsonConvert.DeserializeObject<HFUploaderFilter>(filter);
            }
            else
            {
                return null;
            }
        }





        //R and D on all concepts of Azure function 
        /*private async Task<long>  AddDataToMBSTable(long FileDetailsId)
        {  
            
            //send event to function 
            EventGridPublisherClient client = new EventGridPublisherClient(
                                                    new Uri("https://medicalscheduletopic.australiaeast-1.eventgrid.azure.net/api/events"),
                                                    new AzureKeyCredential("TbrjyucPCWsAdlovSTA1nT/gD6IlcP1E+KOyWaIZ5h0="));

            EventData data = new()
            {
                UploadsId = 12

             };

            EventGridEvent newMessage = new("Uploader", "FileUploaded", "dataversion", data, null);
            //newMessage.Topic = "medicalscheduletopic";

            var response = await client.SendEventAsync(newMessage);



           
            //get the url from DB 


            //get the blob from Azure blob 

              StorageSharedKeyCredential storagekey = new StorageSharedKeyCredential(_appSettings.StorageAccountName, _appSettings.StorageAccountKey);
              Uri bloburi = new Uri("https://capstone2dev2storageacc.blob.core.windows.net" + "/" + "caps" + "/" + "medicalschedule" + "/" + "MBS-XML-********.XML");
              BlobClient newblob = new BlobClient(bloburi, storagekey);
              if (await newblob.ExistsAsync())
              {

                  XmlSerializer serializer = new XmlSerializer(typeof(MBSDataList));
                  serializer.UnknownNode += new
                  XmlNodeEventHandler(serializer_UnknownNode);
                  serializer.UnknownAttribute += new
                  XmlAttributeEventHandler(serializer_UnknownAttribute);

                  var response = await newblob.DownloadAsync();

                  MBSDataList newList = new();
                  newList =  (MBSDataList)serializer.Deserialize(response.Value.Content);
                  var rows = await _mbsDAL.AddMbsDataAsync(newList.MBS_XML);
                  return rows;
               }


            return 0;


            //process the xml and add to table 
        }

        private void serializer_UnknownNode
         (object sender, XmlNodeEventArgs e)
        {
            Console.WriteLine("Unknown Node:" + e.Name + "\t" + e.Text);
        }

        private void serializer_UnknownAttribute
        (object sender, XmlAttributeEventArgs e)
        {
            System.Xml.XmlAttribute attr = e.Attr;
            Console.WriteLine("Unknown attribute " +
            attr.Name + "='" + attr.Value + "'");
        }*/

        /// <summary>
        /// Method to save a entry in Upload
        /// </summary>
        /// <param name="inputHfUpload"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> AddHfUploadBAL(HFUpload inputHfUpload, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<long?> apiResponse = new();

            int orgId = baseHttpRequestContext.OrgId;
            long loggedInUser = baseHttpRequestContext.UserId;
            inputHfUpload.ModifiedBy = loggedInUser;
            inputHfUpload.OrgId = orgId;
            inputHfUpload.UploaderStatusId = (short)UploaderStatus.In_Progress;
            inputHfUpload.ModifiedDate = DateTime.UtcNow;
           
            if (baseHttpRequestContext.OrgCode != "adactin")
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Only adactin organisation is allowed to upload HealthFund documents";
                apiResponse.Result = null;
                return apiResponse;
            }
            // check the status of previous upload of same uploadertype /* Need to check requirement if previous is failed should be stop from upload or allow*/

           /* var latestUpload = await _uploaderDAL.GetLatestUploadDAL(inputUpload);
            if (latestUpload != null && latestUpload != null && latestUpload.UploaderStatusId == (short)UploaderStatus.In_Progress)
            {

                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Previous Upload of the Type " + (UploaderType)inputUpload.UploaderTypeId + " Still In Progress.So Next File can not be uploaded";
                apiResponse.Result = null;
                return apiResponse;


            }*/


            long id = await _uploaderDAL.AddHFUploadAsync(inputHfUpload);
            if (id > 0)
            {
                var azureStatus = await SendHFAzureFunctionRequest(inputHfUpload, baseHttpRequestContext.OrgCode);

                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success And Azure function send status" + azureStatus;
                apiResponse.Result = id;
                return apiResponse;

            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failure";
                apiResponse.Result = null;
                return apiResponse;
            }
        }

       public async Task<ApiResponse<QueryResultList<HFUploadView>>> ListHFUploadBAL(BaseHttpRequestContext baseHttpRequestContext, QueryModel queryModel)
       {
            ApiResponse<QueryResultList<HFUploadView>> apiResponse = new();
            int orgId = baseHttpRequestContext.OrgId;
            long loggedInUser = baseHttpRequestContext.UserId;

            if (baseHttpRequestContext.OrgCode != "adactin")
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Only adactin organisation is allowed to view medical documents";
                apiResponse.Result = null;
                return apiResponse;
            }

            HFUploaderFilter filterModel = PrepareHFFilterParameters(queryModel.Filter);


            var queryList = await _uploaderDAL.ListHFUploadDAL(orgId, queryModel, filterModel);
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result = queryList;
            return apiResponse;

        }
        public async Task<int> SendHFAzureFunctionRequest(HFUpload addedUpload, string orgCode)
        {
            var fileFromDB = await _mbsDAL.GetFileDetails(addedUpload.OrgId, (long)addedUpload.FileDetailsId);
            var RepoDetails = await _mbsDAL.GetFileRepositoryById(fileFromDB.FileRepositoryId);
            //var orgCode = await _mbsDAL.GetOrgcode(addedUpload.OrgId);

            //Get the Url of the file 
            var foldername = ((FileModuleType)RepoDetails.FileModuleTypeId).ToString().ToLower();
            string fileUrl = RepoDetails.Path + "/" + orgCode + "/" + foldername + "/" + fileFromDB.FileName;

            //send request to Azure Function 
            //send event to function 
            EventGridPublisherClient client = new EventGridPublisherClient(
                                                    new Uri(_appSettings.EventGridEndpoint),
                                                    new AzureKeyCredential(_appSettings.EventGridKey));

            EventData data = new()
            {
                UploadsId = addedUpload.Id,
                Url = fileUrl,
                UploaderTypeId = (short)UploaderType.Health_Fund,
                OrgId = addedUpload.OrgId,
                OrgCode = orgCode

            };

            EventGridEvent newMessage = new("Uploader", "FileUploaded", "dataversion", data, null);
            var response = await client.SendEventAsync(newMessage);

            return response.Status;

        }



    }
}
