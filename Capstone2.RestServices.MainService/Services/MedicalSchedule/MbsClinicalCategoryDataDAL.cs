﻿using Capstone2.Framework.Business.Common;
using Capstone2.RestServices.Master.Context;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.MedicalSchedule.Context;
using Capstone2.RestServices.MedicalSchedule.Interfaces;
using Capstone2.RestServices.MedicalSchedule.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Capstone2.RestServices.MedicalSchedule.Services
{
    public class MbsClinicalCategoryDataDAL : IMbsClinicalCategoryDataDAL
    {
        public readonly ReadOnlyMedicalScheduleDBContext _readOnlyDbContext;
        public readonly UpdatableMedicalScheduleDBContext _updatableDBContext;
        public readonly ReadOnlyMasterDBContext _readMasterDbContext;
        public MbsClinicalCategoryDataDAL(ReadOnlyMedicalScheduleDBContext readOnlyDbContext, UpdatableMedicalScheduleDBContext updatableDBContext, ReadOnlyMasterDBContext readMasterDbContext)
        {
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
            _readMasterDbContext = readMasterDbContext;
        }
        public async Task<List<MbsClinicalCategoryData>> GetMbsClinicalCategoryDataListDAL()
        {
            return await _readMasterDbContext.MbsClinicalCategoryData.Where(x => x.StatusId == (short)Status.Active).ToListAsync();
        }
        /// <summary>
        /// Method to list ClinicalCategory Data Items
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        public async Task<QueryResultList<MbsClinicalCategoryData>> ListMbsClinicalCategoryDataDAL(int orgId, QueryModel queryModel, List<MbsClinicalCategoryData> responseData)
        {
            IEnumerable<MbsClinicalCategoryData> mbsClinicalCategoryCollection = from CC in responseData
                                                                       where CC.StatusId == (short)Status.Active
                                                                       select CC;


            if (!(string.IsNullOrWhiteSpace(queryModel.SearchTerm)))
            {
                mbsClinicalCategoryCollection = SearchMbsClinicalCategoryItems(mbsClinicalCategoryCollection, queryModel.SearchTerm);

            }

            if (!(string.IsNullOrWhiteSpace(queryModel.SortOrder)) && !(string.IsNullOrWhiteSpace(queryModel.SortTerm)))
            {
                mbsClinicalCategoryCollection = SortMbsClinicalCategoryItems(mbsClinicalCategoryCollection, queryModel.SortTerm, queryModel.SortOrder);
            }
            QueryResultList<MbsClinicalCategoryData> paginatedList = await PaginatedResultListForItems(mbsClinicalCategoryCollection, queryModel);

            return paginatedList;
        }

        private IEnumerable<MbsClinicalCategoryData> SearchMbsClinicalCategoryItems(IEnumerable<MbsClinicalCategoryData> mbsClinicalCategoryCollection, string searchTerm)
        {
            return mbsClinicalCategoryCollection.Where(s => s.ItemNum.ToString() == searchTerm);
        }

        private async Task<QueryResultList<MbsClinicalCategoryData>> PaginatedResultListForItems(IEnumerable<MbsClinicalCategoryData> mbsClinicalCategoryCollection, QueryModel queryModel)
        {
            QueryResultList<MbsClinicalCategoryData> queryList = new QueryResultList<MbsClinicalCategoryData>();
            List<MbsClinicalCategoryData> paginatedList = null;
            if (queryModel.PageNumber > 0 && queryModel.PageSize > 0)
            {
                if (mbsClinicalCategoryCollection.Any())
                {
                    paginatedList = mbsClinicalCategoryCollection.Skip((queryModel.PageNumber - 1) * queryModel.PageSize)
                 .Take(queryModel.PageSize).ToList();
                }
            }

            if (paginatedList != null)
            {
                queryList.ItemRecords = paginatedList;
                queryList.PageNumber = queryModel.PageNumber;
                queryList.PageSize = queryModel.PageSize;
                queryList.CurrentCount = paginatedList.Count();
            }
            else
            {
                queryList.ItemRecords = null;
                queryList.CurrentCount = 0;
            }
            queryList.TotalCount = mbsClinicalCategoryCollection.Count();
            queryList.PageNumber = queryModel.PageNumber;
            queryList.PageSize = queryModel.PageSize;

            return queryList;
        }

        private IEnumerable<MbsClinicalCategoryData> SortMbsClinicalCategoryItems(IEnumerable<MbsClinicalCategoryData> mbsClinicalCategoryCollection, string sortTerm, string sortOrder)
        {
            switch (sortTerm.ToLower())
            {

                case "id":

                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            mbsClinicalCategoryCollection = mbsClinicalCategoryCollection.OrderBy(x => x.Id);
                        }
                        else
                        {

                            mbsClinicalCategoryCollection = mbsClinicalCategoryCollection.OrderByDescending(x => x.Id);

                        }
                        break;
                    }
                case "itemnum":

                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            mbsClinicalCategoryCollection = mbsClinicalCategoryCollection.OrderBy(x => x.ItemNum);
                        }
                        else
                        {

                            mbsClinicalCategoryCollection = mbsClinicalCategoryCollection.OrderByDescending(x => x.ItemNum);

                        }
                        break;
                    }


                default:
                    mbsClinicalCategoryCollection = mbsClinicalCategoryCollection.OrderBy(x => x.ItemNum);

                    break;
            }
            return mbsClinicalCategoryCollection;
        }
    }

}
