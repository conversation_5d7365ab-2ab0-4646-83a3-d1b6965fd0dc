﻿using AutoMapper;
using Capstone2.Framework.Business.Common;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.Master.Common;
using Capstone2.RestServices.MedicalSchedule.Interfaces;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;

namespace Capstone2.RestServices.MedicalSchedule.Services
{
    public class DvaBAL : IDvaBAL
    {
        public IMapper _mapper;
        public readonly AppSettings _appSettings;
        public readonly IDvaDAL _dvaDAL;
        private readonly HttpClient client;
        private IDistributedCacheHelper _redisCache;

        public DvaBAL(IMapper mapper, IOptions<AppSettings> appSettings, IDvaDAL dvaDAL, IDistributedCacheHelper redisCache)
        {
            _mapper = mapper;
            _appSettings = appSettings.Value;
            _dvaDAL = dvaDAL;
            _redisCache = redisCache;
        }

        /// <summary>
        /// Get List of DvaData
        /// </summary>
        /// <returns></returns>
        public async Task<ApiResponse<QueryResultList<DvaFromDBs>>> GetDvaDataListBAL(BaseHttpRequestContext baseHttpRequestContext, QueryModel queryModel)
        {
            var orgId = baseHttpRequestContext.OrgId;
            List<DvaData> responseData = await _redisCache.GetFromCache<List<DvaData>>($"{CachedKeys.Tbl_DvaData}");
            QueryResultList<DvaFromDBs> dvaDataList = null;
            if (responseData == null)
            {
                responseData = await _dvaDAL.GetDvaDataListDAL();
                await _redisCache.SetIntoCache(responseData, $"{CachedKeys.Tbl_DvaData}");
            }
            dvaDataList = await _dvaDAL.ListDvaDataDAL(orgId, queryModel, responseData);

            var apiResponse = new ApiResponse<QueryResultList<DvaFromDBs>>
            {
                Result = dvaDataList,
                Message = "Success",
                StatusCode = StatusCodes.Status200OK
            };
            return apiResponse;
        }
    }
}
