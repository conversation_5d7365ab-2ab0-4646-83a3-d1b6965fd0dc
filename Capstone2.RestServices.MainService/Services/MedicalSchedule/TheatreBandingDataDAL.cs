﻿using Capstone2.RestServices.Master.Context;
using Capstone2.RestServices.MedicalSchedule.Context;
using Capstone2.RestServices.MedicalSchedule.Interfaces;
using Capstone2.RestServices.MedicalSchedule.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Capstone2.RestServices.MedicalSchedule.Services
{
    public class TheatreBandingDataDAL : ITheatreBandingDataDAL
    {
        public readonly ReadOnlyMedicalScheduleDBContext _readOnlyDbContext;
        public readonly UpdatableMedicalScheduleDBContext _updatableDBContext;
        public readonly ReadOnlyMasterDBContext _readMasterDbContext;
        public TheatreBandingDataDAL(ReadOnlyMedicalScheduleDBContext readOnlyDbContext, UpdatableMedicalScheduleDBContext updatableDBContext, ReadOnlyMasterDBContext readMasterDbContext)
        {
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
            _readMasterDbContext = readMasterDbContext;
        }
        public async Task<List<TheatreBandingData>> GetTheatreBandingDataListDAL()
        {
            return await _readMasterDbContext.TheatreBandingData.Where(x => x.StatusId == (short)Status.Active).ToListAsync();
        }
        /// <summary>
        /// Method to list TheatreBanding Data Items
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        public async Task<QueryResultList<ListTheatreBandingData>> ListTheatreBandingDataDAL(int orgId, QueryModel queryModel, List<TheatreBandingData> responseData)
        {
            IEnumerable<ListTheatreBandingData> theatreBandingItemCollection = responseData.Where(d => d.StatusId == (short)Status.Active).Select(data => new ListTheatreBandingData
            {
                Id = data.Id,
                ItemNum = data.ItemNum,
                Description = data.Description
            });

            if (!(string.IsNullOrWhiteSpace(queryModel.SearchTerm)))
            {
                theatreBandingItemCollection = SearchTheatreBandingDataItems(theatreBandingItemCollection, queryModel.SearchTerm);

            }

            if (!(string.IsNullOrWhiteSpace(queryModel.SortOrder)) && !(string.IsNullOrWhiteSpace(queryModel.SortTerm)))
            {
                theatreBandingItemCollection = SortTheatreBandingDataItems(theatreBandingItemCollection, queryModel.SortTerm, queryModel.SortOrder);
            }
            QueryResultList<ListTheatreBandingData> paginatedList = await PaginatedResultListForItems(theatreBandingItemCollection, queryModel);

            return paginatedList;
        }

        private IEnumerable<ListTheatreBandingData> SearchTheatreBandingDataItems(IEnumerable<ListTheatreBandingData> theatreBandingItemCollection, string searchTerm)
        {
            return theatreBandingItemCollection.Where(s => s.Description.Contains(searchTerm) || s.ItemNum.ToString() == searchTerm);
        }

        private async Task<QueryResultList<ListTheatreBandingData>> PaginatedResultListForItems(IEnumerable<ListTheatreBandingData> theatreBandingItemCollection, QueryModel queryModel)
        {
            QueryResultList<ListTheatreBandingData> queryList = new QueryResultList<ListTheatreBandingData>();
            List<ListTheatreBandingData> paginatedList = null;
            if (queryModel.PageNumber > 0 && queryModel.PageSize > 0)
            {
                if (theatreBandingItemCollection.Any())
                {
                    paginatedList = theatreBandingItemCollection.Skip((queryModel.PageNumber - 1) * queryModel.PageSize)
                 .Take(queryModel.PageSize).ToList();
                }
            }

            if (paginatedList != null)
            {
                queryList.ItemRecords = paginatedList;
                queryList.PageNumber = queryModel.PageNumber;
                queryList.PageSize = queryModel.PageSize;
                queryList.CurrentCount = paginatedList.Count();
            }
            else
            {
                queryList.ItemRecords = null;
                queryList.CurrentCount = 0;
            }
            queryList.TotalCount = theatreBandingItemCollection.Count();
            queryList.PageNumber = queryModel.PageNumber;
            queryList.PageSize = queryModel.PageSize;

            return queryList;
        }

        private IEnumerable<ListTheatreBandingData> SortTheatreBandingDataItems(IEnumerable<ListTheatreBandingData> theatreBandingItemCollection, string sortTerm, string sortOrder)
        {
            switch (sortTerm.ToLower())
            {

                case "id":

                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            theatreBandingItemCollection = theatreBandingItemCollection.OrderBy(x => x.Id);
                        }
                        else
                        {

                            theatreBandingItemCollection = theatreBandingItemCollection.OrderByDescending(x => x.Id);

                        }
                        break;
                    }
                case "itemnum":

                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            theatreBandingItemCollection = theatreBandingItemCollection.OrderBy(x => x.ItemNum);
                        }
                        else
                        {

                            theatreBandingItemCollection = theatreBandingItemCollection.OrderByDescending(x => x.ItemNum);

                        }
                        break;
                    }


                default:
                    theatreBandingItemCollection = theatreBandingItemCollection.OrderBy(x => x.ItemNum);

                    break;
            }
            return theatreBandingItemCollection;
        }
    }

}
