﻿using AutoMapper;
using Capstone2.Framework.Business.Common;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.Master.Common;
using Capstone2.RestServices.MedicalSchedule.Interfaces;
using Capstone2.RestServices.MedicalSchedule.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;

namespace Capstone2.RestServices.MedicalSchedule.Services
{
    public class ClinicalCategoryBAL : IClinicalCategoryBAL
    {
        public IMapper _mapper;
        public readonly AppSettings _appSettings;
        public readonly IClinicalCategoryDAL _clinicalCategoryDAL;
        private readonly HttpClient client;
        private IDistributedCacheHelper _redisCache;

        public ClinicalCategoryBAL(IMapper mapper, IOptions<AppSettings> appSettings, IClinicalCategoryDAL clinicalCategoryDAL, IDistributedCacheHelper redisCache)
        {
            _mapper = mapper;
            _appSettings = appSettings.Value;
            _clinicalCategoryDAL = clinicalCategoryDAL;
            _redisCache = redisCache;
        }

        /// <summary>
        /// Get List of ClinicalCategory data
        /// </summary>
        /// <returns></returns>
        public async Task<ApiResponse<QueryResultList<ClinicalCategory>>> GetClinicalCategoryDataListBAL(BaseHttpRequestContext baseHttpRequestContext, QueryModel queryModel)
        {
            var orgId = baseHttpRequestContext.OrgId;
            ClinicalCategoryFilter filterModel = PrepareFilterParameters(queryModel.Filter);
            List<ClinicalCategory> responseData = await _redisCache.GetFromCache<List<ClinicalCategory>>($"{CachedKeys.Tbl_ClinicalCategory}");
            QueryResultList<ClinicalCategory> clinicalCategoryDataList = null;
            if (responseData == null)
            {
                responseData = await _clinicalCategoryDAL.GetClinicalCategoryDataListDAL();
                await _redisCache.SetIntoCache(responseData, $"{CachedKeys.Tbl_ClinicalCategory}");
            }
            clinicalCategoryDataList = await _clinicalCategoryDAL.ListClinicalCategoryDataDAL(orgId, queryModel, responseData, filterModel);

            var apiResponse = new ApiResponse<QueryResultList<ClinicalCategory>>
            {
                Result = clinicalCategoryDataList,
                Message = "Success",
                StatusCode = StatusCodes.Status200OK
            };
            return apiResponse;
        }

        private ClinicalCategoryFilter PrepareFilterParameters(string filter)
        {
            if (!(String.IsNullOrEmpty(filter)))
            {
                return JsonConvert.DeserializeObject<ClinicalCategoryFilter>(filter);
            }
            else
            {
                return null;
            }
        }
    }
}
