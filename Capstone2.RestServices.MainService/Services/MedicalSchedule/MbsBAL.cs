﻿using AutoMapper;
using Capstone2.Framework.Business.Common;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.Master.Common;
using Capstone2.RestServices.MedicalSchedule.Interfaces;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;

namespace Capstone2.RestServices.MedicalSchedule.Services
{
    public class MbsBAL : IMbsBAL
    {
        public IMapper _mapper;
        public readonly AppSettings _appSettings;
        public readonly IMbsDAL _mbsDAL;
        private readonly HttpClient client;
        private IDistributedCacheHelper _redisCache;

        public MbsBAL(IMapper mapper, IOptions<AppSettings> appSettings, IMbsDAL mbsDAL, IDistributedCacheHelper redisCache)
        {
            _mapper = mapper;
            _appSettings = appSettings.Value;
            _mbsDAL = mbsDAL;
            _redisCache = redisCache;
        }

        /// <summary>
        /// Get MbsData
        /// </summary>
        /// <returns></returns>
        public async Task<ApiResponse<QueryResultList<ListMbsData>>> GetMbsDataListBAL(BaseHttpRequestContext baseHttpRequestContext, QueryModel queryModel)
        {
            var orgId = baseHttpRequestContext.OrgId;
            //await _redisCache.RemoveCache($"{CachedKeys.Tbl_MbsData}");
            List<MbsData> responseData = await _redisCache.GetFromCache<List<MbsData>>($"{CachedKeys.Tbl_MbsData}");
            QueryResultList<ListMbsData> mbsDataList = null;
            if (responseData == null)
            {
                responseData = await _mbsDAL.GetMbsDataListDAL();
                await _redisCache.SetIntoCache(responseData, $"{CachedKeys.Tbl_MbsData}");
            }
            MbsDataFilterModel filterModel = PrepareFilterParameters(queryModel.Filter);
            if (filterModel is not null && filterModel.EpisodeCateogoriesIdLvl1 is not null && filterModel.EpisodeCateogoriesIdLvl1.Any() && (filterModel.SearchAll == false))
            {
                List<MbsClinicalCategoryData> mbsClinicalCategoryDataList = await _redisCache.GetFromCache<List<MbsClinicalCategoryData>>($"{CachedKeys.Tbl_MbsClinicalCategoryData}");
                if (mbsClinicalCategoryDataList == null)
                {
                    mbsClinicalCategoryDataList = await _mbsDAL.GetMbsClinicalCategoryDataListDAL();
                    await _redisCache.SetIntoCache(mbsClinicalCategoryDataList, $"{CachedKeys.Tbl_MbsClinicalCategoryData}");
                }
                List<long> ListofchildMBS = (from MCC in mbsClinicalCategoryDataList
                                             where MCC.StatusId == (short)Status.Active && (filterModel.EpisodeCateogoriesIdLvl1.Contains(MCC.ClinicalCategoryId))
                                             select MCC.ItemNum).ToList();
                mbsDataList = await _mbsDAL.ListMbsDataDAL(orgId, queryModel, responseData, ListofchildMBS, filterModel);
            }
            else
            {
                mbsDataList = await _mbsDAL.ListMbsDataDAL(orgId, queryModel, responseData, null,filterModel);
            }
            var apiResponse = new ApiResponse<QueryResultList<ListMbsData>>
            {
                Result = mbsDataList,
                Message = "Success",
                StatusCode = StatusCodes.Status200OK
            };
            return apiResponse;
        }

        private MbsDataFilterModel PrepareFilterParameters(string filter)
        {
            if (!(String.IsNullOrEmpty(filter)))
            {
                return JsonConvert.DeserializeObject<MbsDataFilterModel>(filter);
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// Method to fetch Mbs Item based on Id
        /// </summary>
        /// <param name="itemnumber"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<MbsDataView>> GetMbsDataBAL(long itemnum, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<MbsDataView> apiResponse = new();
            List<MbsData> mbsResponseData = await _redisCache.GetFromCache<List<MbsData>>($"{CachedKeys.Tbl_MbsData}");
            List<MbsClinicalCategoryData> mbsClinicalCategoryResponseData = await _redisCache.GetFromCache<List<MbsClinicalCategoryData>>($"{CachedKeys.Tbl_MbsClinicalCategoryData}");
            List<ClinicalCategory> clinicalCategoryResponseData = await _redisCache.GetFromCache<List<ClinicalCategory>>($"{CachedKeys.Tbl_ClinicalCategory}");
            List<TheatreBandingData> theatreBandingResponseData = await _redisCache.GetFromCache<List<TheatreBandingData>>($"{CachedKeys.Tbl_TheatreBandingData}");
            List<DvaData> dvaResponseData = await _redisCache.GetFromCache<List<DvaData>>($"{CachedKeys.Tbl_DvaData}");
            List<MbsData> mbsDataList = null;
            List<MbsClinicalCategoryData> mbsClinicalCategoryDataList = null;
            List<TheatreBandingData> theatreBandingDataList = null;
            List<ClinicalCategory> clinicalCategoryDataList = null;
            List<DvaData> dvaDataList = null;
            if (mbsResponseData == null)
            {
                mbsDataList = await _mbsDAL.GetMbsDataListDAL();
                await _redisCache.SetIntoCache(mbsDataList, $"{CachedKeys.Tbl_MbsData}");
            }
            else if (mbsResponseData != null)
            {
                mbsDataList = mbsResponseData;
            }
            if (mbsClinicalCategoryResponseData == null)
            {
                mbsClinicalCategoryDataList = await _mbsDAL.GetMbsClinicalCategoryDataListDAL();
                await _redisCache.SetIntoCache(mbsClinicalCategoryDataList, $"{CachedKeys.Tbl_MbsClinicalCategoryData}");
            }
            else if(mbsClinicalCategoryResponseData != null)
            {
                mbsClinicalCategoryDataList = mbsClinicalCategoryResponseData;
            }
            if (clinicalCategoryResponseData == null)
            {
                clinicalCategoryDataList = await _mbsDAL.GetClinicalCategoryDataListDAL();
                await _redisCache.SetIntoCache(clinicalCategoryDataList, $"{CachedKeys.Tbl_ClinicalCategory}");
            }
            else if (clinicalCategoryResponseData != null)
            {
                clinicalCategoryDataList = clinicalCategoryResponseData;
            }
            if (theatreBandingResponseData == null)
            {
                theatreBandingDataList = await _mbsDAL.GetTheatreBandingDataListDAL();
                await _redisCache.SetIntoCache(theatreBandingDataList, $"{CachedKeys.Tbl_TheatreBandingData}");
            }
            else if (theatreBandingResponseData != null)
            {
                theatreBandingDataList = theatreBandingResponseData;
            }
            if (dvaResponseData == null)
            {
                dvaDataList = await _mbsDAL.GetDvaDataListDAL();
                await _redisCache.SetIntoCache(dvaDataList, $"{CachedKeys.Tbl_DvaData}");
            }
            else if (dvaResponseData != null)
            {
                dvaDataList = dvaResponseData;
            }
            MbsDataView mbsDataViewFromDB = await _mbsDAL.GetMbsDataDAL(itemnum, baseHttpRequestContext.OrgId, mbsDataList, mbsClinicalCategoryDataList, clinicalCategoryDataList, dvaDataList, theatreBandingDataList);
            if (mbsDataViewFromDB is null)
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = null;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("Episode Item not found.");
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";
                apiResponse.Result = mbsDataViewFromDB;
            }

            return apiResponse;

        }
        /// <summary>
        /// Method to fetch Mbs Items -fitler based on ItemNum
        /// </summary>
        /// <param name="itemnumber"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<List<MbsDataView>>> ListMbsDataBAL(QueryModel queryModel, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse< List < MbsDataView >> apiResponse = new();
            List<MbsData> mbsResponseData = await _redisCache.GetFromCache<List<MbsData>>($"{CachedKeys.Tbl_MbsData}");
            List<MbsClinicalCategoryData> mbsClinicalCategoryResponseData = await _redisCache.GetFromCache<List<MbsClinicalCategoryData>>($"{CachedKeys.Tbl_MbsClinicalCategoryData}");
            List<ClinicalCategory> clinicalCategoryResponseData = await _redisCache.GetFromCache<List<ClinicalCategory>>($"{CachedKeys.Tbl_ClinicalCategory}");
            List<TheatreBandingData> theatreBandingResponseData = await _redisCache.GetFromCache<List<TheatreBandingData>>($"{CachedKeys.Tbl_TheatreBandingData}");
            List<DvaData> dvaResponseData = await _redisCache.GetFromCache<List<DvaData>>($"{CachedKeys.Tbl_DvaData}");
            List<MbsData> mbsDataList = null;
            List<MbsClinicalCategoryData> mbsClinicalCategoryDataList = null;
            List<TheatreBandingData> theatreBandingDataList = null;
            List<ClinicalCategory> clinicalCategoryDataList = null;
            List<DvaData> dvaDataList = null;
            if (mbsResponseData == null)
            {
                mbsDataList = await _mbsDAL.GetMbsDataListDAL();
                await _redisCache.SetIntoCache(mbsDataList, $"{CachedKeys.Tbl_MbsData}");
            }
            else if (mbsResponseData != null)
            {
                mbsDataList = mbsResponseData;
            }
            if (mbsClinicalCategoryResponseData == null)
            {
                mbsClinicalCategoryDataList = await _mbsDAL.GetMbsClinicalCategoryDataListDAL();
                await _redisCache.SetIntoCache(mbsClinicalCategoryDataList, $"{CachedKeys.Tbl_MbsClinicalCategoryData}");
            }
            else if (mbsClinicalCategoryResponseData != null)
            {
                mbsClinicalCategoryDataList = mbsClinicalCategoryResponseData;
            }
            if (clinicalCategoryResponseData == null)
            {
                clinicalCategoryDataList = await _mbsDAL.GetClinicalCategoryDataListDAL();
                await _redisCache.SetIntoCache(clinicalCategoryDataList, $"{CachedKeys.Tbl_ClinicalCategory}");
            }
            else if (clinicalCategoryResponseData != null)
            {
                clinicalCategoryDataList = clinicalCategoryResponseData;
            }
            if (theatreBandingResponseData == null)
            {
                theatreBandingDataList = await _mbsDAL.GetTheatreBandingDataListDAL();
                await _redisCache.SetIntoCache(theatreBandingDataList, $"{CachedKeys.Tbl_TheatreBandingData}");
            }
            else if (theatreBandingResponseData != null)
            {
                theatreBandingDataList = theatreBandingResponseData;
            }
            if (dvaResponseData == null)
            {
                dvaDataList = await _mbsDAL.GetDvaDataListDAL();
                await _redisCache.SetIntoCache(dvaDataList, $"{CachedKeys.Tbl_DvaData}");
            }
            else if (dvaResponseData != null)
            {
                dvaDataList = dvaResponseData;
            }
            MbsDataFilterModel filterModel = PrepareFilterParameters(queryModel.Filter);

            List<MbsDataView> lstMbsDataViewFromDB = await _mbsDAL.ListMbsDataBAL(queryModel, filterModel, baseHttpRequestContext.OrgId, mbsDataList, mbsClinicalCategoryDataList, clinicalCategoryDataList, dvaDataList, theatreBandingDataList);
            if (lstMbsDataViewFromDB is null)
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = null;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("Mbs Items cannot be fetched.");
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";
                apiResponse.Result = lstMbsDataViewFromDB;
            }

            return apiResponse;

        }
    }
}
