﻿using Capstone2.RestServices.Master.Context;
using Capstone2.RestServices.MedicalSchedule.Context;
using Capstone2.RestServices.MedicalSchedule.Interfaces;
using Capstone2.RestServices.MedicalSchedule.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Capstone2.RestServices.MedicalSchedule.Services
{
    public class DvaDAL : IDvaDAL
    {
        public readonly ReadOnlyMedicalScheduleDBContext _readOnlyDbContext;
        public readonly UpdatableMedicalScheduleDBContext _updatableDBContext;
        public readonly ReadOnlyMasterDBContext _readMasterDbContext;
        public DvaDAL(ReadOnlyMedicalScheduleDBContext readOnlyDbContext, UpdatableMedicalScheduleDBContext updatableDBContext, ReadOnlyMasterDBContext readMasterDbContext)
        {
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
            _readMasterDbContext = readMasterDbContext;
        }
        public async Task<List<DvaData>> GetDvaDataListDAL()
        {
            return await _readMasterDbContext.DvaData.Where(x => x.StatusId == (short)Status.Active).ToListAsync();
        }
        /// <summary>
        /// Method to list Dva Data Items
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="queryModel"></param>
        /// <param name="filterModel"></param>
        /// <returns></returns>
        public async Task<QueryResultList<DvaFromDBs>> ListDvaDataDAL(int orgId, QueryModel queryModel, List<DvaData> responseData)
        {
            IEnumerable<DvaFromDBs> dvaItemCollection = responseData.Where(d => d.StatusId == (short)Status.Active).Select(data => new DvaFromDBs
            {
                Id = data.Id,
                ItemNum = data.ItemNum
            });

            //IQueryable<ListMbsData> mbsItemQuery = mbsItemCollection.AsQueryable<ListMbsData>();

            if (!(string.IsNullOrWhiteSpace(queryModel.SearchTerm)))
            {
                dvaItemCollection = SearchDvaItems(dvaItemCollection, queryModel.SearchTerm);

            }

            if (!(string.IsNullOrWhiteSpace(queryModel.SortOrder)) && !(string.IsNullOrWhiteSpace(queryModel.SortTerm)))
            {
                dvaItemCollection = SortDvaItems(dvaItemCollection, queryModel.SortTerm, queryModel.SortOrder);
            }
            QueryResultList<DvaFromDBs> paginatedList = await PaginatedResultListForItems(dvaItemCollection, queryModel);

            return paginatedList;
        }

        private IEnumerable<DvaFromDBs> SearchDvaItems(IEnumerable<DvaFromDBs> mbsItemCollection, string searchTerm)
        {
            return mbsItemCollection.Where(s => s.ItemNum.ToString() == searchTerm);
        }

        private async Task<QueryResultList<DvaFromDBs>> PaginatedResultListForItems(IEnumerable<DvaFromDBs> dvaItemCollection, QueryModel queryModel)
        {
            QueryResultList<DvaFromDBs> queryList = new QueryResultList<DvaFromDBs>();
            List<DvaFromDBs> paginatedList = null;
            if (queryModel.PageNumber > 0 && queryModel.PageSize > 0)
            {
                if (dvaItemCollection.Any())
                {
                    paginatedList = dvaItemCollection.Skip((queryModel.PageNumber - 1) * queryModel.PageSize)
                 .Take(queryModel.PageSize).ToList();
                }
            }

            if (paginatedList != null)
            {
                queryList.ItemRecords = paginatedList;
                queryList.PageNumber = queryModel.PageNumber;
                queryList.PageSize = queryModel.PageSize;
                queryList.CurrentCount = paginatedList.Count();
            }
            else
            {
                queryList.ItemRecords = null;
                queryList.CurrentCount = 0;
            }
            queryList.TotalCount = dvaItemCollection.Count();
            queryList.PageNumber = queryModel.PageNumber;
            queryList.PageSize = queryModel.PageSize;

            return queryList;
        }

        private IEnumerable<DvaFromDBs> SortDvaItems(IEnumerable<DvaFromDBs> dvaItemCollection, string sortTerm, string sortOrder)
        {
            switch (sortTerm.ToLower())
            {

                case "id":

                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            dvaItemCollection = dvaItemCollection.OrderBy(x => x.Id);
                        }
                        else
                        {

                            dvaItemCollection = dvaItemCollection.OrderByDescending(x => x.Id);

                        }
                        break;
                    }
                case "itemnum":

                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            dvaItemCollection = dvaItemCollection.OrderBy(x => x.ItemNum);
                        }
                        else
                        {

                            dvaItemCollection = dvaItemCollection.OrderByDescending(x => x.ItemNum);

                        }
                        break;
                    }


                default:
                    dvaItemCollection = dvaItemCollection.OrderBy(x => x.ItemNum);

                    break;
            }
            return dvaItemCollection;
        }
    }

}
