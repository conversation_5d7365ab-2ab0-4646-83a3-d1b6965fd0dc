﻿using Capstone2.Framework.Business.Common;
using Capstone2.Framework.RestApi;
using Capstone2.RestServices.MIMS.Interfaces;
using Capstone2.RestServices.MIMS.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Capstone2.RestServices.MIMS.Services
{
    public class HelperService : IHelperService
    {
        private readonly MIMSApiSetting _mimsApiSetting;
        private readonly IConfiguration _configuration;
        private IDistributedCacheHelper _redisCache;
        public HelperService(IOptions<MIMSApiSetting> options, IConfiguration configuration, IDistributedCacheHelper cache)
        {
            _configuration = configuration;
            _redisCache = cache;
            _mimsApiSetting = options.Value;
        }

        /// <summary>
        /// Get MIMS headers for requesting product API
        /// </summary>
        /// <returns>Header key/Value</returns>
        public async Task<Dictionary<string, string>> GetMimsSecurityHeader()
        {
            Dictionary<string, string> headerKeys = new Dictionary<string, string>();
            string cachedMIMSAuthTokenKey = $"{_configuration["AppSettings:CachedMIMSAuthTokenKey"]}_{_mimsApiSetting.Api_Key}";
            BearerToken authBearerToken = await _redisCache.GetFromCache<BearerToken>(cachedMIMSAuthTokenKey);
            if (authBearerToken == null || authBearerToken.token_expire_at <= DateTime.UtcNow)
            {
                authBearerToken = await GetBearerToken().ConfigureAwait(false);
                authBearerToken.token_generated_at = DateTime.UtcNow;
                authBearerToken.token_expire_at = DateTime.UtcNow.AddSeconds(authBearerToken.expires_in).AddMinutes(-15);
                await _redisCache.SetIntoCache(authBearerToken, cachedMIMSAuthTokenKey).ConfigureAwait(false);
            }

            headerKeys.Add("api-key", _mimsApiSetting.Api_Key);
            headerKeys.Add("Authorization", $"{authBearerToken.token_type} {authBearerToken.access_token}");
            return headerKeys;
        }

        /// <summary>
        /// Generate refresh token 
        /// </summary>
        /// <returns></returns>
        public async Task<Dictionary<string, string>> RefreshMimsSecurityHeader()
        {
            BearerToken authBearerToken = await GetBearerToken();
            Dictionary<string, string> headerKeys = new Dictionary<string, string>();
            headerKeys.Add("api-key", _mimsApiSetting.Api_Key);
            headerKeys.Add("Authorization", $"{authBearerToken.token_type} {authBearerToken.access_token}");
            return headerKeys;
        }

        /// <summary>
        /// Generate OAuth2 authentication bearer token from MIMS
        /// </summary>
        /// <returns></returns>
        private async Task<BearerToken> GetBearerToken()
        {
            Dictionary<string, string> headerKeys = new Dictionary<string, string>();
            headerKeys.Add("client_id", _mimsApiSetting.Client_Id);
            headerKeys.Add("client_secret", _mimsApiSetting.Client_Secret);
            headerKeys.Add("grant_type", _mimsApiSetting.Grant_Type);
            string mediaType = "application/x-www-form-urlencoded";
            string resourceUri = $"{_mimsApiSetting.BaseApiUrl}{Capstone2.RestServices.MIMS.Common.ConstantsHelper.ENDPOINT_AUTH_URL}";

            RestClient restClient = new RestClient(resourceUri, headerKeys);
            return await restClient.PostAsync<BearerToken>(resourceUri, headerKeys, mediaType);
        }
    }
}
