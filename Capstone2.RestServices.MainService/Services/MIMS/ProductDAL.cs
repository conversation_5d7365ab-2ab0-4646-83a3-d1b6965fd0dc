﻿using Capstone2.Framework.RestApi;
//using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.HIService.Common;
using Capstone2.RestServices.MIMS.Interfaces;
using Capstone2.RestServices.MIMS.Models;
using Capstone2.Shared.Models.Common;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Web;

namespace Capstone2.RestServices.MIMS.Services
{
    public class ProductDAL : IProductDAL
    {
        private readonly MIMSApiSetting _mimsApiSetting;
        private readonly IHelperService _helperService;
        public ProductDAL(IOptions<MIMSApiSetting> options, IHelperService helperService)
        {
            _mimsApiSetting = options.Value;
            _helperService = helperService;
        }

        /// <summary>
        /// Search request for product list
        /// </summary>
        /// <param name="paramsRequest">paramsRequest</param>
        /// <returns>Return product list</returns>
        public async Task<List<Models.Product>> GetProducts(SearchParam paramsRequest)
        {
            var properties = from pop in paramsRequest.GetType().GetProperties()
                             where pop.GetValue(paramsRequest, null) != null
                             select pop.Name + "=" + HttpUtility.UrlEncode(pop.GetValue(paramsRequest, null).ToString());
            string queryString = String.Join("&", properties.ToArray());
            string requestUrl = $"{_mimsApiSetting.BaseApiUrl}{Capstone2.RestServices.MIMS.Common.ConstantsHelper.ENDPOINT_PRODUCTS}?{queryString}";
            Dictionary<string, string> headerKeys = await _helperService.GetMimsSecurityHeader();

            RestClient restClient = new RestClient(requestUrl, headerKeys);
            var response = await restClient.GetAsync<List<Models.Product>>(requestUrl, null);
            return response;
        }

        /// <summary>
        /// Search request for product list
        /// </summary>
        /// <param name="paramsRequest">paramsRequest</param>
        /// <returns>Return product list</returns>
        public async Task<List<Models.Product>> GetProducts(QueryModel queryModel)
        {
            string queryString = queryModel.Filter;
            string requestUrl = $"{_mimsApiSetting.BaseApiUrl}{Capstone2.RestServices.MIMS.Common.ConstantsHelper.ENDPOINT_PRODUCTS}?{queryString}";
            Dictionary<string, string> headerKeys = await _helperService.GetMimsSecurityHeader();

            RestClient restClient = new RestClient(requestUrl, headerKeys);
            var response = await restClient.GetAsync<List<Models.Product>>(requestUrl, null);
            return response;
        }


        public async Task<ProductDetail> GetProductDetails(string productId, string searchFields)
        {
            string requestUrl = $"{_mimsApiSetting.BaseApiUrl}{Capstone2.RestServices.MIMS.Common.ConstantsHelper.ENDPOINT_PRODUCTS}/{productId}";
            if (!string.IsNullOrWhiteSpace(searchFields))
            {
                var queryString = HttpUtility.ParseQueryString(string.Empty);
                queryString["fields"] = searchFields;
                requestUrl = $"{requestUrl}?{queryString}";
            }

            Dictionary<string, string> headerKeys = await _helperService.GetMimsSecurityHeader();
            RestClient restClient = new RestClient(requestUrl, headerKeys);
            var response = await restClient.GetAsync<ProductDetail>(requestUrl, null);
            return response;
        }

        /// <summary>
        /// List Allergy Classes
        /// </summary>
        /// <returns>Get a list of the most commonly reported Allergy Classes to be used as pick list</returns>
        public async Task<List<Models.AllergyClasses>> GetAllergyClasses()
        {
            string requestUrl = $"{_mimsApiSetting.BaseApiUrl}{Capstone2.RestServices.MIMS.Common.ConstantsHelper.ENDPOINT_ALLERGY_LIST}";
            Dictionary<string, string> headerKeys = await _helperService.GetMimsSecurityHeader();
            RestClient restClient = new RestClient(requestUrl, headerKeys);
            var response = await restClient.GetAsync<List<Models.AllergyClasses>>(requestUrl, null);
            return response;
        }

        public async Task<VersionInfo> GetVersion()
        {
            string requestUrl = $"{_mimsApiSetting.BaseApiUrl}{Capstone2.RestServices.MIMS.Common.ConstantsHelper.ENDPOINT_DATA_VERSION}";
            Dictionary<string, string> headerKeys = await _helperService.GetMimsSecurityHeader();

            RestClient restClient = new RestClient(requestUrl, headerKeys);
            var response = await restClient.GetAsync<VersionInfo>(requestUrl, null);
            return response;
        }

        public async Task<PBSDetail> GetPBSDetails(string pbsCode, string fields, string manufacturerCode)
        {
            string requestQuery = string.Format(Capstone2.RestServices.MIMS.Common.ConstantsHelper.ENDPOINT_PBS_DETAILS, pbsCode);
            string requestUrl = $"{_mimsApiSetting.BaseApiUrl}{requestQuery}";

            var queryString = HttpUtility.ParseQueryString(string.Empty);
            if (!string.IsNullOrWhiteSpace(fields))
                queryString["fields"] = fields;
            if (!string.IsNullOrWhiteSpace(manufacturerCode))
                queryString["manufacturerCode"] = manufacturerCode;
            requestUrl = $"{requestUrl}?{queryString}";

            Dictionary<string, string> headerKeys = await _helperService.GetMimsSecurityHeader();
            RestClient restClient = new RestClient(requestUrl, headerKeys);
            var response = await restClient.GetAsync<PBSDetail>(requestUrl, null);
            return response;
        }

        public async Task<FullPIDetail> GetFullPIDetails(string fullPIId)
        {
            string requestQuery = string.Format(Capstone2.RestServices.MIMS.Common.ConstantsHelper.ENDPOINT_FULL_PI_DETAILS, fullPIId);
            string requestUrl = $"{_mimsApiSetting.BaseApiUrl}{requestQuery}";
            Dictionary<string, string> headerKeys = await _helperService.GetMimsSecurityHeader();

            RestClient restClient = new RestClient(requestUrl, headerKeys);
            var response = await restClient.GetAsync<FullPIDetail>(requestUrl, null);
            return response;
        }

        public async Task<CMIDetail> GetCMIDetails(string cmiId, string format, bool offmarket)
        {
            string requestQuery = string.Format(Capstone2.RestServices.MIMS.Common.ConstantsHelper.ENDPOINT_CMI_DETAILS, cmiId);
            string requestUrl = $"{_mimsApiSetting.BaseApiUrl}{requestQuery}";

            var queryString = HttpUtility.ParseQueryString(string.Empty);
            queryString["format"] = format; queryString["offmarket"] = offmarket.ToString().ToLower();
            requestUrl = $"{requestUrl}?{queryString}";

            Dictionary<string, string> headerKeys = await _helperService.GetMimsSecurityHeader();

            RestClient restClient = new RestClient(requestUrl, headerKeys);
            var response = await restClient.GetAsync<CMIDetail>(requestUrl, null);
            return response;
        }

    }
}
