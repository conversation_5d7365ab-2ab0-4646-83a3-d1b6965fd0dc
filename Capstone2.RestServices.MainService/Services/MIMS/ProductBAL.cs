﻿using Capstone2.RestServices.MIMS.Interfaces;
using Capstone2.RestServices.MIMS.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;

namespace Capstone2.RestServices.MIMS.Services
{
    public class ProductBAL : IProductBAL
    {
        public IProductDAL _productDAL;
        public ProductBAL(IProductDAL productDAL)
        {
            _productDAL = productDAL;
        }

        public async Task<ApiResponse<List<Models.Product>>> GetProducts(SearchParam paramsRequest)
        {
            ApiResponse<List<Models.Product>> apiResponse = new ApiResponse<List<Models.Product>>();
            List<Models.Product> productList = await _productDAL.GetProducts(paramsRequest);
            apiResponse.Result = productList;
            apiResponse.Message = "Success";
            apiResponse.StatusCode = StatusCodes.Status200OK;
            return apiResponse;
        }

        public async Task<ApiResponse<List<Models.Product>>> GetProducts(QueryModel queryModel)
        {
            ApiResponse<List<Models.Product>> apiResponse = new ApiResponse<List<Models.Product>>();
            List<Models.Product> productList = await _productDAL.GetProducts(queryModel);
            apiResponse.Result = productList;
            apiResponse.Message = "Success";
            apiResponse.StatusCode = StatusCodes.Status200OK;
            return apiResponse;
        }

        public async Task<ApiResponse<Models.ProductDetail>> GetProductDetails(string productId, string searchFields)
        {
            ApiResponse<Models.ProductDetail> apiResponse = new ApiResponse<Models.ProductDetail>();
            Models.ProductDetail productDetails = await _productDAL.GetProductDetails(productId, searchFields);
            apiResponse.Result = productDetails;
            apiResponse.Message = "Success";
            apiResponse.StatusCode = StatusCodes.Status200OK;
            return apiResponse;
        }

        public async Task<ApiResponse<List<Models.AllergyClasses>>> GetAllergyClasses()
        {
            ApiResponse<List<Models.AllergyClasses>> apiResponse = new ApiResponse<List<Models.AllergyClasses>>();
            List<Models.AllergyClasses> allergyClassesList = await _productDAL.GetAllergyClasses();
            apiResponse.Result = allergyClassesList;
            apiResponse.Message = "Success";
            apiResponse.StatusCode = StatusCodes.Status200OK;
            return apiResponse;
        }

        public async Task<ApiResponse<Models.VersionInfo>> GetVersion()
        {
            ApiResponse<Models.VersionInfo> apiResponse = new ApiResponse<Models.VersionInfo>();
            Models.VersionInfo versionInfo = await _productDAL.GetVersion();
            apiResponse.Result = versionInfo;
            apiResponse.Message = "Success";
            apiResponse.StatusCode = StatusCodes.Status200OK;
            return apiResponse;
        }

        public async Task<ApiResponse<PBSDetail>> GetPBSDetails(string pbsCode, string fields, string manufacturerCode)
        {
            ApiResponse<PBSDetail> apiResponse = new ApiResponse<PBSDetail>();
            PBSDetail pbsDetails = await _productDAL.GetPBSDetails(pbsCode, fields, manufacturerCode);
            apiResponse.Result = pbsDetails;
            apiResponse.Message = "Success";
            apiResponse.StatusCode = StatusCodes.Status200OK;
            return apiResponse;
        }

        public async Task<ApiResponse<FullPIDetail>> GetFullPIDetails(string fullPIId)
        {
            ApiResponse<FullPIDetail> apiResponse = new ApiResponse<FullPIDetail>();
            FullPIDetail fullPIDetails = await _productDAL.GetFullPIDetails(fullPIId);
            apiResponse.Result = fullPIDetails;
            apiResponse.Message = "Success";
            apiResponse.StatusCode = StatusCodes.Status200OK;
            return apiResponse;
        }

        public async Task<ApiResponse<CMIDetail>> GetCMIDetails(string cmiId, string format, bool offmarket)
        {
            ApiResponse<CMIDetail> apiResponse = new ApiResponse<CMIDetail>();
            CMIDetail cmiDetails = await _productDAL.GetCMIDetails(cmiId, format, offmarket);
            if (cmiDetails?.cmiDocuments != null && (format.Equals("pdf", StringComparison.OrdinalIgnoreCase) || format.Equals("reducedpdf", StringComparison.OrdinalIgnoreCase)))
            {
                var downloadTasks = cmiDetails.cmiDocuments
                                  .Select(async cmiDocument =>
                                  {
                                      cmiDocument.blob = await GetDownloadFileFromSource(cmiDocument.cmiDocument);
                                  });

                await Task.WhenAll(downloadTasks);
            }
            apiResponse.Result = cmiDetails;
            apiResponse.Message = "Success";
            apiResponse.StatusCode = StatusCodes.Status200OK;
            return apiResponse;
        }

        private async Task<byte[]> GetDownloadFileFromSource(string fileUrl)
        {
            using (HttpClient client = new HttpClient())
            {
                HttpResponseMessage response = await client.GetAsync(fileUrl);
                response.EnsureSuccessStatusCode();
                return await response.Content.ReadAsByteArrayAsync();
            }
        }
    }
}
