﻿using Capstone2.Framework.Business.Common;
using Capstone2.Framework.RestApi;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.User.Interfaces;
using Capstone2.RestServices.User.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.Azure.KeyVault.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Runtime.Serialization.Formatters.Binary;
using System.Security.Cryptography;
using System.Threading.Tasks;
using System.Transactions;
using static System.Collections.Specialized.BitVector32;

namespace Capstone2.RestServices.User.Services
{
    public class IncomingLetterPatientActionsBAL : IIncomingLetterPatientActionsBAL
    {
        public readonly AppSettings _appSettings;
        public readonly IIncomingLetterPatientActionsDAL _incomingLetterPatientActionsDAL;
        public readonly IIncomingLetterDAL _incomingLetterDAL;
        private readonly ILogger<IncomingLetterPatientActionsBAL> _logger;

        public IncomingLetterPatientActionsBAL(IOptions<AppSettings> appSettings, IIncomingLetterPatientActionsDAL incomingLetterPatientActionsDAL, ILogger<IncomingLetterPatientActionsBAL> logger, IIncomingLetterDAL incomingLetterDAL)
        {
            _appSettings = appSettings.Value;
            _incomingLetterPatientActionsDAL = incomingLetterPatientActionsDAL;
            _logger = logger;
            _incomingLetterDAL = incomingLetterDAL;
        }

        public async Task<ApiResponse<long?>> AddIncomingLetterMultiUserPatientAction(List<IncomingLetterPatientAction> incomingLetterPatientAction, BaseHttpRequestContext baseHttpRequestContext)
        {
            var createdDate = await _incomingLetterDAL.GetIncomingLetterById(incomingLetterPatientAction[0].IncomingLetterId);
            ApiResponse<long?> apiResponse = new();
            var userId = baseHttpRequestContext.UserId;
            var orgId = baseHttpRequestContext.OrgId;
            long id = 0;
            var existinDataActionId = 0;
            var existinDataPatientDetailsId = 0;
            var existingCreatedBy = 0;
            long existingLetterInActivityLogId = 0;
            long activityLogId = 0;
            IncomingLetterPatientActionView clonedIncomingLetterDetails = null;
            List<VariableJSON> lstVariableJson = new();
            List<IncomingLetterActions> IncomingLetterActionsList = new List<IncomingLetterActions>();
            IncomingLetterPatientActionView incomingLetterDetails = null;

            using (var transaction = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
            {
                if(incomingLetterPatientAction.Count > 0)
                {
                    var existingData = await _incomingLetterPatientActionsDAL.GetIncomingLetterPatientActionById(incomingLetterPatientAction[0].IncomingLetterId, 0);
                    var existingDatas = Clone(existingData);
                    existinDataActionId = (short)existingDatas.ActionId;
                    existinDataPatientDetailsId = existingDatas.PatientDetails != null ? (int)existingDatas.PatientDetails.Id : 0;
                    existingCreatedBy = (int)existingDatas.CreatedBy;
                    existingLetterInActivityLogId = existingDatas.LetterInActivityLogId != null ? (long)existingDatas.LetterInActivityLogId : 0;
                    clonedIncomingLetterDetails = existingDatas;
                    IncomingLetterActionsList = await _incomingLetterPatientActionsDAL.GetIncomingLetterActionsByIncomingLetterId(incomingLetterPatientAction[0].IncomingLetterId);
                    if (existingData != null) {
                        await _incomingLetterPatientActionsDAL.DeleteIncomingLetterPatientAction(incomingLetterPatientAction[0].IncomingLetterId);
                    }
                }
                foreach (var action in incomingLetterPatientAction)
                {
                    action.OrgId = action.OrgId;
                    //action.StatusId = (short)Status.Active;  // commeting since its passing from FE
                    if(clonedIncomingLetterDetails == null)
                    {
                        action.CreatedBy = action.CreatedBy;
                        action.CreatedDate = createdDate.CreatedDate;
                    }
                    else
                    {
                        action.CreatedBy = existingCreatedBy;
                        action.CreatedDate = createdDate.CreatedDate;
                        action.ModifiedBy = action.CreatedBy;
                        action.ModifiedDate = DateTime.UtcNow;
                        action.LetterInActivityLogId = existingLetterInActivityLogId;
                    }
                }
                activityLogId = 0;
                var result = await _incomingLetterPatientActionsDAL.AddIncomingLetterMultiUserPatientAction(incomingLetterPatientAction, activityLogId);
                id = result.Count;
                var dbActions = await _incomingLetterPatientActionsDAL.GetIncomingLetterPatientActionByIdList(incomingLetterPatientAction[0].IncomingLetterId);

                if ((existinDataActionId == (short)IncomingLetterActionsEnum.Recieved && result.Count > 0 && result.Any(r => r.ActionId != (short)IncomingLetterActionsEnum.Recieved)) || existinDataPatientDetailsId != result[0].PatientDetailsId)
                {
                    List<VariableJSON> lstVariableJSON = await GenerateVariableJSONForAdd(result[0], orgId);
                    incomingLetterDetails = await _incomingLetterPatientActionsDAL.GetIncomingLetterPatientActionById(result[0].IncomingLetterId, 0);
                    var DescString = incomingLetterDetails.Notes;
                    var IsClosed = incomingLetterDetails.IsClosed;
                    ActivityLogInfo activityLog = CreateActivityLogEntry((int)ActivityLogOps.Create, lstVariableJSON, (long)result[0].IncomingLetterId, result[0].PatientDetailsId, (bool)IsClosed ? (short)31 : (short)30, DescString);
                    string patientAPiUrl = _appSettings.ApiUrls["PatientServiceUrl"] + "/patient/" + result[0].PatientDetailsId + "/activity_logs";
                    RestClient restClient = new RestClient(patientAPiUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
                    var activitylogApiResponse = await restClient.PostAsync<ApiResponse<long>>(patientAPiUrl, activityLog);

                    if (activitylogApiResponse.Result == 0 && activitylogApiResponse.StatusCode != StatusCodes.Status200OK)
                    {
                        apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                        apiResponse.Message = "Add Activity Log failed";
                        apiResponse.Result = id;
                    }
                    else
                    {
                        activityLogId = activitylogApiResponse.Result;
                        foreach (var item in incomingLetterPatientAction)
                        {
                            item.CreatedDate = createdDate.CreatedDate;
                        }
                        var resultEdit = await _incomingLetterPatientActionsDAL.AddIncomingLetterMultiUserPatientAction(incomingLetterPatientAction, activityLogId);
                    }
                }

                transaction.Complete();

            }

            if (id > 0)
            {
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Result = id;
                apiResponse.Message = "Success";
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = null;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("Action cannot be added at this time.Please try again later.");
            }



            return apiResponse;
        }

        public async Task<ApiResponse<long?>> AddIncomingLetterPatientAction(IncomingLetterPatientAction incomingLetterPatientAction, BaseHttpRequestContext baseHttpRequestContext)
        {
            var createdDate = await _incomingLetterDAL.GetIncomingLetterById(incomingLetterPatientAction.IncomingLetterId);
            ApiResponse<long?> apiResponse = new();
            incomingLetterPatientAction.CreatedBy = baseHttpRequestContext.UserId;
            incomingLetterPatientAction.CreatedDate = createdDate.CreatedDate;

            var result = await _incomingLetterPatientActionsDAL.AddIncomingLetterPatientAction(incomingLetterPatientAction);
            if (result != null)
            {
                apiResponse.Result = incomingLetterPatientAction.Id;
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failure";
            }
            return apiResponse;
        }

        public async Task<ApiResponse<IncomingLetterPatientActionView>> GetIncomingLetterPatientActionById(long id, short selectedActionId)
        {
            ApiResponse<IncomingLetterPatientActionView> apiResponse = new();
            var result = await _incomingLetterPatientActionsDAL.GetIncomingLetterPatientActionById(id, selectedActionId);
            if (result != null)
            {
                apiResponse.Result = result;
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status404NotFound;
                apiResponse.Message = "Incoming letter patient action not found";
            }
            return apiResponse;
        }

        public async Task<ApiResponse<IncomingLetterPatientActionView>> GetIncomingLetterPatientActionByActivityLogId(short activityLogId)
        {
            ApiResponse<IncomingLetterPatientActionView> apiResponse = new();
            var result = await _incomingLetterPatientActionsDAL.GetIncomingLetterPatientActionByActivityLogId(activityLogId);
            if (result != null)
            {
                apiResponse.Result = result;
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status404NotFound;
                apiResponse.Message = "Incoming letter patient action not found";
            }
            return apiResponse;
        }

        public async Task<ApiResponse<QueryResultList<IncomingLetterPatientActionView>>> GetIncomingLetterPatientActions(QueryModel queryModel, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<QueryResultList<IncomingLetterPatientActionView>> apiResposne = new();
            QueryResultList<IncomingLetterPatientActionView> actions = new();
            var filterModel = PrepareFilterParameters(queryModel.Filter);
            if (filterModel.AssignedToUserIds != null && filterModel.AssignedByUserIds != null && ((filterModel.AssignedByUserIds.Count == 1 && filterModel.AssignedByUserIds.Contains(baseHttpRequestContext.UserId)) || (filterModel.AssignedToUserIds.Count == 1 && filterModel.AssignedToUserIds.Contains(baseHttpRequestContext.UserId))))
            {
                actions = await _incomingLetterPatientActionsDAL.GetIncomingLetterPatientActions(baseHttpRequestContext.OrgId, queryModel, filterModel);

            }
            else if (filterModel.AssignedToUserIds != null && filterModel.AssignedByUserIds == null && filterModel.AssignedToUserIds.Count == 1 && filterModel.AssignedToUserIds.Contains(baseHttpRequestContext.UserId))
            {
                actions = await _incomingLetterPatientActionsDAL.GetIncomingLetterPatientActions(baseHttpRequestContext.OrgId, queryModel, filterModel);
            }
            else
            {
                RolesPermissionsAssoc permission = await _incomingLetterPatientActionsDAL.GetPermissionOnRoleId(baseHttpRequestContext.OrgId, baseHttpRequestContext.RoleId, (short)ApiPermission.Patient_Actions_Report);
                if (permission == null)
                {
                    apiResposne.StatusCode = StatusCodes.Status403Forbidden;
                    apiResposne.Errors.Add("Not an Authorized User");
                    apiResposne.Result = null;
                    return apiResposne;
                }
                if (permission.IsAllowed == false)
                {
                    apiResposne.StatusCode = StatusCodes.Status403Forbidden;
                    apiResposne.Errors.Add("Not an Authorized User");
                    apiResposne.Result = null;
                    return apiResposne;
                }
                actions = await _incomingLetterPatientActionsDAL.GetIncomingLetterPatientActions(baseHttpRequestContext.OrgId, queryModel, filterModel);

            }


            if (actions?.ItemRecords != null)
            {
                // TODO: this logic has to change to implement parallel call or with multiple IDs
                foreach (var action in actions.ItemRecords)
                {
                    //action.ActionDescription = "Received";
                    action.AssigneeDetailsUserIdList = await _incomingLetterPatientActionsDAL.GetUserDetailsFromCommaSeparatedIdsAsync(action.AssigneeDetailsList);
                    action.ActionDescription = EnumExtensions.GetDescription((IncomingLetterActionsEnum)action.ActionId);
                }
            }
            apiResposne.Result = actions;
            apiResposne.StatusCode = StatusCodes.Status200OK;
            apiResposne.Message = "Success";
            return apiResposne;

        }

        public static List<IncomingLetterPatientActionsUserAssocs> DeepCopyList(List<IncomingLetterPatientActionsUserAssocs> list)
        {
            var json = JsonConvert.SerializeObject(list);
            return JsonConvert.DeserializeObject<List<IncomingLetterPatientActionsUserAssocs>>(json);
        }

        public async Task<ApiResponse<long?>> EditIncomingLetterPatientAction(IncomingLetterPatientAction incomingLetterPatientAction, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<long?> apiResponse = new();
            incomingLetterPatientAction.ModifiedBy = baseHttpRequestContext.UserId;
            incomingLetterPatientAction.ModifiedDate = DateTime.UtcNow;

            var result = await _incomingLetterPatientActionsDAL.EditIncomingLetterPatientAction(incomingLetterPatientAction);
            if (result > 0)
            {
                apiResponse.Result = incomingLetterPatientAction.Id;
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failure";
            }
            return apiResponse;
        }

        public async Task<ApiResponse<long?>> DeleteIncomingLetterPatientAction(long id)
        {
            ApiResponse<long?> apiResponse = new();
            var result = await _incomingLetterPatientActionsDAL.DeleteIncomingLetterPatientAction(id);
            if (result > 0)
            {
                apiResponse.Result = id;
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failure";
            }
            return apiResponse;
        }

        private IncomingLetterActionsFilterModel PrepareFilterParameters(string filter)
        {
            if (!(String.IsNullOrEmpty(filter)))
            {
                return JsonConvert.DeserializeObject<IncomingLetterActionsFilterModel>(filter);
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// GetPatientActionsNotificationCount
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<List<IncomingLetterPatientActionsNotificationCount>>> GetIncomingLetterPatientActionsNotificationCount(BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<List<IncomingLetterPatientActionsNotificationCount>> apiResposne = new();
            var actions = await _incomingLetterPatientActionsDAL.GetIncomingLetterPatientActionsNotificationCount(baseHttpRequestContext.OrgId);

            apiResposne.Result = actions;
            apiResposne.StatusCode = StatusCodes.Status200OK;
            apiResposne.Message = "Success";
            return apiResposne;
        }

        /// <summary>
        /// GetPatientActionsNotificationCountByUser
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<List<IncomingLetterPatientActionsNotificationCount>>> GetIncomingLetterPatientActionsNotificationCountByUser(long userId, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<List<IncomingLetterPatientActionsNotificationCount>> apiResposne = new();
            var actions = await _incomingLetterPatientActionsDAL.GetIncomingLetterPatientActionsNotificationCountByUserId(baseHttpRequestContext.OrgId, userId);

            apiResposne.Result = actions;
            apiResposne.StatusCode = StatusCodes.Status200OK;
            apiResposne.Message = "Success";
            return apiResposne;
        }

        private async Task<List<VariableJSON>> GenerateVariableJSONForAdd(IncomingLetterPatientAction newObject, int orgId)
        {
            var props = newObject.GetType().GetProperties();
            List<VariableJSON> lstVariableJSON = new();

            foreach (PropertyInfo i in props)
            {
                if (checkAuditCoumnsAdd(i.Name))
                {
                    var newVal = i.GetValue(newObject, null);

                    VariableJSON variableJson = new();
                    variableJson.Operation = Enum.GetName(ActivityLogOps.Create);
                    switch (i.Name.ToLower())
                    {

                        case "duedate":
                            {
                                variableJson.Column = "Due Date";
                                variableJson.OldValue = null;
                                variableJson.NewValue = (newVal is null) ? null : ((DateTime)newVal).ToString("dd-MM-yyyy");
                                break;
                            }
                        case "isurgent":
                            {
                                variableJson.Column = "IsUrgent";
                                variableJson.OldValue = null;
                                variableJson.NewValue = (newVal is null) ? false : newVal;
                                break;
                            }

                        case "userdetailsid":
                            {
                                variableJson.Column = "Assigned To";
                                variableJson.OldValue = null;
                                //List<UserDetailInfo> lstProviders = await _incomingLetterPatientActionsDAL.FetchUserDetailsFromIds(newObject.IncomingLetterPatientActionsUserAssocs.Select(x => x.UserDetailsId).ToList(), orgId);
                                List<UserDetailInfo> lstProviders = new List<UserDetailInfo>();
                                for (int m = 0; m < lstProviders.Count; m++)
                                {
                                    string title = (lstProviders[m].TitleId is null) ? string.Empty : EnumExtensions.GetDescription((TitleType)lstProviders[m].TitleId);
                                    variableJson.NewValue += title + " " + lstProviders[m].FirstName + " " + lstProviders[m].SurName;
                                    if (m != lstProviders.Count - 1)
                                    {
                                        variableJson.NewValue += ",";
                                    }

                                }
                                break;
                            }

                        default:
                            {
                                variableJson.NewValue = newVal;
                                variableJson.OldValue = null;
                                variableJson.Column = i.Name;
                                break;
                            }

                    }

                    lstVariableJSON.Add(variableJson);
                }

            }


            return lstVariableJSON;
        }

        private bool checkAuditCoumnsAdd(string colName)
        {
            // Userdetails is the dummy col name used to fetch the assoc table and show assigned users 
            if (
                colName.ToLower().Equals("userdetailsid") ||
                colName.ToLower().Equals("duedate") ||
                colName.ToLower().Equals("description") ||
                colName.ToLower().Equals("isurgent"))

                return true;

            return false;
        }

        private ActivityLogInfo CreateActivityLogEntry(int activityLogOp, List<VariableJSON> lstVariableJSON, long id, long? patientDetailsId, short? paStatus, string notes)
        {
            ActivityLogInfo activityLog = new();
            ActivityLogChildEntryInfo activityLogChild = new();
            //ToDo: Change from Enum
            activityLog.ActivityLogTypeId = (short)300;
            activityLog.PatientDetailsId = (long)patientDetailsId;
            activityLog.EntityId = id;
            activityLog.Description = notes;
            activityLog.ActivityStatusId = paStatus;
            activityLogChild.VariableJson = (lstVariableJSON is null || lstVariableJSON.Count == 0) ? null : JsonConvert.SerializeObject(lstVariableJSON);
            activityLogChild.ActivityStatusId = paStatus;
            activityLog.Title = "Letter In";
            activityLog.ActivityLogChildEntries.Add(activityLogChild);
            if ((int)ActivityLogOps.Create == activityLogOp)
            {
                activityLogChild.Description = "Incoming Letter Uploaded";
            }
            return activityLog;
        }


        private async Task<List<VariableJSON>> GenerateVariableJSON(IncomingLetterPatientAction OldObject, IncomingLetterPatientAction newObject, int orgId)
        {
            var props = OldObject.GetType().GetProperties();
            List<VariableJSON> lstVariableJSON = new();
            bool startTimeEdit = false;
            bool endTimeEdit = false;
            List<long> newUserList = new();
            List<long> finalNewUserList = new();

            foreach (PropertyInfo i in props)
            {
                if (checkAuditCoumns(i.Name))
                {
                    var oldVal = i.GetValue(OldObject, null);
                    var newVal = i.GetValue(newObject, null);
                    bool isUpdated = false;
                    if (oldVal is null && newVal is not null || oldVal is not null && newVal is null)
                    {
                        isUpdated = true;
                    }
                    else if (oldVal is not null && newVal is not null && !oldVal.Equals(newVal))
                    {
                        isUpdated = true;

                    }

                    if (isUpdated)
                    {

                        VariableJSON variableJson = new();
                        variableJson.Operation = Enum.GetName(ActivityLogOps.Update);
                        switch (i.Name.ToLower())
                        {

                            case "statusid":
                                {
                                    variableJson.Column = "Status";
                                    variableJson.OldValue = Enum.GetName((Status)((short)oldVal));
                                    variableJson.NewValue = Enum.GetName((Status)((short)newVal));
                                    break;
                                }

                            case "duedate":
                                {
                                    variableJson.Column = "Due Date";
                                    variableJson.OldValue = (oldVal is null) ? null : ((DateTime)oldVal).ToString("dd-MM-yyyy");
                                    variableJson.NewValue = (newVal is null) ? null : ((DateTime)newVal).ToString("dd-MM-yyyy");
                                    break;
                                }
                            case "isurgent":
                                {
                                    variableJson.Column = "IsUrgent";
                                    variableJson.OldValue = (oldVal is null) ? null : oldVal;
                                    variableJson.NewValue = (newVal is null) ? false : newVal;
                                    break;
                                }
                            case "isclosed":
                                {
                                    variableJson.Column = "Closed";
                                    variableJson.OldValue = (oldVal is null) ? null : oldVal;
                                    variableJson.NewValue = (newVal is null) ? false : newVal;
                                    break;
                                }

                            case "userdetailsid":
                                {
                                    variableJson.Column = "Assigned User";
                                    List<UserDetailInfo> lstProviders = await _incomingLetterPatientActionsDAL.FetchUserDetailsFromIds(new List<long> { (long)oldVal, (long)newVal }, orgId);
                                    lstProviders.ForEach(user =>
                                    {
                                        if (oldVal is not null && user.Id == (long)oldVal)
                                        {
                                            string title = (user.TitleId is null) ? string.Empty : EnumExtensions.GetDescription((TitleType)user.TitleId);
                                            variableJson.OldValue = title + " " + user.FirstName + " " + user.SurName;
                                        }
                                        if (newVal is not null && user.Id == (long)newVal)
                                        {
                                            string title = (user.TitleId is null) ? string.Empty : EnumExtensions.GetDescription((TitleType)user.TitleId);
                                            variableJson.NewValue = title + " " + user.FirstName + " " + user.SurName;
                                        }
                                    });
                                    break;
                                }
                            case "patientdetailsid":
                                {
                                    variableJson.Column = "Patient";
                                    List<PatientDetailInfo> lstpatient = await _incomingLetterPatientActionsDAL.FetchPatinetDetailsFromIds(new List<long> { (long)oldVal, (long)newVal }, orgId);
                                    lstpatient.ForEach(patient =>
                                    {
                                        if (oldVal is not null && patient.Id == (long)oldVal)
                                        {
                                            // string title = (user.TitleId is null) ? string.Empty : EnumExtensions.GetDescription((TitleType)user.TitleId);
                                            variableJson.OldValue = patient.FirstName + " " + patient.SurName;
                                        }
                                        if (newVal is not null && patient.Id == (long)newVal)
                                        {
                                            // string title = (user.TitleId is null) ? string.Empty : EnumExtensions.GetDescription((TitleType)user.TitleId);
                                            variableJson.NewValue = patient.FirstName + " " + patient.SurName;
                                        }
                                    });
                                    break;
                                }

                            default:
                                {
                                    variableJson.NewValue = newVal;
                                    variableJson.OldValue = oldVal;
                                    variableJson.Column = i.Name;
                                    break;
                                }

                        }

                        lstVariableJSON.Add(variableJson);
                    }

                }
            }

            //List<long> oldUserList = OldObject.IncomingLetterPatientActionsUserAssocs.Where(y => y.StatusId == (short)Status.Active).Select(x => x.UserDetailsId).ToList();

            //The New List which is updated  will not have the UserIds if they were present before and there NewList also.So to show them in the activity log the below logic is in place 



            //foreach (long userid in oldUserList)
            //{

            //    IncomingLetterPatientActionsUserAssocs deletedObj = newObject.IncomingLetterPatientActionsUserAssocs.Where(x => x.UserDetailsId == userid && x.StatusId == (short)Status.Deleted).FirstOrDefault();
            //    if (deletedObj == null)
            //    {
            //        newUserList.Add(userid);

            //    }

            //}

            //List<long> newUserList2 = newObject.IncomingLetterPatientActionsUserAssocs.Where(y => y.StatusId == (short)Status.Active).Select(x => x.UserDetailsId).ToList();
            List<long> newUserList2 = new List<long>();
            if (newUserList2 == null || newUserList2.Count == 0)
            {
                finalNewUserList = newUserList;
            }
            else if (newUserList == null || newUserList.Count == 0)
            {
                finalNewUserList = newUserList2;
            }
            else
            {
                finalNewUserList = newUserList.Concat(newUserList2).ToList();
            }


            //bool areAssignedUsersEqual = new HashSet<long>(finalNewUserList).SetEquals(oldUserList);
            bool areAssignedUsersEqual = false;

            if (areAssignedUsersEqual == false)
            {
                VariableJSON variableJson1 = new();
                variableJson1.Operation = Enum.GetName(ActivityLogOps.Update);
                variableJson1.Column = "Assigned User";
                variableJson1.OldValue = null;
                List<UserDetailInfo> newlstProviders = await _incomingLetterPatientActionsDAL.FetchUserDetailsFromIds(finalNewUserList, orgId);
                for (int m = 0; m < newlstProviders.Count; m++)
                {
                    string title = (newlstProviders[m].TitleId is null) ? string.Empty : EnumExtensions.GetDescription((TitleType)newlstProviders[m].TitleId);
                    variableJson1.NewValue += title + " " + newlstProviders[m].FirstName + " " + newlstProviders[m].SurName;
                    if (m < newlstProviders.Count - 1)
                    {
                        variableJson1.NewValue += ",";
                    }

                }


                //List<UserDetailInfo> oldlstProviders = await _incomingLetterPatientActionsDAL.FetchUserDetailsFromIds(oldUserList, orgId);
                List<UserDetailInfo> oldlstProviders = new List<UserDetailInfo>();
                for (int m = 0; m < oldlstProviders.Count; m++)
                {
                    string title = (oldlstProviders[m].TitleId is null) ? string.Empty : EnumExtensions.GetDescription((TitleType)oldlstProviders[m].TitleId);
                    variableJson1.OldValue += title + " " + oldlstProviders[m].FirstName + " " + oldlstProviders[m].SurName;
                    if (m < oldlstProviders.Count - 1)
                    {
                        variableJson1.NewValue += ",";
                    }

                }
                lstVariableJSON.Add(variableJson1);
            }


            return lstVariableJSON;
        }

        private T Clone<T>(T source)
        {
            if (Object.ReferenceEquals(source, null))
            {
                return default(T);
            }

            var deserializeSettings = new JsonSerializerSettings { ObjectCreationHandling = ObjectCreationHandling.Replace };
            return JsonConvert.DeserializeObject<T>(JsonConvert.SerializeObject(source), deserializeSettings);
        }

        private bool checkAuditCoumns(string colName)
        {
            if (colName.ToLower().Equals("statusid") ||
                colName.ToLower().Equals("duedate") ||
                colName.ToLower().Equals("patientdetailsid") ||
                colName.ToLower().Equals("description") ||
                colName.ToLower().Equals("isurgent") ||
                colName.ToLower().Equals("isclosed"))

                return true;

            return false;
        }

        public async Task<ApiResponse<long?>> IncomingLetterAccepted(long incomingLetterId, BaseHttpRequestContext baseHttpRequestContext)
        {
            //var createdDate = await _incomingLetterDAL.GetIncomingLetterById(incomingLetterId);
            ApiResponse<long?> apiResponse = new();

            var result = await _incomingLetterPatientActionsDAL.IncomingLetterAccepted(incomingLetterId);
            if (result != null)
            {
                apiResponse.Result = result == true ?  1 : 0 ;
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failure";
            }
            return apiResponse;
        }

    }
}
