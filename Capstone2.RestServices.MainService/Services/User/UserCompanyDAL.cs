﻿using AutoMapper;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.User.Context;
using Capstone2.RestServices.User.Interfaces;
using Capstone2.RestServices.User.Models;
using Capstone2.Shared.Models.Enum;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.RestServices.User.Services
{
    public class UserCompanyDAL : IUserCompanyDAL
    {

        public readonly AppSettings _appSettings;
        public readonly ReadOnlyUserDBContext _readOnlyDbContext;
        public UpdatableUserDBContext _updatableDBContext;
        public IMapper _mapper;

        public UserCompanyDAL(IOptions<AppSettings> appSettings, ReadOnlyUserDBContext readOnlyDbContext, UpdatableUserDBContext updatableDBContext, IMapper mapper)
        {
            _appSettings = appSettings.Value;
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
            _mapper = mapper;
        }

        public async Task<List<UserCompanyAssoc>> GetusercompanyAssocs(long Id, int orgId)
        {
            return await _readOnlyDbContext.UserCompanyAssocs.Where(x => x.UserDetailsId == Id && x.OrgId == orgId && x.StatusId == (short)Status.Active).AsNoTracking().ToListAsync();
        }
        public async Task<List<UserCompanyAssoc>> GetusercompanyAssocsAsync(long Id, int orgId)
        {
            return await _readOnlyDbContext.UserCompanyAssocs.Where(x => x.UserDetailsId == Id && x.OrgId == orgId && x.StatusId == (short)Status.Active).AsNoTracking().ToListAsync();
        }
        public async Task RemoveusercompanyList(List<UserCompanyAssoc> removeList)
        {
            _updatableDBContext.UserCompanyAssocs.UpdateRange(removeList);
            await _updatableDBContext.SaveChangesAsync();
        }
        public async Task UpdateusercompanyList(List<UserCompanyAssoc> updateList)
        {
            _updatableDBContext.UserCompanyAssocs.UpdateRange(updateList);
            await _updatableDBContext.SaveChangesAsync();
        }
        public async Task AddUsercompanyList(List<UserCompanyAssoc> addList)
        {
            _updatableDBContext.UserCompanyAssocs.AddRange(addList);
            await _updatableDBContext.SaveChangesAsync();
        }

        /// <summary>
        /// Fetch the usercompanyassocs based on companyid
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<List<UserCompanyAssoc>> FetchUserCompanyAssocs(int companyId, int orgId)
        {
            return await _updatableDBContext.UserCompanyAssocs.Where(x => x.CompanyId == companyId && x.OrgId == orgId && x.StatusId == (short)Status.Active).ToListAsync();
        }
    }
}
