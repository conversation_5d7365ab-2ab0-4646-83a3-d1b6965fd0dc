﻿using System;
using System.Linq;
using Capstone2.RestServices.User.Interfaces;
using Capstone2.RestServices.User.Context;
using Capstone2.RestServices.User.Models;
using Capstone2.RestServices.MainService.Common;
using Microsoft.Extensions.Options;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using System.Threading.Tasks;
using Capstone2.Shared.Models.Enum;
using System.Collections.Generic;
using Capstone2.Framework.RestApi.Dtos;

namespace Capstone2.RestServices.User.Services
{
    public class UserDAL : IUserDAL
    {

        public readonly AppSettings _appSettings;
        public readonly ReadOnlyUserDBContext _readOnlyDbContext;
        public UpdatableUserDBContext _updatableDBContext;
        public IMapper _mapper;

        public UserDAL(IOptions<AppSettings> appSettings, ReadOnlyUserDBContext readOnlyDbContext, UpdatableUserDBContext updatableDBContext, IMapper mapper)
        {
            _appSettings = appSettings.Value;
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
            _mapper = mapper;

        }

        public async Task<UserDetail> AddUserDetailsDAL(UserDetailInput user, long userId, int orgId, long modifierId)
        {
            
            var UsertoDb = MapperForUser(user, userId, orgId, modifierId);
            _updatableDBContext.UserDetails.Add(UsertoDb);
            await _updatableDBContext.SaveChangesAsync();
            return UsertoDb;
            
        }

        private UserDetail MapperForUser(UserDetailInput user, long userId, int orgId, long modifierId)
        {
            var userDb = _mapper.Map<UserDetailInput, UserDetail>(user);
            userDb.Id = userId;
            userDb.OrgId = orgId;
            userDb.ModifiedBy = modifierId;
            if (userDb.IsInternalEntityBank == true)
            {
                userDb.BankDetails = null;
                userDb.BSB = null;
                userDb.AccountNo = null;
                userDb.AccountName = null;
            }

            foreach (var userAddressInput in user.InputAddresses)
            {
                var AddressDb = _mapper.Map<AddressInput, Address>(userAddressInput);
                AddressDb.OrgId = orgId;
                AddressDb.UserDetailsId = userDb.Id;
                AddressDb.ModifiedBy = modifierId;
                AddressDb.StatusId = (short)Status.Active;
                userDb.Addresses.Add(AddressDb);
            }

            foreach (var CompanyInput in user.InputUserCompanyAssocs)
            {
                var CompanyDb = _mapper.Map<UserCompanyAssocInput, UserCompanyAssoc>(CompanyInput);
                CompanyDb.OrgId = orgId;
                CompanyDb.ModifiedBy = modifierId;
                CompanyDb.UserDetailsId = userDb.Id;
                CompanyDb.StatusId = (short)Status.Active;
                userDb.UserCompanyAssocs.Add(CompanyDb);
            }

            foreach (var userHealthFundAssoc in user.UserHealthFundAssocs)
            {
                // var AddressDb = _mapper.Map<AddressInput, Address>(userAddressInput);
                userHealthFundAssoc.OrgId = orgId;
                userHealthFundAssoc.UserDetailsId = userDb.Id;
                userHealthFundAssoc.ModifiedBy = modifierId;
                userHealthFundAssoc.StatusId = (short)Status.Active;
                userDb.UserHealthFundAssocs.Add(userHealthFundAssoc);
            }
            return userDb;
        }

        public async Task<UserDetailInput> GetUserDetailsDAL(int orgId, long Id)
        {
                var user = await _readOnlyDbContext.UserDetails.Include(e => e.Addresses).Include(e => e.UserCompanyAssocs.Where(x => x.StatusId == (short)Status.Active)).Include(x=>x.UserHealthFundAssocs.Where(x=>x.StatusId==(short)Status.Active)).FirstOrDefaultAsync(x => x.Id == Id && x.OrgId == orgId);
                //var authuser = await _readOnlyDbContext.Users.FirstOrDefaultAsync(x => x.Id == Id && x.OrgId == orgId)
                var authuser = await (from u in _readOnlyDbContext.Users
                                      where u.Id == Id && u.OrgId == orgId
                                      select new UserModel
                                      {
                                          UserIpaddress = u.UserIpaddress,
                                          UserStatusId = u.UserStatusId,
                                          IsOffsiteAccess = u.IsOffsiteAccess,
                                          RoleId = u.RoleId,
                                          RoleName = (from r in _readOnlyDbContext.Roles
                                                      where r.Id == u.RoleId
                                                      select r.Name).FirstOrDefault()
                                      }).FirstOrDefaultAsync();
                var companyList = await _readOnlyDbContext.CompanyDetails.Where(x => x.OrgId == orgId).ToListAsync();
                if (user != null)
                {
                    var userView = _mapper.Map<UserDetail, UserDetailInput>(user);
                    userView.UserIpaddress = authuser.UserIpaddress;
                    userView.UserStatusId = authuser.UserStatusId;
                    userView.OffSiteLogin = authuser.IsOffsiteAccess;
                    userView.Role = new RoleDetail()
                    {
                        Id = authuser.RoleId,
                        Name = authuser.RoleName
                    };
                    var useraddressinputResult = (from useraddress in user.Addresses
                                                  where useraddress.StatusId == (short)Status.Active
                                                  let useraddressinput = _mapper.Map<Address, AddressInput>(useraddress)
                                                  select useraddressinput);
                    foreach (var useraddressinput in useraddressinputResult)
                    {
                        userView.InputAddresses.Add(useraddressinput);
                    }

                    var usercompanyinputs = (from usercompany in user.UserCompanyAssocs
                                             where usercompany.StatusId == (short)Status.Active
                                             let usercompanyinputObj = _mapper.Map<UserCompanyAssoc, UserCompanyAssocInput>(usercompany)
                                             select usercompanyinputObj);
                    foreach (var usercompanyinput in usercompanyinputs)
                    {
                        usercompanyinput.Company = new CompanyName();
                        usercompanyinput.Company.Id = usercompanyinput.CompanyId;
                        var company = companyList.FirstOrDefault(x => x.Id == usercompanyinput.CompanyId);
                        usercompanyinput.Company.Name = company.Name;
                        usercompanyinput.Company.CompanyTypeId = company.CompanyTypeId;
                        userView.InputUserCompanyAssocs.Add(usercompanyinput);
                    }

                    if(userView?.UserHealthFundAssocs!=null && userView?.UserHealthFundAssocs!.Count > 0)
                    {
                        userView?.UserHealthFundAssocs.ToList().ForEach(x => x.UserDetails = null);
                    }
                    return userView;
                }
                else
                    return null;
            
            
        }

        public async Task<bool> CheckUserEmailDAL(string search_term, int orgId)
        {
            var userEmail = await _readOnlyDbContext.UserDetails
                    .Where(p => p.LoginEmail.Equals(search_term) && p.OrgId == orgId).FirstOrDefaultAsync();
            if (userEmail == null)
                return false;
            else
                return true;
        }

        public async Task<bool> CheckUserMobileDAL(string search_term, int orgId)
        {
            var userMobile = await _readOnlyDbContext.UserDetails
                    .Where(p => p.Mobile.Equals(search_term) && p.OrgId == orgId).FirstOrDefaultAsync();
            if (userMobile == null)
                return false;
            else
                return true;
        }

        public async Task<UserDetail> GetUserDetail(long Id, int orgId)
        {
            return await _readOnlyDbContext.UserDetails.AsNoTracking().FirstOrDefaultAsync(x => x.Id == Id && x.OrgId == orgId);
        }

        public async Task<UserDetail> EditUserDetailsDAL(long Id, UserDetail user, int orgId, long modifierId)
        {
            try
            {
                user.Id = Id;
                user.OrgId = orgId;
                user.ModifiedBy = modifierId;
                if (user.IsInternalEntityBank == true)
                {
                    user.BankDetails = null;
                    user.BSB = null;
                    user.AccountNo = null;
                    user.AccountName = null;
                }
                _updatableDBContext.UserDetails.Update(user);
                await _updatableDBContext.SaveChangesAsync();
                return user;
            }
            catch (Exception Ex)
            {
                throw new Exception(Ex.StackTrace);
            }
        }

        /// Method to add user to UserDetail
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        public async Task<UserDetail> AddUserDetail(UserDetail user)
        {
            await _updatableDBContext.UserDetails.AddAsync(user);
            await _updatableDBContext.SaveChangesAsync();
            return user;
        }

        /// <summary>
        /// Method to edit user
        /// </summary>
        /// <param name="Id"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        public async Task EditUserDetail(long Id, UserDetail user)
        {
            _updatableDBContext.UserDetails.Update(user);
            await _updatableDBContext.SaveChangesAsync();
        }
        /// <summary>
        /// Method to check if the roleid is valid 
        /// </summary>
        /// <param name="id"></param>
        /// <param name="orgId"></param>
        public async Task<int> SearchRole(int? id, int orgId)
        {
            return await _readOnlyDbContext.Roles.Where(x => x.Id == id && x.orgId == orgId).CountAsync();
        }

        /// <summary>
        /// Method to return few fileds like firstname,lastname,title etc of a user
        /// </summary>
        /// <param name="id"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<Capstone2.Shared.Models.Entities.UserInfoView> FetchUserDetailInfo(long id, int orgId)
        {
            UserDetail user = await GetUserDetail(id, orgId);
            if (user is not null)
            {
                Capstone2.Shared.Models.Entities.UserInfoView userinfo = _mapper.Map<UserDetail, Capstone2.Shared.Models.Entities.UserInfoView>(user);
                userinfo.Title = (userinfo.TitleId is not null) ? EnumExtensions.GetDescription((TitleType)userinfo.TitleId) : string.Empty;
                return userinfo;
            }
            return null;
        }
        /// <summary>
        /// Method to return few fields like firstname,lastname,title etc of a user - list
        /// </summary>
        /// <param name="id"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<List<Shared.Models.Entities.UserInfoView>> ListUserDetailInfo(List<long> ids, int orgId)
        {
            return await (from user in _readOnlyDbContext.UserDetails
                          where user.OrgId == orgId && ids.Contains(user.Id)
                          select new Shared.Models.Entities.UserInfoView
                          {
                              Id = user.Id,
                              OrgId = user.OrgId,
                              FirstName = user.FirstName,
                              SurName = user.SurName,
                              TitleId = user.TitleId,
                              Title = (user.TitleId != null) ? EnumExtensions.GetDescription((TitleType)user.TitleId) : string.Empty
                          }).ToListAsync(); ;
        }

        public async Task<QuickNote> GetQuickNote(int orgId, long userId)
        {
            var quickNote = await _readOnlyDbContext.QuickNote.FirstOrDefaultAsync(x => x.UserDetailsId == userId && x.OrgId == orgId && x.StatusId == (short)Status.Active);
            return quickNote;
        }

        public async Task<QuickNote> AddQuickNote(QuickNote note)
        {
            await _updatableDBContext.QuickNote.AddAsync(note);
            await _updatableDBContext.SaveChangesAsync();
            return note;
        }

        public async Task<QuickNote> EditQuickNote(QuickNote note)
        {
            _updatableDBContext.QuickNote.Update(note);
            await _updatableDBContext.SaveChangesAsync();
            return note;
        }

        public async Task<List<WhatsOnMessages>> GetWhatsOnMessages(int orgId, long userId)
        {
            var whatsOnMessages = await (from whtMssg in _readOnlyDbContext.WhatsOnMessages
                                         where whtMssg.OrgId == orgId //whtMssg.UserDetailsId == userId &&
                                         && whtMssg.StatusId == (short)Status.Active
                                         select whtMssg).ToListAsync();
            return whatsOnMessages;
        }

        public async Task<WhatsOnMessages> GetWhatsOnMessagesById(int orgId, long userId, long messageId)
        {
            var whatsOnMessages = await _readOnlyDbContext.WhatsOnMessages.FirstOrDefaultAsync(x => x.Id == messageId && x.OrgId == orgId && x.StatusId == (short)Status.Active); //x.UserDetailsId == userId &&
            return whatsOnMessages;
        }

        public async Task<WhatsOnMessages> AddWhatsOnMessages(WhatsOnMessages messages)
        {
            await _updatableDBContext.WhatsOnMessages.AddAsync(messages);
            await _updatableDBContext.SaveChangesAsync();
            return messages;
        }

        public async Task<WhatsOnMessages> EditWhatsOnMessages(WhatsOnMessages messages)
        {
            _updatableDBContext.WhatsOnMessages.Update(messages);
            await _updatableDBContext.SaveChangesAsync();
            return messages;
        }

        public async Task<UserHealthFundAssoc> GetUserHealthHundAssocFromUserId(long user_id, string participantId, BaseHttpRequestContext baseHttpRequestContext)
        {
            return await _readOnlyDbContext.UserHealthFundAssocs.Where(x => x.UserDetailsId == user_id && x.ParticipantId == participantId && x.StatusId == (short)Status.Active).FirstOrDefaultAsync();
        }

        public async Task<ICollection<HIServiceDetail>> GetHIServiceDetails(int orgId, long id)
        {
            var hiServiceDetails = await _readOnlyDbContext.HIServiceDetails.AsNoTracking().Where(x => x.UserDetailsId == id && x.OrgId == orgId && x.HIServiceTypeId == (short)HIServiceType.HPI_Individual).ToListAsync();
            return hiServiceDetails;
        }
        public async Task<long?> AddHIServiceDetail(HIServiceDetail inputHIServiceDetail)
        {
            _updatableDBContext.HIServiceDetails.Add(inputHIServiceDetail);
            return await _updatableDBContext.SaveChangesAsync();
        }

        public async Task<long?> UpdateHIServiceDetail(List<HIServiceDetail> inputHIServiceDetails)
        {
            _updatableDBContext.HIServiceDetails.UpdateRange(inputHIServiceDetails);
            return await _updatableDBContext.SaveChangesAsync();
        }

        public async Task<HIServiceDetail> GetExistingHIServiceDetailById(long id)
        {
            return await _readOnlyDbContext.HIServiceDetails.FirstOrDefaultAsync(s => s.Id == id);
        }
        public async Task<List<UserInfo>> GetUserDetailsByHPIINumber(int orgId, string hpiiNumber)
        {
            return await _readOnlyDbContext.UserDetails
                .Where(x => x.HPIINumber == hpiiNumber
                         && x.OrgId == orgId
                         )
                .Select(x => new UserInfo
                {
                    Id = x.Id,
                    //OrgId = x.OrgId,
                    FirstName = x.FirstName,
                    SurName = x.SurName
                })
                .ToListAsync();
        }
    }
}
