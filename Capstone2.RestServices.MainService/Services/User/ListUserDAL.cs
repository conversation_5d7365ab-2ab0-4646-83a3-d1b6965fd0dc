﻿using AutoMapper;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.User.Context;
using Capstone2.RestServices.User.Interfaces;
using Capstone2.RestServices.User.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Enum;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Capstone2.RestServices.User.Services
{
    public class ListUserDAL : IListUserDAL
    {
        public readonly AppSettings _appSettings;
        public readonly ReadOnlyUserDBContext _readOnlyDbContext;
        public UpdatableUserDBContext _updatableDBContext;
        public IMapper _mapper;
        public ListUserDAL(IOptions<AppSettings> appSettings, ReadOnlyUserDBContext readOnlyDbContext, UpdatableUserDBContext updatableDBContext, IMapper mapper)
        {
            _appSettings = appSettings.Value;
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
            _mapper = mapper;
        }

        public async Task<UserDetail> GetUserDetailsDAL(int orgId, long Id)
        {
            return await _readOnlyDbContext.UserDetails.Include(e => e.Addresses).Include(e => e.UserCompanyAssocs).FirstOrDefaultAsync(x => x.Id == Id && x.OrgId == orgId);
        }

        /// <summary>
        /// Generate a paginated list of Users
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="queryModel"></param>
        /// <param name="userFilterModel"></param>
        /// <returns></returns>
        public async Task<QueryResultList<UserListModel>> ListUser(int orgId, QueryModel queryModel, UserFilterModel userFilterModel)
        {
            var userQuery = (from UD in _readOnlyDbContext.UserDetails
                             join AU in _readOnlyDbContext.Users on UD.Id equals AU.Id
                             join roles in _readOnlyDbContext.Roles on AU.RoleId equals roles.Id into roleGroup
                             from role in roleGroup.DefaultIfEmpty()
                             where UD.OrgId == orgId
                             select new UserListModel()
                             {
                                 Id = UD.Id,
                                 UserId = UD.Id,
                                 PhotoFileDetailsId = UD.PhotoFileDetailsId,
                                 FirstName = UD.FirstName,
                                 LoginEmail = UD.LoginEmail,
                                 SurName = UD.SurName,
                                 Mobile = UD.Mobile,
                                 JobPosition = UD.JobPosition,
                                 StatusId = AU.UserStatusId,
                                 UserTypeId = UD.UserTypeId,
                                 EmergencyContactPhoneNumber = UD.EmergencyContactPhoneNumber,
                                 isOffsiteAccess = AU.IsOffsiteAccess,
                                 IsInternalProvider=UD.UserTypeId==86?true:false,
                                 ShowInAppointmentScreen = UD.ShowInAppointmentScreen,
                                 UserCompanyAssocs = UD.UserCompanyAssocs
                                     .Where(x => x.StatusId == (short)Status.Active)
                                     .ToList(),
                                 RoleId = AU.RoleId ?? 0,
                                 RoleName = role != null ? role.Name : string.Empty,
                                 ProviderTypeId = UD.ProviderTypeId
                             });

            // Apply filters
            if (userFilterModel != null)
            {
                if (userFilterModel.Status?.Any() == true)
                {
                    userQuery = userQuery.Where(x => userFilterModel.Status.Contains(x.StatusId));
                }

                if (userFilterModel.UserType?.Any() == true)
                {
                    userQuery = userQuery.Where(x => userFilterModel.UserType.Contains(x.UserTypeId));
                }

                if (userFilterModel.ShowInAppointmentScreen.HasValue)
                {
                    userQuery = userQuery.Where(x => x.ShowInAppointmentScreen == userFilterModel.ShowInAppointmentScreen);
                }
            }

            // Exclude External_Provider if no user type filter
            if (userFilterModel?.UserType == null || !userFilterModel.UserType.Any())
            {
                userQuery = userQuery.Where(x => x.UserTypeId != (short)UserType.External_Provider);
            }

            // Apply search
            if (!string.IsNullOrWhiteSpace(queryModel.SearchTerm))
            {
                userQuery = SearchUsers(userQuery, queryModel.SearchTerm);
            }

            // Apply sorting
            if (!string.IsNullOrWhiteSpace(queryModel.SortOrder) && !string.IsNullOrWhiteSpace(queryModel.SortTerm))
            {
                userQuery = SortUsers(userQuery, queryModel.SortTerm, queryModel.SortOrder);
            }

            // Materialize query before pagination
            var userList = await userQuery.ToListAsync();

            // Pagination
            var paginatedList = userList.Skip((queryModel.PageNumber - 1) * queryModel.PageSize)
                                        .Take(queryModel.PageSize)
                                        .ToList();

            // Prepare result
            return new QueryResultList<UserListModel>
            {
                ItemRecords = paginatedList,
                TotalCount = userList.Count,
                PageNumber = queryModel.PageNumber,
                PageSize = queryModel.PageSize
            };
        }


        private async Task<List<UserListModel>> CreatePaginatedListAsync(IQueryable<UserListModel> userQuery, QueryModel queryModel)
        {
            if (queryModel.PageNumber > 0 && queryModel.PageSize > 0)
            {
                if (userQuery.Any())
                {
                    List<UserListModel> paginatedList = await userQuery.Skip((queryModel.PageNumber - 1) * queryModel.PageSize)
                 .Take(queryModel.PageSize).ToListAsync();
                    foreach (UserListModel user in paginatedList)
                    {
                        //to display the description attriute
                        user.Status = EnumExtensions.GetDescription((UserStatus)user.StatusId);
                        if (user.isOffsiteAccess == true)
                        {
                            user.OffsiteAccess = "Yes";
                        }
                        else
                        {
                            user.OffsiteAccess = "No";
                        }

                        user.IsInternalProvider = (user.UserTypeId == (short)UserType.Internal_Provider) ? true : false;
                    }
                    return paginatedList;
                }
            }
            return null;
        }

        /// <summary>
        /// Method to add orderby clause to query to fetch users
        /// </summary>
        /// <param name="userQuery"></param>
        /// <param name="sortTerm"></param>
        /// <param name="sortOrder"></param>
        /// <returns></returns>
        private IQueryable<UserListModel> SortUsers(IQueryable<UserListModel> userQuery, string sortTerm, string sortOrder)
        {
            switch (sortTerm.ToLower())
            {
                case "id":
                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            userQuery = userQuery.OrderBy(x => x.UserId);
                        }
                        else
                        {
                            userQuery = userQuery.OrderByDescending(x => x.UserId);
                        }
                        break;
                    }
                case "firstname":
                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            userQuery = userQuery.OrderBy(x => x.FirstName);
                        }
                        else
                        {
                            userQuery = userQuery.OrderByDescending(x => x.FirstName);
                        }
                        break;
                    }
                case "surname":
                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            userQuery = userQuery.OrderBy(x => x.SurName);
                        }
                        else
                        {
                            userQuery = userQuery.OrderByDescending(x => x.SurName);
                        }
                        break;
                    }
                case "loginemail":
                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            userQuery = userQuery.OrderBy(x => x.LoginEmail);
                        }
                        else
                        {
                            userQuery = userQuery.OrderByDescending(x => x.LoginEmail);
                        }
                        break;
                    }
            }

            return userQuery;
        }

        /// <summary>
        /// method to fetch users based on search for first and surname
        /// </summary>
        /// <param name="userQuery"></param>
        /// <param name="searchTerm"></param>
        /// <returns></returns>
        private IQueryable<UserListModel> SearchUsers(IQueryable<UserListModel> userQuery, string searchTerm)
        {
            string filterString = string.Empty;
            bool istwoWordSearch = Regex.IsMatch(searchTerm, @"\s");
            if (!istwoWordSearch)
            {
                userQuery = userQuery.Where(x => (x.FirstName.StartsWith(searchTerm) || x.SurName.StartsWith(searchTerm)));
            }
            else
            {
                string[] search = searchTerm.Split(" ");
                userQuery = userQuery.Where(x => (x.FirstName.StartsWith(search[0]) && x.SurName.StartsWith(search[1])));
            }
            return userQuery;
        }

        public async Task<List<KeyValuePair<int, string>>> FetchCompanyColours(int orgId, short companyType)
        {
            return await _readOnlyDbContext.CompanyDetails
                .Where(x => x.CompanyTypeId == companyType && x.OrgId == orgId && x.StatusId == (short)CompanyStatus.Active)
                .Select(x => new { x.Id, x.CompanyColour }).Select(x => new KeyValuePair<int, string>(x.Id, x.CompanyColour)).ToListAsync();
        }

        public async Task<QueryResultList<ExternalProviderListModel>> ListExtProviders(int orgId, QueryModel queryModel, UserFilterModel filterModel)
        {
            List<SqlParameter> paramsList = new List<SqlParameter>();
            var limit = queryModel.PageSize;
            var offset = (queryModel.PageNumber - 1) * queryModel.PageSize;
            paramsList.Add(new SqlParameter("@userTypeId", (short)UserType.External_Provider));
            paramsList.Add(new SqlParameter("@phyAddressType", (short)AddressType.Physical));
            paramsList.Add(new SqlParameter("@OrgId", orgId));
            paramsList.Add(new SqlParameter("@offset", offset));
            paramsList.Add(new SqlParameter("@limit", limit));

            if (!string.IsNullOrEmpty(queryModel.SearchTerm))
            {
                bool istwoWordSearch = Regex.IsMatch(queryModel.SearchTerm, @"\s");
                if (istwoWordSearch)
                {
                    string[] result = queryModel.SearchTerm.Split(" ", 2);
                    queryModel.SearchTerm = result[0];
                    paramsList.Add(new SqlParameter("@extraSearchTerm", result[1]));
                }
            }

            if (!(filterModel is null))
            {
                if (filterModel.Status != null && filterModel.Status.Any())
                {
                    paramsList.Add(new SqlParameter("@status", string.Join<short?>(",", (IEnumerable<short?>)filterModel.Status)));
                }
            }
            if (!(queryModel.SortOrder is null))
            {
                paramsList.Add(new SqlParameter("@sortOrder", queryModel.SortOrder));
            }
            if (!(queryModel.SortTerm is null))
            {
                paramsList.Add(new SqlParameter("@sortTerm", queryModel.SortTerm));
            }
            if (!(string.IsNullOrEmpty(queryModel.SearchTerm)))
            {
                paramsList.Add(new SqlParameter("@searchTerm", queryModel.SearchTerm));
            }

            var response = await ExecuteStoredProcedure("[User].[ListExternalProviders]", paramsList);
            response.PageNumber = queryModel.PageNumber;
            response.PageSize = queryModel.PageSize;
            return response;

        }
        private async Task<QueryResultList<ExternalProviderListModel>> ExecuteStoredProcedure(string storedProcedureName, IEnumerable<SqlParameter> parameters)
        {
            var dbConnection = (_readOnlyDbContext.Database.GetDbConnection());
            using (dbConnection)
            {
                using (var cmd = dbConnection.CreateCommand())
                {
                    cmd.CommandText = storedProcedureName;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandTimeout = 1800;
                    foreach (var parameter in parameters)
                    {
                        cmd.Parameters.Add(parameter);
                    }
                    await dbConnection.OpenAsync().ConfigureAwait(false);
                    var reader = await cmd.ExecuteReaderAsync();
                    return SqlDataToJson(reader);
                }
            }
        }

        private QueryResultList<ExternalProviderListModel> SqlDataToJson(DbDataReader dataReader)
        {
            var dataTable = new DataTable();
            List<ExternalProviderListModel> convList = new List<ExternalProviderListModel>();
            dataTable.Load(dataReader);
            try
            {
                convList = (from rw in dataTable.AsEnumerable()
                            select new ExternalProviderListModel
                            {
                                UserId = Convert.ToInt64(rw["Id"]),
                                OrgId = Convert.ToInt32(rw["OrgId"]),
                                FirstName = rw["FirstName"] == DBNull.Value ? null : Convert.ToString(rw["FirstName"]),
                                SurName = rw["SurName"] == DBNull.Value ? null : Convert.ToString(rw["SurName"]),
                                Specialisation = rw["Specialisation"] == DBNull.Value ? null : Convert.ToString(rw["Specialisation"]),
                                StatusId = Convert.ToInt16(rw["UserStatusId"]),
                                ProviderTypeId = Convert.ToInt16(rw["ProviderTypeId"]),
                                Addresses = JsonConvert.DeserializeObject<List<Address>>(Convert.ToString(rw["Addresses"])),
                                LstUserCompanyInfo = rw["CompanyInfo"] == DBNull.Value ? null : JsonConvert.DeserializeObject<List<UserCompanyInfo>>(Convert.ToString(rw["CompanyInfo"])),
                            }).ToList();
            }
            catch (Exception E)
            {
            }
            var dataTable2 = new DataTable();
            dataTable2.Load(dataReader);
            var Total = int.Parse(JsonConvert.SerializeObject(dataTable2.Rows[0][0]));
            var respose = new QueryResultList<ExternalProviderListModel>()
            {
                ItemRecords = convList,
                CurrentCount = convList.Count(),
                TotalCount = Total,
                PageNumber = 0,
                PageSize = 0
            };
            return respose;
        }

        public async Task<QueryResultList<ProviderInfo>> FetchProviders(int orgId, QueryModel queryModel, UserFilterModel filterModel)
        {
            var query = (from UC in _readOnlyDbContext.UserCompanyAssocs
                         join CD in _readOnlyDbContext.CompanyDetails on UC.CompanyId equals CD.Id
                         join UD in _readOnlyDbContext.UserDetails on UC.UserDetailsId equals UD.Id
                         join AU in _readOnlyDbContext.Users on UC.UserDetailsId equals AU.Id
                         where UC.OrgId == orgId && CD.OrgId == orgId && UD.OrgId == orgId && AU.OrgId == orgId && UC.StatusId == (short)Status.Active //&& AU.UserStatusId == (short)UserStatus.Active
                         select new ProviderInfo
                         {
                             Id = UC.Id,
                             OrgId = UC.OrgId,
                             UserDetailsId = UC.UserDetailsId,
                             FirstName = UD.FirstName,
                             SurName = UD.SurName,
                             ProviderTypeId = UD.ProviderTypeId,
                             Specialisation = UD.Specialisation,
                             CompanyId = UC.CompanyId,
                             CompanyName = CD.Name,
                             ProviderNumber = UC.ProviderNumber,
                             UserTypeId = UD.UserTypeId,
                             CompanyTypeId = UC.CompanyTypeId,
                             StatusId = AU.UserStatusId,
                             Salutation = UD.Salutation,
                             CompanyEmail = CD.Email
                         });

            if (filterModel is not null)
            {
                if (filterModel.CompanyType != null && filterModel.CompanyType.Any())
                {
                    query = query.Where(x => filterModel.CompanyType.Contains(x.CompanyTypeId));
                }
                if (filterModel.UserType != null && filterModel.UserType.Any())
                {
                    query = query.Where(x => filterModel.UserType.Contains(x.UserTypeId));
                }
                if (filterModel.Status != null && filterModel.Status.Any())
                {
                    query = query.Where(x => filterModel.Status.Contains(x.StatusId));
                }
            }

            if (!string.IsNullOrWhiteSpace(queryModel.SearchTerm))
            {
                query = SearchProviders(query, queryModel.SearchTerm);
            }
            if (!string.IsNullOrWhiteSpace(queryModel.SortOrder) && !string.IsNullOrWhiteSpace(queryModel.SortTerm))
            {
                query = SortProviders(query, queryModel.SortOrder, queryModel.SortTerm);
            }

            List<ProviderInfo> paginatedList = await CreatePaginatedProviderList(query, queryModel);
            QueryResultList<ProviderInfo> queryList = new QueryResultList<ProviderInfo>();
            if (paginatedList != null)
            {
                queryList.ItemRecords = paginatedList;
                queryList.PageNumber = queryModel.PageNumber;
                queryList.PageSize = queryModel.PageSize;
                queryList.CurrentCount = paginatedList.Count();
            }
            else
            {
                queryList.ItemRecords = null;
                queryList.CurrentCount = 0;
            }
            queryList.TotalCount = query.Count();
            queryList.PageNumber = queryModel.PageNumber;
            queryList.PageSize = queryModel.PageSize;

            return queryList;
        }

        private async Task<List<ProviderInfo>> CreatePaginatedProviderList(IQueryable<ProviderInfo> userQuery, QueryModel queryModel)
        {
            if (queryModel.PageNumber > 0 && queryModel.PageSize > 0)
            {
                if (userQuery.Any())
                {
                    List<ProviderInfo> paginatedList = await userQuery.Skip((queryModel.PageNumber - 1) * queryModel.PageSize)
                 .Take(queryModel.PageSize).ToListAsync();
                    paginatedList.ForEach(x => x.ProviderType = (x.ProviderTypeId == null) ? null : EnumExtensions.GetDescription((ProviderType)x.ProviderTypeId));
                    return paginatedList;
                }
            }
            return null;
        }

        private IQueryable<ProviderInfo> SortProviders(IQueryable<ProviderInfo> query, string sortOrder, string sortTerm)
        {
            switch (sortTerm.ToLower())
            {
                case "firstname":
                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            query = query.OrderBy(x => x.FirstName);
                        }
                        else
                        {
                            query = query.OrderByDescending(x => x.FirstName);
                        }
                        break;
                    }
                case "surname":
                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            query = query.OrderBy(x => x.SurName);
                        }
                        else
                        {
                            query = query.OrderByDescending(x => x.SurName);
                        }
                        break;
                    }
                default:
                    {
                        query = query.OrderBy(x => x.FirstName);
                        break;
                    }
            }
            return query;
        }

        /// <summary>
        /// method to fetch users based on search for first and surname
        /// </summary>
        /// <param name="userQuery"></param>
        /// <param name="searchTerm"></param>
        /// <returns></returns>
        private IQueryable<ProviderInfo> SearchProviders(IQueryable<ProviderInfo> userQuery, string searchTerm)
        {
            string filterString = string.Empty;
            bool istwoWordSearch = Regex.IsMatch(searchTerm, @"\s");
            if (!istwoWordSearch)
            {
                userQuery = userQuery.Where(x => (x.FirstName.StartsWith(searchTerm) || x.SurName.StartsWith(searchTerm)));
            }
            else
            {
                string[] search = searchTerm.Split(" ");
                userQuery = userQuery.Where(x => (x.FirstName.StartsWith(search[0]) && x.SurName.StartsWith(search[1])));
            }
            return userQuery;
        }

        public async Task<List<Shared.Models.Entities.CompanyDetailInfo>> FetchCompanyColoursForCompanyIds(int orgId, List<int> lstCompanyIds)
        {
            return await _readOnlyDbContext.CompanyDetails
                .Where(x => x.OrgId == orgId && x.StatusId == (short)CompanyStatus.Active && lstCompanyIds.Contains(x.Id))
                .Select(x => new Shared.Models.Entities.CompanyDetailInfo
                {
                    Id = x.Id,
                    OrgId = x.OrgId,
                    Name = x.Name,
                    Colour = x.CompanyColour,
                    CompanyTypeId=x.CompanyTypeId
                }).ToListAsync();
                                 
        }
        /// <summary>
        /// Method to return few fields like firstname,lastname,title etc of a user - list inc company assoc
        /// </summary>
        /// <param name="id"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<List<Shared.Models.Entities.UserInfoView>> ListUserDetailInfoWithCompanyAssocs(UserInfoFilterModel filter, int orgId)
        {
            List<long> lstIds = filter.UserId;
            List<int> companyId = filter.CompanyDetailsId;
            return await (from user in _readOnlyDbContext.UserDetails
                          where user.OrgId == orgId && lstIds.Contains(user.Id)
                          select new Shared.Models.Entities.UserInfoView
                          {
                              Id = user.Id,
                              OrgId = user.OrgId,
                              FirstName = user.FirstName,
                              SurName = user.SurName,
                              TitleId = user.TitleId,
                              Title = (user.TitleId != null) ? EnumExtensions.GetDescription((TitleType)user.TitleId) : string.Empty,
                              ProviderTypeId = user.ProviderTypeId,
                              LstUserCompanyInfo = (from UCA in _readOnlyDbContext.UserCompanyAssocs
                                                   where UCA.OrgId == orgId
                                                    && UCA.StatusId == (short)Status.Active && UCA.UserDetailsId == user.Id &&( (companyId==null || companyId.Count==0) || companyId.Contains(UCA.CompanyId))
                                                   select new Shared.Models.Entities.UserCompanyInfo
                                                   {
                                                       Id = UCA.Id,
                                                       UserCompanyId = UCA.CompanyId,
                                                       IsDefault = UCA.IsDefault ,
                                                       ProviderNumber = UCA.ProviderNumber                                                       
                                                   }).ToList()
                          }).ToListAsync();
        }

    }
}
