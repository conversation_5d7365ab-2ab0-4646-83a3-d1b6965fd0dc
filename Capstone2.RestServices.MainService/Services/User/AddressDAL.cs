﻿using AutoMapper;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.User.Context;
using Capstone2.RestServices.User.Interfaces;
using Capstone2.RestServices.User.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.RestServices.User.Services
{
    public class AddressDAL : IAddressDAL
    {
        public readonly AppSettings _appSettings;
        public readonly ReadOnlyUserDBContext _readOnlyDbContext;
        public UpdatableUserDBContext _updatableDBContext;
        public IMapper _mapper;
        public AddressDAL(IOptions<AppSettings> appSettings, ReadOnlyUserDBContext readOnlyDbContext, UpdatableUserDBContext updatableDBContext, IMapper mapper)
        {
            _appSettings = appSettings.Value;
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
            _mapper = mapper;
        }

        public async Task<List<Address>> GetAddresses(long Id, int orgId)
        {
            return await _readOnlyDbContext.Addresses.Where(x => x.UserDetailsId == Id && x.OrgId == orgId).AsNoTracking().ToListAsync();
        }

        public async Task UpdateAddressList(List<Address> removeList)
        {
            _updatableDBContext.Addresses.UpdateRange(removeList);
            await _updatableDBContext.SaveChangesAsync();

        }
        public async Task AddAddressList(List<Address> addList)
        {
            _updatableDBContext.Addresses.AddRange(addList);
            await _updatableDBContext.SaveChangesAsync();
        }

    }
}
