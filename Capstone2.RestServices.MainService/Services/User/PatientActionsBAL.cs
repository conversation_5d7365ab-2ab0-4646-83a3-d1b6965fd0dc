﻿using AutoMapper;
using Capstone2.Framework.Business.Common;
using Capstone2.Framework.RestApi;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.User.Interfaces;
using Capstone2.RestServices.User.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.Azure.KeyVault.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.Serialization.Formatters.Binary;
using System.Security.Cryptography;
using System.Threading.Tasks;
using System.Transactions;

namespace Capstone2.RestServices.User.Services
{
    public class PatientActionsBAL : IPatientActionsBAL
    {

        public readonly AppSettings _appSettings;
        public readonly IPatientActionsDAL _patientActionsDAL;
        private readonly ILogger<PatientActionsBAL> _logger;
        public IMapper _mapper;
        public PatientActionsBAL(IOptions<AppSettings> appSettings, IPatientActionsDAL patientActionsDAL, ILogger<PatientActionsBAL> logger, IMapper mapper)
        {
            _appSettings = appSettings.Value;
            _patientActionsDAL = patientActionsDAL;
            _logger = logger;
            _mapper = mapper;
        }

        /// <summary>
        /// AddPatientActions
        /// </summary>
        /// <param name="actions"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> AddPatientActions(List<PatientAction> actions, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<long?> apiResponse = new();
            var userId = baseHttpRequestContext.UserId;
            var orgId = baseHttpRequestContext.OrgId;
            long id = 0;
            PatientActionsPathologyDetails pathologyDetails = null;

            if (actions.FirstOrDefault()?.PathologyRequestId != null)
            {
                // this is only for checking IsUrgent
                pathologyDetails = await FetchPathologyDetailsById(actions.FirstOrDefault()?.PathologyRequestId, baseHttpRequestContext);
            }

            foreach (var action in actions)
            {
                action.OrgId = orgId;
                //action.StatusId = (short)Status.Active;  // commeting since its passing from FE
                action.IsPending = false;
                action.IsClosed = false;
                action.CreatedBy = userId;  
                if(action.PathologyRequestId is not null)
                {
                    action.PatientDetailsId = pathologyDetails.PatientId;
                }
                if(action.LetterId is not null)
                {

                }

                //action.assignedto need to made as null from frontend
                if (action.PatientActionUserAssocs is not null && action.PatientActionUserAssocs.Any())
                {
                    action.PatientActionUserAssocs.ToList().ForEach(a => { a.OrgId = orgId; a.CreatedBy = userId; a.StatusId = (short)Status.Active; });

                }


            }

            using (var transaction = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
            {
                var pactions = await _patientActionsDAL.AddPatientActions(actions);
                id = pactions.Count;
                foreach (var action in pactions) {

                    if (action.PathologyRequestId == null && action.LetterId == null)
                    {                       
                        List<VariableJSON> lstVariableJSON = await GenerateVariableJSONForAdd(action, orgId);
                        var DescString = (action.Description == null) ? string.Empty : (action.Description.Length < 50) ? action.Description : action.Description.Substring(0, 50);
                        ActivityLogInfo activityLog = CreateActivityLogEntry((int)ActivityLogOps.Create, lstVariableJSON, (long)action.Id, action.PatientDetailsId, (bool)action.IsClosed ? (short)31 : (short)30, DescString);

                        string patientAPiUrl = _appSettings.ApiUrls["PatientServiceUrl"] + "/patient/" + action.PatientDetailsId + "/activity_logs";
                        RestClient restClient = new RestClient(patientAPiUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
                        var activitylogApiResponse = await restClient.PostAsync<ApiResponse<long>>(patientAPiUrl, activityLog);

                        if (activitylogApiResponse.Result == 0 && activitylogApiResponse.StatusCode != StatusCodes.Status200OK)
                        {
                            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                            apiResponse.Message = "Add Activity Log failed";
                            apiResponse.Result = id;
                            return apiResponse;
                        }
                    }
                }

                transaction.Complete();
            }

            if (id > 0)
            {
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Result = id;
                apiResponse.Message = "Success";
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = null;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("Action cannot be added at this time.Please try again later.");
            }
            return apiResponse;
        }

        public async Task<ApiResponse<bool>> HidePatientActionBAL(long actionId, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<bool> apiResponse = new();
            try
            {
                PatientAction action = await _patientActionsDAL.GetPatientActionsSyncById(baseHttpRequestContext.OrgId, actionId);
                if (action == null)
                {
                    apiResponse.StatusCode = StatusCodes.Status404NotFound;
                    apiResponse.Message = "Patient action not found";
                    apiResponse.Result = false;
                    apiResponse.Errors.Add("The specified patient action does not exist.");
                    return apiResponse;
                }

                List<long> hiddenFor = string.IsNullOrEmpty(action.HiddenFor)
                    ? new List<long>()
                    : JsonConvert.DeserializeObject<List<long>>(action.HiddenFor);

                if (!hiddenFor.Contains(baseHttpRequestContext.UserId))
                {
                    hiddenFor.Add(baseHttpRequestContext.UserId);
                    action.HiddenFor = JsonConvert.SerializeObject(hiddenFor);
                    action.ModifiedBy = baseHttpRequestContext.UserId;
                    action.ModifiedDate = DateTime.UtcNow;

                    int rowsAffected = await _patientActionsDAL.EditPatientActions(action);
                    if (rowsAffected > 0)
                    {
                        apiResponse.StatusCode = StatusCodes.Status200OK;
                        apiResponse.Message = "Patient action hidden successfully";
                        apiResponse.Result = true;
                    }
                    else
                    {
                        apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                        apiResponse.Message = "Failed to hide patient action";
                        apiResponse.Result = false;
                        apiResponse.Errors.Add("Error occurred while hiding the patient action.");
                    }
                }
                else
                {
                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    apiResponse.Message = "Patient action already hidden for this user";
                    apiResponse.Result = true;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error hiding patient action: {ex.Message} {ex.StackTrace}");
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Message = "Internal server error";
                apiResponse.Result = false;
                apiResponse.Errors.Add("An error occurred while processing the request.");
            }
            return apiResponse;
        }

        /// <summary>
        /// Method to fetch PatientActions
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<QueryResultList<PatientActionsView>>> GetPatientActions(QueryModel queryModel, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<QueryResultList<PatientActionsView>> apiResposne = new();
            QueryResultList<PatientActionsView> actions = new();             
            var filterModel = PrepareFilterParameters(queryModel.Filter);
            if (filterModel.AssignedToUserIds != null && filterModel.AssignedByUserIds != null && ((filterModel.AssignedByUserIds.Count == 1 && filterModel.AssignedByUserIds.Contains(baseHttpRequestContext.UserId)) || (filterModel.AssignedToUserIds.Count == 1 && filterModel.AssignedToUserIds.Contains(baseHttpRequestContext.UserId))))
            {
                    actions = await _patientActionsDAL.GetPatientActions(baseHttpRequestContext.UserId,baseHttpRequestContext.OrgId, queryModel, filterModel);

            }
            else if (filterModel.AssignedToUserIds != null && filterModel.AssignedByUserIds == null && filterModel.AssignedToUserIds.Count == 1 && filterModel.AssignedToUserIds.Contains(baseHttpRequestContext.UserId))
            {
                actions = await _patientActionsDAL.GetPatientActions(baseHttpRequestContext.UserId,baseHttpRequestContext.OrgId, queryModel, filterModel);
            }
            else
            {
                RolesPermissionsAssoc permission = await _patientActionsDAL.GetPermissionOnRoleId(baseHttpRequestContext.OrgId, baseHttpRequestContext.RoleId, (short)ApiPermission.Patient_Actions_Report);
                if (permission == null)
                {
                    apiResposne.StatusCode = StatusCodes.Status403Forbidden;
                    apiResposne.Errors.Add("Not an Authorized User");
                    apiResposne.Result = null;
                    return apiResposne;
                }
                if (permission.IsAllowed == false)
                {
                    apiResposne.StatusCode = StatusCodes.Status403Forbidden;
                    apiResposne.Errors.Add("Not an Authorized User");
                    apiResposne.Result = null;
                    return apiResposne;
                }
                actions = await _patientActionsDAL.GetPatientActions(baseHttpRequestContext.UserId,baseHttpRequestContext.OrgId, queryModel, filterModel);


            }


            if (actions?.ItemRecords != null)
            {
                // TODO: this logic has to change to implement parallel call or with multiple IDs
                foreach (var action in actions.ItemRecords)
                {
                    if (action.PathologyRequestId != null)
                    {
                        action.PathologyDetails = await FetchPathologyDetailsById(action.PathologyRequestId, baseHttpRequestContext);
                    }

                    if (action.LetterId != null)
                    {
                        action.LetterDetails = await FetchLetterDetailsById(action.LetterId, baseHttpRequestContext);
                    }

                    action.ActionDescription = EnumExtensions.GetDescription((PatientActions)action.ActionId);
                }
            }
            apiResposne.Result = actions;
            apiResposne.StatusCode = StatusCodes.Status200OK;
            apiResposne.Message = "Success";
            return apiResposne;
        }

        private PatientActionsFilterModel PrepareFilterParameters(string filter)
        {
            if (!(String.IsNullOrEmpty(filter)))
            {
                return JsonConvert.DeserializeObject<PatientActionsFilterModel>(filter);
            }
            else
            {
                return null;
            }
        }

        private async Task<PatientActionsPathologyDetails> FetchPathologyDetailsById(long? pathologyRequestId, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<PatientActionsPathologyDetails> apiResponseUsers = new();
            var patientSeviceUrl = _appSettings.ApiUrls["PatientServiceUrl"] + "/patient/pathology_details_by_id/" + pathologyRequestId;

            var restClientUser = new RestClient(patientSeviceUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            apiResponseUsers = await restClientUser.GetAsync<ApiResponse<PatientActionsPathologyDetails>>(patientSeviceUrl);
            if (apiResponseUsers?.StatusCode == StatusCodes.Status200OK && apiResponseUsers?.Result is not null)
            {
                return apiResponseUsers.Result;
            }
            return null;
        }

        private async Task<PatientActionsPathologyDetails> FetchPathologyDetailsByMediaId(long? pathologyResponseMediaId, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<PatientActionsPathologyDetails> apiResponseUsers = new();
            var patientSeviceUrl = _appSettings.ApiUrls["PatientServiceUrl"] + "/patient/pathology_details_by_media_id/" + pathologyResponseMediaId;

            var restClientUser = new RestClient(patientSeviceUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            apiResponseUsers = await restClientUser.GetAsync<ApiResponse<PatientActionsPathologyDetails>>(patientSeviceUrl);
            if (apiResponseUsers?.StatusCode == StatusCodes.Status200OK && apiResponseUsers?.Result is not null)
            {
                return apiResponseUsers.Result;
            }
            return null;
        }

        private async Task<PatientActionsLetterDetails> FetchLetterDetailsById(long? letterId, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<PatientActionsLetterDetails> apiResponseUsers = new();
            var patientSeviceUrl = _appSettings.ApiUrls["PatientServiceUrl"] + "/patient/letter_details_by_id/" + letterId;

            var restClientUser = new RestClient(patientSeviceUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
            apiResponseUsers = await restClientUser.GetAsync<ApiResponse<PatientActionsLetterDetails>>(patientSeviceUrl);
            if (apiResponseUsers?.StatusCode == StatusCodes.Status200OK && apiResponseUsers?.Result is not null)
            {
                return apiResponseUsers.Result;
            }
            return null;
        }

        /// <summary>
        /// EditPatientActions
        /// </summary>
        /// <param name="actions"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> EditPatientActions(List<PatientAction> actions, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<long?> apiResponse = new();
            var userId = baseHttpRequestContext.UserId;
            var orgId = baseHttpRequestContext.OrgId;
            long id = 0;

            var dbActions = await _patientActionsDAL.GetPatientActionsSyncByMultipleIds(orgId, actions.Select(x => (long)x.Id).ToList());

            foreach (var action in actions)
            {
                action.OrgId = orgId;
                action.CreatedBy = dbActions.Find(x => x.Id == action.Id).CreatedBy;
                action.CreatedDate = dbActions.Find(x => x.Id == action.Id).CreatedDate;
                action.ModifiedBy = userId;
                action.ModifiedDate = DateTime.UtcNow;

                if (action.PatientActionUserAssocs != null &&  action.PatientActionUserAssocs.Any())
                {
                    action.PatientActionUserAssocs.ToList().ForEach(a => { a.OrgId = orgId; a.PatientActionsId = (long)action.Id; });
                }

                if (action.isCommentAdded == true || action.isPendingStatusChanged == true)
                {
                    action.HiddenFor = null;
                }


                var removeUserList = await _patientActionsDAL.GetPatientActionUserAssoc(orgId, (long)action.Id);
                dbActions.Where(x => x.Id == action.Id).FirstOrDefault().PatientActionUserAssocs = DeepCopyList(removeUserList);

                var inputUserList = action.PatientActionUserAssocs;
                action.PatientActionUserAssocs = null;
                List<PatientActionUserAssoc> userList = EditPatientActionUserAssocs(removeUserList, inputUserList, userId);
                action.PatientActionUserAssocs = userList;

            }

            using (var transaction = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
            {

                id = await _patientActionsDAL.EditMultiPatientActions(actions);

                if (id > 0)
                {
                    foreach (var action in actions)
                    {
                        if (action.PathologyRequestId == null && action.LetterId == null)
                        {
                            PatientAction dbAction = dbActions.Where(x => x.Id == action.Id).FirstOrDefault();
                            var descString = (action.Description == null) ? string.Empty : (action.Description.Length < 50) ? action.Description : action.Description.Substring(0, 50);

                            List<VariableJSON> lstVariableJSON = await GenerateVariableJSON(dbAction, action, orgId);
                            ActivityLogChildEntryInfo activityLogChild = CreateActivityLogChildEntry((int)ActivityLogOps.Update, lstVariableJSON, (long)action.Id, action.PatientDetailsId, (bool)action.IsClosed ? (short)31 : (short)30, descString, null, null);
                            string patientAPiUrl = _appSettings.ApiUrls["PatientServiceUrl"] + "/patient/" + action.PatientDetailsId + "/activity_logs/activitylogchild_entries";
                            RestClient restClient = new RestClient(patientAPiUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
                            var activitylogApiResponse = await restClient.PostAsync<ApiResponse<long?>>(patientAPiUrl, activityLogChild);

                            if (activitylogApiResponse is null && activitylogApiResponse.StatusCode != StatusCodes.Status200OK)
                            {
                                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                                apiResponse.Message = "Add Activity Log failed";
                                apiResponse.Result = 0;
                                return apiResponse;
                            }
                        }
                    }
                }
                transaction.Complete();
            }

            if (id > 0)
            {
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Result = id;
                apiResponse.Message = "Success";
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = null;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("Action cannot be Edited at this time.Please try again later.");
            }
            return apiResponse;


        }


        public static List<PatientActionUserAssoc> DeepCopyList(List<PatientActionUserAssoc> list)
        {
            var json = JsonConvert.SerializeObject(list);
            return JsonConvert.DeserializeObject<List<PatientActionUserAssoc>>(json);
        }

        /// <summary>
        /// UpdatePatientActionsByPathologyID
        /// </summary>
        /// <param name="pathologyId"></param>
        /// <param name="oldAssigneeId"></param>
        /// <param name="newAssigneeId"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> UpdatePatientActionsByPathologyID(long pathologyId, long oldAssigneeId, long newAssigneeId, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<long?> apiResponse = new();
            var userId = baseHttpRequestContext.UserId;
            var payload = new List<PatientActionUserAssoc>();
            int id = 0;

            var actions = await _patientActionsDAL.GetPAUserAssocForPathologyId(baseHttpRequestContext.OrgId, pathologyId);

            foreach (var action in actions.Where(p => p.UserDetailsId == oldAssigneeId))
            {
               
                action.UserDetailsId = newAssigneeId;
                action.ModifiedBy = userId;
                action.ModifiedDate = DateTime.UtcNow;
                payload.Add(action);
            }

            id = await _patientActionsDAL.EditMultiplePAUserAssoc(actions);
            if (id > 0)
            {
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Result = id;
                apiResponse.Message = "Success";
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = null;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("Action cannot be Edited at this time.Please try again later.");
            }
            return apiResponse;


        }

        /// <summary>
        /// DeletePatientActionsByPathologyId
        /// </summary>
        /// <param name="pathologyId"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> DeletePatientActionsByPathologyId(long pathologyId, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<long?> apiResponse = new();
            var userId = baseHttpRequestContext.UserId;
            var payload = new List<PatientAction>();
            var dbActions = new List<PatientAction>();
            int id = 0;

            var actions = await _patientActionsDAL.GetPatientActionsByPathologyRequestId(baseHttpRequestContext.OrgId, pathologyId);

            foreach (var action in actions)
            {
               // dbActions.Add(DeepCopyPA(action));
                action.StatusId = (short)Status.Deleted;
                action.ModifiedBy = userId;
                action.ModifiedDate = DateTime.UtcNow;
                payload.Add(action);
            }

            id = await _patientActionsDAL.EditMultiPatientActions(payload);

            /* using (var transaction = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
            {

                

                if (id > 0)
                {
                    foreach (var action in payload)
                    {
                        PatientAction dbAction = dbActions.Where(x => x.Id == action.Id).FirstOrDefault();
                        var descString = (action.Description == null) ? string.Empty : (action.Description.Length < 50) ? action.Description : action.Description.Substring(0, 50);

                        List<VariableJSON> lstVariableJSON = await GenerateVariableJSON(dbAction, action, baseHttpRequestContext.OrgId);
                        ActivityLogChildEntryInfo activityLogChild = CreateActivityLogChildEntry((int)ActivityLogOps.Update, lstVariableJSON, (long)action.Id, action.PatientDetailsId, action.StatusId, descString, null, null);
                        string patientAPiUrl = _appSettings.ApiUrls["PatientServiceUrl"] + "/patient/" + action.PatientDetailsId + "/activity_logs/activitylogchild_entries";
                        RestClient restClient = new RestClient(patientAPiUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
                        var activitylogApiResponse = await restClient.PostAsync<ApiResponse<long?>>(patientAPiUrl, activityLogChild);

                        if (activitylogApiResponse is null && activitylogApiResponse.StatusCode != StatusCodes.Status200OK)
                        {
                            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                            apiResponse.Message = "Add Activity Log failed";
                            apiResponse.Result = 0;
                            return apiResponse;
                        }
                    }
                }
                transaction.Complete();
            } */
            if (id > 0)
            {
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Result = id;
                apiResponse.Message = "Success";
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = null;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("Action cannot be Edited at this time.Please try again later.");
            }
            return apiResponse;

            

        }

        /// <summary>
        /// DeletePatientActions
        /// </summary>
        /// <param name="deleteIds"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> DeletePatientActions(string deleteIds, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<long?> apiResponse = new();
            var userId = baseHttpRequestContext.UserId;
            var orgId = baseHttpRequestContext.OrgId;
            var payload = new List<PatientAction>();
            var dbActionsLog = new List<PatientAction>();
            int id = 0;

            if (!string.IsNullOrWhiteSpace(deleteIds))
            {
                List<long> lstIds = deleteIds.Split(',').ToList().Select(s => long.Parse(s)).ToList();
                if (lstIds.Count > 0)
                {
                    var dbActions = await _patientActionsDAL.GetPatientActionsSyncByMultipleIds(orgId, lstIds);

                    foreach (var action in dbActions)
                    {
                        if (lstIds.Contains((long)action.Id))
                        {
                            dbActionsLog.Add(DeepCopyPA(action));
                            action.StatusId = (short)Status.Deleted;
                            action.ModifiedBy = userId;
                            action.ModifiedDate = DateTime.UtcNow;
                            payload.Add(action);
                        }
                    }

                }
            }

            using (var transaction = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
            {

                id = await _patientActionsDAL.EditMultiPatientActions(payload);

                if (id > 0)
                {
                    foreach (var action in payload)
                    {
                        if (action.PathologyRequestId == null && action.LetterId == null)
                        {
                            PatientAction dbAction = dbActionsLog.Where(x => x.Id == action.Id).FirstOrDefault();
                            var descString = (action.Description == null) ? string.Empty : (action.Description.Length < 50) ? action.Description : action.Description.Substring(0, 50);

                            List<VariableJSON> lstVariableJSON = await GenerateVariableJSON(dbAction, action, baseHttpRequestContext.OrgId);
                            ActivityLogChildEntryInfo activityLogChild = CreateActivityLogChildEntry((int)ActivityLogOps.Update, lstVariableJSON, (long)action.Id, action.PatientDetailsId, (bool)action.IsClosed ? (short)31:(short)30, descString, null, null);
                            string patientAPiUrl = _appSettings.ApiUrls["PatientServiceUrl"] + "/patient/" + action.PatientDetailsId + "/activity_logs/activitylogchild_entries";
                            RestClient restClient = new RestClient(patientAPiUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
                            var activitylogApiResponse = await restClient.PostAsync<ApiResponse<long?>>(patientAPiUrl, activityLogChild);

                            if (activitylogApiResponse is null && activitylogApiResponse.StatusCode != StatusCodes.Status200OK)
                            {
                                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                                apiResponse.Message = "Add Activity Log failed";
                                apiResponse.Result = 0;
                                return apiResponse;
                            }
                        }
                    }
                }
                transaction.Complete();
            }
            if (id > 0)
            {
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Result = id;
                apiResponse.Message = "Success";
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = null;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("Action cannot be Edited at this time.Please try again later.");
            }
            return apiResponse;

        }

        /// <summary>
        /// GetPatientActionsByMediaId
        /// </summary>
        /// <param name="medeaId"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<List<PatientAction>>> GetPatientActionsByMediaId(long medeaId, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<List<PatientAction>> apiResposne = new();
            var actions = await _patientActionsDAL.GetPatientActionsByMediaId(baseHttpRequestContext.OrgId, medeaId);

            apiResposne.Result = actions;
            apiResposne.StatusCode = StatusCodes.Status200OK;
            apiResposne.Message = "Success";
            return apiResposne;
        }

        /// <summary>
        /// GetPatientActionsByPathologyId
        /// </summary>
        /// <param name="pathologyId"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<List<PatientActionsView>>> GetPatientActionsByPathologyId(long pathologyId, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<List<PatientActionsView>> apiResposne = new();
            var actions = await _patientActionsDAL.GetPAViewByPathologyRequestId(baseHttpRequestContext.OrgId, pathologyId);

            apiResposne.Result = actions;
            apiResposne.StatusCode = StatusCodes.Status200OK;
            apiResposne.Message = "Success";
            return apiResposne;
        }

        /// <summary>
        /// GetPatientActionsByLetterId
        /// </summary>
        /// <param name="letterId"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<List<PatientAction>>> GetPatientActionsByLetterId(long letterId, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<List<PatientAction>> apiResposne = new();
            var actions = await _patientActionsDAL.GetPatientActionsByLetterId(baseHttpRequestContext.OrgId, letterId);

            apiResposne.Result = actions;
            apiResposne.StatusCode = StatusCodes.Status200OK;
            apiResposne.Message = "Success";
            return apiResposne;
        }

        /// <summary>
        /// GetPatientActionsNotificationCount
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<List<PatientActionsNotificationCount>>> GetPatientActionsNotificationCount(BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<List<PatientActionsNotificationCount>> apiResposne = new();
            var actions = await _patientActionsDAL.GetPatientActionsNotificationCount(baseHttpRequestContext.OrgId);

            apiResposne.Result = actions;
            apiResposne.StatusCode = StatusCodes.Status200OK;
            apiResposne.Message = "Success";
            return apiResposne;
        }

        /// <summary>
        /// GetPatientActionsNotificationCountByUser
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<List<PatientActionsNotificationCount>>> GetPatientActionsNotificationCountByUser(long userId, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<List<PatientActionsNotificationCount>> apiResposne = new();
            var actions = await _patientActionsDAL.GetPatientActionsNotificationCountByUserId(baseHttpRequestContext.OrgId, userId);

            apiResposne.Result = actions;
            apiResposne.StatusCode = StatusCodes.Status200OK;
            apiResposne.Message = "Success";
            return apiResposne;
        }

        /// <summary>
        /// EditParentPatientActionStatus - only for update status for Pathology Request  - Waiting for Reports
        /// </summary>
        /// <param name="pathologyRequestId"></param>
        /// <param name="statusId"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<long?>> EditParentPatientActionStatus(long pathologyRequestId, short statusId, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<long?> apiResponse = new();
            var userId = baseHttpRequestContext.UserId;
            var orgId = baseHttpRequestContext.OrgId;
            long id = 0;

            var dbAction = await _patientActionsDAL.GetPatientActionsByPathologyRequestId(orgId, pathologyRequestId);

            var action = dbAction?.Find(x => x.StatusId == (short)Status.Active && x.ActionId == 303);  // if any open "Waiting for Reports"

            if(action == null ) {

                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Result = null;
                apiResponse.Message = "Success";
                return apiResponse;

            }
            
            if (action != null)
            {
                action.StatusId = statusId;
                action.ModifiedBy = userId;
                action.ModifiedDate = DateTime.UtcNow;

                id = await _patientActionsDAL.EditPatientActions(action);

            }

           if (id > 0)
            {
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Result = id;
                apiResponse.Message = "Success";
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = null;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("Action cannot be Updated at this time.Please try again later.");
            }
            return apiResponse;
        }

        public async Task<ApiResponse<long?>> AddPACommentBAL(long action_id, PatientActionComment inputComment, BaseHttpRequestContext baseHttpRequestContext)
        {
            inputComment.OrgId = baseHttpRequestContext.OrgId;
            inputComment.StatusId = (short)Status.Active;
            inputComment.CreatedBy = baseHttpRequestContext.UserId;
            inputComment.CreatedDate = DateTime.UtcNow;
            inputComment.PatientActionId = action_id;
            List<VariableJSON> lstVariableJson = new();
            long id = 0;
           
            PatientAction action = await _patientActionsDAL.GetPatientActionsSyncById( baseHttpRequestContext.OrgId,action_id);
            
            ApiResponse<long?> apiResponse = new();
            if (inputComment.CommentDescription is not null)
            {

                using (var transaction = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
                {

                     id = await _patientActionsDAL.AddPACommentDAL(inputComment);

                    if (id > 0)
                    {
                        action.HiddenFor = null;
                        action.ModifiedBy = baseHttpRequestContext.UserId;
                        action.ModifiedDate = DateTime.UtcNow;
                        await _patientActionsDAL.EditPatientActions(action);

                        VariableJSON variableJSON = new VariableJSON();
                        variableJSON.Operation = Enum.GetName(ActivityLogOps.Create);
                        variableJSON.Column = "comment";
                        variableJSON.OldValue = null;
                        variableJSON.NewValue = inputComment.CommentDescription;
                        lstVariableJson.Add(variableJSON);


                        var descString = (action.Description == null) ? string.Empty : (action.Description.Length < 50) ? action.Description : action.Description.Substring(0, 50);
                        ActivityLogChildEntryInfo activityLogChild = CreateActivityLogChildEntry((int)ActivityLogOps.Comment, lstVariableJson, (long)action_id, action.PatientDetailsId, (bool)action.IsClosed ? (short)31 : (short)30, descString, id, null);
                        string patientAPiUrl = _appSettings.ApiUrls["PatientServiceUrl"] + "/patient/" + action.PatientDetailsId + "/activity_logs/activitylogchild_entries";
                        RestClient restClient = new RestClient(patientAPiUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
                        var activitylogApiResponse = await restClient.PostAsync<ApiResponse<long?>>(patientAPiUrl, activityLogChild);

                        if (activitylogApiResponse is null && activitylogApiResponse.StatusCode != StatusCodes.Status200OK)
                        {
                            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                            apiResponse.Message = "Add Activity Log failed";
                            apiResponse.Result = 0;
                            return apiResponse;
                        }
                            
                    }
                    transaction.Complete();
                }
               

                if (id > 0)
                {
                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    apiResponse.Result = id;
                    apiResponse.Message = "Success";

                }
                else
                {
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Result = null;
                    apiResponse.Message = "Failure";
                    apiResponse.Errors.Add("Comment cannot be added at this time.Please try again later.");
                }
                return apiResponse;
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = null;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("No Text in the Comment. Please try again later.");
            }
            return apiResponse;
        }

        /// <summary>
        /// Method to fetch Patient Action details
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<PatientActionsView>> GetPatientActionBAL(BaseHttpRequestContext baseHttpRequestContext, long id)
        {
            int orgId = baseHttpRequestContext.OrgId;
            long readingUserId = baseHttpRequestContext.UserId;
            ApiResponse<PatientActionsView> apiResposne = new();
            PatientActionsView actionFromDB = await _patientActionsDAL.GetPatientActionById(orgId, id);
            List<PatientActionComment> editCommentList = new();
            if (actionFromDB is null)
            {
                apiResposne.Result = null;
                apiResposne.StatusCode = StatusCodes.Status400BadRequest;
                apiResposne.Message = "Failure";
                apiResposne.Errors.Add("The Patient Action doesnot exist.");
                return apiResposne;
            }

            else
            {
                actionFromDB.PatientActionCommentViews = await _patientActionsDAL.GetPatientActionComments(id);

                if(actionFromDB.PatientActionCommentViews is not null && actionFromDB.PatientActionCommentViews.Count > 0)
                {
                    List<long> AssignedUserIds = actionFromDB.PatientActionUserAssocs.Select(x => x.UserDetailsId).ToList();
                    AssignedUserIds.Add(actionFromDB.UserDetailsId);

                    foreach (var paComment in actionFromDB.PatientActionCommentViews)
                    {
                       if(paComment.CommentReadBy is null && AssignedUserIds.Contains(readingUserId) && readingUserId != paComment.CreatedBy)

                       {
                            PatientActionComment commentToAdd = _mapper.Map<PatientActionCommentView, PatientActionComment>(paComment);
                            commentToAdd.CommentReadBy = readingUserId;
                            commentToAdd.CommentReadDate = DateTime.UtcNow;
                            editCommentList.Add(commentToAdd);
                           
                       }
                    }

                    if (editCommentList.Count > 0)
                    {

                        int updatedCount = await _patientActionsDAL.EditPAComments(editCommentList);
                    }

                }



                if (actionFromDB.PathologyRequestId != null)
                {
                    actionFromDB.PathologyDetails = await FetchPathologyDetailsById(actionFromDB.PathologyRequestId, baseHttpRequestContext);
                }

                if (actionFromDB.LetterId != null)
                {
                    actionFromDB.LetterDetails = await FetchLetterDetailsById(actionFromDB.LetterId, baseHttpRequestContext);
                }

                actionFromDB.ActionDescription = EnumExtensions.GetDescription((PatientActions)actionFromDB.ActionId);


                apiResposne.Result = actionFromDB;
                apiResposne.StatusCode = StatusCodes.Status200OK;
                apiResposne.Message = "Success";
                return apiResposne;
            }
        }

        private ActivityLogInfo CreateActivityLogEntry(int activityLogOp, List<VariableJSON> lstVariableJSON, long id, long? patientDetailsId, short? paStatus, string notes)
        {
            ActivityLogInfo activityLog = new();
            ActivityLogChildEntryInfo activityLogChild = new();
            activityLog.ActivityLogTypeId = (short)ActivityLogType.Patient_Actions;
            activityLog.PatientDetailsId = (long)patientDetailsId;
            activityLog.EntityId = id;
            activityLog.Description = notes;
            activityLog.ActivityStatusId = paStatus;
            activityLogChild.VariableJson = (lstVariableJSON is null || lstVariableJSON.Count == 0) ? null : JsonConvert.SerializeObject(lstVariableJSON);
            activityLogChild.ActivityStatusId = paStatus;
            activityLog.ActivityLogChildEntries.Add(activityLogChild);
            if ((int)ActivityLogOps.Create == activityLogOp)
            {
                activityLogChild.Description = "Patient Action Created";
            }


            //else if((int)ActivityLogOps.Update == activityLogOp)
            //{

            //        activityLogChild.VariableJson = (variableJSON is null) ? null : JsonConvert.SerializeObject(variableJSON);
            //        activityLogChild.ActivityStatusId = appointmentStatus;
            //        activityLog.ActivityLogChildEntries.Add(activityLogChild);

            //}
            return activityLog;
        }

        private async Task<List<VariableJSON>> GenerateVariableJSON(PatientAction OldObject, PatientAction newObject, int orgId)
        {
            var props = OldObject.GetType().GetProperties();
            List<VariableJSON> lstVariableJSON = new();
            bool startTimeEdit = false;
            bool endTimeEdit = false;
            List<long> newUserList = new();
            List<long> finalNewUserList = new();

            foreach (PropertyInfo i in props)
            {
                if (checkAuditCoumns(i.Name))
                {
                    var oldVal = i.GetValue(OldObject, null);
                    var newVal = i.GetValue(newObject, null);
                    bool isUpdated = false;
                    if (oldVal is null && newVal is not null || oldVal is not null && newVal is null)
                    {
                        isUpdated = true;
                    }
                    else if (oldVal is not null && newVal is not null && !oldVal.Equals(newVal))
                    {
                        isUpdated = true;

                    }

                    if (isUpdated)
                    {

                        VariableJSON variableJson = new();
                        variableJson.Operation = Enum.GetName(ActivityLogOps.Update);
                        switch (i.Name.ToLower())
                        {

                            case "statusid":
                                {
                                    variableJson.Column = "Status";
                                    variableJson.OldValue = Enum.GetName((Status)((short)oldVal));
                                    variableJson.NewValue = Enum.GetName((Status)((short)newVal));
                                    break;
                                }

                            case "duedate":
                                {
                                    variableJson.Column = "Due Date";
                                    variableJson.OldValue = (oldVal is null) ? null : ((DateTime)oldVal).ToString("dd-MM-yyyy");
                                    variableJson.NewValue = (newVal is null) ? null : ((DateTime)newVal).ToString("dd-MM-yyyy");
                                    break;
                                }
                            case "isurgent":
                                {
                                    variableJson.Column = "IsUrgent";
                                    variableJson.OldValue = (oldVal is null) ? null : oldVal;
                                    variableJson.NewValue = (newVal is null) ? false : newVal;
                                    break;
                                }
                            case "isclosed":
                                {
                                    variableJson.Column = "Closed";
                                    variableJson.OldValue = (oldVal is null) ? null : oldVal;
                                    variableJson.NewValue = (newVal is null) ? false : newVal;
                                    break;
                                }
                           
                            case "userdetailsid":
                                {
                                    variableJson.Column = "Assigned User";
                                    List<UserDetailInfo> lstProviders = await _patientActionsDAL.FetchUserDetailsFromIds(new List<long> { (long)oldVal, (long)newVal }, orgId);
                                    lstProviders.ForEach(user =>
                                    {
                                        if (oldVal is not null && user.Id == (long)oldVal)
                                        {
                                            string title = (user.TitleId is null) ? string.Empty : EnumExtensions.GetDescription((TitleType)user.TitleId);
                                            variableJson.OldValue = title + " " + user.FirstName + " " + user.SurName;
                                        }
                                        if (newVal is not null && user.Id == (long)newVal)
                                        {
                                            string title = (user.TitleId is null) ? string.Empty : EnumExtensions.GetDescription((TitleType)user.TitleId);
                                            variableJson.NewValue = title + " " + user.FirstName + " " + user.SurName;
                                        }
                                    });
                                    break;
                                }
                            case "patientdetailsid":
                                {
                                    variableJson.Column = "Patient";
                                    List<PatientDetailInfo> lstpatient = await _patientActionsDAL.FetchPatinetDetailsFromIds(new List<long> { (long)oldVal, (long)newVal }, orgId);
                                    lstpatient.ForEach(patient =>
                                    {
                                        if (oldVal is not null && patient.Id == (long)oldVal)
                                        {
                                           // string title = (user.TitleId is null) ? string.Empty : EnumExtensions.GetDescription((TitleType)user.TitleId);
                                            variableJson.OldValue = patient.FirstName + " " + patient.SurName;
                                        }
                                        if (newVal is not null && patient.Id == (long)newVal)
                                        {
                                           // string title = (user.TitleId is null) ? string.Empty : EnumExtensions.GetDescription((TitleType)user.TitleId);
                                            variableJson.NewValue = patient.FirstName + " " + patient.SurName;
                                        }
                                    });
                                    break;
                                }

                            default:
                                {
                                    variableJson.NewValue = newVal;
                                    variableJson.OldValue = oldVal;
                                    variableJson.Column = i.Name;
                                    break;
                                }

                        }
                        
                            lstVariableJSON.Add(variableJson);
                    }

                }
            }

            List<long> oldUserList = OldObject.PatientActionUserAssocs.Where(y => y.StatusId == (short)Status.Active).Select(x => x.UserDetailsId).ToList();
           
            //The New List which is updated  will not have the UserIds if they were present before and there NewList also.So to show them in the activity log the below logic is in place 
            
            
            
            foreach(long userid in oldUserList)
            {

                PatientActionUserAssoc deletedObj = newObject.PatientActionUserAssocs.Where(x => x.UserDetailsId == userid && x.StatusId == (short)Status.Deleted).FirstOrDefault();
                if (deletedObj == null)
                {
                    newUserList.Add(userid);

                }                

            }
            
            List<long> newUserList2 = newObject.PatientActionUserAssocs.Where(y => y.StatusId == (short)Status.Active).Select(x => x.UserDetailsId).ToList();
            if(newUserList2 == null || newUserList2.Count == 0)
            {
                finalNewUserList = newUserList;
            }else if(newUserList == null || newUserList.Count == 0)
            {
                finalNewUserList = newUserList2;
            }
            else
            {
               finalNewUserList = newUserList.Concat(newUserList2).ToList();
            }
            

            bool areAssignedUsersEqual = new HashSet<long>(finalNewUserList).SetEquals(oldUserList);

            if (areAssignedUsersEqual == false)
            {
                VariableJSON variableJson1 = new();
                variableJson1.Operation = Enum.GetName(ActivityLogOps.Update);
                variableJson1.Column = "Assigned User";
                variableJson1.OldValue = null;
                List<UserDetailInfo> newlstProviders = await _patientActionsDAL.FetchUserDetailsFromIds(finalNewUserList, orgId);
                short oldCount = 0;
                short newCount = 0;


                foreach (UserDetailInfo provider in newlstProviders)
                {
                    newCount += 1;
                    string title = (provider.TitleId is null) ? string.Empty : EnumExtensions.GetDescription((TitleType)provider.TitleId);
                    variableJson1.NewValue += title + " " + provider.FirstName + " " + provider.SurName;
                    if (newCount < newlstProviders.Count)
                    {
                        variableJson1.NewValue += ",";
                    }

                }


                List<UserDetailInfo> oldlstProviders = await _patientActionsDAL.FetchUserDetailsFromIds(oldUserList, orgId);
                foreach (UserDetailInfo provider in oldlstProviders)
                {
                    oldCount += 1;
                    string title = (provider.TitleId is null) ? string.Empty : EnumExtensions.GetDescription((TitleType)provider.TitleId);
                    variableJson1.OldValue += title + " " + provider.FirstName + " " + provider.SurName;
                    if (oldCount < oldlstProviders.Count)
                    {
                        variableJson1.OldValue += ",";
                    }

                }
                lstVariableJSON.Add(variableJson1);
            }


            return lstVariableJSON;
        }

        private bool checkAuditCoumns(string colName)
        {
            if (colName.ToLower().Equals("statusid") ||
                colName.ToLower().Equals("duedate") ||
                colName.ToLower().Equals("patientdetailsid") ||
                colName.ToLower().Equals("description") ||
                colName.ToLower().Equals("isurgent") ||
                colName.ToLower().Equals("isclosed"))
              


            {

            }

                return true;

            return false;
        }

        private bool checkAuditCoumnsAdd(string colName)
        {
            // Userdetails is the dummy col name used to fetch the assoc table and show assigned users 
            if (
                colName.ToLower().Equals("userdetailsid") || 
                colName.ToLower().Equals("duedate") ||
                colName.ToLower().Equals("description") ||
                colName.ToLower().Equals("isurgent"))
                
                return true;

            return false;
        }


        private async Task<List<VariableJSON>> GenerateVariableJSONForAdd(PatientAction newObject, int orgId)
        {
            var props = newObject.GetType().GetProperties();
            List<VariableJSON> lstVariableJSON = new();

            foreach (PropertyInfo i in props)
            {
                if (checkAuditCoumnsAdd(i.Name))
                {
                    var newVal = i.GetValue(newObject, null);

                    VariableJSON variableJson = new();
                    variableJson.Operation = Enum.GetName(ActivityLogOps.Create);
                    switch (i.Name.ToLower())
                    {

                        case "duedate":
                            {
                                variableJson.Column = "Due Date";
                                variableJson.OldValue = null;
                                variableJson.NewValue = (newVal is null) ? null : ((DateTime)newVal).ToString("dd-MM-yyyy");
                                break;
                            }
                        case "isurgent":
                            {
                                variableJson.Column = "IsUrgent";
                                variableJson.OldValue = null;
                                variableJson.NewValue = (newVal is null) ? false : newVal;
                                break;
                            }


                        case "userdetailsid":
                 {
                                variableJson.Column = "Assigned To";
                                variableJson.OldValue = null;
                                List<UserDetailInfo> lstProviders = await _patientActionsDAL.FetchUserDetailsFromIds(newObject.PatientActionUserAssocs.Select(x => x.UserDetailsId).ToList(), orgId);
                                for (int m = 0; m < lstProviders.Count; m++)
                                {
                                    string title = (lstProviders[m].TitleId is null) ? string.Empty : EnumExtensions.GetDescription((TitleType)lstProviders[m].TitleId);
                                    variableJson.NewValue += title + " " + lstProviders[m].FirstName + " " + lstProviders[m].SurName;
                                    if( m != lstProviders.Count - 1)
                                    {
                                        variableJson.NewValue += ",";
                                    }

                                }
                                    break;
                            }

                        default:
                        {
                            variableJson.NewValue = newVal;
                            variableJson.OldValue = null;
                            variableJson.Column = i.Name;
                            break;
                        }

                    }

                    lstVariableJSON.Add(variableJson);
                }

            } 


            return lstVariableJSON;
        }

        private ActivityLogChildEntryInfo CreateActivityLogChildEntry(int activityLogOp, List<VariableJSON> lstVariableJSON, long id, long? patientDetailsId, short? patientActionStatus, string notes, long? childId, string templateName)
        {
            ActivityLogChildEntryInfo activityLogChild = new();
            activityLogChild.ParentActivityLogTypeId = (short)ActivityLogType.Patient_Actions;
            activityLogChild.ParentEntityId = id;
            activityLogChild.ParentDescription = notes;
            activityLogChild.VariableJson = (lstVariableJSON is null || lstVariableJSON.Count == 0) ? null : JsonConvert.SerializeObject(lstVariableJSON);
            activityLogChild.ActivityStatusId = patientActionStatus;
            if ((int)ActivityLogOps.Create == activityLogOp)
            {
                activityLogChild.Description = "Patient Action Created";
            }
            else if ((int)ActivityLogOps.Update == activityLogOp)
            {
                activityLogChild.Description = "Patient Action Updated";
            }
            else if ((int)ActivityLogOps.Comment == activityLogOp)
            {
                activityLogChild.Description = "Commented";
                activityLogChild.EntityId = childId;
            }

            return activityLogChild;
        }

        public PatientAction DeepCopyPA(PatientAction inputPA)
        {
            return new PatientAction
            {
                StatusId = inputPA.StatusId,
                ActionId = inputPA.ActionId,
                ActionTypeId = inputPA.ActionTypeId,
                IsClosed = inputPA.IsClosed,
                //IsPending = inputPA.IsPending,
                IsUrgent = inputPA.IsUrgent,
                Id = inputPA.Id,
                UserDetailsId = inputPA.UserDetailsId,
                PatientDetailsId = inputPA.PatientDetailsId,
                PathologyRequestId = inputPA.PathologyRequestId,
                PathologyResponseMediaId = inputPA.PathologyResponseMediaId,
                PatientActionComments = inputPA.PatientActionComments,
                Description = inputPA.Description,
                DueDate = inputPA.DueDate,
                CreatedDate = inputPA.CreatedDate,
                CreatedBy = inputPA.CreatedBy,
                LetterId = inputPA.LetterId,
                OrgId = inputPA.OrgId,
                ModifiedBy  = inputPA.ModifiedBy,
                ModifiedDate = inputPA.ModifiedDate,   
                RequestedDate = inputPA.RequestedDate
                

               
            };
        }

        private List<PatientActionUserAssoc> EditPatientActionUserAssocs(ICollection<PatientActionUserAssoc> removeUserList, ICollection<PatientActionUserAssoc> inputUserList, long userId)
        {
            List<PatientActionUserAssoc> addUserList = new();

            foreach (var userAssoc in inputUserList)
            {
                if (userAssoc.Id > 0)
                {
                    var existingobj = removeUserList.FirstOrDefault(x => x.Id == userAssoc.Id);
                    removeUserList.Remove(existingobj);
                }
                else
                {
                    PatientActionUserAssoc userAssocDB = removeUserList.Where(x => x.UserDetailsId == userAssoc.UserDetailsId).FirstOrDefault();
                    if (userAssocDB is null)
                    {
                        userAssoc.CreatedDate = DateTime.UtcNow;
                        userAssoc.CreatedBy = userId;
                        userAssoc.StatusId = (short)Status.Active;
                        addUserList.Add(userAssoc);
                    }
                    else
                    {
                        if (userAssocDB.StatusId == (short)Status.Deleted)
                        {
                            userAssocDB.StatusId = (short)Status.Active;
                            userAssocDB.ModifiedDate = DateTime.UtcNow;
                            userAssocDB.ModifiedBy = userId;
                            addUserList.Add(userAssocDB);

                        }
                        removeUserList.Remove(userAssocDB);

                    }
                }
            }

            foreach (var userAssoc in removeUserList.Where(x => x.StatusId == (short)Status.Active))
            {
                userAssoc.StatusId = (short)Status.Deleted;
                userAssoc.ModifiedDate = DateTime.UtcNow;
                userAssoc.ModifiedBy = userId;
            }
            addUserList = addUserList.Concat(removeUserList).ToList();
            return addUserList;
        }


    }
}
