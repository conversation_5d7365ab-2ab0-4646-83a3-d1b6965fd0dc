﻿using Capstone2.Framework.Business.Common;
using Capstone2.Framework.RestApi;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.User.Interfaces;
using Capstone2.RestServices.User.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.ApplicationModels;
using Microsoft.Azure.KeyVault.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.Serialization.Formatters.Binary;
using System.Security.Cryptography;
using System.Threading.Tasks;
using System.Transactions;
using static System.Collections.Specialized.BitVector32;

namespace Capstone2.RestServices.User.Services
{
    public class IncomingLetterActionsBAL : IIncomingLetterActionsBAL
    {
        public readonly AppSettings _appSettings;
        public readonly IIncomingLetterActionsDAL _incomingLetterActionsDAL;
        private readonly ILogger<IncomingLetterActionsBAL> _logger;

        public IncomingLetterActionsBAL(IOptions<AppSettings> appSettings, IIncomingLetterActionsDAL incomingLetterActionsDAL, ILogger<IncomingLetterActionsBAL> logger)
        {
            _appSettings = appSettings.Value;
            _incomingLetterActionsDAL = incomingLetterActionsDAL;
            _logger = logger;
        }

        public async Task<ApiResponse<long?>> AddIncomingLetterAction(IncomingLetterActions incomingLetterAction, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<long?> apiResponse = new();
            var userId = baseHttpRequestContext.UserId;
            var orgId = baseHttpRequestContext.OrgId;
            List<VariableJSON> lstVariableJson = new();
            List<IncomingLetterPatientAction> incomingLetterDetails = new List<IncomingLetterPatientAction>();
            IncomingLetterActions oldILA = new IncomingLetterActions();
            IncomingLetterActions newILA = new IncomingLetterActions();
            oldILA = await _incomingLetterActionsDAL.GetIncomingLetterActionById(incomingLetterAction.IncomingLetterId, orgId);
            var oldILACopy = Clone(oldILA);  // Clone oldILA before performing the insert
            incomingLetterDetails = await _incomingLetterActionsDAL.GetIncomingLetterPatientActionById(incomingLetterAction.IncomingLetterId, orgId);
            var id = 0;
            var result = await _incomingLetterActionsDAL.AddIncomingLetterAction(incomingLetterAction);
            newILA = await _incomingLetterActionsDAL.GetIncomingLetterActionById(incomingLetterAction.IncomingLetterId, orgId);
            var dbActions = await _incomingLetterActionsDAL.GetIncomingLetterPatientActionByIdList(incomingLetterAction.IncomingLetterId, orgId);
            if (dbActions[0].ActionId != (short)IncomingLetterActionsEnum.Recieved)
            {
                IncomingLetterPatientAction dbAction = dbActions.Where(x => x.ActionId == dbActions[0].ActionId).FirstOrDefault();
                var descString = dbActions[0].Notes;

                oldILACopy = oldILACopy?.PatientDetailsId != incomingLetterAction.PatientDetailsId ? null : oldILACopy;
                List<VariableJSON> lstVariableJSONForTirageLetter = await GenerateVariableJSONForTirageLetter(oldILACopy, newILA, orgId);
                List<VariableJSON> lstVariableJSONForBookUrgentAppointment = await GenerateVariableJSONForBookUrgentAppointment(oldILACopy, newILA, orgId);
                List<VariableJSON> lstVariableJSONForBookNotUrgentAppointment = await GenerateVariableJSONForBookNotUrgentAppointment(oldILACopy, newILA, orgId);
                List<VariableJSON> lstVariableJSONForCallPatient = await GenerateVariableJSONForCallPatient(oldILACopy, newILA, orgId);
                List<VariableJSON> lstVariableJSONForNoAction = await GenerateVariableJSONForNoAction(oldILACopy, newILA, orgId);

                if (lstVariableJSONForTirageLetter.Count > 0)
                {
                    ActivityLogChildEntryInfo activityLogChild = CreateActivityLogChildEntry((int)ActivityLogOps.Update, lstVariableJSONForTirageLetter, (long)dbActions[0].IncomingLetterId, dbActions[0].PatientDetailsId, (bool)dbActions[0].IsClosed ? (short)31 : (short)30, descString, null, null, "Triage Letter");
                    string patientAPiUrl = _appSettings.ApiUrls["PatientServiceUrl"] + "/patient/" + dbActions[0].PatientDetailsId + "/activity_logs/activitylogchild_entries";
                    RestClient restClient = new RestClient(patientAPiUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
                    var activitylogApiResponse = await restClient.PostAsync<ApiResponse<long?>>(patientAPiUrl, activityLogChild);

                    if (activitylogApiResponse is null && activitylogApiResponse.StatusCode != StatusCodes.Status200OK)
                    {
                        apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                        apiResponse.Message = "Add Activity Log failed";
                        apiResponse.Result = 0;
                        return apiResponse;
                    }
                }
                if (lstVariableJSONForBookUrgentAppointment.Count > 0)
                {
                    ActivityLogChildEntryInfo activityLogChild = CreateActivityLogChildEntry((int)ActivityLogOps.Update, lstVariableJSONForBookUrgentAppointment, (long)dbActions[0].IncomingLetterId, dbActions[0].PatientDetailsId, (bool)dbActions[0].IsClosed ? (short)31 : (short)30, descString, null, null, "Book Urgent Appointment");
                    string patientAPiUrl = _appSettings.ApiUrls["PatientServiceUrl"] + "/patient/" + dbActions[0].PatientDetailsId + "/activity_logs/activitylogchild_entries";
                    RestClient restClient = new RestClient(patientAPiUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
                    var activitylogApiResponse = await restClient.PostAsync<ApiResponse<long?>>(patientAPiUrl, activityLogChild);

                    if (activitylogApiResponse is null && activitylogApiResponse.StatusCode != StatusCodes.Status200OK)
                    {
                        apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                        apiResponse.Message = "Add Activity Log failed";
                        apiResponse.Result = 0;
                        return apiResponse;
                    }
                }
                if (lstVariableJSONForBookNotUrgentAppointment.Count > 0)
                {
                    ActivityLogChildEntryInfo activityLogChild = CreateActivityLogChildEntry((int)ActivityLogOps.Update, lstVariableJSONForBookNotUrgentAppointment, (long)dbActions[0].IncomingLetterId, dbActions[0].PatientDetailsId, (bool)dbActions[0].IsClosed ? (short)31 : (short)30, descString, null, null, "Book Not Urgent Appointment");
                    string patientAPiUrl = _appSettings.ApiUrls["PatientServiceUrl"] + "/patient/" + dbActions[0].PatientDetailsId + "/activity_logs/activitylogchild_entries";
                    RestClient restClient = new RestClient(patientAPiUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
                    var activitylogApiResponse = await restClient.PostAsync<ApiResponse<long?>>(patientAPiUrl, activityLogChild);

                    if (activitylogApiResponse is null && activitylogApiResponse.StatusCode != StatusCodes.Status200OK)
                    {
                        apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                        apiResponse.Message = "Add Activity Log failed";
                        apiResponse.Result = 0;
                        return apiResponse;
                    }
                }
                if (lstVariableJSONForCallPatient.Count > 0)
                {
                    ActivityLogChildEntryInfo activityLogChild = CreateActivityLogChildEntry((int)ActivityLogOps.Update, lstVariableJSONForCallPatient, (long)dbActions[0].IncomingLetterId, dbActions[0].PatientDetailsId, (bool)dbActions[0].IsClosed ? (short)31 : (short)30, descString, null, null, "Nurse to Advise Patient");
                    string patientAPiUrl = _appSettings.ApiUrls["PatientServiceUrl"] + "/patient/" + dbActions[0].PatientDetailsId + "/activity_logs/activitylogchild_entries";
                    RestClient restClient = new RestClient(patientAPiUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
                    var activitylogApiResponse = await restClient.PostAsync<ApiResponse<long?>>(patientAPiUrl, activityLogChild);

                    if (activitylogApiResponse is null && activitylogApiResponse.StatusCode != StatusCodes.Status200OK)
                    {
                        apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                        apiResponse.Message = "Add Activity Log failed";
                        apiResponse.Result = 0;
                        return apiResponse;
                    }
                }
                if (lstVariableJSONForNoAction.Count > 0)
                {
                    ActivityLogChildEntryInfo activityLogChild = CreateActivityLogChildEntry((int)ActivityLogOps.Update, lstVariableJSONForNoAction, (long)dbActions[0].IncomingLetterId, dbActions[0].PatientDetailsId, (bool)dbActions[0].IsClosed ? (short)31 : (short)30, descString, null, null, "No Action");
                    string patientAPiUrl = _appSettings.ApiUrls["PatientServiceUrl"] + "/patient/" + dbActions[0].PatientDetailsId + "/activity_logs/activitylogchild_entries";
                    RestClient restClient = new RestClient(patientAPiUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);
                    var activitylogApiResponse = await restClient.PostAsync<ApiResponse<long?>>(patientAPiUrl, activityLogChild);

                    if (activitylogApiResponse is null && activitylogApiResponse.StatusCode != StatusCodes.Status200OK)
                    {
                        apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                        apiResponse.Message = "Add Activity Log failed";
                        apiResponse.Result = 0;
                        return apiResponse;
                    }
                }
            }
            

            if (result != null)
            {
                apiResponse.Result = incomingLetterAction.Id;
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failure";
            }



            return apiResponse;

        }

        public async Task<ApiResponse<IncomingLetterActions>> GetIncomingLetterActionById(long id, int orgId)
        {
            ApiResponse<IncomingLetterActions> apiResponse = new();
            var result = await _incomingLetterActionsDAL.GetIncomingLetterActionById(id, orgId);
            if (result != null)
            {
                apiResponse.Result = result;
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status404NotFound;
                apiResponse.Message = "Incoming letter action not found";
            }
            return apiResponse;
        }

        public async Task<ApiResponse<List<IncomingLetterActions>>> GetIncomingLetterActionsByOrgId(int orgId)
        {
            ApiResponse<List<IncomingLetterActions>> apiResponse = new();
            var result = await _incomingLetterActionsDAL.GetIncomingLetterActionsByOrgId(orgId);
            apiResponse.Result = result;
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            return apiResponse;
        }

        public async Task<ApiResponse<long?>> EditIncomingLetterAction(IncomingLetterActions incomingLetterAction, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<long?> apiResponse = new();
            incomingLetterAction.ModifiedBy = baseHttpRequestContext.UserId;
            incomingLetterAction.ModifiedDate = DateTime.UtcNow;

            var result = await _incomingLetterActionsDAL.EditIncomingLetterAction(incomingLetterAction);
            if (result > 0)
            {
                apiResponse.Result = incomingLetterAction.Id;
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failure";
            }
            return apiResponse;
        }

        public async Task<ApiResponse<long?>> DeleteIncomingLetterAction(long id)
        {
            ApiResponse<long?> apiResponse = new();
            var result = await _incomingLetterActionsDAL.DeleteIncomingLetterAction(id);
            if (result > 0)
            {
                apiResponse.Result = id;
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failure";
            }
            return apiResponse;
        }

        private IncomingLetterActionsFilterModel PrepareFilterParameters(string filter)
        {
            if (!(String.IsNullOrEmpty(filter)))
            {
                return JsonConvert.DeserializeObject<IncomingLetterActionsFilterModel>(filter);
            }
            else
            {
                return null;
            }
        }

        private ActivityLogInfo CreateActivityLogEntry(int activityLogOp, List<VariableJSON> lstVariableJSON, long id, long? patientDetailsId, short? paStatus, string notes)
        {
            ActivityLogInfo activityLog = new();
            ActivityLogChildEntryInfo activityLogChild = new();
            activityLog.ActivityLogTypeId = (short)ActivityLogType.Patient_Actions;
            activityLog.PatientDetailsId = (long)patientDetailsId;
            activityLog.EntityId = id;
            activityLog.Description = notes;
            activityLog.ActivityStatusId = paStatus;
            activityLogChild.VariableJson = (lstVariableJSON is null || lstVariableJSON.Count == 0) ? null : JsonConvert.SerializeObject(lstVariableJSON);
            activityLogChild.ActivityStatusId = paStatus;
            activityLog.ActivityLogChildEntries.Add(activityLogChild);
            if ((int)ActivityLogOps.Create == activityLogOp)
            {
                activityLogChild.Description = "Incoming Letter Action Created";
            }
            return activityLog;
        }

        private bool checkAuditCoumns(string colName)
        {
            if (colName.ToLower().Equals("statusid") ||
                colName.ToLower().Equals("assigneeid") ||
                colName.ToLower().Equals("duedate") ||
                colName.ToLower().Equals("patientdetailsid") ||
                colName.ToLower().Equals("description") ||
                colName.ToLower().Equals("isurgent") ||
                colName.ToLower().Equals("isclosed"))

                return true;

            return false;
        }

        private ActivityLogChildEntryInfo CreateActivityLogChildEntry(int activityLogOp, List<VariableJSON> lstVariableJSON, long id, long? patientDetailsId, short? patientActionStatus, string notes, long? childId, string templateName, string activityLogDesc)
        {
            ActivityLogChildEntryInfo activityLogChild = new();
            //ToDo:Change From Enum
            activityLogChild.ParentActivityLogTypeId = (short)300;
            activityLogChild.ParentEntityId = id;
            activityLogChild.ParentDescription = notes;
            activityLogChild.VariableJson = (lstVariableJSON is null || lstVariableJSON.Count == 0) ? null : JsonConvert.SerializeObject(lstVariableJSON);
            activityLogChild.ActivityStatusId = patientActionStatus;
            if ((int)ActivityLogOps.Create == activityLogOp)
            {
                activityLogChild.Description = "Incoming Letter Uploaded";
            }
            else if ((int)ActivityLogOps.Update == activityLogOp)
            {
                activityLogChild.Description = activityLogDesc;
            }
            else if ((int)ActivityLogOps.Comment == activityLogOp)
            {
                activityLogChild.Description = "Commented";
                activityLogChild.EntityId = childId;
            }

            return activityLogChild;
        }

        public IncomingLetterActions DeepCopyILA(IncomingLetterActions inputILA)
        {
            return new IncomingLetterActions
            {
                Id = inputILA.Id,
                IncomingLetterId = inputILA.IncomingLetterId,
                OrgId = inputILA.OrgId,
                PatientDetailsId = inputILA.PatientDetailsId,
                ActionTypeId = inputILA.ActionTypeId,
                ActionId = inputILA.ActionId,
                IsNoAction = inputILA.IsNoAction,
                IsNoActionCompleted = inputILA.IsNoActionCompleted,
                NoActionUserIds = inputILA.NoActionUserIds,
                IsTriageLetter = inputILA.IsTriageLetter,
                IsTriageLetterCompleted = inputILA.IsTriageLetterCompleted,
                TriageLetterUserIds = inputILA.TriageLetterUserIds,
                IsBookUrgentAppointment = inputILA.IsBookUrgentAppointment,
                IsBookUrgentAppointmentCompleted = inputILA.IsBookUrgentAppointmentCompleted,
                BookUrgentAppointmentUserIds = inputILA.BookUrgentAppointmentUserIds,
                IsBookNotUrgentAppointment = inputILA.IsBookNotUrgentAppointment,
                IsBookNotUrgentAppointmentCompleted = inputILA.IsBookNotUrgentAppointmentCompleted,
                BookNotUrgentAppointmentUserIds = inputILA.BookNotUrgentAppointmentUserIds,
                IsCallPatient = inputILA.IsCallPatient,
                IsCallPatientCompleted = inputILA.IsCallPatientCompleted,
                CallPatientUserIds = inputILA.CallPatientUserIds,
                IsReceived = inputILA.IsReceived,
                StatusId = inputILA.StatusId,
                DueDate = inputILA.DueDate,
                CreatedDate = inputILA.CreatedDate,
                ModifiedDate = inputILA.ModifiedDate,
                CreatedBy = inputILA.CreatedBy,
                ModifiedBy = inputILA.ModifiedBy
            };
        }

        private async Task<List<VariableJSON>> GenerateVariableJSONForAdd(IncomingLetterPatientAction newObject, int orgId)
        {
            var props = newObject.GetType().GetProperties();
            List<VariableJSON> lstVariableJSON = new();

            foreach (PropertyInfo i in props)
            {
                if (checkAuditCoumnsAdd(i.Name))
                {
                    var newVal = i.GetValue(newObject, null);

                    VariableJSON variableJson = new();
                    variableJson.Operation = Enum.GetName(ActivityLogOps.Create);
                    switch (i.Name.ToLower())
                    {

                        case "duedate":
                            {
                                variableJson.Column = "Due Date";
                                variableJson.OldValue = null;
                                variableJson.NewValue = (newVal is null) ? null : ((DateTime)newVal).ToString("dd-MM-yyyy");
                                break;
                            }
                        case "isurgent":
                            {
                                variableJson.Column = "IsUrgent";
                                variableJson.OldValue = null;
                                variableJson.NewValue = (newVal is null) ? false : newVal;
                                break;
                            }


                        case "userdetailsid":
                            {
                                variableJson.Column = "Assigned To";
                                variableJson.OldValue = null;
                               // List<UserDetailInfo> lstProviders = await _incomingLetterActionsDAL.FetchUserDetailsFromIds(newObject.IncomingLetterPatientActionsUserAssocs.Select(x => x.UserDetailsId).ToList(), orgId);
                                //for (int m = 0; m < lstProviders.Count; m++)
                                //{
                                //    string title = (lstProviders[m].TitleId is null) ? string.Empty : EnumExtensions.GetDescription((TitleType)lstProviders[m].TitleId);
                                //    variableJson.NewValue += title + " " + lstProviders[m].FirstName + " " + lstProviders[m].SurName;
                                //    if (m != lstProviders.Count - 1)
                                //    {
                                //        variableJson.NewValue += ",";
                                //    }

                                //}
                                break;
                            }

                        default:
                            {
                                variableJson.NewValue = newVal;
                                variableJson.OldValue = null;
                                variableJson.Column = i.Name;
                                break;
                            }

                    }

                    lstVariableJSON.Add(variableJson);
                }

            }


            return lstVariableJSON;
        }

        private bool checkAuditCoumnsAdd(string colName)
        {
            // Userdetails is the dummy col name used to fetch the assoc table and show assigned users 
            if (
                colName.ToLower().Equals("userdetailsid") ||
                colName.ToLower().Equals("duedate") ||
                colName.ToLower().Equals("description") ||
                colName.ToLower().Equals("isurgent"))

                return true;

            return false;
        }

        private async Task<List<VariableJSON>> GenerateVariableJSONForTirageLetter(IncomingLetterActions oldILA, IncomingLetterActions newILA, int orgId)
        {
            List<VariableJSON> lstVariableJSON = new();

            // Define the properties to check
            var propertiesToCheck = new List<string>
            {
                "IsTriageLetter",
                "IsTriageLetterCompleted",
                "TriageLetterUserIds",
            };

            var props = typeof(IncomingLetterActions).GetProperties();

            foreach (PropertyInfo i in props)
            {
                if (propertiesToCheck.Contains(i.Name))
                {
                    var oldVal = oldILA != null ? i.GetValue(oldILA, null) : null;
                    var newVal = i.GetValue(newILA, null);
                    bool isUpdated = oldILA == null || (oldVal is null && newVal is not null) || (oldVal is not null && newVal is null) || (oldVal is not null && newVal is not null && !oldVal.Equals(newVal));

                    // Add the corresponding TriageLetterUserIds as a separate entry
                    var oldUserIds = oldILA != null ? typeof(IncomingLetterActions).GetProperty("TriageLetterUserIds").GetValue(oldILA, null)?.ToString() : null;
                    var newUserIds = typeof(IncomingLetterActions).GetProperty("TriageLetterUserIds").GetValue(newILA, null)?.ToString();

                    if (isUpdated && !string.IsNullOrEmpty(newUserIds))
                    {
                        // Handle special cases for specific fields
                        if (i.Name == "IsTriageLetterCompleted" || i.Name == "TriageLetterUserIds")
                        {
                            if (i.Name == "IsTriageLetterCompleted" && ((oldVal != null || newILA.IsTriageLetterCompleted == true)))
                            {
                                VariableJSON variableJson = new()
                                {
                                    Operation = "",
                                    Column = newVal.ToString().Trim().ToLower() == "true".Trim().ToLower() ? "Completed Action : Triage Letter" : "Revoked Completed Action : Triage Letter",
                                    OldValue = oldVal?.ToString(),
                                    NewValue = newVal?.ToString()
                                };
                                lstVariableJSON.Add(variableJson);
                            }

                            if (i.Name == "TriageLetterUserIds")
                            {

                                if (oldUserIds != newUserIds)
                                {
                                    VariableJSON userIdsJson = new()
                                    {
                                        Operation = oldUserIds == null ? "Add" : "Update",
                                        Column = "Assigned To",
                                        OldValue = oldUserIds,
                                        NewValue = await _incomingLetterActionsDAL.GetUserDetailsFromCommaSeparatedIdsAsync(newUserIds.ToString())
                                    };
                                    lstVariableJSON.Add(userIdsJson);
                                }
                            }

                        }
                    }
                }
            }

            return lstVariableJSON;
        }

        private async Task<List<VariableJSON>> GenerateVariableJSONForBookUrgentAppointment(IncomingLetterActions oldILA, IncomingLetterActions newILA, int orgId)
        {
            List<VariableJSON> lstVariableJSON = new();

            // Define the properties to check
            var propertiesToCheck = new List<string>
            {
                "IsBookUrgentAppointment",
                "IsBookUrgentAppointmentCompleted",
                "BookUrgentAppointmentUserIds"
            };

            var props = typeof(IncomingLetterActions).GetProperties();

            foreach (PropertyInfo i in props)
            {
                if (propertiesToCheck.Contains(i.Name))
                {
                    var oldVal = oldILA != null ? i.GetValue(oldILA, null) : null;
                    var newVal = i.GetValue(newILA, null);
                    bool isUpdated = oldILA == null || (oldVal is null && newVal is not null) || (oldVal is not null && newVal is null) || (oldVal is not null && newVal is not null && !oldVal.Equals(newVal));

                    // Add the corresponding BookUrgentAppointmentUserIds as a separate entry
                    var oldUserIds = oldILA != null ? typeof(IncomingLetterActions).GetProperty("BookUrgentAppointmentUserIds").GetValue(oldILA, null)?.ToString() : null;
                    var newUserIds = typeof(IncomingLetterActions).GetProperty("BookUrgentAppointmentUserIds").GetValue(newILA, null)?.ToString();

                    if (isUpdated && !string.IsNullOrEmpty(newUserIds))
                    {
                        if (i.Name == "IsBookUrgentAppointmentCompleted" || i.Name == "BookUrgentAppointmentUserIds")
                        {
                            if (i.Name == "IsBookUrgentAppointmentCompleted" && ((oldVal != null || newILA.IsBookUrgentAppointmentCompleted == true)))
                            {
                                VariableJSON variableJson = new()
                                {
                                    //Operation = "Update",
                                    Operation = "",
                                    //Column = "Completed Action",
                                    Column = newVal.ToString().Trim().ToLower() == "true".Trim().ToLower() ? "Completed Action : Book Urgent Appointment" : "Revoked Completed Action : Book Urgent Appointment",
                                    OldValue = oldVal?.ToString(),
                                    NewValue = newVal?.ToString()
                                };
                                lstVariableJSON.Add(variableJson);
                            }

                            if (i.Name == "BookUrgentAppointmentUserIds")
                            {
                                if (oldUserIds != newUserIds)
                                {
                                    VariableJSON userIdsJson = new()
                                    {
                                        Operation = oldUserIds == null ? "Add" : "Update",
                                        Column = "Assigned To",
                                        OldValue = oldUserIds,
                                        NewValue = await _incomingLetterActionsDAL.GetUserDetailsFromCommaSeparatedIdsAsync(newUserIds.ToString())
                                    };
                                    lstVariableJSON.Add(userIdsJson);
                                }
                            }
                        }
                    }
                }
            }

            return lstVariableJSON;
        }

        private async Task<List<VariableJSON>> GenerateVariableJSONForBookNotUrgentAppointment(IncomingLetterActions oldILA, IncomingLetterActions newILA, int orgId)
        {
            List<VariableJSON> lstVariableJSON = new();

            // Define the properties to check
            var propertiesToCheck = new List<string>
            {
                "IsBookNotUrgentAppointment",
                "IsBookNotUrgentAppointmentCompleted",
                "BookNotUrgentAppointmentUserIds"
            };

            var props = typeof(IncomingLetterActions).GetProperties();

            foreach (PropertyInfo i in props)
            {
                if (propertiesToCheck.Contains(i.Name))
                {
                    var oldVal = oldILA != null ? i.GetValue(oldILA, null) : null;
                    var newVal = i.GetValue(newILA, null);
                    bool isUpdated = oldILA == null || (oldVal is null && newVal is not null) || (oldVal is not null && newVal is null) || (oldVal is not null && newVal is not null && !oldVal.Equals(newVal));

                    // Add the corresponding BookNotUrgentAppointmentUserIds as a separate entry
                    var oldUserIds = oldILA != null ? typeof(IncomingLetterActions).GetProperty("BookNotUrgentAppointmentUserIds").GetValue(oldILA, null)?.ToString() : null;
                    var newUserIds = typeof(IncomingLetterActions).GetProperty("BookNotUrgentAppointmentUserIds").GetValue(newILA, null)?.ToString();

                    if (isUpdated && !string.IsNullOrEmpty(newUserIds))
                    {
                        // Handle special cases for specific fields
                        if (i.Name == "IsBookNotUrgentAppointmentCompleted" || i.Name == "BookNotUrgentAppointmentUserIds")
                        {
                            if (i.Name == "IsBookNotUrgentAppointmentCompleted" && ((oldVal != null || newILA.IsBookNotUrgentAppointmentCompleted == true)))
                            {
                                VariableJSON variableJson = new()
                                {
                                    //Operation = "Update",
                                    Operation = "",
                                    //Column = "Completed Action",
                                    Column = newVal.ToString().Trim().ToLower() == "true".Trim().ToLower() ? "Completed Action : Book Not Urgent Appointment" : "Revoked Completed Action : Book Not Urgent Appointment",
                                    OldValue = oldVal?.ToString(),
                                    NewValue = newVal?.ToString()
                                };
                                lstVariableJSON.Add(variableJson);
                            }

                            if (i.Name == "BookNotUrgentAppointmentUserIds")
                            {
                                if (oldUserIds != newUserIds)
                                {
                                    VariableJSON userIdsJson = new()
                                    {
                                        Operation = oldUserIds == null ? "Add" : "Update",
                                        Column = "Assigned To",
                                        OldValue = oldUserIds,
                                        NewValue = await _incomingLetterActionsDAL.GetUserDetailsFromCommaSeparatedIdsAsync(newUserIds.ToString())
                                    };
                                    lstVariableJSON.Add(userIdsJson);
                                }
                            }

                        }
                    }
                }
            }

            return lstVariableJSON;
        }

        private async Task<List<VariableJSON>> GenerateVariableJSONForCallPatient(IncomingLetterActions oldILA, IncomingLetterActions newILA, int orgId)
        {
            List<VariableJSON> lstVariableJSON = new();

            // Define the properties to check
            var propertiesToCheck = new List<string>
            {
                "IsCallPatient",
                "IsCallPatientCompleted",
                "CallPatientUserIds",
            };

            var props = typeof(IncomingLetterActions).GetProperties();

            foreach (PropertyInfo i in props)
            {
                if (propertiesToCheck.Contains(i.Name))
                {
                    var oldVal = oldILA != null ? i.GetValue(oldILA, null) : null;
                    var newVal = i.GetValue(newILA, null);
                    bool isUpdated = oldILA == null || (oldVal is null && newVal is not null) || (oldVal is not null && newVal is null) || (oldVal is not null && newVal is not null && !oldVal.Equals(newVal));

                    var oldUserIds = oldILA != null ? typeof(IncomingLetterActions).GetProperty("CallPatientUserIds").GetValue(oldILA, null)?.ToString() : null;
                    var newUserIds = typeof(IncomingLetterActions).GetProperty("CallPatientUserIds").GetValue(newILA, null)?.ToString();

                    if (isUpdated && !string.IsNullOrEmpty(newUserIds))
                    {
                        // Handle special cases for specific fields
                        if (i.Name == "IsCallPatientCompleted" || i.Name == "CallPatientUserIds")
                        {
                            if (i.Name == "IsCallPatientCompleted" && ((oldVal != null || newILA.IsCallPatientCompleted == true)))
                            {
                                VariableJSON variableJson = new()
                                {
                                    //Operation = "Update",
                                    Operation = "",
                                    //Column = "Completed Action",
                                    Column = newVal.ToString().Trim().ToLower() == "true".Trim().ToLower() ? "Completed Action : Nurse to Advise Patient" : "Revoked Completed Action : Nurse to Advise Patient",
                                    OldValue = oldVal?.ToString(),
                                    NewValue = newVal?.ToString()
                                };
                                lstVariableJSON.Add(variableJson);
                            }

                            if (i.Name == "CallPatientUserIds")
                            {
                                // Add the corresponding CallPatientUserIds as a separate entry
                                if (oldUserIds != newUserIds)
                                {
                                    VariableJSON userIdsJson = new()
                                    {
                                        Operation = oldUserIds == null ? "Add" : "Update",
                                        Column = "Assigned To",
                                        OldValue = oldUserIds,
                                        NewValue = await _incomingLetterActionsDAL.GetUserDetailsFromCommaSeparatedIdsAsync(newUserIds.ToString())
                                    };
                                    lstVariableJSON.Add(userIdsJson);
                                }
                            }

                        }
                    }
                }
            }

            return lstVariableJSON;
        }

        private async Task<List<VariableJSON>> GenerateVariableJSONForNoAction(IncomingLetterActions oldILA, IncomingLetterActions newILA, int orgId)
        {
            List<VariableJSON> lstVariableJSON = new();

            // Define the properties to check
            var propertiesToCheck = new List<string>
            {
                "IsNoAction",
            };

            var props = typeof(IncomingLetterActions).GetProperties();

            foreach (PropertyInfo i in props)
            {
                if (propertiesToCheck.Contains(i.Name))
                {
                    var oldVal = oldILA != null ? i.GetValue(oldILA, null) : null;
                    var newVal = i.GetValue(newILA, null);
                    bool isUpdated = oldILA == null || (oldVal is null && newVal is not null) || (oldVal is not null && newVal is null) || (oldVal is not null && newVal is not null && !oldVal.Equals(newVal));

                    if (isUpdated)
                    {
                        // Handle special cases for specific fields
                        if(i.Name == "IsNoAction" && newVal?.ToString().Trim().ToLower() == "true".Trim().ToLower())
                        {
                            VariableJSON variableJson = new()
                            {
                                Operation = "Update",
                                Column = "Status",
                                OldValue = oldVal?.ToString(),
                                NewValue = newVal?.ToString()
                            };

                            lstVariableJSON.Add(variableJson);
                        }
                    }
                }
            }

            return lstVariableJSON;
        }
        private T Clone<T>(T source)
        {
            if (Object.ReferenceEquals(source, null))
            {
                return default(T);
            }

            var deserializeSettings = new JsonSerializerSettings { ObjectCreationHandling = ObjectCreationHandling.Replace };
            return JsonConvert.DeserializeObject<T>(JsonConvert.SerializeObject(source), deserializeSettings);
        }

    }

    public class NewVariableJSON
    {
        public string Operation { get; set; }
        public string Column { get; set; }
        public string OldValue { get; set; }
        public string NewValue { get; set; }
        public string CurrentUserIds { get; set; }  // New property for CurrentUserIds
    }

}
