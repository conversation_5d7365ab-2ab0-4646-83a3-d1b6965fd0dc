﻿using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.User.Context;
using Capstone2.RestServices.User.Interfaces;
using Capstone2.RestServices.User.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Security.Cryptography;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Capstone2.RestServices.User.Services
{
    public class IncomingLetterPatientActionsDAL : IIncomingLetterPatientActionsDAL
    {
        public readonly AppSettings _appSettings;
        public readonly ReadOnlyUserDBContext _readOnlyDbContext;
        public UpdatableUserDBContext _updatableDBContext;

        public IncomingLetterPatientActionsDAL(IOptions<AppSettings> appSettings, ReadOnlyUserDBContext readOnlyDbContext, UpdatableUserDBContext updatableDBContext)
        {
            _appSettings = appSettings.Value;
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
        }

        public async Task<IncomingLetterPatientAction> AddIncomingLetterPatientAction(IncomingLetterPatientAction incomingLetterPatientAction)
        {
            await _updatableDBContext.IncomingLetterPatientActions.AddAsync(incomingLetterPatientAction);
            await _updatableDBContext.SaveChangesAsync();
            return incomingLetterPatientAction;
        }
        public async Task<List<IncomingLetterPatientAction>> AddIncomingLetterMultiUserPatientAction(List<IncomingLetterPatientAction> incomingLetterPatientAction, long activityLogId)
        {
            try
            {
                if (activityLogId == 0)
                {
                    await _updatableDBContext.IncomingLetterPatientActions.AddRangeAsync(incomingLetterPatientAction);
                }
                else
                {
                    foreach (var action in incomingLetterPatientAction)
                    {
                        // Attach the entity to the context if not already tracked
                        _updatableDBContext.IncomingLetterPatientActions.Attach(action);

                        // Only update the LetterInActivityLogId property
                        action.LetterInActivityLogId = activityLogId;

                        // Mark the specific property as modified
                        _updatableDBContext.Entry(action).Property(a => a.LetterInActivityLogId).IsModified = true;
                    }
                }

                await _updatableDBContext.SaveChangesAsync();

                return incomingLetterPatientAction;
            }
            catch(Exception ex)
            {
                throw ex;
            }
            
        }

        public async Task<IncomingLetterPatientActionView> GetIncomingLetterPatientActionById(long id, short selectedActionId)
        {
            //return await _readOnlyDbContext.IncomingLetterPatientActions
            //    .FirstOrDefaultAsync(x => x.IncomingLetterId == id);



            var query = from ILPA in _readOnlyDbContext.IncomingLetterPatientActions
                        where ILPA.IncomingLetterId == id
                        orderby ILPA.CreatedDate
                        select new IncomingLetterPatientActionView
                        {
                            Id = ILPA.Id,
                            OrgId = ILPA.OrgId,
                            IncomingLetterId = ILPA.IncomingLetterId,
                            ActionTypeId = ILPA.ActionTypeId,
                            ActionId = ILPA.ActionId,
                            AssigneeId = ILPA.AssigneeId,
                            DueDate = ILPA.DueDate,
                            StatusId = ILPA.StatusId,
                            IsUrgent = ILPA.IsUrgent,
                            IsClosed = ILPA.IsClosed,
                            IsMediaUploaded = ILPA.IsMediaUploaded,
                            ActivityLogId = ILPA.ActivityLogId,
                            PatientDetailsId = ILPA.PatientDetailsId,
                            CreatedBy = ILPA.CreatedBy,
                            CreatedDate = ILPA.CreatedDate,
                            ModifiedDate = ILPA.ModifiedDate,
                            ModifiedBy = ILPA.ModifiedBy,
                            Tags = ILPA.Tags,
                            Notes = ILPA.Notes,
                            LetterInActivityLogId = ILPA.LetterInActivityLogId,
                            IsAccepted=ILPA.IsAccepted,
                            HubMessageGuidId =ILPA.HubMessageGuidId,
                            HubMessageRequestBody=ILPA.HubMessageRequestBody,
                            SendBy=ILPA.SendBy,
                            ReceivedFrom=ILPA.ReceivedFrom,
                            SendTo = ILPA.SendTo,

                            UserDetails =
                                (from UD in _readOnlyDbContext.UserDetails
                                 where UD.Id == ILPA.UserDetailsId
                                 select new IncomingLetterPatientActionsUserDetails
                                 {
                                     Id = UD.Id,
                                     Title = UD.TitleId,
                                     FirstName = UD.FirstName,
                                     SurName = UD.SurName
                                 }).FirstOrDefault(),
                            AssigneeDetails =
                                (from UA in _readOnlyDbContext.UserDetails
                                 where UA.Id == ILPA.AssigneeId
                                 select new IncomingLetterPatientActionsUserDetails
                                 {
                                     Id = UA.Id,
                                     Title = UA.TitleId,
                                     FirstName = UA.FirstName,
                                     SurName = UA.SurName
                                 }).FirstOrDefault(),
                            AssigneeDetailsList = (from PAU in _readOnlyDbContext.IncomingLetterActions
                                                   where PAU.StatusId == (short)Status.Active && PAU.IncomingLetterId == ILPA.IncomingLetterId
                                                   select new IncomingLetterActionsView
                                                   {
                                                       Id = PAU.Id,
                                                       OrgId = PAU.OrgId,
                                                       StatusId = PAU.StatusId,
                                                       CreatedBy = PAU.CreatedBy,
                                                       CreatedDate = PAU.CreatedDate,
                                                       ModifiedDate = PAU.ModifiedDate,
                                                       ModifiedBy = PAU.ModifiedBy,
                                                       AssigneeIds = (from IA in _readOnlyDbContext.IncomingLetterActions
                                                                      where IA.IncomingLetterId == ILPA.IncomingLetterId
                                                                      select new
                                                                      {
                                                                          UserIds = ILPA.ActionId == (short)IncomingLetterActionsEnum.Call_patient_and_Advise ? IA.CallPatientUserIds :
                                                                         ILPA.ActionId == (short)IncomingLetterActionsEnum.Book_Non_Urgent_Appointment ? IA.BookNotUrgentAppointmentUserIds :
                                                                         ILPA.ActionId == (short)IncomingLetterActionsEnum.Book_Urgent_Appointment ? IA.BookUrgentAppointmentUserIds :
                                                                         ILPA.ActionId == (short)IncomingLetterActionsEnum.Triage_Letter ? IA.TriageLetterUserIds : null
                                                                      }).FirstOrDefault().UserIds,
                                                   }).FirstOrDefault().AssigneeIds,

                            IncomingLetterActionsRecord = (from ILActions in _readOnlyDbContext.IncomingLetterActions
                                                           where ILActions.StatusId == (short)Status.Active && ILActions.IncomingLetterId == ILPA.IncomingLetterId
                                                           select new IncomingLetterActionsView
                                                           {
                                                               Id = ILActions.Id,
                                                               OrgId = ILActions.OrgId,
                                                               StatusId = ILActions.StatusId,
                                                               CreatedBy = ILActions.CreatedBy,
                                                               CreatedDate = ILActions.CreatedDate,
                                                               ModifiedDate = ILActions.ModifiedDate,
                                                               ModifiedBy = ILActions.ModifiedBy,
                                                               IsNoAction = ILActions.IsNoAction,
                                                               BookNotUrgentAppointmentUserIds = ILActions.BookNotUrgentAppointmentUserIds,
                                                               BookUrgentAppointmentUserIds = ILActions.BookUrgentAppointmentUserIds,
                                                               CallPatientUserIds = ILActions.CallPatientUserIds,
                                                               TriageLetterUserIds = ILActions.TriageLetterUserIds,
                                                               IsBookNotUrgentAppointment = ILActions.IsBookNotUrgentAppointment,
                                                               IsCallPatient = ILActions.IsCallPatient,
                                                               IsTriageLetter = ILActions.IsTriageLetter,
                                                               IsBookUrgentAppointment = ILActions.IsBookUrgentAppointment,
                                                               IncomingLetterId = ILActions.IncomingLetterId,
                                                               PatientDetailsId = ILActions.PatientDetailsId,
                                                               IsBookNotUrgentAppointmentCompleted = ILActions.IsBookNotUrgentAppointmentCompleted,
                                                               IsCallPatientCompleted = ILActions.IsCallPatientCompleted,
                                                               IsBookUrgentAppointmentCompleted = ILActions.IsBookUrgentAppointmentCompleted,
                                                               IsTriageLetterCompleted = ILActions.IsTriageLetterCompleted
                                                           }).FirstOrDefault(),

                            PatientDetails = (ILPA.PatientDetailsId != null) ?
                                (
                                 from PD in _readOnlyDbContext.PatientDetails
                                 where PD.Id == ILPA.PatientDetailsId
                                 select new PatientDetailInfo
                                 {
                                     Id = PD.Id,
                                     FirstName = PD.FirstName,
                                     SurName = PD.SurName,
                                     Mobile = PD.Mobile,
                                     RecordId = PD.RecordId,
                                 }).FirstOrDefault() : null,
                        };
            if (selectedActionId != 0)
            {
                query = query.Where(ILPA => ILPA.ActionId == selectedActionId);
            }
            var result = await query.FirstOrDefaultAsync();
            return result;
        }

        public async Task<IncomingLetterPatientActionView> GetIncomingLetterPatientActionByActivityLogId(short activityLogId)
        {
            //return await _readOnlyDbContext.IncomingLetterPatientActions
            //    .FirstOrDefaultAsync(x => x.IncomingLetterId == id);



            var query = from ILPA in _readOnlyDbContext.IncomingLetterPatientActions
                        where ILPA.LetterInActivityLogId == activityLogId
                        orderby ILPA.CreatedDate
                        select new IncomingLetterPatientActionView
                        {
                            Id = ILPA.Id,
                            OrgId = ILPA.OrgId,
                            IncomingLetterId = ILPA.IncomingLetterId,
                            ActionTypeId = ILPA.ActionTypeId,
                            ActionId = ILPA.ActionId,
                            AssigneeId = ILPA.AssigneeId,
                            DueDate = ILPA.DueDate,
                            StatusId = ILPA.StatusId,
                            IsUrgent = ILPA.IsUrgent,
                            IsClosed = ILPA.IsClosed,
                            IsMediaUploaded = ILPA.IsMediaUploaded,
                            ActivityLogId = ILPA.ActivityLogId,
                            PatientDetailsId = ILPA.PatientDetailsId,
                            CreatedBy = ILPA.CreatedBy,
                            CreatedDate = ILPA.CreatedDate,
                            ModifiedDate = ILPA.ModifiedDate,
                            ModifiedBy = ILPA.ModifiedBy,
                            Tags = ILPA.Tags,
                            Notes = ILPA.Notes,

                            UserDetails =
                                (from UD in _readOnlyDbContext.UserDetails
                                 where UD.Id == ILPA.UserDetailsId
                                 select new IncomingLetterPatientActionsUserDetails
                                 {
                                     Id = UD.Id,
                                     Title = UD.TitleId,
                                     FirstName = UD.FirstName,
                                     SurName = UD.SurName
                                 }).FirstOrDefault(),
                            AssigneeDetails =
                                (from UA in _readOnlyDbContext.UserDetails
                                 where UA.Id == ILPA.AssigneeId
                                 select new IncomingLetterPatientActionsUserDetails
                                 {
                                     Id = UA.Id,
                                     Title = UA.TitleId,
                                     FirstName = UA.FirstName,
                                     SurName = UA.SurName
                                 }).FirstOrDefault(),
                            AssigneeDetailsList = (from PAU in _readOnlyDbContext.IncomingLetterActions
                                                   where PAU.StatusId == (short)Status.Active && PAU.IncomingLetterId == ILPA.IncomingLetterId
                                                   select new IncomingLetterActionsView
                                                   {
                                                       Id = PAU.Id,
                                                       OrgId = PAU.OrgId,
                                                       StatusId = PAU.StatusId,
                                                       CreatedBy = PAU.CreatedBy,
                                                       CreatedDate = PAU.CreatedDate,
                                                       ModifiedDate = PAU.ModifiedDate,
                                                       ModifiedBy = PAU.ModifiedBy,
                                                       AssigneeIds = (from IA in _readOnlyDbContext.IncomingLetterActions
                                                                      where IA.IncomingLetterId == ILPA.IncomingLetterId
                                                                      select new
                                                                      {
                                                                          UserIds = ILPA.ActionId == (short)IncomingLetterActionsEnum.Call_patient_and_Advise ? IA.CallPatientUserIds :
                                                                         ILPA.ActionId == (short)IncomingLetterActionsEnum.Book_Non_Urgent_Appointment ? IA.BookNotUrgentAppointmentUserIds :
                                                                         ILPA.ActionId == (short)IncomingLetterActionsEnum.Book_Urgent_Appointment ? IA.BookUrgentAppointmentUserIds :
                                                                         ILPA.ActionId == (short)IncomingLetterActionsEnum.Triage_Letter ? IA.TriageLetterUserIds : null
                                                                      }).FirstOrDefault().UserIds,
                                                   }).FirstOrDefault().AssigneeIds,

                            IncomingLetterActionsRecord = (from ILActions in _readOnlyDbContext.IncomingLetterActions
                                                           where ILActions.StatusId == (short)Status.Active && ILActions.IncomingLetterId == ILPA.IncomingLetterId
                                                           select new IncomingLetterActionsView
                                                           {
                                                               Id = ILActions.Id,
                                                               OrgId = ILActions.OrgId,
                                                               StatusId = ILActions.StatusId,
                                                               CreatedBy = ILActions.CreatedBy,
                                                               CreatedDate = ILActions.CreatedDate,
                                                               ModifiedDate = ILActions.ModifiedDate,
                                                               ModifiedBy = ILActions.ModifiedBy,
                                                               IsNoAction = ILActions.IsNoAction,
                                                               BookNotUrgentAppointmentUserIds = ILActions.BookNotUrgentAppointmentUserIds,
                                                               BookUrgentAppointmentUserIds = ILActions.BookUrgentAppointmentUserIds,
                                                               CallPatientUserIds = ILActions.CallPatientUserIds,
                                                               TriageLetterUserIds = ILActions.TriageLetterUserIds,
                                                               IsBookNotUrgentAppointment = ILActions.IsBookNotUrgentAppointment,
                                                               IsCallPatient = ILActions.IsCallPatient,
                                                               IsTriageLetter = ILActions.IsTriageLetter,
                                                               IsBookUrgentAppointment = ILActions.IsBookUrgentAppointment,
                                                               IncomingLetterId = ILActions.IncomingLetterId,
                                                               PatientDetailsId = ILActions.PatientDetailsId,
                                                               IsBookNotUrgentAppointmentCompleted = ILActions.IsBookNotUrgentAppointmentCompleted,
                                                               IsCallPatientCompleted = ILActions.IsCallPatientCompleted,
                                                               IsBookUrgentAppointmentCompleted = ILActions.IsBookUrgentAppointmentCompleted,
                                                               IsTriageLetterCompleted = ILActions.IsTriageLetterCompleted
                                                           }).FirstOrDefault(),

                            PatientDetails = (ILPA.PatientDetailsId != null) ?
                                (
                                 from PD in _readOnlyDbContext.PatientDetails
                                 where PD.Id == ILPA.PatientDetailsId
                                 select new PatientDetailInfo
                                 {
                                     Id = PD.Id,
                                     FirstName = PD.FirstName,
                                     SurName = PD.SurName,
                                     Mobile = PD.Mobile,
                                     RecordId = PD.RecordId,
                                 }).FirstOrDefault() : null,
                        };

            //query = query.Where(ILPA => ILPA.ActivityLogId == activityLogId);
            var result = query.FirstOrDefault();
            return result;
        }
        public async Task<IncomingLetterPatientAction> GetIncomingLetterPatientActionByIdOnly(long id,int orgId)
        {
            return await _readOnlyDbContext.IncomingLetterPatientActions.Where(x => x.Id == id).FirstOrDefaultAsync();

        }
        public async Task<int> PartialUpdateIncomingLetterPatientAction(Dictionary<string, object> nameValuePairProperties, long userId, IncomingLetterPatientAction incomingLetterPatientActionDB)
        {
            _updatableDBContext.IncomingLetterPatientActions.Attach(incomingLetterPatientActionDB);
            var currentValues = _updatableDBContext.Entry(incomingLetterPatientActionDB).CurrentValues;
            var type = incomingLetterPatientActionDB.GetType();
            PropertyInfo[] p = type.GetProperties();
            foreach (string key in nameValuePairProperties.Keys)
            {
                if (p.ToList().Where(p => p.Name == key).Any())
                {
                    var value = nameValuePairProperties[key];

                    currentValues[key] = Convert.ChangeType(value, currentValues[key].GetType(), CultureInfo.InvariantCulture);

                }
            }
            if (_updatableDBContext.Entry(incomingLetterPatientActionDB).State == EntityState.Modified)
            {
                incomingLetterPatientActionDB.ModifiedDate = DateTime.UtcNow;
                incomingLetterPatientActionDB.ModifiedBy = userId;
            }
            return await _updatableDBContext.SaveChangesAsync();
        }
        public async Task<List<IncomingLetterPatientAction>> GetIncomingLetterPatientActionByIdList(long id)
        {
            return  _readOnlyDbContext.IncomingLetterPatientActions.Where(x => x.IncomingLetterId == id).ToList();



            //var query = from ILPA in _readOnlyDbContext.IncomingLetterPatientActions
            //            where ILPA.IncomingLetterId == id
            //            orderby ILPA.CreatedDate
            //            select new IncomingLetterPatientActionView
            //            {
            //                Id = ILPA.Id,
            //                OrgId = ILPA.OrgId,
            //                IncomingLetterId = ILPA.IncomingLetterId,
            //                ActionTypeId = ILPA.ActionTypeId,
            //                ActionId = ILPA.ActionId,
            //                AssigneeId = ILPA.AssigneeId,
            //                DueDate = ILPA.DueDate,
            //                StatusId = ILPA.StatusId,
            //                IsUrgent = ILPA.IsUrgent,
            //                IsClosed = ILPA.IsClosed,
            //                IsMediaUploaded = ILPA.IsMediaUploaded,
            //                ActivityLogId = ILPA.ActivityLogId,
            //                PatientDetailsId = ILPA.PatientDetailsId,
            //                CreatedBy = ILPA.CreatedBy,
            //                CreatedDate = ILPA.CreatedDate,
            //                ModifiedDate = ILPA.ModifiedDate,
            //                ModifiedBy = ILPA.ModifiedBy,
            //                Tags = ILPA.Tags,
            //                Notes = ILPA.Notes,

            //                UserDetails =
            //                    (from UD in _readOnlyDbContext.UserDetails
            //                     where UD.Id == ILPA.UserDetailsId
            //                     select new IncomingLetterPatientActionsUserDetails
            //                     {
            //                         Id = UD.Id,
            //                         Title = UD.TitleId,
            //                         FirstName = UD.FirstName,
            //                         SurName = UD.SurName
            //                     }).FirstOrDefault(),
            //                AssigneeDetails =
            //                    (from UA in _readOnlyDbContext.UserDetails
            //                     where UA.Id == ILPA.AssigneeId
            //                     select new IncomingLetterPatientActionsUserDetails
            //                     {
            //                         Id = UA.Id,
            //                         Title = UA.TitleId,
            //                         FirstName = UA.FirstName,
            //                         SurName = UA.SurName
            //                     }).FirstOrDefault(),
            //                AssigneeDetailsList = (from PAU in _readOnlyDbContext.IncomingLetterActions
            //                                       where PAU.StatusId == (short)Status.Active && PAU.IncomingLetterId == ILPA.IncomingLetterId
            //                                       select new IncomingLetterActionsView
            //                                       {
            //                                           Id = PAU.Id,
            //                                           OrgId = PAU.OrgId,
            //                                           StatusId = PAU.StatusId,
            //                                           CreatedBy = PAU.CreatedBy,
            //                                           CreatedDate = PAU.CreatedDate,
            //                                           ModifiedDate = PAU.ModifiedDate,
            //                                           ModifiedBy = PAU.ModifiedBy,
            //                                           AssigneeIds = (from IA in _readOnlyDbContext.IncomingLetterActions
            //                                                          where IA.IncomingLetterId == ILPA.IncomingLetterId
            //                                                          select new
            //                                                          {
            //                                                              UserIds = ILPA.ActionId == (short)IncomingLetterActionsEnum.Call_patient_and_Advise ? IA.CallPatientUserIds :
            //                                                             ILPA.ActionId == (short)IncomingLetterActionsEnum.Book_Non_Urgent_Appointment ? IA.BookNotUrgentAppointmentUserIds :
            //                                                             ILPA.ActionId == (short)IncomingLetterActionsEnum.Book_Urgent_Appointment ? IA.BookUrgentAppointmentUserIds :
            //                                                             ILPA.ActionId == (short)IncomingLetterActionsEnum.Triage_Letter ? IA.TriageLetterUserIds : null
            //                                                          }).FirstOrDefault().UserIds,
            //                                       }).FirstOrDefault().AssigneeIds,

            //                IncomingLetterActionsRecord = (from ILActions in _readOnlyDbContext.IncomingLetterActions
            //                                               where ILActions.StatusId == (short)Status.Active && ILActions.IncomingLetterId == ILPA.IncomingLetterId
            //                                               select new IncomingLetterActionsView
            //                                               {
            //                                                   Id = ILActions.Id,
            //                                                   OrgId = ILActions.OrgId,
            //                                                   StatusId = ILActions.StatusId,
            //                                                   CreatedBy = ILActions.CreatedBy,
            //                                                   CreatedDate = ILActions.CreatedDate,
            //                                                   ModifiedDate = ILActions.ModifiedDate,
            //                                                   ModifiedBy = ILActions.ModifiedBy,
            //                                                   IsNoAction = ILActions.IsNoAction,
            //                                                   BookNotUrgentAppointmentUserIds = ILActions.BookNotUrgentAppointmentUserIds,
            //                                                   BookUrgentAppointmentUserIds = ILActions.BookUrgentAppointmentUserIds,
            //                                                   CallPatientUserIds = ILActions.CallPatientUserIds,
            //                                                   TriageLetterUserIds = ILActions.TriageLetterUserIds,
            //                                                   IsBookNotUrgentAppointment = ILActions.IsBookNotUrgentAppointment,
            //                                                   IsCallPatient = ILActions.IsCallPatient,
            //                                                   IsTriageLetter = ILActions.IsTriageLetter,
            //                                                   IsBookUrgentAppointment = ILActions.IsBookUrgentAppointment,
            //                                                   IncomingLetterId = ILActions.IncomingLetterId,
            //                                                   PatientDetailsId = ILActions.PatientDetailsId,
            //                                                   IsBookNotUrgentAppointmentCompleted = ILActions.IsBookNotUrgentAppointmentCompleted,
            //                                                   IsCallPatientCompleted = ILActions.IsCallPatientCompleted,
            //                                                   IsBookUrgentAppointmentCompleted = ILActions.IsBookUrgentAppointmentCompleted,
            //                                                   IsTriageLetterCompleted = ILActions.IsTriageLetterCompleted
            //                                               }).FirstOrDefault(),

            //                PatientDetails = (ILPA.PatientDetailsId != null) ?
            //                    (
            //                     from PD in _readOnlyDbContext.PatientDetails
            //                     where PD.Id == ILPA.PatientDetailsId
            //                     select new PatientDetailInfo
            //                     {
            //                         Id = PD.Id,
            //                         FirstName = PD.FirstName,
            //                         SurName = PD.SurName,
            //                         Mobile = PD.Mobile
            //                     }).FirstOrDefault() : null,
            //            };
            //var result = query.ToList();
            //return result;
        }

        public async Task<List<IncomingLetterActions>> GetIncomingLetterActionsByIncomingLetterId(long id)
        {
            return _readOnlyDbContext.IncomingLetterActions.Where(x => x.IncomingLetterId == id).ToList();
        }

        public async Task<QueryResultList<IncomingLetterPatientActionView>> GetIncomingLetterPatientActions(int orgId, QueryModel queryModel, IncomingLetterActionsFilterModel filterModel)
        {
            var query = from ILPA in _readOnlyDbContext.IncomingLetterPatientActions
                        //join ILA in _readOnlyDbContext.IncomingLetterActions
                        //on ILPA.IncomingLetterId equals ILA.IncomingLetterId
                        where ILPA.OrgId == orgId && ILPA.StatusId != (short)Status.Deleted
                        //(
                        //  (ILPA.ActionId == (short)IncomingLetterActionsEnum.Triage_Letter && ILA.IsTriageLetterCompleted == false) ||
                        //  (ILPA.ActionId == (short)IncomingLetterActionsEnum.Book_Urgent_Appointment && ILA.IsBookUrgentAppointmentCompleted == false) ||
                        //  (ILPA.ActionId == (short)IncomingLetterActionsEnum.Book_Non_Urgent_Appointment && ILA.IsBookNotUrgentAppointmentCompleted == false) ||
                        //  (ILPA.ActionId == (short)IncomingLetterActionsEnum.Call_patient_and_Advise && ILA.IsCallPatientCompleted == false)
                        //)
                        select new IncomingLetterPatientActionView
                        {
                            Id = ILPA.Id,
                            OrgId = ILPA.OrgId,
                            IncomingLetterId = ILPA.IncomingLetterId,
                            ActionTypeId = ILPA.ActionTypeId,
                            ActionId = ILPA.ActionId,
                            UserDetailsId = ILPA.UserDetailsId,
                            AssigneeId = ILPA.AssigneeId,
                            DueDate = ILPA.DueDate,
                            StatusId = ILPA.StatusId,
                            IsUrgent = ILPA.IsUrgent,
                            IsClosed = ILPA.IsClosed,
                            PatientDetailsId = ILPA.PatientDetailsId,
                            CreatedBy = ILPA.CreatedBy,
                            CreatedDate = ILPA.CreatedDate,
                            ModifiedDate = ILPA.ModifiedDate,
                            ModifiedBy = ILPA.ModifiedBy,
                            Tags = ILPA.Tags,
                            Notes = ILPA.Notes,
                            IsMediaUploaded = ILPA.IsMediaUploaded,
                            ActivityLogId = ILPA.ActivityLogId,
                            LetterInActivityLogId = ILPA.LetterInActivityLogId,
                            IsAccepted=ILPA.IsAccepted,
                            SendBy=ILPA.SendBy,
                            ReceivedFrom=ILPA.ReceivedFrom,
                            HubMessageRequestBody=ILPA.HubMessageRequestBody,
                            SendTo=ILPA.SendTo,
                            UserDetails =
                                (from UD in _readOnlyDbContext.UserDetails
                                 where UD.Id == ILPA.UserDetailsId
                                 select new IncomingLetterPatientActionsUserDetails
                                 {
                                     Id = UD.Id,
                                     Title = UD.TitleId,
                                     FirstName = UD.FirstName,
                                     SurName = UD.SurName
                                 }).FirstOrDefault(),

                            AssigneeDetailsList = (from PAU in _readOnlyDbContext.IncomingLetterActions
                                                   where PAU.StatusId == (short)Status.Active && PAU.IncomingLetterId == ILPA.IncomingLetterId
                                                   select new IncomingLetterActionsView
                                                   {
                                                       Id = PAU.Id,
                                                       OrgId = PAU.OrgId,
                                                       StatusId = PAU.StatusId,
                                                       CreatedBy = PAU.CreatedBy,
                                                       CreatedDate = PAU.CreatedDate,
                                                       ModifiedDate = PAU.ModifiedDate,
                                                       ModifiedBy = PAU.ModifiedBy,
                                                       AssigneeIds = (from IA in _readOnlyDbContext.IncomingLetterActions
                                                                      where IA.IncomingLetterId == ILPA.IncomingLetterId
                                                                      select new
                                                                      {
                                                                         UserIds = ILPA.ActionId == (short)IncomingLetterActionsEnum.Call_patient_and_Advise ? IA.CallPatientUserIds :
                                                                         ILPA.ActionId == (short)IncomingLetterActionsEnum.Book_Non_Urgent_Appointment ? IA.BookNotUrgentAppointmentUserIds :
                                                                         ILPA.ActionId == (short)IncomingLetterActionsEnum.Book_Urgent_Appointment ? IA.BookUrgentAppointmentUserIds :
                                                                         ILPA.ActionId == (short)IncomingLetterActionsEnum.Triage_Letter ? IA.TriageLetterUserIds : null
                                                                      }).FirstOrDefault().UserIds,
                                                   }).FirstOrDefault().AssigneeIds,

                            PatientDetails = (ILPA.PatientDetailsId != null) ?
                                (
                                 from PD in _readOnlyDbContext.PatientDetails
                                 where PD.Id == ILPA.PatientDetailsId
                                 select new PatientDetailInfo
                                 {
                                     Id = PD.Id,
                                     FirstName = PD.FirstName,
                                     SurName = PD.SurName,
                                     Mobile = PD.Mobile,
                                     RecordId = PD.RecordId
                                 }).FirstOrDefault() : null,

                        };

            var resultList = query;
            List<long> allAssigneeIds = new List<long>();

            foreach (var item in resultList)
            {
                if (!string.IsNullOrEmpty(item.AssigneeDetailsList))
                {
                    var assigneeDetails = item.AssigneeDetailsList
                        .Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                        .Select(id => long.Parse(id.Trim()))
                        .ToArray();

                    allAssigneeIds.AddRange(assigneeDetails);
                }
            }

            long[] assigneeDetailsArray = allAssigneeIds.ToArray();

            if (filterModel != null)
            {
                if (filterModel.StartDate != null)
                {
                    query = query.Where(x => x.DueDate >= filterModel.StartDate);
                }
                if (filterModel.EndDate != null)
                {
                    query = query.Where(x => x.DueDate <= filterModel.EndDate);
                }
                // Apply filtering based on AssignedToUserIds
                if (filterModel.AssignedToUserIds != null && filterModel.AssignedToUserIds.Any())
                {
                    //var assignedToUserIds = filterModel.AssignedToUserIds;
                    //foreach (var userId in assignedToUserIds)
                    //{
                    //    var idString = "," + userId.ToString() + ",";
                    //    query = query.Where(x => EF.Functions.Like("," + x.AssigneeDetailsList + ",", "%" + idString + "%"));
                    //}

                    //var assignedToUserIds = filterModel.AssignedToUserIds;
                    //foreach (var userId in assignedToUserIds)
                    //{
                    //    var idString = "," + userId.ToString() + ",";
                    //    query = query.Where(x => EF.Functions.Like("," + x.AssigneeDetailsList + ",", "%" + idString + "%"));
                    //}

                    //var idString = ",";
                    //foreach (var userId in filterModel.AssignedToUserIds)
                    //{
                    //    idString += userId.ToString() + ",";
                    //}

                    //query = query.Where(x => EF.Functions.Like("," + x.AssigneeDetailsList + ",", "%" + idString + "%"));

                    var assignedToUserIds = filterModel.AssignedToUserIds;
                    Expression<Func<IncomingLetterPatientActionView, bool>> combinedExpression = null;

                    foreach (var userId in assignedToUserIds)
                    {
                        var idString = "," + userId.ToString() + ",";
                        Expression<Func<IncomingLetterPatientActionView, bool>> currentExpression = x => EF.Functions.Like("," + x.AssigneeDetailsList + ",", "%" + idString + "%");

                        if (combinedExpression == null)
                        {
                            combinedExpression = currentExpression;
                        }
                        else
                        {
                            var parameter = combinedExpression.Parameters[0];
                            var body = Expression.OrElse(combinedExpression.Body, Expression.Invoke(currentExpression, parameter));
                            combinedExpression = Expression.Lambda<Func<IncomingLetterPatientActionView, bool>>(body, parameter);
                        }
                    }

                    query = query.Where(combinedExpression);

                }
                if (filterModel.AssignedByUserIds != null)
                {
                    query = query.Where(x => filterModel.AssignedByUserIds.Contains(x.UserDetailsId));
                }
                if (filterModel.Status != null && filterModel.Status > 0)
                {
                    query = query.Where(x => x.StatusId == filterModel.Status);
                }
                if (filterModel.ActionTypeId != null)
                {
                    query = query.Where(x => x.ActionTypeId == filterModel.ActionTypeId);
                }
                if (filterModel.ActionIds != null && filterModel.ActionIds.Any())
                {
                    query = query.Where(x => x.ActionId.HasValue && filterModel.ActionIds.Contains(x.ActionId.Value));
                }
                if (filterModel.IsClosed != null && filterModel.IsClosed.Any())
                {
                    query = query.Where(x => x.IsClosed.HasValue && filterModel.IsClosed.Contains(x.IsClosed.Value));
                }

            }
            else
            {
                //decided to display all records irrespective of user
                //query = query.Where(x => x.AssigneeId == userId || x.UserDetailsId == userId);
            }

            if (!(string.IsNullOrWhiteSpace(queryModel.SearchTerm)))
            {
                //commenting now as design issue
                //query = SearchPatientActions(query, queryModel.SearchTerm);
            }

            if (!string.IsNullOrWhiteSpace(queryModel.SortOrder) && !string.IsNullOrWhiteSpace(queryModel.SortTerm))
            {
                query = SortMyActions(query, queryModel.SortOrder, queryModel.SortTerm);
            }

            var paginatedList = await CreatePaginateList(query, queryModel);
            var queryList = new QueryResultList<IncomingLetterPatientActionView>();
            if (paginatedList != null)
            {
                queryList.ItemRecords = paginatedList;
                queryList.PageNumber = queryModel.PageNumber;
                queryList.PageSize = queryModel.PageSize;
                queryList.CurrentCount = paginatedList.Count();
            }
            else
            {
                queryList.ItemRecords = null;
                queryList.CurrentCount = 0;
            }
            queryList.TotalCount = query.Count();
            queryList.PageNumber = queryModel.PageNumber;
            queryList.PageSize = queryModel.PageSize;
            return queryList;
        }

        public bool ContainsAnyAssignee(string assigneeDetailsList, IEnumerable<long?> assignedToUserIds)
        {
            if (assignedToUserIds == null || !assignedToUserIds.Any())
            {
                return false;
            }

            if (string.IsNullOrEmpty(assigneeDetailsList))
            {
                return false;
            }

            var assigneeIds = assigneeDetailsList.Split(',')
                                                 .Select(id => long.Parse(id.Trim()))
                                                 .ToList();

            foreach (var id in assignedToUserIds)
            {
                if (id.HasValue && assigneeIds.Contains(id.Value))
                {
                    return true;
                }
            }

            return false;
        }

        public async Task<int> EditIncomingLetterPatientAction(IncomingLetterPatientAction incomingLetterPatientAction)
        {
            // Retrieve the existing record based on PatientDetailsId
            var existingRecord = await _updatableDBContext.IncomingLetterPatientActions
                .FirstOrDefaultAsync(x => x.IncomingLetterId == incomingLetterPatientAction.IncomingLetterId);

            // Update the existing record with incoming changes
            existingRecord.IncomingLetterId = incomingLetterPatientAction.IncomingLetterId;
            existingRecord.OrgId = incomingLetterPatientAction.OrgId;
            existingRecord.ActionTypeId = incomingLetterPatientAction.ActionTypeId;
            existingRecord.ActionId = incomingLetterPatientAction.ActionId;
            existingRecord.UserDetailsId = incomingLetterPatientAction.UserDetailsId;
            existingRecord.PatientDetailsId = incomingLetterPatientAction.PatientDetailsId;
            existingRecord.DueDate = incomingLetterPatientAction.DueDate;
            existingRecord.AssigneeId = incomingLetterPatientAction.AssigneeId;
            existingRecord.StatusId = incomingLetterPatientAction.StatusId;
            existingRecord.Tags = incomingLetterPatientAction.Tags;
            existingRecord.IsClosed = incomingLetterPatientAction.IsClosed;
            existingRecord.IsUrgent = incomingLetterPatientAction.IsUrgent;
            existingRecord.ModifiedDate = DateTime.Now; // Set the modified date
            existingRecord.ModifiedBy = incomingLetterPatientAction.ModifiedBy;
            existingRecord.isReceived = incomingLetterPatientAction.isReceived;
            existingRecord.Notes = incomingLetterPatientAction.Notes;
            existingRecord.IsMediaUploaded = incomingLetterPatientAction.IsMediaUploaded;
            existingRecord.ActivityLogId = incomingLetterPatientAction.ActivityLogId;
            existingRecord.IsAccepted = incomingLetterPatientAction.IsAccepted;

            // Save the changes to the database
            return await _updatableDBContext.SaveChangesAsync();
        }

        public async Task<int> DeleteIncomingLetterPatientAction(long id)
        {
            try
            {
                var entity = await _readOnlyDbContext.IncomingLetterPatientActions.Where(x => x.IncomingLetterId == id).ToListAsync();
                if (entity != null)
                {
                    try
                    {
                        _updatableDBContext.IncomingLetterPatientActions.RemoveRange(entity);
                        return await _updatableDBContext.SaveChangesAsync();
                    }
                    catch (Exception ex)
                    {
                        throw ex;
                    }
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            
            return 0;
        }

        private async Task<List<IncomingLetterPatientActionView>> CreatePaginateList(IQueryable<IncomingLetterPatientActionView> actionQuery, QueryModel queryModel)
        {
            try
            {
                if (queryModel.PageNumber > 0 && queryModel.PageSize > 0)
                {
                    if (actionQuery.Any())
                    {
                        var paginatedList = await actionQuery.Skip((queryModel.PageNumber - 1) * queryModel.PageSize).Take(queryModel.PageSize).ToListAsync();
                        return paginatedList;
                    }
                }
            }
            catch (Exception ex) { 
                throw ex;
            }
            return null;
        }

        private IQueryable<IncomingLetterPatientActionView> SortMyActions(IQueryable<IncomingLetterPatientActionView> query, string sortOrder, string sortTerm)
        {
            switch (sortTerm.ToLower())
            {
                case "duedate":
                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            query = query.OrderBy(x => x.DueDate);
                        }
                        else
                        {
                            query = query.OrderByDescending(x => x.DueDate);
                        }
                        break;
                    }
                default:
                    {
                        query = query.OrderBy(x => x.DueDate);
                        break;
                    }
            }
            return query;
        }


        /// <summary>
        /// GetPatientActionsNotificationCount
        /// </summary>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<List<IncomingLetterPatientActionsNotificationCount>> GetIncomingLetterPatientActionsNotificationCount(int orgId)
        {
            var query = from ILPA in _readOnlyDbContext.IncomingLetterPatientActions
                        where ILPA.OrgId == orgId && ILPA.StatusId == (short)Status.Active
                        group ILPA by ILPA.AssigneeId into g
                        select new IncomingLetterPatientActionsNotificationCount
                        {
                            Count = g.Count()
                        };

            return await query.ToListAsync();
        }


        /// <summary>
        /// GetPatientActionsNotificationCountBuUserId
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task<List<IncomingLetterPatientActionsNotificationCount>> GetIncomingLetterPatientActionsNotificationCountByUserId(int orgId, long userId)
        {
            var query = from ILPA in _readOnlyDbContext.IncomingLetterPatientActions
                        where ILPA.OrgId == orgId && ILPA.StatusId == (short)Status.Active && ILPA.AssigneeId == userId && ILPA.IsClosed == false
                        group ILPA by ILPA.CreatedBy into g
                        select new IncomingLetterPatientActionsNotificationCount
                        {
                            ActionTypeId = 0,
                            Count = g.Count()
                        };

            return await query.ToListAsync();
        }

        public async Task<RolesPermissionsAssoc> GetPermissionOnRoleId(int orgId, int rid, short pid)
        {
            var permissionobject = _readOnlyDbContext.RolesPermissionsAssocs.FirstOrDefault(x => x.RolesId == rid && x.StatusId == (short)Status.Active && x.OrgId == orgId && x.PermissionsId == pid);
            return permissionobject;
        }

        public async Task<List<UserDetailInfo>> FetchUserDetailsFromIds(List<long> listIds, int orgId)
        {
            return await _readOnlyDbContext.UserDetails.Where(x => listIds.Contains(x.Id) && x.OrgId == orgId).Select(x => new UserDetailInfo
            {
                Id = x.Id,
                FirstName = x.FirstName,
                SurName = x.SurName,
                TitleId = x.TitleId,
                FileDetailsOutput = null
            }).ToListAsync();
        }

        public async Task<List<PatientDetailInfo>> FetchPatinetDetailsFromIds(List<long> listIds, int orgId)
        {
            return await _readOnlyDbContext.PatientDetails.Where(x => listIds.Contains(x.Id) && x.OrgId == orgId).Select(x => new PatientDetailInfo
            {
                Id = x.Id,
                FirstName = x.FirstName,
                SurName = x.SurName,
                RecordId = x.RecordId

            }).ToListAsync();
        }
        public async Task<List<long>> FetchPatintActionListForUser(List<long?> userIds, int orgId)
        {
            return await _readOnlyDbContext.IncomingLetterPatientActions.Where(x => userIds.Contains(x.UserDetailsId)
            && x.OrgId == orgId && x.StatusId == (short)Status.Active).Select(y => y.IncomingLetterId).ToListAsync();
        }

        public async Task<List<IncomingLetterPatientActionsUserAssocs>> GetIncomingLetterPatientUserAssoc(int orgId, long patientActionsId)
        {
            return await _readOnlyDbContext.IncomingLetterPatientActionsUserAssocs.Where(s => s.IncomingLetterPatientActionsId == patientActionsId && s.OrgId == orgId).AsNoTracking().ToListAsync();
        }

        /// <summary>
        /// GetIncomingLetterActionsSyncByMultipleIds
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="actionIds"></param>
        /// <returns></returns>
        public async Task<List<IncomingLetterPatientAction>> GetIncomingLetterActionsSyncByMultipleIds(int orgId, List<long> actionIds)
        {
            return await _readOnlyDbContext.IncomingLetterPatientActions
                .Where(s => actionIds.Contains((long)s.Id) && s.OrgId == orgId)
                .ToListAsync();
        }

        /// <summary>
        /// EditMultiIncomingLetterPatientActions
        /// </summary>
        /// <param name="patientActions"></param>
        /// <returns></returns>
        public async Task<int> EditIncomingLetterMultiUserPatientActions(List<IncomingLetterPatientAction> patientActions)
        {
            _updatableDBContext.IncomingLetterPatientActions.UpdateRange(patientActions);
            return await _updatableDBContext.SaveChangesAsync();
        }

        public async Task<List<IncomingLetterPatientActionsUserDetails>> GetUserDetailsFromCommaSeparatedIdsAsync(string commaSeparatedUserIds)
        {
            if (string.IsNullOrEmpty(commaSeparatedUserIds))
            {
                return new List<IncomingLetterPatientActionsUserDetails>();
            }

            var userIds = commaSeparatedUserIds.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                                               .Select(long.Parse)
                                               .ToList();

            var userDetailsList = await (from UD in _readOnlyDbContext.UserDetails
                                         where userIds.Contains(UD.Id)
                                         select new IncomingLetterPatientActionsUserDetails
                                         {
                                             Id = UD.Id,
                                             Title = UD.TitleId,
                                             FirstName = UD.FirstName,
                                             SurName = UD.SurName
                                         }).ToListAsync();

            return userDetailsList;
        }

        public async Task<bool?> IncomingLetterAccepted(long incomingLetterId)
        {

            // Fetch the incoming letter patient action based on the incomingLetterId
            var incomingLetter = await _readOnlyDbContext.IncomingLetterPatientActions
                                                 .FirstOrDefaultAsync(s => s.IncomingLetterId == incomingLetterId);

            // Check if the record exists
            if (incomingLetter != null)
            {
                // Update the IsAccepted flag to true
                incomingLetter.IsAccepted = true; 

                // Save the changes to the database
                await _readOnlyDbContext.SaveChangesAsync();
                return true;
            }
            return false;
        }


    }
}
