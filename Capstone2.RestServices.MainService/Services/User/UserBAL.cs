﻿using AutoMapper;
using Capstone2.Framework.RestApi;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.Framework.RestApi.Utility;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.User.Interfaces;
using Capstone2.RestServices.User.Models;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Transactions;
using EntitiesModel = Capstone2.Shared.Models.Entities;

namespace Capstone2.RestServices.User.Services
{
    public class UserBAL : IUserBAL
    {
        public readonly AppSettings _appSettings;
        public readonly IUserDAL _userDAL;
        public IMapper _mapper;
        public IUserCompanyDAL _userCompanyDAL;
        public IAddressDAL _addressDAL;
        private IASBMessageSenderHelper _asbMessageSenderHelper;
        private readonly IConfiguration _configuration;
        private readonly ILogger<UserBAL> _logger;

        public UserBAL(IOptions<AppSettings> appSettings, IConfiguration configuration, IASBMessageSenderHelper asbMessageSenderHelper, IUserDAL userDAL, IMapper mapper, IUserCompanyDAL userCompanyDAL, IAddressDAL addressDAL, ILogger<UserBAL> logger)
        {
            _appSettings = appSettings.Value;
            this._asbMessageSenderHelper = asbMessageSenderHelper;
            this._configuration = configuration;
            _userDAL = userDAL;
            _mapper = mapper;
            _userCompanyDAL = userCompanyDAL;
            _addressDAL = addressDAL;
            _logger = logger;
        }

        public async Task<ApiResponse<UserDetail>> AddUserDetailsBAL(UserDetailInput user, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<UserDetail> apiResponse = new ApiResponse<UserDetail>();
            if (user.Role is not null)
            {
                int count = 0;
                if (user.Role.Id is not null)
                    count = await _userDAL.SearchRole(user.Role.Id, baseHttpRequestContext.OrgId);
                if (count == 0)
                {
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Message = "Failed";
                    apiResponse.Errors.Add("Invalid role.");
                    return apiResponse;
                }
            }
            AuthUserRequest newloginuser = new AuthUserRequest()
            {
                Email = user.LoginEmail,
                IsOffsiteAccess = (bool)user.OffSiteLogin,
                Mobile = user.Mobile,
                UserIpaddress = user.UserIpaddress,
                RoleId = (int)user.Role.Id
            };

            var token = baseHttpRequestContext.BearerToken;
            var interServiceToken = baseHttpRequestContext.InterServiceToken;

            string adduserAPiUrl = _appSettings.ApiUrls["AuthServiceUrl"] + "/auth/adduser";
            RestClient restClient = new(adduserAPiUrl, null, token, interServiceToken);
            var authuserResponse = await restClient.PostAsync<ApiResponse<string>>(adduserAPiUrl, newloginuser);
            long userId = Convert.ToInt64(authuserResponse.Result);
            if (authuserResponse.StatusCode == 200)
            {
                var addeduser = await _userDAL.AddUserDetailsDAL(user, userId, baseHttpRequestContext.OrgId, baseHttpRequestContext.UserId);
                UserActivationRequest newuseractivate = new UserActivationRequest()
                {
                    Type = "Email_Password",
                    UserId = addeduser.Id,
                    Identifier = addeduser.LoginEmail,
                    RoleId = (int)newloginuser.RoleId

                };
                string sendUserMailUrl = _appSettings.ApiUrls["AuthServiceUrl"] + "/auth/factors/activate";
                RestClient restClient1 = new RestClient(sendUserMailUrl, null, token);
                var useractivateResponse = await restClient1.PostAsync<ApiResponse<string>>(sendUserMailUrl, newuseractivate);
                if (useractivateResponse.StatusCode == 200)
                {
                    apiResponse.Result = addeduser;
                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    apiResponse.Message = "Success";
                    return apiResponse;
                }
                else
                {
                    apiResponse.StatusCode = useractivateResponse.StatusCode;
                    apiResponse.Errors = useractivateResponse.Errors;
                    apiResponse.Message = useractivateResponse.Message;
                    return apiResponse;
                }
            }
            else
            {
                apiResponse.StatusCode = authuserResponse.StatusCode;
                apiResponse.Errors = authuserResponse.Errors;
                apiResponse.Message = authuserResponse.Message;
                return apiResponse;
            }
        }

        public async Task<ApiResponse<UserDetailInput>> GetUserDetailsBAL(int orgId, long Id, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<UserDetailInput> apiResponse = new ApiResponse<UserDetailInput>();
            apiResponse.Result = new UserDetailInput();
            var userDetailFromDB = await _userDAL.GetUserDetailsDAL(orgId, Id);

            userDetailFromDB.HIServiceDetails = await _userDAL.GetHIServiceDetails(orgId, Id);
            if (userDetailFromDB?.HIServiceDetails?.Count > 0)
            {
                var hiserviceDetail = userDetailFromDB.HIServiceDetails
                                      .Where(x => x.HPIINumber == userDetailFromDB.HPIINumber)
                                      .OrderByDescending(x => x.ModifiedDate ?? x.CreatedDate)
                                      .FirstOrDefault();
                if (hiserviceDetail != null)
                {
                    userDetailFromDB.HPIISearchTypeId = hiserviceDetail.HISearchTypeId;
                    userDetailFromDB.HPIINumberRecordId = hiserviceDetail.Id;
                    userDetailFromDB.HPIIRecordStatusId = hiserviceDetail.HIRecordStatusId;
                    userDetailFromDB.HPIIStatusId = hiserviceDetail.HIStatusId;
                    userDetailFromDB.HPIIVerificationMessage = hiserviceDetail.VerificationMessage;
                    userDetailFromDB.HPIINumberVerifiedDate = hiserviceDetail.ModifiedDate ?? hiserviceDetail.CreatedDate;
                }
            }

            if (userDetailFromDB.PhotoFileDetailsId != null)
            {
                long fileId = (long)userDetailFromDB.PhotoFileDetailsId;
                var token = baseHttpRequestContext.BearerToken;
                string interServiceToken = baseHttpRequestContext.InterServiceToken;
                string fileAPiUrl = _appSettings.ApiUrls["FileServiceUrl"] + "/file/file_details/" + fileId;
                RestClient restClient = new RestClient(fileAPiUrl, null, token, interServiceToken);
                var fileApiResponse = await restClient.GetAsync<ApiResponse<FileDetailsOutputForId>>(fileAPiUrl, null);
                if (fileApiResponse.StatusCode == StatusCodes.Status200OK)
                {
                    userDetailFromDB.FileDetailsOutput = fileApiResponse.Result;
                }
                else
                {
                    apiResponse.StatusCode = fileApiResponse.StatusCode;
                    apiResponse.Errors = fileApiResponse.Errors;
                    apiResponse.Message = fileApiResponse.Message;
                    return apiResponse;
                }
            }

            apiResponse.Result = userDetailFromDB;
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            return apiResponse;
        }

        public async Task<ApiResponse<bool>> CheckUserEmailBAL(string search_term, int orgId)
        {

            var userEmailBool = await _userDAL.CheckUserEmailDAL(search_term, orgId);

            var apiResponse = new ApiResponse<bool>
            {
                Result = userEmailBool,
                Message = "Success",
                StatusCode = StatusCodes.Status200OK
            };
            return apiResponse;
        }

        public async Task<ApiResponse<bool>> CheckUserMobileBAL(string search_term, int orgId)
        {
            var userEmailBool = await _userDAL.CheckUserMobileDAL(search_term, orgId);
            var apiResponse = new ApiResponse<bool>
            {
                Result = userEmailBool,
                Message = "Success",
                StatusCode = StatusCodes.Status200OK
            };
            return apiResponse;
        }

        public async Task<ApiResponse<UserDetail>> EditUserDetailsBAL(long Id, UserDetailInput user, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<UserDetail> apiResponse = new ApiResponse<UserDetail>();
            var userFromDb = await _userDAL.GetUserDetailsDAL(baseHttpRequestContext.OrgId, Id);
            if (userFromDb == null)
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failed";
                apiResponse.Errors.Add("User not found");
                return apiResponse;
            }

            if (user.UserStatusId == (int)UserStatus.AccountLocked || user.UserStatusId == (int)UserStatus.AccountSuspended)
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failed";
                apiResponse.Errors.Add("Profile Status Can not be changed to " + EnumExtensions.GetDescription((UserStatus)user.UserStatusId));
                return apiResponse;
            }

            if (userFromDb.UserStatusId != (int)UserStatus.PendingRegistration && user.UserStatusId == (int)UserStatus.PendingRegistration)
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failed";
                apiResponse.Errors.Add("Profile Status Can not be changed to " + EnumExtensions.GetDescription((UserStatus)user.UserStatusId));
                return apiResponse;
            }

            if (userFromDb.UserStatusId == (int)UserStatus.PendingRegistration && user.UserStatusId != (int)UserStatus.PendingRegistration)
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failed";
                apiResponse.Errors.Add("Profile Status Can not be changed to " + EnumExtensions.GetDescription((UserStatus)user.UserStatusId));
                return apiResponse;
            }
            if (user.Role is not null)
            {
                int count = 0;
                if (user.Role.Id is not null)
                    count = await _userDAL.SearchRole(user.Role.Id, baseHttpRequestContext.OrgId);
                if (count == 0)
                {
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Message = "Failed";
                    apiResponse.Errors.Add("Invalid role.");
                    return apiResponse;
                }
            }
            var userHealthFundAssocInput = user.UserHealthFundAssocs;
            user.UserHealthFundAssocs = null;
            user.UserHealthFundAssocs = EditUserHealthFundAssocs(userHealthFundAssocInput, userFromDb.UserHealthFundAssocs, baseHttpRequestContext, (long)user.Id);
            AuthUserRequest newloginuser = new AuthUserRequest()
            {
                IsOffsiteAccess = (bool)user.OffSiteLogin,
                Mobile = user.Mobile,
                UserIpaddress = user.UserIpaddress,
                UserStatusId = user.UserStatusId,
                RoleId = (int)((user.Role is not null) ? user.Role.Id : default(int))
            };
            var token = baseHttpRequestContext.BearerToken;
            var interserviceToken = baseHttpRequestContext.InterServiceToken;
            string adduserAPiUrl = _appSettings.ApiUrls["AuthServiceUrl"] + "/auth/edituser/" + Id;
            RestClient restClient = new RestClient(adduserAPiUrl, null, token, interserviceToken);
            var authuserResponse = await restClient.PutAsync<ApiResponse<string>>(adduserAPiUrl, newloginuser);
            long userId = Convert.ToInt64(authuserResponse.Result);
            if (authuserResponse.StatusCode == 200)
            {
                bool isASBChange = false; string fullName = string.Empty;
                using (var transaction = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
                {
                    var mappedUser = _mapper.Map<UserDetailInput, UserDetail>(user);
                    mappedUser.LoginEmail = userFromDb.LoginEmail;
                    mappedUser.ModifiedBy = baseHttpRequestContext.UserId;
                    fullName = $"{user.FirstName} {user.SurName}";
                    if (!fullName.Equals($"{userFromDb.FirstName} {userFromDb.SurName}", StringComparison.OrdinalIgnoreCase))
                        isASBChange = true;
                    var addeduser = await _userDAL.EditUserDetailsDAL(Id, mappedUser, baseHttpRequestContext.OrgId, baseHttpRequestContext.UserId);

                    //Edit the InternalComapnies
                    var internalComapnyAssocs = await _userCompanyDAL.GetusercompanyAssocs(Id, baseHttpRequestContext.OrgId);
                    var addCompanyList = new List<UserCompanyAssoc>();
                    var removeCompanyList = internalComapnyAssocs;
                    var inputComapnyList = user.InputUserCompanyAssocs;
                    var updateCompanyList = new List<UserCompanyAssoc>();

                    foreach (var company in inputComapnyList)
                    {
                        if (company.Id != null)
                        {
                            var existingobj = removeCompanyList.FirstOrDefault(x => x.Id == company.Id);
                            if (existingobj is not null && (!existingobj.ProviderNumber.Equals(company.ProviderNumber) || company.IsDefault != existingobj.IsDefault))
                            {
                                var companyTodb = _mapper.Map<UserCompanyAssocInput, UserCompanyAssoc>(company);
                                companyTodb.OrgId = baseHttpRequestContext.OrgId;
                                companyTodb.UserDetailsId = Id;
                                companyTodb.ModifiedBy = baseHttpRequestContext.UserId;
                                companyTodb.ModifiedDate = DateTime.UtcNow;
                                companyTodb.StatusId = (short)Status.Active;
                                updateCompanyList.Add(companyTodb);
                            }
                            var result = removeCompanyList.Remove(existingobj);
                        }
                        else
                        {
                            var companyTodb = _mapper.Map<UserCompanyAssocInput, UserCompanyAssoc>(company);
                            companyTodb.OrgId = baseHttpRequestContext.OrgId;
                            companyTodb.UserDetailsId = Id;
                            companyTodb.ModifiedBy = baseHttpRequestContext.UserId;
                            companyTodb.StatusId = (short)Status.Active;
                            addCompanyList.Add(companyTodb);
                        }
                    }
                    if (removeCompanyList.Count > 0)
                    {
                        removeCompanyList.ForEach(x => { x.StatusId = (short)Status.Deleted; x.ModifiedBy = baseHttpRequestContext.UserId; x.ModifiedDate = DateTime.UtcNow; });
                        await _userCompanyDAL.RemoveusercompanyList(removeCompanyList);
                    }
                    if (addCompanyList.Count > 0)
                    {
                        await _userCompanyDAL.AddUsercompanyList(addCompanyList);
                    }
                    if (updateCompanyList.Count > 0)
                    {
                        await _userCompanyDAL.UpdateusercompanyList(updateCompanyList);
                    }

                    //Edit the Addresses
                    var addresses = await _addressDAL.GetAddresses(Id, baseHttpRequestContext.OrgId);
                    var addAddressList = new List<Address>();
                    var removeAddressList = addresses;
                    var inputAddressList = user.InputAddresses;

                    foreach (var address in inputAddressList)
                    {
                        if (address.Id != null)
                        {
                            var existingobj = removeAddressList.FirstOrDefault(x => x.Id == address.Id);
                            var result = removeAddressList.Remove(existingobj);
                        }
                        else
                        {
                            var AddressTodb = _mapper.Map<AddressInput, Address>(address);
                            AddressTodb.OrgId = baseHttpRequestContext.OrgId;
                            AddressTodb.UserDetailsId = Id;
                            AddressTodb.ModifiedBy = baseHttpRequestContext.UserId;
                            AddressTodb.StatusId = (short)Status.Active;
                            addAddressList.Add(AddressTodb);
                        }
                    }

                    foreach (var address in removeAddressList)
                    {
                        address.StatusId = (short)Status.Deleted;
                    }

                    if (removeAddressList.Count > 0)
                    {
                        await _addressDAL.UpdateAddressList(removeAddressList);
                    }
                    if (addAddressList.Count > 0)
                    {
                        await _addressDAL.AddAddressList(addAddressList);
                    }

                    transaction.Complete();
                    apiResponse.Result = addeduser;
                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    apiResponse.Message = "Success";
                }

                //TODO:Send message to Service Bus and name to be update in EOD report
                if (isASBChange)
                    await StoreUserDetailMessage(orgCode: baseHttpRequestContext.OrgCode, userId: Id, fullName: fullName);

                return apiResponse;
            }
            else
            {
                apiResponse.StatusCode = authuserResponse.StatusCode;
                apiResponse.Errors = authuserResponse.Errors;
                apiResponse.Message = authuserResponse.Message;
                return apiResponse;
            }
        }

        private ICollection<UserHealthFundAssoc> EditUserHealthFundAssocs(ICollection<UserHealthFundAssoc> userHealthFundAssocsInput, ICollection<UserHealthFundAssoc> userHealthFundAssocsDB, BaseHttpRequestContext baseHttpRequestContext, long userDetailsID)
        {
            List<UserHealthFundAssoc> addUserHealthFundAssoc = new();
            foreach (var healthFundAssoc in userHealthFundAssocsInput)
            {
                if (healthFundAssoc.Id > 0)
                {
                    var existingobj = userHealthFundAssocsDB.FirstOrDefault(x => x.Id == healthFundAssoc.Id && x.ParticipantId == healthFundAssoc.ParticipantId);
                    existingobj.StatusId = (short)Status.Active;
                    healthFundAssoc.OrgId = baseHttpRequestContext.OrgId;
                    existingobj.ModifiedDate = DateTime.UtcNow;
                    existingobj.ModifiedBy = baseHttpRequestContext.UserId;
                    existingobj.FundPayeeId = healthFundAssoc.FundPayeeId;
                    existingobj.HasScheme = healthFundAssoc.HasScheme;
                    existingobj.HasAgreement = healthFundAssoc.HasAgreement;

                    addUserHealthFundAssoc.Add(existingobj);
                    var result = userHealthFundAssocsDB.Remove(existingobj);
                }
                else
                {
                    var existingobj = userHealthFundAssocsDB.FirstOrDefault(x => x.ParticipantId == healthFundAssoc.ParticipantId);
                    if (existingobj != null)
                    {

                        existingobj.StatusId = (short)Status.Active;
                        existingobj.OrgId = baseHttpRequestContext.OrgId;
                        existingobj.ModifiedDate = DateTime.UtcNow;
                        addUserHealthFundAssoc.Add(existingobj);
                        existingobj.FundPayeeId = healthFundAssoc.FundPayeeId;
                        existingobj.HasScheme = healthFundAssoc.HasScheme;
                        existingobj.HasAgreement = healthFundAssoc.HasAgreement;
                        //result = userHealthFundAssocsDB.Remove(existingobj);
                        existingobj.ModifiedBy = baseHttpRequestContext.UserId;


                        var result = userHealthFundAssocsDB.Remove(existingobj);


                    }
                    else
                    {
                        healthFundAssoc.StatusId = (short)Status.Active;
                        healthFundAssoc.OrgId = baseHttpRequestContext.OrgId;
                        healthFundAssoc.CreatedDate = DateTime.UtcNow;
                        healthFundAssoc.ModifiedBy = baseHttpRequestContext.UserId;
                        healthFundAssoc.UserDetailsId = userDetailsID;
                        addUserHealthFundAssoc.Add(healthFundAssoc);
                    }

                }
            }

            foreach (var userHealthFund in userHealthFundAssocsDB)
            {
                userHealthFund.StatusId = (short)Status.Deleted;
                userHealthFund.ModifiedDate = DateTime.UtcNow;
                userHealthFund.ModifiedBy = baseHttpRequestContext.UserId;
            }
            if (userHealthFundAssocsDB != null && userHealthFundAssocsDB.Count > 0)
                addUserHealthFundAssoc = addUserHealthFundAssoc.Concat(userHealthFundAssocsDB).ToList();
            return addUserHealthFundAssoc;
        }

        private async Task StoreUserDetailMessage(string orgCode, long userId, string fullName)
        {

            EntitiesModel.PaymentRequestDataModel updateRequestDataModel = new EntitiesModel.PaymentRequestDataModel
            {
                OrgName = orgCode,
                PropertyType = (int)EODRequestDataType.User,
                PropertyId = new long[] { userId },
                PropertyValue = fullName
            };
            Dictionary<string, string> userPros = new Dictionary<string, string>
                        {
                            { "RequestType","User"},
                            { "to",_configuration["AzureAD:ASBSubNameUserEOD"]}
                        };

            byte[] jsonContentByteArr = Encoding.GetEncoding("iso-8859-1").GetBytes(JsonConvert.SerializeObject(updateRequestDataModel));
            await _asbMessageSenderHelper.MessageSenderTopic(jsonContentByteArr, userPros, _configuration["AzureAD:AzSBConnStringUser"], _configuration["AzureAD:ASBTopicUser"]);

        }

        public async Task<ApiResponse<string>> DeleteLinkedProviders(int companyId, BaseHttpRequestContext baseHttpRequestContext)
        {
            List<UserCompanyAssoc> userCompList = await _userCompanyDAL.FetchUserCompanyAssocs(companyId, baseHttpRequestContext.OrgId);
            userCompList.ForEach(x => { x.StatusId = (short)Status.Deleted; x.ModifiedBy = baseHttpRequestContext.UserId; x.ModifiedDate = DateTime.UtcNow; });
            await _userCompanyDAL.RemoveusercompanyList(userCompList);
            return new ApiResponse<string>
            {
                StatusCode = StatusCodes.Status200OK,
                Result = "Success"
            };
        }
        /// <summary>
        /// Method to few few details of a user
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<Shared.Models.Entities.UserInfoView>> GetUserDetailInfo(int orgId, long id)
        {
            ApiResponse<Shared.Models.Entities.UserInfoView> apiResponse = new();
            Shared.Models.Entities.UserInfoView userinfo = await _userDAL.FetchUserDetailInfo(id, orgId);
            if (userinfo is null)
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status200OK;
            }
            apiResponse.Result = userinfo;
            return apiResponse;

        }
        /// <summary>
        /// Methos to fetch a list of users with limited info
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task<ApiResponse<List<Shared.Models.Entities.UserInfoView>>> ListUserDetailInfo(int orgId, string ids)
        {
            ApiResponse<List<Shared.Models.Entities.UserInfoView>> apiResponse = new();
            List<long> lstIds = ids.Split(',').ToList().Select(s => long.Parse(s)).ToList();

            List<Shared.Models.Entities.UserInfoView> lstUser = await _userDAL.ListUserDetailInfo(lstIds, orgId);

            if (lstUser is null)
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status200OK;
            }
            apiResponse.Result = lstUser;
            return apiResponse;
        }

        /// <summary>
        /// Get User notes
        /// </summary>
        /// <param name="orgId">orgId</param>
        /// <param name="userId">userId</param>
        /// <returns>quicknote object</returns>
        public async Task<ApiResponse<QuickNote>> GetQuickNote(int orgId, long userId)
        {
            var noteDetails = await _userDAL.GetQuickNote(orgId, userId);

            if (noteDetails != null)
            {
                noteDetails.NoteDescription = System.Web.HttpUtility.HtmlDecode(noteDetails.NoteDescription);
            }
            var apiResponse = new ApiResponse<QuickNote>
            {
                Result = noteDetails,
                Message = "Success",
                StatusCode = StatusCodes.Status200OK
            };
            return apiResponse;
        }

        /// <summary>
        /// Add/Edit method for user quicknote
        /// </summary>
        /// <param name="userId">userId</param>
        /// <param name="noteInput">noteInput</param>
        /// <param name="baseHttpRequestContext">baseHttpRequestContext</param>
        /// <returns>quicknote object</returns>
        public async Task<ApiResponse<QuickNote>> UpsertQuickNote(long userId, QuickNoteInput noteInput, BaseHttpRequestContext baseHttpRequestContext)
        {
            var apiResponse = new ApiResponse<QuickNote>();
            var noteDetails = await _userDAL.GetQuickNote(baseHttpRequestContext.OrgId, userId);
            if (noteDetails == null)
            {
                string encodedNote = System.Web.HttpUtility.HtmlEncode(noteInput.NoteDescription);
                noteDetails = new QuickNote();
                noteDetails.OrgId = baseHttpRequestContext.OrgId;
                noteDetails.UserDetailsId = userId;
                noteDetails.NoteDescription = encodedNote;
                noteDetails.StatusId = (short)Status.Active;
                noteDetails.CreatedBy = baseHttpRequestContext.UserId;
                noteDetails = await _userDAL.AddQuickNote(noteDetails);
            }
            else
            {
                string encodedNote = System.Web.HttpUtility.HtmlEncode(noteInput.NoteDescription);
                noteDetails.NoteDescription = encodedNote;
                noteDetails.ModifiedBy = baseHttpRequestContext.UserId;
                noteDetails.ModifiedDate = DateTime.UtcNow;
                noteDetails = await _userDAL.EditQuickNote(noteDetails);
            }

            apiResponse.Result = noteDetails;
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            return apiResponse;
        }

        /// <summary>
        /// Get all whatson message
        /// </summary>
        /// <param name="orgId">orgId</param>
        /// <param name="userId">userId</param>
        /// <returns>return list of message</returns>
        public async Task<ApiResponse<List<WhatsOnMessages>>> GetWhatsOnMessages(int orgId, long userId)
        {
            var messageDetails = await _userDAL.GetWhatsOnMessages(orgId, userId);
            var apiResponse = new ApiResponse<List<WhatsOnMessages>>
            {
                Result = messageDetails,
                Message = "Success",
                StatusCode = StatusCodes.Status200OK
            };
            return apiResponse;
        }

        /// <summary>
        /// Add/Edit method for whatson message
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="messageInput"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns>whatsonmessage object</returns>
        //public async Task<ApiResponse<WhatsOnMessages>> UpsertWhatsOnMessages(long userId, WhatsOnMessagesInput messageInput, BaseHttpRequestContext baseHttpRequestContext)
        //{
        //    var apiResponse = new ApiResponse<WhatsOnMessages>();
        //    var messageDetails = await _userDAL.GetWhatsOnMessagesById(baseHttpRequestContext.OrgId, userId, messageId: messageInput.Id.HasValue ? messageInput.Id.Value : 0);
        //    if (messageDetails == null)
        //    {
        //        messageDetails = new WhatsOnMessages();
        //        messageDetails.OrgId = baseHttpRequestContext.OrgId;
        //        messageDetails.UserDetailsId = userId;
        //        messageDetails.Description = messageInput.Description;
        //        messageDetails.StatusId = (short)Status.Active;
        //        messageDetails.CreatedBy = baseHttpRequestContext.UserId;
        //        messageDetails = await _userDAL.AddWhatsOnMessages(messageDetails);
        //    }
        //    else
        //    {
        //        messageDetails.Description = messageInput.Description;
        //        messageDetails.ModifiedBy = baseHttpRequestContext.UserId;
        //        messageDetails.ModifiedDate = DateTime.UtcNow;
        //        messageDetails = await _userDAL.EditWhatsOnMessages(messageDetails);
        //    }

        //    apiResponse.Result = messageDetails;
        //    apiResponse.StatusCode = StatusCodes.Status200OK;
        //    apiResponse.Message = "Success";
        //    return apiResponse;
        //}

        public async Task<ApiResponse<WhatsOnMessages>> UpsertWhatsOnMessages(long userId, WhatsOnMessagesInput messageInput, BaseHttpRequestContext baseHttpRequestContext)
        {
            var apiResponse = new ApiResponse<WhatsOnMessages>();
            var messageDetails = await _userDAL.GetWhatsOnMessagesById(baseHttpRequestContext.OrgId, userId, messageId: messageInput.Id.HasValue ? messageInput.Id.Value : 0);

            if (messageDetails == null) // If no message found, add new
            {
                messageDetails = new WhatsOnMessages
                {
                    OrgId = baseHttpRequestContext.OrgId,
                    UserDetailsId = userId,
                    Description = messageInput.Description,
                    StatusId = (short)Status.Active,
                    CreatedBy = baseHttpRequestContext.UserId,
                    CreatedDate = DateTime.UtcNow
                };

                if (messageInput.StartDate.HasValue)
                {
                    messageDetails.StartDate = messageInput.StartDate.Value.Date + messageInput.StartDate.Value.TimeOfDay;
                }

                if (messageInput.EndDate.HasValue)
                {
                    messageDetails.EndDate = messageInput.EndDate.Value.Date + messageInput.EndDate.Value.TimeOfDay;
                }

                // Add new message to the database
                messageDetails = await _userDAL.AddWhatsOnMessages(messageDetails);
            }
            else // If message found, update existing
            {
                messageDetails.Description = messageInput.Description;
                messageDetails.ModifiedBy = baseHttpRequestContext.UserId;
                messageDetails.ModifiedDate = DateTime.UtcNow;

                if (messageInput.StartDate.HasValue)
                {
                    messageDetails.StartDate = messageInput.StartDate.Value.Date + messageInput.StartDate.Value.TimeOfDay;
                }

                if (messageInput.EndDate.HasValue)
                {
                    messageDetails.EndDate = messageInput.EndDate.Value.Date + messageInput.EndDate.Value.TimeOfDay;
                }

                // Ensure changes are persisted to the database
                messageDetails = await _userDAL.EditWhatsOnMessages(messageDetails); // Save changes to DB
            }

            apiResponse.Result = messageDetails; // Return updated result
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            return apiResponse;
        }

        public async Task<ApiResponse<string>> DeleteWhatsOnMessages(long userId, long messageId, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<string> apiResponse = new();
            var messageDetails = await _userDAL.GetWhatsOnMessagesById(baseHttpRequestContext.OrgId, userId, messageId);
            if (messageDetails != null)
            {
                messageDetails.StatusId = (short)Status.Deleted;
                messageDetails.ModifiedBy = baseHttpRequestContext.UserId;
                messageDetails.ModifiedDate = DateTime.UtcNow;
                messageDetails = await _userDAL.EditWhatsOnMessages(messageDetails);

                apiResponse.Result = "Delete Successful";
                apiResponse.Message = "Success";
                apiResponse.StatusCode = StatusCodes.Status200OK;
                return apiResponse;
            }
            else
            {
                apiResponse.Errors.Add("Whats-On message cannot be deleted at this time.");
                apiResponse.Message = "Failure";
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                return apiResponse;
            }
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="user_id"></param>
        /// <param name="participantId"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<UserHealthFundAssoc>> GetUserHealthHundAssocFromUserId(long user_id, string participantId, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<UserHealthFundAssoc> apiResponse = new();
            UserHealthFundAssoc userHealthFundAssoc = await _userDAL.GetUserHealthHundAssocFromUserId(user_id, participantId, baseHttpRequestContext);

            if (!string.IsNullOrWhiteSpace(participantId))
            {
                apiResponse.Result = userHealthFundAssoc;
                apiResponse.StatusCode = StatusCodes.Status200OK;
                return apiResponse;

            }

            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            apiResponse.Errors.Add("Error while fetching User Health Fund Association");
            return apiResponse;
        }

        public async Task<ApiResponse<List<HIServiceDetailLogs>>> GetHIServiceDetails(BaseHttpRequestContext baseHttpRequestContext, long id)
        {
            ApiResponse<List<HIServiceDetailLogs>> apiResponse = new();
            ICollection<HIServiceDetail> hpiiServiceDetails = await _userDAL.GetHIServiceDetails(baseHttpRequestContext.OrgId, id);
            List<HIServiceDetailLogs> hpiiServiceDetailList = _mapper.Map<List<HIServiceDetailLogs>>(hpiiServiceDetails);
            foreach (var item in hpiiServiceDetails)
            {
                var matchingDetail = hpiiServiceDetailList.FirstOrDefault(x => x.Id == item.Id);
                if (matchingDetail != null)
                {
                    matchingDetail.Response = JsonConvert.DeserializeObject<List<HIServiceDetailResponse>>(item.HIResponse);
                }
            }

            apiResponse.Result = hpiiServiceDetailList;
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            return apiResponse;
        }


        public async Task<ApiResponse<long?>> AddHIServiceDetail(BaseHttpRequestContext baseHttpRequestContext, long userDetailId, InputHIServiceDetails inputHIServiceDetail)
        {
            var orgId = baseHttpRequestContext.OrgId;
            var userId = baseHttpRequestContext.UserId;

            // Check for existing HPI-I number
            var isHPIINumberExist = await _userDAL.GetUserDetailsByHPIINumber(orgId, inputHIServiceDetail.HPIINumber);
            if (isHPIINumberExist?.Any(x => x.Id != userDetailId) == true)
            {
                var existingUser = isHPIINumberExist.First(x => x.Id != userDetailId);
                var validationMsg = $"HPIINumber ({inputHIServiceDetail.HPIINumber}) is already associated with another user - {existingUser.SurName}, {existingUser.FirstName} || ID: {existingUser.Id}.";

                HIServiceDetail hIServiceDetail = new HIServiceDetail
                {
                    UserDetailsId = userDetailId,
                    OrgId = orgId,
                    HIServiceTypeId = (short)HIServiceType.HPI_Individual,
                    HISearchTypeId = inputHIServiceDetail.HISearchTypeId,
                    HPIINumber = inputHIServiceDetail.HPIINumber,
                    HIRecordStatusId = 0, 
                    HIStatusId = 0,       
                    VerificationMessage = "Verification failed for the given HPI-I number",
                    HIResponse = "null",
                    CreatedDate = DateTime.UtcNow,
                    CreatedBy = userId
                };

                DateTime.SpecifyKind(hIServiceDetail.CreatedDate, DateTimeKind.Utc);
                await _userDAL.AddHIServiceDetail(hIServiceDetail);

                return new ApiResponse<long?>
                {
                    StatusCode = StatusCodes.Status200OK,
                    Message = "Failure",
                    Result = null,
                    Errors = new List<string> { validationMsg, hIServiceDetail.VerificationMessage }
                };
            }

            // Normal flow if no duplicate
            HIServiceDetail hIServiceDetailNormal = new HIServiceDetail
            {
                UserDetailsId = userDetailId,
                OrgId = orgId,
                HIServiceTypeId = (short)HIServiceType.HPI_Individual,
                HISearchTypeId = inputHIServiceDetail.HISearchTypeId,
                HPIINumber = inputHIServiceDetail.HPIINumber,
                HIRecordStatusId = inputHIServiceDetail.HIRecordStatusId,
                HIStatusId = inputHIServiceDetail.HIStatusId,
                VerificationMessage = inputHIServiceDetail.VerificationMessage,
                HIResponse = JsonConvert.SerializeObject(inputHIServiceDetail.Response),
                CreatedDate = DateTime.UtcNow,
                CreatedBy = userId
            };

            DateTime.SpecifyKind(hIServiceDetailNormal.CreatedDate, DateTimeKind.Utc);
            var id = await _userDAL.AddHIServiceDetail(hIServiceDetailNormal);
            if (id > 0)
            {
                ApiResponse<long?> apiResponse = new()
                {
                    StatusCode = StatusCodes.Status200OK,
                    Message = "Success",
                    Result = id
                };
                return apiResponse;
            }
            else
            {
                ApiResponse<long?> apiResponse = new()
                {
                    StatusCode = StatusCodes.Status400BadRequest,
                    Message = "Failure",
                    Result = null
                };
                return apiResponse;
            }
        }

        public async Task<ApiResponse<string>> EditHIServiceDetail(BaseHttpRequestContext baseHttpRequestContext, long userDetailId, long hiservicedetailId, InputHIServiceDetails inputHIServiceDetail)
        {
            ApiResponse<string> apiResponse = new();
            var orgId = baseHttpRequestContext.OrgId;
            var userId = baseHttpRequestContext.UserId;

            List<HIServiceDetail> hpiiDetails = new();

            var existingihiDetails = await _userDAL.GetExistingHIServiceDetailById(hiservicedetailId);
            existingihiDetails.ModifiedBy = userId;
            existingihiDetails.ModifiedDate = DateTime.UtcNow;
            if (existingihiDetails.HIRecordStatusId == (short)HIServiceStatusType.Verified)
                existingihiDetails.HIRecordStatusId = (short)HIServiceStatusType.Retired;
            if (existingihiDetails.HIStatusId == (short)HIServiceStatusType.Active)
                existingihiDetails.HIStatusId = (short)HIServiceStatusType.Deactivated;
            hpiiDetails.Add(existingihiDetails);

            HIServiceDetail hIServiceDetail = new HIServiceDetail
            {
                UserDetailsId = userDetailId,
                OrgId = orgId,
                HIServiceTypeId = (short)HIServiceType.HPI_Individual,
                HISearchTypeId = inputHIServiceDetail.HISearchTypeId,
                HPIINumber = inputHIServiceDetail.HPIINumber,
                HIRecordStatusId = inputHIServiceDetail.HIRecordStatusId,
                HIStatusId = inputHIServiceDetail.HIStatusId,
                VerificationMessage = inputHIServiceDetail.VerificationMessage,
                HIResponse = JsonConvert.SerializeObject(inputHIServiceDetail.Response),
                CreatedDate = DateTime.UtcNow,
                CreatedBy = userId,
                ModifiedDate = DateTime.UtcNow,
                ModifiedBy = userId
            };

            hpiiDetails.Add(hIServiceDetail);

            await _userDAL.UpdateHIServiceDetail(hpiiDetails);

            apiResponse.Result = "Success";
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            return apiResponse;
        }
    }
}
