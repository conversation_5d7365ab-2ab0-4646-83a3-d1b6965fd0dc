﻿using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.User.Context;
using Capstone2.RestServices.User.Interfaces;
using Capstone2.RestServices.User.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;


namespace Capstone2.RestServices.User.Services
{
    public class IncomingLetterActionsDAL : IIncomingLetterActionsDAL
    {
        public readonly AppSettings _appSettings;
        public readonly ReadOnlyUserDBContext _readOnlyDbContext;
        public UpdatableUserDBContext _updatableDBContext;

        public IncomingLetterActionsDAL(IOptions<AppSettings> appSettings, ReadOnlyUserDBContext readOnlyDbContext, UpdatableUserDBContext updatableDBContext)
        {
            _appSettings = appSettings.Value;
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
        }

        public async Task<IncomingLetterActions> AddIncomingLetterAction(IncomingLetterActions incomingLetterAction)
        {
            // Retrieve the existing entity from the database
            try
            {
                var existing = await _readOnlyDbContext.IncomingLetterActions
               .FirstOrDefaultAsync(x => x.IncomingLetterId == incomingLetterAction.IncomingLetterId && x.OrgId == incomingLetterAction.OrgId);

                if (existing == null)
                {
                    // Add new record
                    await _updatableDBContext.IncomingLetterActions.AddAsync(incomingLetterAction);
                }
                else
                {
                    // Update existing record
                    existing.ActionTypeId = incomingLetterAction.ActionTypeId;
                    existing.ActionId = incomingLetterAction.ActionId;
                    existing.IsNoAction = incomingLetterAction.IsNoAction;
                    existing.IsNoActionCompleted = incomingLetterAction.IsNoActionCompleted;
                    existing.NoActionUserIds = incomingLetterAction.NoActionUserIds;
                    existing.IsTriageLetter = incomingLetterAction.IsTriageLetter;
                    existing.IsTriageLetterCompleted = incomingLetterAction.IsTriageLetterCompleted;
                    existing.TriageLetterUserIds = incomingLetterAction.TriageLetterUserIds;
                    existing.IsBookUrgentAppointment = incomingLetterAction.IsBookUrgentAppointment;
                    existing.IsBookUrgentAppointmentCompleted = incomingLetterAction.IsBookUrgentAppointmentCompleted;
                    existing.BookUrgentAppointmentUserIds = incomingLetterAction.BookUrgentAppointmentUserIds;
                    existing.IsBookNotUrgentAppointment = incomingLetterAction.IsBookNotUrgentAppointment;
                    existing.IsBookNotUrgentAppointmentCompleted = incomingLetterAction.IsBookNotUrgentAppointmentCompleted;
                    existing.BookNotUrgentAppointmentUserIds = incomingLetterAction.BookNotUrgentAppointmentUserIds;
                    existing.IsCallPatient = incomingLetterAction.IsCallPatient;
                    existing.IsCallPatientCompleted = incomingLetterAction.IsCallPatientCompleted;
                    existing.CallPatientUserIds = incomingLetterAction.CallPatientUserIds;
                    existing.IsReceived = incomingLetterAction.IsReceived;
                    existing.StatusId = incomingLetterAction.StatusId;
                    existing.DueDate = incomingLetterAction.DueDate;
                    existing.ModifiedDate = DateTime.UtcNow;
                    existing.ModifiedBy = incomingLetterAction.ModifiedBy;

                    _updatableDBContext.IncomingLetterActions.Update(existing);
                }
                bool IsNoAction = incomingLetterAction.IsNoAction ?? false;
                bool IsTriageLetterCompleted = incomingLetterAction.IsTriageLetterCompleted ?? false;
                bool IsBookUrgentAppointmentCompleted = incomingLetterAction.IsBookUrgentAppointmentCompleted ?? false;
                bool IsBookNotUrgentAppointmentCompleted = incomingLetterAction.IsBookNotUrgentAppointmentCompleted ?? false;
                bool IsCallPatientCompleted = incomingLetterAction.IsCallPatientCompleted ?? false;
                if (IsNoAction)
                {
                    var existingILA = await _readOnlyDbContext.IncomingLetterPatientActions.FirstOrDefaultAsync(x => x.IncomingLetterId == incomingLetterAction.IncomingLetterId && x.OrgId == incomingLetterAction.OrgId);
                    if(existingILA != null)
                    {
                        existingILA.StatusId = (short)Status.Deleted;
                        _updatableDBContext.IncomingLetterPatientActions.Update(existingILA);
                    }
                }
                else
                {
                    if (IsTriageLetterCompleted)
                    {
                        var existingILATriage = await _readOnlyDbContext.IncomingLetterPatientActions.FirstOrDefaultAsync(x => x.IncomingLetterId == incomingLetterAction.IncomingLetterId && x.OrgId == incomingLetterAction.OrgId && x.ActionId == (short)IncomingLetterActionsEnum.Triage_Letter);
                        if (existingILATriage != null)
                        {
                            existingILATriage.StatusId = (short)Status.Deleted;
                            _updatableDBContext.IncomingLetterPatientActions.Update(existingILATriage);
                        }
                    }
                    if (IsBookUrgentAppointmentCompleted)
                    {
                        var existingILABU = await _readOnlyDbContext.IncomingLetterPatientActions.FirstOrDefaultAsync(x => x.IncomingLetterId == incomingLetterAction.IncomingLetterId && x.OrgId == incomingLetterAction.OrgId && x.ActionId == (short)IncomingLetterActionsEnum.Book_Urgent_Appointment);
                        if (existingILABU != null)
                        {
                            existingILABU.StatusId = (short)Status.Deleted;
                            _updatableDBContext.IncomingLetterPatientActions.Update(existingILABU);
                        }
                    }
                    if (IsBookNotUrgentAppointmentCompleted)
                    {
                        var existingILABNU = await _readOnlyDbContext.IncomingLetterPatientActions.FirstOrDefaultAsync(x => x.IncomingLetterId == incomingLetterAction.IncomingLetterId && x.OrgId == incomingLetterAction.OrgId && x.ActionId == (short)IncomingLetterActionsEnum.Book_Non_Urgent_Appointment);
                        if (existingILABNU != null)
                        {
                            existingILABNU.StatusId = (short)Status.Deleted;
                            _updatableDBContext.IncomingLetterPatientActions.Update(existingILABNU);
                        }
                    }
                    if (IsCallPatientCompleted)
                    {
                        var existingILACallPatient = await _readOnlyDbContext.IncomingLetterPatientActions.FirstOrDefaultAsync(x => x.IncomingLetterId == incomingLetterAction.IncomingLetterId && x.OrgId == incomingLetterAction.OrgId && x.ActionId == (short)IncomingLetterActionsEnum.Call_patient_and_Advise);
                        if (existingILACallPatient != null)
                        {
                            existingILACallPatient.StatusId = (short)Status.Deleted;
                            _updatableDBContext.IncomingLetterPatientActions.Update(existingILACallPatient);
                        }
                    }
                    // Mapping of flags to corresponding ActionIds
                    //                var actionsToCheck = new Dictionary<bool, short>
                    //{
                    //                                        { IsTriageLetterCompleted, (short)IncomingLetterActionsEnum.Triage_Letter },
                    //                                        { IsBookUrgentAppointmentCompleted, (short)IncomingLetterActionsEnum.Book_Urgent_Appointment },
                    //                                        { IsBookNotUrgentAppointmentCompleted, (short)IncomingLetterActionsEnum.Book_Non_Urgent_Appointment },
                    //                                        { IsCallPatientCompleted, (short)IncomingLetterActionsEnum.Call_patient_and_Advise }
                    //};

                    //                foreach (var action in actionsToCheck)
                    //                {
                    //                    if (action.Key)
                    //                    {
                    //                        var existingILA = await _readOnlyDbContext.IncomingLetterPatientActions
                    //                            .FirstOrDefaultAsync(x => x.IncomingLetterId == incomingLetterAction.IncomingLetterId &&
                    //                                                      x.OrgId == incomingLetterAction.OrgId &&
                    //                                                      x.ActionId == action.Value);
                    //                        if (existingILA != null)
                    //                        {
                    //                            existingILA.StatusId = (short)Status.Deleted;
                    //                            _updatableDBContext.IncomingLetterPatientActions.Update(existingILA);
                    //                        }
                    //                    }
                    //                }
                }
            }
            catch(Exception ex)
            {
                throw ex;
            }
           

            await _updatableDBContext.SaveChangesAsync();
            return incomingLetterAction;

        }

        public async Task<IncomingLetterActions> GetIncomingLetterActionById(long id, int orgId)
        {
            return await _readOnlyDbContext.IncomingLetterActions.Where(x => x.IncomingLetterId == id && x.OrgId == orgId).FirstOrDefaultAsync();
        }

        /// <summary>
        /// GetIncomingLetterActions
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task<List<IncomingLetterActionsView>> GetIncomingLetterActions(int orgId, QueryModel queryModel, PatientActionsFilterModel filterModel)
        {
            List<long> pafilter = null;
            if (filterModel.AssignedToUserIds is not null && filterModel.AssignedToUserIds.Any())
            {
                pafilter = await FetchIncomingLetterActionsListForUser(filterModel.AssignedToUserIds, orgId);
            }

            var query = from PA in _readOnlyDbContext.IncomingLetterActions
                        where PA.OrgId == orgId && PA.StatusId != (short)Status.Deleted
                        select new IncomingLetterActionsView
                        {
                            Id = PA.Id,
                            OrgId = PA.OrgId,
                            ActionTypeId = PA.ActionTypeId,
                            ActionId = PA.ActionId,
                            IncomingLetterId = PA.IncomingLetterId,
                            DueDate = PA.DueDate,
                            StatusId = PA.StatusId,
                            PatientDetailsId = PA.PatientDetailsId,
                            CreatedBy = PA.CreatedBy,
                            CreatedDate = PA.CreatedDate,
                            ModifiedDate = PA.ModifiedDate,
                            ModifiedBy = PA.ModifiedBy,
                            IsNoAction = PA.IsNoAction,
                            IsNoActionCompleted = PA.IsNoActionCompleted,
                            NoActionUserIds = PA.NoActionUserIds,
                            IsBookNotUrgentAppointment = PA.IsBookNotUrgentAppointment,
                            IsBookNotUrgentAppointmentCompleted = PA.IsBookUrgentAppointmentCompleted,
                            BookNotUrgentAppointmentUserIds = PA.BookNotUrgentAppointmentUserIds,
                            IsTriageLetter = PA.IsTriageLetter,
                            IsTriageLetterCompleted = PA.IsTriageLetterCompleted,
                            TriageLetterUserIds = PA.TriageLetterUserIds,
                            IsBookUrgentAppointmentCompleted = PA.IsBookUrgentAppointmentCompleted,
                            BookUrgentAppointmentUserIds = PA.BookUrgentAppointmentUserIds,
                            IsCallPatient = PA.IsCallPatient,
                            IsCallPatientCompleted = PA.IsCallPatientCompleted,
                            IsReceived = PA.IsReceived,
                            PatientDetails = (PA.PatientDetailsId != 0) ?
                                (
                                 from PD in _readOnlyDbContext.PatientDetails
                                 where PD.Id == PA.PatientDetailsId
                                 select new PatientDetailInfo
                                 {
                                     Id = PD.Id,
                                     FirstName = PD.FirstName,
                                     SurName = PD.SurName,
                                     Mobile = PD.Mobile
                                 }).FirstOrDefault() : null,

                        };

            var queryList = query.ToList();
            return queryList;
        }

        /// <summary>
        /// EditMultiIncomingLetterPatientActions
        /// </summary>
        /// <param name="incomingLetterActions"></param>
        /// <returns></returns>
        public async Task<int> EditMultiIncomingLetterPatientActions(List<IncomingLetterActions> incomingLetterActions)
        {
            _updatableDBContext.IncomingLetterActions.UpdateRange(incomingLetterActions);
            return await _updatableDBContext.SaveChangesAsync();
        }

        public async Task<List<long>> FetchIncomingLetterActionsListForUser(List<long?> userIds, int orgId)
        {
            return await _readOnlyDbContext.IncomingLetterPatientActionsUserAssocs.Where(x => userIds.Contains(x.UserDetailsId)
            && x.OrgId == orgId && x.StatusId == (short)Status.Active).Select(y => y.IncomingLetterPatientActionsId).ToListAsync();
        }

        public async Task<List<IncomingLetterActions>> GetIncomingLetterActionsByOrgId(int orgId)
        {
            return await _readOnlyDbContext.IncomingLetterActions
                .Where(x => x.OrgId == orgId)
                .ToListAsync();
        }

        public async Task<int> EditIncomingLetterAction(IncomingLetterActions incomingLetterAction)
        {
            _updatableDBContext.IncomingLetterActions.Update(incomingLetterAction);
            return await _updatableDBContext.SaveChangesAsync();
        }

        public async Task<int> DeleteIncomingLetterAction(long id)
        {
            var entity = await _updatableDBContext.IncomingLetterActions.Where(x => x.IncomingLetterId == id).FirstOrDefaultAsync();
            if (entity != null)
            {
                _updatableDBContext.IncomingLetterActions.Remove(entity);
                return await _updatableDBContext.SaveChangesAsync();
            }
            return 0;
        }

        public async Task<List<UserDetailInfo>> FetchUserDetailsFromIds(List<long> listIds, int orgId)
        {
            return await _readOnlyDbContext.UserDetails.Where(x => listIds.Contains(x.Id) && x.OrgId == orgId).Select(x => new UserDetailInfo
            {
                Id = x.Id,
                FirstName = x.FirstName,
                SurName = x.SurName,
                TitleId = x.TitleId,
                FileDetailsOutput = null
            }).ToListAsync();
        }

        public async Task<List<PatientDetailInfo>> FetchPatinetDetailsFromIds(List<long> listIds, int orgId)
        {
            return await _readOnlyDbContext.PatientDetails.Where(x => listIds.Contains(x.Id) && x.OrgId == orgId).Select(x => new PatientDetailInfo
            {
                Id = x.Id,
                FirstName = x.FirstName,
                SurName = x.SurName,

            }).ToListAsync();
        }

        public async Task<List<IncomingLetterPatientAction>> GetIncomingLetterPatientActionById(long id, int orgId)
        {
            return await _readOnlyDbContext.IncomingLetterPatientActions.Where(x => x.IncomingLetterId == id && x.OrgId == orgId).ToListAsync();
            //var query = from ILPA in _readOnlyDbContext.IncomingLetterPatientActions
            //            where ILPA.IncomingLetterId == id
            //            select new IncomingLetterPatientActionView
            //            {
            //                Id = ILPA.Id,
            //                OrgId = ILPA.OrgId,
            //                IncomingLetterId = ILPA.IncomingLetterId,
            //                ActionTypeId = ILPA.ActionTypeId,
            //                ActionId = ILPA.ActionId,
            //                AssigneeId = ILPA.AssigneeId,
            //                DueDate = ILPA.DueDate,
            //                StatusId = ILPA.StatusId,
            //                IsUrgent = ILPA.IsUrgent,
            //                IsClosed = ILPA.IsClosed,
            //                IsMediaUploaded = ILPA.IsMediaUploaded,
            //                ActivityLogId = ILPA.ActivityLogId,
            //                PatientDetailsId = ILPA.PatientDetailsId,
            //                CreatedBy = ILPA.CreatedBy,
            //                CreatedDate = ILPA.CreatedDate,
            //                ModifiedDate = ILPA.ModifiedDate,
            //                ModifiedBy = ILPA.ModifiedBy,
            //                Tags = ILPA.Tags,
            //                Notes = ILPA.Notes,
            //                PatientDetails = (ILPA.PatientDetailsId != null) ?
            //                    (
            //                     from PD in _readOnlyDbContext.PatientDetails
            //                     where PD.Id == ILPA.PatientDetailsId
            //                     select new PatientDetailInfo
            //                     {
            //                         Id = PD.Id,
            //                         FirstName = PD.FirstName,
            //                         SurName = PD.SurName,
            //                         Mobile = PD.Mobile
            //                     }).FirstOrDefault() : null,
            //            };
            //return await query.FirstOrDefaultAsync();
        }

        /// <summary>
        /// EditMultiPatientActions
        /// </summary>
        /// <param name="IncomingLetterActions"></param>
        /// <returns></returns>
        public async Task<int> EditMultiIncomingLetterActions(List<IncomingLetterPatientAction> actions)
        {
            _updatableDBContext.IncomingLetterPatientActions.UpdateRange(actions);
            return await _updatableDBContext.SaveChangesAsync();
        }

        /// <summary>
        /// EditMultipleINLUserAssoc
        /// </summary>
        /// <param name="IncomingLetterActions"></param>
        /// <returns></returns>
        //public async Task<int> EditMultipleINLUserAssoc(List<IncomingLetterPatientActionUserAssoc> actions)
        //{
        //    _updatableDBContext.IncomingLetterPatientActionUserAssoc.UpdateRange(actions);
        //    return await _updatableDBContext.SaveChangesAsync();
        //}

        public async Task<List<IncomingLetterPatientAction>> GetIncomingLetterPatientActionByIdList(long id, int orgId)
        {
            return _readOnlyDbContext.IncomingLetterPatientActions.Where(x => x.IncomingLetterId == id && x.OrgId == orgId).ToList();
        }

        public async Task<string> GetUserDetailsFromCommaSeparatedIdsAsync(string commaSeparatedUserIds)
        {
            if (string.IsNullOrEmpty(commaSeparatedUserIds))
            {
                return string.Empty;
            }

            var userIds = commaSeparatedUserIds.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                                               .Select(long.Parse)
                                               .ToList();
            try
            {
                var userDetailsList = await (from UD in _readOnlyDbContext.UserDetails
                                             where userIds.Contains(UD.Id)
                                             select new
                                             {
                                                 FullName = (UD.TitleId.HasValue ? EnumExtensions.GetDescription((TitleType)UD.TitleId.Value) + " " : string.Empty) + UD.FirstName + " " + UD.SurName
                                             }).ToListAsync();
                var commaSeparatedUserDetails = string.Join(",", userDetailsList.Select(ud => ud.FullName));
                return commaSeparatedUserDetails;
            }
            catch(Exception ex)
            {
                ex.ToString();
            }

            // Join the FullName into a comma-separated string
           

            return "";
        }

    }
}
