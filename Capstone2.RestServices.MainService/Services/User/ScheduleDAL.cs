﻿using AutoMapper;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.User.Context;
using Capstone2.RestServices.User.Interfaces;
using Capstone2.RestServices.User.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Enum;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Syncfusion.EJ2.Gantt;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.RestServices.User.Services
{
    public class ScheduleDAL : IScheduleDAL
    {
        public readonly AppSettings _appSettings;
        public readonly ReadOnlyUserDBContext _readOnlyDbContext;
        public UpdatableUserDBContext _updatableDBContext;
        public IMapper _mapper;
        public ScheduleDAL(IOptions<AppSettings> appSettings, ReadOnlyUserDBContext readOnlyDbContext, UpdatableUserDBContext updatableDBContext, IMapper mapper)
        {
            _appSettings = appSettings.Value;
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
            _mapper = mapper;
        }

        /// <summary>
        /// Method to add Schedule
        /// </summary>
        /// <param name="newSchedule"></param>
        /// <returns></returns>
        public async Task<long> AddScheduleDAL(Schedule newSchedule)
        {
            await _updatableDBContext.Schedules.AddAsync(newSchedule);
            int rows = await _updatableDBContext.SaveChangesAsync();
            if (rows > default(int))
                return newSchedule.Id;
            return 0;
        }

        public async Task<int> AddScheduleList(List<Schedule> newSchedulesList)
        {
            _updatableDBContext.Schedules.AddRange(newSchedulesList);
            int rows = await _updatableDBContext.SaveChangesAsync();
            if (rows > default(int))
                return rows;
            return 0;
        }

        public async Task<long> AddRecurrenceDAL(ScheduleRecurrence newRecurrrence)
        {
            await _updatableDBContext.ScheduleRecurrences.AddAsync(newRecurrrence);
            int rows = await _updatableDBContext.SaveChangesAsync();
            if (rows > default(int))
                return newRecurrrence.Id;
            return 0;
        }

        /// <summary>
        /// LIsting of schedules
        /// </summary>
        /// <param name="user_id"></param>
        /// <param name="orgId"></param>
        /// <param name="queryModel"></param>
        /// <param name="filterModel"></param>
        /// <returns></returns>
        public async Task<QueryResultList<ScheduleList>> FetchSchedules(int orgId, QueryModel queryModel, ScheduleFilter filterModel)
        {
            var query = from S in _readOnlyDbContext.Schedules
                        where S.OrgId == orgId && S.StatusId == (short)Status.Active //&& AU.UserStatusId == (short)UserStatus.Active
                        select new ScheduleList
                        {

                            Id = S.Id,
                            UserDetailsId = S.UserDetailsId,
                            OrgId = S.OrgId,
                            StartTime = S.StartTime,
                            EndTime = S.EndTime,
                            StartDate = S.StartDate,
                            EndDate = S.EndDate,
                            ScheduleRecurrencesId = S.ScheduleRecurrencesId,
                            AppointmentTypesId = S.AppointmentTypesId,
                            StatusId = S.StatusId,
                            CreatedDate = S.CreatedDate,
                            ModifiedDate = S.ModifiedDate,
                            ModifiedBy = S.ModifiedBy,
                            ScheduleNote = S.ScheduleNote,
                            AppointmentType = (from AP in _readOnlyDbContext.AppointmentTypes
                                               where AP.OrgId == orgId && AP.Id == S.AppointmentTypesId //&& AP.StatusId == (short)Status.Active
                                               select new AppointmentTypeOutput
                                               {
                                                   Id = AP.Id,
                                                   Category = AP.Category,
                                                   OrgId = AP.OrgId,
                                                   Colour = AP.Colour,
                                                   Type = AP.Type

                                               }).FirstOrDefault()

                        };

            if (filterModel is not null)
            {
                if (filterModel.AppointmentTypesId != null)
                {
                    query = query.Where(x => x.AppointmentTypesId == filterModel.AppointmentTypesId);

                }
                if (filterModel.UserDetailsId != null)
                {
                    query = query.Where(x => x.UserDetailsId == filterModel.UserDetailsId);

                }
                if (filterModel.StartDate != null)
                {
                    query = query.Where(x => x.StartDate >= filterModel.StartDate);

                }
                if (filterModel.EndDate != null)
                {
                    query = query.Where(x => x.EndDate <= filterModel.EndDate);

                }
            }

            if (!string.IsNullOrWhiteSpace(queryModel.SortOrder) && !string.IsNullOrWhiteSpace(queryModel.SortTerm))
            {
                query = SortSchedules(query, queryModel.SortOrder, queryModel.SortTerm);

            }
            List<ScheduleList> paginatedList = await CreatePaginatedProviderList(query, queryModel);
            QueryResultList<ScheduleList> queryList = new QueryResultList<ScheduleList>();
            if (paginatedList != null)
            {

                queryList.ItemRecords = paginatedList;
                queryList.PageNumber = queryModel.PageNumber;
                queryList.PageSize = queryModel.PageSize;
                queryList.CurrentCount = paginatedList.Count();

            }
            else
            {
                queryList.ItemRecords = null;
                queryList.CurrentCount = 0;

            }
            queryList.TotalCount = query.Count();
            queryList.PageNumber = queryModel.PageNumber;
            queryList.PageSize = queryModel.PageSize;
            return queryList;
        }
        private async Task<List<ScheduleList>> CreatePaginatedProviderList(IQueryable<ScheduleList> scheduleQuery, QueryModel queryModel)
        {
            if (queryModel.PageNumber > 0 && queryModel.PageSize > 0)
            {
                if (scheduleQuery.Any())
                {

                    List<ScheduleList> paginatedList = await scheduleQuery.Skip((queryModel.PageNumber - 1) * queryModel.PageSize)
                 .Take(queryModel.PageSize).ToListAsync();

                    return paginatedList;

                }

            }
            return null;
        }
        private IQueryable<ScheduleList> SortSchedules(IQueryable<ScheduleList> query, string sortOrder, string sortTerm)
        {
            switch (sortTerm.ToLower())
            {
                case "ModifiedDate":
                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                            query = query.OrderBy(x => x.ModifiedDate);
                        else
                            query = query.OrderByDescending(x => x.ModifiedDate);
                        break;
                    }
                case "StartDate":
                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                            query = query.OrderBy(x => x.StartDate);
                        else
                            query = query.OrderByDescending(x => x.StartDate);
                        break;
                    }
                case "EndDate":
                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                            query = query.OrderBy(x => x.EndDate);
                        else
                            query = query.OrderByDescending(x => x.EndDate);
                        break;
                    }
                default:
                    {
                        query = query.OrderByDescending(x => x.StartDate);
                        break;
                    }
            }
            return query;
        }

        public async Task<ScheduleView> GetScheduleDAL(long user_id, long id, int orgId)
        {
            var query = await (from S in _readOnlyDbContext.Schedules
                               where S.OrgId == orgId && S.UserDetailsId == user_id && S.StatusId == (short)Status.Active && S.Id == id
                               select new ScheduleView
                               {
                                   Id = S.Id,
                                   UserDetailsId = S.UserDetailsId,
                                   OrgId = S.OrgId,
                                   StartTime = S.StartTime,
                                   EndTime = S.EndTime,
                                   StartDate = S.StartDate,
                                   EndDate = S.EndDate,
                                   ScheduleRecurrencesId = S.ScheduleRecurrencesId,
                                   AppointmentTypesId = S.AppointmentTypesId,
                                   StatusId = S.StatusId,
                                   CreatedDate = S.CreatedDate,
                                   ModifiedDate = S.ModifiedDate,
                                   ModifiedBy = S.ModifiedBy,
                                   ScheduleNote = S.ScheduleNote,
                                   AppointmentType = (from AP in _readOnlyDbContext.AppointmentTypes
                                                      where AP.OrgId == orgId && AP.Id == S.AppointmentTypesId && AP.StatusId == (short)Status.Active
                                                      select new AppointmentTypeOutput
                                                      {
                                                          Id = AP.Id,
                                                          Category = AP.Category,
                                                          OrgId = AP.OrgId,
                                                          Colour = AP.Colour,
                                                          Type = AP.Type

                                                      }).FirstOrDefault(),
                                   ScheduleRecurrence = (from SR in _readOnlyDbContext.ScheduleRecurrences
                                                         where SR.OrgId == orgId && SR.Id == S.ScheduleRecurrencesId && S.StatusId == (short)Status.Active
                                                         select SR).FirstOrDefault()

                               }).FirstOrDefaultAsync();
            return query;
        }

        /// <summary>
        /// Fetching the schedules for a user within a timeframe
        /// </summary>
        /// <param name="user_id"></param>
        /// <param name="orgId"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        public async Task<List<Schedule>> FetchScheduleForTimeFrame(long user_id, int orgId, DateTime startDate, DateTime endDate)
        {
            var query = await (from S in _readOnlyDbContext.Schedules
                               where S.OrgId == orgId && S.UserDetailsId == user_id && S.StatusId == (short)Status.Active && (S.StartDate == startDate.Date && S.EndDate == endDate.Date)
                               select S).AsNoTracking().ToListAsync();
            return query;
        }
        public async Task<Schedule> GetScheduleOnlyDAL(long user_id, long id, int orgId)
        {
            return await (from S in _readOnlyDbContext.Schedules
                          where S.OrgId == orgId && S.UserDetailsId == user_id && S.StatusId == (short)Status.Active && S.Id == id
                          select S).FirstOrDefaultAsync();
        }
        /// <summary>
        /// Method to fetch a schedule
        /// </summary>
        /// <param name="user_id"></param>
        /// <param name="id"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<Schedule> GetSchedule(long user_id, long id, int orgId)
        {

            var query = (from S in _readOnlyDbContext.Schedules
                         where S.OrgId == orgId && S.UserDetailsId == user_id && S.StatusId == (short)Status.Active && S.Id == id
                         select S);

            var result = await query.AsNoTracking().FirstOrDefaultAsync();
            return result;

        }

        public async Task<int> UpdateSchedule(Schedule newSchedule)
        {
            _updatableDBContext.Schedules.Update(newSchedule);
            return await _updatableDBContext.SaveChangesAsync();
        }

        public async Task DeleteScheduleList(long recurrenceId, long loggedInUser)
        {
            var ScheduleList = await _updatableDBContext.Schedules.Where(x => x.ScheduleRecurrencesId == recurrenceId && x.StatusId == (short)Status.Active).ToListAsync();
            ScheduleList.ForEach(a =>
            {
                a.StatusId = (short)Status.Deleted;
                a.ModifiedBy = loggedInUser;
                a.ModifiedDate = DateTime.UtcNow;
            });
            await _updatableDBContext.SaveChangesAsync();
        }

        public async Task DeleteScheduleRecurrence(long recurrenceId, long loggedInUser, int OrgId)
        {
            var recurrence = await _updatableDBContext.ScheduleRecurrences.Where(x => x.Id == recurrenceId && x.StatusId == (short)Status.Active && x.OrgId == OrgId).FirstOrDefaultAsync();
            recurrence.StatusId = (short)Status.Deleted;
            recurrence.ModifiedBy = loggedInUser;
            recurrence.ModifiedDate = DateTime.UtcNow;
            await _updatableDBContext.SaveChangesAsync();
        }

        /// <summary>
        /// Method to fetch schedules based on list of Ids
        /// </summary>
        /// <param name="user_id"></param>
        /// <param name="orgId"></param>
        /// <param name="lstScheduleId"></param>
        /// <returns></returns>
        public async Task<List<Schedule>> FetchSchedulesFromIds(long user_id, int orgId, List<long> lstScheduleId)
        {
            return await _readOnlyDbContext.Schedules.Where(x => lstScheduleId.Contains(x.Id) && x.OrgId == orgId).AsNoTracking().ToListAsync();
        }

        /// <summary>
        /// Method to fetch Schedules from ScheduleRecurrencesId
        /// </summary>
        /// <param name="user_id"></param>
        /// <param name="orgId"></param>
        /// <param name="lstRecuId"></param>
        /// <returns></returns>
        public async Task<List<Schedule>> FetchSchedulesFromRecurranceIds(long user_id, int orgId, List<long?> lstRecuId)
        {
            return await _readOnlyDbContext.Schedules.Where(x => lstRecuId.Contains(x.ScheduleRecurrencesId) && x.OrgId == orgId).AsNoTracking().ToListAsync();
        }

        /// <summary>
        /// Method to fetch ScheduleRecurrance from ids
        /// </summary>
        /// <param name="lstRecuId"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<List<ScheduleRecurrence>> FetchScheduleRecurrancesFromIds(List<long?> lstRecuId, int orgId)
        {
            return await _readOnlyDbContext.ScheduleRecurrences.Where(x => lstRecuId.Contains((long)x.Id) && x.OrgId == orgId).AsNoTracking().ToListAsync();
        }

        public async Task<int> UpdateScheduleRange(List<Schedule> lstSchedule)
        {
            _updatableDBContext.Schedules.UpdateRange(lstSchedule);
            return await _updatableDBContext.SaveChangesAsync();
        }

        public async Task<int> UpdateScheduleRecurranceRange(List<ScheduleRecurrence> lstScheduleRecur)
        {
            _updatableDBContext.ScheduleRecurrences.UpdateRange(lstScheduleRecur);
            return await _updatableDBContext.SaveChangesAsync();
        }
    }
}
