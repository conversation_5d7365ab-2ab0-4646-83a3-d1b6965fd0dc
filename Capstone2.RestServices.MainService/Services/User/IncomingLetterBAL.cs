﻿using Capstone2.Framework.Business.Common;
using Capstone2.Framework.RestApi;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.User.Interfaces;
using Capstone2.RestServices.User.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.Azure.KeyVault.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.Serialization.Formatters.Binary;
using System.Security.Cryptography;
using System.Threading.Tasks;
using System.Transactions;

namespace Capstone2.RestServices.User.Services
{
    public class IncomingLetterBAL : IIncomingLetterBAL
    {
        public readonly AppSettings _appSettings;
        public readonly IIncomingLetterDAL _incomingLetterDAL;
        private readonly ILogger<IncomingLetterBAL> _logger;

        public IncomingLetterBAL(IOptions<AppSettings> appSettings, IIncomingLetterDAL incomingLetterDAL, ILogger<IncomingLetterBAL> logger)
        {
            _appSettings = appSettings.Value;
            _incomingLetterDAL = incomingLetterDAL;
            _logger = logger;
        }

        public async Task<ApiResponse<IncomingLetterPostView>> AddIncomingLetter(IncomingLetter incomingLetter, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<IncomingLetterPostView> apiResponse = new ApiResponse<IncomingLetterPostView>();
            incomingLetter.CreatedBy = baseHttpRequestContext.UserId;
            incomingLetter.CreatedDate = DateTime.UtcNow;

            var result = await _incomingLetterDAL.AddIncomingLetter(incomingLetter);

            apiResponse.Result = new IncomingLetterPostView
            {
                CreatedDate = result.CreatedDate,
                CreatedBy = result.CreatedBy,
                OrgId = result.OrgId,
                StatusId = result.StatusId,
                Id = result.Id,
                FileDetailsIds = result.FileDetailsIds
            };

            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            return apiResponse;
        }

        public async Task<ApiResponse<IncomingLetter>> GetIncomingLetterById(long id)
        {
            ApiResponse<IncomingLetter> apiResponse = new();
            var result = await _incomingLetterDAL.GetIncomingLetterById(id);
            if (result != null)
            {
                apiResponse.Result = result;
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status404NotFound;
                apiResponse.Message = "Incoming letter not found";
            }
            return apiResponse;
        }

        public async Task<ApiResponse<List<IncomingLetter>>> GetIncomingLettersByOrgId(int orgId)
        {
            ApiResponse<List<IncomingLetter>> apiResponse = new();
            var result = await _incomingLetterDAL.GetIncomingLettersByOrgId(orgId);
            apiResponse.Result = result;
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            return apiResponse;
        }

        public async Task<ApiResponse<long?>> EditIncomingLetter(IncomingLetter incomingLetter, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<long?> apiResponse = new();
            incomingLetter.ModifiedBy = baseHttpRequestContext.UserId;
            incomingLetter.ModifiedDate = DateTime.UtcNow;

            var result = await _incomingLetterDAL.EditIncomingLetter(incomingLetter);
            if (result > 0)
            {
                apiResponse.Result = incomingLetter.Id;
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failure";
            }
            return apiResponse;
        }

        public async Task<ApiResponse<long?>> DeleteIncomingLetter(long id)
        {
            ApiResponse<long?> apiResponse = new();
            var result = await _incomingLetterDAL.DeleteIncomingLetter(id);
            if (result > 0)
            {
                apiResponse.Result = id;
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failure";
            }
            return apiResponse;
        }
    }
}
