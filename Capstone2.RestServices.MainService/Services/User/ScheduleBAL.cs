﻿using System;
using System.Threading.Tasks;
using Capstone2.RestServices.User.Interfaces;
using Capstone2.RestServices.User.Models;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Capstone2.RestServices.MainService.Common;
using Microsoft.Extensions.Options;
using Capstone2.Shared.Models.Enum;
using AutoMapper;
using System.Collections.Generic;
using System.Transactions;
using Ical.Net.DataTypes;
using Ical.Net.CalendarComponents;
using Capstone2.Shared.Models.Common;
using System.Linq;
using Newtonsoft.Json;
using Microsoft.EntityFrameworkCore;
using Capstone2.Framework.RestApi.Dtos;

namespace Capstone2.RestServices.User.Services
{
    public class ScheduleBAL : IScheduleBAL
    {

        public readonly AppSettings _appSettings;
        public readonly IScheduleDAL _scheduleDAL;
        public IMapper _mapper;
        public ScheduleBAL(IOptions<AppSettings> appSettings, IScheduleDAL scheduleDAL, IMapper mapper)
        {
            _appSettings = appSettings.Value;
            _mapper = mapper;
            _scheduleDAL = scheduleDAL;
        }

        public async Task<ApiResponse<string>> AddScheduleBAL(long user_id, ScheduleInput inputSchedule, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<string> apiResponse = new ApiResponse<string>();

            //Condition to add the Schedule Recurrence
            if (inputSchedule.ScheduleRecurrenceObj is not null)
            {
                using (var transaction = new TransactionScope(TransactionScopeOption.Required, TransactionScopeAsyncFlowOption.Enabled))
                {
                    List<Schedule> overrideScheduleList = new();
                    var newSchedulesList = new List<Schedule>();
                    var eventStartTime = inputSchedule.ScheduleRecurrenceObj.RecurrenceStartTime;
                    var eventEndTime = inputSchedule.ScheduleRecurrenceObj.RecurrenceEndTime;
                    var eventStartDate = inputSchedule.ScheduleRecurrenceObj.RecurrenceStartDate.AddHours(eventStartTime.Hours).AddMinutes(eventStartTime.Minutes);
                    var eventStart = new CalDateTime(eventStartDate); //monday
                    var eventEndDate = inputSchedule.ScheduleRecurrenceObj.RecurrenceEndDate.AddHours(eventEndTime.Hours).AddMinutes(eventEndTime.Minutes); //sunday
                    var eventEnd = new CalDateTime(eventEndDate);//sunday

                    var vEvent = new ScheduleCalendarEvent
                    {
                        DtStart = eventStart,
                        DtEnd = eventEnd

                    };

                    vEvent.RecurrenceRules.Add(
                    // new RecurrencePattern("DTSTART:20220101T151800Z\nRRULE:FREQ=WEEKLY;UNTIL=20220409T151900Z;INTERVAL=1;WKST=MO;BYDAY=MO"));
                    new RecurrencePattern(inputSchedule.ScheduleRecurrenceObj.RruleStr));
                    var result = vEvent.GetOccurrences(eventStart, eventEnd);

                    //add schedule Recurrence Object to DB 
                    var newRecurrrence = inputSchedule.ScheduleRecurrenceObj;
                    newRecurrrence.Id = 0;
                    newRecurrrence.OrgId = baseHttpRequestContext.OrgId;
                    newRecurrrence.ModifiedBy = baseHttpRequestContext.UserId;
                    newRecurrrence.ModifiedDate = DateTime.UtcNow;
                    newRecurrrence.StatusId = (short)Status.Active;
                    newRecurrrence.CreatedDate = DateTime.UtcNow;
                    var recurenceToDB = _mapper.Map<ScheduleRecurrenceView, ScheduleRecurrence>(newRecurrrence);
                    var addedRecurrence = await _scheduleDAL.AddRecurrenceDAL(recurenceToDB);
                    if (addedRecurrence > 0)
                    {

                        foreach (var r in result)
                        {
                            var date = r.Period.StartTime.Date;
                            var newSchedule = new Schedule();
                            newSchedule.OrgId = baseHttpRequestContext.OrgId;
                            newSchedule.AppointmentTypesId = inputSchedule.ScheduleRecurrenceObj.AppointmentTypesId;
                            newSchedule.ScheduleRecurrencesId = addedRecurrence;
                            newSchedule.CreatedBy = baseHttpRequestContext.UserId;
                            //newSchedule.ModifiedBy = baseHttpRequestContext.UserId;
                            newSchedule.ModifiedDate = DateTime.UtcNow;
                            newSchedule.StatusId = (short)Status.Active;
                            newSchedule.CreatedDate = DateTime.UtcNow;
                            newSchedule.UserDetailsId = user_id;
                            newSchedule.StartDate = date;
                            newSchedule.EndDate = date;
                            newSchedule.StartTime = inputSchedule.ScheduleRecurrenceObj.RecurrenceStartTime;
                            newSchedule.EndTime = inputSchedule.ScheduleRecurrenceObj.RecurrenceEndTime;
                            newSchedule.ScheduleNote = inputSchedule.ScheduleRecurrenceObj.ScheduleNote;

                            newSchedulesList.Add(newSchedule);
                            List<Schedule> lstConflictSchedule = null;
                            if (inputSchedule.IsOverride is not null && inputSchedule.IsOverride == true)
                            {
                                lstConflictSchedule = await FetchConflictSchedule(newSchedule, baseHttpRequestContext, user_id);
                                lstConflictSchedule.ForEach(x =>
                                {
                                    x.DeleteReason = EnumExtensions.GetDescription(DeleteReason.SCHEDULE_OVERRIDE);
                                    x.ModifiedBy = baseHttpRequestContext.UserId;
                                    x.ModifiedDate = DateTime.UtcNow;
                                    x.StatusId = (short)Status.Deleted;
                                });
                                overrideScheduleList = overrideScheduleList.Union(lstConflictSchedule).ToList();

                            }
                        }

                        var addedSchedule = await _scheduleDAL.AddScheduleList(newSchedulesList);
                        if (overrideScheduleList is not null && overrideScheduleList.Count > 0)
                        {
                            await _scheduleDAL.UpdateScheduleRange(overrideScheduleList);
                        }
                    }

                    transaction.Complete();
                    transaction.Dispose();
                    apiResponse.Result = "Success";
                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    apiResponse.Message = "Schedule Added Successfully";

                }


            }
            else if (inputSchedule.ScheduleObj is not null)             //Condition to add the Schedule
            {
                var newSchedule = inputSchedule.ScheduleObj;
                newSchedule.OrgId = baseHttpRequestContext.OrgId;
                newSchedule.CreatedBy = baseHttpRequestContext.UserId;
                //newSchedule.ModifiedBy = baseHttpRequestContext.UserId;
                newSchedule.ModifiedDate = DateTime.UtcNow;
                newSchedule.StatusId = (short)Status.Active;
                newSchedule.CreatedDate = DateTime.UtcNow;
                newSchedule.UserDetailsId = user_id;

                List<Schedule> lstConflictSchedule = null;
                if (inputSchedule.IsOverride is not null && inputSchedule.IsOverride == true)
                {
                    lstConflictSchedule = await FetchConflictSchedule(newSchedule, baseHttpRequestContext, user_id);
                    lstConflictSchedule.ForEach(x =>
                    {
                        x.DeleteReason = EnumExtensions.GetDescription(DeleteReason.SCHEDULE_OVERRIDE);//"override";
                        x.ModifiedBy = baseHttpRequestContext.UserId;
                        x.ModifiedDate = DateTime.UtcNow;
                        x.StatusId = (short)Status.Deleted;
                    });
                }
                using (var transaction = new TransactionScope(TransactionScopeOption.Required, TransactionScopeAsyncFlowOption.Enabled))
                {
                    var addedSchedule = await _scheduleDAL.AddScheduleDAL(newSchedule);
                    if (lstConflictSchedule is not null && lstConflictSchedule.Count > 0)
                    {
                        await _scheduleDAL.UpdateScheduleRange(lstConflictSchedule);
                    }
                    if (addedSchedule == 0)
                    {
                        transaction.Dispose();

                        apiResponse.Result = "Failure";
                        apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                        apiResponse.Message = "Schedule Can not be added at this time";
                        return apiResponse;
                    }
                    apiResponse.Result = "Success";
                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    apiResponse.Message = "Schedule Added Successfully";
                    transaction.Complete();
                    transaction.Dispose();
                }

            }

            return apiResponse;

        }

        private async Task<List<Schedule>> FetchConflictSchedule(Schedule schedule, BaseHttpRequestContext baseHttpRequestContext, long user_id)
        {
            List<Schedule> lstConflictSchedule = null;
            DateTime startDate = schedule.StartDate;
            DateTime endDate = schedule.EndDate;
            var eventStartTime = schedule.StartTime;
            var eventEndTime = schedule.EndTime;
            schedule.StartDate = schedule.StartDate.AddHours(eventStartTime.Hours).AddMinutes(eventStartTime.Minutes);
            schedule.EndDate = schedule.EndDate.AddHours(eventEndTime.Hours).AddMinutes(eventEndTime.Minutes);
            List<Schedule> lstScheduleDB = await _scheduleDAL.FetchScheduleForTimeFrame(user_id, baseHttpRequestContext.OrgId, startDate, endDate);
            lstConflictSchedule = GetConflictSchedules(lstScheduleDB, schedule);


            return lstConflictSchedule;
        }

        private List<Schedule> GetConflictSchedules(List<Schedule> lstScheduleDB, Schedule schedule)
        {
            if (schedule.Id > 0)
                lstScheduleDB.Remove(lstScheduleDB.Find(x => x.Id == schedule.Id));
            List<Schedule> lstConflictSchedules = new();
            foreach (var scheduleDB in lstScheduleDB)
            {
                var startDateDB = scheduleDB.StartDate.AddHours(scheduleDB.StartTime.Hours).AddMinutes(scheduleDB.StartTime.Minutes);
                var endDateDB = scheduleDB.EndDate.AddHours(scheduleDB.EndTime.Hours).AddMinutes(scheduleDB.EndTime.Minutes);
                if ((schedule.StartDate >= startDateDB && schedule.StartDate < endDateDB) || (schedule.EndDate > startDateDB && schedule.EndDate <= endDateDB))
                {
                    lstConflictSchedules.Add(scheduleDB);
                }
                else if ((startDateDB >= schedule.StartDate && startDateDB < schedule.EndDate) || (endDateDB > schedule.StartDate && endDateDB <= schedule.EndDate))
                {
                    lstConflictSchedules.Add(scheduleDB);
                }
            };
            return lstConflictSchedules;

        }

        public async Task<ApiResponse<QueryResultList<ScheduleList>>> ListScheduleBAL(QueryModel queryModel, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<QueryResultList<ScheduleList>> apiResponse = new();
            ScheduleFilter filterModel = PrepareFilterParameters(queryModel.Filter);

            if (filterModel is null)
            {
                apiResponse.Errors.Add("Filter is needed to get the list of Schedules");
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failure";
                return apiResponse;
            }

            QueryResultList<ScheduleList> queryList = await _scheduleDAL.FetchSchedules(baseHttpRequestContext.OrgId, queryModel, filterModel);
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result = queryList;

            return apiResponse;
        }

        /// <summary>
        /// method to generate the filter parameters from the request
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        private ScheduleFilter PrepareFilterParameters(string filter)
        {
            if (!(String.IsNullOrEmpty(filter)))
            {
                return JsonConvert.DeserializeObject<ScheduleFilter>(filter);
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// method to get ScheduleView fron Schedule Id
        /// /// </summary>

        public async Task<ApiResponse<ScheduleView>> GetScheduleBAL(long user_id, long id, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<ScheduleView> apiResponse = new ApiResponse<ScheduleView>();
            var scheduleFromDB = await _scheduleDAL.GetScheduleDAL(user_id, id, baseHttpRequestContext.OrgId);
            if (scheduleFromDB is null)
            {
                apiResponse.Result = null;
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("The Schedule does not exist.");
                return apiResponse;
            }

            apiResponse.Result = scheduleFromDB;
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            return apiResponse;

        }
        /// <summary>
        /// method to delete schedules
        /// </summary>
        /// <param name="user_id"></param>
        /// <param name="recurrenceDelete"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<string>> DeleteSchedulesBAL(long user_id, RecurrenceDeleteObject recurrenceDelete, BaseHttpRequestContext baseHttpRequestContext)
        {
            long loggedInUser = baseHttpRequestContext.UserId;
            ApiResponse<string> apiResponse = new();
            if (recurrenceDelete is not null && recurrenceDelete.DeleteReason is not null && recurrenceDelete.Ids is not null)
            {
                List<long> lstIds = recurrenceDelete.Ids.Split(',').ToList().Select(s => long.Parse(s)).ToList();
                if (lstIds.Count > 0)
                {
                    List<Schedule> lstSchedule = await _scheduleDAL.FetchSchedulesFromIds(user_id, baseHttpRequestContext.OrgId, lstIds);
                    if (lstSchedule is not null && lstSchedule.Count > 0)
                    {
                        List<long?> lstRecurIds = null;
                        if (recurrenceDelete.isAllRecurrence is not null && recurrenceDelete.isAllRecurrence == true)
                        {
                            lstRecurIds = (List<long?>)lstSchedule.Where(x => x.ScheduleRecurrencesId != null).Select(x => x.ScheduleRecurrencesId).Distinct().ToList();
                            if (lstRecurIds is not null && lstRecurIds.Count > 0)
                            {
                                List<Schedule> lstScheduleNew = await _scheduleDAL.FetchSchedulesFromRecurranceIds(user_id, baseHttpRequestContext.OrgId, lstRecurIds);
                                if (lstScheduleNew.Count > 0)
                                {
                                    lstSchedule.AddRange(lstScheduleNew.Where(x => !(lstIds.Contains(x.Id))));
                                }
                            }
                        }
                        lstSchedule.ForEach(scheduleFromDB =>
                        {
                            scheduleFromDB.StatusId = (short)Status.Deleted;
                            scheduleFromDB.ModifiedBy = loggedInUser;
                            scheduleFromDB.ModifiedDate = DateTime.UtcNow;
                            scheduleFromDB.DeleteReason = recurrenceDelete.DeleteReason;
                        });
                        using (var transaction = new TransactionScope(TransactionScopeOption.Required, TransactionScopeAsyncFlowOption.Enabled))
                        {
                            int rows = await _scheduleDAL.UpdateScheduleRange(lstSchedule);
                            transaction.Complete();
                            transaction.Dispose();
                            if (rows > 0)
                            {
                                apiResponse.Result = "Delete Successful";
                                apiResponse.Message = "Success";
                                apiResponse.StatusCode = StatusCodes.Status200OK;
                                return apiResponse;
                            }
                        }
                    }
                }
            }

            apiResponse.Errors.Add("Schedules cannot be deleted at this time.");
            apiResponse.Message = "Failure";
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            return apiResponse;
        }

        /// <summary>
        /// method to Edit a schedule
        /// </summary>
        /// <param name="user_id"></param>
        /// <param name="id"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="inputSchedule"></param>
        /// <returns></returns>
        public async Task<ApiResponse<string>> UpdateSchedulesBAL(long user_id, long id, BaseHttpRequestContext baseHttpRequestContext, ScheduleInput inputSchedule)
        {
            ApiResponse<string> apiResponse = new();
            long loggedInUser = baseHttpRequestContext.UserId;
            List<Schedule> lstSchedulesUpd = new();
            List<Schedule> lstSchedules = new();

            var scheduleFromDb = await _scheduleDAL.GetSchedule(user_id, id, baseHttpRequestContext.OrgId);

            if (scheduleFromDb is null)
            {
                apiResponse.Errors.Add("Schedule cannot be edited");
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failure";
                return apiResponse;
            }
            //just updating the occurance
            if (inputSchedule is not null /*&& inputSchedule.IsRecurranceEdit == false*/)
            {
                if (inputSchedule.ScheduleObj is not null && inputSchedule.ScheduleRecurrenceObj is null)
                {
                    scheduleFromDb.StartDate = inputSchedule.ScheduleObj.StartDate;
                    scheduleFromDb.EndDate = inputSchedule.ScheduleObj.EndDate;
                    scheduleFromDb.StartTime = inputSchedule.ScheduleObj.StartTime;
                    scheduleFromDb.EndTime = inputSchedule.ScheduleObj.EndTime;
                    scheduleFromDb.AppointmentTypesId = inputSchedule.ScheduleObj.AppointmentTypesId;
                    scheduleFromDb.ModifiedBy = loggedInUser;
                    scheduleFromDb.ModifiedDate = DateTime.UtcNow;
                    scheduleFromDb.ScheduleNote = inputSchedule.ScheduleObj.ScheduleNote;

                    lstSchedulesUpd.Add(scheduleFromDb);
                    if (inputSchedule.IsOverride is not null && inputSchedule.IsOverride == true)
                    {
                        List<Schedule> lstConflictSchedule = await FetchConflictSchedule(scheduleFromDb, baseHttpRequestContext, user_id);
                        if (lstConflictSchedule is not null && lstConflictSchedule.Count > 0)
                        {
                            lstConflictSchedule.ForEach(x =>
                            {
                                x.ModifiedBy = loggedInUser;
                                x.ModifiedDate = DateTime.UtcNow;
                                x.StatusId = (short)Status.Deleted;
                                x.DeleteReason = EnumExtensions.GetDescription(DeleteReason.SCHEDULE_OVERRIDE);
                            });
                            lstSchedulesUpd = lstSchedulesUpd.Union(lstConflictSchedule).ToList(); ;

                        }
                    }
                    if (lstSchedulesUpd is not null && lstSchedulesUpd.Count > 0)
                    {

                        int rows = await _scheduleDAL.UpdateScheduleRange(lstSchedulesUpd);
                        apiResponse.Result = "Update Successful";
                        apiResponse.Message = "Success";
                        apiResponse.StatusCode = StatusCodes.Status200OK;
                        return apiResponse;
                    }

                }

                else if ( inputSchedule.ScheduleRecurrenceObj is not null)
                {
                    inputSchedule.ScheduleObj = null;

                    if (scheduleFromDb.ScheduleRecurrencesId is not null /*&& inputSchedule.IsRecurranceEdit is not null && inputSchedule.IsRecurranceEdit == true */&& inputSchedule.ScheduleRecurrenceObj is not null)
                    {
                        List<long?> lstRecurId = new List<long?> { scheduleFromDb.ScheduleRecurrencesId };
                        lstSchedulesUpd = await _scheduleDAL.FetchSchedulesFromRecurranceIds(user_id, baseHttpRequestContext.OrgId, lstRecurId);
                        lstSchedulesUpd.ForEach(x =>
                        {
                            x.ModifiedDate = DateTime.UtcNow;
                            x.ModifiedBy = loggedInUser;
                            x.StatusId = (short)Status.Deleted;
                            x.DeleteReason = EnumExtensions.GetDescription(DeleteReason.SCHEDULE_SERIES_EDIT);
                        });
                    }
                    else
                    {
                        scheduleFromDb.ModifiedDate = DateTime.UtcNow;
                        scheduleFromDb.ModifiedBy = loggedInUser;
                        scheduleFromDb.StatusId = (short)Status.Deleted;
                        scheduleFromDb.DeleteReason = EnumExtensions.GetDescription(DeleteReason.SCHEDULE_SERIES_EDIT);
                        lstSchedulesUpd.Add(scheduleFromDb);
                        //inputSchedule.ScheduleObj=new() { Id = scheduleFromDb.Id };
                    }
                    using (var transaction = new TransactionScope(TransactionScopeOption.Required, TransactionScopeAsyncFlowOption.Enabled))
                    {
                        if (lstSchedulesUpd is not null && lstSchedulesUpd.Count > 0)
                        {
                            int rows = await _scheduleDAL.UpdateScheduleRange(lstSchedulesUpd);

                        }

                        ApiResponse<string> apiResponseAdd = await AddScheduleBAL(user_id, inputSchedule, baseHttpRequestContext);
                        if (apiResponseAdd.StatusCode == StatusCodes.Status200OK)
                        {
                            transaction.Complete();
                            transaction.Dispose();

                            apiResponse.Result = "Update Successful";
                            apiResponse.Message = "Success";
                            apiResponse.StatusCode = StatusCodes.Status200OK;
                            return apiResponse;
                        }

                    }

                }

            }

            apiResponse.Result = "Schedule cannot be updated at this time.";
            apiResponse.Message = "Failure";
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            return apiResponse;
        }

        /// <summary>
        /// method to check for conflicts in user schedule
        /// </summary>
        /// <param name="user_id"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="inputSchedule"></param>
        /// <returns></returns>
        public async Task<ApiResponse<bool?>> VerifySchedules(long user_id, BaseHttpRequestContext baseHttpRequestContext, ScheduleInput inputSchedule)
        {
            DateTime startDate;
            DateTime endDate;
            bool? isConflict = false;
            List<Schedule> lstScheduleDB = new();
            if (inputSchedule.ScheduleObj is not null && inputSchedule.ScheduleRecurrenceObj is null)
            {
                startDate = inputSchedule.ScheduleObj.StartDate;
                endDate = inputSchedule.ScheduleObj.EndDate;
                var eventStartTime = inputSchedule.ScheduleObj.StartTime;
                var eventEndTime = inputSchedule.ScheduleObj.EndTime;
                inputSchedule.ScheduleObj.StartDate = inputSchedule.ScheduleObj.StartDate.AddHours(eventStartTime.Hours).AddMinutes(eventStartTime.Minutes);
                inputSchedule.ScheduleObj.EndDate = inputSchedule.ScheduleObj.EndDate.AddHours(eventEndTime.Hours).AddMinutes(eventEndTime.Minutes);
                lstScheduleDB.AddRange( await _scheduleDAL.FetchScheduleForTimeFrame(user_id, baseHttpRequestContext.OrgId, startDate, endDate));
                isConflict = IsScheduleAvailable(inputSchedule.ScheduleObj, lstScheduleDB);
                inputSchedule.ScheduleObj.ScheduleNote = inputSchedule.ScheduleObj.ScheduleNote;
            }
            if (inputSchedule.ScheduleRecurrenceObj is not null)
            {


                var newSchedulesList = new List<Schedule>();

                var eventStartTime = inputSchedule.ScheduleRecurrenceObj.RecurrenceStartTime;
                var eventEndTime = inputSchedule.ScheduleRecurrenceObj.RecurrenceEndTime;
                var eventStartDate = inputSchedule.ScheduleRecurrenceObj.RecurrenceStartDate.AddHours(eventStartTime.Hours).AddMinutes(eventStartTime.Minutes);
                var eventStart = new CalDateTime(eventStartDate);
                var eventEndDate = inputSchedule.ScheduleRecurrenceObj.RecurrenceEndDate.AddHours(eventEndTime.Hours).AddMinutes(eventEndTime.Minutes); //sunday
                var eventEnd = new CalDateTime(eventEndDate);

                var vEvent = new ScheduleCalendarEvent
                {
                    DtStart = eventStart,
                    DtEnd = eventEnd

                };

                vEvent.RecurrenceRules.Add(
                new RecurrencePattern(inputSchedule.ScheduleRecurrenceObj.RruleStr));
                var result = vEvent.GetOccurrences(eventStart, eventEnd);
                //lstScheduleDB.AddRange(await _scheduleDAL.FetchScheduleForTimeFrame(user_id, baseHttpRequestContext.OrgId, eventStartDate, eventEndDate));
                //in edit scenario - entires for the same recurrance series need not be part of the conflict list

                foreach (var r in result)
                {
                    var date = r.Period.StartTime.Date; ;
                    var newSchedule = new Schedule();

                    newSchedule.StartDate = date.AddHours(eventStartTime.Hours).AddMinutes(eventStartTime.Minutes);
                    newSchedule.EndDate = date.AddHours(eventEndTime.Hours).AddMinutes(eventEndTime.Minutes);
                    newSchedule.StartTime = inputSchedule.ScheduleRecurrenceObj.RecurrenceStartTime;
                    newSchedule.EndTime = inputSchedule.ScheduleRecurrenceObj.RecurrenceEndTime;
                    newSchedule.ScheduleRecurrencesId = inputSchedule.ScheduleRecurrenceObj.Id;
                    List<Schedule> lstSchedule = await _scheduleDAL.FetchScheduleForTimeFrame(user_id, baseHttpRequestContext.OrgId, newSchedule.StartDate, newSchedule.EndDate);
                    if(inputSchedule.ScheduleObj is not null)
                    {
                        lstSchedule.RemoveAll(x => x.Id == inputSchedule.ScheduleObj.Id);
                    }
                    if (inputSchedule.ScheduleRecurrenceObj.Id > 0)
                        lstSchedule.RemoveAll(x => x.ScheduleRecurrencesId == inputSchedule.ScheduleRecurrenceObj.Id);
                    isConflict = IsScheduleAvailable(newSchedule, lstSchedule);
                    if (isConflict is not null && isConflict == true)
                        break;

                }
            }
            ApiResponse<bool?> apiResponse = new();
            apiResponse.Result = isConflict;
            apiResponse.Message = "Success";
            apiResponse.StatusCode = StatusCodes.Status200OK;
            return apiResponse;
        }

        /// <summary>
        /// method to return true if there is a conflict in schedule
        /// </summary>
        /// <param name="scheduleObj"></param>
        /// <param name="lstScheduleDB"></param>
        /// <returns>bool</returns>
        private bool? IsScheduleAvailable(Schedule scheduleObj, List<Schedule> lstScheduleDB)
        {

            if (scheduleObj.Id > 0)
                lstScheduleDB.RemoveAll(x => x.Id == scheduleObj.Id);

            foreach (var scheduleDB in lstScheduleDB)
            {
                var startDateDB = scheduleDB.StartDate.AddHours(scheduleDB.StartTime.Hours).AddMinutes(scheduleDB.StartTime.Minutes);
                var endDateDB = scheduleDB.EndDate.AddHours(scheduleDB.EndTime.Hours).AddMinutes(scheduleDB.EndTime.Minutes);
                if ((scheduleObj.StartDate >= startDateDB && scheduleObj.StartDate < endDateDB) || (scheduleObj.EndDate > startDateDB && scheduleObj.EndDate <= endDateDB))
                {
                    return true;
                }
                else if ((startDateDB >= scheduleObj.StartDate && startDateDB < scheduleObj.EndDate) || (endDateDB > scheduleObj.StartDate && endDateDB <= scheduleObj.EndDate))
                {
                    return true;
                }
            };
            return false;
        }
    }




}

