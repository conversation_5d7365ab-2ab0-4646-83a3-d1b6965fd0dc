﻿using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.User.Context;
using Capstone2.RestServices.User.Interfaces;
using Capstone2.RestServices.User.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Capstone2.RestServices.User.Services
{
    public class IncomingLetterDAL : IIncomingLetterDAL
    {
        public readonly AppSettings _appSettings;
        public readonly ReadOnlyUserDBContext _readOnlyDbContext;
        public UpdatableUserDBContext _updatableDBContext;

        public IncomingLetterDAL(IOptions<AppSettings> appSettings, ReadOnlyUserDBContext readOnlyDbContext, UpdatableUserDBContext updatableDBContext)
        {
            _appSettings = appSettings.Value;
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
        }

        public async Task<IncomingLetter> AddIncomingLetter(IncomingLetter incomingLetter)
        {
            await _updatableDBContext.IncomingLetter.AddAsync(incomingLetter);
            await _updatableDBContext.SaveChangesAsync();
            return incomingLetter;
        }

        public async Task<IncomingLetter> GetIncomingLetterById(long id)
        {
            return await _readOnlyDbContext.IncomingLetter
                .FirstOrDefaultAsync(x => x.Id == id);
        }

        public async Task<List<IncomingLetter>> GetIncomingLettersByOrgId(int orgId)
        {
            return await _readOnlyDbContext.IncomingLetter
                .Where(x => x.OrgId == orgId)
                .ToListAsync();
        }

        public async Task<int> EditIncomingLetter(IncomingLetter incomingLetter)
        {
            _updatableDBContext.IncomingLetter.Update(incomingLetter);
            return await _updatableDBContext.SaveChangesAsync();
        }

        public async Task<int> DeleteIncomingLetter(long id)
        {
            var entity = await _updatableDBContext.IncomingLetter.FindAsync(id);
            if (entity != null)
            {
                _updatableDBContext.IncomingLetter.Remove(entity);
                return await _updatableDBContext.SaveChangesAsync();
            }
            return 0;
        }

    }
}
