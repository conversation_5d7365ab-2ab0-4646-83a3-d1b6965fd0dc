﻿using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.User.Context;
using Capstone2.RestServices.User.Interfaces;
using Capstone2.RestServices.User.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Microsoft.AspNetCore.Mvc.ApplicationModels;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Syncfusion.XlsIO.Implementation.Security;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Capstone2.RestServices.User.Services
{
    public class PatientActionsDAL : IPatientActionsDAL
    {
        public readonly AppSettings _appSettings;
        public readonly ReadOnlyUserDBContext _readOnlyDbContext;
        public UpdatableUserDBContext _updatableDBContext;
        public PatientActionsDAL(IOptions<AppSettings> appSettings, ReadOnlyUserDBContext readOnlyDbContext, UpdatableUserDBContext updatableDBContext)
        {
            _appSettings = appSettings.Value;
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
        }

        /// <summary>
        /// Method to create a new PatientActions
        /// </summary>
        /// <param name="action"></param>
        /// <returns></returns>
        public async Task<List<PatientAction>> AddPatientActions(List<PatientAction> actions)
        {
            await _updatableDBContext.PatientActions.AddRangeAsync(actions);
             await _updatableDBContext.SaveChangesAsync();
            return actions;
        }

        /// <summary>
        /// Method to fetch a permission
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="rid"></param>
        public async Task<RolesPermissionsAssoc> GetPermissionOnRoleId(int orgId, int rid, short pid)
        {
            var permissionobject = _readOnlyDbContext.RolesPermissionsAssocs.FirstOrDefault(x => x.RolesId == rid && x.StatusId == (short)Status.Active && x.OrgId == orgId && x.PermissionsId == pid);
            return permissionobject;
        }

        /// <summary>
        /// GetPatientActions
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task<QueryResultList<PatientActionsView>> GetPatientActions(long userId, int orgId, QueryModel queryModel, PatientActionsFilterModel filterModel)
        {
            List<long> pafilter = null;
            if(filterModel.AssignedToUserIds is not null && filterModel.AssignedToUserIds.Any())
            {
                pafilter = await  FetchPatintActionListForUser(filterModel.AssignedToUserIds, orgId);
            }

            var query = from PA in _readOnlyDbContext.PatientActions where PA.OrgId == orgId && PA.StatusId != (short)Status.Deleted
                        select new PatientActionsView
                        {
                            Id = PA.Id,
                            OrgId = PA.OrgId,
                            ActionTypeId = PA.ActionTypeId,
                            ActionId = PA.ActionId,
                            PathologyRequestId = PA.PathologyRequestId,
                            PathologyResponseMediaId = PA.PathologyResponseMediaId,
                            LetterId = PA.LetterId,
                            UserDetailsId = PA.UserDetailsId,
                            DueDate = PA.DueDate,
                            StatusId = PA.StatusId,
                            Description = PA.Description,
                            IsUrgent = PA.IsUrgent,
                            IsClosed = PA.IsClosed,
                            IsPending = PA.IsPending,
                            PatientDetailsId = PA.PatientDetailsId,
                            RequestedDate = PA.RequestedDate,
                            CreatedBy = PA.CreatedBy,
                            CreatedDate = PA.CreatedDate,
                            ModifiedDate = PA.ModifiedDate,
                            ModifiedBy = PA.ModifiedBy,
                            HiddenFor = PA.HiddenFor,
                            UserDetails = 
                                (from UD in _readOnlyDbContext.UserDetails where UD.Id == PA.UserDetailsId
                                 select new PatientActionsUserDetails
                                 {
                                    Id = UD.Id,
                                    Title = UD.TitleId,
                                    FirstName = UD.FirstName,
                                    SurName = UD.SurName
                                 }).FirstOrDefault(),
                            PatientActionUserAssocs = (from PAU in _readOnlyDbContext.PatientActionUserAssocs
                                                           where PAU.StatusId == (short)Status.Active && PAU.PatientActionsId == PA.Id
                                                           select new PatientActionUserAssocView
                                                           {
                                                               Id = PAU.Id,
                                                               OrgId = PAU.OrgId,
                                                               PatientActionsId = PAU.PatientActionsId,
                                                               StatusId = PAU.StatusId,
                                                               CreatedBy = PAU.CreatedBy,
                                                               UserDetailsId = PAU.UserDetailsId,
                                                               CreatedDate = PAU.CreatedDate,
                                                               ModifiedDate = PAU.ModifiedDate,
                                                               ModifiedBy = PAU.ModifiedBy,
                                                               UserDetailsInfo = (from UD in _readOnlyDbContext.UserDetails
                                                                                  where UD.Id == PAU.UserDetailsId
                                                                                  select new PatientActionsUserDetails
                                                                                  {
                                                                                      Id = UD.Id,
                                                                                      Title = UD.TitleId,
                                                                                      FirstName = UD.FirstName,
                                                                                      SurName = UD.SurName
                                                                                  }).FirstOrDefault()

                                                           }).ToList(),
                            TotalComments =
                                (short)(from PAC1 in _readOnlyDbContext.PatientActionComments
                                 where PAC1.PatientActionId == PA.Id
                                 select PAC1.Id).ToList().Count,

                            UnReadComments =
                                (short)(from PAC2 in _readOnlyDbContext.PatientActionComments
                                        where PAC2.PatientActionId == PA.Id && PAC2.CommentReadBy == null
                                        select PAC2.Id).ToList().Count,



                            PatientDetails = (PA.PatientDetailsId != null )?
                                (
                                 from PD in _readOnlyDbContext.PatientDetails
                                 where  PD.Id == PA.PatientDetailsId
                                 select new PatientDetailInfo
                                 {
                                     Id = PD.Id,
                                     FirstName = PD.FirstName,
                                     SurName = PD.SurName,
                                     Mobile =PD.Mobile
                                 }).FirstOrDefault():null,

                        };
            if (!filterModel.ShowHiddenActions)
            {
                query = query.Where(x => string.IsNullOrEmpty(x.HiddenFor) || !x.HiddenFor.Contains(userId.ToString()));
            }
            if (filterModel != null)
            {
                if (filterModel.StartDate != null)
                {
                    query = query.Where(x => x.DueDate >= filterModel.StartDate);
                }
                if (filterModel.EndDate != null)
                {
                    query = query.Where(x => x.DueDate <= filterModel.EndDate);
                }
                if (pafilter != null)
                {
                    query = query.Where(x => pafilter.Contains((long)x.Id));
                }
                if (filterModel.AssignedByUserIds != null)
                {
                    query = query.Where(x => filterModel.AssignedByUserIds.Contains(x.UserDetailsId));
                }
                if (filterModel.Status != null && filterModel.Status > 0)
                {
                    query = query.Where(x => x.StatusId == filterModel.Status);
                }
                if (filterModel.ActionTypeId != null)
                {
                    query = query.Where(x => x.ActionTypeId == filterModel.ActionTypeId);
                }
                if (filterModel.ActionIds != null)
                {
                    query = query.Where(x => filterModel.ActionIds.Contains(x.ActionId));
                }
                if (filterModel.IsClosed != null )
                {
                    query = query.Where(x => filterModel.IsClosed.Contains(x.IsClosed));
                }
                if (filterModel.IsPending != null )
                {
                    query = query.Where(x => filterModel.IsPending.Contains(x.IsPending));
                }
            }
            else
            {
                //decided to display all records irrespective of user
                //query = query.Where(x => x.AssigneeId == userId || x.UserDetailsId == userId);
            }

            if (!(string.IsNullOrWhiteSpace(queryModel.SearchTerm)))
            {
                //commenting now as design issue
                query = SearchPatientActions(query, queryModel.SearchTerm);
            }

            if (!string.IsNullOrWhiteSpace(queryModel.SortOrder) && !string.IsNullOrWhiteSpace(queryModel.SortTerm))
            {
                query = SortMyActions(query, queryModel.SortOrder, queryModel.SortTerm);
            }

            var paginatedList = await CreatePaginateList(query, queryModel);
            var queryList = new QueryResultList<PatientActionsView>();
            if (paginatedList != null)
            {
                queryList.ItemRecords = paginatedList;
                queryList.PageNumber = queryModel.PageNumber;
                queryList.PageSize = queryModel.PageSize;
                queryList.CurrentCount = paginatedList.Count();
            }
            else
            {
                queryList.ItemRecords = null;
                queryList.CurrentCount = 0;
            }
            queryList.TotalCount = query.Count();
            queryList.PageNumber = queryModel.PageNumber;
            queryList.PageSize = queryModel.PageSize;
            return queryList;
        }

        private IQueryable<PatientActionsView> SearchPatientActions(IQueryable<PatientActionsView> actionQuery, string searchTerm)
        {
            string filterString = string.Empty;
            bool istwoWordSearch = Regex.IsMatch(searchTerm, @"\s");

            if (!istwoWordSearch)
            {
                actionQuery = actionQuery.Where(s => s.Description.Contains(searchTerm));
            }
            else
            {
                string[] search = searchTerm.Split(" ");
                actionQuery = actionQuery.Where(s => s.Description.Contains(searchTerm));
            }
            return actionQuery;
        }
        private async Task<List<PatientActionsView>> CreatePaginateList(IQueryable<PatientActionsView> actionQuery, QueryModel queryModel)
        {
            if (queryModel.PageNumber > 0 && queryModel.PageSize > 0)
            {
                if (actionQuery.Any())
                {
                    var paginatedList = await actionQuery.Skip((queryModel.PageNumber - 1) * queryModel.PageSize).Take(queryModel.PageSize).ToListAsync();
                    return paginatedList;
                }
            }
            return null;
        }
        private IQueryable<PatientActionsView> SortMyActions(IQueryable<PatientActionsView> query, string sortOrder, string sortTerm)
        {
            switch (sortTerm.ToLower())
            {
                case "duedate":
                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            query = query.OrderBy(x => x.DueDate);
                        }
                        else
                        {
                            query = query.OrderByDescending(x => x.DueDate);
                        }
                        break;
                    }
                default:
                    {
                        query = query.OrderBy(x => x.DueDate);
                        break;
                    }
            }
            return query;
        }

        public List<PatientActionsUserDetails> GetUserDetailsFromCommaSeparatedIds(string commaSeparatedUserIds)
        {
            if (string.IsNullOrEmpty(commaSeparatedUserIds))
            {
                return new List<PatientActionsUserDetails>();
            }

            var userIds = commaSeparatedUserIds.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                                               .Select(long.Parse)
                                               .ToList();

            var userDetailsList = (from UD in _readOnlyDbContext.UserDetails
                                   where userIds.Contains(UD.Id)
                                   select new PatientActionsUserDetails
                                   {
                                       Id = UD.Id,
                                       Title = UD.TitleId,
                                       FirstName = UD.FirstName,
                                       SurName = UD.SurName
                                   }).ToList();

            return userDetailsList;
        }

        /// <summary>
        /// GetPatientActionsSyncById
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<PatientAction> GetPatientActionsSyncById(int orgId, long actionId)
        {
            return await _readOnlyDbContext.PatientActions
                .Where(s => s.Id == actionId && s.OrgId == orgId)
                .AsNoTracking()
                .FirstOrDefaultAsync();
        }

       

        /// <summary>
        /// GetPatientActionsSyncByMultipleIds
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="actionIds"></param>
        /// <returns></returns>
        public async Task<List<PatientAction>> GetPatientActionsSyncByMultipleIds(int orgId, List<long> actionIds)
        {
            return await _readOnlyDbContext.PatientActions
                .Where(s => actionIds.Contains((long)s.Id) && s.OrgId == orgId)
                .ToListAsync();
        }

        /// <summary>
        /// GetPatientActionsSyncByMediaId
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="mediaId"></param>
        /// <returns></returns>
        public async Task<List<PatientAction>> GetPatientActionsByMediaId(int orgId, long mediaId)
        {
            return await _readOnlyDbContext.PatientActions
                .Where(s => s.PathologyResponseMediaId == mediaId && s.OrgId == orgId)? // && s.StatusId == (short)Status.Active)?
                .ToListAsync();
        }

        /// <summary>
        /// GetPatientActionsSyncByMediaId
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="mediaId"></param>
        /// <returns></returns>
        public async Task<List<PatientAction>> GetPatientActionsByPathologyRequestId(int orgId, long pathologyRequestId)
        {
            return await _readOnlyDbContext.PatientActions
                .Where(s => s.PathologyRequestId == pathologyRequestId && s.OrgId == orgId)?
                .ToListAsync();
        }

        /// <summary>
        /// GetPatientActionsByLetterId
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="letterId"></param>
        /// <returns></returns>
        public async Task<List<PatientAction>> GetPatientActionsByLetterId(int orgId, long letterId)
        {
            return await _readOnlyDbContext.PatientActions
                .Where(s => s.LetterId == letterId && s.OrgId == orgId)?
                .ToListAsync();
        }

        /// <summary>
        /// EditPatientActions
        /// </summary>
        /// <param name="patientActions"></param>
        /// <returns></returns>
        public async Task<int> EditPatientActions(PatientAction patientActions)
        {
            _updatableDBContext.PatientActions.Update(patientActions);
            return await _updatableDBContext.SaveChangesAsync();
        }

        /// <summary>
        /// EditMultiPatientActions
        /// </summary>
        /// <param name="patientActions"></param>
        /// <returns></returns>
        public async Task<int> EditMultiPatientActions(List<PatientAction> patientActions)
        {
            _updatableDBContext.PatientActions.UpdateRange(patientActions);
            return await _updatableDBContext.SaveChangesAsync();
        }

        /// <summary>
        /// GetPatientActionsNotificationCount
        /// </summary>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<List<PatientActionsNotificationCount>> GetPatientActionsNotificationCount(int orgId)
        {
            var query =  from PA in _readOnlyDbContext.PatientActions
                         where PA.OrgId == orgId && PA.StatusId == (short)Status.Active
                         group PA by PA.ActionTypeId into g
                         select new PatientActionsNotificationCount
                         {
                             ActionTypeId = g.Key,
                             Count = g.Count()
                         };
            return await query.ToListAsync();
        }

        /// <summary>
        /// GetPatientActionsNotificationCountBuUserId
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task<List<PatientActionsNotificationCount>> GetPatientActionsNotificationCountByUserId(int orgId, long userId)
        {
            List<long?> userList = new()
            {
                userId
            };
            List<long> pafilter =   await FetchPatintActionListForUser(userList, orgId);

            var query = from PA in _readOnlyDbContext.PatientActions
                        where PA.OrgId == orgId && PA.StatusId == (short)Status.Active && pafilter.Contains((long)PA.Id) && PA.IsClosed == false
                        group PA by PA.ActionTypeId into g
                        select new PatientActionsNotificationCount
                        {
                            ActionTypeId = g.Key,
                            Count = g.Count()
                        };
            return await query.ToListAsync();
        }

        /// <summary>
        /// Method to create a new Comment
        /// </summary>
        /// <param name="comment"></param>
        /// <returns></returns>
        public async Task<long> AddPACommentDAL(PatientActionComment comment)
        {
            await _updatableDBContext.PatientActionComments.AddAsync(comment);
            await _updatableDBContext.SaveChangesAsync();
            return comment.Id;
        }

        /// <summary>
        /// Method to get patient Action by Id
        /// /// </summary>
        /// <param name="comment"></param>
        /// <returns></returns>


        public async Task<PatientActionsView> GetPatientActionById(int orgId, long id)
        {
            var query = await (from PA in _readOnlyDbContext.PatientActions
                               where PA.OrgId == orgId && PA.StatusId == (short)Status.Active && PA.Id == id
                               select new PatientActionsView
                               {
                                   Id = PA.Id,
                                   OrgId = PA.OrgId,
                                   ActionTypeId = PA.ActionTypeId,
                                   ActionId = PA.ActionId,
                                   PathologyRequestId = PA.PathologyRequestId,
                                   PathologyResponseMediaId = PA.PathologyResponseMediaId,
                                   LetterId = PA.LetterId,
                                   UserDetailsId = PA.UserDetailsId,
                                   DueDate = PA.DueDate,
                                   StatusId = PA.StatusId,
                                   Description = PA.Description,
                                   IsUrgent = PA.IsUrgent,
                                   IsClosed = PA.IsClosed,
                                   IsPending = PA.IsPending,
                                   HiddenFor= PA.HiddenFor,

                                   PatientDetailsId = PA.PatientDetailsId,
                                   RequestedDate = PA.RequestedDate,
                                   CreatedBy = PA.CreatedBy,
                                   CreatedDate = PA.CreatedDate,
                                   ModifiedDate = PA.ModifiedDate,
                                   ModifiedBy = PA.ModifiedBy,
                                   UserDetails =
                                       (from UD in _readOnlyDbContext.UserDetails
                                        where UD.Id == PA.UserDetailsId
                                        select new PatientActionsUserDetails
                                        {
                                            Id = UD.Id,
                                            Title = UD.TitleId,
                                            FirstName = UD.FirstName,
                                            SurName = UD.SurName
                                        }).FirstOrDefault(),
                                   PatientActionUserAssocs = (from PAU in _readOnlyDbContext.PatientActionUserAssocs
                                                                  where PAU.StatusId == (short)Status.Active && PAU.PatientActionsId == id
                                                                  select new PatientActionUserAssocView
                                                                  {
                                                                      Id = PAU.Id,
                                                                      OrgId = PAU.OrgId,
                                                                      PatientActionsId = PAU.PatientActionsId,
                                                                      StatusId = PAU.StatusId,
                                                                      CreatedBy = PAU.CreatedBy,
                                                                      UserDetailsId = PAU.UserDetailsId,
                                                                      CreatedDate = PAU.CreatedDate,
                                                                      ModifiedDate = PAU.ModifiedDate,
                                                                      ModifiedBy = PAU.ModifiedBy,
                                                                      UserDetailsInfo = (from UD in _readOnlyDbContext.UserDetails
                                                                                            where UD.Id == PAU.UserDetailsId
                                                                                            select new PatientActionsUserDetails
                                                                                            {
                                                                                                Id = UD.Id,
                                                                                                Title = UD.TitleId,
                                                                                                FirstName = UD.FirstName,
                                                                                                SurName = UD.SurName
                                                                                            }).FirstOrDefault()

                                                                  }).ToList(),
            PatientDetails = (PA.PatientDetailsId != null) ?
                                       (
                                           from PD in _readOnlyDbContext.PatientDetails
                                           where PD.Id == PA.PatientDetailsId
                                           select new PatientDetailInfo
                                           {
                                               Id = PD.Id,
                                               FirstName = PD.FirstName,
                                               SurName = PD.SurName,
                                               Mobile = PD.Mobile
                                           }).FirstOrDefault() : null,                               
                               }).FirstOrDefaultAsync();
                             return query;


        }

        public async Task<List<PatientActionCommentView>> GetPatientActionComments(long id)
        {
            var query = await (from PAC in _readOnlyDbContext.PatientActionComments
                               where PAC.StatusId == (short)Status.Active && PAC.PatientActionId == id
                               orderby PAC.CreatedDate descending
                               select new PatientActionCommentView
                               {
                                   Id = PAC.Id,
                                   OrgId = PAC.OrgId,
                                   PatientActionId = PAC.PatientActionId,
                                   CommentDescription = PAC.CommentDescription,
                                   StatusId = PAC.StatusId,
                                   CreatedBy = PAC.CreatedBy,
                                   CreatedDate = PAC.CreatedDate,
                                   ModifiedDate = PAC.ModifiedDate,
                                   ModifiedBy = PAC.ModifiedBy,
                                   CommentReadBy = PAC.CommentReadBy,
                                   CommentReadDate = PAC.CommentReadDate,
                                   CommentUserDetails = (from UD in _readOnlyDbContext.UserDetails
                                                   where UD.Id == PAC.CreatedBy
                                                   select new PatientActionsUserDetails
                                                   {
                                                       Id = UD.Id,
                                                       Title = UD.TitleId,
                                                       FirstName = UD.FirstName,
                                                       SurName = UD.SurName
                                                   }).FirstOrDefault(),

                               }).ToListAsync();

            return query;


        }

        public async Task<List<UserDetailInfo>> FetchUserDetailsFromIds(List<long> listIds, int orgId)
        {
            return await _readOnlyDbContext.UserDetails.Where(x => listIds.Contains(x.Id) && x.OrgId == orgId).Select(x => new UserDetailInfo
            {
                Id = x.Id,
                FirstName = x.FirstName,
                SurName = x.SurName,
                TitleId = x.TitleId,
                FileDetailsOutput = null
            }).ToListAsync();
        }

        public async Task<List<PatientDetailInfo>> FetchPatinetDetailsFromIds(List<long> listIds, int orgId)
        {
            return await _readOnlyDbContext.PatientDetails.Where(x => listIds.Contains(x.Id) && x.OrgId == orgId).Select(x => new PatientDetailInfo
            {
                Id = x.Id,
                FirstName = x.FirstName,
                SurName = x.SurName,
              
               }).ToListAsync();
        }


        public async Task<List<long>> FetchPatintActionListForUser(List<long?> userIds, int orgId)
        {
            return await _readOnlyDbContext.PatientActionUserAssocs.Where(x => userIds.Contains(x.UserDetailsId)
            && x.OrgId == orgId && x.StatusId == (short)Status.Active).Select(y => y.PatientActionsId).ToListAsync();
        }

        public async Task<List<PatientActionUserAssoc>> GetPatientActionUserAssoc(int orgId, long patientActionsId)
        {
            return await _readOnlyDbContext.PatientActionUserAssocs.Where(s => s.PatientActionsId == patientActionsId && s.OrgId == orgId).AsNoTracking().ToListAsync();
        }

        public async Task<List<PatientActionUserAssoc>> GetPAUserAssocForPathologyId(int orgId, long pathologyRequestId)
        { 
                
            var query = from t2 in _readOnlyDbContext.PatientActionUserAssocs
                        where (_readOnlyDbContext.PatientActions
                        .Where(s => s.PathologyRequestId == pathologyRequestId && s.OrgId == orgId).Select(x =>x.Id).Contains(t2.PatientActionsId) && t2.StatusId == (short)Status.Active)
                        select t2;
            return await query.ToListAsync();
        }

        /// <summary>
        /// EditMultiPatientActionUserAssoc
        /// </summary>
        /// <param name="patientActions"></param>
        /// <returns></returns>
        public async Task<int> EditMultiplePAUserAssoc(List<PatientActionUserAssoc> patientActions)
        {
            _updatableDBContext.PatientActionUserAssocs.UpdateRange(patientActions);
            return await _updatableDBContext.SaveChangesAsync();
        }


        public async Task<List<PatientActionsView>> GetPAViewByPathologyRequestId(int orgId,long pathologyRequestId)
        {
            var query = await (from PA in _readOnlyDbContext.PatientActions
                               where PA.OrgId == orgId && PA.StatusId == (short)Status.Active && PA.PathologyRequestId == pathologyRequestId
                               select new PatientActionsView
                               {
                                   Id = PA.Id,
                                   OrgId = PA.OrgId,
                                   ActionTypeId = PA.ActionTypeId,
                                   ActionId = PA.ActionId,
                                   PathologyRequestId = PA.PathologyRequestId,
                                   PathologyResponseMediaId = PA.PathologyResponseMediaId,
                                   LetterId = PA.LetterId,
                                   UserDetailsId = PA.UserDetailsId,
                                   DueDate = PA.DueDate,
                                   StatusId = PA.StatusId,
                                   Description = PA.Description,
                                   IsUrgent = PA.IsUrgent,
                                   IsClosed = PA.IsClosed,
                                   PatientDetailsId = PA.PatientDetailsId,
                                   RequestedDate = PA.RequestedDate,
                                   CreatedBy = PA.CreatedBy,
                                   CreatedDate = PA.CreatedDate,
                                   ModifiedDate = PA.ModifiedDate,
                                   ModifiedBy = PA.ModifiedBy,
                                   UserDetails =
                                       (from UD in _readOnlyDbContext.UserDetails
                                        where UD.Id == PA.UserDetailsId
                                        select new PatientActionsUserDetails
                                        {
                                            Id = UD.Id,
                                            Title = UD.TitleId,
                                            FirstName = UD.FirstName,
                                            SurName = UD.SurName
                                        }).FirstOrDefault(),
                                   PatientActionUserAssocs = (from PAU in _readOnlyDbContext.PatientActionUserAssocs
                                                              where PAU.StatusId == (short)Status.Active && PAU.PatientActionsId == PA.Id
                                                              select new PatientActionUserAssocView
                                                              {
                                                                  Id = PAU.Id,
                                                                  OrgId = PAU.OrgId,
                                                                  PatientActionsId = PAU.PatientActionsId,
                                                                  StatusId = PAU.StatusId,
                                                                  CreatedBy = PAU.CreatedBy,
                                                                  UserDetailsId = PAU.UserDetailsId,
                                                                  CreatedDate = PAU.CreatedDate,
                                                                  ModifiedDate = PAU.ModifiedDate,
                                                                  ModifiedBy = PAU.ModifiedBy,
                                                                  UserDetailsInfo = (from UD in _readOnlyDbContext.UserDetails
                                                                                     where UD.Id == PAU.UserDetailsId
                                                                                     select new PatientActionsUserDetails
                                                                                     {
                                                                                         Id = UD.Id,
                                                                                         Title = UD.TitleId,
                                                                                         FirstName = UD.FirstName,
                                                                                         SurName = UD.SurName
                                                                                     }).FirstOrDefault()

                                                              }).ToList(),
                                   PatientDetails = (PA.PatientDetailsId != null) ?
                                       (
                                           from PD in _readOnlyDbContext.PatientDetails
                                           where PD.Id == PA.PatientDetailsId
                                           select new PatientDetailInfo
                                           {
                                               Id = PD.Id,
                                               FirstName = PD.FirstName,
                                               SurName = PD.SurName,
                                               Mobile = PD.Mobile
                                           }).FirstOrDefault() : null,
                               }).ToListAsync();
            return query;

        }

        /// <summary>
        /// EditCommentsForReadBy
        /// </summary>
        /// <param name="patientActions"></param>
        /// <returns></returns>
        public async Task<int> EditPAComments(List<PatientActionComment> paComments)
        {
            _updatableDBContext.PatientActionComments.UpdateRange(paComments);
            return await _updatableDBContext.SaveChangesAsync();
        }


    }
}
