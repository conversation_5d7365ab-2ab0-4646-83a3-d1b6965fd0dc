﻿using AutoMapper;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.User.Context;
using Capstone2.RestServices.User.Interfaces;
using Capstone2.RestServices.User.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.RestServices.User.Services
{
    public class DashboardDAL : IDashboardDAL
    {
        public readonly AppSettings _appSettings;
        public readonly ReadOnlyUserDBContext _readOnlyDbContext;
        public UpdatableUserDBContext _updatableDBContext;
        public IMapper _mapper;
        public DashboardDAL(IOptions<AppSettings> appSettings, ReadOnlyUserDBContext readOnlyDbContext, UpdatableUserDBContext updatableDBContext, IMapper mapper)
        {
            _appSettings = appSettings.Value;
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
            _mapper = mapper;
        }
        public async Task<int> AddPreferencesDAL(Preference inputPreference)
        {
            _updatableDBContext.Preferences.Add(inputPreference);
            await _updatableDBContext.SaveChangesAsync();
            return inputPreference.Id;
        }
        /// <summary>
        /// Method to fetch a user preference
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        public async Task<Preference> GetPreferencesDAL(int orgId, long userId, short screenType)
        {
            var dashboardPreference = await _readOnlyDbContext.Preferences.Where(x => x.UserDetailsId == userId && x.ScreenType == screenType && x.OrgId == orgId).AsNoTracking().FirstOrDefaultAsync();
            return dashboardPreference;
        }

        public async Task<Preference> GetPreferencesByIdDAL(int orgId, int id)
        {
            var preference = await _readOnlyDbContext.Preferences.Where(x => x.Id == id && x.OrgId == orgId).AsNoTracking().FirstOrDefaultAsync();
            return preference;
        }

        /// <summary>
        /// Method to update a preference
        /// </summary>
        /// <param name="category"></param>

        public async Task<int> EditPreferencesDAL(Preference inputPreference)
        {
            _updatableDBContext.Preferences.Update(inputPreference);
            return await _updatableDBContext.SaveChangesAsync();
        }
    }
}
