﻿using AutoMapper;
using Capstone2.Framework.Business.Common;
using Capstone2.Framework.RestApi;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.User.Interfaces;
using Capstone2.RestServices.User.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Web;

namespace Capstone2.RestServices.User.Services
{
    public class ListUserBAL : IListUserBAL
    {
        public readonly AppSettings _appSettings;
        public readonly IListUserDAL _userDAL;
        public IMapper _mapper;
        public ListUserBAL(IOptions<AppSettings> appSettings, IListUserDAL userDAL, IMapper mapper)
        {
            _appSettings = appSettings.Value;
            _userDAL = userDAL;
            _mapper = mapper;
        }

        /// <summary>
        /// Method to call DAL to get a list of users
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        public async Task<ApiResponse<QueryResultList<UserInfo>>> ListUser(int orgId, QueryModel queryModel, BaseHttpRequestContext baseHttpRequestContext)
        {
            List<long> ListFileDetailsId = new();
            List<FileDetailsOutputForId> fileResponse = new();
            ApiResponse<QueryResultList<UserInfo>> apiResponse = new ApiResponse<QueryResultList<UserInfo>>();
            UserFilterModel filterModel = PrepareFilterParameters(queryModel.Filter);
            QueryResultList<UserListModel> queryList = await _userDAL.ListUser(orgId, queryModel, filterModel);
            QueryResultList<UserInfo> userQueryList = new QueryResultList<UserInfo>();
            if (queryList != null && queryList.ItemRecords is not null)
            {
                List<UserInfo> items = await FetchCompanyColoursForUsers(queryList.ItemRecords, orgId, (short)CompanyType.InternalCompany,baseHttpRequestContext);
                userQueryList.ItemRecords = items;
                userQueryList.PageNumber = queryModel.PageNumber;
                userQueryList.PageSize = queryModel.PageSize;
                userQueryList.CurrentCount = items.Count();

                foreach (var item in items)
                {
                    if (item.PhotoFileDetailsId != null)
                    {
                        ListFileDetailsId.Add((long)item.PhotoFileDetailsId);
                    }
                }
                if (ListFileDetailsId.Count > 0)
                {
                    string stringFileDetailsId = string.Join(",", ListFileDetailsId);
                    var token = baseHttpRequestContext.BearerToken;
                    string interServiceToken = baseHttpRequestContext.InterServiceToken;
                    string fileAPiUrl = _appSettings.ApiUrls["FileServiceUrl"] + "/file/file_details_list?" + "stringFileDetailsId" + "=" + HttpUtility.UrlEncode(stringFileDetailsId);
                    RestClient restClient = new RestClient(fileAPiUrl, null, token, interServiceToken);
                    var fileApiResponse = await restClient.GetAsync<ApiResponse<List<FileDetailsOutputForId>>>(fileAPiUrl, null);
                    if (fileApiResponse.StatusCode == StatusCodes.Status200OK)
                    {
                        fileResponse = fileApiResponse.Result;
                    }
                    else
                    {
                        apiResponse.StatusCode = fileApiResponse.StatusCode;
                        apiResponse.Errors = fileApiResponse.Errors;
                        apiResponse.Message = fileApiResponse.Message;
                        return apiResponse;
                    }

                    foreach (var item in items)
                    {
                        if (item.PhotoFileDetailsId != null)
                        {
                            var filedata = fileResponse.Find(x => x.FileDetail.Id == item.PhotoFileDetailsId);
                            item.FileDetailsOutput = filedata;
                        }
                    }
                }
            }
            else
            {
                queryList.ItemRecords = null;
                queryList.CurrentCount = 0;

            }
            userQueryList.TotalCount = queryList.TotalCount;
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            apiResponse.Result = userQueryList;
            return apiResponse;
        }


        private async Task<List<UserInfo>> FetchCompanyColoursForUsers(ICollection<UserListModel> items, int orgId, short companyType, BaseHttpRequestContext baseHttpRequestContext)
        {
            List<KeyValuePair<int, string>> companyColurs = await FetchCompanyColours(orgId, companyType);
            List<int> CompanyIds = companyColurs.Select(kvp => kvp.Key).ToList();

            //Get CompanyDetailsDetails
            var companyDetails = await ListCompanyDetailInfo(CompanyIds, baseHttpRequestContext);            

            List<UserInfo> lstUserInfo = new List<UserInfo>();
            if (items.Count > 0)
            {
                foreach (UserListModel user in items)
                {
                    UserInfo userInfo = _mapper.Map<UserListModel, UserInfo>(user);
                    lstUserInfo.Add(userInfo);
                    user.CompanyColours = new List<string>();
                    userInfo.LstUserCompanyInfo = user.UserCompanyAssocs.Where(x => CompanyIds.Contains(x.CompanyId))
                        .Select(x => new UserCompanyInfo()
                        {
                            UserCompanyId = x.CompanyId,
                            IsDefault = x.IsDefault,
                            Id = x.Id,
                            CompanyColour = companyColurs.SingleOrDefault(y => y.Key == x.CompanyId).Value,
                            CompanyName = (companyDetails != null && companyDetails.Any(c=> c.Id == x.CompanyId)) ? companyDetails.FirstOrDefault(c => c.Id == x.CompanyId).Name : "",
                        }).ToList();
                }
            }
            return lstUserInfo;
        }

        private async Task<List<Shared.Models.Entities.CompanyDetailInfo>> ListCompanyDetailInfo(List<int> companyIds, BaseHttpRequestContext baseHttpRequestContext)
        {
            string filter = "{ CompanyDetailId:[" + string.Join(",", companyIds) + "]}";
            string encodedFilter = Base64EncoderHelper.EncodeQueryModelFilterParam(filter);
            List<Shared.Models.Entities.CompanyDetailInfo> companyDetailInfos = new List<Shared.Models.Entities.CompanyDetailInfo>();
            string companySeviceUrl = _appSettings.ApiUrls["CompanyServiceUrl"] + "/company/company_details_info?f=" + encodedFilter;
            RestClient restClientCompany = new RestClient(companySeviceUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken);

            var data = await restClientCompany.GetAsync<ApiResponse<List<Shared.Models.Entities.CompanyDetailInfo>>>(companySeviceUrl);
            return data.Result;

        }
        /// <summary>
        /// method to generate the filter parameters from the request
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        private UserFilterModel PrepareFilterParameters(string filter)
        {
            if (!(string.IsNullOrEmpty(filter)))
                return JsonConvert.DeserializeObject<UserFilterModel>(filter);
            else
                return null;
        }


        private async Task<List<KeyValuePair<int, string>>> FetchCompanyColours(int orgId, short companyType)
        {
            return await _userDAL.FetchCompanyColours(orgId, companyType);
        }

        public async Task<ApiResponse<QueryResultList<ProviderInfo>>> ListProviders(int orgId, QueryModel queryModel)
        {
            UserFilterModel filterModel = PrepareFilterParameters(queryModel.Filter);
            QueryResultList<ProviderInfo> queryList = await _userDAL.FetchProviders(orgId, queryModel, filterModel);
            ApiResponse<QueryResultList<ProviderInfo>> apiResponse = new()
            {
                StatusCode = StatusCodes.Status200OK,
                Message = "Success",
                Result = queryList
            };

            return apiResponse;
        }

        public async Task<ApiResponse<List<Shared.Models.Entities.UserInfoView>>> ListUserDetailInfoWithCompanyAssocs(int orgId, QueryModel queryModel)
        {
            ApiResponse<List<Shared.Models.Entities.UserInfoView>> apiResponse = new();
            UserInfoFilterModel filterModel = PrepareFilterParametersForUserInfo(queryModel.Filter);

            List<Shared.Models.Entities.UserInfoView> lstUser = await _userDAL.ListUserDetailInfoWithCompanyAssocs(filterModel   , orgId);
            if (lstUser is null)
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            }
            else
            {
               // List<int> lstCompanyIds = new();
               // lstUser.ForEach(user =>
               // {
               //     if(user.LstUserCompanyInfo is not null && user.LstUserCompanyInfo.Any())
               //     {
               //         lstCompanyIds.AddRange( user.LstUserCompanyInfo.Select(x => x.UserCompanyId).ToList());
                        
               //     }
               // });
               //List< Shared.Models.Entities.CompanyDetailInfo> lstCompanyDetails = await _userDAL.FetchCompanyColoursForCompanyIds(orgId, lstCompanyIds);

               // if (lstCompanyDetails is not null && lstCompanyDetails.Any())
               // {
               //     lstUser.ForEach(user =>
               //     {
               //         if (user.LstUserCompanyInfo is not null && user.LstUserCompanyInfo.Any())
               //         {
               //             user.LstUserCompanyInfo.Where(assoc => assoc.UserCompanyId != null && assoc.UserCompanyId > 0).ToList()
               //             .ForEach(x =>
               //             {
               //                 Shared.Models.Entities.CompanyDetailInfo company = lstCompanyDetails.Where(a => a.Id == x.UserCompanyId).FirstOrDefault();
               //                 if (company is not null)
               //                 {
               //                     x.CompanyName = company.Name;
               //                     x.CompanyColour = company.Colour;
               //                 }
               //             });
               //         }
               //     });
               // }
                    
                apiResponse.StatusCode = StatusCodes.Status200OK;                        
            }
            apiResponse.Result = lstUser;
            return apiResponse;
        }
        private UserInfoFilterModel PrepareFilterParametersForUserInfo(string filter)
        {
            if (!(string.IsNullOrEmpty(filter)))
                return JsonConvert.DeserializeObject<UserInfoFilterModel>(filter);
            else
                return null;
        }
    }
}
