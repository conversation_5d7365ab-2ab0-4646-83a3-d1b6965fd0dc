﻿using Capstone2.Framework.RestApi;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.User.Interfaces;
using Capstone2.RestServices.User.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.RestServices.User.Services
{
    public class DashboardBAL : IDashboardBAL
    {

        public readonly AppSettings _appSettings;
        public readonly IDashboardDAL _dashboardDAL;
        private readonly ILogger<DashboardBAL> _logger;
        public DashboardBAL(IOptions<AppSettings> appSettings, IDashboardDAL dashboardDAL, ILogger<DashboardBAL> logger)
        {
            _appSettings = appSettings.Value;
            _dashboardDAL = dashboardDAL;
            _logger = logger;
        }
        public async Task<ApiResponse<int>> AddPreferencesBAL(long user_id, Preference inputPreference, BaseHttpRequestContext baseHttpRequestContext)
        {
            inputPreference.OrgId = baseHttpRequestContext.OrgId;
            inputPreference.StatusId = (short)Status.Active;
            ApiResponse<int> apiResponse = new();
            inputPreference.CreatedBy = baseHttpRequestContext.UserId;
            inputPreference.CreatedDate = DateTime.UtcNow;
            int id = await _dashboardDAL.AddPreferencesDAL(inputPreference);
            if (id > 0)
            {
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Result = id;
                apiResponse.Message = "Success";
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = 0;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("DashboardPreference cannot be added at this time.Please try again later.");
            }
            return apiResponse;
        }

        /// <summary>
        /// Method to fetch user Preference details
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<Preference>> GetPreferencesBAL(BaseHttpRequestContext baseHttpRequestContext, long user_id, short screen_type)
        {
            int orgId = baseHttpRequestContext.OrgId;
            ApiResponse<Preference> apiResposne = new();
            Preference preferenceFromDB = await _dashboardDAL.GetPreferencesDAL(orgId, user_id, screen_type);
            
            apiResposne.Result = preferenceFromDB;
            apiResposne.StatusCode = StatusCodes.Status200OK;
            apiResposne.Message = "Success";
            return apiResposne;

        }

        /// <summary>
        /// Method to update a user Preference
        /// </summary>
        /// <param name="id"></param>
        /// <param name="inputDashboardPreference"></param>
        /// <returns></returns>
        public async Task<ApiResponse<string>> EditPreferencesBAL(int id, Preference inputPreference, BaseHttpRequestContext baseHttpRequestContext)
        {
            int orgId = baseHttpRequestContext.OrgId;
            long userId = baseHttpRequestContext.UserId;
            ApiResponse<string> apiResponse = new();
            Preference preferenceFromDB = await _dashboardDAL.GetPreferencesByIdDAL(orgId, id);
            if (preferenceFromDB is null)
            {
                apiResponse.Result = null;
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("The Preference doesnot exist.");
                return apiResponse;
            }
            inputPreference.Id = preferenceFromDB.Id;
            inputPreference.OrgId = orgId;
            inputPreference.ModifiedDate = DateTime.UtcNow;
            inputPreference.ModifiedBy = userId;
            inputPreference.CreatedBy = preferenceFromDB.CreatedBy;
            inputPreference.CreatedDate = preferenceFromDB.CreatedDate;
            inputPreference.StatusId = preferenceFromDB.StatusId;
            int row = await _dashboardDAL.EditPreferencesDAL(inputPreference);

            apiResponse.Result = "Success";
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            return apiResponse;
        }
    }
}
