﻿using AutoMapper;
using BoldReports.RDL.DOM;
using Capstone2.Framework.RestApi;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.Framework.RestApi.Middlewares;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.User.Interfaces;
using Capstone2.RestServices.User.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.Azure.KeyVault.Models;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Threading.Tasks;
using static System.Collections.Specialized.BitVector32;

namespace Capstone2.RestServices.User.Services
{
    public class MyActionBAL : IMyActionBAL
    {

        public readonly AppSettings _appSettings;
        public readonly IMyActionDAL _myActionDAL;
        public readonly IUserDAL _userDAL;
        public readonly IMapper _mapper;
        private readonly ILogger<MyActionBAL> _logger;
        public MyActionBAL(IOptions<AppSettings> appSettings, IMyActionDAL myActionDAL, IUserDAL userDAL, ILogger<MyActionBAL> logger,IMapper mapper)
        {
            _appSettings = appSettings.Value;
            _myActionDAL = myActionDAL;
            _userDAL = userDAL;
            _logger = logger;
            _mapper = mapper;
        }
        public async Task<ApiResponse<long?>> AddMyActionBAL(long user_id, MyAction inputAction, BaseHttpRequestContext baseHttpRequestContext)
        {
            inputAction.OrgId = baseHttpRequestContext.OrgId;
            inputAction.StatusId = (short)Status.Active;
            ApiResponse<long?> apiResponse = new();
            if (inputAction.DueDate == null)
            {
                inputAction.DueDate = DateTime.UtcNow;
            }
            else if (inputAction.DueDate >= DateTime.Today)
            {
                inputAction.IsClosed = false;
                inputAction.IsPending = false;
                inputAction.CreatedBy = baseHttpRequestContext.UserId;
                inputAction.CreatedDate = DateTime.UtcNow;

                if (inputAction.MyActionUserAssocs is not null && inputAction.MyActionUserAssocs.Any())
                {
                    inputAction.MyActionUserAssocs.ToList().ForEach(a => { a.OrgId = baseHttpRequestContext.OrgId; a.CreatedBy = baseHttpRequestContext.UserId; a.StatusId = (short)Status.Active; });

                }

                long id = await _myActionDAL.AddMyActionDAL(inputAction);

                if (id > 0)
                {
                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    apiResponse.Result = id;
                    apiResponse.Message = "Success";

                    foreach(MyActionUserAssoc userAssoc in inputAction.MyActionUserAssocs) { 

                    await SendNotificationCountToPubSub(baseHttpRequestContext, userAssoc.UserDetailsId);

                    }
                }
                else
                {
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Result = null;
                    apiResponse.Message = "Failure";
                    apiResponse.Errors.Add("Action cannot be added at this time.Please try again later.");
                }
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = null;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("Action cannot be added at this time. Not a valid Due date");
            }
            return apiResponse;
        }

        public async Task<ApiResponse<bool>> HideMyActionBAL(long actionId, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<bool> apiResponse = new();
            try
            {
                MyAction action = await _myActionDAL.GetMyActionDAL(baseHttpRequestContext.OrgId, actionId);
                if (action == null)
                {
                    apiResponse.StatusCode = StatusCodes.Status404NotFound;
                    apiResponse.Message = "Action not found";
                    apiResponse.Result = false;
                    apiResponse.Errors.Add("The specified action does not exist.");
                    return apiResponse;
                }

                List<long> hiddenFor = string.IsNullOrEmpty(action.HiddenFor)
                    ? new List<long>()
                    : JsonConvert.DeserializeObject<List<long>>(action.HiddenFor);

                if (!hiddenFor.Contains(baseHttpRequestContext.UserId))
                {
                    hiddenFor.Add(baseHttpRequestContext.UserId);
                    action.HiddenFor = JsonConvert.SerializeObject(hiddenFor);
                    action.ModifiedBy = baseHttpRequestContext.UserId;
                    action.ModifiedDate = DateTime.UtcNow;

                    int rowsAffected = await _myActionDAL.EditMyActionDAL(action);
                    if (rowsAffected > 0)
                    {
                        apiResponse.StatusCode = StatusCodes.Status200OK;
                        apiResponse.Message = "Action hidden successfully";
                        apiResponse.Result = true;
                    }
                    else
                    {
                        apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                        apiResponse.Message = "Failed to hide action";
                        apiResponse.Result = false;
                        apiResponse.Errors.Add("Error occurred while hiding the action.");
                    }
                }
                else
                {
                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    apiResponse.Message = "Action already hidden for this user";
                    apiResponse.Result = true;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error hiding action: {ex.Message} {ex.StackTrace}");
                apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                apiResponse.Message = "Internal server error";
                apiResponse.Result = false;
                apiResponse.Errors.Add("An error occurred while processing the request.");
            }
            return apiResponse;
        }
        private async Task<bool> SendNotificationCountToPubSub(BaseHttpRequestContext baseHttpRequestContext, long userId)
        {
            try
            {
                int count = await _myActionDAL.GetMyActionsNotificationCount(baseHttpRequestContext.OrgId, userId);
                if (count != 0)
                {
                    var token = baseHttpRequestContext.BearerToken;
                    PubSubGenericResponse syncData = new PubSubGenericResponse();
                    syncData.NotificationCount = new Dictionary<string, int>();
                    syncData.NotificationCount.Add("myActions", count);
                    syncData.UserId = userId;
                    syncData.HubName = baseHttpRequestContext.OrgCode + ScreenName.MAIN_LAYOUT.GetDescription();

                    string syncServiceAPiUrl = _appSettings.ApiUrls["SyncServiceUrl"] + "/syncservice/SendDataToHubGeneric";
                    RestClient syncServiceClient = new RestClient(syncServiceAPiUrl, null, token, baseHttpRequestContext.InterServiceToken);
                    await syncServiceClient.PostAsync<ApiResponse<string>>(syncServiceAPiUrl, syncData);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Send NotificationCount to pubSub failed " + ex.Message);
            }
            return true;
        }

        public async Task<ApiResponse<long?>> AddCommentBAL(long action_id, Comment inputComment, BaseHttpRequestContext baseHttpRequestContext)
        {
            inputComment.OrgId = baseHttpRequestContext.OrgId;
            inputComment.StatusId = (short)Status.Active;
            inputComment.CreatedBy = baseHttpRequestContext.UserId;
            inputComment.CreatedDate = DateTime.UtcNow;
            inputComment.MyActionId = action_id;
            ApiResponse<long?> apiResponse = new();
            if (inputComment.CommentDescription is not null)
            {               
                long id = await _myActionDAL.AddCommentDAL(inputComment);
                var action = await _myActionDAL.GetMyActionDALVersion2(baseHttpRequestContext.OrgId, action_id);
                if (id > 0)
                {
                    action.HiddenFor = null;
                    action.ModifiedBy = baseHttpRequestContext.UserId;
                    action.ModifiedDate = DateTime.UtcNow;
                    await _myActionDAL.EditMyActionDAL(action);

                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    apiResponse.Result = id;
                    apiResponse.Message = "Success"; 
                }
                else
                {
                    apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                    apiResponse.Result = null;
                    apiResponse.Message = "Failure";
                    apiResponse.Errors.Add("Comment cannot be added at this time.Please try again later.");
                }
                return apiResponse;
            }
            else
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = null;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("No Text in the Comment. Please try again later.");
            }
            return apiResponse;
        }


        public async Task<ApiResponse<string>> EditMyActionBAL(long id, MyAction inputAction, BaseHttpRequestContext baseHttpRequestContext)
        {
            int orgId = baseHttpRequestContext.OrgId;
            long userId = baseHttpRequestContext.UserId;
            ApiResponse<string> apiResponse = new();
            MyAction actionFromDB = await _myActionDAL.GetMyActionDAL(orgId, id);
            if (actionFromDB is null)
            {
                apiResponse.Result = null;
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("The Action doesnot exist.");
                return apiResponse;
            }
            if (inputAction.isCommentAdded == true || inputAction.isPendingStatusChanged == true)
            {
                inputAction.HiddenFor = null;
            }

            inputAction.Id = actionFromDB.Id;
            inputAction.OrgId = orgId;
            inputAction.ModifiedDate = DateTime.UtcNow;
            inputAction.ModifiedBy = userId;
            inputAction.CreatedBy = actionFromDB.CreatedBy;
            inputAction.CreatedDate = actionFromDB.CreatedDate;
            inputAction.StatusId = actionFromDB.StatusId;
            if (inputAction.DueDate != actionFromDB.DueDate && inputAction.DueDate < DateTime.Today)
            {
                apiResponse.Result = null;
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failure";
                apiResponse.Errors.Add("The Action cannot be edited. DueDate is not valid.");
                return apiResponse;
            }

            if (inputAction.MyActionUserAssocs != null && inputAction.MyActionUserAssocs.Any())
            {
                inputAction.MyActionUserAssocs.ToList().ForEach(a => { a.OrgId = orgId; a.MyActionsId = (long)inputAction.Id; });
            }

            var removeUserList = await _myActionDAL.GetMyActionUserAssoc(orgId, (long)inputAction.Id);
            var inputUserList = inputAction.MyActionUserAssocs;
            inputAction.MyActionUserAssocs = null;
            List<MyActionUserAssoc> userList = EditMyActionUserAssocs(removeUserList, inputUserList, userId);
            inputAction.MyActionUserAssocs = userList;

            int row = await _myActionDAL.EditMyActionDAL(inputAction);

            apiResponse.Result = "Success";
            apiResponse.StatusCode = StatusCodes.Status200OK;
            apiResponse.Message = "Success";
            foreach (var item in inputAction.MyActionUserAssocs)
            {
                if (item.StatusId == 30)
                {
                    await SendNotificationCountToPubSub(baseHttpRequestContext, (long)item.UserDetailsId);
                }
            }

            return apiResponse;
        }

        /// <summary>
        /// Method to fetch Action details
        /// </summary>
        /// <param name="baseHttpRequestContext"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<MyActionList>> GetMyActionBAL(BaseHttpRequestContext baseHttpRequestContext, long id)
        {
            int orgId = baseHttpRequestContext.OrgId;
            ApiResponse<MyActionList> apiResposne = new();
            MyActionList actionFromDB = await _myActionDAL.GetMyActionDALView(orgId, id);
            if (actionFromDB is null)
            {
                apiResposne.Result = null;
                apiResposne.StatusCode = StatusCodes.Status400BadRequest;
                apiResposne.Message = "Failure";
                apiResposne.Errors.Add("The Action doesnot exist.");
                return apiResposne;
            }

            else
            {
                apiResposne.Result = actionFromDB;
                apiResposne.StatusCode = StatusCodes.Status200OK;
                apiResposne.Message = "Success";
                return apiResposne;
            }
        }

        /// <summary>
        /// Method to Fetch all Comments
        /// </summary>
        /// <param name="st"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<ApiResponse<List<CommentView>>> GetCommentsBAL(long actionId, BaseHttpRequestContext baseHttpRequestContext)
        {
            var comments = await _myActionDAL.GetComments(baseHttpRequestContext.OrgId, actionId);
            long readingUserId = baseHttpRequestContext.UserId;
            List<Comment> editCommentList = new();

            MyActionList actionFromDB = await _myActionDAL.GetMyActionDALView(baseHttpRequestContext.OrgId, actionId);

            if (comments is not null && comments.Count > 0)
            {
                List<long> AssignedUserIds = actionFromDB.MyActionUserAssocs.Select(x => x.UserDetailsId).ToList();
                if(actionFromDB.AssignFrom is not null)
                {
                    AssignedUserIds.Add((long)actionFromDB.AssignFrom);
                }

                foreach (var myComment in comments)
                {
                    if (myComment.CommentReadBy is null && AssignedUserIds.Contains(readingUserId) && readingUserId != myComment.CreatedBy)

                    {
                        Comment commentToAdd = _mapper.Map<CommentView, Comment>(myComment);
                        commentToAdd.CommentReadBy = readingUserId;
                        commentToAdd.CommentReadDate = DateTime.UtcNow;
                        editCommentList.Add(commentToAdd);

                    }
                }

                if (editCommentList.Count > 0)
                {

                    int updatedCount = await _myActionDAL.EditComments(editCommentList);
                }

            }


            var apiResponse = new ApiResponse<List<CommentView>>
            {
                Result = comments,
                Message = "Success",
                StatusCode = StatusCodes.Status200OK

            };
            return apiResponse;

        }

        /// <summary>
        /// Method to fetch  list of my actions
        /// </summary>
        /// <param name="queryModel"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>

        public async Task<ApiResponse<QueryResultList<MyActionList>>> GetMyActionsBAL(QueryModel queryModel, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<QueryResultList<MyActionList>> apiResponse = new();
            QueryResultList<MyActionList> queryList = new();
            MyActionsFilterModel filterModel = PrepareFilterParameters(queryModel.Filter);

            //if permission is false(not allowed) for given role throw the error msg that not authorilzed
           
            if (filterModel.UserDetailsIds.Count == 1 && filterModel.UserDetailsIds.Contains(baseHttpRequestContext.UserId))
            {
                queryList = await _myActionDAL.GetMyActionsDAL(baseHttpRequestContext.UserId, baseHttpRequestContext.OrgId, queryModel, filterModel);
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";
                apiResponse.Result = queryList;
                return apiResponse;
            }
            else 
            {

                RolesPermissionsAssoc permission = await _myActionDAL.GetPermissionOnRoleId(baseHttpRequestContext.OrgId, baseHttpRequestContext.RoleId, (short)ApiPermission.My_Actions_Report);

                if (permission == null)
                {
                    apiResponse.StatusCode = StatusCodes.Status403Forbidden;
                    apiResponse.Errors.Add("Not an Authorized User");
                    apiResponse.Result = null;
                    return apiResponse;
                }
                if (permission.IsAllowed == false)
                {
                    apiResponse.StatusCode = StatusCodes.Status403Forbidden;
                    apiResponse.Errors.Add("Not an Authorized User");
                    apiResponse.Result = null;
                    return apiResponse;
                }
                
                queryList = await _myActionDAL.GetMyActionsDAL(baseHttpRequestContext.UserId, baseHttpRequestContext.OrgId, queryModel, filterModel);
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";
                apiResponse.Result = queryList;
                return apiResponse;
            } 
        }

        /// <summary>
        /// method to generate the filter parameters from the request
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        private MyActionsFilterModel PrepareFilterParameters(string filter)
        {
            if (!(String.IsNullOrEmpty(filter)))
            {
                return JsonConvert.DeserializeObject<MyActionsFilterModel>(filter);
            }
            else
            {
                return null;
            }
        }

        public async Task<ApiResponse<int>> GetMyActionsNotificationCount(BaseHttpRequestContext baseHttpRequestContext, long user_id)
        {
            int count = await _myActionDAL.GetMyActionsNotificationCount(baseHttpRequestContext.OrgId, user_id);
            ApiResponse<int> apiResponse = new ApiResponse<int>()
            {
                StatusCode = StatusCodes.Status200OK,
                Message = "Success",
                Result = count
            };
            return apiResponse;

        }


        private List<MyActionUserAssoc> EditMyActionUserAssocs(ICollection<MyActionUserAssoc> removeUserList, ICollection<MyActionUserAssoc> inputUserList, long userId)
        {
            List<MyActionUserAssoc> addUserList = new();

            foreach (var userAssoc in inputUserList)
            {
                if (userAssoc.Id > 0)
                {
                    var existingobj = removeUserList.FirstOrDefault(x => x.Id == userAssoc.Id);
                    removeUserList.Remove(existingobj);
                }
                else
                {
                    MyActionUserAssoc userAssocDB = removeUserList.Where(x => x.UserDetailsId == userAssoc.UserDetailsId).FirstOrDefault();
                    if (userAssocDB is null)
                    {
                        userAssoc.CreatedDate = DateTime.UtcNow;
                        userAssoc.CreatedBy = userId;
                        userAssoc.StatusId = (short)Status.Active;
                        addUserList.Add(userAssoc);
                    }
                    else
                    {
                        if (userAssocDB.StatusId == (short)Status.Deleted)
                        {
                            userAssocDB.StatusId = (short)Status.Active;
                            userAssocDB.ModifiedDate = DateTime.UtcNow;
                            userAssocDB.ModifiedBy = userId;
                            addUserList.Add(userAssocDB);

                        }
                        removeUserList.Remove(userAssocDB);

                    }
                }
            }

            foreach (var userAssoc in removeUserList.Where(x => x.StatusId == (short)Status.Active))
            {
                userAssoc.StatusId = (short)Status.Deleted;
                userAssoc.ModifiedDate = DateTime.UtcNow;
                userAssoc.ModifiedBy = userId;
            }
            addUserList = addUserList.Concat(removeUserList).ToList();
            return addUserList;
        }
    }
}
