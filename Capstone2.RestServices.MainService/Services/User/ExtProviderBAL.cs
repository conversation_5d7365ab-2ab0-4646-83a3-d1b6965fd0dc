﻿using AutoMapper;
using Capstone2.Framework.RestApi;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.User.Interfaces;
using Capstone2.RestServices.User.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Transactions;

namespace Capstone2.RestServices.User.Services
{
    public class ExtProviderBAL : IExtProviderBAL
    {
        public readonly AppSettings _appSettings;
        public readonly IUserDAL _userDAL;
        public IMapper _mapper;
        public IUserCompanyDAL _userCompanyDAL;
        public IListUserDAL _listUserDAL;
        public IAddressDAL _addressDAL;
        public ExtProviderBAL(IOptions<AppSettings> appSettings, IUserDAL userDAL, IMapper mapper, IUserCompanyDAL userCompanyDAL, IListUserDAL listUserDAL, IAddressDAL addressDAL)
        {
            _appSettings = appSettings.Value;
            _userDAL = userDAL;
            _mapper = mapper;
            _userCompanyDAL = userCompanyDAL;
            _listUserDAL = listUserDAL;
            _addressDAL = addressDAL;
        }

        /// <summary>
        /// Method to add External Provider
        /// </summary>
        /// <param name="externalProvider"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns></returns>
        public async Task<ApiResponse<UserDetail>> AddExternalProvider(ExternalProvider externalProvider, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<UserDetail> apiResponse = new ApiResponse<UserDetail>();
            AuthUserRequest newloginuser = new AuthUserRequest()
            {
                IsOffsiteAccess = false,
                Mobile = externalProvider.Mobile,
                UserStatusId = (short)UserStatus.Active

            };
            var token = baseHttpRequestContext.BearerToken;
            var interServiceToken = baseHttpRequestContext.InterServiceToken;

            string adduserAPiUrl = _appSettings.ApiUrls["AuthServiceUrl"] + "/auth/addextprovider";
            RestClient restClient = new(adduserAPiUrl, null, token, interServiceToken);
            var authuserResponse = await restClient.PostAsync<ApiResponse<string>>(adduserAPiUrl, newloginuser);
            long userId = Convert.ToInt64(authuserResponse.Result);
            if (authuserResponse.StatusCode == 200)
            {
                UserDetail user = _mapper.Map<ExternalProvider, UserDetail>(externalProvider);
                user.OrgId = baseHttpRequestContext.OrgId;
                user.Id = userId;
                user.UserTypeId = (short?)UserType.External_Provider;
                user.ModifiedBy = baseHttpRequestContext.UserId;
                if (user != null && user.Addresses.Any())
                {
                    user.Addresses.ToList().ForEach(a => { a.OrgId = baseHttpRequestContext.OrgId; a.ModifiedBy = baseHttpRequestContext.UserId; a.StatusId = (short)Status.Active; });
                }
                if (user != null && user.UserCompanyAssocs.Any())
                {
                    user.UserCompanyAssocs.ToList().ForEach(a => { a.OrgId = baseHttpRequestContext.OrgId; a.ModifiedBy = baseHttpRequestContext.UserId; a.StatusId = (short)Status.Active; });
                }
                user = await _userDAL.AddUserDetail(user);
                if (user is not null)
                {
                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    apiResponse.Message = "Success";
                    apiResponse.Result = user;
                    return apiResponse;
                }
            }

            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            apiResponse.Message = "Failure";

            apiResponse.Errors.Add("External Provider cannot be added at this time.Please try again later.");
            return apiResponse;
        }

        public async Task<ApiResponse<long?>> EditExternalProvider(long id, ExternalProvider externalProvider, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<long?> apiResponse = new();
            int orgId = baseHttpRequestContext.OrgId;
            long modifiedUserId = baseHttpRequestContext.UserId;
            UserDetail user = _mapper.Map<ExternalProvider, UserDetail>(externalProvider);
            var extProviderDb = await _userDAL.GetUserDetail(id, orgId);
            if (extProviderDb == null)
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Failed";
                apiResponse.Errors.Add("External Provider not found");
                return apiResponse;
            }

            AuthUserRequest newloginuser = new AuthUserRequest()
            {
                UserStatusId = externalProvider.UserStatusId,
                RoleId=null
            };
            var token = baseHttpRequestContext.BearerToken;
            var interserviceToken = baseHttpRequestContext.InterServiceToken;
            string adduserAPiUrl = _appSettings.ApiUrls["AuthServiceUrl"] + "/auth/edituser/" + id;
            RestClient restClient = new RestClient(adduserAPiUrl, null, token, interserviceToken);
            var authuserResponse = await restClient.PutAsync<ApiResponse<string>>(adduserAPiUrl, newloginuser);
            long userId = Convert.ToInt64(authuserResponse.Result);
            if (authuserResponse.StatusCode == 200)
            {
                using (var transaction = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
                {
                    if (externalProvider != null && externalProvider.InputUserCompanyAssocs.Any())
                    {
                        externalProvider.InputUserCompanyAssocs?.ToList().ForEach(a => { a.OrgId = orgId; a.UserDetailsId = id; });
                    }
                    var inputAddressList = externalProvider.InputAddresses;
                    var removeCompanyAssocList = await _userCompanyDAL.GetusercompanyAssocs(id, orgId);
                    var inputCompanyAssocsList = externalProvider.InputUserCompanyAssocs;

                    user.Addresses = null;
                    user.UserCompanyAssocs = null;
                    user.ModifiedBy = baseHttpRequestContext.UserId;
                    user.ModifiedDate = DateTime.UtcNow;
                    user.OrgId = orgId;

                    var removeAddressList = await _addressDAL.GetAddresses(id, orgId);
                    var addAddressList = EditAddress(id, externalProvider, removeAddressList, baseHttpRequestContext);
                    user.Addresses = addAddressList;

                    var addCompanyList = new List<UserCompanyAssoc>();
                    foreach (var company in inputCompanyAssocsList)
                    {
                        if (company.Id > 0)
                        {
                            var existingobj = removeCompanyAssocList?.FirstOrDefault(x => x.Id == company.Id);
                            if (existingobj is not null && ((existingobj.ProviderNumber is not null && !existingobj.ProviderNumber.Equals(company.ProviderNumber)) || (existingobj.ProviderNumber is null)))
                            {
                                company.ModifiedBy = modifiedUserId;
                                company.ModifiedDate = DateTime.UtcNow;
                                company.StatusId = (short)Status.Active;
                                addCompanyList.Add(company);
                            }

                            var result = removeCompanyAssocList.Remove(existingobj);
                        }
                        else
                        {
                            company.OrgId = orgId;
                            company.UserDetailsId = id;
                            company.ModifiedBy = modifiedUserId;
                            company.StatusId = (short)Status.Active;
                            addCompanyList.Add(company);
                        }
                    }

                    if (addCompanyList is not null && addCompanyList.Count > 0)
                    {
                        user.UserCompanyAssocs = addCompanyList;
                    }

                    if (removeCompanyAssocList is not null && removeCompanyAssocList?.Count > 0)
                    {
                        removeCompanyAssocList.ForEach(x => { x.StatusId = (short)Status.Deleted; x.ModifiedBy = modifiedUserId; x.ModifiedDate = DateTime.UtcNow; });
                        await _userCompanyDAL.RemoveusercompanyList(removeCompanyAssocList);
                    }

                    await _userDAL.EditUserDetail(id, user);

                    transaction.Complete();

                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    apiResponse.Result = (long)externalProvider.Id;
                    apiResponse.Message = "Success";
                    return apiResponse;
                }
            }
            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            apiResponse.Message = "Failed";
            apiResponse.Errors.Add("External Provider cannot be updated.");
            return apiResponse;
        }

        private async Task EditCompanyAssocs(long id, ExternalProvider externalProvider, UserDetail extProviderDb, BaseHttpRequestContext baseHttpRequestContext)
        {
            int orgId = baseHttpRequestContext.OrgId;
            long modifiedUserId = baseHttpRequestContext.UserId;
            var removeCompanyAssocList = extProviderDb.UserCompanyAssocs.ToList();
            var inputCompanyAssocsList = externalProvider.InputUserCompanyAssocs;
            var addCompanyList = new List<UserCompanyAssoc>();

            foreach (var company in inputCompanyAssocsList)
            {
                if (company.Id > 0)
                {
                    var existingobj = removeCompanyAssocList.FirstOrDefault(x => x.Id == company.Id);
                    var result = removeCompanyAssocList.Remove(existingobj);
                }
                else
                {
                    company.OrgId = orgId;
                    company.UserDetailsId = id;
                    company.ModifiedBy = modifiedUserId;
                    addCompanyList.Add(company);
                }
            }

            if (removeCompanyAssocList.Count > 0)
            {
                await _userCompanyDAL.RemoveusercompanyList(removeCompanyAssocList);
            }
            if (addCompanyList.Count > 0)
            {
                await _userCompanyDAL.AddUsercompanyList(addCompanyList);
            }
        }

        private List<Address> EditAddress(long id, ExternalProvider externalProvider, List<Address> removeAddressList, BaseHttpRequestContext baseHttpRequestContext)
        {
            var addAddressList = new List<Address>();
            int orgId = baseHttpRequestContext.OrgId;
            long modifiedUserId = baseHttpRequestContext.UserId;
            var inputAddressList = externalProvider.InputAddresses;
            foreach (var address in inputAddressList)
            {
                if (address.Id > 0)
                {
                    var existingobj = removeAddressList.FirstOrDefault(x => x.Id == address.Id);
                    var result = removeAddressList.Remove(existingobj);
                }
                else
                {
                    var AddressTodb = new Address();
                    AddressTodb = address;
                    AddressTodb.OrgId = orgId;
                    AddressTodb.UserDetailsId = (int)id;
                    AddressTodb.ModifiedBy = baseHttpRequestContext.UserId;
                    AddressTodb.StatusId = (short)Status.Active;
                    addAddressList.Add(AddressTodb);
                }
            }

            foreach (var address in removeAddressList)
            {
                address.StatusId = (short)Status.Deleted;
                address.ModifiedDate = DateTime.UtcNow;
                address.ModifiedBy = modifiedUserId;
            }
            addAddressList = addAddressList.Concat(removeAddressList).ToList();
            return addAddressList;
        }

        /// <summary>
        /// Method to get list of external providers
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        public async Task<ApiResponse<QueryResultList<ExternalProviderListModel>>> ListExternalProviders(int orgId, QueryModel queryModel)
        {
            UserFilterModel filterModel = PrepareFilterParameters(queryModel.Filter);
            QueryResultList<ExternalProviderListModel> userQuery = await _listUserDAL.ListExtProviders(orgId, queryModel, filterModel);
            ApiResponse<QueryResultList<ExternalProviderListModel>> apiResponse = new()
            {
                StatusCode = StatusCodes.Status200OK,
                Message = "Success",
                Result = userQuery
            };

            return apiResponse;
        }

        /// <summary>
        /// method to generate the filter parameters from the request
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        private UserFilterModel PrepareFilterParameters(string filter)
        {
            if (!(String.IsNullOrEmpty(filter)))
                return JsonConvert.DeserializeObject<UserFilterModel>(filter);
            else
                return null;
        }

    }
}
