﻿using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.User.Context;
using Capstone2.RestServices.User.Interfaces;
using Capstone2.RestServices.User.Models;
using Capstone2.Shared.Models.Common;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Capstone2.RestServices.User.Services
{
    public class MyActionDAL : IMyActionDAL
    {
        public readonly AppSettings _appSettings;
        public readonly ReadOnlyUserDBContext _readOnlyDbContext;
        public UpdatableUserDBContext _updatableDBContext;
        public MyActionDAL(IOptions<AppSettings> appSettings, ReadOnlyUserDBContext readOnlyDbContext, UpdatableUserDBContext updatableDBContext)
        {
            _appSettings = appSettings.Value;
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
        }
        /// <summary>
        /// Method to create a new MyAction
        /// </summary>
        /// <param name="action"></param>
        /// <returns></returns>
        public async Task<long> AddMyActionDAL(MyAction action)
        {
            await _updatableDBContext.MyActions.AddAsync(action);
            await _updatableDBContext.SaveChangesAsync();
            return action.Id;
        }

        /// <summary>
        /// Method to create a new Comment
        /// </summary>
        /// <param name="comment"></param>
        /// <returns></returns>
        public async Task<long> AddCommentDAL(Comment comment)
        {
            await _updatableDBContext.Comments.AddAsync(comment);
            await _updatableDBContext.SaveChangesAsync();
            return comment.Id;
        }
        /// <summary>
        /// Fetch List of Comments
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="actionId"></param>
        /// <returns></returns>
        public async Task<List<CommentView>> GetComments(int orgId, long actionId)
        {
            var query = from C in _readOnlyDbContext.Comments
                        where C.OrgId == orgId && C.StatusId == (short)Status.Active && C.MyActionId == actionId
                        from UD1 in _readOnlyDbContext.UserDetails
                        where UD1.OrgId == orgId && UD1.Id == C.CreatedBy
                        select new CommentView
                        {
                            Id = C.Id,
                            OrgId = C.OrgId,
                            MyActionId = C.MyActionId,
                            CommentDescription = C.CommentDescription,
                            StatusId = C.StatusId,
                            CreatedBy = C.CreatedBy,
                            CreatedDate = C.CreatedDate,
                            ModifiedDate = C.ModifiedDate,
                            ModifiedBy = C.ModifiedBy,
                            UserDetails = new UserDetailInfo
                            {
                                Id = UD1.Id,
                                OrgId = UD1.OrgId,
                                FirstName = UD1.FirstName,
                                SurName = UD1.SurName,
                                TitleId = UD1.TitleId
                            }
                        };
            query = query.OrderByDescending(x => x.CreatedDate);
            return await query.ToListAsync();
        }


        public async Task<MyAction> GetMyActionDALVersion2(int orgId, long id)
        {
            var action = await _readOnlyDbContext.MyActions.Where(x => x.Id == id && x.OrgId == orgId).AsNoTracking().FirstOrDefaultAsync();
            return action;
        }
        public async Task<MyAction> GetMyActionDAL(int orgId, long id)
        {
            var action = await _readOnlyDbContext.MyActions.Include(c => c.Comments).Where(x => x.Id == id && x.OrgId == orgId).AsNoTracking().FirstOrDefaultAsync();
            return action;
        }

        /// <summary>
        /// Method to fetch an action
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="id"></param>
        public async Task<MyActionList> GetMyActionDALView(int orgId, long id)
        {
            MyActionList query = await (from A in _readOnlyDbContext.MyActions
                                        where A.OrgId == orgId && A.StatusId == (short)Status.Active && A.Id == id
                                        select new MyActionList
                                        {
                                            Id = A.Id,
                                            OrgId = A.OrgId,
                                            Description = A.Description,
                                            DueDate = A.DueDate,
                                            AssignFrom = A.AssignFrom,
                                            IsClosed = A.IsClosed,
                                            IsPending = A.IsPending,
                                            IsUrgent = A.IsUrgent,
                                            StatusId = A.StatusId,
                                            CreatedDate = A.CreatedDate,
                                            ModifiedDate = A.ModifiedDate,
                                            CreatedBy = A.CreatedBy,
                                            ModifiedBy = A.ModifiedBy,
                                            HiddenFor = A.HiddenFor,
                                            AssignedFromDetails = (from UD1 in _readOnlyDbContext.UserDetails
                                                                   where UD1.OrgId == orgId && UD1.Id == A.AssignFrom
                                                                   select new UserDetailInfo
                                                                   {
                                                                       Id = UD1.Id,
                                                                       OrgId = UD1.OrgId,
                                                                       FirstName = UD1.FirstName,
                                                                       SurName = UD1.SurName,
                                                                       TitleId = UD1.TitleId
                                                                   }).FirstOrDefault(),
                                            MyActionUserAssocs = (from MAU in _readOnlyDbContext.MyActionUserAssocs
                                                                  where MAU.StatusId == (short)Status.Active && MAU.MyActionsId == A.Id
                                                                  select new MyActionUserAssocView
                                                                  {
                                                                      Id = MAU.Id,
                                                                      OrgId = MAU.OrgId,
                                                                      MyActionsId = MAU.MyActionsId,
                                                                      UserDetailsId = MAU.UserDetailsId,
                                                                      StatusId = MAU.StatusId,
                                                                      CreatedBy = MAU.CreatedBy,
                                                                      CreatedDate = MAU.CreatedDate,
                                                                      ModifiedDate = MAU.ModifiedDate,
                                                                      ModifiedBy = MAU.ModifiedBy,
                                                                      UserDetailsInfo = (from UD in _readOnlyDbContext.UserDetails
                                                                                         where UD.Id == MAU.UserDetailsId
                                                                                         select new PatientActionsUserDetails
                                                                                         {
                                                                                             Id = UD.Id,
                                                                                             Title = UD.TitleId,
                                                                                             FirstName = UD.FirstName,
                                                                                             SurName = UD.SurName
                                                                                         }).FirstOrDefault()
                                                                  }).ToList(),
                                            TotalComments = (short)(from PAC1 in _readOnlyDbContext.Comments
                                                                    where PAC1.MyActionId == A.Id
                                                                    select PAC1.Id).ToList().Count,
                                            UnReadComments = (short)(from PAC2 in _readOnlyDbContext.Comments
                                                                     where PAC2.MyActionId == A.Id && PAC2.CommentReadBy == null
                                                                     select PAC2.Id).ToList().Count
                                        }).FirstOrDefaultAsync();

            return query;
        }


        /// <summary>
        /// Method to fetch a permission
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="rid"></param>
        public async Task<RolesPermissionsAssoc> GetPermissionOnRoleId(int orgId, int rid, short pid)
        {
            var permissionobject = _readOnlyDbContext.RolesPermissionsAssocs.FirstOrDefault(x => x.RolesId == rid && x.StatusId == (short)Status.Active && x.OrgId == orgId && x.PermissionsId == pid);
            return permissionobject;
        }

        /// <summary>
        /// Method to update an action
        /// </summary>
        /// <param name="category"></param>

        public async Task<int> EditMyActionDAL(MyAction action)
        {
            _updatableDBContext.MyActions.Update(action);
            return await _updatableDBContext.SaveChangesAsync();
        }

        public async Task<QueryResultList<MyActionList>> GetMyActionsDAL(long userId, int orgId, QueryModel queryModel, MyActionsFilterModel filterModel)
        {
            List<long> mafilter = null;
            List<long?> userList = null;
            if (filterModel.UserDetailsIds is not null && filterModel.UserDetailsIds.Any())
            {
                mafilter = await FetchMyActionListForUser(filterModel.UserDetailsIds, orgId);
            }
            else
            {
                userList.Add(userId);
                mafilter = await FetchMyActionListForUser(userList, orgId);
            }

            var query = from A in _readOnlyDbContext.MyActions
                        where A.OrgId == orgId && A.StatusId == (short)Status.Active
                        select new MyActionList
                        {
                            Id = A.Id,
                            OrgId = A.OrgId,
                            Description = A.Description,
                            DueDate = A.DueDate,
                            AssignFrom = A.AssignFrom,
                            IsClosed = A.IsClosed,
                            IsPending = A.IsPending,
                            IsUrgent = A.IsUrgent,
                            StatusId = A.StatusId,
                            CreatedDate = A.CreatedDate,
                            ModifiedDate = A.ModifiedDate,
                            CreatedBy = A.CreatedBy,
                            ModifiedBy = A.ModifiedBy,
                            HiddenFor = A.HiddenFor,
                            AssignedFromDetails = (from UD1 in _readOnlyDbContext.UserDetails
                                                   where UD1.OrgId == orgId && UD1.Id == A.AssignFrom //&& AP.StatusId == (short)Status.Active
                                                   select new UserDetailInfo
                                                   {
                                                       Id = UD1.Id,
                                                       OrgId = UD1.OrgId,
                                                       FirstName = UD1.FirstName,
                                                       SurName = UD1.SurName,
                                                       TitleId = UD1.TitleId
                                                   }).FirstOrDefault(),
                            MyActionUserAssocs = (from MAU in _readOnlyDbContext.MyActionUserAssocs
                                                  where MAU.StatusId == (short)Status.Active && MAU.MyActionsId == A.Id
                                                  select new MyActionUserAssocView
                                                  {
                                                      Id = MAU.Id,
                                                      OrgId = MAU.OrgId,
                                                      MyActionsId = MAU.MyActionsId,
                                                      UserDetailsId = MAU.UserDetailsId,
                                                      StatusId = MAU.StatusId,
                                                      CreatedBy = MAU.CreatedBy,
                                                      CreatedDate = MAU.CreatedDate,
                                                      ModifiedDate = MAU.ModifiedDate,
                                                      ModifiedBy = MAU.ModifiedBy,
                                                      UserDetailsInfo = (from UD in _readOnlyDbContext.UserDetails
                                                                         where UD.Id == MAU.UserDetailsId
                                                                         select new PatientActionsUserDetails
                                                                         {
                                                                             Id = UD.Id,
                                                                             Title = UD.TitleId,
                                                                             FirstName = UD.FirstName,
                                                                             SurName = UD.SurName
                                                                         }).FirstOrDefault()

                                                  }).ToList(),

                            TotalComments =
                                (short)(from PAC1 in _readOnlyDbContext.Comments
                                        where PAC1.MyActionId == A.Id
                                        select PAC1.Id).ToList().Count,

                            UnReadComments =
                                (short)(from PAC2 in _readOnlyDbContext.Comments
                                        where PAC2.MyActionId == A.Id && PAC2.CommentReadBy == null
                                        select PAC2.Id).ToList().Count


                        };

            // Apply hidden actions filter
            if (!filterModel.ShowHiddenActions)
            {
                query = query.Where(x => string.IsNullOrEmpty(x.HiddenFor) || !x.HiddenFor.Contains(userId.ToString()));
            }
            if (filterModel != null)
            {
                if (filterModel.StartDate != null)
                {
                    query = query.Where(x => x.DueDate >= filterModel.StartDate);

                }
                if (filterModel.EndDate != null)
                {
                    query = query.Where(x => x.DueDate <= filterModel.EndDate);

                }

                if (filterModel.UserDetailsIds != null)
                {
                    query = query.Where(x => filterModel.UserDetailsIds.Contains(x.AssignFrom) || mafilter.Contains(x.Id));
                }
                if (filterModel.Status != null)
                {
                    query = query.Where(x => filterModel.Status.Contains(x.IsClosed));
                }
                if (filterModel.IsPending != null)
                {
                    query = query.Where(x => filterModel.IsPending.Contains(x.IsPending));
                }
            }
            else
            {
                query = query.Where(x => mafilter.Contains(x.Id) || x.AssignFrom == userId);
            }

            if (!(string.IsNullOrWhiteSpace(queryModel.SearchTerm)))
            {
                query = SearchMyActions(query, queryModel.SearchTerm);

            }

            if (!string.IsNullOrWhiteSpace(queryModel.SortOrder) && !string.IsNullOrWhiteSpace(queryModel.SortTerm))
            {
                query = SortMyActions(query, queryModel.SortOrder, queryModel.SortTerm);

            }
            List<MyActionList> paginatedList = await CreatePaginateList(query, queryModel);
            QueryResultList<MyActionList> queryList = new QueryResultList<MyActionList>();
            if (paginatedList != null)
            {

                queryList.ItemRecords = paginatedList;
                queryList.PageNumber = queryModel.PageNumber;
                queryList.PageSize = queryModel.PageSize;
                queryList.CurrentCount = paginatedList.Count();

            }
            else
            {
                queryList.ItemRecords = null;
                queryList.CurrentCount = 0;

            }
            queryList.TotalCount = query.Count();
            queryList.PageNumber = queryModel.PageNumber;
            queryList.PageSize = queryModel.PageSize;
            return queryList;

        }

        private IQueryable<MyActionList> SearchMyActions(IQueryable<MyActionList> actionQuery, string searchTerm)
        {

            string filterString = string.Empty;
            bool istwoWordSearch = Regex.IsMatch(searchTerm, @"\s");

            if (!istwoWordSearch)
            {
                actionQuery = actionQuery.Where(s => s.Description.Contains(searchTerm));
            }
            else
            {
                string[] search = searchTerm.Split(" ");
                actionQuery = actionQuery.Where(s => s.Description.Contains(searchTerm));
            }
            return actionQuery;

        }
        private async Task<List<MyActionList>> CreatePaginateList(IQueryable<MyActionList> actionQuery, QueryModel queryModel)
        {
            if (queryModel.PageNumber > 0 && queryModel.PageSize > 0)
            {
                if (actionQuery.Any())
                {

                    List<MyActionList> paginatedList = await actionQuery.Skip((queryModel.PageNumber - 1) * queryModel.PageSize)
                 .Take(queryModel.PageSize).ToListAsync();

                    return paginatedList;

                }

            }
            return null;
        }
        private IQueryable<MyActionList> SortMyActions(IQueryable<MyActionList> query, string sortOrder, string sortTerm)
        {
            switch (sortTerm.ToLower())
            {
                case "duedate":

                    {
                        if ("asc".Equals(sortOrder.ToLower()))
                        {
                            query = query.OrderBy(x => x.IsClosed).ThenBy(x => x.DueDate).ThenBy(x => x.CreatedDate);
                        }
                        else
                        {

                            query = query.OrderByDescending(x => x.IsClosed).ThenBy(x => x.DueDate).ThenBy(x => x.CreatedDate);

                        }
                        break;
                    }

                default:
                    {
                        query = query.OrderByDescending(x => x.IsClosed).ThenBy(x => x.DueDate).ThenBy(x => x.CreatedDate);
                        break;
                    }

            }
            return query;

        }
        /// <summary>
        /// Method to get the my action notifications count
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="user_id"></param>
        /// <returns></returns>
        public async Task<int> GetMyActionsNotificationCount(int orgId, long user_id)
        {
            List<long?> userList = new() { user_id };
            List<long> mafilter = await FetchMyActionListForUser(userList, orgId);

            return await _readOnlyDbContext.MyActions
                .Where(x => mafilter.Contains(x.Id) && x.IsClosed == false && x.OrgId == orgId
                    && (string.IsNullOrEmpty(x.HiddenFor) || !x.HiddenFor.Contains(user_id.ToString())))
                .CountAsync();
        }


        public async Task<List<long>> FetchMyActionListForUser(List<long?> userIds, int orgId)
        {
            return await _readOnlyDbContext.MyActionUserAssocs.Where(x => userIds.Contains(x.UserDetailsId)
            && x.OrgId == orgId && x.StatusId == (short)Status.Active).Select(y => y.MyActionsId).ToListAsync();
        }

        public async Task<List<MyActionUserAssoc>> GetMyActionUserAssoc(int orgId, long myActionsId)
        {
            return await _readOnlyDbContext.MyActionUserAssocs.Where(s => s.MyActionsId == myActionsId && s.OrgId == orgId).AsNoTracking().ToListAsync();
        }

        /// <summary>
        /// EditCommentsForReadBy
        /// </summary>
        /// <param name="patientActions"></param>
        /// <returns></returns>
        public async Task<int> EditComments(List<Comment> myComments)
        {
            _updatableDBContext.Comments.UpdateRange(myComments);
            return await _updatableDBContext.SaveChangesAsync();
        }
    }
}
