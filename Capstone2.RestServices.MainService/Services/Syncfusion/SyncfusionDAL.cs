﻿using Capstone2.RestServices.Syncfusion.Context;
using Capstone2.RestServices.Syncfusion.Interfaces;

namespace Capstone2.RestServices.Syncfusion.Services
{
    public class SyncfusionDAL : ISyncfusionDAL
    {
        public readonly ReadOnlySyncfusionDBContext _readOnlyDbContext;
        public readonly UpdatableSyncfusionDBContext _updatableDBContext;

        public SyncfusionDAL(ReadOnlySyncfusionDBContext readOnlyDbContext, UpdatableSyncfusionDBContext updatableDBContext)
        {
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
        }
    }
}
