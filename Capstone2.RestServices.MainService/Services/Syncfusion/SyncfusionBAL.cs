﻿using AutoMapper;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.Syncfusion.Interfaces;
using Capstone2.RestServices.Syncfusion.Models;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Newtonsoft.Json.Linq;
using Syncfusion.EJ2.DocumentEditor;
using System;
using System.IO;
using System.Threading.Tasks;
using docIO = Syncfusion.DocIO.DLS;
using Syncfusion.DocIORenderer;
//using Syncfusion.DocIO;
using Syncfusion.Pdf;
using FormatType = Syncfusion.EJ2.DocumentEditor.FormatType;
using Microsoft.Extensions.Logging;
using Syncfusion.DocIO;

namespace Capstone2.RestServices.Syncfusion.Services
{
    public class SyncfusionBAL : ISyncfusionBAL
    {
        public IMapper _mapper;
        public readonly AppSettings _appSettings;
        private readonly ILogger<SyncfusionBAL> _logger;
        public IConfiguration _configuration { get; }

        public SyncfusionBAL(IMapper mapper, IOptions<AppSettings> appSettings, IConfiguration configuration, ILogger<SyncfusionBAL> logger)
        {
            _mapper = mapper;
            _appSettings = appSettings.Value;
            _configuration = configuration;
            _logger = logger;
        }

        public async Task<ApiResponse<JObject>> ConvertToJsonBAL(DocInput data)
        {
            ApiResponse<JObject> apiResponse = new ApiResponse<JObject>();
            try
            {
                string mystr = data.content.Replace("base64,", string.Empty);
                var testb = Convert.FromBase64String(mystr);
                Stream stream = new MemoryStream(testb);

                WordDocument document = WordDocument.Load(stream, FormatType.Docx); //GetFormatType(path));
                string json= Newtonsoft.Json.JsonConvert.SerializeObject(document);
                JObject jsonObject = Newtonsoft.Json.JsonConvert.DeserializeObject<JObject>(json);
                document.Dispose();
                stream.Dispose();

                apiResponse.Result = jsonObject;
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";
                return apiResponse;
            }
            catch(Exception ex)
            {
                apiResponse.Result = null;
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Message = "Bad Request";
                return apiResponse;
            }
        }

        /*Need to fix this conflict */


        public async Task<ApiResponse<FileUploadObject>> ConvertSFDToPDF(string data)
        {
            ApiResponse<FileUploadObject> apiResponse = new ApiResponse<FileUploadObject>();
            FileUploadObject fileUpload = new FileUploadObject();
            if (!string.IsNullOrEmpty(data))
            {
                Stream document = WordDocument.Save(data, FormatType.Docx);
                using (docIO.WordDocument wordDocument = new docIO.WordDocument(document, ""))
                {
                    DocIORenderer render = new DocIORenderer();
                    render.Settings.ExportBookmarks = ExportBookmarkType.Headings;
                    render.Settings.EmbedFonts = true;
                    render.Settings.EmbedCompleteFonts = true;
                    render.Settings.AutoDetectComplexScript = true;
                    // creating PDF
                    PdfDocument pdfDocument = render.ConvertToPDF(wordDocument);
                    fileUpload.CustomFileName = pdfDocument.DocumentInformation.Title + ".pdf";

                    // creating thumbnail
                    Stream[] imageStream = wordDocument.RenderAsImages();
                    using (var thumnnailStream = imageStream[0])
                    {
                        thumnnailStream.Position = 0;
                        // Thumbnail memory stream
                        using (MemoryStream imageMemoryStream = new MemoryStream())
                        {
                            thumnnailStream.CopyTo(imageMemoryStream);

                            // pdf memory stream
                            using (MemoryStream memoryStream = new MemoryStream())
                            {
                                pdfDocument.Save(memoryStream);
                                fileUpload.FileBytes = memoryStream.ToArray();
                                fileUpload.FileSize = (int)memoryStream.Length;
                                fileUpload.ThumbNailBytes = imageMemoryStream.ToArray();
                            }
                        }
                    }
                }
                fileUpload.MimeType = "application/pdf";
                fileUpload.FileName = System.Guid.NewGuid().ToString() + ".pdf";
                fileUpload.ThumbNailName = System.Guid.NewGuid().ToString() + ".jpeg";
                fileUpload.FileModuleTypeId = (short)FileModuleType.Patients;



                apiResponse.Result = fileUpload;
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";
                return apiResponse;
            }


            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            apiResponse.Message = "Failed";
            return apiResponse;

        }


        public async Task<ApiResponse<JObject>> ConvertHTMLTOSFDT(string data)
        {
            ApiResponse<JObject> apiResponse = new ApiResponse<JObject>();
            if (!string.IsNullOrEmpty(data))
            {
                WordDocument document = WordDocument.LoadString(data,FormatType.Html); //GetFormatType(path));
                string json = Newtonsoft.Json.JsonConvert.SerializeObject(document);
                JObject jsonObject = Newtonsoft.Json.JsonConvert.DeserializeObject<JObject>(json);

                apiResponse.Result = jsonObject;
                apiResponse.StatusCode = StatusCodes.Status200OK;
                apiResponse.Message = "Success";
                return apiResponse;
            }

            apiResponse.StatusCode = StatusCodes.Status400BadRequest;
            apiResponse.Message = "Failed";
            return apiResponse;
        }
    }
}
    