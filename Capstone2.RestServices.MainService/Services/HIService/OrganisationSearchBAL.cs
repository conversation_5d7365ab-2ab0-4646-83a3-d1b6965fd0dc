﻿using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.HIService.Common;
using Capstone2.RestServices.HIService.Interfaces;
using Capstone2.RestServices.HIService.Models;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Nehta.VendorLibrary.HI;
using Newtonsoft.Json;
using System;
using System.Linq;
using System.Threading.Tasks;
using static Capstone2.RestServices.HIService.Common.ConstantsHelper;
using DirectoryForOrganisation = nehta.mcaR32.ProviderSearchHIProviderDirectoryForOrganisation;
using ProviderOrganisation = nehta.mcaR50.ProviderSearchForProviderOrganisation;

namespace Capstone2.RestServices.HIService.Services
{
    public class OrganisationSearchBAL : IOrganisationSearchBAL
    {
        public readonly IOrganisationSearchService _organisationSearchService;
        private readonly IRequestLogsBAL _requestLogsBAL;
        private readonly ILogger<OrganisationSearchBAL> _logger;
        public OrganisationSearchBAL(IOrganisationSearchService organisationSearchService, IRequestLogsBAL requestLogsBAL, ILogger<OrganisationSearchBAL> logger)
        {
            _organisationSearchService = organisationSearchService;
            _requestLogsBAL = requestLogsBAL;
            _logger = logger;
        }

        public async Task<ApiResponse<HPIOSearchResponse<ProviderOrganisation.ServiceMessagesType, object>>> GetIdentifier(HPIORequestType requestType, BaseHttpRequestContext baseHttpRequestContext)
        {
            string orgId = Convert.ToString(baseHttpRequestContext.OrgId);
            long requesterId = baseHttpRequestContext.UserId;
            return requestType.Type switch
            {
                TypeofHISearch.HPIONumber => await GetProviderSearchResponse(requestType, orgId, requesterId, _organisationSearchService.ProviderOrganisationSearch),
                _ => new ApiResponse<HPIOSearchResponse<ProviderOrganisation.ServiceMessagesType, object>> { StatusCode = StatusCodes.Status400BadRequest, Message = "Invalid HPI-O search type" }
            };
        }

        private async Task<ApiResponse<HPIOSearchResponse<ProviderOrganisation.ServiceMessagesType, object>>> GetProviderSearchResponse(HPIORequestType requestType, string orgId, long requesterId, Func<HPIORequestType, string, Task<Tuple<ProviderOrganisation.searchForProviderOrganisationResponse, ProviderSearchForProviderOrganisationClient>>> hpioSearchMethod)
        {
            var requestLogs = new RequestLogs
            {
                Request = JsonConvert.SerializeObject(requestType),
                CreatedBy = requesterId,
                ModifiedBy = requesterId,
                ServiceName = $"{ConstantsHelper.HI_Organisation}- {requestType.Type.ToString()}"
            };

            var (searchResponse, requestClient) = await hpioSearchMethod(requestType, orgId);//Action:Search result from HI service for HPI-O
            if (searchResponse != null)
            {
                string hpioNumber = string.Empty;
                if (!string.IsNullOrWhiteSpace(searchResponse.searchForProviderOrganisationResult.hpioNumber))
                {
                    var uri = new Uri(searchResponse.searchForProviderOrganisationResult.hpioNumber);
                    hpioNumber = uri.Segments[^1].TrimEnd('/');
                }

                var hISearchResponse = new HPIOSearchResponse<ProviderOrganisation.ServiceMessagesType, object>
                {
                    UserId = requestType.UserId,
                    HPIONumber = hpioNumber,
                    Status = searchResponse.searchForProviderOrganisationResult.status,
                    ServiceMessagesType = searchResponse.searchForProviderOrganisationResult.serviceMessages
                };

                await LogRequestAndResponse(requestClient, requestLogs, ConstantsHelper.Status_Complete);

                return new ApiResponse<HPIOSearchResponse<ProviderOrganisation.ServiceMessagesType, object>>
                {
                    Result = hISearchResponse,
                    StatusCode = StatusCodes.Status200OK,
                    Message = ConstantsHelper.Status_Success
                };
            }
            else
            {
                ProviderOrganisation.ServiceMessagesType errorMsg = ConstantsHelper.DeserializeServiceMsgXml<ProviderOrganisation.ServiceMessagesType>(requestClient.SoapMessages.SoapResponse);

                await LogRequestAndResponse(requestClient, requestLogs, ConstantsHelper.Status_Error);

                return new ApiResponse<HPIOSearchResponse<ProviderOrganisation.ServiceMessagesType, object>>
                {
                    Result = new HPIOSearchResponse<ProviderOrganisation.ServiceMessagesType, object>
                    {
                        ServiceMessagesType = errorMsg
                    },
                    StatusCode = StatusCodes.Status200OK,
                    Message = ConstantsHelper.Status_Failed
                };
            }
        }

        public async Task<ApiResponse<HPIOSearchResponse<DirectoryForOrganisation.ServiceMessagesType, DirectoryForOrganisation.OrganisationProviderDirectoryEntryType>>> GetDirectoryEntry(HPIORequestType requestType, BaseHttpRequestContext baseHttpRequestContext)
        {
            string orgId = Convert.ToString(baseHttpRequestContext.OrgId);
            long requesterId = baseHttpRequestContext.UserId;
            return requestType.Type switch
            {
                TypeofHISearch.HPIODirectory => await GetDirectoryEntrySearchResponse(requestType, orgId, requesterId, _organisationSearchService.BasicDirectorySearch),
                TypeofHISearch.HPIODirectoryOrg => await GetDirectoryEntrySearchResponse(requestType, orgId, requesterId, _organisationSearchService.DirectoryEntryByDemographics),
                TypeofHISearch.HPIODirectoryStreetAddress => await GetDirectoryEntrySearchResponse(requestType, orgId, requesterId, _organisationSearchService.DirectoryAustralianAddressSearch),
                _ => new ApiResponse<HPIOSearchResponse<DirectoryForOrganisation.ServiceMessagesType, DirectoryForOrganisation.OrganisationProviderDirectoryEntryType>> { StatusCode = StatusCodes.Status400BadRequest, Message = "Invalid HPI-O search type" }
            };
        }

        private async Task<ApiResponse<HPIOSearchResponse<DirectoryForOrganisation.ServiceMessagesType, DirectoryForOrganisation.OrganisationProviderDirectoryEntryType>>> GetDirectoryEntrySearchResponse(HPIORequestType requestType, string orgId, long requesterId, Func<HPIORequestType, string, Task<Tuple<DirectoryForOrganisation.searchHIProviderDirectoryForOrganisationResponse, ProviderSearchHIProviderDirectoryForOrganisationClient>>> hpoSearchMethod)
        {
            var requestLogs = new RequestLogs
            {
                Request = JsonConvert.SerializeObject(requestType),
                CreatedBy = requesterId,
                ModifiedBy = requesterId,
                ServiceName = $"{ConstantsHelper.HI_Organisation}- {requestType.Type.ToString()}"
            };

            var (searchResponse, requestClient) = await hpoSearchMethod(requestType, orgId);//Action:Search result from HI service for HPI-O
            if (searchResponse != null)
            {
                var hISearchResponse = new HPIOSearchResponse<DirectoryForOrganisation.ServiceMessagesType, DirectoryForOrganisation.OrganisationProviderDirectoryEntryType>
                {
                    UserId = requestType.UserId,
                    OrganisationProviderDirectoryEntryType = searchResponse.searchHIProviderDirectoryForOrganisationResult?.organisationProviderDirectoryEntries?.ToList(),
                    ServiceMessagesType = searchResponse.searchHIProviderDirectoryForOrganisationResult.serviceMessages
                };

                await LogRequestAndResponse(requestClient, requestLogs, ConstantsHelper.Status_Complete);

                return new ApiResponse<HPIOSearchResponse<DirectoryForOrganisation.ServiceMessagesType, DirectoryForOrganisation.OrganisationProviderDirectoryEntryType>>
                {
                    Result = hISearchResponse,
                    StatusCode = StatusCodes.Status200OK,
                    Message = ConstantsHelper.Status_Success
                };
            }
            else
            {
                DirectoryForOrganisation.ServiceMessagesType errorMsg = ConstantsHelper.DeserializeServiceMsgXml<DirectoryForOrganisation.ServiceMessagesType>(requestClient.SoapMessages.SoapResponse);

                await LogRequestAndResponse(requestClient, requestLogs, ConstantsHelper.Status_Error);

                return new ApiResponse<HPIOSearchResponse<DirectoryForOrganisation.ServiceMessagesType, DirectoryForOrganisation.OrganisationProviderDirectoryEntryType>>
                {
                    Result = new HPIOSearchResponse<DirectoryForOrganisation.ServiceMessagesType, DirectoryForOrganisation.OrganisationProviderDirectoryEntryType>
                    {
                        ServiceMessagesType = errorMsg
                    },
                    StatusCode = StatusCodes.Status200OK,
                    Message = ConstantsHelper.Status_Failed
                };
            }
        }
        private async Task LogRequestAndResponse(dynamic requestClient, RequestLogs requestLogs, string status)
        {
            requestLogs.Request = ConstantsHelper.RemoveEncodingDeclaration(requestClient.SoapMessages.SoapRequest);
            requestLogs.Response = ConstantsHelper.RemoveEncodingDeclaration(requestClient.SoapMessages.SoapResponse);
            requestLogs.Status = status;
            using (requestClient)
            {
                await _requestLogsBAL.AddRequestLogs(requestLogs);
            }
        }

    }
}
