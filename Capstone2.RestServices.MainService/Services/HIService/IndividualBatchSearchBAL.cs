﻿using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.HIService.Common;
using Capstone2.RestServices.HIService.Interfaces;
using Capstone2.RestServices.HIService.Models;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Nehta.VendorLibrary.HI;
using Newtonsoft.Json;
using System;
using System.Linq;
using System.Threading.Tasks;
using BatchAsync = nehta.mcaR51.ProviderBatchAsyncSearchForProviderIndividual;

namespace Capstone2.RestServices.HIService.Services
{
    public class IndividualBatchSearchBAL: IIndividualBatchSearchBAL
    {
        public readonly IIndividualBatchSearchService _individualBatchSearchService;
        private readonly IRequestLogsBAL _requestLogsBAL;
        private readonly ILogger<IndividualBatchSearchBAL> _logger;
        public IndividualBatchSearchBAL(IIndividualBatchSearchService individualBatchSearchService, IRequestLogsBAL requestLogsBAL, ILogger<IndividualBatchSearchBAL> logger)
        {
            _individualBatchSearchService = individualBatchSearchService;
            _requestLogsBAL = requestLogsBAL;
            _logger = logger;
        }

        public async Task<ApiResponse<HPIIBatchSubmitResponse<BatchAsync.ServiceMessagesType>>> SubmitBatchSearchAsync(HPIIBatchSearchRequest requestType, BaseHttpRequestContext baseHttpRequestContext)
        {
            string orgId = Convert.ToString(baseHttpRequestContext.OrgId);
            long requesterId = baseHttpRequestContext.UserId;
            requestType.Id = Convert.ToString(requesterId);
            return await SubmitBatchSearchResponseAsync(requestType, orgId, requesterId, _individualBatchSearchService.SubmitBatchSearchAsync);
        }

        private async Task<ApiResponse<HPIIBatchSubmitResponse<BatchAsync.ServiceMessagesType>>> SubmitBatchSearchResponseAsync(HPIIBatchSearchRequest requestType, string orgId, long requesterId, Func<HPIIBatchSearchRequest, string, Task<Tuple<BatchAsync.submitSearchForProviderIndividualResponse, ProviderBatchAsyncSearchForProviderIndividualClient>>> hpiiBatchSearchMethodAsync)
        {
            var requestLogs = new RequestLogs
            {
                Request = JsonConvert.SerializeObject(requestType),
                CreatedBy = requesterId,
                ModifiedBy = requesterId,
                ServiceName = ConstantsHelper.HI_Individual_Batch_Submit_Async
            };

            var (searchResponse, requestClient) = await hpiiBatchSearchMethodAsync(requestType, orgId);//Action:Batch Search result from HI service

            if (searchResponse != null)
            {
                var hISearchResponse = new HPIIBatchSubmitResponse<BatchAsync.ServiceMessagesType>
                {
                    BatchIdentifier = searchResponse.submitSearchForProviderIndividualResult.batchIdentifier,
                    ServiceMessagesType = searchResponse.submitSearchForProviderIndividualResult.serviceMessages
                };

                await LogRequestAndResponse(requestClient, requestLogs, ConstantsHelper.Status_Complete);

                return new ApiResponse<HPIIBatchSubmitResponse<BatchAsync.ServiceMessagesType>>
                {
                    Result = hISearchResponse,
                    StatusCode = StatusCodes.Status200OK,
                    Message = ConstantsHelper.Status_Success
                };
            }
            else
            {
                BatchAsync.ServiceMessagesType errorMsg = ConstantsHelper.DeserializeServiceMsgXml<BatchAsync.ServiceMessagesType>(requestClient.SoapMessages.SoapResponse);

                await LogRequestAndResponse(requestClient, requestLogs, ConstantsHelper.Status_Error);

                return new ApiResponse<HPIIBatchSubmitResponse<BatchAsync.ServiceMessagesType>>
                {
                    Result = new HPIIBatchSubmitResponse<BatchAsync.ServiceMessagesType>
                    {
                        ServiceMessagesType = errorMsg
                    },
                    StatusCode = StatusCodes.Status200OK,
                    Message = ConstantsHelper.Status_Failed
                };
            }
        }

        public async Task<ApiResponse<HPIIBatchRetrieveResponse<BatchAsync.ServiceMessagesType, BatchAsync.BatchSearchForProviderIndividualResultType>>> GetRetrieveHPIIBatchSearch(HPIIBatchSearchStatus requestType, BaseHttpRequestContext baseHttpRequestContext)
        {
            string orgId = Convert.ToString(baseHttpRequestContext.OrgId);
            long requesterId = baseHttpRequestContext.UserId;
            requestType.Id = Convert.ToString(requesterId);
            return await GetRetrieveHPIIBatchResponse(requestType, orgId, requesterId, _individualBatchSearchService.GetRetrieveHPIIBatchSearch);
        }

        private async Task<ApiResponse<HPIIBatchRetrieveResponse<BatchAsync.ServiceMessagesType, BatchAsync.BatchSearchForProviderIndividualResultType>>> GetRetrieveHPIIBatchResponse(HPIIBatchSearchStatus requestType, string orgId, long requesterId, Func<HPIIBatchSearchStatus, string, Task<Tuple<BatchAsync.retrieveSearchForProviderIndividualResponse, ProviderBatchAsyncSearchForProviderIndividualClient>>> hpiiBatchSearchMethod)
        {
            var requestLogs = new RequestLogs
            {
                Request = JsonConvert.SerializeObject(requestType),
                CreatedBy = requesterId,
                ModifiedBy = requesterId,
                ServiceName = ConstantsHelper.HI_Individual_Batch_Retrieve
            };

            var (searchResponse, requestClient) = await hpiiBatchSearchMethod(requestType, orgId);//Action:Batch Search result from HI service

            if (searchResponse != null)
            {
                var hISearchResponse = new HPIIBatchRetrieveResponse<BatchAsync.ServiceMessagesType, BatchAsync.BatchSearchForProviderIndividualResultType>
                {
                    BatchIdentifier = searchResponse.retrieveSearchForProviderIndividualResult?.batchIdentifier,
                    SearchHPIIResult = searchResponse.retrieveSearchForProviderIndividualResult?.batchSearchForProviderIndividualResult?.ToList(),
                    ServiceMessagesType = searchResponse.retrieveSearchForProviderIndividualResult?.batchSearchForProviderIndividualResult?.FirstOrDefault()?.searchForProviderIndividualResult?.serviceMessages
                };

                await LogRequestAndResponse(requestClient, requestLogs, ConstantsHelper.Status_Complete);

                return new ApiResponse<HPIIBatchRetrieveResponse<BatchAsync.ServiceMessagesType, BatchAsync.BatchSearchForProviderIndividualResultType>>
                {
                    Result = hISearchResponse,
                    StatusCode = StatusCodes.Status200OK,
                    Message = ConstantsHelper.Status_Success
                };
            }
            else
            {
                BatchAsync.ServiceMessagesType errorMsg = ConstantsHelper.DeserializeServiceMsgXml<BatchAsync.ServiceMessagesType>(requestClient.SoapMessages.SoapResponse);

                await LogRequestAndResponse(requestClient, requestLogs, ConstantsHelper.Status_Error);

                return new ApiResponse<HPIIBatchRetrieveResponse<BatchAsync.ServiceMessagesType, BatchAsync.BatchSearchForProviderIndividualResultType>>
                {
                    Result = new HPIIBatchRetrieveResponse<BatchAsync.ServiceMessagesType, BatchAsync.BatchSearchForProviderIndividualResultType>
                    {
                        ServiceMessagesType = errorMsg
                    },
                    StatusCode = StatusCodes.Status200OK,
                    Message = ConstantsHelper.Status_Failed
                };
            }
        }

        private async Task LogRequestAndResponse(dynamic requestClient, RequestLogs requestLogs, string status)
        {
            requestLogs.Request = ConstantsHelper.RemoveEncodingDeclaration(requestClient.SoapMessages.SoapRequest);
            requestLogs.Response = ConstantsHelper.RemoveEncodingDeclaration(requestClient.SoapMessages.SoapResponse);
            requestLogs.Status = status;
            using (requestClient)
            {
                await _requestLogsBAL.AddRequestLogs(requestLogs);
            }
        }

    }
}
