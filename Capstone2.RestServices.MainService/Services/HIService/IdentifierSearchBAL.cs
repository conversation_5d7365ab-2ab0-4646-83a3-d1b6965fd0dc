﻿using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.HIService.Common;
using Capstone2.RestServices.HIService.Interfaces;
using Capstone2.RestServices.HIService.Models;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using nehta.mcaR3.ConsumerSearchIHI;
using Nehta.VendorLibrary.HI;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;
using static Capstone2.RestServices.HIService.Common.ConstantsHelper;

namespace Capstone2.RestServices.HIService.Services
{
    public class IdentifierSearchBAL : IIdentifierSearchBAL
    {
        public readonly IIdentifierSearchService _identifierSearchService;
        private readonly IRequestLogsBAL _requestLogsBAL;
        private readonly ILogger<IdentifierSearchBAL> _logger;
        public IdentifierSearchBAL(IIdentifierSearchService identifierSearchService, IRequestLogsBAL requestLogsBAL, ILogger<IdentifierSearchBAL> logger)
        {
            _identifierSearchService = identifierSearchService;
            _requestLogsBAL = requestLogsBAL;
            _logger = logger;
        }

        public async Task<ApiResponse<IHISearchResponse<ServiceMessagesType>>> GetIdentifier(IHIRequestType requestType, BaseHttpRequestContext baseHttpRequestContext)
        {
            string orgId = Convert.ToString(baseHttpRequestContext.OrgId);
            long requesterId = baseHttpRequestContext.UserId;
            return requestType.Type switch
            {
                TypeofHISearch.IHINumber => await GetSearchResponse(requestType, orgId, requesterId, _identifierSearchService.BasicSearch),
                TypeofHISearch.MedicareCardNumber => await GetSearchResponse(requestType, orgId, requesterId, _identifierSearchService.BasicMedicareSearch),
                TypeofHISearch.DVAFileNumber => await GetSearchResponse(requestType, orgId, requesterId, _identifierSearchService.BasicDvaSearch),
                TypeofHISearch.PostalAddress => await GetSearchResponse(requestType, orgId, requesterId, _identifierSearchService.AustralianPostalAddressSearch),
                TypeofHISearch.StreetAddress => await GetSearchResponse(requestType, orgId, requesterId, _identifierSearchService.AustralianStreetAddressSearch),
                TypeofHISearch.UnstructuredStreetAddress => await GetSearchResponse(requestType, orgId, requesterId, _identifierSearchService.AustralianUnstructuredAddressSearch),
                _ => new ApiResponse<IHISearchResponse<ServiceMessagesType>> { StatusCode = StatusCodes.Status400BadRequest, Message = "Invalid HI search type" }
            };
        }

        private async Task<ApiResponse<IHISearchResponse<ServiceMessagesType>>> GetSearchResponse(IHIRequestType requestType, string orgId, long requesterId, Func<IHIRequestType, string, Task<Tuple<searchIHIResponse, ConsumerSearchIHIClient>>> ihiSearchMethod)
        {
            var requestLogs = new RequestLogs
            {
                Request = JsonConvert.SerializeObject(requestType),
                CreatedBy = requesterId,
                ModifiedBy = requesterId,
                ServiceName = $"{ConstantsHelper.HI_Identifier}- {requestType.Type.ToString()}"
            };

            var (searchResponse, requestClient) = await ihiSearchMethod(requestType, orgId);//Action:Search result from HI service

            if (searchResponse != null)
            {
                string ihiNumber = string.Empty;
                if (!string.IsNullOrWhiteSpace(searchResponse.searchIHIResult.ihiNumber))
                {
                    var uri = new Uri(searchResponse.searchIHIResult.ihiNumber);
                    ihiNumber = uri.Segments[^1].TrimEnd('/');
                }

                var hISearchResponse = new IHISearchResponse<ServiceMessagesType>
                {
                    UserId = requestType.UserId,
                    FamilyName = searchResponse.searchIHIResult.familyName,
                    GivenName = searchResponse.searchIHIResult.givenName,
                    DateOfBirth = requestType.DateOfBirth,
                    Sex = requestType.Sex,
                    IHINumber = ihiNumber,
                    MedicareCardNumber = requestType.MedicareCardNumber,
                    DVAFileNumber = requestType.DVAFileNumber,
                    IHIRecordStatus = searchResponse.searchIHIResult.ihiRecordStatus.ToString(),
                    IHIStatus = searchResponse.searchIHIResult.ihiStatus.ToString(),
                    ServiceMessagesType = searchResponse.searchIHIResult.serviceMessages
                };

                await LogRequestAndResponse(requestClient, requestLogs, ConstantsHelper.Status_Complete);

                return new ApiResponse<IHISearchResponse<ServiceMessagesType>>
                {
                    Result = hISearchResponse,
                    StatusCode = StatusCodes.Status200OK,
                    Message = ConstantsHelper.Status_Success
                };
            }
            else
            {
                ServiceMessagesType errorMsg = ConstantsHelper.DeserializeServiceMsgXml<ServiceMessagesType>(requestClient.SoapMessages.SoapResponse);

                await LogRequestAndResponse(requestClient, requestLogs, ConstantsHelper.Status_Error);

                return new ApiResponse<IHISearchResponse<ServiceMessagesType>>
                {
                    Result = new IHISearchResponse<ServiceMessagesType>
                    {
                        ServiceMessagesType = errorMsg
                    },
                    StatusCode = StatusCodes.Status200OK,
                    Message = ConstantsHelper.Status_Failed
                };
            }
        }
        private async Task LogRequestAndResponse(dynamic requestClient, RequestLogs requestLogs, string status)
        {
            requestLogs.Request = ConstantsHelper.RemoveEncodingDeclaration(requestClient.SoapMessages.SoapRequest);
            requestLogs.Response = ConstantsHelper.RemoveEncodingDeclaration(requestClient.SoapMessages.SoapResponse);
            requestLogs.Status = status;
            using (requestClient)
            {
                await _requestLogsBAL.AddRequestLogs(requestLogs);
            }
        }
    }
}
