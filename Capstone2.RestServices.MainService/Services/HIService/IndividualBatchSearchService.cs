﻿using Azure.Core;
using Capstone2.Framework.Business.Common;
using Capstone2.Framework.RestApi.Utility;
using Capstone2.RestServices.HIService.Interfaces;
using Capstone2.RestServices.HIService.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Nehta.VendorLibrary.Common;
using Nehta.VendorLibrary.HI;
using System;
using System.Collections.Generic;
using System.ServiceModel;
using System.Threading.Tasks;
using BatchAsync = nehta.mcaR51.ProviderBatchAsyncSearchForProviderIndividual;

namespace Capstone2.RestServices.HIService.Services
{
    public class IndividualBatchSearchService : BaseSearchService, IIndividualBatchSearchService
    {
        private IKeyVaultHelper _keyVaultHelper;
        private readonly IConfiguration _configuration;
        private readonly ILogger<IndividualBatchSearchService> _logger;
        private readonly HIServiceSettings _hiServiceSettings;
        private IDistributedCacheHelper _redisCache;
        public IndividualBatchSearchService(ILogger<IndividualBatchSearchService> logger, IOptions<HIServiceSettings> hiServiceSettings, IKeyVaultHelper keyVaultHelper, IConfiguration configuration, IDistributedCacheHelper cache) : base(hiServiceSettings, keyVaultHelper, configuration, cache)
        {
            _logger = logger;
        }

        public async Task<Tuple<BatchAsync.submitSearchForProviderIndividualResponse, ProviderBatchAsyncSearchForProviderIndividualClient>> SubmitBatchSearchAsync(HPIIBatchSearchRequest requestType, string orgId)
        {
            var batchRequest = new List<BatchAsync.BatchSearchForProviderIndividualCriteriaType>();

            requestType.hpiiNumber.ForEach(data =>
            {
                var searchData = new BatchAsync.BatchSearchForProviderIndividualCriteriaType()
                {
                    requestIdentifier = Guid.NewGuid().ToString(),
                    searchForProviderIndividual = new BatchAsync.searchForProviderIndividual()
                    {
                        hpiiNumber = $"{HIQualifiers.HPIIQualifier}{data.HPIINumber}",
                        familyName = data.FamilyName
                    }
                };

                batchRequest.Add(searchData);
            });

            requestType.registrationNumber.ForEach(data =>
            {
                var searchData = new BatchAsync.BatchSearchForProviderIndividualCriteriaType()
                {
                    requestIdentifier = Guid.NewGuid().ToString(),
                    searchForProviderIndividual = new BatchAsync.searchForProviderIndividual()
                    {
                        registrationId = data.RegistrationNumber,
                        familyName = data.FamilyName
                    }
                };

                batchRequest.Add(searchData);
            });

            requestType.australianAddress.ForEach(data =>
            {
                var searchData = new BatchAsync.BatchSearchForProviderIndividualCriteriaType()
                {
                    requestIdentifier = Guid.NewGuid().ToString(),
                    searchForProviderIndividual = new BatchAsync.searchForProviderIndividual()
                    {
                        familyName = data.FamilyName,
                        onlyNameIndicator = data.OnlyNameIndicator,
                        dateOfBirth = data.DateOfBirth,
                        dateOfBirthSpecified = true,
                        sex = Enum.TryParse(data.Sex, out BatchAsync.SexType parsedGender) ? parsedGender : BatchAsync.SexType.M,
                        sexSpecified = true,
                        searchAustralianAddress = new BatchAsync.SearchAustralianAddressType()
                        {
                            streetName = data.Address.StreetName,
                            streetNumber = string.IsNullOrWhiteSpace(data.Address.StreetNumber) ? null : data.Address.StreetNumber,
                            lotNumber = string.IsNullOrWhiteSpace(data.Address.LotNumber) ? null : data.Address.LotNumber,
                            suburb = data.Address.Suburb,
                            state = Enum.TryParse(data.Address.State, out BatchAsync.StateType parsedState) ? parsedState : BatchAsync.StateType.ACT,
                            postcode = data.Address.Postcode
                        }
                    }
                };

                if (!data.OnlyNameIndicator && !string.IsNullOrWhiteSpace(data.GivenName))
                {
                    //searchData.searchForProviderIndividual.onlyNameIndicator = data.OnlyNameIndicator;
                    searchData.searchForProviderIndividual.givenName = data.GivenName.Split(";");
                }

                batchRequest.Add(searchData);
            });

            return await PerformBatchHISearchAsync(requestType.Id, orgId, batchRequest, client => client.BatchSubmitProviderIndividuals(batchRequest.ToArray()));
        }

        private async Task<Tuple<BatchAsync.submitSearchForProviderIndividualResponse, ProviderBatchAsyncSearchForProviderIndividualClient>> PerformBatchHISearchAsync(string userId, string orgId, List<BatchAsync.BatchSearchForProviderIndividualCriteriaType> request, Func<ProviderBatchAsyncSearchForProviderIndividualClient, BatchAsync.submitSearchForProviderIndividualResponse> searchFunc)
        {
            BatchAsync.submitSearchForProviderIndividualResponse response = null;
            ProviderBatchAsyncSearchForProviderIndividualClient client = null;
            try
            {
                client = await GetHISearchClient<ProviderBatchAsyncSearchForProviderIndividualClient, BatchAsync.ProductType, BatchAsync.QualifiedId>(userId, orgId);
                response = searchFunc(client);
            }
            catch (FaultException fex)
            {
                string returnError = "";
                var fault = fex.CreateMessageFault();
                if (fault.HasDetail)
                {
                    var error = fault.GetDetail<BatchAsync.ServiceMessagesType>();
                    if (error.serviceMessage.Length > 0)
                        returnError = $"{error.serviceMessage[0].code}: {error.serviceMessage[0].reason}";
                }

                _logger.LogError($"BatchSubmitProviderIndividuals HIService FaultException: {returnError}");
            }
            catch (Exception ex)
            {
                _logger.LogError($"BatchSubmitProviderIndividuals HIService Exception: {ex.Message}");
                throw;
            }

            return new Tuple<BatchAsync.submitSearchForProviderIndividualResponse, ProviderBatchAsyncSearchForProviderIndividualClient>(response, client);
        }

        public async Task<Tuple<BatchAsync.retrieveSearchForProviderIndividualResponse, ProviderBatchAsyncSearchForProviderIndividualClient>> GetRetrieveHPIIBatchSearch(HPIIBatchSearchStatus requestType, string orgId)
        {
            return await PerformRetrieveHPIIBatchSearch(requestType.Id, orgId, client => client.BatchRetrieveProviderIndividuals
            (new BatchAsync.retrieveSearchForProviderIndividual()
            {
                batchIdentifier = requestType.BatchIdentifier
            }
            ));
        }

        private async Task<Tuple<BatchAsync.retrieveSearchForProviderIndividualResponse, ProviderBatchAsyncSearchForProviderIndividualClient>> PerformRetrieveHPIIBatchSearch(string userId, string orgId, Func<ProviderBatchAsyncSearchForProviderIndividualClient, BatchAsync.retrieveSearchForProviderIndividualResponse> searchFunc)
        {
            BatchAsync.retrieveSearchForProviderIndividualResponse response = null;
            ProviderBatchAsyncSearchForProviderIndividualClient client = null;
            try
            {
                client = await GetHISearchClient<ProviderBatchAsyncSearchForProviderIndividualClient, BatchAsync.ProductType, BatchAsync.QualifiedId>(userId, orgId);
                response = searchFunc(client);
            }
            catch (FaultException fex)
            {
                string returnError = "";
                var fault = fex.CreateMessageFault();
                if (fault.HasDetail)
                {
                    var error = fault.GetDetail<BatchAsync.ServiceMessagesType>();
                    if (error.serviceMessage.Length > 0)
                        returnError = $"{error.serviceMessage[0].code}: {error.serviceMessage[0].reason}";
                }

                _logger.LogError($"RetrieveHPIIBatchSearch HIService FaultException: {returnError}");
            }
            catch (Exception ex)
            {
                _logger.LogError($"RetrieveHPIIBatchSearch HIService Exception: {ex.Message}");
                throw;
            }

            return new Tuple<BatchAsync.retrieveSearchForProviderIndividualResponse, ProviderBatchAsyncSearchForProviderIndividualClient>(response, client);
        }

    }
}
