﻿using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.HIService.Common;
using Capstone2.RestServices.HIService.Interfaces;
using Capstone2.RestServices.HIService.Models;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Nehta.VendorLibrary.HI;
using Newtonsoft.Json;
using System;
using System.Linq;
using System.Threading.Tasks;
using static Capstone2.RestServices.HIService.Common.ConstantsHelper;
using DirectoryForIndividual = nehta.mcaR32.ProviderSearchHIProviderDirectoryForIndividual;
using ProviderIndividual = nehta.mcaR50.ProviderSearchForProviderIndividual;

namespace Capstone2.RestServices.HIService.Services
{
    public class IndividualSearchBAL : IIndividualSearchBAL
    {
        public readonly IIndividualSearchService _individualSearchService;
        private readonly IRequestLogsBAL _requestLogsBAL;
        private readonly ILogger<IndividualSearchBAL> _logger;
        public IndividualSearchBAL(IIndividualSearchService individualSearchService, IRequestLogsBAL requestLogsBAL, ILogger<IndividualSearchBAL> logger)
        {
            _individualSearchService = individualSearchService;
            _requestLogsBAL = requestLogsBAL;
            _logger = logger;
        }

        public async Task<ApiResponse<HPIISearchResponse<ProviderIndividual.ServiceMessagesType, object>>> GetIdentifier(HPIIRequestType requestType, BaseHttpRequestContext baseHttpRequestContext)
        {
            string orgId = Convert.ToString(baseHttpRequestContext.OrgId);
            long requesterId = baseHttpRequestContext.UserId;
            return requestType.Type switch
            {
                TypeofHISearch.HPIINumber => await GetProviderSearchResponse(requestType, orgId, requesterId, _individualSearchService.BasicHPIISearch),
                TypeofHISearch.HPIRegistrationNumber => await GetProviderSearchResponse(requestType, orgId, requesterId, _individualSearchService.BasicRegistrationSearch),
                TypeofHISearch.HPIIStreetAddress => await GetProviderSearchResponse(requestType, orgId, requesterId, _individualSearchService.AustralianAddressSearch),
                _ => new ApiResponse<HPIISearchResponse<ProviderIndividual.ServiceMessagesType, object>> { StatusCode = StatusCodes.Status400BadRequest, Message = "Invalid HPI-I search type" }
            };
        }

        private async Task<ApiResponse<HPIISearchResponse<ProviderIndividual.ServiceMessagesType, object>>> GetProviderSearchResponse(HPIIRequestType requestType, string orgId, long requesterId, Func<HPIIRequestType, string, Task<Tuple<ProviderIndividual.searchForProviderIndividualResponse, ProviderSearchForProviderIndividualClient>>> hpiSearchMethod)
        {
            var requestLogs = new RequestLogs
            {
                Request = JsonConvert.SerializeObject(requestType),
                CreatedBy = requesterId,
                ModifiedBy = requesterId,
                ServiceName = $"{ConstantsHelper.HI_Individual}- {requestType.Type.ToString()}"
            };

            var (searchResponse, requestClient) = await hpiSearchMethod(requestType, orgId);//Action:Search result from HI service for HPI-I
            if (searchResponse != null)
            {
                string hpiiNumber = string.Empty;
                if (!string.IsNullOrWhiteSpace(searchResponse.searchForProviderIndividualResult.hpiiNumber))
                {
                    var uri = new Uri(searchResponse.searchForProviderIndividualResult.hpiiNumber);
                    hpiiNumber = uri.Segments[^1].TrimEnd('/');
                }

                var hISearchResponse = new HPIISearchResponse<ProviderIndividual.ServiceMessagesType, object>
                {
                    UserId = requestType.UserId,
                    FamilyName = requestType.FamilyName,
                    HPIINumber = hpiiNumber,
                    Status = searchResponse.searchForProviderIndividualResult.status,
                    ServiceMessagesType = searchResponse.searchForProviderIndividualResult.serviceMessages
                };

                await LogRequestAndResponse(requestClient, requestLogs, ConstantsHelper.Status_Complete);

                return new ApiResponse<HPIISearchResponse<ProviderIndividual.ServiceMessagesType, object>>
                {
                    Result = hISearchResponse,
                    StatusCode = StatusCodes.Status200OK,
                    Message = ConstantsHelper.Status_Success
                };
            }
            else
            {
                ProviderIndividual.ServiceMessagesType errorMsg =
                    ConstantsHelper.DeserializeServiceMsgXml<ProviderIndividual.ServiceMessagesType>(requestClient.SoapMessages.SoapResponse);

                await LogRequestAndResponse(requestClient, requestLogs, ConstantsHelper.Status_Error);

                return new ApiResponse<HPIISearchResponse<ProviderIndividual.ServiceMessagesType, object>>
                {
                    Result = new HPIISearchResponse<ProviderIndividual.ServiceMessagesType, object>
                    {
                        ServiceMessagesType = errorMsg
                    },
                    StatusCode = StatusCodes.Status200OK,
                    Message = ConstantsHelper.Status_Failed
                };
            }
        }

        public async Task<ApiResponse<HPIISearchResponse<DirectoryForIndividual.ServiceMessagesType, DirectoryForIndividual.IndividualProviderDirectoryEntryType>>> GetDirectoryEntry(HPIIRequestType requestType, BaseHttpRequestContext baseHttpRequestContext)
        {
            string orgId = Convert.ToString(baseHttpRequestContext.OrgId);
            long requesterId = baseHttpRequestContext.UserId;
            return requestType.Type switch
            {
                TypeofHISearch.HPIDirectory => await GetDirectoryEntrySearchResponse(requestType, orgId, requesterId, _individualSearchService.BasicDirectorySearch),
                TypeofHISearch.HPIDirectoryByProvider => await GetDirectoryEntrySearchResponse(requestType, orgId, requesterId, _individualSearchService.DirectoryByProviderSearch),
                TypeofHISearch.HPIDirectoryStreetAddress => await GetDirectoryEntrySearchResponse(requestType, orgId, requesterId, _individualSearchService.DirectoryAustralianAddressSearch),
                _ => new ApiResponse<HPIISearchResponse<DirectoryForIndividual.ServiceMessagesType, DirectoryForIndividual.IndividualProviderDirectoryEntryType>> { StatusCode = StatusCodes.Status400BadRequest, Message = "Invalid HPI-I search type" }
            };
        }

        private async Task<ApiResponse<HPIISearchResponse<DirectoryForIndividual.ServiceMessagesType, DirectoryForIndividual.IndividualProviderDirectoryEntryType>>> GetDirectoryEntrySearchResponse(HPIIRequestType requestType, string orgId, long requesterId, Func<HPIIRequestType, string, Task<Tuple<DirectoryForIndividual.searchHIProviderDirectoryForIndividualResponse, ProviderSearchHIProviderDirectoryForIndividualClient>>> hpiSearchMethod)
        {
            var requestLogs = new RequestLogs
            {
                Request = JsonConvert.SerializeObject(requestType),
                CreatedBy = requesterId,
                ModifiedBy = requesterId,
                ServiceName = $"{ConstantsHelper.HI_Individual}- {requestType.Type.ToString()}"
            };

            var (searchResponse, requestClient) = await hpiSearchMethod(requestType, orgId);//Action:Search result from HI service for HPI-I

            if (searchResponse != null)
            {
                var hISearchResponse = new HPIISearchResponse<DirectoryForIndividual.ServiceMessagesType, DirectoryForIndividual.IndividualProviderDirectoryEntryType>
                {
                    UserId = requestType.UserId,
                    IndividualProviderDirectoryEntries = searchResponse.searchHIProviderDirectoryForIndividualResult?.individualProviderDirectoryEntries?.ToList(),
                    ServiceMessagesType = searchResponse.searchHIProviderDirectoryForIndividualResult.serviceMessages
                };

                await LogRequestAndResponse(requestClient, requestLogs, ConstantsHelper.Status_Complete);

                return new ApiResponse<HPIISearchResponse<DirectoryForIndividual.ServiceMessagesType, DirectoryForIndividual.IndividualProviderDirectoryEntryType>>
                {
                    Result = hISearchResponse,
                    StatusCode = StatusCodes.Status200OK,
                    Message = ConstantsHelper.Status_Success
                };
            }
            else
            {
                DirectoryForIndividual.ServiceMessagesType errorMsg = ConstantsHelper.DeserializeServiceMsgXml<DirectoryForIndividual.ServiceMessagesType>(requestClient.SoapMessages.SoapResponse);

                await LogRequestAndResponse(requestClient, requestLogs, ConstantsHelper.Status_Error);

                return new ApiResponse<HPIISearchResponse<DirectoryForIndividual.ServiceMessagesType, DirectoryForIndividual.IndividualProviderDirectoryEntryType>>
                {
                    Result = new HPIISearchResponse<DirectoryForIndividual.ServiceMessagesType, DirectoryForIndividual.IndividualProviderDirectoryEntryType>
                    {
                        ServiceMessagesType = errorMsg
                    },
                    StatusCode = StatusCodes.Status200OK,
                    Message = ConstantsHelper.Status_Failed
                };
            }
        }


        private async Task LogRequestAndResponse(dynamic requestClient, RequestLogs requestLogs, string status)
        {
            requestLogs.Request = ConstantsHelper.RemoveEncodingDeclaration(requestClient.SoapMessages.SoapRequest);
            requestLogs.Response = ConstantsHelper.RemoveEncodingDeclaration(requestClient.SoapMessages.SoapResponse);
            requestLogs.Status = status;
            using (requestClient)
            {
                await _requestLogsBAL.AddRequestLogs(requestLogs);
            }
        }
    }
}
