﻿using Capstone2.Framework.Business.Common;
using Capstone2.Framework.RestApi.Utility;
using Capstone2.RestServices.HIService.Interfaces;
using Capstone2.RestServices.HIService.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Nehta.VendorLibrary.Common;
using Nehta.VendorLibrary.HI;
using System;
using System.ServiceModel;
using System.Threading.Tasks;
using DirectoryForOrganisation = nehta.mcaR32.ProviderSearchHIProviderDirectoryForOrganisation;
using ProviderOrganisation = nehta.mcaR50.ProviderSearchForProviderOrganisation;

namespace Capstone2.RestServices.HIService.Services
{
    public class OrganisationSearchService : BaseSearchService, IOrganisationSearchService
    {
        private IKeyVaultHelper _keyVaultHelper;
        private readonly IConfiguration _configuration;
        private readonly ILogger<OrganisationSearchService> _logger;
        private readonly HIServiceSettings _hiServiceSettings;
        private IDistributedCacheHelper _redisCache;
        public OrganisationSearchService(ILogger<OrganisationSearchService> logger, IOptions<HIServiceSettings> hiServiceSettings, IKeyVaultHelper keyVaultHelper, IConfiguration configuration, IDistributedCacheHelper cache) : base(hiServiceSettings, keyVaultHelper, configuration, cache)
        {
            _logger = logger;
        }

        public async Task<Tuple<ProviderOrganisation.searchForProviderOrganisationResponse, ProviderSearchForProviderOrganisationClient>> ProviderOrganisationSearch(HPIORequestType requestType, string orgId)
        {
            var request = new ProviderOrganisation.searchForProviderOrganisation()
            {
                hpioNumber = $"{HIQualifiers.HPIOQualifier}{requestType.HPIONumber}"
            };

            ProviderOrganisation.searchForProviderOrganisationResponse response = null;
            ProviderSearchForProviderOrganisationClient client = null;
            try
            {
                client = await GetHISearchClient<ProviderSearchForProviderOrganisationClient, ProviderOrganisation.ProductType, ProviderOrganisation.QualifiedId>(requestType.UserId, orgId);
                response = client.ProviderOrganisationSearch(request);
            }
            catch (FaultException fex)
            {
                string returnError = "";
                var fault = fex.CreateMessageFault();
                if (fault.HasDetail)
                {
                    var error = fault.GetDetail<ProviderOrganisation.ServiceMessagesType>();
                    if (error.serviceMessage.Length > 0)
                        returnError = $"{error.serviceMessage[0].code}: {error.serviceMessage[0].reason}";
                }

                _logger.LogError($"HIService FaultException in ProviderOrganisationSearch: {returnError}");
            }
            catch (Exception ex)
            {
                _logger.LogError($"HIService Exception in ProviderOrganisationSearch: {ex.Message}");
                throw;
            }

            return new Tuple<ProviderOrganisation.searchForProviderOrganisationResponse, ProviderSearchForProviderOrganisationClient>(response, client);
        }

        public async Task<Tuple<DirectoryForOrganisation.searchHIProviderDirectoryForOrganisationResponse, ProviderSearchHIProviderDirectoryForOrganisationClient>> BasicDirectorySearch(HPIORequestType requestType, string orgId)
        {
            var request = new DirectoryForOrganisation.searchHIProviderDirectoryForOrganisation()
            {
                hpioNumber = $"{HIQualifiers.HPIOQualifier}{requestType.HPIONumber}",
                linkSearchType = "ALL"
            };

            return await PerformHPIODirectorySearch(requestType.UserId, orgId, request, client => client.IdentifierSearch(request));
        }
        public async Task<Tuple<DirectoryForOrganisation.searchHIProviderDirectoryForOrganisationResponse, ProviderSearchHIProviderDirectoryForOrganisationClient>> DirectoryEntryByDemographics(HPIORequestType requestType, string orgId)
        {
            var request = new DirectoryForOrganisation.searchHIProviderDirectoryForOrganisation()
            {
                name = requestType.OrgName,
                organisationType = requestType.OrgType
            };

            return await PerformHPIODirectorySearch(requestType.UserId, orgId, request, client => client.DemographicSearch(request));
        }

        public async Task<Tuple<DirectoryForOrganisation.searchHIProviderDirectoryForOrganisationResponse, ProviderSearchHIProviderDirectoryForOrganisationClient>> DirectoryAustralianAddressSearch(HPIORequestType requestType, string orgId)
        {
            var request = new DirectoryForOrganisation.searchHIProviderDirectoryForOrganisation()
            {
                name = requestType.OrgName,
                organisationType = requestType.OrgType,
                serviceType = requestType.ServiceType
                //,organisationDetails
            };

            request.australianAddressCriteria = new DirectoryForOrganisation.AustralianAddressCriteriaType()
            {
                streetName = requestType.Address.StreetName,
                streetNumber = string.IsNullOrWhiteSpace(requestType.Address.StreetNumber) ? null : requestType.Address.StreetNumber,
                lotNumber = string.IsNullOrWhiteSpace(requestType.Address.LotNumber) ? null : requestType.Address.LotNumber,
                suburb = requestType.Address.Suburb,
                state = Enum.TryParse(requestType.Address.State, out DirectoryForOrganisation.StateType parsedState) ? parsedState : DirectoryForOrganisation.StateType.ACT,
                postcode = requestType.Address.Postcode
            };

            return await PerformHPIODirectorySearch(requestType.UserId, orgId, request, client => client.DemographicSearch(request));
        }

        private async Task<Tuple<DirectoryForOrganisation.searchHIProviderDirectoryForOrganisationResponse, ProviderSearchHIProviderDirectoryForOrganisationClient>> PerformHPIODirectorySearch(string userId, string orgId, DirectoryForOrganisation.searchHIProviderDirectoryForOrganisation request, Func<ProviderSearchHIProviderDirectoryForOrganisationClient, DirectoryForOrganisation.searchHIProviderDirectoryForOrganisationResponse> searchFunc)
        {
            DirectoryForOrganisation.searchHIProviderDirectoryForOrganisationResponse response = null;
            ProviderSearchHIProviderDirectoryForOrganisationClient client = null;
            try
            {
                client = await GetHISearchClient<ProviderSearchHIProviderDirectoryForOrganisationClient, DirectoryForOrganisation.ProductType, DirectoryForOrganisation.QualifiedId>(userId, orgId);
                response = searchFunc(client);
            }
            catch (FaultException fex)
            {
                string returnError = "";
                var fault = fex.CreateMessageFault();
                if (fault.HasDetail)
                {
                    var error = fault.GetDetail<DirectoryForOrganisation.ServiceMessagesType>();
                    if (error.serviceMessage.Length > 0)
                        returnError = $"{error.serviceMessage[0].code}: {error.serviceMessage[0].reason}";
                }

                _logger.LogError($"HIService FaultException in DirectoryForOrganisationSearch: {returnError}");
            }
            catch (Exception ex)
            {
                _logger.LogError($"HIService Exception in DirectoryForOrganisationSearch: {ex.Message}");
                throw;
            }

            return new Tuple<DirectoryForOrganisation.searchHIProviderDirectoryForOrganisationResponse, ProviderSearchHIProviderDirectoryForOrganisationClient>(response, client);
        }

    }
}
