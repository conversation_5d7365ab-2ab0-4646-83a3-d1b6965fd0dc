﻿using Capstone2.Framework.Business.Common;
using Capstone2.Framework.RestApi.Utility;
using Capstone2.RestServices.HIService.Interfaces;
using Capstone2.RestServices.HIService.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using nehta.mcaR3.ConsumerSearchIHI;
using Nehta.VendorLibrary.Common;
using Nehta.VendorLibrary.HI;
using System;
using System.ServiceModel;
using System.Threading.Tasks;

namespace Capstone2.RestServices.HIService.Services
{
    public class IdentifierSearchService : BaseSearchService, IIdentifierSearchService
    {
        private IKeyVaultHelper _keyVaultHelper;
        private readonly IConfiguration _configuration;
        private readonly ILogger<IdentifierSearchService> _logger;
        private readonly HIServiceSettings _hiServiceSettings;
        private IDistributedCacheHelper _redisCache;
        public IdentifierSearchService(ILogger<IdentifierSearchService> logger, IOptions<HIServiceSettings> hiServiceSettings, IKeyVaultHelper keyVaultHelper, IConfiguration configuration, IDistributedCacheHelper cache) : base(hiServiceSettings, keyVaultHelper, configuration, cache)
        {
            _logger = logger;
        }

        public async Task<Tuple<searchIHIResponse, ConsumerSearchIHIClient>> BasicSearch(IHIRequestType requestType, string orgId)
        {
            var request = new searchIHI
            {
                ihiNumber = $"{HIQualifiers.IHIQualifier}{requestType.IHINumber}",
                familyName = requestType.FamilyName,
                givenName = string.IsNullOrWhiteSpace(requestType.GivenName) ? null : requestType.GivenName,
                dateOfBirth = requestType.DateOfBirth,
                sex = Enum.TryParse(requestType.Sex, out SexType parsedGender) ? parsedGender : SexType.M
            };

            return await PerformHISearch(requestType.UserId, orgId, request, client => client.BasicSearch(request));
        }

        public async Task<Tuple<searchIHIResponse, ConsumerSearchIHIClient>> BasicMedicareSearch(IHIRequestType requestType, string orgId)
        {
            var request = new searchIHI
            {
                medicareCardNumber = requestType.MedicareCardNumber,
                familyName = requestType.FamilyName,
                givenName = string.IsNullOrWhiteSpace(requestType.GivenName) ? null : requestType.GivenName,
                dateOfBirth = requestType.DateOfBirth,
                sex = Enum.TryParse(requestType.Sex, out SexType parsedGender) ? parsedGender : SexType.M
            };

            return await PerformHISearch(requestType.UserId, orgId, request, client => client.BasicMedicareSearch(request));
        }

        public async Task<Tuple<searchIHIResponse, ConsumerSearchIHIClient>> BasicDvaSearch(IHIRequestType requestType, string orgId)
        {
            var request = new searchIHI
            {
                dvaFileNumber = requestType.DVAFileNumber,
                familyName = requestType.FamilyName,
                givenName = string.IsNullOrWhiteSpace(requestType.GivenName) ? null : requestType.GivenName,
                dateOfBirth = requestType.DateOfBirth,
                sex = Enum.TryParse(requestType.Sex, out SexType parsedGender) ? parsedGender : SexType.M
            };

            return await PerformHISearch(requestType.UserId, orgId, request, client => client.BasicDvaSearch(request));
        }

        public async Task<Tuple<searchIHIResponse, ConsumerSearchIHIClient>> AustralianPostalAddressSearch(IHIRequestType requestType, string orgId)
        {
            var request = new searchIHI
            {
                familyName = requestType.FamilyName,
                givenName = string.IsNullOrWhiteSpace(requestType.GivenName) ? null : requestType.GivenName,
                dateOfBirth = requestType.DateOfBirth,
                sex = Enum.TryParse(requestType.Sex, out SexType parsedGender) ? parsedGender : SexType.M
            };

            request.australianPostalAddress = new AustralianPostalAddressType()
            {
                postalDeliveryGroup = new PostalDeliveryGroupType()
                {
                    postalDeliveryType = Enum.TryParse(requestType.Address.PostalDeliveryType, out PostalDeliveryType parsedPDTypeCode)
                    ? parsedPDTypeCode : PostalDeliveryType.CMA
                },
                suburb = requestType.Address.Suburb,
                state = Enum.TryParse(requestType.Address.State, out StateType parsedState) ? parsedState : StateType.ACT,
                postcode = requestType.Address.Postcode
            };

            return await PerformHISearch(requestType.UserId, orgId, request, client => client.AustralianPostalAddressSearch(request));
        }

        public async Task<Tuple<searchIHIResponse, ConsumerSearchIHIClient>> AustralianStreetAddressSearch(IHIRequestType requestType, string orgId)
        {
            var request = new searchIHI
            {
                familyName = requestType.FamilyName,
                givenName = string.IsNullOrWhiteSpace(requestType.GivenName) ? null : requestType.GivenName,
                dateOfBirth = requestType.DateOfBirth,
                sex = Enum.TryParse(requestType.Sex, out SexType parsedGender) ? parsedGender : SexType.M
            };

            request.australianStreetAddress = new AustralianStreetAddressType()
            {
                streetName = requestType.Address.StreetName,
                streetNumber = string.IsNullOrWhiteSpace(requestType.Address.StreetNumber) ? null : requestType.Address.StreetNumber,
                lotNumber = string.IsNullOrWhiteSpace(requestType.Address.LotNumber) ? null : requestType.Address.LotNumber,
                suburb = requestType.Address.Suburb,
                state = Enum.TryParse(requestType.Address.State, out StateType parsedState) ? parsedState : StateType.ACT,
                postcode = requestType.Address.Postcode
            };

            return await PerformHISearch(requestType.UserId, orgId, request, client => client.AustralianStreetAddressSearch(request));
        }

        public async Task<Tuple<searchIHIResponse, ConsumerSearchIHIClient>> AustralianUnstructuredAddressSearch(IHIRequestType requestType, string orgId)
        {
            var request = new searchIHI
            {
                familyName = requestType.FamilyName,
                givenName = string.IsNullOrWhiteSpace(requestType.GivenName) ? null : requestType.GivenName,
                dateOfBirth = requestType.DateOfBirth,
                sex = Enum.TryParse(requestType.Sex, out SexType parsedGender) ? parsedGender : SexType.M
            };

            request.australianUnstructuredStreetAddress = new AustralianUnstructuredStreetAddressType()
            {
                addressLineOne = string.IsNullOrWhiteSpace(requestType.Address.AddressLineOne) ? null : requestType.Address.AddressLineOne,
                addressLineTwo = string.IsNullOrWhiteSpace(requestType.Address.AddressLineTwo) ? null : requestType.Address.AddressLineTwo,
                suburb = requestType.Address.Suburb,
                state = Enum.TryParse(requestType.Address.State, out StateType parsedState) ? parsedState : StateType.ACT,
                postcode = requestType.Address.Postcode
            };

            return await PerformHISearch(requestType.UserId, orgId, request, client => client.AustralianUnstructuredAddressSearch(request));
        }

        private async Task<Tuple<searchIHIResponse, ConsumerSearchIHIClient>> PerformHISearch(string userId, string orgId, searchIHI request, Func<ConsumerSearchIHIClient, searchIHIResponse> searchFunc)
        {
            searchIHIResponse response = null;
            ConsumerSearchIHIClient client = null;
            try
            {
                client = await GetHISearchClient<ConsumerSearchIHIClient, ProductType, QualifiedId>(userId, orgId);
                response = searchFunc(client);
            }
            catch (FaultException fex)
            {
                string returnError = "";
                var fault = fex.CreateMessageFault();
                if (fault.HasDetail)
                {
                    var error = fault.GetDetail<ServiceMessagesType>();
                    if (error.serviceMessage.Length > 0)
                        returnError = $"{error.serviceMessage[0].code}: {error.serviceMessage[0].reason}";
                }

                _logger.LogError($"HIService FaultException: {returnError}");
            }
            catch (Exception ex)
            {
                _logger.LogError($"HIService Exception: {ex.Message}");
                throw;
            }

            return new Tuple<searchIHIResponse, ConsumerSearchIHIClient>(response, client);
        }
    }
}
