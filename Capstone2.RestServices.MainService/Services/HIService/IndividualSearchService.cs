﻿using Capstone2.Framework.Business.Common;
using Capstone2.Framework.RestApi.Utility;
using Capstone2.RestServices.HIService.Interfaces;
using Capstone2.RestServices.HIService.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Nehta.VendorLibrary.Common;
using Nehta.VendorLibrary.HI;
using System;
using System.ServiceModel;
using System.Threading.Tasks;
using DirectoryForIndividual = nehta.mcaR32.ProviderSearchHIProviderDirectoryForIndividual;
using ProviderIndividual = nehta.mcaR50.ProviderSearchForProviderIndividual;

namespace Capstone2.RestServices.HIService.Services
{
    public class IndividualSearchService : BaseSearchService, IIndividualSearchService
    {
        private IKeyVaultHelper _keyVaultHelper;
        private readonly IConfiguration _configuration;
        private readonly ILogger<IndividualSearchService> _logger;
        private readonly HIServiceSettings _hiServiceSettings;
        private IDistributedCacheHelper _redisCache;
        public IndividualSearchService(ILogger<IndividualSearchService> logger, IOptions<HIServiceSettings> hiServiceSettings, IKeyVaultHelper keyVaultHelper, IConfiguration configuration, IDistributedCacheHelper cache) : base(hiServiceSettings, keyVaultHelper, configuration, cache)
        {
            _logger = logger;
        }

        public async Task<Tuple<ProviderIndividual.searchForProviderIndividualResponse, ProviderSearchForProviderIndividualClient>> BasicHPIISearch(HPIIRequestType requestType, string orgId)
        {
            var request = new ProviderIndividual.searchForProviderIndividual
            {
                hpiiNumber = $"{HIQualifiers.HPIIQualifier}{requestType.HPIINumber}",
                familyName = requestType.FamilyName
            };

            return await PerformHISearch(requestType.UserId, orgId, request, client => client.ProviderIndividualSearch(request));
        }

        public async Task<Tuple<ProviderIndividual.searchForProviderIndividualResponse, ProviderSearchForProviderIndividualClient>> BasicRegistrationSearch(HPIIRequestType requestType, string orgId)
        {
            var request = new ProviderIndividual.searchForProviderIndividual
            {
                registrationId = requestType.RegistrationNumber,
                familyName = requestType.FamilyName
            };

            return await PerformHISearch(requestType.UserId, orgId, request, client => client.ProviderIndividualSearch(request));
        }

        public async Task<Tuple<ProviderIndividual.searchForProviderIndividualResponse, ProviderSearchForProviderIndividualClient>> AustralianAddressSearch(HPIIRequestType requestType, string orgId)
        {
            var request = new ProviderIndividual.searchForProviderIndividual
            {
                familyName = requestType.FamilyName,
                onlyNameIndicator = requestType.OnlyNameIndicator,
                dateOfBirth = requestType.DateOfBirth,
                dateOfBirthSpecified = true,
                sex = Enum.TryParse(requestType.Sex, out ProviderIndividual.SexType parsedGender) ? parsedGender : ProviderIndividual.SexType.M,
                sexSpecified = true
            };

            if (!requestType.OnlyNameIndicator && !string.IsNullOrWhiteSpace(requestType.GivenName))
            {
                //request.onlyNameIndicator = requestType.OnlyNameIndicator;
                request.givenName = requestType.GivenName.Split(";");
            }

            request.searchAustralianAddress = new ProviderIndividual.SearchAustralianAddressType()
            {
                streetName = requestType.Address.StreetName,
                //streetNumber = string.IsNullOrWhiteSpace(requestType.Address.StreetNumber) ? null : requestType.Address.StreetNumber,
                //lotNumber = string.IsNullOrWhiteSpace(requestType.Address.LotNumber) ? null : requestType.Address.LotNumber,
                suburb = requestType.Address.Suburb,
                state = Enum.TryParse(requestType.Address.State, out ProviderIndividual.StateType parsedState) ? parsedState : ProviderIndividual.StateType.ACT,
                postcode = requestType.Address.Postcode
            };

            return await PerformHISearch(requestType.UserId, orgId, request, client => client.ProviderIndividualSearch(request));
        }

        private async Task<Tuple<ProviderIndividual.searchForProviderIndividualResponse, ProviderSearchForProviderIndividualClient>> PerformHISearch(string userId, string orgId, ProviderIndividual.searchForProviderIndividual request, Func<ProviderSearchForProviderIndividualClient, ProviderIndividual.searchForProviderIndividualResponse> searchFunc)
        {
            ProviderIndividual.searchForProviderIndividualResponse response = null;
            ProviderSearchForProviderIndividualClient client = null;
            try
            {
                client = await GetHISearchClient<ProviderSearchForProviderIndividualClient, ProviderIndividual.ProductType, ProviderIndividual.QualifiedId>(userId, orgId);
                response = searchFunc(client);
            }
            catch (FaultException fex)
            {
                string returnError = "";
                var fault = fex.CreateMessageFault();
                if (fault.HasDetail)
                {
                    var error = fault.GetDetail<ProviderIndividual.ServiceMessagesType>();
                    if (error.serviceMessage.Length > 0)
                        returnError = $"{error.serviceMessage[0].code}: {error.serviceMessage[0].reason}";
                }

                _logger.LogError($"HIService FaultException in ProviderIndividualSearch: {returnError}");
            }
            catch (Exception ex)
            {
                _logger.LogError($"HIService Exception in ProviderIndividualSearch: {ex.Message}");
                throw;
            }

            return new Tuple<ProviderIndividual.searchForProviderIndividualResponse, ProviderSearchForProviderIndividualClient>(response, client);
        }

        public async Task<Tuple<DirectoryForIndividual.searchHIProviderDirectoryForIndividualResponse, ProviderSearchHIProviderDirectoryForIndividualClient>> BasicDirectorySearch(HPIIRequestType requestType, string orgId)
        {
            var request = new DirectoryForIndividual.searchHIProviderDirectoryForIndividual()
            {
                hpiiNumber = $"{HIQualifiers.HPIIQualifier}{requestType.HPIINumber}"
            };

            return await PerformHIDirectorySearch(requestType.UserId, orgId, request, client => client.IdentifierSearch(request));
        }

        public async Task<Tuple<DirectoryForIndividual.searchHIProviderDirectoryForIndividualResponse, ProviderSearchHIProviderDirectoryForIndividualClient>> DirectoryByProviderSearch(HPIIRequestType requestType, string orgId)
        {
            var request = new DirectoryForIndividual.searchHIProviderDirectoryForIndividual
            {
                familyName = requestType.FamilyName,
                givenName = requestType.GivenName,
                sex = Enum.TryParse(requestType.Sex, out DirectoryForIndividual.SexType parsedGender) ? parsedGender : DirectoryForIndividual.SexType.M,
                sexSpecified = true,

                providerTypeCode = requestType.ProviderTypeCode,
                providerSpecialty = requestType.ProviderSpecialty,
                //providerSpecialisation = requestType.ProviderSpecialisation
            };

            return await PerformHIDirectorySearch(requestType.UserId, orgId, request, client => client.DemographicSearch(request));
        }


        public async Task<Tuple<DirectoryForIndividual.searchHIProviderDirectoryForIndividualResponse, ProviderSearchHIProviderDirectoryForIndividualClient>> DirectoryAustralianAddressSearch(HPIIRequestType requestType, string orgId)
        {
            var request = new DirectoryForIndividual.searchHIProviderDirectoryForIndividual
            {
                familyName = requestType.FamilyName,
                givenName = requestType.GivenName,
                sex = Enum.TryParse(requestType.Sex, out DirectoryForIndividual.SexType parsedGender) ? parsedGender : DirectoryForIndividual.SexType.M,
                sexSpecified = true
            };

            request.australianAddressCriteria = new DirectoryForIndividual.AustralianAddressCriteriaType()
            {
                streetName = requestType.Address.StreetName,
                streetNumber = string.IsNullOrWhiteSpace(requestType.Address.StreetNumber) ? null : requestType.Address.StreetNumber,
                lotNumber = string.IsNullOrWhiteSpace(requestType.Address.LotNumber) ? null : requestType.Address.LotNumber,
                suburb = requestType.Address.Suburb,
                state = Enum.TryParse(requestType.Address.State, out DirectoryForIndividual.StateType parsedState) ? parsedState : DirectoryForIndividual.StateType.ACT,
                postcode = requestType.Address.Postcode
            };

            return await PerformHIDirectorySearch(requestType.UserId, orgId, request, client => client.DemographicSearch(request));
        }

        private async Task<Tuple<DirectoryForIndividual.searchHIProviderDirectoryForIndividualResponse, ProviderSearchHIProviderDirectoryForIndividualClient>> PerformHIDirectorySearch(string userId, string orgId, DirectoryForIndividual.searchHIProviderDirectoryForIndividual request, Func<ProviderSearchHIProviderDirectoryForIndividualClient, DirectoryForIndividual.searchHIProviderDirectoryForIndividualResponse> searchFunc)
        {
            DirectoryForIndividual.searchHIProviderDirectoryForIndividualResponse response = null;
            ProviderSearchHIProviderDirectoryForIndividualClient client = null;
            try
            {
                client = await GetHISearchClient<ProviderSearchHIProviderDirectoryForIndividualClient, DirectoryForIndividual.ProductType, DirectoryForIndividual.QualifiedId>(userId, orgId);
                response = searchFunc(client);
            }
            catch (FaultException fex)
            {
                string returnError = "";
                var fault = fex.CreateMessageFault();
                if (fault.HasDetail)
                {
                    var error = fault.GetDetail<DirectoryForIndividual.ServiceMessagesType>();
                    if (error.serviceMessage.Length > 0)
                        returnError = $"{error.serviceMessage[0].code}: {error.serviceMessage[0].reason}";
                }

                _logger.LogError($"HIService FaultException in DirectoryIndividualSearch: {returnError}");
            }
            catch (Exception ex)
            {
                _logger.LogError($"HIService Exception in DirectoryIndividualSearch: {ex.Message}");
                throw;
            }

            return new Tuple<DirectoryForIndividual.searchHIProviderDirectoryForIndividualResponse, ProviderSearchHIProviderDirectoryForIndividualClient>(response, client);
        }

    }
}
