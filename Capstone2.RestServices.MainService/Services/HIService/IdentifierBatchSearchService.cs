﻿using Capstone2.Framework.Business.Common;
using Capstone2.Framework.RestApi.Utility;
using Capstone2.RestServices.HIService.Interfaces;
using Capstone2.RestServices.HIService.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Nehta.VendorLibrary.Common;
using Nehta.VendorLibrary.HI;
using Nehta.VendorLibrary.HI.Common;
using System;
using System.Collections.Generic;
using System.ServiceModel;
using System.Threading.Tasks;
using BatchAsync = nehta.mcaR3.ConsumerSearchIHIBatchAsync;

namespace Capstone2.RestServices.HIService.Services
{
    public class IdentifierBatchSearchService : BaseSearchService, IIdentifierBatchSearchService
    {
        private IKeyVaultHelper _keyVaultHelper;
        private readonly IConfiguration _configuration;
        private readonly ILogger<IdentifierBatchSearchService> _logger;
        private readonly HIServiceSettings _hiServiceSettings;
        private IDistributedCacheHelper _redisCache;
        public IdentifierBatchSearchService(ILogger<IdentifierBatchSearchService> logger, IOptions<HIServiceSettings> hiServiceSettings, IKeyVaultHelper keyVaultHelper, IConfiguration configuration, IDistributedCacheHelper cache) : base(hiServiceSettings, keyVaultHelper, configuration, cache)
        {
            _logger = logger;
        }

        public async Task<Tuple<BatchAsync.submitSearchIHIBatchResponse1, ConsumerSearchIHIBatchAsyncClient>> SubmitBatchSearchAsync(IHIBatchSearchRequest requestType, string orgId)
        {
            var batchRequest = new List<CommonSearchIHIRequestType>();

            requestType.ihiNumber.ForEach(data =>
            {
                batchRequest.AddBasicSearch(Guid.NewGuid().ToString(), new CommonSearchIHI()
                {
                    ihiNumber = $"{HIQualifiers.IHIQualifier}{data.IHINumber}",
                    familyName = data.FamilyName,
                    givenName = string.IsNullOrWhiteSpace(data.GivenName) ? null : data.GivenName,
                    dateOfBirth = data.DateOfBirth,
                    sex = Enum.TryParse(data.Sex, out CommonSexType parsedGender) ? parsedGender : CommonSexType.M
                });
            });

            requestType.medicareCardNumber.ForEach(data =>
            {
                batchRequest.AddBasicMedicareSearch(Guid.NewGuid().ToString(), new CommonSearchIHI()
                {
                    medicareCardNumber = data.MedicareCardNumber,
                    familyName = data.FamilyName,
                    givenName = string.IsNullOrWhiteSpace(data.GivenName) ? null : data.GivenName,
                    dateOfBirth = data.DateOfBirth,
                    sex = Enum.TryParse(data.Sex, out CommonSexType parsedGender) ? parsedGender : CommonSexType.M
                });
            });

            requestType.dvaFileNumber.ForEach(data =>
            {
                batchRequest.AddBasicDvaSearch(Guid.NewGuid().ToString(), new CommonSearchIHI()
                {
                    dvaFileNumber = data.DVAFileNumber,
                    familyName = data.FamilyName,
                    givenName = string.IsNullOrWhiteSpace(data.GivenName) ? null : data.GivenName,
                    dateOfBirth = data.DateOfBirth,
                    sex = Enum.TryParse(data.Sex, out CommonSexType parsedGender) ? parsedGender : CommonSexType.M
                });
            });

            requestType.unstructuredAddress.ForEach(data =>
            {
                batchRequest.AddAustraliaUnstructuredAddressSearch(Guid.NewGuid().ToString(), new CommonSearchIHI()
                {
                    familyName = data.FamilyName,
                    givenName = string.IsNullOrWhiteSpace(data.GivenName) ? null : data.GivenName,
                    dateOfBirth = data.DateOfBirth,
                    sex = Enum.TryParse(data.Sex, out CommonSexType parsedGender) ? parsedGender : CommonSexType.M,

                    australianUnstructuredStreetAddress = new CommonAustralianUnstructuredStreetAddressType()
                    {
                        addressLineOne = string.IsNullOrWhiteSpace(data.Address.AddressLineOne) ? null : data.Address.AddressLineOne,
                        addressLineTwo = string.IsNullOrWhiteSpace(data.Address.AddressLineTwo) ? null : data.Address.AddressLineTwo,
                        suburb = data.Address.Suburb,
                        state = Enum.TryParse(data.Address.State, out CommonStateType parsedState) ? parsedState : CommonStateType.ACT,
                        postcode = data.Address.Postcode
                    }

                });
            });

            requestType.postalAddress.ForEach(data =>
            {
                batchRequest.AddAustralianPostalAddressSearch(Guid.NewGuid().ToString(), new CommonSearchIHI()
                {
                    familyName = data.FamilyName,
                    givenName = string.IsNullOrWhiteSpace(data.GivenName) ? null : data.GivenName,
                    dateOfBirth = data.DateOfBirth,
                    sex = Enum.TryParse(data.Sex, out CommonSexType parsedGender) ? parsedGender : CommonSexType.M,

                    australianPostalAddress = new CommonAustralianPostalAddressType()
                    {
                        postalDeliveryGroup = new CommonPostalDeliveryGroupType()
                        {
                            postalDeliveryType = Enum.TryParse(data.Address.PostalDeliveryType, out CommonPostalDeliveryType parsedPDTypeCode)
                    ? parsedPDTypeCode : CommonPostalDeliveryType.CMA
                        },
                        suburb = data.Address.Suburb,
                        state = Enum.TryParse(data.Address.State, out CommonStateType parsedState) ? parsedState : CommonStateType.ACT,
                        postcode = data.Address.Postcode
                    }

                });
            });

            requestType.streetAddress.ForEach(data =>
            {
                batchRequest.AddAustralianStreetAddressSearch(Guid.NewGuid().ToString(), new CommonSearchIHI()
                {
                    familyName = data.FamilyName,
                    givenName = string.IsNullOrWhiteSpace(data.GivenName) ? null : data.GivenName,
                    dateOfBirth = data.DateOfBirth,
                    sex = Enum.TryParse(data.Sex, out CommonSexType parsedGender) ? parsedGender : CommonSexType.M,

                    australianStreetAddress = new CommonAustralianStreetAddressType()
                    {
                        streetName = data.Address.StreetName,
                        streetNumber = string.IsNullOrWhiteSpace(data.Address.StreetNumber) ? null : data.Address.StreetNumber,
                        lotNumber = string.IsNullOrWhiteSpace(data.Address.LotNumber) ? null : data.Address.LotNumber,
                        suburb = data.Address.Suburb,
                        state = Enum.TryParse(data.Address.State, out CommonStateType parsedState) ? parsedState : CommonStateType.ACT,
                        postcode = data.Address.Postcode
                    }
                });
            });

            return await PerformBatchHISearchAsync(requestType.Id, orgId, batchRequest, client => client.SubmitSearchIHIBatch(batchRequest));
        }

        private async Task<Tuple<BatchAsync.submitSearchIHIBatchResponse1, ConsumerSearchIHIBatchAsyncClient>> PerformBatchHISearchAsync(string userId, string orgId, List<CommonSearchIHIRequestType> request, Func<ConsumerSearchIHIBatchAsyncClient, BatchAsync.submitSearchIHIBatchResponse1> searchFunc)
        {
            BatchAsync.submitSearchIHIBatchResponse1 response = null;
            ConsumerSearchIHIBatchAsyncClient client = null;
            try
            {
                client = await GetHISearchClient<ConsumerSearchIHIBatchAsyncClient, BatchAsync.ProductType, BatchAsync.QualifiedId>(userId, orgId);
                response = searchFunc(client);
            }
            catch (FaultException fex)
            {
                string returnError = "";
                var fault = fex.CreateMessageFault();
                if (fault.HasDetail)
                {
                    var error = fault.GetDetail<BatchAsync.ServiceMessagesType>();
                    if (error.serviceMessage.Length > 0)
                        returnError = $"{error.serviceMessage[0].code}: {error.serviceMessage[0].reason}";
                }

                _logger.LogError($"SubmitBatchAsync HIService FaultException: {returnError}");
            }
            catch (Exception ex)
            {
                _logger.LogError($"SubmitBatchAsync HIService Exception: {ex.Message}");
                throw;
            }

            return new Tuple<BatchAsync.submitSearchIHIBatchResponse1, ConsumerSearchIHIBatchAsyncClient>(response, client);
        }

        public async Task<Tuple<BatchAsync.getSearchIHIBatchStatusResponse1, ConsumerSearchIHIBatchAsyncClient>> GetSearchIHIBatchStatus(IHIBatchSearchStatus requestType, string orgId)
        {
            return await PerformSearchIHIBatchStatus(requestType.Id, orgId, client => client.GetSearchIHIBatchStatus(requestType.BatchIdentifier));
        }

        private async Task<Tuple<BatchAsync.getSearchIHIBatchStatusResponse1, ConsumerSearchIHIBatchAsyncClient>> PerformSearchIHIBatchStatus(string userId, string orgId, Func<ConsumerSearchIHIBatchAsyncClient, BatchAsync.getSearchIHIBatchStatusResponse1> searchFunc)
        {
            BatchAsync.getSearchIHIBatchStatusResponse1 response = null;
            ConsumerSearchIHIBatchAsyncClient client = null;
            try
            {
                client = await GetHISearchClient<ConsumerSearchIHIBatchAsyncClient, BatchAsync.ProductType, BatchAsync.QualifiedId>(userId, orgId);
                response = searchFunc(client);
            }
            catch (FaultException fex)
            {
                string returnError = "";
                var fault = fex.CreateMessageFault();
                if (fault.HasDetail)
                {
                    var error = fault.GetDetail<BatchAsync.ServiceMessagesType>();
                    if (error.serviceMessage.Length > 0)
                        returnError = $"{error.serviceMessage[0].code}: {error.serviceMessage[0].reason}";
                }

                _logger.LogError($"BatchStatus HIService FaultException: {returnError}");
            }
            catch (Exception ex)
            {
                _logger.LogError($"BatchStatus HIService Exception: {ex.Message}");
                throw;
            }

            return new Tuple<BatchAsync.getSearchIHIBatchStatusResponse1, ConsumerSearchIHIBatchAsyncClient>(response, client);
        }

        public async Task<Tuple<BatchAsync.retrieveSearchIHIBatchResponse1, ConsumerSearchIHIBatchAsyncClient>> GetRetrieveSearchIHIBatch(IHIBatchSearchStatus requestType, string orgId)
        {
            return await PerformRetrieveSearchIHIBatch(requestType.Id, orgId, client => client.RetrieveSearchIHIBatch(requestType.BatchIdentifier));
        }

        private async Task<Tuple<BatchAsync.retrieveSearchIHIBatchResponse1, ConsumerSearchIHIBatchAsyncClient>> PerformRetrieveSearchIHIBatch(string userId, string orgId, Func<ConsumerSearchIHIBatchAsyncClient, BatchAsync.retrieveSearchIHIBatchResponse1> searchFunc)
        {
            BatchAsync.retrieveSearchIHIBatchResponse1 response = null;
            ConsumerSearchIHIBatchAsyncClient client = null;
            try
            {
                client = await GetHISearchClient<ConsumerSearchIHIBatchAsyncClient, BatchAsync.ProductType, BatchAsync.QualifiedId>(userId, orgId);
                response = searchFunc(client);
            }
            catch (FaultException fex)
            {
                string returnError = "";
                var fault = fex.CreateMessageFault();
                if (fault.HasDetail)
                {
                    var error = fault.GetDetail<BatchAsync.ServiceMessagesType>();
                    if (error.serviceMessage.Length > 0)
                        returnError = $"{error.serviceMessage[0].code}: {error.serviceMessage[0].reason}";
                }

                _logger.LogError($"RetrieveIHIBatch HIService FaultException: {returnError}");
            }
            catch (Exception ex)
            {
                _logger.LogError($"RetrieveIHIBatch HIService Exception: {ex.Message}");
                throw;
            }

            return new Tuple<BatchAsync.retrieveSearchIHIBatchResponse1, ConsumerSearchIHIBatchAsyncClient>(response, client);
        }

        public async Task<Tuple<BatchAsync.deleteSearchIHIBatchResponse1, ConsumerSearchIHIBatchAsyncClient>> DeleteSearchIHIBatch(IHIBatchSearchStatus requestType, string orgId)
        {
            return await PerformDeleteSearchIHIBatch(requestType.Id, orgId, client => client.DeleteSearchIHIBatch(requestType.BatchIdentifier));
        }

        private async Task<Tuple<BatchAsync.deleteSearchIHIBatchResponse1, ConsumerSearchIHIBatchAsyncClient>> PerformDeleteSearchIHIBatch(string userId, string orgId, Func<ConsumerSearchIHIBatchAsyncClient, BatchAsync.deleteSearchIHIBatchResponse1> searchFunc)
        {
            BatchAsync.deleteSearchIHIBatchResponse1 response = null;
            ConsumerSearchIHIBatchAsyncClient client = null;
            try
            {
                client = await GetHISearchClient<ConsumerSearchIHIBatchAsyncClient, BatchAsync.ProductType, BatchAsync.QualifiedId>(userId, orgId);
                response = searchFunc(client);
            }
            catch (FaultException fex)
            {
                string returnError = "";
                var fault = fex.CreateMessageFault();
                if (fault.HasDetail)
                {
                    var error = fault.GetDetail<BatchAsync.ServiceMessagesType>();
                    if (error.serviceMessage.Length > 0)
                        returnError = $"{error.serviceMessage[0].code}: {error.serviceMessage[0].reason}";
                }

                _logger.LogError($"DeleteIHIBatch HIService FaultException: {returnError}");
            }
            catch (Exception ex)
            {
                _logger.LogError($"DeleteIHIBatch HIService Exception: {ex.Message}");
                throw;
            }

            return new Tuple<BatchAsync.deleteSearchIHIBatchResponse1, ConsumerSearchIHIBatchAsyncClient>(response, client);
        }

    }
}
