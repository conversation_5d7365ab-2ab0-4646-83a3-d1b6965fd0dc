﻿using Capstone2.Framework.Business.Common;
using Capstone2.Framework.RestApi.Utility;
using Capstone2.RestServices.HIService.Interfaces;
using Capstone2.RestServices.HIService.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Nehta.VendorLibrary.Common;
using Nehta.VendorLibrary.HI;
using System;
using System.Collections.Generic;
using System.ServiceModel;
using System.Threading.Tasks;
using BatchAsync = nehta.mcaR51.ProviderBatchAsyncSearchForProviderOrganisation;

namespace Capstone2.RestServices.HIService.Services
{
    public class OrganisationBatchSearchService : BaseSearchService, IOrganisationBatchSearchService
    {
        private IKeyVaultHelper _keyVaultHelper;
        private readonly IConfiguration _configuration;
        private readonly ILogger<OrganisationBatchSearchService> _logger;
        private readonly HIServiceSettings _hiServiceSettings;
        private IDistributedCacheHelper _redisCache;
        public OrganisationBatchSearchService(ILogger<OrganisationBatchSearchService> logger, IOptions<HIServiceSettings> hiServiceSettings, IKeyVaultHelper keyVaultHelper, IConfiguration configuration, IDistributedCacheHelper cache) : base(hiServiceSettings, keyVaultHelper, configuration, cache)
        {
            _logger = logger;
        }

        public async Task<Tuple<BatchAsync.submitSearchForProviderOrganisationResponse, ProviderBatchAsyncSearchForProviderOrganisationClient>> SubmitBatchSearchAsync(HPIOBatchSearchRequest requestType, string orgId)
        {
            var batchRequest = new List<BatchAsync.BatchSearchForProviderOrganisationCriteriaType>();

            requestType.hpioNumber.ForEach(data =>
            {
                var searchData = new BatchAsync.BatchSearchForProviderOrganisationCriteriaType()
                {
                    requestIdentifier = Guid.NewGuid().ToString(),
                    searchForProviderOrganisation = new BatchAsync.searchForProviderOrganisation()
                    {
                        hpioNumber = $"{HIQualifiers.HPIOQualifier}{data.HPIONumber}"
                    }
                };

                batchRequest.Add(searchData);
            });

            return await PerformBatchHISearchAsync(requestType.Id, orgId, batchRequest, client => client.BatchSubmitProviderOrganisations(batchRequest.ToArray()));
        }

        private async Task<Tuple<BatchAsync.submitSearchForProviderOrganisationResponse, ProviderBatchAsyncSearchForProviderOrganisationClient>> PerformBatchHISearchAsync(string userId, string orgId, List<BatchAsync.BatchSearchForProviderOrganisationCriteriaType> request, Func<ProviderBatchAsyncSearchForProviderOrganisationClient, BatchAsync.submitSearchForProviderOrganisationResponse> searchFunc)
        {
            BatchAsync.submitSearchForProviderOrganisationResponse response = null;
            ProviderBatchAsyncSearchForProviderOrganisationClient client = null;
            try
            {
                client = await GetHISearchClient<ProviderBatchAsyncSearchForProviderOrganisationClient, BatchAsync.ProductType, BatchAsync.QualifiedId>(userId, orgId);
                response = searchFunc(client);
            }
            catch (FaultException fex)
            {
                string returnError = "";
                var fault = fex.CreateMessageFault();
                if (fault.HasDetail)
                {
                    var error = fault.GetDetail<BatchAsync.ServiceMessagesType>();
                    if (error.serviceMessage.Length > 0)
                        returnError = $"{error.serviceMessage[0].code}: {error.serviceMessage[0].reason}";
                }

                _logger.LogError($"BatchSubmitProviderOrganisation HIService FaultException: {returnError}");
            }
            catch (Exception ex)
            {
                _logger.LogError($"BatchSubmitProviderOrganisation HIService Exception: {ex.Message}");
                throw;
            }

            return new Tuple<BatchAsync.submitSearchForProviderOrganisationResponse, ProviderBatchAsyncSearchForProviderOrganisationClient>(response, client);
        }

        public async Task<Tuple<BatchAsync.retrieveSearchForProviderOrganisationResponse, ProviderBatchAsyncSearchForProviderOrganisationClient>> GetRetrieveHPIOBatchSearch(HPIOBatchSearchStatus requestType, string orgId)
        {
            return await PerformRetrieveHPIOBatchSearch(requestType.Id, orgId, client => client.BatchRetrieveProviderOrganisations
            (new BatchAsync.retrieveSearchForProviderOrganisation()
            {
                batchIdentifier = requestType.BatchIdentifier
            }
            ));
        }

        private async Task<Tuple<BatchAsync.retrieveSearchForProviderOrganisationResponse, ProviderBatchAsyncSearchForProviderOrganisationClient>> PerformRetrieveHPIOBatchSearch(string userId, string orgId, Func<ProviderBatchAsyncSearchForProviderOrganisationClient, BatchAsync.retrieveSearchForProviderOrganisationResponse> searchFunc)
        {
            BatchAsync.retrieveSearchForProviderOrganisationResponse response = null;
            ProviderBatchAsyncSearchForProviderOrganisationClient client = null;
            try
            {
                client = await GetHISearchClient<ProviderBatchAsyncSearchForProviderOrganisationClient, BatchAsync.ProductType, BatchAsync.QualifiedId>(userId, orgId);
                response = searchFunc(client);
            }
            catch (FaultException fex)
            {
                string returnError = "";
                var fault = fex.CreateMessageFault();
                if (fault.HasDetail)
                {
                    var error = fault.GetDetail<BatchAsync.ServiceMessagesType>();
                    if (error.serviceMessage.Length > 0)
                        returnError = $"{error.serviceMessage[0].code}: {error.serviceMessage[0].reason}";
                }

                _logger.LogError($"RetrieveHPIOBatchSearch HIService FaultException: {returnError}");
            }
            catch (Exception ex)
            {
                _logger.LogError($"RetrieveHPIOBatchSearch HIService Exception: {ex.Message}");
                throw;
            }

            return new Tuple<BatchAsync.retrieveSearchForProviderOrganisationResponse, ProviderBatchAsyncSearchForProviderOrganisationClient>(response, client);
        }
    }
}
