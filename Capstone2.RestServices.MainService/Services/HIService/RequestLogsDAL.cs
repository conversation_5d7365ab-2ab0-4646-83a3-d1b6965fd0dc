﻿using Capstone2.RestServices.HIService.Context;
using Capstone2.RestServices.HIService.Interfaces;
using Capstone2.RestServices.HIService.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.RestServices.HIService.Services
{
    public class RequestLogsDAL : IRequestLogsDAL
    {

        public readonly ReadOnlyHIServiceDBContext _readOnlyDbContext;
        public readonly UpdatableHIServiceDBContext _updatableDBContext;
        public RequestLogsDAL(ReadOnlyHIServiceDBContext readOnlyDbContext, UpdatableHIServiceDBContext updatableDBContext)
        {
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
        }

        public async Task<Guid> AddRequestLogs(RequestLogs requestLog)
        {
            requestLog.CreatedDate = DateTime.UtcNow;
            await _updatableDBContext.RequestLogs.AddAsync(requestLog);
            await _updatableDBContext.SaveChangesAsync();
            return requestLog.MessageId;
        }

        public async Task<Guid> UpdateRequestLogs(RequestLogs requestLog)
        {
            requestLog.ModifiedDate = DateTime.UtcNow;
            _updatableDBContext.RequestLogs.Update(requestLog);
            await _updatableDBContext.SaveChangesAsync();
            return requestLog.MessageId;
        }

        public async Task<Guid> UpdateRequestLogs(Guid messageId, string status, string response)
        {
            var requestLog = _updatableDBContext.RequestLogs.Find(messageId);
            requestLog.Status = status;
            requestLog.Response = response;
            requestLog.ModifiedDate = DateTime.UtcNow;
            _updatableDBContext.RequestLogs.Update(requestLog);
            await _updatableDBContext.SaveChangesAsync();
            return requestLog.MessageId;
        }

        public async Task<RequestLogs> GetRequestLogs(Guid id)
        {
            return await _readOnlyDbContext.RequestLogs.FirstOrDefaultAsync(x => x.MessageId == id);
        }
    }
}
