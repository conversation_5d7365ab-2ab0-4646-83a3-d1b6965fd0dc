﻿using Capstone2.Framework.RestApi.Context;
using Capstone2.RestServices.HIService.Context;
using Capstone2.RestServices.HIService.Interfaces;

namespace Capstone2.RestServices.HIService.Services
{
    public class IdentifierSearchDAL : IIdentifierSearchDAL
    {
        public readonly ReadOnlyDBMasterContext _readOnlyDbMasterContext;
        public readonly ReadOnlyHIServiceDBContext _readOnlyDbContext;
        public IdentifierSearchDAL(ReadOnlyDBMasterContext readOnlyDbMasterContext, ReadOnlyHIServiceDBContext readOnlyDbContext)
        {
            _readOnlyDbMasterContext = readOnlyDbMasterContext;
            _readOnlyDbContext = readOnlyDbContext;
        }

    }
}
