﻿using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.HIService.Common;
using Capstone2.RestServices.HIService.Interfaces;
using Capstone2.RestServices.HIService.Models;
using Capstone2.Shared.Models.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Nehta.VendorLibrary.HI;
using Newtonsoft.Json;
using System;
using System.Linq;
using System.Threading.Tasks;
using BatchAsync = nehta.mcaR51.ProviderBatchAsyncSearchForProviderOrganisation;
namespace Capstone2.RestServices.HIService.Services
{
    public class OrganisationBatchSearchBAL: IOrganisationBatchSearchBAL
    {
        public readonly IOrganisationBatchSearchService _organisationBatchSearchService;
        private readonly IRequestLogsBAL _requestLogsBAL;
        private readonly ILogger<OrganisationBatchSearchBAL> _logger;
        public OrganisationBatchSearchBAL(IOrganisationBatchSearchService organisationBatchSearchService, IRequestLogsBAL requestLogsBAL, ILogger<OrganisationBatchSearchBAL> logger)
        {
            _organisationBatchSearchService = organisationBatchSearchService;
            _requestLogsBAL = requestLogsBAL;
            _logger = logger;
        }

        public async Task<ApiResponse<HPIOBatchSubmitResponse<BatchAsync.ServiceMessagesType>>> SubmitBatchSearchAsync(HPIOBatchSearchRequest requestType, BaseHttpRequestContext baseHttpRequestContext)
        {
            string orgId = Convert.ToString(baseHttpRequestContext.OrgId);
            long requesterId = baseHttpRequestContext.UserId;
            requestType.Id = Convert.ToString(requesterId);
            return await SubmitBatchSearchResponseAsync(requestType, orgId, requesterId, _organisationBatchSearchService.SubmitBatchSearchAsync);
        }

        private async Task<ApiResponse<HPIOBatchSubmitResponse<BatchAsync.ServiceMessagesType>>> SubmitBatchSearchResponseAsync(HPIOBatchSearchRequest requestType, string orgId, long requesterId, Func<HPIOBatchSearchRequest, string, Task<Tuple<BatchAsync.submitSearchForProviderOrganisationResponse, ProviderBatchAsyncSearchForProviderOrganisationClient>>> hpioBatchSearchMethodAsync)
        {
            var requestLogs = new RequestLogs
            {
                Request = JsonConvert.SerializeObject(requestType),
                CreatedBy = requesterId,
                ModifiedBy = requesterId,
                ServiceName = ConstantsHelper.HI_Organisation_Batch_Submit_Async
            };

            var (searchResponse, requestClient) = await hpioBatchSearchMethodAsync(requestType, orgId);//Action:Batch Search result from HI service

            if (searchResponse != null)
            {
                var hISearchResponse = new HPIOBatchSubmitResponse<BatchAsync.ServiceMessagesType>
                {
                    BatchIdentifier = searchResponse.submitSearchForProviderOrganisationResult.batchIdentifier,
                    ServiceMessagesType = searchResponse.submitSearchForProviderOrganisationResult.serviceMessages
                };

                await LogRequestAndResponse(requestClient, requestLogs, ConstantsHelper.Status_Complete);

                return new ApiResponse<HPIOBatchSubmitResponse<BatchAsync.ServiceMessagesType>>
                {
                    Result = hISearchResponse,
                    StatusCode = StatusCodes.Status200OK,
                    Message = ConstantsHelper.Status_Success
                };
            }
            else
            {
                BatchAsync.ServiceMessagesType errorMsg = ConstantsHelper.DeserializeServiceMsgXml<BatchAsync.ServiceMessagesType>(requestClient.SoapMessages.SoapResponse);

                await LogRequestAndResponse(requestClient, requestLogs, ConstantsHelper.Status_Error);

                return new ApiResponse<HPIOBatchSubmitResponse<BatchAsync.ServiceMessagesType>>
                {
                    Result = new HPIOBatchSubmitResponse<BatchAsync.ServiceMessagesType>
                    {
                        ServiceMessagesType = errorMsg
                    },
                    StatusCode = StatusCodes.Status200OK,
                    Message = ConstantsHelper.Status_Failed
                };
            }
        }

        public async Task<ApiResponse<HPIOBatchRetrieveResponse<BatchAsync.ServiceMessagesType, BatchAsync.BatchSearchForProviderOrganisationResultType>>> GetRetrieveHPIOBatchSearch(HPIOBatchSearchStatus requestType, BaseHttpRequestContext baseHttpRequestContext)
        {
            string orgId = Convert.ToString(baseHttpRequestContext.OrgId);
            long requesterId = baseHttpRequestContext.UserId;
            requestType.Id = Convert.ToString(requesterId);
            return await GetRetrieveHPIOBatchResponse(requestType, orgId, requesterId, _organisationBatchSearchService.GetRetrieveHPIOBatchSearch);
        }

        private async Task<ApiResponse<HPIOBatchRetrieveResponse<BatchAsync.ServiceMessagesType, BatchAsync.BatchSearchForProviderOrganisationResultType>>> GetRetrieveHPIOBatchResponse(HPIOBatchSearchStatus requestType, string orgId, long requesterId, Func<HPIOBatchSearchStatus, string, Task<Tuple<BatchAsync.retrieveSearchForProviderOrganisationResponse, ProviderBatchAsyncSearchForProviderOrganisationClient>>> hpioBatchSearchMethod)
        {
            var requestLogs = new RequestLogs
            {
                Request = JsonConvert.SerializeObject(requestType),
                CreatedBy = requesterId,
                ModifiedBy = requesterId,
                ServiceName = ConstantsHelper.HI_Organisation_Batch_Retrieve
            };

            var (searchResponse, requestClient) = await hpioBatchSearchMethod(requestType, orgId);//Action:Batch Search result from HI service

            if (searchResponse != null)
            {
                var hISearchResponse = new HPIOBatchRetrieveResponse<BatchAsync.ServiceMessagesType, BatchAsync.BatchSearchForProviderOrganisationResultType>
                {
                    BatchIdentifier = searchResponse.retrieveSearchForProviderOrganisationResult?.batchIdentifier,
                    SearchHPIOResult = searchResponse.retrieveSearchForProviderOrganisationResult?.batchSearchForProviderOrganisationResult?.ToList(),
                    ServiceMessagesType = searchResponse.retrieveSearchForProviderOrganisationResult?.batchSearchForProviderOrganisationResult?.FirstOrDefault()?.searchForProviderOrganisationResult?.serviceMessages
                };

                await LogRequestAndResponse(requestClient, requestLogs, ConstantsHelper.Status_Complete);

                return new ApiResponse<HPIOBatchRetrieveResponse<BatchAsync.ServiceMessagesType, BatchAsync.BatchSearchForProviderOrganisationResultType>>
                {
                    Result = hISearchResponse,
                    StatusCode = StatusCodes.Status200OK,
                    Message = ConstantsHelper.Status_Success
                };
            }
            else
            {
                BatchAsync.ServiceMessagesType errorMsg = ConstantsHelper.DeserializeServiceMsgXml<BatchAsync.ServiceMessagesType>(requestClient.SoapMessages.SoapResponse);

                await LogRequestAndResponse(requestClient, requestLogs, ConstantsHelper.Status_Error);

                return new ApiResponse<HPIOBatchRetrieveResponse<BatchAsync.ServiceMessagesType, BatchAsync.BatchSearchForProviderOrganisationResultType>>
                {
                    Result = new HPIOBatchRetrieveResponse<BatchAsync.ServiceMessagesType, BatchAsync.BatchSearchForProviderOrganisationResultType>
                    {
                        ServiceMessagesType = errorMsg
                    },
                    StatusCode = StatusCodes.Status200OK,
                    Message = ConstantsHelper.Status_Failed
                };
            }
        }

        private async Task LogRequestAndResponse(dynamic requestClient, RequestLogs requestLogs, string status)
        {
            requestLogs.Request = ConstantsHelper.RemoveEncodingDeclaration(requestClient.SoapMessages.SoapRequest);
            requestLogs.Response = ConstantsHelper.RemoveEncodingDeclaration(requestClient.SoapMessages.SoapResponse);
            requestLogs.Status = status;
            using (requestClient)
            {
                await _requestLogsBAL.AddRequestLogs(requestLogs);
            }
        }
    }
}
