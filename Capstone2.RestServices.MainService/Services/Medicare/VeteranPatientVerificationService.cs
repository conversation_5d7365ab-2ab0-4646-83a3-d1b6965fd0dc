﻿using Capstone2.Common.MedicareOnline.OVVW;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.Medicare.Interfaces;
using Capstone2.RestServices.Medicare.Models;
using Capstone2.RestServices.Medicare.Utility;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Medicare.Services
{
    public class VeteranPatientVerificationService : IVeteranPatientVerificationService
    {
        private readonly ILogger<VeteranPatientVerificationService> _logger;
        public readonly AppSettings _appSettings;
        private readonly IRequestLogsBAL _requestLogsBAL;
        private readonly IAccessTokenHelper _accessTokenHelper;
        public VeteranPatientVerificationService(ILogger<VeteranPatientVerificationService> logger, IOptions<AppSettings> appSettings, IRequestLogsBAL requestLogsBAL, IAccessTokenHelper accessTokenHelper)
        {
            _logger = logger;
            _appSettings = appSettings.Value;
            _requestLogsBAL = requestLogsBAL;
            _accessTokenHelper = accessTokenHelper;
        }

        /// <summary>
        /// Request for Veteran Patient verification(OVVW)
        /// </summary>
        /// <param name="veteranVerificationRequestType">veteranVerificationRequestType</param>
        /// <param name="baseHttpRequestContext">baseHttpRequestContext</param>
        /// <returns>return veteran patient information of MedicareStatus/HealthFundStatus</returns>
        public async Task<ApiResponse<dynamic>> IsVeteranValid(VeteranVerificationRequestType veteranVerificationRequestType, BaseHttpRequestContext baseHttpRequestContext)
        {
            var apiResponse = new ApiResponse<dynamic>();
            var subscriberDetails = await _accessTokenHelper.GetMedicareAccessToken(baseHttpRequestContext, minorId: "");
            string medicareAccessToken = subscriberDetails.Item1; string subscriberMinorId = subscriberDetails.Item2;
            if (string.IsNullOrWhiteSpace(medicareAccessToken))
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = ConstantsHelper.TOKEN_ERROR_400;
                return apiResponse;
            }

            string endpoint = MedicareHelper.GetServiceEndPoint(ConstantsHelper.TYPECODE_OVVW);
            if (string.IsNullOrWhiteSpace(endpoint))
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = ConstantsHelper.DATA_ERROR_400;
                return apiResponse;
            }

            var httpClient = new System.Net.Http.HttpClient();
            var apiClient = new VeteranVerification(httpClient);
            string dhs_subjectId = "N/A"; string dhs_subjectIdType = "N/A";
            if (veteranVerificationRequestType.Patient.VeteranMembership is Object)
            {
                dhs_subjectId = veteranVerificationRequestType.Patient.VeteranMembership.VeteranNumber;
                dhs_subjectIdType = ConstantsHelper.SubjectId_Type_Veteran_FN;
            }

            string dhs_auditId = subscriberMinorId;
            string dhs_auditIdType = ConstantsHelper.AuditId_Type_Location;
            string dhs_correlationId = MedicareHelper.GenerateCorrelationId(dhs_auditId);
            string dhs_productId = _appSettings.ProdaApplicationName;
            string apiKey = _appSettings.MediCareClientAPIKey;
            string strReq = JsonConvert.SerializeObject(veteranVerificationRequestType);

            RequestLogs requestLogs = new RequestLogs();
            requestLogs.CorrelationId = dhs_correlationId;
            requestLogs.Request = strReq;
            requestLogs.ModifiedBy = baseHttpRequestContext.UserId;
            requestLogs.ServiceName = ConstantsHelper.TYPECODE_OVVW;
            Guid dhs_messageId = await _requestLogsBAL.AddRequestLogs(requestLogs);
            try
            {
                VeteranVerificationResponseType veteranVerificationResponseType = await apiClient.McpVeteranVerification(veteranVerificationRequestType, $"{"Bearer "}{medicareAccessToken}", dhs_auditId, dhs_subjectId, $"{"urn:uuid:"}{dhs_messageId}", dhs_auditIdType, $"{"urn:uuid:"}{dhs_correlationId}", dhs_productId, dhs_subjectIdType, apiKey, endpoint, _appSettings.MedicareBaseUrl).ConfigureAwait(false);
                if (veteranVerificationResponseType != null)
                {
                    string status = string.Empty;
                    string response = JsonConvert.SerializeObject(veteranVerificationResponseType);
                    apiResponse.Result = response; apiResponse.StatusCode = StatusCodes.Status200OK;
                    if (veteranVerificationResponseType.VeteranStatus is Object)
                    {
                        status = veteranVerificationResponseType.VeteranStatus.Status.Code.Value.ToString();
                    }
                    requestLogs.Response = response;
                    requestLogs.Status = status;
                    await _requestLogsBAL.UpdateRequestLogs(requestLogs);
                }
                return apiResponse;
            }
            catch (Exception e)
            {
                _logger.LogError("Medicare IsVeteranValid Error: " + e.Message);
                string resp = e.Message;
                if (typeof(ApiException<ServiceMessagesType>) == e.InnerException.GetType())
                {
                    ApiException<ServiceMessagesType> exp = (ApiException<ServiceMessagesType>)e.InnerException;
                    resp = JsonConvert.SerializeObject(exp.Result);
                }
                else if (typeof(ApiException<StatusCodeType>) == e.InnerException.GetType())
                {
                    ApiException<StatusCodeType> exp = (ApiException<StatusCodeType>)e.InnerException;
                    resp = JsonConvert.SerializeObject(exp.Result);
                }
                else if (typeof(ApiException) == e.InnerException.GetType())
                {
                    ApiException exp = (ApiException)e.InnerException;
                    resp = exp.Response;
                }
                else
                    resp = JsonConvert.SerializeObject(e.Message);
                await _requestLogsBAL.UpdateRequestLogs(dhs_messageId, ConstantsHelper.Status_Error, resp);
                apiResponse.Result = resp; apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                return apiResponse;
            }

        }

    }
}
