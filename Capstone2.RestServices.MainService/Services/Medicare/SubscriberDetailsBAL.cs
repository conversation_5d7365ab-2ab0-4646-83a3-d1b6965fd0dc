﻿using Capstone2.RestServices.Medicare.Interfaces;
using Capstone2.Shared.Models.Medicare;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Medicare.Services
{
    public class SubscriberDetailsBAL : ISubscriberDetailsBAL
    {
        private readonly ISubscriberDetailsDAL _subscriberDetailsDAL;
        public SubscriberDetailsBAL(ISubscriberDetailsDAL subscriberDetailsDAL)
        {
            _subscriberDetailsDAL = subscriberDetailsDAL;
        }
        public async Task<SubscriberDetails> GetSubscriberDetails(int orgId)
        {
            return await _subscriberDetailsDAL.GetSubscriberDetails(orgId);
        }

        public async Task<object> GetSubscriberDetailByUser(int orgId, long userId, string minorId)
        {
            return await _subscriberDetailsDAL.GetSubscriberDetailByUser(orgId, userId, minorId);
        }
    }
}
