﻿using Capstone2.Framework.RestApi;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.Medicare.Interfaces;
using Capstone2.RestServices.Medicare.Models;
using Capstone2.RestServices.Medicare.Utility;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Medicare.Services
{
    public class AccessTokenHelper : IAccessTokenHelper
    {
        private readonly ILogger<AccessTokenHelper> _logger;
        public readonly AppSettings _appSettings;
        private readonly ISubscriberDetailsBAL _subscriberDetailsBAL;
        public AccessTokenHelper(ILogger<AccessTokenHelper> logger, IOptions<AppSettings> appSettings, ISubscriberDetailsBAL subscriberDetailsBAL)
        {
            _logger = logger;
            _appSettings = appSettings.Value;
            _subscriberDetailsBAL = subscriberDetailsBAL;
        }
        public async Task<(string, string)> GetMedicareAccessToken(BaseHttpRequestContext baseHttpRequestContext, string minorId)
        {
            string medicareAccessToken = string.Empty;
            var subscriberObject = await _subscriberDetailsBAL.GetSubscriberDetailByUser(baseHttpRequestContext.OrgId, baseHttpRequestContext.UserId, minorId);
            var subscriberDetails = AnonymousParse.Cast(subscriberObject, new { AccessTokenExpiry = DateTime.MinValue, AccessToken = "", MinorId = "" });
            if (subscriberDetails != null && (subscriberDetails.AccessTokenExpiry != DateTime.MinValue && (subscriberDetails.AccessTokenExpiry - DateTime.UtcNow).TotalMinutes > 0) && !string.IsNullOrWhiteSpace(subscriberDetails.AccessToken))
            {
                medicareAccessToken = subscriberDetails.AccessToken;
            }
            else
            {
                string prodaTokenAPIUrl = string.Format("{0}/{1}", _appSettings.ApiUrls["ProdaServiceUrl"], ConstantsHelper.PRODA_MEDICARE_ACCESS_TOOKEN);
                var headerParam = new Dictionary<string, string> { { "MinorId", subscriberDetails.MinorId } };
                RestClient restClient = new RestClient(prodaTokenAPIUrl, null, baseHttpRequestContext.BearerToken, baseHttpRequestContext.InterServiceToken, headerParam);                
                var prodaResponse = await restClient.PostAsync<ApiResponse<dynamic>>(prodaTokenAPIUrl, null);
                if (prodaResponse != null && prodaResponse.StatusCode == StatusCodes.Status200OK && !string.IsNullOrWhiteSpace(prodaResponse.Result))
                {
                    JObject jsonResp = JObject.Parse(prodaResponse.Result);
                    medicareAccessToken = (string)jsonResp[ConstantsHelper.ACCESS_TYPE];
                }
            }
            return (medicareAccessToken, minorId = subscriberDetails.MinorId);
        }
    }
}
