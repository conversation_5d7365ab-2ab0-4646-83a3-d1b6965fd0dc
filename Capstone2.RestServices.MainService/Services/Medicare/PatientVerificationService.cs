﻿using Capstone2.Common.MedicareOnline.OPV;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.Medicare.Common;
using Capstone2.RestServices.Medicare.Interfaces;
using Capstone2.RestServices.Medicare.Models;
using Capstone2.RestServices.Medicare.Utility;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Medicare.Services
{
    public class PatientVerificationService : IPatientVerificationService
    {
        private readonly ILogger<PatientVerificationService> _logger;
        public readonly AppSettings _appSettings;
        private readonly IRequestLogsBAL _requestLogsBAL;
        private readonly IAccessTokenHelper _accessTokenHelper;
        public PatientVerificationService(ILogger<PatientVerificationService> logger, IOptions<AppSettings> appSettings, IRequestLogsBAL requestLogsBAL, IAccessTokenHelper accessTokenHelper)
        {
            _logger = logger;
            _appSettings = appSettings.Value;
            _requestLogsBAL = requestLogsBAL;
            _accessTokenHelper = accessTokenHelper;
        }

        /// <summary>
        /// Request for Patient verification(OPV)
        /// </summary>
        /// <param name="patientVerificationRequestType">patientVerificationRequestType</param>
        /// <param name="baseHttpRequestContext">baseHttpRequestContext</param>
        /// <returns>return patient information of MedicareStatus/HealthFundStatus</returns>
        public async Task<ApiResponse<dynamic>> IsMedicareValid(PatientVerificationRequestType patientVerificationRequestType, BaseHttpRequestContext baseHttpRequestContext)
        {
            var apiResponse = new ApiResponse<dynamic>();
            var subscriberDetails = await _accessTokenHelper.GetMedicareAccessToken(baseHttpRequestContext, minorId: "");
            string medicareAccessToken = subscriberDetails.Item1; string subscriberMinorId = subscriberDetails.Item2;
            if (string.IsNullOrWhiteSpace(medicareAccessToken))
            { apiResponse.StatusCode = StatusCodes.Status400BadRequest; apiResponse.Result = ConstantsHelper.TOKEN_ERROR_400; return apiResponse; }

            string endpoint = MedicareHelper.GetServiceEndPoint(patientVerificationRequestType.TypeCode);
            if (string.IsNullOrWhiteSpace(endpoint))
            { apiResponse.StatusCode = StatusCodes.Status400BadRequest; apiResponse.Result = ConstantsHelper.DATA_ERROR_400; return apiResponse; }

            var httpClient = new System.Net.Http.HttpClient();
            var apiClient = new Verification(httpClient);
            string dhs_subjectId = "N/A"; string dhs_subjectIdType = "N/A";
            if (patientVerificationRequestType.Patient.Medicare is Object)
            {
                bool valid = MedicareCommonValidations.MedicareValidityCheck(patientVerificationRequestType.Patient.Medicare.MemberNumber);
                if (valid)
                {
                    dhs_subjectId = patientVerificationRequestType.Patient.Medicare.MemberNumber;
                    dhs_subjectIdType = ConstantsHelper.SubjectId_Type_MediCard;
                }
                else if (string.IsNullOrWhiteSpace(patientVerificationRequestType.Patient.Medicare.MemberNumber))
                { apiResponse.StatusCode = StatusCodes.Status400BadRequest; apiResponse.Result = ConstantsHelper.INVALID_MEDICARE_MEMBER_NUMBER_400; return apiResponse; }
                else
                { apiResponse.StatusCode = StatusCodes.Status400BadRequest; apiResponse.Result = ConstantsHelper.INVALID_MEDICARE_NUMBER_400; return apiResponse; }

                if (!patientVerificationRequestType.TypeCode.Equals(ConstantsHelper.TYPECODE_PVF) && string.IsNullOrWhiteSpace(patientVerificationRequestType.Patient.Medicare.MemberRefNumber))
                { apiResponse.StatusCode = StatusCodes.Status400BadRequest; apiResponse.Result = ConstantsHelper.INVALID_MEDICARE_MEMBER_REFNUMBER_400; return apiResponse; }
            }
            else if (patientVerificationRequestType.Patient.HealthFund is Object)
            {
                dhs_subjectId = patientVerificationRequestType.Patient.HealthFund.MemberNumber;
                dhs_subjectIdType = ConstantsHelper.SubjectId_Type_HealthFund;
            }
            else { apiResponse.StatusCode = StatusCodes.Status400BadRequest; apiResponse.Result = ConstantsHelper.DATA_ERROR_400; return apiResponse; }
            string dhs_auditId = subscriberMinorId;
            string dhs_auditIdType = ConstantsHelper.AuditId_Type_Location;
            string dhs_correlationId = MedicareHelper.GenerateCorrelationId(dhs_auditId);
            string dhs_productId = _appSettings.ProdaApplicationName;
            string apiKey = _appSettings.MediCareClientAPIKey;
            string strReq = JsonConvert.SerializeObject(patientVerificationRequestType);

            RequestLogs requestLogs = new RequestLogs();
            requestLogs.CorrelationId = dhs_correlationId;
            requestLogs.Request = strReq;
            requestLogs.ModifiedBy = baseHttpRequestContext.UserId;
            requestLogs.ServiceName = patientVerificationRequestType.TypeCode;
            Guid dhs_messageId = await _requestLogsBAL.AddRequestLogs(requestLogs);
            try
            {
                PatientVerificationResponseType patientVerificationResponseType = await apiClient.McpPatientVerification(patientVerificationRequestType, $"{"Bearer "}{medicareAccessToken}", dhs_auditId, dhs_subjectId, $"{"urn:uuid:"}{dhs_messageId}", dhs_auditIdType, $"{"urn:uuid:"}{dhs_correlationId}", dhs_productId, dhs_subjectIdType, apiKey, endpoint, _appSettings.MedicareBaseUrl).ConfigureAwait(false);
                if (patientVerificationResponseType != null)
                {
                    string status = string.Empty;
                    string response = JsonConvert.SerializeObject(patientVerificationResponseType);
                    apiResponse.Result = response; apiResponse.StatusCode = StatusCodes.Status200OK;
                    if (patientVerificationResponseType.MedicareStatus is Object && patientVerificationResponseType.HealthFundStatus is Object)
                    {
                        status = patientVerificationResponseType.MedicareStatus.Status.Code.Value.ToString() + ',' + patientVerificationResponseType.HealthFundStatus.Status.Code.Value.ToString();
                    }
                    else if (patientVerificationResponseType.MedicareStatus is Object)
                    {
                        status = patientVerificationResponseType.MedicareStatus.Status.Code.Value.ToString();
                    }
                    else if (patientVerificationResponseType.HealthFundStatus is Object)
                    {
                        status = patientVerificationResponseType.HealthFundStatus.Status.Code.Value.ToString();
                    }
                    requestLogs.Response = response;
                    requestLogs.Status = status;
                    await _requestLogsBAL.UpdateRequestLogs(requestLogs);
                    
                }
                return apiResponse;
            }

            catch (Exception e)
            {
                _logger.LogError("Medicare IsMedicareValid Error: " + e.Message);
                string resp = e.Message;
                if (typeof(ApiException<ServiceMessagesType>) == e.InnerException.GetType())
                {
                    ApiException<ServiceMessagesType> exp = (ApiException<ServiceMessagesType>)e.InnerException;
                    resp = JsonConvert.SerializeObject(exp.Result);
                }
                else if (typeof(ApiException<StatusCodeType>) == e.InnerException.GetType())
                {
                    ApiException<StatusCodeType> exp = (ApiException<StatusCodeType>)e.InnerException;
                    resp = JsonConvert.SerializeObject(exp.Result);
                }
                else if (typeof(ApiException) == e.InnerException.GetType())
                {
                    ApiException exp = (ApiException)e.InnerException;
                    resp = exp.Response;
                }
                else
                    resp = JsonConvert.SerializeObject(e.Message);
                await _requestLogsBAL.UpdateRequestLogs(dhs_messageId, ConstantsHelper.Status_Error, resp);
                //apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                //apiResponse.Result = string.Format(ConstantsHelper.CATCH_ERROR, resp);                
                apiResponse.Result = resp; apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                return apiResponse;
            }

        }

    }
}
