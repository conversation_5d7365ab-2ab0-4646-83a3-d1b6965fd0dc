﻿using Capstone2.Common.MedicareOnline.SDDClaim;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.Medicare.Common;
using Capstone2.RestServices.Medicare.Interfaces;
using Capstone2.RestServices.Medicare.Models;
using Capstone2.RestServices.Medicare.Utility;
using Microsoft.AspNetCore.Http;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Medicare.Services
{
    public class SDDClaimService : ISDDClaimService
    {
        private readonly ILogger<SDDClaimService> _logger;
        public readonly AppSettings _appSettings;
        private readonly IRequestLogsBAL _requestLogsBAL;
        private readonly IAccessTokenHelper _accessTokenHelper;
        public SDDClaimService(ILogger<SDDClaimService> logger, IOptions<AppSettings> appSettings, IRequestLogsBAL requestLogsBAL, IAccessTokenHelper accessTokenHelper)
        {
            _logger = logger;
            _appSettings = appSettings.Value;
            _requestLogsBAL = requestLogsBAL;
            _accessTokenHelper = accessTokenHelper;
        }

        /// <summary>
        /// Request for Same day delete for Patient Claim (SDDW)
        /// </summary>
        /// <param name="sameDayDeleteRequest">sameDayDeleteRequest</param>
        /// <param name="baseHttpRequestContext">baseHttpRequestContext</param>
        /// <param name="correlationId">correlationId</param>
        /// <returns>return patient information of claim for same day delete</returns>
        public async Task<ApiResponse<dynamic>> SubmitForSameDayDeleteClaim(SameDayDeleteRequestType sameDayDeleteRequest, BaseHttpRequestContext baseHttpRequestContext, string correlationId, string minorId)
        {
            var apiResponse = new ApiResponse<dynamic>();
            var subscriberDetails = await _accessTokenHelper.GetMedicareAccessToken(baseHttpRequestContext, minorId);
            string medicareAccessToken = subscriberDetails.Item1; string subscriberMinorId = subscriberDetails.Item2;
            //if (!string.IsNullOrWhiteSpace(minorId)) subscriberMinorId = minorId;

            if (string.IsNullOrWhiteSpace(medicareAccessToken))
            { apiResponse.StatusCode = StatusCodes.Status400BadRequest; apiResponse.Result = ConstantsHelper.TOKEN_ERROR_400; return apiResponse; }

            string endpoint = MedicareHelper.GetServiceEndPoint(ConstantsHelper.TYPECODE_SDDW);
            if (string.IsNullOrWhiteSpace(endpoint))
            { apiResponse.StatusCode = StatusCodes.Status400BadRequest; apiResponse.Result = ConstantsHelper.DATA_ERROR_400; return apiResponse; }

            string dhs_subjectId = string.Empty; string dhs_subjectIdType = string.Empty;
            if (sameDayDeleteRequest.SameDayDelete.Patient.Medicare is Object)
            {
                string medicareNumber = sameDayDeleteRequest.SameDayDelete.Patient.Medicare.MemberNumber;
                bool valid = MedicareCommonValidations.MedicareValidityCheck(medicareNumber);
                if (valid)
                {
                    dhs_subjectId = medicareNumber;
                    dhs_subjectIdType = ConstantsHelper.SubjectId_Type_MediCard;
                }
                else if (string.IsNullOrWhiteSpace(medicareNumber))
                { apiResponse.StatusCode = StatusCodes.Status400BadRequest; apiResponse.Result = ConstantsHelper.INVALID_MEDICARE_MEMBER_NUMBER_400; return apiResponse; }
                else
                { apiResponse.StatusCode = StatusCodes.Status400BadRequest; apiResponse.Result = ConstantsHelper.INVALID_MEDICARE_NUMBER_400; return apiResponse; }
            }
            else { apiResponse.StatusCode = StatusCodes.Status400BadRequest; apiResponse.Result = ConstantsHelper.DATA_ERROR_400; return apiResponse; }

            string dhs_auditId = subscriberMinorId;
            string dhs_auditIdType = ConstantsHelper.AuditId_Type_Location;
            string dhs_correlationId = correlationId;
            string dhs_productId = _appSettings.ProdaApplicationName;
            string apiKey = _appSettings.MediCareClientAPIKey;
            string strReq = JsonConvert.SerializeObject(sameDayDeleteRequest);

            RequestLogs requestLogs = new RequestLogs();
            requestLogs.CorrelationId = dhs_correlationId;
            requestLogs.Request = strReq;
            requestLogs.ModifiedBy = baseHttpRequestContext.UserId;
            requestLogs.ServiceName = ConstantsHelper.TYPECODE_SDDW;
            Guid dhs_messageId = await _requestLogsBAL.AddRequestLogs(requestLogs);

            var httpClient = new System.Net.Http.HttpClient();
            var apiClient = new SameDayDelete(httpClient);
            try
            {
                SameDayDeleteResponseType sameDayDeleteResponseType = await apiClient.McpSameDayDelete(sameDayDeleteRequest, $"{"Bearer "}{medicareAccessToken}", dhs_auditId, dhs_subjectId, $"{"urn:uuid:"}{dhs_messageId}", dhs_auditIdType, $"{"urn:uuid:"}{dhs_correlationId}", dhs_productId, dhs_subjectIdType, apiKey, endpoint, _appSettings.MedicareBaseUrl).ConfigureAwait(false);
                if (sameDayDeleteResponseType != null)
                {
                    string response = JsonConvert.SerializeObject(sameDayDeleteResponseType);
                    apiResponse.Result = response; apiResponse.TransactionId = dhs_correlationId; apiResponse.StatusCode = StatusCodes.Status200OK;
                    requestLogs.Response = response;
                    requestLogs.Status = sameDayDeleteResponseType.Status;
                    await _requestLogsBAL.UpdateRequestLogs(requestLogs);
                   
                }
                return apiResponse;
            }

            catch (Exception e)
            {
                _logger.LogError("Medicare SubmitForSameDayDeleteClaim Error: " + e.Message);
                string resp = e.Message;
                if (typeof(ApiException<ServiceMessagesType>) == e.InnerException.GetType())
                {
                    ApiException<ServiceMessagesType> exp = (ApiException<ServiceMessagesType>)e.InnerException;
                    resp = JsonConvert.SerializeObject(exp.Result);
                }
                else if (typeof(ApiException<StatusCodeType>) == e.InnerException.GetType())
                {
                    ApiException<StatusCodeType> exp = (ApiException<StatusCodeType>)e.InnerException;
                    resp = JsonConvert.SerializeObject(exp.Result);
                }
                else if (typeof(ApiException) == e.InnerException.GetType())
                {
                    ApiException exp = (ApiException)e.InnerException;
                    resp = exp.Response;
                }
                else if (typeof(SqlException) == e.InnerException.GetType())
                {
                    SqlException sqlExp = (SqlException)e.InnerException;
                    resp = sqlExp.Message;
                    apiResponse.StatusCode = StatusCodes.Status500InternalServerError;
                }
                else
                    resp = JsonConvert.SerializeObject(e.Message);
                await _requestLogsBAL.UpdateRequestLogs(dhs_messageId, ConstantsHelper.Status_Error, resp);
                apiResponse.Result = resp; apiResponse.StatusCode = StatusCodes.Status400BadRequest; apiResponse.ErrorCode = ConstantsHelper.ERROR_CODE_98;
                return apiResponse;
            }

        }
    }
}
