﻿//using Capstone2.RestServices.Medicare.Context;
using Capstone2.RestServices.Medicare.Context;
using Capstone2.RestServices.Medicare.Interfaces;
using Capstone2.Shared.Models.Enum;
using Capstone2.Shared.Models.Medicare;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Capstone2.Framework.RestApi.Context;

namespace Capstone2.RestServices.Medicare.Services
{
    public class SubscriberDetailsDAL : ISubscriberDetailsDAL
    {
        public readonly ReadOnlyDBMasterContext _readOnlyDbMasterContext;
        public readonly UpdatableDBMasterContext _updatableDBMasterContext;
        public readonly ReadOnlyMedicareDBContext _readOnlyDbContext;
        public SubscriberDetailsDAL(ReadOnlyDBMasterContext readOnlyDbMasterContext, UpdatableDBMasterContext updatableDBMasterContext, ReadOnlyMedicareDBContext readOnlyDbContext)
        {
            _readOnlyDbMasterContext = readOnlyDbMasterContext;
            _updatableDBMasterContext = updatableDBMasterContext;
            _readOnlyDbContext = readOnlyDbContext;
        }

        public async Task<SubscriberDetails> GetSubscriberDetails(int orgId)
        {
            return await _readOnlyDbMasterContext.SubscriberDetails.FirstOrDefaultAsync(x => x.OrgId == orgId);
        }

        public async Task<object> GetSubscriberDetailByUser(int orgId, long userId, string minorId)
        {
            var subscriberCompany = await (from userAssoc in _readOnlyDbContext.UserCompanyAssocs
                                           join company in _readOnlyDbContext.CompanyDetails on userAssoc.CompanyId equals company.Id
                                           where userAssoc.OrgId == orgId && userAssoc.UserDetailsId == userId
                                           && userAssoc.CompanyTypeId == (short)CompanyType.InternalCompany && userAssoc.StatusId.Value == (short)Status.Active
                                           && userAssoc.IsDefault == true
                                           select company).FirstOrDefaultAsync();
            if (subscriberCompany == null)
                return new { AccessTokenExpiry = DateTime.MinValue, AccessToken = "", MinorId = minorId };

            if (!string.IsNullOrWhiteSpace(minorId))
                subscriberCompany.MinorId = minorId;

            var subscriberDetails = await _readOnlyDbMasterContext.SubscriberDetails.Where(x => x.OrgId == orgId && x.MinorId == subscriberCompany.MinorId).FirstOrDefaultAsync();
            if (subscriberDetails != null && subscriberCompany != null)
                return new { AccessTokenExpiry = subscriberDetails.AccessTokenExpiry.Value, AccessToken = subscriberDetails.AccessToken, MinorId = subscriberDetails.MinorId };
            else
                return new { AccessTokenExpiry = DateTime.MinValue, AccessToken = "", MinorId = subscriberCompany.MinorId };
        }

    }
}
