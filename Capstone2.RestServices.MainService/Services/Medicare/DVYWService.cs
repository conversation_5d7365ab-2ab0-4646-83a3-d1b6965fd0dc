﻿using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.Medicare.Interfaces;
using Capstone2.RestServices.Medicare.Models;
using Capstone2.RestServices.Medicare.Utility;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Linq;
using System.Threading.Tasks;
using ServiceMessagesType = Capstone2.Common.MedicareOnline.DVAW.ServiceMessagesType;
using ApiException = Capstone2.Common.MedicareOnline.DVAW.ApiException;
using Capstone.Common.MedicareOnline.Veteran.PaymentReport;

namespace Capstone2.RestServices.Medicare.Services
{
    public class DVYWService : IDVYWService
    {
        private readonly ILogger<DVYWService> _logger;
        public readonly AppSettings _appSettings;
        private readonly IAccessTokenHelper _accessTokenHelper;
        private readonly IRequestLogsBAL _requestLogsBAL;
        public DVYWService(ILogger<DVYWService> logger, IOptions<AppSettings> appSettings, IRequestLogsBAL requestLogsBAL, IAccessTokenHelper accessTokenHelper)
        {
            _logger = logger;
            _appSettings = appSettings.Value;
            _accessTokenHelper = accessTokenHelper;
            _requestLogsBAL = requestLogsBAL;
        }

        /// <summary>
        /// Request for call Veteran Payment report
        /// </summary>
        /// <param name="bBSReportRequestType"></param>
        /// <param name="baseHttpRequestContext"></param>
        /// <returns>return information of veteran request</returns>
        public async Task<ApiResponse<dynamic>> CallVeteranPaymentReport(DVAReportRequestType dVAReportRequestType, BaseHttpRequestContext baseHttpRequestContext, string correlationId, string minorId)
        {
            var apiResponse = new ApiResponse<dynamic>();
            var subscriberDetails = await _accessTokenHelper.GetMedicareAccessToken(baseHttpRequestContext, minorId);
            string medicareAccessToken = subscriberDetails.Item1; string subscriberMinorId = subscriberDetails.Item2;
            //if (!string.IsNullOrWhiteSpace(minorId)) subscriberMinorId = minorId;
            if (string.IsNullOrWhiteSpace(medicareAccessToken))
            {
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.Result = ConstantsHelper.TOKEN_ERROR_400;
                return apiResponse;
            }
            string MedicareNo = string.Empty;
            var httpClient = new System.Net.Http.HttpClient();
            VeteranPaymentReport apiClient = new VeteranPaymentReport(httpClient);
            //Header values
            string dhs_subjectId = "N/A"; //Veteran Number
            string dhs_subjectIdType = "N/A";
            //Header values
            string dhs_auditIdType = ConstantsHelper.AuditId_Type_Location;
            string dhs_auditId = subscriberMinorId;
            string dhs_productId = _appSettings.ProdaApplicationName;
            string apiKey = _appSettings.MediCareClientAPIKey;
            string dhs_correlationId = correlationId;
            string strReq = JsonConvert.SerializeObject(dVAReportRequestType);

            RequestLogs requestLogs = new RequestLogs();
            //requestLogs.TenantId = Guid.Parse(tenantId);
            requestLogs.Request = strReq;
            requestLogs.CorrelationId = dhs_correlationId;
            requestLogs.ModifiedBy = baseHttpRequestContext.UserId;
            requestLogs.ServiceName = ConstantsHelper.SERVICE_NAME_DVYW;
            Guid dhs_messageId = await _requestLogsBAL.AddRequestLogs(requestLogs);
            try
            {
                DVAPaymentReportResponseType dVAPaymentReportResponseType = await apiClient.McpDvaPaymentReport(dVAReportRequestType, $"{"Bearer "}{medicareAccessToken}", dhs_auditId, dhs_subjectId, $"{"urn:uuid:"}{dhs_messageId}", dhs_auditIdType, $"{"urn:uuid:"}{dhs_correlationId}", dhs_productId, dhs_subjectIdType, apiKey, _appSettings.MedicareBaseUrl).ConfigureAwait(false);
                if (dVAPaymentReportResponseType != null)
                {
                    string response = JsonConvert.SerializeObject(dVAPaymentReportResponseType);
                    apiResponse.Result = response; apiResponse.StatusCode = StatusCodes.Status200OK;
                    string status = dVAPaymentReportResponseType.Status;

                    requestLogs.Response = response;
                    requestLogs.Status = status;
                    await _requestLogsBAL.UpdateRequestLogs(requestLogs);
                   
                }
                return apiResponse;
            }
            catch (Exception e)
            {
                _logger.LogError($"CallVeteranPaymentReport Error: {e.Message} {e.StackTrace}");
                string resp = e.Message;
                if (typeof(ApiException<ServiceMessagesType>) == e.InnerException.GetType())
                {
                    ApiException<ServiceMessagesType> exp = (ApiException<ServiceMessagesType>)e.InnerException;
                    resp = JsonConvert.SerializeObject(exp.Result);
                    apiResponse.StatusCode = exp.StatusCode;
                }
                else if (typeof(ApiException) == e.InnerException.GetType())
                {
                    ApiException exp = (ApiException)e.InnerException;
                    resp = exp.Response;
                    apiResponse.StatusCode = exp.StatusCode;
                }
                else
                    resp = JsonConvert.SerializeObject(e.Message);
                await _requestLogsBAL.UpdateRequestLogs(dhs_messageId, ConstantsHelper.Status_Error, resp);
                apiResponse.Result = resp;
                apiResponse.StatusCode = StatusCodes.Status400BadRequest;
                apiResponse.ErrorCode = 98;
                apiResponse.Errors.Add(resp);
                apiResponse.TransactionId = dhs_correlationId;
                return apiResponse;
            }
        }
    }
}
