﻿using Capstone2.Common.MedicareOnline.OEC;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.Medicare.Common;
using Capstone2.RestServices.Medicare.Interfaces;
using Capstone2.RestServices.Medicare.Models;
using Capstone2.RestServices.Medicare.Utility;
using Capstone2.Shared.Models.Entities;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Azure;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Identity.Client;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
namespace Capstone2.RestServices.Medicare.Services
{
    public class OnlineEligibilityCheckService : IOnlineEligibilityCheckService
    {
        private readonly ILogger<OnlineEligibilityCheckService> _logger;
        public readonly AppSettings _appSettings;
        private readonly IRequestLogsBAL _requestLogsBAL;
        private readonly IAccessTokenHelper _accessTokenHelper;
        public OnlineEligibilityCheckService(ILogger<OnlineEligibilityCheckService> logger, IOptions<AppSettings> appSettings, IRequestLogsBAL requestLogsBAL, IAccessTokenHelper accessTokenHelper)
        {
            _logger = logger;
            _appSettings = appSettings.Value;
            _requestLogsBAL = requestLogsBAL;
            _accessTokenHelper = accessTokenHelper;
        }

        /// <summary>
        /// Request for Patient Online Eligibility Check Claim(OEC)/(ECF)/(ECM)
        /// </summary>
        /// <param name="patientOECClaimRequest">patientOECClaimRequest</param>
        /// <param name="baseHttpRequestContext">baseHttpRequestContext</param>
        /// <returns>return patient OEC information</returns>
        public async Task<ApiResponse<dynamic>> SubmitPatientOECClaim(OnlineEligibilityCheckRequestType patientOECClaimRequest, BaseHttpRequestContext baseHttpRequestContext, string minorId)
        {
            var apiResponse = new ApiResponse<dynamic>();
            var subscriberDetails = await _accessTokenHelper.GetMedicareAccessToken(baseHttpRequestContext, minorId);
            string medicareAccessToken = subscriberDetails.Item1; string subscriberMinorId = subscriberDetails.Item2;
            //if (!string.IsNullOrWhiteSpace(minorId)) subscriberMinorId = minorId;

            if (string.IsNullOrWhiteSpace(medicareAccessToken))
            { apiResponse.StatusCode = StatusCodes.Status400BadRequest; apiResponse.Result = ConstantsHelper.TOKEN_ERROR_400; return apiResponse; }

            string typeCode = ConstantsHelper.TYPECODE_OECW;//Default
            if (patientOECClaimRequest.Claim is Object)
                typeCode = patientOECClaimRequest.Claim.TypeCode;
            string endpoint = MedicareHelper.GetServiceEndPoint(typeCode);
            if (string.IsNullOrWhiteSpace(endpoint))
            { apiResponse.StatusCode = StatusCodes.Status400BadRequest; apiResponse.Result = ConstantsHelper.DATA_ERROR_400; return apiResponse; }

            string dhs_subjectId = string.Empty; string dhs_subjectIdType = string.Empty;
            if (patientOECClaimRequest.Claim.Patient is Object)
            {
                string medicareNumber = string.Empty;
                if (typeCode.Equals(ConstantsHelper.TYPECODE_ECFW, StringComparison.OrdinalIgnoreCase) && patientOECClaimRequest.Claim.Patient.HealthFund is Object)
                {
                    medicareNumber = dhs_subjectId = patientOECClaimRequest.Claim.Patient.HealthFund.MemberNumber;
                    dhs_subjectIdType = ConstantsHelper.SubjectId_Type_HealthFund;
                    if (string.IsNullOrWhiteSpace(medicareNumber))
                    { apiResponse.StatusCode = StatusCodes.Status400BadRequest; apiResponse.Result = ConstantsHelper.DATA_ERROR_INVALID_HEALTH_FUND_400; return apiResponse; }
                }
                else if (patientOECClaimRequest.Claim.Patient.Medicare is Object)
                {
                    medicareNumber = patientOECClaimRequest.Claim.Patient.Medicare.MemberNumber;
                    dhs_subjectIdType = ConstantsHelper.SubjectId_Type_MediCard;
                    bool valid = !string.IsNullOrWhiteSpace(medicareNumber) ? MedicareCommonValidations.MedicareValidityCheck(medicareNumber) : false;
                    if (valid)
                    { dhs_subjectId = medicareNumber; }
                    else if (string.IsNullOrWhiteSpace(medicareNumber))
                    { apiResponse.StatusCode = StatusCodes.Status400BadRequest; apiResponse.Result = ConstantsHelper.INVALID_MEDICARE_MEMBER_NUMBER_400; return apiResponse; }
                    else
                    { apiResponse.StatusCode = StatusCodes.Status400BadRequest; apiResponse.Result = ConstantsHelper.INVALID_MEDICARE_NUMBER_400; return apiResponse; }
                }
                else
                { apiResponse.StatusCode = StatusCodes.Status400BadRequest; apiResponse.Result = ConstantsHelper.DATA_ERROR_INVALID_HEALTH_FUND_400; return apiResponse; }
            }
            else { apiResponse.StatusCode = StatusCodes.Status400BadRequest; apiResponse.Result = ConstantsHelper.DATA_ERROR_400; return apiResponse; }

            string dhs_auditId = subscriberMinorId;
            string dhs_auditIdType = ConstantsHelper.AuditId_Type_Location;
            string dhs_correlationId = MedicareHelper.GenerateCorrelationId(dhs_auditId);
            string dhs_productId = _appSettings.ProdaApplicationName;
            string apiKey = _appSettings.MediCareClientAPIKey;
            string strReq = JsonConvert.SerializeObject(patientOECClaimRequest);

            RequestLogs requestLogs = new RequestLogs();
            requestLogs.CorrelationId = dhs_correlationId;
            requestLogs.Request = strReq;
            requestLogs.ModifiedBy = baseHttpRequestContext.UserId;
            requestLogs.ServiceName = MedicareHelper.GetServiceName(typeCode);
            Guid dhs_messageId = await _requestLogsBAL.AddRequestLogs(requestLogs);
            try
            {
                var httpClient = new System.Net.Http.HttpClient(); var apiClient = new OnlineEligibilityCheck(httpClient);
                OnlineEligibilityCheckResponseType onlineEligibilityCheckResponseType = await apiClient.McpEligibilityCheck(patientOECClaimRequest, $"{"Bearer "}{medicareAccessToken}", dhs_auditId, dhs_subjectId, $"{"urn:uuid:"}{dhs_messageId}", dhs_auditIdType, $"{"urn:uuid:"}{dhs_correlationId}", dhs_productId, dhs_subjectIdType, apiKey, endpoint, _appSettings.MedicareBaseUrl).ConfigureAwait(false);
                if (onlineEligibilityCheckResponseType != null)
                {
                    string response = JsonConvert.SerializeObject(onlineEligibilityCheckResponseType);
                    apiResponse.Result = response; apiResponse.TransactionId = dhs_correlationId; apiResponse.StatusCode = StatusCodes.Status200OK;
                    requestLogs.Response = response;
                    requestLogs.Status = onlineEligibilityCheckResponseType.Status;
                    await _requestLogsBAL.UpdateRequestLogs(requestLogs);
                }
                return apiResponse;
            }

            catch (Exception e)
            {
                _logger.LogError("Medicare SubmitPatientOECClaim Error: " + e.Message);
                string resp = e.Message;
                if (typeof(ApiException<ServiceMessagesType>) == e.InnerException.GetType())
                {
                    ApiException<ServiceMessagesType> exp = (ApiException<ServiceMessagesType>)e.InnerException;
                    resp = JsonConvert.SerializeObject(exp.Result);
                }
                else if (typeof(ApiException<StatusCodeType>) == e.InnerException.GetType())
                {
                    ApiException<StatusCodeType> exp = (ApiException<StatusCodeType>)e.InnerException;
                    resp = JsonConvert.SerializeObject(exp.Result);
                }
                else if (typeof(ApiException) == e.InnerException.GetType())
                {
                    ApiException exp = (ApiException)e.InnerException;
                    resp = exp.Response;
                }
                else
                    resp = JsonConvert.SerializeObject(e.Message);
                await _requestLogsBAL.UpdateRequestLogs(dhs_messageId, ConstantsHelper.Status_Error, resp);
                apiResponse.Result = resp; apiResponse.StatusCode = StatusCodes.Status400BadRequest; apiResponse.TransactionId = dhs_correlationId;
                return apiResponse;
            }
        }

        public async Task<ApiResponse<OECTemplateTags>> GetOECTemplateTagsData(long invoiceDetailId, string transactionId, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<OECTemplateTags> response = new ApiResponse<OECTemplateTags>();
            OECTemplateTags oecTemplateTags = new OECTemplateTags();

            RequestLogs transactionDetails = await _requestLogsBAL.GetRequestLogsByTransactionID(transactionId);
            if(transactionDetails != null)
            {
                OnlineEligibilityCheckRequestType oecRequestType = Newtonsoft.Json.JsonConvert.DeserializeObject<OnlineEligibilityCheckRequestType>(transactionDetails.Request);
                OnlineEligibilityCheckResponseType oecResponse = Newtonsoft.Json.JsonConvert.DeserializeObject<OnlineEligibilityCheckResponseType>(transactionDetails.Response);

                

                if(oecResponse != null)
                {
                    oecTemplateTags.LocationId = oecResponse.MedicareClaimEstimation != null ? oecResponse.MedicareClaimEstimation.FundLocationId : "";
                    oecTemplateTags.AccountReferenceId = oecResponse.ClaimSummary != null ? oecResponse.ClaimSummary.AccountReferenceId : "";
                    if (oecResponse.HealthFundClaimEstimation != null)
                    {
                        if (oecResponse.HealthFundClaimEstimation.Explanation != null)
                        {
                            oecTemplateTags.Outcome = oecResponse.HealthFundClaimEstimation.Explanation.FirstOrDefault().Code;
                            oecTemplateTags.FundExplanationText = oecResponse.HealthFundClaimEstimation.Explanation.FirstOrDefault().Text;
                        }
                       
                        if (oecResponse.HealthFundClaimEstimation.HealthFundTable != null)
                        {
                            oecTemplateTags.TableScale = oecResponse.HealthFundClaimEstimation.HealthFundTable.TableScale;
                            oecTemplateTags.TableDescription = oecResponse.HealthFundClaimEstimation.HealthFundTable.TableDescription;
                        }
                       
                        if (oecResponse.HealthFundClaimEstimation.PatientExcessPayment != null)
                        {
                            oecTemplateTags.ExcessAmount = oecResponse.HealthFundClaimEstimation.PatientExcessPayment.Amount.HasValue ? oecResponse.HealthFundClaimEstimation.PatientExcessPayment.Amount.Value : 0;
                            oecTemplateTags.ExcessBonusAmount = oecResponse.HealthFundClaimEstimation.PatientExcessPayment.BonusAmount.HasValue ? oecResponse.HealthFundClaimEstimation.PatientExcessPayment.BonusAmount.Value : 0;
                            oecTemplateTags.ExcessAmountDescription = oecResponse.HealthFundClaimEstimation.PatientExcessPayment.AmountDescription;
                        }
                  
                        if (oecResponse.HealthFundClaimEstimation.PatientCoPayment != null)
                        {
                            oecTemplateTags.CoPaymentAmount = oecResponse.HealthFundClaimEstimation.PatientCoPayment.Amount.HasValue ? oecResponse.HealthFundClaimEstimation.PatientCoPayment.Amount.Value : 0;
                            oecTemplateTags.CoPaymentDayszRemaining = oecResponse.HealthFundClaimEstimation.PatientCoPayment.DaysRemaining.HasValue ? oecResponse.HealthFundClaimEstimation.PatientCoPayment.DaysRemaining.Value.ToString() : "";
                        }
                     
                        oecTemplateTags.FundReferenceID = oecResponse.HealthFundClaimEstimation.FundReferenceId;
                        oecTemplateTags.ExclusionDescription = oecResponse.HealthFundClaimEstimation.ExclusionDescription;
                        oecTemplateTags.FinancialStatus = oecResponse.HealthFundClaimEstimation.FinancialStatus;
                        oecTemplateTags.BenefitLimitations = oecResponse.HealthFundClaimEstimation.BenefitLimitations;

                        
                        if(oecRequestType != null && oecRequestType.Claim != null)
                        {
                            if(oecRequestType.Claim.Patient != null)
                            {
                                oecTemplateTags.PatientIRN = oecRequestType.Claim.Patient.Medicare != null ? oecRequestType.Claim.Patient.Medicare.MemberRefNumber : "";
                                oecTemplateTags.PatientMedicareNumber = oecRequestType.Claim.Patient.Medicare != null ? oecRequestType.Claim.Patient.Medicare.MemberNumber : "";
                            }
                        }

                        List<OECPaymentDetailsTags> paymentDetails = new List<OECPaymentDetailsTags>();
                        if(oecResponse.HealthFundClaimEstimation.MedicalEvent != null)
                        {
                            oecResponse.HealthFundClaimEstimation.MedicalEvent.ToList().ForEach(x =>
                            {

                                x.Service.ToList().ForEach(service =>
                                {
                                    OECPaymentDetailsTags payment = new OECPaymentDetailsTags();
                                    payment.DateOfService = service.DateOfService.Value.DateTime;
                                    payment.ItemNumber = long.Parse(service.ItemNumber);
                                    payment.ChargeAmount = service.ChargeAmount;
                                    payment.ServiceFundAssessmentCode = service.FundAssessmentCode;
                                    payment.FundBenefitAmount = service.Benefit.HasValue ? service.Benefit.Value : 0;
                                    
                                    if(service.Explanation != null)
                                    {
                                        payment.FundExplanationCode = service.Explanation.FirstOrDefault().Code;
                                    }
                                    
                                    if(oecResponse.MedicareClaimEstimation != null && oecResponse.MedicareClaimEstimation.MedicalEvent != null)
                                    {
                                        oecResponse.MedicareClaimEstimation.MedicalEvent.ToList().ForEach(e =>
                                        {
                                            var medicareService = e.Service.ToList().Where(s => s.ItemNumber == service.ItemNumber).FirstOrDefault();
                                            if(medicareService != null)
                                            {
                                                payment.MedicalExplanationCode = medicareService.ExplanationCode;
                                                payment.MedicareBenefitAmount = medicareService.Benefit.HasValue ? medicareService.Benefit.Value : 0;
                                                payment.ScheduleFee = medicareService.ScheduleFee.HasValue ? medicareService.ScheduleFee.Value : 0;
                                            }
                                        });
                                    }

                                    paymentDetails.Add(payment);
                                });
                            });
                        }
                        oecTemplateTags.OecPaymentDetails = paymentDetails;
                    }
                }

                response.StatusCode = StatusCodes.Status200OK;
                response.Result = oecTemplateTags;
                return response;
            }

            response.Message = "No Medicare OEC data found";
            response.StatusCode = StatusCodes.Status400BadRequest;
            response.Result = null;
            return response;
        }
    }
}
