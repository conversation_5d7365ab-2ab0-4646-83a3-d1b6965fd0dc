﻿using Capstone2.RestServices.Medicare.Context;
using Capstone2.RestServices.Medicare.Interfaces;
using Capstone2.RestServices.Medicare.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Medicare.Services
{
    public class RequestLogsDAL : IRequestLogsDAL
    {

        public readonly ReadOnlyMedicareDBContext _readOnlyDbContext;
        public readonly UpdatableMedicareDBContext _updatableDBContext;
        public RequestLogsDAL(ReadOnlyMedicareDBContext readOnlyDbContext, UpdatableMedicareDBContext updatableDBContext)
        {
            _readOnlyDbContext = readOnlyDbContext;
            _updatableDBContext = updatableDBContext;
        }

        public async Task<Guid> AddRequestLogs(RequestLogs requestLog)
        {
            requestLog.CreatedDate = DateTime.UtcNow;
            await _updatableDBContext.RequestLogs.AddAsync(requestLog);
            await _updatableDBContext.SaveChangesAsync();
            return requestLog.MessageId;
        }

        public async Task<Guid> UpdateRequestLogs(RequestLogs requestLog)
        {
            requestLog.ModifiedDate = DateTime.UtcNow;
            _updatableDBContext.RequestLogs.Update(requestLog);
            await _updatableDBContext.SaveChangesAsync();
            return requestLog.MessageId;
        }

        public async Task<Guid> UpdateRequestLogs(Guid messageId, string status, string response)
        {
           var requestLog = _updatableDBContext.RequestLogs.Find(messageId);
            requestLog.Status = status;
            requestLog.Response = response;
            requestLog.ModifiedDate = DateTime.UtcNow;
            _updatableDBContext.RequestLogs.Update(requestLog);
            await _updatableDBContext.SaveChangesAsync();
            return requestLog.MessageId;
        }

        public async Task<RequestLogs> GetRequestLogs(Guid id)
        {
            return await _readOnlyDbContext.RequestLogs.FirstOrDefaultAsync(x => x.MessageId == id);
        }

        public async Task<RequestLogs> GetRequestLogsByTransactionID(string transaction_id)
        {
            return await _readOnlyDbContext.RequestLogs.OrderByDescending(x=> x.CreatedDate).Where(w => w.CorrelationId == transaction_id).FirstOrDefaultAsync();
        }
    }
}
