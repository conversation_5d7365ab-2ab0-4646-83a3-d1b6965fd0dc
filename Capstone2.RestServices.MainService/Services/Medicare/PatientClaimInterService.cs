﻿using Capstone2.Common.MedicareOnline.PCI.General;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.Medicare.Common;
using Capstone2.RestServices.Medicare.Interfaces;
using Capstone2.RestServices.Medicare.Models;
using Capstone2.RestServices.Medicare.Utility;
using Capstone2.Shared.Models.Entities;
using Capstone2.Shared.Models.Enum;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AddressType = Capstone2.Common.MedicareOnline.PCI.General.AddressType;

namespace Capstone2.RestServices.Medicare.Services
{
    public class PatientClaimInterService : IPatientClaimInterService
    {
        private readonly ILogger<PatientClaimInterService> _logger;
        public readonly AppSettings _appSettings;
        private readonly IRequestLogsBAL _requestLogsBAL;
        private readonly IAccessTokenHelper _accessTokenHelper;
        public PatientClaimInterService(ILogger<PatientClaimInterService> logger, IOptions<AppSettings> appSettings, IRequestLogsBAL requestLogsBAL, IAccessTokenHelper accessTokenHelper)
        {
            _logger = logger;
            _appSettings = appSettings.Value;
            _requestLogsBAL = requestLogsBAL;
            _accessTokenHelper = accessTokenHelper;
        }

        /// <summary>
        /// Request for Patient Claim(PCI)
        /// </summary>
        /// <param name="patientClaimRequest">patientClaimRequest</param>
        /// <param name="serviceType">serviceType</param>
        /// <param name="baseHttpRequestContext">baseHttpRequestContext</param>
        /// <param name="correlationId">correlationId</param>
        /// <returns>return patient information of claim</returns>
        public async Task<ApiResponse<dynamic>> SubmitPatientClaimIGeneral(PatientClaimInteractiveRequestType patientClaimRequest, string serviceType, BaseHttpRequestContext baseHttpRequestContext, string correlationId, string minorId)
        {
            var apiResponse = new ApiResponse<dynamic>();
            var subscriberDetails = await _accessTokenHelper.GetMedicareAccessToken(baseHttpRequestContext, minorId);
            string medicareAccessToken = subscriberDetails.Item1; string subscriberMinorId = subscriberDetails.Item2;
            //if (!string.IsNullOrWhiteSpace(minorId)) subscriberMinorId = minorId;
            if (string.IsNullOrWhiteSpace(medicareAccessToken))
            { apiResponse.StatusCode = StatusCodes.Status400BadRequest; apiResponse.Result = ConstantsHelper.TOKEN_ERROR_400; return apiResponse; }

            //string typeCode = ConstantsHelper.TYPECODE_PCI_G;//Default            
            //if (patientClaimRequest.PatientClaimInteractive.Referral is Object)
            //    typeCode = patientClaimRequest.PatientClaimInteractive.Referral.TypeCode;

            string typeCode = $"PCI-{serviceType}";
            string endpoint = MedicareHelper.GetServiceEndPoint(typeCode);
            if (string.IsNullOrWhiteSpace(endpoint))
            { apiResponse.StatusCode = StatusCodes.Status400BadRequest; apiResponse.Result = ConstantsHelper.DATA_ERROR_400; return apiResponse; }

            var httpClient = new System.Net.Http.HttpClient();
            var apiClient = new PCIGeneral(httpClient);
            string dhs_subjectId = string.Empty; string dhs_subjectIdType = string.Empty;
            if (patientClaimRequest.PatientClaimInteractive.Patient.Medicare is Object)
            {
                string medicareNumber = patientClaimRequest.PatientClaimInteractive.Patient.Medicare.MemberNumber;
                bool valid = MedicareCommonValidations.MedicareValidityCheck(medicareNumber);
                if (valid)
                {
                    dhs_subjectId = medicareNumber;
                    dhs_subjectIdType = ConstantsHelper.SubjectId_Type_MediCard;
                }
                else if (string.IsNullOrWhiteSpace(medicareNumber))
                { apiResponse.StatusCode = StatusCodes.Status400BadRequest; apiResponse.Result = ConstantsHelper.INVALID_MEDICARE_MEMBER_NUMBER_400; return apiResponse; }
                else
                { apiResponse.StatusCode = StatusCodes.Status400BadRequest; apiResponse.Result = ConstantsHelper.INVALID_MEDICARE_NUMBER_400; return apiResponse; }
            }
            else { apiResponse.StatusCode = StatusCodes.Status400BadRequest; apiResponse.Result = ConstantsHelper.DATA_ERROR_400; return apiResponse; }

            string dhs_auditId = subscriberMinorId;
            string dhs_auditIdType = ConstantsHelper.AuditId_Type_Location;
            string dhs_correlationId = !string.IsNullOrWhiteSpace(correlationId) ? correlationId : MedicareHelper.GenerateCorrelationId(dhs_auditId);
            string dhs_productId = _appSettings.ProdaApplicationName;
            string apiKey = _appSettings.MediCareClientAPIKey;
            string strReq = JsonConvert.SerializeObject(patientClaimRequest);

            RequestLogs requestLogs = new RequestLogs();
            requestLogs.CorrelationId = dhs_correlationId;
            requestLogs.Request = strReq;
            requestLogs.ModifiedBy = baseHttpRequestContext.UserId;
            requestLogs.ServiceName = typeCode; //MedicareHelper.GetServiceName(typeCode);
            Guid dhs_messageId = await _requestLogsBAL.AddRequestLogs(requestLogs);
            try
            {
                PatientClaimInteractiveResponseType patientClaimInteractiveResponseType = await apiClient.McpPatientClaimInteractiveGeneral(patientClaimRequest, $"{"Bearer "}{medicareAccessToken}", dhs_auditId, dhs_subjectId, $"{"urn:uuid:"}{dhs_messageId}", dhs_auditIdType, $"{"urn:uuid:"}{dhs_correlationId}", dhs_productId, dhs_subjectIdType, apiKey, endpoint, _appSettings.MedicareBaseUrl).ConfigureAwait(false);
                if (patientClaimInteractiveResponseType != null)
                {
                    string status = ConstantsHelper.Status_Complete;
                    string response = JsonConvert.SerializeObject(patientClaimInteractiveResponseType);
                    apiResponse.Result = response; apiResponse.TransactionId = dhs_correlationId; apiResponse.StatusCode = StatusCodes.Status200OK;
                    if (patientClaimInteractiveResponseType.ClaimAssessment != null)
                    {
                        requestLogs.ClaimId = patientClaimInteractiveResponseType.ClaimAssessment.ClaimId;
                        status = patientClaimInteractiveResponseType.Status;
                    }
                    requestLogs.Response = response;
                    requestLogs.Status = status;
                    await _requestLogsBAL.UpdateRequestLogs(requestLogs);
                   
                }
                return apiResponse;
            }

            catch (Exception e)
            {
                _logger.LogError("Medicare SubmitPatientClaimIGeneral Error: " + e.Message);
                string resp = e.Message;
                if (typeof(ApiException<ServiceMessagesType>) == e.InnerException.GetType())
                {
                    ApiException<ServiceMessagesType> exp = (ApiException<ServiceMessagesType>)e.InnerException;
                    resp = JsonConvert.SerializeObject(exp.Result);
                }
                else if (typeof(ApiException<StatusCodeType>) == e.InnerException.GetType())
                {
                    ApiException<StatusCodeType> exp = (ApiException<StatusCodeType>)e.InnerException;
                    resp = JsonConvert.SerializeObject(exp.Result);
                }
                else if (typeof(ApiException) == e.InnerException.GetType())
                {
                    ApiException exp = (ApiException)e.InnerException;
                    resp = exp.Response;
                }
                else
                    resp = JsonConvert.SerializeObject(e.Message);
                await _requestLogsBAL.UpdateRequestLogs(dhs_messageId, ConstantsHelper.Status_Error, resp);
                apiResponse.Result = resp; apiResponse.TransactionId = dhs_correlationId; apiResponse.StatusCode = StatusCodes.Status400BadRequest; apiResponse.ErrorCode = ConstantsHelper.ERROR_CODE_98;
                return apiResponse;
            }
        }

        public async Task<ApiResponse<LodgmentAndStatementTemplateTags>> GetLodgmentAndStatementTags(long invoiceDetailsID, string transactionID, BaseHttpRequestContext baseHttpRequestContext)
        {
            ApiResponse<LodgmentAndStatementTemplateTags> lodgmentAndStatementTemplate = new ApiResponse<LodgmentAndStatementTemplateTags>();
            LodgmentAndStatementTemplateTags lodgmentandStatementTemplateTags = new LodgmentAndStatementTemplateTags();

            RequestLogs transactionDetails = await _requestLogsBAL.GetRequestLogsByTransactionID(transactionID);
            if (transactionDetails != null)
            {
               // _logger.LogError("Medicare GetRequestLogsByTransactionID finished --> MessageID : " + transactionDetails.MessageId);
                if (transactionDetails.Status == "MEDICARE_PENDED" || transactionDetails.Status == "MEDICARE_ASSESSED")
                {
                    PatientClaimInteractiveResponseType patientClaimInteractiveResponseType = Newtonsoft.Json.JsonConvert.DeserializeObject<PatientClaimInteractiveResponseType>(transactionDetails.Response);
                    PatientClaimInteractiveRequestType patientClaimInteractiveRequest = Newtonsoft.Json.JsonConvert.DeserializeObject<PatientClaimInteractiveRequestType>(transactionDetails.Request);
                    PatientClaimantDetails patientDetails = new PatientClaimantDetails();
                    //_logger.LogInformation("Medicare GetLodgmentAndStatementTags DeserializeObject success ");
                    //patient Details
                    patientDetails.FirstName = patientClaimInteractiveRequest.PatientClaimInteractive.Patient.Identity.GivenName;
                    patientDetails.Surname = patientClaimInteractiveRequest.PatientClaimInteractive.Patient.Identity.FamilyName;
                    patientDetails.DateOfBirth = patientClaimInteractiveRequest.PatientClaimInteractive.Patient.Identity.DateOfBirth.Value.ToString("dd/MM/yyyy");
                    patientDetails.MedicareCardNo = patientClaimInteractiveRequest.PatientClaimInteractive.Patient.Medicare.MemberNumber;
                    patientDetails.IRN = patientClaimInteractiveRequest.PatientClaimInteractive.Patient.Medicare.MemberRefNumber;
                    lodgmentandStatementTemplateTags.PatientDetails = patientDetails;
               
                    //ClaimantDetails
                    PatientClaimantDetails claimantDetails = new PatientClaimantDetails();
                    claimantDetails.FirstName = patientClaimInteractiveRequest.PatientClaimInteractive.Claimant.Identity.GivenName;
                    claimantDetails.Surname = patientClaimInteractiveRequest.PatientClaimInteractive.Claimant.Identity.FamilyName;
                    claimantDetails.MedicareCardNo = patientClaimInteractiveRequest.PatientClaimInteractive.Claimant.Medicare.MemberNumber;
                    claimantDetails.Address = GetAddress(patientClaimInteractiveRequest.PatientClaimInteractive.Claimant.ResidentialAddress);
                    claimantDetails.DateOfBirth = patientClaimInteractiveRequest.PatientClaimInteractive.Claimant.Identity.DateOfBirth.Value.ToString("dd/MM/yyyy");

                    if (patientClaimInteractiveRequest.PatientClaimInteractive.Claimant.ContactDetails != null)
                    {
                        claimantDetails.TelephoneNumber = patientClaimInteractiveRequest.PatientClaimInteractive.Claimant.ContactDetails.PhoneNumber;
                        claimantDetails.Email = patientClaimInteractiveRequest.PatientClaimInteractive.Claimant.ContactDetails.EmailAddress;
                    }
                    claimantDetails.IRN = patientClaimInteractiveRequest.PatientClaimInteractive.Claimant.Medicare.MemberRefNumber;
                    lodgmentandStatementTemplateTags.ClaimantDetails = claimantDetails;
                   // _logger.LogInformation("Medicare GetLodgmentAndStatementTags claimant success ");

                    DateTime? eventDate = transactionDetails.CreatedDate.HasValue ? transactionDetails.CreatedDate.Value : null;
                    //PaymentDetails
                    lodgmentandStatementTemplateTags.paymentDetails = new System.Collections.Generic.List<LodgmentStatementTemplatePaymentDetails>();
                    patientClaimInteractiveRequest.PatientClaimInteractive.MedicalEvent.ToList().ForEach(x =>
                    {
                        x.Service.ToList().ForEach(s =>
                        {
                            DateTime medEventDate = x.MedicalEventDate.Date; ;

                            if (!string.IsNullOrWhiteSpace(x.MedicalEventTime) )
                            {
                                string medTIme = x.MedicalEventTime.Substring(0, x.MedicalEventTime.IndexOf('+'));
                                TimeSpan eventTime = TimeSpan.Parse(medTIme);
                                medEventDate = medEventDate.Add(eventTime);
                            }


                            LodgmentStatementTemplatePaymentDetails payment = new LodgmentStatementTemplatePaymentDetails()
                            {
                                DateOfService = medEventDate,
                                Charges = string.IsNullOrEmpty(s.ChargeAmount) ? 0 : Decimal.Parse(s.ChargeAmount),
                                ItemNumber = string.IsNullOrEmpty(s.ItemNumber) ? 0 : long.Parse(s.ItemNumber),
                                PatientContribution = string.IsNullOrEmpty(s.PatientContribAmount) ? 0 : long.Parse(s.PatientContribAmount),
                                RSNCode = patientClaimInteractiveResponseType.ClaimAssessment.MedicalEvent.Where(m => m.Id == x.Id).FirstOrDefault().Service.FirstOrDefault(service => service.Id == s.Id).AssessmentCode,
                                Benefit = patientClaimInteractiveResponseType.ClaimAssessment.MedicalEvent.Where(m => m.Id == x.Id).FirstOrDefault().Service.FirstOrDefault(service => service.Id == s.Id).BenefitPaid,
                                HospitalInd = s.HospitalInd,
                                FieldQuantity=(string.IsNullOrWhiteSpace(s.FieldQuantity)?null:s.FieldQuantity)
                            };

                            List<string> itemCodes = new List<string>();
                            if (!string.IsNullOrWhiteSpace(s.RestrictiveOverrideCode))
                                itemCodes.Add(s.RestrictiveOverrideCode);
                            if (!string.IsNullOrWhiteSpace(s.AftercareOverrideInd))
                                itemCodes.Add("NNAC");
                            if (!string.IsNullOrWhiteSpace(s.DuplicateServiceOverrideInd))
                                itemCodes.Add("NDS");
                            if (!string.IsNullOrWhiteSpace(s.MultipleProcedureOverrideInd))
                                itemCodes.Add("MPO");
                           

                            if (s.SelfDeemedCode != null)
                            {                                
                               itemCodes.Add(s.SelfDeemedCode.ToUpper());                                
                            }

                            if (!string.IsNullOrWhiteSpace(s.Text)) itemCodes.Add(s.Text);
                            if (!string.IsNullOrWhiteSpace(s.TimeDuration))
                                itemCodes.Add(s.TimeDuration + "mins");

                            payment.Description = String.Join(",", itemCodes);

                            lodgmentandStatementTemplateTags.paymentDetails.Add(payment);
                        });

                        if (x.Service != null)
                            lodgmentandStatementTemplateTags.FacilityID = x.Service.FirstOrDefault().FacilityId;
                    });
                    lodgmentandStatementTemplateTags.TransactionStatus = patientClaimInteractiveResponseType.Status;
                    lodgmentandStatementTemplateTags.ServiceProviderNumber = patientClaimInteractiveRequest.PatientClaimInteractive.ServiceProvider != null ? patientClaimInteractiveRequest.PatientClaimInteractive.ServiceProvider.ProviderNumber : "";
                    lodgmentandStatementTemplateTags.PayeeProviderNumber = patientClaimInteractiveRequest.PatientClaimInteractive.PayeeProvider != null ? patientClaimInteractiveRequest.PatientClaimInteractive.PayeeProvider.ProviderNumber : "";

                    lodgmentandStatementTemplateTags.ACRF = patientClaimInteractiveRequest.PatientClaimInteractive.AccountReferenceId;
                    lodgmentandStatementTemplateTags.IsAccountFullyPaid = patientClaimInteractiveRequest.PatientClaimInteractive.AccountPaidInd;

                    lodgmentandStatementTemplateTags.ClaimReference = eventDate.HasValue ? eventDate.Value.ToString("dd/MM/yyyy h:mm tt") : "";

                    if (patientClaimInteractiveRequest.PatientClaimInteractive.Claimant.EftDetails != null)
                    {
                        lodgmentandStatementTemplateTags.BSBNumber = patientClaimInteractiveRequest.PatientClaimInteractive.Claimant.EftDetails.BsbCode;
                        lodgmentandStatementTemplateTags.AccountNumber = patientClaimInteractiveRequest.PatientClaimInteractive.Claimant.EftDetails.AccountNumber;
                        lodgmentandStatementTemplateTags.AccountName = patientClaimInteractiveRequest.PatientClaimInteractive.Claimant.EftDetails.AccountName;
                    }

                    if (patientClaimInteractiveRequest.PatientClaimInteractive.ReferralOverrideCode != null && patientClaimInteractiveRequest.PatientClaimInteractive.ReferralOverrideCode != "")
                    {
                        lodgmentandStatementTemplateTags.ReferralOverrideCode = patientClaimInteractiveRequest.PatientClaimInteractive.ReferralOverrideCode;
                    }
                    else
                    {
                        if (patientClaimInteractiveRequest.PatientClaimInteractive.Referral != null)
                        {
                            lodgmentandStatementTemplateTags.DateOfRequestReferral = patientClaimInteractiveRequest.PatientClaimInteractive.Referral.IssueDate.DateTime;

                            string periodCode = patientClaimInteractiveRequest.PatientClaimInteractive.Referral.PeriodCode != null ? patientClaimInteractiveRequest.PatientClaimInteractive.Referral.PeriodCode.ToLower() : "";

                            if (periodCode == "n")
                            {
                                lodgmentandStatementTemplateTags.PeriodOfReferral = patientClaimInteractiveRequest.PatientClaimInteractive.Referral.Period + "Months";
                            }
                            else
                            {
                                lodgmentandStatementTemplateTags.PeriodOfReferral = periodCode == "i" ? "Indefinite" : patientClaimInteractiveRequest.PatientClaimInteractive.Referral.PeriodCode;
                            }

                            if (patientClaimInteractiveRequest.PatientClaimInteractive.Referral.Provider != null)
                            {
                                lodgmentandStatementTemplateTags.RequestingReferringProviderNumber = patientClaimInteractiveRequest.PatientClaimInteractive.Referral.Provider.ProviderNumber;
                                lodgmentandStatementTemplateTags.RequestingReferringProviderName = patientClaimInteractiveRequest.PatientClaimInteractive.Referral.Provider.ProviderNumber;
                            }
                        }
                    }

                    if (patientClaimInteractiveRequest.PatientClaimInteractive.PayeeProvider != null)
                    {
                        lodgmentandStatementTemplateTags.PayeeProvider = patientClaimInteractiveRequest.PatientClaimInteractive.PayeeProvider.ProviderNumber;
                        //Will Get this data in patientService.
                        lodgmentandStatementTemplateTags.PayeeProviderNumber = patientClaimInteractiveRequest.PatientClaimInteractive.PayeeProvider.ProviderNumber;
                    }

                    lodgmentandStatementTemplateTags.LocationId = 0;
                    //ServiceLocation Missing
                    lodgmentandStatementTemplateTags.ServiceLocation = "ServiceLocation";
                    lodgmentAndStatementTemplate.Result = lodgmentandStatementTemplateTags;
                    lodgmentAndStatementTemplate.StatusCode = StatusCodes.Status200OK;
                    lodgmentAndStatementTemplate.Message = "Success";
                    return lodgmentAndStatementTemplate;
                }
                _logger.LogError("Could not fetch details as transaction status is Error");
                lodgmentAndStatementTemplate.Result = null;
                lodgmentAndStatementTemplate.StatusCode = StatusCodes.Status400BadRequest;
                lodgmentAndStatementTemplate.Message = "Could not fetch details as transaction status is Error";
                return lodgmentAndStatementTemplate;
            }
            _logger.LogError("Medicare GetLodgmentAndStatementTags transaction Id not found ");
            lodgmentAndStatementTemplate.Result = null;
            lodgmentAndStatementTemplate.Errors.Add("Medicare GetLodgmentAndStatementTags transaction Id not found");
            lodgmentAndStatementTemplate.StatusCode = StatusCodes.Status400BadRequest;
            lodgmentAndStatementTemplate.Message = "Medicare GetLodgmentAndStatementTags transaction Id not found ";
            return lodgmentAndStatementTemplate;

        }

        private string GetAddress(AddressType pAddress)
        {
            string address = string.Empty;
            if (pAddress != null)
            {
                address = (string.IsNullOrWhiteSpace(pAddress.AddressLineOne) ? string.Empty : pAddress.AddressLineOne + ",") + (string.IsNullOrWhiteSpace(pAddress.AddressLineTwo) ? string.Empty : pAddress.AddressLineTwo + ",") + (string.IsNullOrWhiteSpace(pAddress.Locality) ? string.Empty : pAddress.Locality + ",") +
                    (string.IsNullOrWhiteSpace(pAddress.Postcode) ? string.Empty : pAddress.Postcode );
            }
               // return pAddress.AddressLineOne + " " + pAddress.AddressLineTwo + " " + pAddress.Locality + ", " + pAddress.Postcode;
            
            return address;
        }
    }
}
