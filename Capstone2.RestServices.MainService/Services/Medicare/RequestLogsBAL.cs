﻿using Capstone2.RestServices.Medicare.Interfaces;
using Capstone2.RestServices.Medicare.Models;
using System;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Medicare.Services
{
    public class RequestLogsBAL : IRequestLogsBAL
    {
        private readonly IRequestLogsDAL _requestLogsDAL;
        public RequestLogsBAL(IRequestLogsDAL requestLogsDAL)
        {
            _requestLogsDAL = requestLogsDAL;
        }

        public async Task<Guid> AddRequestLogs(RequestLogs requestLog)
        {
            return await _requestLogsDAL.AddRequestLogs(requestLog);
        }

        public async Task<Guid> UpdateRequestLogs(RequestLogs requestLog)
        {
            return await _requestLogsDAL.UpdateRequestLogs(requestLog);
        }

        public async Task<Guid> UpdateRequestLogs(Guid messageId, string status, string response)
        {
            return await _requestLogsDAL.UpdateRequestLogs(messageId, status, response);
        }

        public async Task<RequestLogs> GetRequestLogs(Guid id)
        {
            return await _requestLogsDAL.GetRequestLogs(id);
        }

        public async Task<RequestLogs> GetRequestLogsByTransactionID(string transaction_id)
        {
            return await _requestLogsDAL.GetRequestLogsByTransactionID(transaction_id);
        }

    }
}
