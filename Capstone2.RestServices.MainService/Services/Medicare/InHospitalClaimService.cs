﻿using Capstone2.Common.MedicareOnline.IHCW;
using Capstone2.Framework.RestApi.Dtos;
using Capstone2.RestServices.MainService.Common;
using Capstone2.RestServices.Medicare.Interfaces;
using Capstone2.RestServices.Medicare.Models;
using Capstone2.RestServices.Medicare.Utility;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Medicare.Services
{
    public class InHospitalClaimService : IInHospitalClaimService
    {
        private readonly ILogger<InHospitalClaimService> _logger;
        public readonly AppSettings _appSettings;
        private readonly IRequestLogsBAL _requestLogsBAL;
        private readonly IAccessTokenHelper _accessTokenHelper;
        public InHospitalClaimService(ILogger<InHospitalClaimService> logger, IOptions<AppSettings> appSettings, IRequestLogsBAL requestLogsBAL, IAccessTokenHelper accessTokenHelper)
        {
            _logger = logger;
            _appSettings = appSettings.Value;
            _requestLogsBAL = requestLogsBAL;
            _accessTokenHelper = accessTokenHelper;
        }

        /// <summary>
        /// Request for In Hospital Claim(IHCW)
        /// </summary>
        /// <param name="inHospitalClaimRequest">inHospitalClaimRequest</param>
        /// <param name="baseHttpRequestContext">baseHttpRequestContext</param>
        /// <param name="correlationId">correlationId</param>
        /// <returns>return In Hospital information of claim</returns>
        public async Task<ApiResponse<dynamic>> SubmitInHospitalClaim(InHospitalClaimRequestType inHospitalClaimRequest, BaseHttpRequestContext baseHttpRequestContext, string minorId)
        {
            var apiResponse = new ApiResponse<dynamic>();
            var subscriberDetails = await _accessTokenHelper.GetMedicareAccessToken(baseHttpRequestContext, minorId);
            string medicareAccessToken = subscriberDetails.Item1; string subscriberMinorId = subscriberDetails.Item2;
            if (string.IsNullOrWhiteSpace(medicareAccessToken))
            { apiResponse.StatusCode = StatusCodes.Status400BadRequest; apiResponse.Result = ConstantsHelper.TOKEN_ERROR_400; return apiResponse; }

            string typeCode = ConstantsHelper.SERVICE_TYPECODE_IHCW;

            string endpoint = MedicareHelper.GetServiceEndPoint(typeCode);
            if (string.IsNullOrWhiteSpace(endpoint))
            { apiResponse.StatusCode = StatusCodes.Status400BadRequest; apiResponse.Result = ConstantsHelper.DATA_ERROR_400; return apiResponse; }

            string serviceName = MedicareHelper.GetServiceName(typeCode);
            if (string.IsNullOrWhiteSpace(serviceName))
            { apiResponse.StatusCode = StatusCodes.Status400BadRequest; apiResponse.Result = ConstantsHelper.DATA_ERROR_400; return apiResponse; }

            string dhs_subjectId = "N/A"; string dhs_subjectIdType = "N/A";
            if (inHospitalClaimRequest.Claim.PatientSummary.FundMembership is Object)
            {
                string membershipNumber = inHospitalClaimRequest.Claim.PatientSummary.FundMembership.MemberNumber;
                //bool valid = MedicareCommonValidations.MedicareValidityCheck(medicareNumber);
                dhs_subjectId = membershipNumber;
                dhs_subjectIdType = ConstantsHelper.SubjectId_Type_HealthFund;
                //if (valid)
                //{
                //    dhs_subjectId = medicareNumber;
                //    dhs_subjectIdType = ConstantsHelper.SubjectId_Type_MediCard;
                //}
                //else if (string.IsNullOrWhiteSpace(medicareNumber))
                //{ apiResponse.StatusCode = StatusCodes.Status400BadRequest; apiResponse.Result = ConstantsHelper.INVALID_MEDICARE_MEMBER_NUMBER_400; return apiResponse; }
                //else
                //{ apiResponse.StatusCode = StatusCodes.Status400BadRequest; apiResponse.Result = ConstantsHelper.INVALID_MEDICARE_NUMBER_400; return apiResponse; }
            }
            else { apiResponse.StatusCode = StatusCodes.Status400BadRequest; apiResponse.Result = ConstantsHelper.DATA_ERROR_400; return apiResponse; }

            string dhs_auditId = subscriberMinorId;
            string dhs_auditIdType = ConstantsHelper.AuditId_Type_Location;
            //string dhs_correlationId = !string.IsNullOrWhiteSpace(correlationId) ? correlationId : MedicareHelper.GenerateCorrelationId(dhs_auditId);
            string dhs_correlationId = MedicareHelper.GenerateCorrelationId(dhs_auditId);

            string dhs_productId = _appSettings.ProdaApplicationName;
            string apiKey = _appSettings.MediCareClientAPIKey;
            string strReq = JsonConvert.SerializeObject(inHospitalClaimRequest);

            RequestLogs requestLogs = new RequestLogs();
            requestLogs.CorrelationId = dhs_correlationId;
            requestLogs.ClaimId = inHospitalClaimRequest.Claim.Summary.AccountReferenceId;
            requestLogs.Request = strReq;
            requestLogs.ModifiedBy = baseHttpRequestContext.UserId;
            requestLogs.ServiceName = serviceName;
            Guid dhs_messageId = await _requestLogsBAL.AddRequestLogs(requestLogs);
            try
            {
                var httpClient = new System.Net.Http.HttpClient();
                var apiClient = new InHospitalClaim(httpClient, _appSettings.MedicareBaseUrl);

                InHospitalClaimResponseType inHospitalClaimResponse = await apiClient.McpInHospitalClaimPrivatAsync(inHospitalClaimRequest, 
                    $"{"Bearer "}{medicareAccessToken}", dhs_auditId, dhs_subjectId, $"{"urn:uuid:"}{dhs_messageId}", dhs_auditIdType, 
                    $"{"urn:uuid:"}{dhs_correlationId}", dhs_productId, dhs_subjectIdType, apiKey, endpoint, _appSettings.MedicareBaseUrl, 
                    CancellationToken.None);
                if (inHospitalClaimResponse != null)
                {
                    string response = JsonConvert.SerializeObject(inHospitalClaimResponse);
                    string claimId = inHospitalClaimRequest.Claim.Summary.AccountReferenceId;
                    apiResponse.Result = response; apiResponse.TransactionId = dhs_correlationId;
                    apiResponse.StatusCode = StatusCodes.Status200OK;
                    string status = inHospitalClaimResponse.Status;
                    if (inHospitalClaimResponse.FundAssessment != null)
                        claimId = inHospitalClaimResponse.FundAssessment.AccountReferenceId;
                    requestLogs.Response = response;
                    requestLogs.Status = status;
                    requestLogs.ClaimId = claimId;
                    await _requestLogsBAL.UpdateRequestLogs(requestLogs);
                }
                return apiResponse;
            }

            catch (Exception e)
            {
                _logger.LogError("Medicare SubmitInHospitalClaim Error: " + e.Message);
                string resp = e.Message;
                if (typeof(ApiException<ServiceMessagesType>) == e?.GetType())
                {
                    ApiException<ServiceMessagesType> exp = (ApiException<ServiceMessagesType>)e;
                    resp = JsonConvert.SerializeObject(exp.Result);
                }
                else if (typeof(ApiException<StatusCodeType>) == e?.GetType())
                {
                    ApiException<StatusCodeType> exp = (ApiException<StatusCodeType>)e;
                    resp = JsonConvert.SerializeObject(exp.Result);
                }
                else if (typeof(ApiException) == e?.GetType())
                {
                    ApiException exp = (ApiException)e;
                    resp = exp.Response;
                }
                else
                {
                    resp = JsonConvert.SerializeObject(e.Message);
                }
                    
                await _requestLogsBAL.UpdateRequestLogs(dhs_messageId, ConstantsHelper.Status_Error, resp);
                apiResponse.Result = resp; apiResponse.TransactionId = dhs_correlationId; apiResponse.StatusCode = StatusCodes.Status400BadRequest; apiResponse.ErrorCode = ConstantsHelper.ERROR_CODE_98;
                return apiResponse;
            }
        }
    }
}

