apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: finance-restservice-deployment-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: finance-restservice-deployment
  minReplicas: $(Min_Replicas)
  maxReplicas: $(Max_Replicas)
  metrics:      
  - type: Resource
    resource:
      name: memory
      target:
         type: Utilization
         averageUtilization: $(Memory_Utilization)
  - type: Resource
    resource:
      name: cpu
      target:
         type: Utilization
         averageUtilization: $(CPU_Utilization)
