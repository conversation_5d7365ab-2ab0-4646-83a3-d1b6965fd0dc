apiVersion: apps/v1
kind: Deployment
metadata:
  name: finance-restservice-deployment
  labels:
    app: finance-restservice-dep
spec:
  replicas: $(Replicas)
  selector:
    matchLabels:
      app: finance-restservice-pod
  template:
    metadata:
      labels:
        app: finance-restservice-pod
    spec:
      affinity:
       podAntiAffinity:
        requiredDuringSchedulingIgnoredDuringExecution:
        - labelSelector:
           matchExpressions:
           - key: app
             operator: In
             values:
             - finance-restservice-pod
          topologyKey: "kubernetes.io/hostname"
      hostIPC: false
      containers:
      - name: finance-restservice-container
        image: $(Acr_Server)/finance-service:$(Build.BuildNumber)
        imagePullPolicy: Always
        securityContext:
         runAsNonRoot: true
         runAsUser: 1000
         runAsGroup: 2000
         allowPrivilegeEscalation: false
         privileged: false
        #readOnlyRootFilesystem: true
         capabilities:
           drop: ["ALL"]
        resources:
          requests:
            cpu: $(Request_CPU)
            memory: $(Request_Memory)
          limits:
            cpu: $(Limit_CPU)
            memory: $(Limit_Memory)
        livenessProbe:
          httpGet:
            path: /finance/healthcheck
            port: 8080
          periodSeconds: 20
          failureThreshold: 2
          timeoutSeconds: 15
          initialDelaySeconds: 30
        readinessProbe:
          httpGet:
            path: /finance/healthcheck
            port: 8080
          periodSeconds: 20
          timeoutSeconds: 15
          initialDelaySeconds: 30
      imagePullSecrets:
      - name: dockerimgpullsecrets
---
apiVersion: v1
kind: Service
metadata:
  annotations:
    service.beta.kubernetes.io/azure-load-balancer-internal: "true"
  name: finance-restservice
  labels:
    run: finance-restservice-svc
spec:
  type: LoadBalancer
  loadBalancerIP: $(ILB_IP)
  ports:
  - port: 8095
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: finance-restservice-pod
