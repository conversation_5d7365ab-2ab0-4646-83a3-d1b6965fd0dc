using Jose;
using System;
using System.Collections.Generic;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;

namespace Capstone2.RestServices.Proda.Utility
{
    public class ProdaCertificateHelper : IProdaCertificateHelper
    {
        public byte[] GenerateRSACertificateBytes(string deviceName)
        {
            try
            {
                using (RSA parentRSA = RSA.Create(4096))
                using (RSA scopeRSA = RSA.Create(2048))
                {
                    byte[] pubKeyByte = scopeRSA.ExportRSAPublicKey();
                    string pubKeyStr = Convert.ToBase64String(pubKeyByte);
                    byte[] pvtKeyByte = scopeRSA.ExportRSAPrivateKey();
                    string pvtKeyStr = Convert.ToBase64String(pvtKeyByte);

                    CertificateRequest parentCertReq = new CertificateRequest(
                        "CN=Capstone Systems Certificate Authority",
                        parentRSA,
                        HashAlgorithmName.SHA256,
                        RSASignaturePadding.Pkcs1);
                    parentCertReq.CertificateExtensions.Add(new X509BasicConstraintsExtension(true, false, 0, true));
                    parentCertReq.CertificateExtensions.Add(new X509SubjectKeyIdentifierExtension(parentCertReq.PublicKey, false));
                    using (X509Certificate2 parentCert = parentCertReq.CreateSelfSigned(DateTimeOffset.UtcNow.AddDays(-45), DateTimeOffset.UtcNow.AddDays(365)))
                    {
                        CertificateRequest certRequest = new CertificateRequest("CN=" + deviceName + ",O=Capstone Systems,L=Canberra,S=ACT,C=AU",
                                                                    scopeRSA, HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1);

                        certRequest.CertificateExtensions.Add(new X509BasicConstraintsExtension(false, false, 0, false));

                        certRequest.CertificateExtensions.Add(new X509KeyUsageExtension(X509KeyUsageFlags.DigitalSignature | X509KeyUsageFlags.NonRepudiation, false));

                        certRequest.CertificateExtensions.Add(new X509EnhancedKeyUsageExtension(
                                    new OidCollection
                                    {
                                     new Oid("1.3.6.1.5.5.7.3.8")
                                    }, true));

                        certRequest.CertificateExtensions.Add(new X509SubjectKeyIdentifierExtension(certRequest.PublicKey, false));

                        using (X509Certificate2 x509Cert = certRequest.Create(parentCert, DateTimeOffset.UtcNow.AddDays(-1), DateTimeOffset.UtcNow.AddDays(90),
                            new byte[] { 1, 2, 3, 4 }))
                        {
                            using (X509Certificate2 certNew = x509Cert.CopyWithPrivateKey(scopeRSA))
                            {
                                return certNew.Export(X509ContentType.Pfx);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                //TODO:AI Log
                return null;
            }

        }
        public X509Certificate2 GetRSACertificate(byte[] rsaCertificate)
        {
            try
            {
                // Use EphemeralKeySet to avoid key container issues
                return new X509Certificate2(
                    rsaCertificate,
                    string.Empty,
                    X509KeyStorageFlags.EphemeralKeySet | X509KeyStorageFlags.Exportable);
            }
            catch (CryptographicException ex) when (ex.Message.Contains("Keyset does not exist"))
            {
                // Fallback approach for "Keyset does not exist" error
                try
                {
                    // Try with UserKeySet instead of MachineKeySet
                    return new X509Certificate2(
                        rsaCertificate,
                        string.Empty,
                        X509KeyStorageFlags.UserKeySet | X509KeyStorageFlags.Exportable);
                }
                catch (Exception fallbackEx)
                {
                    throw new InvalidOperationException($"Unable to load certificate. Original error: {ex.Message}, Fallback error: {fallbackEx.Message}", ex);
                }
            }
        }
        public string GeneratePublicKey(X509Certificate2 certificate, string deviceName, string activationCode, string orgRA)
        {
            RSA rsaPubKey = certificate.GetRSAPublicKey();
            RSAParameters rsp = rsaPubKey.ExportParameters(false);
            Dictionary<string, object> publicKeyDict = new Dictionary<string, object>()
            {
                {"kty", "RSA"},
                {"kid", deviceName},
                {"use", "sig"},
                {"alg", "RS256"},
                {"n", Jose.Base64Url.Encode(rsp.Modulus) },
                {"e", Jose.Base64Url.Encode(rsp.Exponent)}
            };

            return System.Text.Json.JsonSerializer.Serialize(new
            {
                orgId = orgRA,
                otac = activationCode,
                key = publicKeyDict
            }).ToString();
        }

        public string GenerateEncryptedToken(byte[] rsaCertificate, string prodaOrgRa, string deviceName, string prodaAudience, string medicareAudience)
        {
            X509Certificate2 certificate = GetRSACertificate(rsaCertificate);
            RSA rsaPubKey = certificate.GetRSAPrivateKey();
            var issueTime = DateTimeOffset.Now.ToUnixTimeSeconds();
            var expriryTime = DateTimeOffset.Now.ToUnixTimeSeconds() + 600;
            string token_aud = string.IsNullOrWhiteSpace(medicareAudience) ? prodaAudience : medicareAudience;
            var payload = new Dictionary<string, object>()
                    {
                        {"iss",prodaOrgRa},
                        {"sub", deviceName},
                        {"aud",prodaAudience },
                        {"token.aud", token_aud },
                        {"exp", expriryTime},
                        {"iat" ,issueTime }
                    };

            var extraHeader = new Dictionary<string, object>()
                    {
                        {"kid", deviceName}
                    };
            var encryptedToken = JWT.Encode(payload, rsaPubKey, JwsAlgorithm.RS256, extraHeader, null, null);
            return encryptedToken;
        }

    }
}
