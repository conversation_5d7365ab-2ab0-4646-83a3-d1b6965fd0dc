﻿using System;

namespace Capstone2.RestServices.Proda.Utility
{
    public class TokenHelper
    {
        public static DateTime GetCalculatedToeknExpiry(int tokenType, DateTime expiryDate, int expires_in)
        {
            DateTime tokenExpiryTime = DateTime.UtcNow;
            switch (tokenType)
            {
                case (int)TokenEnum.ACCESS_TOKEN_EXPIRY:
                    tokenExpiryTime.AddSeconds(0.8 * expires_in);
                    break;
                case (int)TokenEnum.KEY_EXPIRY:
                    tokenExpiryTime = expiryDate.ToUniversalTime().AddDays(-expires_in);
                    break;
                case (int)TokenEnum.DEVICE_EXPIRY:
                    tokenExpiryTime = expiryDate.ToUniversalTime().AddDays(-expires_in);
                    break;
                default:
                    break;
            }

            return tokenExpiryTime;
        }
    }

    public enum TokenEnum
    {
        DEVICE_EXPIRY = 0,
        KEY_EXPIRY = 1,
        ACCESS_TOKEN_EXPIRY = 2
    }

}
