﻿using System.Security.Cryptography.X509Certificates;

namespace Capstone2.RestServices.Proda.Utility
{
    public interface IProdaCertificateHelper
    {
        byte[] GenerateRSACertificateBytes(string deviceName);
        X509Certificate2 GetRSACertificate(byte[] rsaCertificate);
        string GeneratePublicKey(X509Certificate2 certificate, string deviceName, string activationCode, string orgRA);
        string GenerateEncryptedToken(byte[] rsaCertificate, string prodaOrgRa, string deviceName, string prodaAudience, string medicareAudience = "");
    }
}
