﻿using Azure;
using Azure.Identity;
using Azure.Security.KeyVault.Certificates;
using Azure.Security.KeyVault.Secrets;
using System;
using System.Threading.Tasks;

namespace Capstone2.RestServices.Proda.Utility
{
    public class ProdaKeyVaultHelper : IProdaKeyVaultHelper
    {
        public async Task<byte[]> GetSetCertificateKey(string certificateName, string tenantId, string clientId, string secretKey, string keyVaultUri, byte[] rsaCertificate = null)
        {
            try
            {
                ClientSecretCredential csCredential = new ClientSecretCredential(tenantId, clientId, secretKey);
                if (rsaCertificate == null)
                {
                    SecretClient client = new SecretClient(vaultUri: new Uri(keyVaultUri), credential: csCredential);
                    Response<KeyVaultSecret> resKeyVault = await client.GetSecretAsync(certificateName);
                    KeyVaultSecret vaultSecret = resKeyVault?.Value;
                    if (vaultSecret != null)
                        return System.Convert.FromBase64String(vaultSecret.Value);
                }
                else
                {
                    var certClient = new CertificateClient(vaultUri: new Uri(keyVaultUri), credential: csCredential);
                    ImportCertificateOptions options = new ImportCertificateOptions(certificateName, rsaCertificate);
                    Response<KeyVaultCertificateWithPolicy> resp = await certClient.ImportCertificateAsync(options);
                }
                return null;
            }
            catch (Exception ex)
            {
                throw;
            }

        }
    }
}
