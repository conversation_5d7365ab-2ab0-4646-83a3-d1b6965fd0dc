﻿namespace Capstone2.RestServices.Medicare.Utility
{
    public static class ConstantsHelper
    {
        public const string TYPECODE_OPV = "OPV";
        public const string TYPECODE_PVM = "PVM";
        public const string TYPECODE_PVF = "PVF";
        public const string TYPECODE_OVVW = "OVVW";
        public const string TYPECODE_GPRW = "GPRW";
        public const string TYPECODE_SDDW = "SDDW";

        public const string TYPECODE_OECW = "OEC";
        public const string TYPECODE_ECFW = "ECF";
        public const string TYPECODE_ECMW = "ECM";

        public const string TYPECODE_PCI_P = "P";
        public const string TYPECODE_PCI_S = "S";
        public const string TYPECODE_PCI_D = "D";
        public const string TYPECODE_PCI_G = "G";
        public const string TYPECODE_PCI_H = "H";
        public const string TYPECODE_PCI_L = "L";
        public const string TYPECODE_PCI_E = "E";
        public const string TYPECODE_PCI_N = "N";
        public const string TYPECODE_PCI_SD = "SD";
        public const string TYPECODE_PCI_SS = "SS";

        public const string TYPECODE_PCI_GENERAL = "PCI-general";
        public const string TYPECODE_PCI_SPECIALIST = "PCI-specialist";
        public const string TYPECODE_PCI_PATHOLOGY = "PCI-pathology";

        public const string TYPECODE_IMCW_MO = "MO";
        public const string TYPECODE_IMCW_MB = "MB";
        public const string TYPECODE_IMCW_AG = "AG";
        public const string TYPECODE_IMCW_SC = "SC";
        public const string TYPECODE_IMCW_PC = "PC";

        public const string TYPECODE_BBSW_GENERAL = "BBSW-general";
        public const string TYPECODE_BBSW_SPECIALIST = "BBSW-specialist";
        public const string TYPECODE_BBSW_PATHOLOGY = "BBSW-pathology";

        public const string TYPECODE_DVAW_GENERAL = "DVAW-general";
        public const string TYPECODE_DVAW_SPECIALIST = "DVAW-specialist";
        public const string TYPECODE_DVAW_PATHOLOGY = "DVAW-pathology";

        public const string SERVICE_TYPECODE_BBSWG = "O-BBSW"; // i.e. 'BBSW' Added for comparison 
        public const string SERVICE_TYPECODE_BBSWP = "P-BBSW";
        public const string SERVICE_TYPECODE_BBSWS = "S-BBSW";

        public const string SERVICE_TYPECODE_DVAWG = "O-DVAW"; // i.e. 'DVAW' Added for comparison 
        public const string SERVICE_TYPECODE_DVAWP = "P-DVAW";
        public const string SERVICE_TYPECODE_DVAWS = "S-DVAW";

        public const string SERVICE_TYPECODE_IMCWO = "O-IMCW"; // i.e. 'IMCW' Added for comparison 
        public const string SERVICE_TYPECODE_IMCWP = "P-IMCW";
        public const string SERVICE_TYPECODE_IMCWS = "S-IMCW";

        public const string SERVICE_TYPECODE_IHCW = "IHCW";

        public const string SERVICE_NAME_BBSWG = "BBSW - General";
        public const string SERVICE_NAME_BBSWS = "BBSW - Specialist";
        public const string SERVICE_NAME_BBSWP = "BBSW - Pathology";
        public const string SERVICE_NAME_BPRW = "BPRW";
        public const string SERVICE_NAME_BPYW = "BPYW";

        public const string SERVICE_NAME_DVAWG = "DVAW-general";
        public const string SERVICE_NAME_DVAWS = "DVAW-specialist";
        public const string SERVICE_NAME_DVAWP = "DVAW-pathology";
        public const string SERVICE_NAME_DVYW = "DVYW";
        public const string SERVICE_NAME_DVRW = "DVRW";

        public const string SERVICE_NAME_PCI_P = "PCI-pathology";
        public const string SERVICE_NAME_PCI_S_D = "PCI-specialist";
        public const string SERVICE_NAME_PCI_G = "PCI-general";

        public const string SERVICE_NAME_IMCW_MO_O = "IMCW-MO-general";
        public const string SERVICE_NAME_IMCW_MO_S = "IMCW-MO-specialist";
        public const string SERVICE_NAME_IMCW_MO_P = "IMCW-MO-pathology";

        public const string SERVICE_NAME_IMCW_MB_O = "IMCW-MB-general";
        public const string SERVICE_NAME_IMCW_MB_S = "IMCW-MB-specialist";
        public const string SERVICE_NAME_IMCW_MB_P = "IMCW-MB-pathology";

        public const string SERVICE_NAME_IMCW_AG_O = "IMCW-AG-general";
        public const string SERVICE_NAME_IMCW_AG_S = "IMCW-AG-specialist";
        public const string SERVICE_NAME_IMCW_AG_P = "IMCW-AG-pathology";

        public const string SERVICE_NAME_IMCW_SC_O = "IMCW-SC-general";
        public const string SERVICE_NAME_IMCW_SC_S = "IMCW-SC-specialist";
        public const string SERVICE_NAME_IMCW_SC_P = "IMCW-SC-pathology";

        public const string SERVICE_NAME_IMCW_PC_O = "IMCW-PC-general";
        public const string SERVICE_NAME_IMCW_PC_S = "IMCW-PC-specialist";
        public const string SERVICE_NAME_IMCW_PC_P = "IMCW-PC-pathology";

        public const string SERVICE_NAME_IHCW = "IHCW";

        public const string SERVICE_NAME_STSW = "STSW";
        public const string SERVICE_NAME_RTVW = "RTVW";
        public const string SERVICE_NAME_RTVW_IHC = "RTVW-IHC";
        public const string SERVICE_NAME_ERAW = "ERAW";        

        public const string SERVICE_ENDPOINT_OPV = "/mcp/patientverification/v1";
        public const string SERVICE_ENDPOINT_PVM = "/mcp/patientverification/medicare/v1";
        public const string SERVICE_ENDPOINT_PVF = "/mcp/patientverification/hf/v1";
        public const string SERVICE_ENDPOINT_OVVW = "/mcp/veteranverification/v1";
        public const string SERVICE_ENDPOINT_GPRW = "/mcp/getparticipants/v1";

        public const string SERVICE_ENDPOINT_PCI_P = "/mcp/patientclaiminteractive/pathology/v1";
        public const string SERVICE_ENDPOINT_PCI_S_D = "/mcp/patientclaiminteractive/specialist/v1";
        public const string SERVICE_ENDPOINT_PCI_G = "/mcp/patientclaiminteractive/general/v1";

        public const string SERVICE_ENDPOINT_SDDW = "/mcp/samedaydelete/v1";

        public const string SERVICE_ENDPOINT_BBSWG = "/mcp/bulkbillstoreforward/general/v1";
        public const string SERVICE_ENDPOINT_BBSWP = "/mcp/bulkbillstoreforward/pathology/v1";
        public const string SERVICE_ENDPOINT_BBSWS = "/mcp/bulkbillstoreforward/specialist/v1";

        public const string SERVICE_ENDPOINT_DVAWG = "/mcp/dvaclaim/general/v1";
        public const string SERVICE_ENDPOINT_DVAWP = "/mcp/dvaclaim/pathology/v1";
        public const string SERVICE_ENDPOINT_DVAWS = "/mcp/dvaclaim/specialist/v1";

        public const string SERVICE_ENDPOINT_OECW = "/mcp/onlineeligibilitycheck/v1";
        public const string SERVICE_ENDPOINT_ECFW = "/mcp/onlineeligibilitycheck/hf/v1";
        public const string SERVICE_ENDPOINT_ECMW = "/mcp/onlineeligibilitycheck/medicare/v1";

        public const string SERVICE_ENDPOINT_IMCW_MO_O = "/mcp/inpatientmedicalclaim/mo/general/v1";
        public const string SERVICE_ENDPOINT_IMCW_MO_S = "/mcp/inpatientmedicalclaim/mo/specialist/v1";
        public const string SERVICE_ENDPOINT_IMCW_MO_P = "/mcp/inpatientmedicalclaim/mo/pathology/v1";

        public const string SERVICE_ENDPOINT_IMCW_MB_O = "/mcp/inpatientmedicalclaim/mb/general/v1";
        public const string SERVICE_ENDPOINT_IMCW_MB_S = "/mcp/inpatientmedicalclaim/mb/specialist/v1";
        public const string SERVICE_ENDPOINT_IMCW_MB_P = "/mcp/inpatientmedicalclaim/mb/pathology/v1";

        public const string SERVICE_ENDPOINT_IMCW_AG_O = "/mcp/inpatientmedicalclaim/ag/general/v1";
        public const string SERVICE_ENDPOINT_IMCW_AG_S = "/mcp/inpatientmedicalclaim/ag/specialist/v1";
        public const string SERVICE_ENDPOINT_IMCW_AG_P = "/mcp/inpatientmedicalclaim/ag/pathology/v1";

        public const string SERVICE_ENDPOINT_IMCW_SC_O = "/mcp/inpatientmedicalclaim/sc/general/v1";
        public const string SERVICE_ENDPOINT_IMCW_SC_S = "/mcp/inpatientmedicalclaim/sc/specialist/v1";
        public const string SERVICE_ENDPOINT_IMCW_SC_P = "/mcp/inpatientmedicalclaim/sc/pathology/v1";

        public const string SERVICE_ENDPOINT_IMCW_PC_O = "/mcp/inpatientmedicalclaim/pc/general/v1";
        public const string SERVICE_ENDPOINT_IMCW_PC_S = "/mcp/inpatientmedicalclaim/pc/specialist/v1";
        public const string SERVICE_ENDPOINT_IMCW_PC_P = "/mcp/inpatientmedicalclaim/pc/pathology/v1";

        public const string SERVICE_ENDPOINT_IHCW = "/mcp/inhospitalclaim/private/v1";

        public const string PRODA_MEDICARE_ACCESS_TOOKEN = "proda/medicareaccesstoken";
        public const string ACCESS_TYPE = "access_token";
        public const string AuditId_Type_Location = "Location Id";
        public const string SubjectId_Type_MediCard = "Medicare Card";
        public const string SubjectId_Type_HealthFund = "Health Fund Member Number";
        public const string SubjectId_Type_Veteran_FN = "Veteran File Number";
        public const string Status_Complete = "COMPLETE";
        public const string Status_Error = "ERROR";

        public const string TOKEN_ERROR_400 = "{ \"statusCode\": 400,\"errorCode\": 7,\"errors\": \"Token Errors\"}";
        public const string DATA_ERROR_400 = "{ \"statusCode\": 400,\"errorCode\": 9202,\"errors\": \"Data Errors\"}";
        public const string DATA_ERROR_INVALID_HEALTH_FUND_400 = "{ \"statusCode\": 400,\"errorCode\": 9202,\"errors\": \"Invalid Health Fund Details\"}";
        public const string INVALID_MEDICARE_NUMBER_400 = "{ \"statusCode\": 400,\"errorCode\": 9202,\"errors\": \"The check digit becomes the 9th digit in the Medicare Card number. The 10th digit is the Medicare Card number and must not be zero.\"}";
        public const string INVALID_MEDICARE_MEMBER_NUMBER_400 = "{ \"statusCode\": 400,\"errorCode\": 9202,\"errors\": \"Patient Medicare Card Number must be supplied.\"}";
        public const string INVALID_MEDICARE_MEMBER_REFNUMBER_400 = "{ \"statusCode\": 400,\"errorCode\": 9202,\"errors\": \"Patient Medicare Individual Reference Number must be supplied.\"}";
        public const string INVALID_IMCW_TYPECODE_400 = "{ \"statusCode\": 400,\"errorCode\": 9202,\"errors\": \"TypeCode is invalid.It should be  MO,MB,PC,AG or SC.\"}";
        public const string INVALID_IMCW_SERVICETYPECODE_400 = "{ \"statusCode\": 400,\"errorCode\": 9202,\"errors\": \"ServiceTypeCode is invalid.It should be  S,P or O.\"}";
        public const string CATCH_ERROR = "{ \"statusCode\": 400,\"errorCode\": 98,\"errors\":{0}}";
        public const string PARTICIPANT_TYPE_ERROR = "Invalid value supplied for Participant Type.";
        public const int ERROR_CODE_98 = 98;
    }
}
