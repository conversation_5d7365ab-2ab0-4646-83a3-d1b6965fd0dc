﻿using System;

namespace Capstone2.RestServices.Medicare.Utility
{
    public static class MedicareHelper
    {
        public static string GetServiceEndPoint(string typeCode)
        {
            string serviceEndPoint = string.Empty;
            if (typeCode.Equals(ConstantsHelper.TYPECODE_OPV, StringComparison.OrdinalIgnoreCase))
                serviceEndPoint = ConstantsHelper.SERVICE_ENDPOINT_OPV;
            else if (typeCode.Equals(ConstantsHelper.TYPECODE_PVM, StringComparison.OrdinalIgnoreCase))
                serviceEndPoint = ConstantsHelper.SERVICE_ENDPOINT_PVM;
            else if (typeCode.Equals(ConstantsHelper.TYPECODE_PVF, StringComparison.OrdinalIgnoreCase))
                serviceEndPoint = ConstantsHelper.SERVICE_ENDPOINT_PVF;
            else if (typeCode.Equals(ConstantsHelper.TYPECODE_OVVW, StringComparison.OrdinalIgnoreCase))
                serviceEndPoint = ConstantsHelper.SERVICE_ENDPOINT_OVVW;
            else if (typeCode.Equals(ConstantsHelper.TYPECODE_GPRW, StringComparison.OrdinalIgnoreCase))
                serviceEndPoint = ConstantsHelper.SERVICE_ENDPOINT_GPRW;
            else if (typeCode.Equals(ConstantsHelper.TYPECODE_PCI_GENERAL, StringComparison.OrdinalIgnoreCase))
                serviceEndPoint = ConstantsHelper.SERVICE_ENDPOINT_PCI_G;
            else if (typeCode.Equals(ConstantsHelper.TYPECODE_PCI_SPECIALIST, StringComparison.OrdinalIgnoreCase))
                serviceEndPoint = ConstantsHelper.SERVICE_ENDPOINT_PCI_S_D;
            else if (typeCode.Equals(ConstantsHelper.TYPECODE_PCI_PATHOLOGY, StringComparison.OrdinalIgnoreCase))
                serviceEndPoint = ConstantsHelper.SERVICE_ENDPOINT_PCI_P;
            else if (typeCode.Equals(ConstantsHelper.TYPECODE_PCI_P, StringComparison.OrdinalIgnoreCase))
                serviceEndPoint = ConstantsHelper.SERVICE_ENDPOINT_PCI_P;
            else if (typeCode.Equals(ConstantsHelper.TYPECODE_PCI_S, StringComparison.OrdinalIgnoreCase) || typeCode.Equals(ConstantsHelper.TYPECODE_PCI_D, StringComparison.OrdinalIgnoreCase))
                serviceEndPoint = ConstantsHelper.SERVICE_ENDPOINT_PCI_S_D;
            else if (typeCode.Equals(ConstantsHelper.TYPECODE_PCI_G, StringComparison.OrdinalIgnoreCase))
                serviceEndPoint = ConstantsHelper.SERVICE_ENDPOINT_PCI_G;
            else if (typeCode.Equals(ConstantsHelper.TYPECODE_SDDW, StringComparison.OrdinalIgnoreCase))
                serviceEndPoint = ConstantsHelper.SERVICE_ENDPOINT_SDDW;
            else if (typeCode.Equals(ConstantsHelper.TYPECODE_BBSW_GENERAL, StringComparison.OrdinalIgnoreCase))
                serviceEndPoint = ConstantsHelper.SERVICE_ENDPOINT_BBSWG;
            else if (typeCode.Equals(ConstantsHelper.TYPECODE_BBSW_SPECIALIST, StringComparison.OrdinalIgnoreCase))
                serviceEndPoint = ConstantsHelper.SERVICE_ENDPOINT_BBSWS;
            else if (typeCode.Equals(ConstantsHelper.TYPECODE_BBSW_PATHOLOGY, StringComparison.OrdinalIgnoreCase))
                serviceEndPoint = ConstantsHelper.SERVICE_ENDPOINT_BBSWP;
            else if (typeCode.Equals(ConstantsHelper.SERVICE_TYPECODE_BBSWG, StringComparison.OrdinalIgnoreCase))
                serviceEndPoint = ConstantsHelper.SERVICE_ENDPOINT_BBSWG;
            else if (typeCode.Equals(ConstantsHelper.SERVICE_TYPECODE_BBSWS, StringComparison.OrdinalIgnoreCase))
                serviceEndPoint = ConstantsHelper.SERVICE_ENDPOINT_BBSWS;
            else if (typeCode.Equals(ConstantsHelper.SERVICE_TYPECODE_BBSWP, StringComparison.OrdinalIgnoreCase))
                serviceEndPoint = ConstantsHelper.SERVICE_ENDPOINT_BBSWP;
            else if (typeCode.Equals(ConstantsHelper.TYPECODE_OECW, StringComparison.OrdinalIgnoreCase))
                serviceEndPoint = ConstantsHelper.SERVICE_ENDPOINT_OECW;
            else if (typeCode.Equals(ConstantsHelper.TYPECODE_ECFW, StringComparison.OrdinalIgnoreCase))
                serviceEndPoint = ConstantsHelper.SERVICE_ENDPOINT_ECFW;
            else if (typeCode.Equals(ConstantsHelper.TYPECODE_ECMW, StringComparison.OrdinalIgnoreCase))
                serviceEndPoint = ConstantsHelper.SERVICE_ENDPOINT_ECMW;
            else if (typeCode.Equals(ConstantsHelper.TYPECODE_DVAW_GENERAL, StringComparison.OrdinalIgnoreCase))
                serviceEndPoint = ConstantsHelper.SERVICE_ENDPOINT_DVAWG;
            else if (typeCode.Equals(ConstantsHelper.TYPECODE_DVAW_SPECIALIST, StringComparison.OrdinalIgnoreCase))
                serviceEndPoint = ConstantsHelper.SERVICE_ENDPOINT_DVAWS;
            else if (typeCode.Equals(ConstantsHelper.TYPECODE_DVAW_PATHOLOGY, StringComparison.OrdinalIgnoreCase))
                serviceEndPoint = ConstantsHelper.SERVICE_ENDPOINT_DVAWP;
            else if (typeCode.Equals(ConstantsHelper.SERVICE_TYPECODE_DVAWG, StringComparison.OrdinalIgnoreCase))
                serviceEndPoint = ConstantsHelper.SERVICE_ENDPOINT_DVAWG;
            else if (typeCode.Equals(ConstantsHelper.SERVICE_TYPECODE_DVAWP, StringComparison.OrdinalIgnoreCase))
                serviceEndPoint = ConstantsHelper.SERVICE_ENDPOINT_DVAWP;
            else if (typeCode.Equals(ConstantsHelper.SERVICE_TYPECODE_DVAWS, StringComparison.OrdinalIgnoreCase))
                serviceEndPoint = ConstantsHelper.SERVICE_ENDPOINT_DVAWS;
            else if (typeCode.Equals(ConstantsHelper.SERVICE_TYPECODE_IHCW, StringComparison.OrdinalIgnoreCase))
                serviceEndPoint = ConstantsHelper.SERVICE_ENDPOINT_IHCW;
            return serviceEndPoint;
        }

        public static string GetServiceName(string typeCode)
        {
            string serviceName = string.Empty;
            if (typeCode.Equals(ConstantsHelper.TYPECODE_PCI_P, StringComparison.OrdinalIgnoreCase))
                serviceName = ConstantsHelper.SERVICE_NAME_PCI_P;
            else if (typeCode.Equals(ConstantsHelper.TYPECODE_PCI_S, StringComparison.OrdinalIgnoreCase) || typeCode.Equals(ConstantsHelper.TYPECODE_PCI_D, StringComparison.OrdinalIgnoreCase))
                serviceName = ConstantsHelper.SERVICE_NAME_PCI_S_D;
            else if (typeCode.Equals(ConstantsHelper.TYPECODE_PCI_G, StringComparison.OrdinalIgnoreCase))
                serviceName = ConstantsHelper.SERVICE_NAME_PCI_G;
            else if (typeCode.Equals(ConstantsHelper.SERVICE_TYPECODE_BBSWG, StringComparison.OrdinalIgnoreCase))
                serviceName = ConstantsHelper.SERVICE_NAME_BBSWG;
            else if (typeCode.Equals(ConstantsHelper.SERVICE_TYPECODE_BBSWS, StringComparison.OrdinalIgnoreCase))
                serviceName = ConstantsHelper.SERVICE_NAME_BBSWS;
            else if (typeCode.Equals(ConstantsHelper.SERVICE_TYPECODE_BBSWP, StringComparison.OrdinalIgnoreCase))
                serviceName = ConstantsHelper.SERVICE_NAME_BBSWP;
            else if (typeCode.Equals(ConstantsHelper.TYPECODE_OECW, StringComparison.OrdinalIgnoreCase))
                serviceName = ConstantsHelper.TYPECODE_OECW;
            else if (typeCode.Equals(ConstantsHelper.TYPECODE_ECFW, StringComparison.OrdinalIgnoreCase))
                serviceName = ConstantsHelper.TYPECODE_ECFW;
            else if (typeCode.Equals(ConstantsHelper.TYPECODE_ECMW, StringComparison.OrdinalIgnoreCase))
                serviceName = ConstantsHelper.TYPECODE_ECMW;
            else if (typeCode.Equals(ConstantsHelper.SERVICE_TYPECODE_DVAWG, StringComparison.OrdinalIgnoreCase))
                serviceName = ConstantsHelper.SERVICE_NAME_DVAWG;
            else if (typeCode.Equals(ConstantsHelper.SERVICE_TYPECODE_DVAWP, StringComparison.OrdinalIgnoreCase))
                serviceName = ConstantsHelper.SERVICE_NAME_DVAWP;
            else if (typeCode.Equals(ConstantsHelper.SERVICE_TYPECODE_DVAWS, StringComparison.OrdinalIgnoreCase))
                serviceName = ConstantsHelper.SERVICE_NAME_DVAWS;
            else if (typeCode.Equals(ConstantsHelper.SERVICE_TYPECODE_IHCW, StringComparison.OrdinalIgnoreCase))
                serviceName = ConstantsHelper.SERVICE_NAME_IHCW;
            return serviceName;
        }

        public static string GetServiceEndPoint(string typeCode, string serviceTypeCode)
        {
            string serviceEndPoint = string.Empty;
            if (typeCode.Equals(ConstantsHelper.TYPECODE_IMCW_MO, StringComparison.OrdinalIgnoreCase))
            {
                if (serviceTypeCode.Equals(ConstantsHelper.SERVICE_TYPECODE_IMCWO, StringComparison.OrdinalIgnoreCase))
                    serviceEndPoint = ConstantsHelper.SERVICE_ENDPOINT_IMCW_MO_O;
                else if (serviceTypeCode.Equals(ConstantsHelper.SERVICE_TYPECODE_IMCWP, StringComparison.OrdinalIgnoreCase))
                    serviceEndPoint = ConstantsHelper.SERVICE_ENDPOINT_IMCW_MO_P;
                else if (serviceTypeCode.Equals(ConstantsHelper.SERVICE_TYPECODE_IMCWS, StringComparison.OrdinalIgnoreCase))
                    serviceEndPoint = ConstantsHelper.SERVICE_ENDPOINT_IMCW_MO_S;
            }
            else if (typeCode.Equals(ConstantsHelper.TYPECODE_IMCW_MB, StringComparison.OrdinalIgnoreCase))
            {
                if (serviceTypeCode.Equals(ConstantsHelper.SERVICE_TYPECODE_IMCWO, StringComparison.OrdinalIgnoreCase))
                    serviceEndPoint = ConstantsHelper.SERVICE_ENDPOINT_IMCW_MB_O;
                else if (serviceTypeCode.Equals(ConstantsHelper.SERVICE_TYPECODE_IMCWP, StringComparison.OrdinalIgnoreCase))
                    serviceEndPoint = ConstantsHelper.SERVICE_ENDPOINT_IMCW_MB_P;
                else if (serviceTypeCode.Equals(ConstantsHelper.SERVICE_TYPECODE_IMCWS, StringComparison.OrdinalIgnoreCase))
                    serviceEndPoint = ConstantsHelper.SERVICE_ENDPOINT_IMCW_MB_S;
            }
            else if (typeCode.Equals(ConstantsHelper.TYPECODE_IMCW_AG, StringComparison.OrdinalIgnoreCase))
            {
                if (serviceTypeCode.Equals(ConstantsHelper.SERVICE_TYPECODE_IMCWO, StringComparison.OrdinalIgnoreCase))
                    serviceEndPoint = ConstantsHelper.SERVICE_ENDPOINT_IMCW_AG_O;
                else if (serviceTypeCode.Equals(ConstantsHelper.SERVICE_TYPECODE_IMCWP, StringComparison.OrdinalIgnoreCase))
                    serviceEndPoint = ConstantsHelper.SERVICE_ENDPOINT_IMCW_AG_P;
                else if (serviceTypeCode.Equals(ConstantsHelper.SERVICE_TYPECODE_IMCWS, StringComparison.OrdinalIgnoreCase))
                    serviceEndPoint = ConstantsHelper.SERVICE_ENDPOINT_IMCW_AG_S;
            }
            else if (typeCode.Equals(ConstantsHelper.TYPECODE_IMCW_SC, StringComparison.OrdinalIgnoreCase))
            {
                if (serviceTypeCode.Equals(ConstantsHelper.SERVICE_TYPECODE_IMCWO, StringComparison.OrdinalIgnoreCase))
                    serviceEndPoint = ConstantsHelper.SERVICE_ENDPOINT_IMCW_SC_O;
                else if (serviceTypeCode.Equals(ConstantsHelper.SERVICE_TYPECODE_IMCWP, StringComparison.OrdinalIgnoreCase))
                    serviceEndPoint = ConstantsHelper.SERVICE_ENDPOINT_IMCW_SC_P;
                else if (serviceTypeCode.Equals(ConstantsHelper.SERVICE_TYPECODE_IMCWS, StringComparison.OrdinalIgnoreCase))
                    serviceEndPoint = ConstantsHelper.SERVICE_ENDPOINT_IMCW_SC_S;
            }
            else if (typeCode.Equals(ConstantsHelper.TYPECODE_IMCW_PC, StringComparison.OrdinalIgnoreCase))
            {
                if (serviceTypeCode.Equals(ConstantsHelper.SERVICE_TYPECODE_IMCWO, StringComparison.OrdinalIgnoreCase))
                    serviceEndPoint = ConstantsHelper.SERVICE_ENDPOINT_IMCW_PC_O;
                else if (serviceTypeCode.Equals(ConstantsHelper.SERVICE_TYPECODE_IMCWP, StringComparison.OrdinalIgnoreCase))
                    serviceEndPoint = ConstantsHelper.SERVICE_ENDPOINT_IMCW_PC_P;
                else if (serviceTypeCode.Equals(ConstantsHelper.SERVICE_TYPECODE_IMCWS, StringComparison.OrdinalIgnoreCase))
                    serviceEndPoint = ConstantsHelper.SERVICE_ENDPOINT_IMCW_PC_S;
            }

            return serviceEndPoint;
        }

        public static string GetServiceName(string typeCode, string serviceTypeCode)
        {
            string serviceName = string.Empty;
            if (typeCode.Equals(ConstantsHelper.TYPECODE_IMCW_MO, StringComparison.OrdinalIgnoreCase))
            {
                if (serviceTypeCode.Equals(ConstantsHelper.SERVICE_TYPECODE_IMCWO, StringComparison.OrdinalIgnoreCase))
                    serviceName = ConstantsHelper.SERVICE_NAME_IMCW_MO_O;
                else if (serviceTypeCode.Equals(ConstantsHelper.SERVICE_TYPECODE_IMCWP, StringComparison.OrdinalIgnoreCase))
                    serviceName = ConstantsHelper.SERVICE_NAME_IMCW_MO_P;
                else if (serviceTypeCode.Equals(ConstantsHelper.SERVICE_TYPECODE_IMCWS, StringComparison.OrdinalIgnoreCase))
                    serviceName = ConstantsHelper.SERVICE_NAME_IMCW_MO_S;
            }
            else if (typeCode.Equals(ConstantsHelper.TYPECODE_IMCW_MB, StringComparison.OrdinalIgnoreCase))
            {
                if (serviceTypeCode.Equals(ConstantsHelper.SERVICE_TYPECODE_IMCWO, StringComparison.OrdinalIgnoreCase))
                    serviceName = ConstantsHelper.SERVICE_NAME_IMCW_MB_O;
                else if (serviceTypeCode.Equals(ConstantsHelper.SERVICE_TYPECODE_IMCWP, StringComparison.OrdinalIgnoreCase))
                    serviceName = ConstantsHelper.SERVICE_NAME_IMCW_MB_P;
                else if (serviceTypeCode.Equals(ConstantsHelper.SERVICE_TYPECODE_IMCWS, StringComparison.OrdinalIgnoreCase))
                    serviceName = ConstantsHelper.SERVICE_NAME_IMCW_MB_S;
            }
            else if (typeCode.Equals(ConstantsHelper.TYPECODE_IMCW_AG, StringComparison.OrdinalIgnoreCase))
            {
                if (serviceTypeCode.Equals(ConstantsHelper.SERVICE_TYPECODE_IMCWO, StringComparison.OrdinalIgnoreCase))
                    serviceName = ConstantsHelper.SERVICE_NAME_IMCW_AG_O;
                else if (serviceTypeCode.Equals(ConstantsHelper.SERVICE_TYPECODE_IMCWP, StringComparison.OrdinalIgnoreCase))
                    serviceName = ConstantsHelper.SERVICE_NAME_IMCW_AG_P;
                else if (serviceTypeCode.Equals(ConstantsHelper.SERVICE_TYPECODE_IMCWS, StringComparison.OrdinalIgnoreCase))
                    serviceName = ConstantsHelper.SERVICE_NAME_IMCW_AG_S;
            }
            else if (typeCode.Equals(ConstantsHelper.TYPECODE_IMCW_SC, StringComparison.OrdinalIgnoreCase))
            {
                if (serviceTypeCode.Equals(ConstantsHelper.SERVICE_TYPECODE_IMCWO, StringComparison.OrdinalIgnoreCase))
                    serviceName = ConstantsHelper.SERVICE_NAME_IMCW_SC_O;
                else if (serviceTypeCode.Equals(ConstantsHelper.SERVICE_TYPECODE_IMCWP, StringComparison.OrdinalIgnoreCase))
                    serviceName = ConstantsHelper.SERVICE_NAME_IMCW_SC_P;
                else if (serviceTypeCode.Equals(ConstantsHelper.SERVICE_TYPECODE_IMCWS, StringComparison.OrdinalIgnoreCase))
                    serviceName = ConstantsHelper.SERVICE_NAME_IMCW_SC_S;
            }
            else if (typeCode.Equals(ConstantsHelper.TYPECODE_IMCW_PC, StringComparison.OrdinalIgnoreCase))
            {
                if (serviceTypeCode.Equals(ConstantsHelper.SERVICE_TYPECODE_IMCWO, StringComparison.OrdinalIgnoreCase))
                    serviceName = ConstantsHelper.SERVICE_NAME_IMCW_PC_O;
                else if (serviceTypeCode.Equals(ConstantsHelper.SERVICE_TYPECODE_IMCWP, StringComparison.OrdinalIgnoreCase))
                    serviceName = ConstantsHelper.SERVICE_NAME_IMCW_PC_P;
                else if (serviceTypeCode.Equals(ConstantsHelper.SERVICE_TYPECODE_IMCWS, StringComparison.OrdinalIgnoreCase))
                    serviceName = ConstantsHelper.SERVICE_NAME_IMCW_PC_S;
            }

            return serviceName;
        }

        public static string GenerateCorrelationId(string subscriberMinorId)
        {
            int noOfCharLength = 24;
            string dhs_correlationId = $"{subscriberMinorId}{DateTime.UtcNow.ToString("yyMMddHHmmssffff")}";
            if (!string.IsNullOrWhiteSpace(subscriberMinorId) && dhs_correlationId.Length > noOfCharLength)
                dhs_correlationId = dhs_correlationId.Substring(0, noOfCharLength);
            else if (dhs_correlationId.Length < noOfCharLength)
                dhs_correlationId = dhs_correlationId.PadRight(noOfCharLength, '0');
            return dhs_correlationId;
        }
    }

    public static class AnonymousParse
    {
        internal static T Cast<T>(object target, T typeObject)
        {
            return (T)target;
        }
    }
}
