﻿//----------------------
// <auto-generated>
//     Generated using the NSwag toolchain v13.0.5.0 (NJsonSchema v********* (Newtonsoft.Json v11.0.0.0)) (http://NSwag.org)
// </auto-generated>
//----------------------

#pragma warning disable 108 // Disable "CS0108 '{derivedDto}.To<PERSON><PERSON>()' hides inherited member '{dtoBase}.ToJson()'. Use the new keyword if hiding was intended."
#pragma warning disable 114 // Disable "CS0114 '{derivedDto}.RaisePropertyChanged(String)' hides inherited member 'dtoBase.RaisePropertyChanged(String)'. To make the current member override that implementation, add the override keyword. Otherwise add the new keyword."
#pragma warning disable 472 // Disable "CS0472 The result of the expression is always 'false' since a value of type 'Int32' is never equal to 'null' of type 'Int32?'
#pragma warning disable 1573 // Disable "CS1573 Parameter '...' has no matching param tag in the XML comment for ...
#pragma warning disable 1591 // Disable "CS1591 Missing XML comment for publicly visible type or member ..."

namespace Capstone2.Common.MedicareOnline.IHCW
{
    [System.CodeDom.Compiler.GeneratedCode("NSwag", "13.0.5.0 (NJsonSchema v********* (Newtonsoft.Json v11.0.0.0))")]
    public partial class InHospitalClaim 
    {
        private string _baseUrl;
        private System.Net.Http.HttpClient _httpClient;
        private System.Lazy<Newtonsoft.Json.JsonSerializerSettings> _settings;
    
        public InHospitalClaim(System.Net.Http.HttpClient httpClient, string baseUrl)
        {
            _httpClient = httpClient; 
            _settings = new System.Lazy<Newtonsoft.Json.JsonSerializerSettings>(() => 
            {
                var settings = new Newtonsoft.Json.JsonSerializerSettings();
                UpdateJsonSerializerSettings(settings);
                return settings;
            });
            _baseUrl = baseUrl;
        }
    
        public string BaseUrl 
        {
            get { return _baseUrl; }
            set { _baseUrl = value; }
        }
    
        protected Newtonsoft.Json.JsonSerializerSettings JsonSerializerSettings { get { return _settings.Value; } }
    
        partial void UpdateJsonSerializerSettings(Newtonsoft.Json.JsonSerializerSettings settings);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, string url);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, System.Text.StringBuilder urlBuilder);
        partial void ProcessResponse(System.Net.Http.HttpClient client, System.Net.Http.HttpResponseMessage response);
    
        /// <summary>This is the request</summary>
        /// <param name="authorization">JWT header for authorization</param>
        /// <param name="dhs_auditId">DHS Audit ID</param>
        /// <param name="dhs_subjectId">DHS Subject ID</param>
        /// <param name="dhs_messageId">DHS Message ID</param>
        /// <param name="dhs_auditIdType">DHS Audit Type</param>
        /// <param name="dhs_correlationId">DHS Correlation ID</param>
        /// <param name="dhs_productId">DHS Product ID</param>
        /// <param name="dhs_subjectIdType">DHS Subject ID Type</param>
        /// <returns>successful operation</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<InHospitalClaimResponseType> McpInHospitalClaimPrivateAsync(InHospitalClaimRequestType body, string authorization, string dhs_auditId, string dhs_subjectId, string dhs_messageId, string dhs_auditIdType, string dhs_correlationId, string dhs_productId, string dhs_subjectIdType)
        {
            return McpInHospitalClaimPrivateAsync(body, authorization, dhs_auditId, dhs_subjectId, dhs_messageId, dhs_auditIdType, dhs_correlationId, dhs_productId, dhs_subjectIdType);
        }
    
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>This is the request</summary>
        /// <param name="authorization">JWT header for authorization</param>
        /// <param name="dhs_auditId">DHS Audit ID</param>
        /// <param name="dhs_subjectId">DHS Subject ID</param>
        /// <param name="dhs_messageId">DHS Message ID</param>
        /// <param name="dhs_auditIdType">DHS Audit Type</param>
        /// <param name="dhs_correlationId">DHS Correlation ID</param>
        /// <param name="dhs_productId">DHS Product ID</param>
        /// <param name="dhs_subjectIdType">DHS Subject ID Type</param>
        /// <returns>successful operation</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public async System.Threading.Tasks.Task<InHospitalClaimResponseType> McpInHospitalClaimPrivatAsync(InHospitalClaimRequestType body, string authorization, string dhs_auditId, string dhs_subjectId, string dhs_messageId, string dhs_auditIdType, string dhs_correlationId, string dhs_productId, string dhs_subjectIdType, string apiKey, string endpoint, string baseUrl, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(baseUrl != null ? BaseUrl.TrimEnd('/') : "").Append(endpoint);
    
            var client_ = _httpClient;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    if (authorization == null)
                        throw new System.ArgumentNullException("authorization");
                    request_.Headers.TryAddWithoutValidation("Authorization", ConvertToString(authorization, System.Globalization.CultureInfo.InvariantCulture));
                    if (dhs_auditId == null)
                        throw new System.ArgumentNullException("dhs_auditId");
                    request_.Headers.TryAddWithoutValidation("dhs-auditId", ConvertToString(dhs_auditId, System.Globalization.CultureInfo.InvariantCulture));
                    if (dhs_subjectId == null)
                        throw new System.ArgumentNullException("dhs_subjectId");
                    request_.Headers.TryAddWithoutValidation("dhs-subjectId", ConvertToString(dhs_subjectId, System.Globalization.CultureInfo.InvariantCulture));
                    if (dhs_messageId == null)
                        throw new System.ArgumentNullException("dhs_messageId");
                    request_.Headers.TryAddWithoutValidation("dhs-messageId", ConvertToString(dhs_messageId, System.Globalization.CultureInfo.InvariantCulture));
                    if (dhs_auditIdType == null)
                        throw new System.ArgumentNullException("dhs_auditIdType");
                    request_.Headers.TryAddWithoutValidation("dhs-auditIdType", ConvertToString(dhs_auditIdType, System.Globalization.CultureInfo.InvariantCulture));
                    if (dhs_correlationId == null)
                        throw new System.ArgumentNullException("dhs_correlationId");
                    request_.Headers.TryAddWithoutValidation("dhs-correlationId", ConvertToString(dhs_correlationId, System.Globalization.CultureInfo.InvariantCulture));
                    if (dhs_productId == null)
                        throw new System.ArgumentNullException("dhs_productId");
                    request_.Headers.TryAddWithoutValidation("dhs-productId", ConvertToString(dhs_productId, System.Globalization.CultureInfo.InvariantCulture));
                    if (dhs_subjectIdType == null)
                        throw new System.ArgumentNullException("dhs_subjectIdType");
                    request_.Headers.TryAddWithoutValidation("dhs-subjectIdType", ConvertToString(dhs_subjectIdType, System.Globalization.CultureInfo.InvariantCulture));
                    if (apiKey == null)
                        throw new System.ArgumentNullException("apiKey");
                    request_.Headers.TryAddWithoutValidation("X-IBM-Client-Id", ConvertToString(apiKey, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(body, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<InHospitalClaimResponseType>(response_, headers_).ConfigureAwait(false);
                            return objectResponse_.Object;
                        }
                        else
                        if (status_ == "400")
                        {
                            string content = await response_.Content.ReadAsStringAsync();
                            if (content.Contains("codeType") && content.Contains("DHSEIN"))
                            {
                                var objectResponse_ = await ReadObjectResponseAsync<StatusCodeType>(response_, headers_).ConfigureAwait(false);
                                throw new ApiException<StatusCodeType>("server cannot or will not process the request", (int)response_.StatusCode, objectResponse_.Text, headers_, objectResponse_.Object, null);
                            }
                            else
                            {
                                var objectResponse_ = await ReadObjectResponseAsync<ServiceMessagesType>(response_, headers_).ConfigureAwait(false);
                                throw new ApiException<ServiceMessagesType>("server cannot or will not process the request", (int)response_.StatusCode, objectResponse_.Text, headers_, objectResponse_.Object, null);
                            }

                            //var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync();
                            //var objectResponse_ = await ReadObjectResponseAsync<ServiceMessagesType>(response_, headers_).ConfigureAwait(false);
                            //if (!(objectResponse_.Object is null) && objectResponse_.Object.ServiceMessage.Count == 0)
                            //{
                            //    throw new ApiException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", (int)response_.StatusCode, responseData_, headers_, null);

                            //}
                            //else
                            //{
                            //    throw new ApiException<ServiceMessagesType>("server cannot or will not process the request", (int)response_.StatusCode, objectResponse_.Text, headers_, objectResponse_.Object, null);

                            //}
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", (int)response_.StatusCode, responseData_, headers_, null);
                        }

                        return default(InHospitalClaimResponseType);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
            }
        }
    
        protected struct ObjectResponseResult<T>
        {
            public ObjectResponseResult(T responseObject, string responseText)
            {
                this.Object = responseObject;
                this.Text = responseText;
            }
    
            public T Object { get; }
    
            public string Text { get; }
        }
    
        public bool ReadResponseAsString { get; set; }
        
        protected virtual async System.Threading.Tasks.Task<ObjectResponseResult<T>> ReadObjectResponseAsync<T>(System.Net.Http.HttpResponseMessage response, System.Collections.Generic.IReadOnlyDictionary<string, System.Collections.Generic.IEnumerable<string>> headers)
        {
            if (response == null || response.Content == null)
            {
                return new ObjectResponseResult<T>(default(T), string.Empty);
            }
        
            if (ReadResponseAsString)
            {
                var responseText = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
                try
                {
                    var typedBody = Newtonsoft.Json.JsonConvert.DeserializeObject<T>(responseText, JsonSerializerSettings);
                    return new ObjectResponseResult<T>(typedBody, responseText);
                }
                catch (Newtonsoft.Json.JsonException exception)
                {
                    var message = "Could not deserialize the response body string as " + typeof(T).FullName + ".";
                    throw new ApiException(message, (int)response.StatusCode, responseText, headers, exception);
                }
            }
            else
            {
                try
                {
                    using (var responseStream = await response.Content.ReadAsStreamAsync().ConfigureAwait(false))
                    using (var streamReader = new System.IO.StreamReader(responseStream))
                    using (var jsonTextReader = new Newtonsoft.Json.JsonTextReader(streamReader))
                    {
                        var serializer = Newtonsoft.Json.JsonSerializer.Create(JsonSerializerSettings);
                        var typedBody = serializer.Deserialize<T>(jsonTextReader);
                        return new ObjectResponseResult<T>(typedBody, string.Empty);
                    }
                }
                catch (Newtonsoft.Json.JsonException exception)
                {
                    var message = "Could not deserialize the response body stream as " + typeof(T).FullName + ".";
                    throw new ApiException(message, (int)response.StatusCode, string.Empty, headers, exception);
                }
            }
        }
    
        private string ConvertToString(object value, System.Globalization.CultureInfo cultureInfo)
        {
            if (value is System.Enum)
            {
                string name = System.Enum.GetName(value.GetType(), value);
                if (name != null)
                {
                    var field = System.Reflection.IntrospectionExtensions.GetTypeInfo(value.GetType()).GetDeclaredField(name);
                    if (field != null)
                    {
                        var attribute = System.Reflection.CustomAttributeExtensions.GetCustomAttribute(field, typeof(System.Runtime.Serialization.EnumMemberAttribute)) 
                            as System.Runtime.Serialization.EnumMemberAttribute;
                        if (attribute != null)
                        {
                            return attribute.Value != null ? attribute.Value : name;
                        }
                    }
                }
            }
            else if (value is bool) {
                return System.Convert.ToString(value, cultureInfo).ToLowerInvariant();
            }
            else if (value is byte[])
            {
                return System.Convert.ToBase64String((byte[]) value);
            }
            else if (value != null && value.GetType().IsArray)
            {
                var array = System.Linq.Enumerable.OfType<object>((System.Array) value);
                return string.Join(",", System.Linq.Enumerable.Select(array, o => ConvertToString(o, cultureInfo)));
            }
        
            return System.Convert.ToString(value, cultureInfo);
        }
    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v11.0.0.0)")]
    public partial class InHospitalClaimRequestType 
    {
        [Newtonsoft.Json.JsonProperty("claim", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required]
        public HospitalClaimType Claim { get; set; }
    
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v11.0.0.0)")]
    public partial class HospitalClaimType : MessagePartType
    {
        [Newtonsoft.Json.JsonProperty("compensationClaimCode", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string CompensationClaimCode { get; set; }
    
        [Newtonsoft.Json.JsonProperty("contiguousClaimCode", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string ContiguousClaimCode { get; set; }
    
        [Newtonsoft.Json.JsonProperty("facilityTypeCode", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string FacilityTypeCode { get; set; }
    
        [Newtonsoft.Json.JsonProperty("paymentModel", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string PaymentModel { get; set; }
    
        [Newtonsoft.Json.JsonProperty("previousClaimCode", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string PreviousClaimCode { get; set; }
    
        [Newtonsoft.Json.JsonProperty("previousTransactionId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string PreviousTransactionId { get; set; }
    
        [Newtonsoft.Json.JsonProperty("totalChargeAmount", Required = Newtonsoft.Json.Required.Always)]
        public int TotalChargeAmount { get; set; }
    
        [Newtonsoft.Json.JsonProperty("totalHospitalChargeAmount", Required = Newtonsoft.Json.Required.Always)]
        public int TotalHospitalChargeAmount { get; set; }
    
        [Newtonsoft.Json.JsonProperty("totalMedicalChargeAmount", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? TotalMedicalChargeAmount { get; set; }
    
        [Newtonsoft.Json.JsonProperty("urgencyCode", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string UrgencyCode { get; set; }
    
        [Newtonsoft.Json.JsonProperty("accommodationSummary", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public AccommodationSummarySegmentType AccommodationSummary { get; set; }
    
        [Newtonsoft.Json.JsonProperty("addNewBaby", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [System.ComponentModel.DataAnnotations.MinLength(1)]
        public System.Collections.Generic.ICollection<AddNewBabySegmentType> AddNewBaby { get; set; }
    
        [Newtonsoft.Json.JsonProperty("certificate", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [System.ComponentModel.DataAnnotations.MinLength(1)]
        public System.Collections.Generic.ICollection<CertificateSegmentType> Certificate { get; set; }
    
        [Newtonsoft.Json.JsonProperty("drgMorbidity", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public DRGMorbiditySegmentType DrgMorbidity { get; set; }
    
        [Newtonsoft.Json.JsonProperty("episode", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required]
        public HospitalEpisodeType Episode { get; set; }
    
        [Newtonsoft.Json.JsonProperty("medical", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [System.ComponentModel.DataAnnotations.MinLength(1)]
        public System.Collections.Generic.ICollection<MedicalServiceSegmentType> Medical { get; set; }
    
        [Newtonsoft.Json.JsonProperty("miscellaneous", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [System.ComponentModel.DataAnnotations.MinLength(1)]
        public System.Collections.Generic.ICollection<MiscellaneousServiceSegmentType> Miscellaneous { get; set; }
    
        [Newtonsoft.Json.JsonProperty("nonDRGMorbidity", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [System.ComponentModel.DataAnnotations.MinLength(1)]
        public System.Collections.Generic.ICollection<NonDRGMorbiditySegmentType> NonDRGMorbidity { get; set; }
    
        [Newtonsoft.Json.JsonProperty("patientSummary", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required]
        public HospitalPatientType PatientSummary { get; set; }
    
        [Newtonsoft.Json.JsonProperty("principal", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [System.ComponentModel.DataAnnotations.MinLength(1)]
        public System.Collections.Generic.ICollection<PrincipalServiceSegmentType> Principal { get; set; }
    
        [Newtonsoft.Json.JsonProperty("remarks", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [System.ComponentModel.DataAnnotations.MinLength(1)]
        public System.Collections.Generic.ICollection<RemarksSegmentType> Remarks { get; set; }
    
        [Newtonsoft.Json.JsonProperty("senderContact", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ContactType SenderContact { get; set; }
    
        [Newtonsoft.Json.JsonProperty("singleValueBenefit", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public SingleValueBenefitSegmentType SingleValueBenefit { get; set; }
    
        [Newtonsoft.Json.JsonProperty("summary", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required]
        public HospitalClaimSummaryType Summary { get; set; } = new HospitalClaimSummaryType();
    
        [Newtonsoft.Json.JsonProperty("transfer", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [System.ComponentModel.DataAnnotations.MinLength(1)]
        public System.Collections.Generic.ICollection<TransferSegmentType> Transfer { get; set; }
    
        [Newtonsoft.Json.JsonProperty("transport", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [System.ComponentModel.DataAnnotations.MinLength(1)]
        public System.Collections.Generic.ICollection<TransportSegmentType> Transport { get; set; }
    
    
    }
    
    //[Newtonsoft.Json.JsonConverter(typeof(JsonInheritanceConverter), "messagePartType")]
    //[JsonInheritanceAttribute("HospitalClaimType", typeof(HospitalClaimType))]
    //[JsonInheritanceAttribute("AccommodationSummarySegmentType", typeof(AccommodationSummarySegmentType))]
    //[JsonInheritanceAttribute("ChargedSegmentPartType", typeof(ChargedSegmentPartType))]
    //[JsonInheritanceAttribute("AccommodationDetailsSegmentType", typeof(AccommodationDetailsSegmentType))]
    //[JsonInheritanceAttribute("CriticalCareSegmentType", typeof(CriticalCareSegmentType))]
    //[JsonInheritanceAttribute("LeavePeriodSegmentType", typeof(LeavePeriodSegmentType))]
    //[JsonInheritanceAttribute("AddNewBabySegmentType", typeof(AddNewBabySegmentType))]
    //[JsonInheritanceAttribute("CertificateSegmentType", typeof(CertificateSegmentType))]
    //[JsonInheritanceAttribute("DRGMorbiditySegmentType", typeof(DRGMorbiditySegmentType))]
    //[JsonInheritanceAttribute("HospitalEpisodeType", typeof(HospitalEpisodeType))]
    //[JsonInheritanceAttribute("MedicalServiceSegmentType", typeof(MedicalServiceSegmentType))]
    //[JsonInheritanceAttribute("MiscellaneousServiceSegmentType", typeof(MiscellaneousServiceSegmentType))]
    //[JsonInheritanceAttribute("NonDRGMorbiditySegmentType", typeof(NonDRGMorbiditySegmentType))]
    //[JsonInheritanceAttribute("HospitalPatientType", typeof(HospitalPatientType))]
    //[JsonInheritanceAttribute("PrincipalServiceSegmentType", typeof(PrincipalServiceSegmentType))]
    //[JsonInheritanceAttribute("MultipleServiceSegmentType", typeof(MultipleServiceSegmentType))]
    //[JsonInheritanceAttribute("RemarksSegmentType", typeof(RemarksSegmentType))]
    //[JsonInheritanceAttribute("SingleValueBenefitSegmentType", typeof(SingleValueBenefitSegmentType))]
    //[JsonInheritanceAttribute("TransferSegmentType", typeof(TransferSegmentType))]
    //[JsonInheritanceAttribute("TransportSegmentType", typeof(TransportSegmentType))]
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v11.0.0.0)")]
    public partial class MessagePartType 
    {
        [Newtonsoft.Json.JsonProperty("id", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string Id { get; set; }
    
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v11.0.0.0)")]
    public partial class AccommodationSummarySegmentType : ChargedSegmentPartType
    {
        [Newtonsoft.Json.JsonProperty("accommodationDays", Required = Newtonsoft.Json.Required.Always)]
        public int AccommodationDays { get; set; }
    
        [Newtonsoft.Json.JsonProperty("leaveDays", Required = Newtonsoft.Json.Required.Always)]
        public int LeaveDays { get; set; }
    
        [Newtonsoft.Json.JsonProperty("nonCertifiedDaysOfStay", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? NonCertifiedDaysOfStay { get; set; }
    
        [Newtonsoft.Json.JsonProperty("totalChargeAmount", Required = Newtonsoft.Json.Required.Always)]
        public int TotalChargeAmount { get; set; }
    
        [Newtonsoft.Json.JsonProperty("accommodation", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [System.ComponentModel.DataAnnotations.MinLength(1)]
        public System.Collections.Generic.ICollection<AccommodationDetailsSegmentType> Accommodation { get; set; }
    
        [Newtonsoft.Json.JsonProperty("criticalCare", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [System.ComponentModel.DataAnnotations.MinLength(1)]
        public System.Collections.Generic.ICollection<CriticalCareSegmentType> CriticalCare { get; set; }
    
        [Newtonsoft.Json.JsonProperty("leavePeriod", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [System.ComponentModel.DataAnnotations.MinLength(1)]
        public System.Collections.Generic.ICollection<LeavePeriodSegmentType> LeavePeriod { get; set; }
    
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v11.0.0.0)")]
    public partial class ChargedSegmentPartType : MessagePartType
    {
        [Newtonsoft.Json.JsonProperty("chargeRaisedCode", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string ChargeRaisedCode { get; set; }
    
        [Newtonsoft.Json.JsonProperty("fromDate", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        [Newtonsoft.Json.JsonConverter(typeof(DateFormatConverter))]
        public System.DateTimeOffset FromDate { get; set; }
    
        [Newtonsoft.Json.JsonProperty("numberOfDays", Required = Newtonsoft.Json.Required.Always)]
        public int NumberOfDays { get; set; }
    
        [Newtonsoft.Json.JsonProperty("toDate", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        [Newtonsoft.Json.JsonConverter(typeof(DateFormatConverter))]
        public System.DateTimeOffset ToDate { get; set; }
    
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v11.0.0.0)")]
    public partial class AccommodationDetailsSegmentType : ChargedSegmentPartType
    {
        [Newtonsoft.Json.JsonProperty("bedBandCode", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string BedBandCode { get; set; }
    
        [Newtonsoft.Json.JsonProperty("bedLevelAddOnInd", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string BedLevelAddOnInd { get; set; }
    
        [Newtonsoft.Json.JsonProperty("bedLevelCode", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string BedLevelCode { get; set; }
    
        [Newtonsoft.Json.JsonProperty("chargeAmount", Required = Newtonsoft.Json.Required.Always)]
        public int ChargeAmount { get; set; }
    
        [Newtonsoft.Json.JsonProperty("dayRate", Required = Newtonsoft.Json.Required.Always)]
        public int DayRate { get; set; }
    
        [Newtonsoft.Json.JsonProperty("patientClassificationCode", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string PatientClassificationCode { get; set; }
    
        [Newtonsoft.Json.JsonProperty("programCode", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ProgramCode { get; set; }
    
        [Newtonsoft.Json.JsonProperty("serviceCode", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ServiceCode { get; set; }
    
        [Newtonsoft.Json.JsonProperty("serviceCodeTypeCode", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ServiceCodeTypeCode { get; set; }
    
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v11.0.0.0)")]
    public partial class CriticalCareSegmentType : ChargedSegmentPartType
    {
        [Newtonsoft.Json.JsonProperty("chargeAmount", Required = Newtonsoft.Json.Required.Always)]
        public int ChargeAmount { get; set; }
    
        [Newtonsoft.Json.JsonProperty("criticalCareAddOnInd", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string CriticalCareAddOnInd { get; set; }
    
        [Newtonsoft.Json.JsonProperty("criticalCareLevelCode", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CriticalCareLevelCode { get; set; }
    
        [Newtonsoft.Json.JsonProperty("criticalCareTypeCode", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string CriticalCareTypeCode { get; set; }
    
        [Newtonsoft.Json.JsonProperty("dayRate", Required = Newtonsoft.Json.Required.Always)]
        public int DayRate { get; set; }
    
        [Newtonsoft.Json.JsonProperty("icuHours", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? IcuHours { get; set; }
    
        [Newtonsoft.Json.JsonProperty("numberOfHours", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? NumberOfHours { get; set; }
    
        [Newtonsoft.Json.JsonProperty("serviceCode", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ServiceCode { get; set; }
    
        [Newtonsoft.Json.JsonProperty("serviceCodeTypeCode", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ServiceCodeTypeCode { get; set; }
    
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v11.0.0.0)")]
    public partial class LeavePeriodSegmentType : MessagePartType
    {
        [Newtonsoft.Json.JsonProperty("fromDate", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        [Newtonsoft.Json.JsonConverter(typeof(DateFormatConverter))]
        public System.DateTimeOffset FromDate { get; set; }
    
        [Newtonsoft.Json.JsonProperty("numberOfDays", Required = Newtonsoft.Json.Required.Always)]
        public int NumberOfDays { get; set; }
    
        [Newtonsoft.Json.JsonProperty("toDate", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        [Newtonsoft.Json.JsonConverter(typeof(DateFormatConverter))]
        public System.DateTimeOffset ToDate { get; set; }
    
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v11.0.0.0)")]
    public partial class AddNewBabySegmentType : MessagePartType
    {
        [Newtonsoft.Json.JsonProperty("baby", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required]
        public IdentityType Baby { get; set; } = new IdentityType();
    
    
    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v11.0.0.0)")]
    public partial class StatusCodeType
    {
        [Newtonsoft.Json.JsonProperty("code", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Code { get; set; }

        [Newtonsoft.Json.JsonProperty("codeType", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CodeType { get; set; }

        [Newtonsoft.Json.JsonProperty("message", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Message { get; set; }
    }

    //[Newtonsoft.Json.JsonConverter(typeof(JsonInheritanceConverter), "identityType")]
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v11.0.0.0)")]
    public partial class IdentityType 
    {
        [Newtonsoft.Json.JsonProperty("dateOfBirth", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(DateFormatConverter))]
        public System.DateTimeOffset? DateOfBirth { get; set; }
    
        [Newtonsoft.Json.JsonProperty("familyName", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string FamilyName { get; set; }
    
        [Newtonsoft.Json.JsonProperty("givenName", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string GivenName { get; set; }
    
        [Newtonsoft.Json.JsonProperty("secondInitial", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [System.ComponentModel.DataAnnotations.StringLength(1, MinimumLength = 1)]
        public string SecondInitial { get; set; }
    
        [Newtonsoft.Json.JsonProperty("sex", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [System.ComponentModel.DataAnnotations.StringLength(1, MinimumLength = 1)]
        public string Sex { get; set; }
    
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v11.0.0.0)")]
    public partial class CertificateSegmentType : MessagePartType
    {
        [Newtonsoft.Json.JsonProperty("fromDate", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        [Newtonsoft.Json.JsonConverter(typeof(DateFormatConverter))]
        public System.DateTimeOffset FromDate { get; set; }
    
        [Newtonsoft.Json.JsonProperty("issueDate", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        [Newtonsoft.Json.JsonConverter(typeof(DateFormatConverter))]
        public System.DateTimeOffset IssueDate { get; set; }
    
        [Newtonsoft.Json.JsonProperty("issuerName", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string IssuerName { get; set; }
    
        [Newtonsoft.Json.JsonProperty("numberOfDays", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? NumberOfDays { get; set; }
    
        [Newtonsoft.Json.JsonProperty("providerNumber", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [System.ComponentModel.DataAnnotations.StringLength(8, MinimumLength = 8)]
        public string ProviderNumber { get; set; }
    
        [Newtonsoft.Json.JsonProperty("text", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Text { get; set; }
    
        [Newtonsoft.Json.JsonProperty("toDate", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(DateFormatConverter))]
        public System.DateTimeOffset? ToDate { get; set; }
    
        [Newtonsoft.Json.JsonProperty("typeCode", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string TypeCode { get; set; }
    
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v11.0.0.0)")]
    public partial class DRGMorbiditySegmentType : MessagePartType
    {
        [Newtonsoft.Json.JsonProperty("drgCode", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string DrgCode { get; set; }
    
        [Newtonsoft.Json.JsonProperty("drgVersion", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string DrgVersion { get; set; }
    
        [Newtonsoft.Json.JsonProperty("principalDiagnosis", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string PrincipalDiagnosis { get; set; }
    
        [Newtonsoft.Json.JsonProperty("ventilationHours", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? VentilationHours { get; set; }
    
        [Newtonsoft.Json.JsonProperty("additionalDiagnosis", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [System.ComponentModel.DataAnnotations.MinLength(1)]
        public System.Collections.Generic.ICollection<string> AdditionalDiagnosis { get; set; }
    
        [Newtonsoft.Json.JsonProperty("procedures", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [System.ComponentModel.DataAnnotations.MinLength(1)]
        public System.Collections.Generic.ICollection<string> Procedures { get; set; }
    
        [Newtonsoft.Json.JsonProperty("icdVersion", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string IcdVersion { get; set; }
    
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v11.0.0.0)")]
    public partial class HospitalEpisodeType : MessagePartType
    {
        [Newtonsoft.Json.JsonProperty("accommodationStatusCode", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string AccommodationStatusCode { get; set; }
    
        [Newtonsoft.Json.JsonProperty("admissionCategoryCode", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string AdmissionCategoryCode { get; set; }
    
        [Newtonsoft.Json.JsonProperty("admissionDate", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        [Newtonsoft.Json.JsonConverter(typeof(DateFormatConverter))]
        public System.DateTimeOffset AdmissionDate { get; set; }
    
        [Newtonsoft.Json.JsonProperty("admissionTime", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        [System.ComponentModel.DataAnnotations.RegularExpression(@"^([0-1][0-9]|2[0-3]):[0-5][0-9]:([0-5][0-9]|60)([.][0-9]*)?(Z|z|(([+]|-)([0-1][0-9]|2[0-3]):[0-5][0-9]))?$")]
        public string AdmissionTime { get; set; }
    
        [Newtonsoft.Json.JsonProperty("anticipatedLengthOfStay", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? AnticipatedLengthOfStay { get; set; }
    
        [Newtonsoft.Json.JsonProperty("dischargeDate", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(DateFormatConverter))]
        public System.DateTimeOffset? DischargeDate { get; set; }
    
        [Newtonsoft.Json.JsonProperty("dischargeIntentionCode", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string DischargeIntentionCode { get; set; }
    
        [Newtonsoft.Json.JsonProperty("dischargeTime", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [System.ComponentModel.DataAnnotations.RegularExpression(@"^([0-1][0-9]|2[0-3]):[0-5][0-9]:([0-5][0-9]|60)([.][0-9]*)?(Z|z|(([+]|-)([0-1][0-9]|2[0-3]):[0-5][0-9]))?$")]
        public string DischargeTime { get; set; }
    
        [Newtonsoft.Json.JsonProperty("dischargeTypeCode", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string DischargeTypeCode { get; set; }
    
        [Newtonsoft.Json.JsonProperty("episodeId", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string EpisodeId { get; set; }
    
        [Newtonsoft.Json.JsonProperty("lengthOfStay", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? LengthOfStay { get; set; }
    
        [Newtonsoft.Json.JsonProperty("mentalHealthLegalStatusCode", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string MentalHealthLegalStatusCode { get; set; }
    
        [Newtonsoft.Json.JsonProperty("palliativeCareDays", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? PalliativeCareDays { get; set; }
    
        [Newtonsoft.Json.JsonProperty("palliativeCareStatusCode", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string PalliativeCareStatusCode { get; set; }
    
        [Newtonsoft.Json.JsonProperty("patientClassificationCode", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string PatientClassificationCode { get; set; }
    
        [Newtonsoft.Json.JsonProperty("qualifiedDaysForNewBorns", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? QualifiedDaysForNewBorns { get; set; }
    
        [Newtonsoft.Json.JsonProperty("readmissionCode", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ReadmissionCode { get; set; }
    
        [Newtonsoft.Json.JsonProperty("referralSourceCode", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string ReferralSourceCode { get; set; }
    
        [Newtonsoft.Json.JsonProperty("sameDayCode", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string SameDayCode { get; set; }
    
        [Newtonsoft.Json.JsonProperty("totalPsychiatricCareDays", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? TotalPsychiatricCareDays { get; set; }
    
        [Newtonsoft.Json.JsonProperty("unplannedTheatreCode", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string UnplannedTheatreCode { get; set; }
    
        [Newtonsoft.Json.JsonProperty("careType", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string CareType { get; set; }
    
        [Newtonsoft.Json.JsonProperty("interHospitalContractStatus", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string InterHospitalContractStatus { get; set; }
    
        [Newtonsoft.Json.JsonProperty("unplannedReturnTheatre", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string UnplannedReturnTheatre { get; set; }
    
        [Newtonsoft.Json.JsonProperty("unplannedAdmissionICU", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string UnplannedAdmissionICU { get; set; }
    
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v11.0.0.0)")]
    public partial class MedicalServiceSegmentType : MessagePartType
    {
        [Newtonsoft.Json.JsonProperty("chargeAmount", Required = Newtonsoft.Json.Required.Always)]
        public int ChargeAmount { get; set; }
    
        [Newtonsoft.Json.JsonProperty("chargeRaisedCode", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string ChargeRaisedCode { get; set; }
    
        [Newtonsoft.Json.JsonProperty("payeeProvider", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string PayeeProvider { get; set; }
    
        [Newtonsoft.Json.JsonProperty("serviceCode", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string ServiceCode { get; set; }
    
        [Newtonsoft.Json.JsonProperty("serviceCodeTypeCode", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string ServiceCodeTypeCode { get; set; }
    
        [Newtonsoft.Json.JsonProperty("serviceDate", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        [Newtonsoft.Json.JsonConverter(typeof(DateFormatConverter))]
        public System.DateTimeOffset ServiceDate { get; set; }
    
        [Newtonsoft.Json.JsonProperty("serviceProviderNumber", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string ServiceProviderNumber { get; set; }
    
        [Newtonsoft.Json.JsonProperty("serviceProviderName", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string ServiceProviderName { get; set; }
    
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v11.0.0.0)")]
    public partial class MiscellaneousServiceSegmentType : MessagePartType
    {
        [Newtonsoft.Json.JsonProperty("chargeAmount", Required = Newtonsoft.Json.Required.Always)]
        public int ChargeAmount { get; set; }
    
        [Newtonsoft.Json.JsonProperty("chargeRaisedCode", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string ChargeRaisedCode { get; set; }
    
        [Newtonsoft.Json.JsonProperty("serviceCode", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string ServiceCode { get; set; }
    
        [Newtonsoft.Json.JsonProperty("serviceDate", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        [Newtonsoft.Json.JsonConverter(typeof(DateFormatConverter))]
        public System.DateTimeOffset ServiceDate { get; set; }
    
        [Newtonsoft.Json.JsonProperty("serviceQuantity", Required = Newtonsoft.Json.Required.Always)]
        public int ServiceQuantity { get; set; }
    
        [Newtonsoft.Json.JsonProperty("serviceRate", Required = Newtonsoft.Json.Required.Always)]
        public int ServiceRate { get; set; }
    
        [Newtonsoft.Json.JsonProperty("text", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Text { get; set; }
    
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v11.0.0.0)")]
    public partial class NonDRGMorbiditySegmentType : MessagePartType
    {
        [Newtonsoft.Json.JsonProperty("ansnapClass", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string AnsnapClass { get; set; }
    
        [Newtonsoft.Json.JsonProperty("ansnapId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string AnsnapId { get; set; }
    
        [Newtonsoft.Json.JsonProperty("arocImpairmentCode", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ArocImpairmentCode { get; set; }
    
        [Newtonsoft.Json.JsonProperty("casemixCode", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CasemixCode { get; set; }
    
        [Newtonsoft.Json.JsonProperty("casemixCodeTypeCode", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string CasemixCodeTypeCode { get; set; }
    
        [Newtonsoft.Json.JsonProperty("admissionFimScore", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [System.ComponentModel.DataAnnotations.MinLength(1)]
        public System.Collections.Generic.ICollection<FimScoreType> AdmissionFimScore { get; set; }
    
        [Newtonsoft.Json.JsonProperty("dischargeFimScore", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [System.ComponentModel.DataAnnotations.MinLength(1)]
        public System.Collections.Generic.ICollection<FimScoreType> DischargeFimScore { get; set; }
    
        [Newtonsoft.Json.JsonProperty("ansnapVersion", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string AnsnapVersion { get; set; }
    
        [Newtonsoft.Json.JsonProperty("modeOfEpisodeStartInpatient", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ModeOfEpisodeStartInpatient { get; set; }
    
        [Newtonsoft.Json.JsonProperty("modeOfEpisodeEndInpatient", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ModeOfEpisodeEndInpatient { get; set; }
    
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v11.0.0.0)")]
    public partial class FimScoreType 
    {
        [Newtonsoft.Json.JsonProperty("scoreCode", Required = Newtonsoft.Json.Required.Always)]
        public int ScoreCode { get; set; }
    
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v11.0.0.0)")]
    public partial class HospitalPatientType : MessagePartType
    {
        [Newtonsoft.Json.JsonProperty("admissionWeight", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? AdmissionWeight { get; set; }
    
        [Newtonsoft.Json.JsonProperty("medicalRecordId", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string MedicalRecordId { get; set; }
    
        [Newtonsoft.Json.JsonProperty("symptomAwarenessDate", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(DateFormatConverter))]
        public System.DateTimeOffset? SymptomAwarenessDate { get; set; }
    
        [Newtonsoft.Json.JsonProperty("alsoKnownAs", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public IdentityType AlsoKnownAs { get; set; }
    
        [Newtonsoft.Json.JsonProperty("fundMembership", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required]
        public FundMembershipType FundMembership { get; set; } = new FundMembershipType();
    
        [Newtonsoft.Json.JsonProperty("patient", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required]
        public PatientType Patient { get; set; } = new PatientType();
    
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v11.0.0.0)")]
    public partial class FundMembershipType 
    {
        [Newtonsoft.Json.JsonProperty("memberNumber", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [System.ComponentModel.DataAnnotations.StringLength(19, MinimumLength = 1)]
        public string MemberNumber { get; set; }
    
        [Newtonsoft.Json.JsonProperty("memberRefNumber", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [System.ComponentModel.DataAnnotations.StringLength(2, MinimumLength = 1)]
        public string MemberRefNumber { get; set; }
    
        [Newtonsoft.Json.JsonProperty("organisation", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [System.ComponentModel.DataAnnotations.StringLength(3, MinimumLength = 3)]
        public string Organisation { get; set; }
    
    
    }
    
    //[Newtonsoft.Json.JsonConverter(typeof(JsonInheritanceConverter), "patientType")]
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v11.0.0.0)")]
    public partial class PatientType 
    {
        [Newtonsoft.Json.JsonProperty("identity", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required]
        public IdentityType Identity { get; set; } = new IdentityType();
    
        [Newtonsoft.Json.JsonProperty("residentialAddress", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public AddressType ResidentialAddress { get; set; }
    
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v11.0.0.0)")]
    public partial class AddressType 
    {
        [Newtonsoft.Json.JsonProperty("addressLineOne", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [System.ComponentModel.DataAnnotations.StringLength(40, MinimumLength = 1)]
        public string AddressLineOne { get; set; }
    
        [Newtonsoft.Json.JsonProperty("addressLineTwo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [System.ComponentModel.DataAnnotations.StringLength(40, MinimumLength = 1)]
        public string AddressLineTwo { get; set; }
    
        [Newtonsoft.Json.JsonProperty("locality", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [System.ComponentModel.DataAnnotations.StringLength(40, MinimumLength = 1)]
        public string Locality { get; set; }
    
        [Newtonsoft.Json.JsonProperty("postcode", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [System.ComponentModel.DataAnnotations.StringLength(4, MinimumLength = 4)]
        public string Postcode { get; set; }
    
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v11.0.0.0)")]
    public partial class PrincipalServiceSegmentType : MultipleServiceSegmentType
    {
        [Newtonsoft.Json.JsonProperty("anaestheticTypeCode", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string AnaestheticTypeCode { get; set; }
    
        [Newtonsoft.Json.JsonProperty("serviceDate", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        [Newtonsoft.Json.JsonConverter(typeof(DateFormatConverter))]
        public System.DateTimeOffset ServiceDate { get; set; }
    
        [Newtonsoft.Json.JsonProperty("serviceTime", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [System.ComponentModel.DataAnnotations.RegularExpression(@"^([0-1][0-9]|2[0-3]):[0-5][0-9]:([0-5][0-9]|60)([.][0-9]*)?(Z|z|(([+]|-)([0-1][0-9]|2[0-3]):[0-5][0-9]))?$")]
        public string ServiceTime { get; set; }
    
        [Newtonsoft.Json.JsonProperty("theatreMinutes", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? TheatreMinutes { get; set; }
    
        [Newtonsoft.Json.JsonProperty("totalChargeAmount", Required = Newtonsoft.Json.Required.Always)]
        public int TotalChargeAmount { get; set; }
    
        [Newtonsoft.Json.JsonProperty("multipleService", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [System.ComponentModel.DataAnnotations.MinLength(1)]
        public System.Collections.Generic.ICollection<MultipleServiceSegmentType> MultipleService { get; set; }
    
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v11.0.0.0)")]
    public partial class MultipleServiceSegmentType : MessagePartType
    {
        [Newtonsoft.Json.JsonProperty("chargeAmount", Required = Newtonsoft.Json.Required.Always)]
        public int ChargeAmount { get; set; }
    
        [Newtonsoft.Json.JsonProperty("chargeRaisedCode", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string ChargeRaisedCode { get; set; }
    
        [Newtonsoft.Json.JsonProperty("serviceCode", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string ServiceCode { get; set; }
    
        [Newtonsoft.Json.JsonProperty("serviceCodeTypeCode", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string ServiceCodeTypeCode { get; set; }
    
        [Newtonsoft.Json.JsonProperty("theatreBandCode", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string TheatreBandCode { get; set; }
    
        [Newtonsoft.Json.JsonProperty("theatreBandTypeCode", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string TheatreBandTypeCode { get; set; }
    
        [Newtonsoft.Json.JsonProperty("theatreCategoryCode", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string TheatreCategoryCode { get; set; }
    
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v11.0.0.0)")]
    public partial class RemarksSegmentType : MessagePartType
    {
        [Newtonsoft.Json.JsonProperty("text", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string Text { get; set; }
    
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v11.0.0.0)")]
    public partial class ContactType 
    {
        [Newtonsoft.Json.JsonProperty("emailAddress", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [System.ComponentModel.DataAnnotations.StringLength(128, MinimumLength = 5)]
        public string EmailAddress { get; set; }
    
        [Newtonsoft.Json.JsonProperty("name", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Name { get; set; }
    
        [Newtonsoft.Json.JsonProperty("phoneNumber", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [System.ComponentModel.DataAnnotations.StringLength(19, MinimumLength = 8)]
        public string PhoneNumber { get; set; }
    
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v11.0.0.0)")]
    public partial class SingleValueBenefitSegmentType : MessagePartType
    {
        [Newtonsoft.Json.JsonProperty("chargeRaisedCode", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string ChargeRaisedCode { get; set; }
    
        [Newtonsoft.Json.JsonProperty("fromDate", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(DateFormatConverter))]
        public System.DateTimeOffset? FromDate { get; set; }
    
        [Newtonsoft.Json.JsonProperty("numberOfDays", Required = Newtonsoft.Json.Required.Always)]
        public int NumberOfDays { get; set; }
    
        [Newtonsoft.Json.JsonProperty("toDate", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(DateFormatConverter))]
        public System.DateTimeOffset? ToDate { get; set; }
    
        [Newtonsoft.Json.JsonProperty("chargeAmount", Required = Newtonsoft.Json.Required.Always)]
        public int ChargeAmount { get; set; }
    
        [Newtonsoft.Json.JsonProperty("serviceCode", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string ServiceCode { get; set; }
    
        [Newtonsoft.Json.JsonProperty("serviceCodeTypeCode", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string ServiceCodeTypeCode { get; set; }
    
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v11.0.0.0)")]
    public partial class HospitalClaimSummaryType 
    {
        [Newtonsoft.Json.JsonProperty("accountReferenceId", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string AccountReferenceId { get; set; }
    
        [Newtonsoft.Json.JsonProperty("facilityId", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required]
        [System.ComponentModel.DataAnnotations.StringLength(8, MinimumLength = 8)]
        public string FacilityId { get; set; }
    
        [Newtonsoft.Json.JsonProperty("typeCode", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string TypeCode { get; set; }
    
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v11.0.0.0)")]
    public partial class TransferSegmentType : MessagePartType
    {
        [Newtonsoft.Json.JsonProperty("facilityId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [System.ComponentModel.DataAnnotations.StringLength(8, MinimumLength = 8)]
        public string FacilityId { get; set; }
    
        [Newtonsoft.Json.JsonProperty("previousProviderDays", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? PreviousProviderDays { get; set; }
    
        [Newtonsoft.Json.JsonProperty("previousProviderHours", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? PreviousProviderHours { get; set; }
    
        [Newtonsoft.Json.JsonProperty("serviceDate", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        [Newtonsoft.Json.JsonConverter(typeof(DateFormatConverter))]
        public System.DateTimeOffset ServiceDate { get; set; }
    
        [Newtonsoft.Json.JsonProperty("transferCode", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string TransferCode { get; set; }
    
        [Newtonsoft.Json.JsonProperty("transferTypeCode", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string TransferTypeCode { get; set; }
    
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v11.0.0.0)")]
    public partial class TransportSegmentType : MessagePartType
    {
        [Newtonsoft.Json.JsonProperty("ambulanceReferenceNumber", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string AmbulanceReferenceNumber { get; set; }
    
        [Newtonsoft.Json.JsonProperty("chargeAmount", Required = Newtonsoft.Json.Required.Always)]
        public int ChargeAmount { get; set; }
    
        [Newtonsoft.Json.JsonProperty("chargeRaisedCode", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string ChargeRaisedCode { get; set; }
    
        [Newtonsoft.Json.JsonProperty("distanceKilometres", Required = Newtonsoft.Json.Required.Always)]
        public int DistanceKilometres { get; set; }
    
        [Newtonsoft.Json.JsonProperty("fromLocality", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string FromLocality { get; set; }
    
        [Newtonsoft.Json.JsonProperty("fromTime", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        [System.ComponentModel.DataAnnotations.RegularExpression(@"^([0-1][0-9]|2[0-3]):[0-5][0-9]:([0-5][0-9]|60)([.][0-9]*)?(Z|z|(([+]|-)([0-1][0-9]|2[0-3]):[0-5][0-9]))?$")]
        public string FromTime { get; set; }
    
        [Newtonsoft.Json.JsonProperty("serviceDate", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        [Newtonsoft.Json.JsonConverter(typeof(DateFormatConverter))]
        public System.DateTimeOffset ServiceDate { get; set; }
    
        [Newtonsoft.Json.JsonProperty("toLocality", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string ToLocality { get; set; }
    
        [Newtonsoft.Json.JsonProperty("transportHoursMinutes", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required]
        [System.ComponentModel.DataAnnotations.StringLength(4, MinimumLength = 4)]
        public string TransportHoursMinutes { get; set; }
    
        [Newtonsoft.Json.JsonProperty("transportTypeCode", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string TransportTypeCode { get; set; }
    
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v11.0.0.0)")]
    public partial class InHospitalClaimResponseType 
    {
        [Newtonsoft.Json.JsonProperty("fundAssessment", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public HospitalClaimAssessmentType FundAssessment { get; set; }
    
        [Newtonsoft.Json.JsonProperty("fundStatus", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public MembershipStatusType FundStatus { get; set; }
    
        [Newtonsoft.Json.JsonProperty("status", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Status { get; set; }
    
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v11.0.0.0)")]
    public partial class HospitalClaimAssessmentType 
    {
        [Newtonsoft.Json.JsonProperty("accommodationBenefit", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? AccommodationBenefit { get; set; }
    
        [Newtonsoft.Json.JsonProperty("accountReferenceId", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string AccountReferenceId { get; set; }
    
        [Newtonsoft.Json.JsonProperty("ancillaryBenefits", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? AncillaryBenefits { get; set; }
    
        [Newtonsoft.Json.JsonProperty("ancillaryCharges", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? AncillaryCharges { get; set; }
    
        [Newtonsoft.Json.JsonProperty("ancillaryCoverStatus", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string AncillaryCoverStatus { get; set; }
    
        [Newtonsoft.Json.JsonProperty("assessmentCode", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string AssessmentCode { get; set; }
    
        [Newtonsoft.Json.JsonProperty("bundledBenefits", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? BundledBenefits { get; set; }
    
        [Newtonsoft.Json.JsonProperty("coPaymentAmount", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? CoPaymentAmount { get; set; }
    
        [Newtonsoft.Json.JsonProperty("coronaryCareUnitBenefits", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? CoronaryCareUnitBenefits { get; set; }
    
        [Newtonsoft.Json.JsonProperty("excessAmount", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? ExcessAmount { get; set; }
    
        [Newtonsoft.Json.JsonProperty("facilityId", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required]
        [System.ComponentModel.DataAnnotations.StringLength(8, MinimumLength = 8)]
        public string FacilityId { get; set; }
    
        [Newtonsoft.Json.JsonProperty("frontEndDeductible", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? FrontEndDeductible { get; set; }
    
        [Newtonsoft.Json.JsonProperty("hospitalContractStatus", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string HospitalContractStatus { get; set; }
    
        [Newtonsoft.Json.JsonProperty("hospitalInTheHomeCareBenefits", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? HospitalInTheHomeCareBenefits { get; set; }
    
        [Newtonsoft.Json.JsonProperty("intensiveCareUnitBenefit", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? IntensiveCareUnitBenefit { get; set; }
    
        [Newtonsoft.Json.JsonProperty("labourWardBenefit", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? LabourWardBenefit { get; set; }
    
        [Newtonsoft.Json.JsonProperty("otherBenefits", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? OtherBenefits { get; set; }
    
        [Newtonsoft.Json.JsonProperty("personIdentifier", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string PersonIdentifier { get; set; }
    
        [Newtonsoft.Json.JsonProperty("pharmacyBenefit", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? PharmacyBenefit { get; set; }
    
        [Newtonsoft.Json.JsonProperty("organisation", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string Organisation { get; set; }
    
        [Newtonsoft.Json.JsonProperty("fundReferenceId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string FundReferenceId { get; set; }
    
        [Newtonsoft.Json.JsonProperty("productCode", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ProductCode { get; set; }
    
        [Newtonsoft.Json.JsonProperty("prosthesisBenefit", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? ProsthesisBenefit { get; set; }
    
        [Newtonsoft.Json.JsonProperty("specialCareNurseryBenefits", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? SpecialCareNurseryBenefits { get; set; }
    
        [Newtonsoft.Json.JsonProperty("theatreBenefit", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? TheatreBenefit { get; set; }
    
        [Newtonsoft.Json.JsonProperty("totalBenefitAmount", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? TotalBenefitAmount { get; set; }
    
        [Newtonsoft.Json.JsonProperty("totalChargeAmount", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? TotalChargeAmount { get; set; }
    
        [Newtonsoft.Json.JsonProperty("totalDaysPaid", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? TotalDaysPaid { get; set; }
    
        [Newtonsoft.Json.JsonProperty("totalProstheticItemBenefit", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? TotalProstheticItemBenefit { get; set; }
    
        [Newtonsoft.Json.JsonProperty("claimAssessment", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [System.ComponentModel.DataAnnotations.MinLength(1)]
        public System.Collections.Generic.ICollection<HospitalClaimExplanationType> ClaimAssessment { get; set; }
    
        [Newtonsoft.Json.JsonProperty("serviceAssessment", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [System.ComponentModel.DataAnnotations.MinLength(1)]
        public System.Collections.Generic.ICollection<HospitalServiceAssessmentType> ServiceAssessment { get; set; }
    
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v11.0.0.0)")]
    public partial class HospitalClaimExplanationType 
    {
        [Newtonsoft.Json.JsonProperty("explanation", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ExplanationType Explanation { get; set; }
    
        [Newtonsoft.Json.JsonProperty("elementName", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ElementName { get; set; }
    
        [Newtonsoft.Json.JsonProperty("messagePartId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string MessagePartId { get; set; }
    
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v11.0.0.0)")]
    public partial class ExplanationType 
    {
        [Newtonsoft.Json.JsonProperty("code", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Code { get; set; }
    
        [Newtonsoft.Json.JsonProperty("text", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Text { get; set; }
    
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v11.0.0.0)")]
    public partial class HospitalServiceAssessmentType 
    {
        [Newtonsoft.Json.JsonProperty("chargeAmount", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? ChargeAmount { get; set; }
    
        [Newtonsoft.Json.JsonProperty("code", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Code { get; set; }
    
        [Newtonsoft.Json.JsonProperty("description", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Description { get; set; }
    
        [Newtonsoft.Json.JsonProperty("fromDate", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(DateFormatConverter))]
        public System.DateTimeOffset? FromDate { get; set; }
    
        [Newtonsoft.Json.JsonProperty("numberOfServices", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? NumberOfServices { get; set; }
    
        [Newtonsoft.Json.JsonProperty("fundBenefit", Required = Newtonsoft.Json.Required.Always)]
        public int FundBenefit { get; set; }
    
        [Newtonsoft.Json.JsonProperty("serviceDate", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(DateFormatConverter))]
        public System.DateTimeOffset? ServiceDate { get; set; }
    
        [Newtonsoft.Json.JsonProperty("toDate", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(DateFormatConverter))]
        public System.DateTimeOffset? ToDate { get; set; }
    
        [Newtonsoft.Json.JsonProperty("explanation", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [System.ComponentModel.DataAnnotations.MinLength(1)]
        public System.Collections.Generic.ICollection<ExplanationType> Explanation { get; set; }
    
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v11.0.0.0)")]
    public partial class MembershipStatusType 
    {
        [Newtonsoft.Json.JsonProperty("status", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public StatusType Status { get; set; }
    
        [Newtonsoft.Json.JsonProperty("currentMembership", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public MembershipType CurrentMembership { get; set; }
    
        [Newtonsoft.Json.JsonProperty("currentMember", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public IdentityType CurrentMember { get; set; }
    
        [Newtonsoft.Json.JsonProperty("processDate", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(DateFormatConverter))]
        public System.DateTimeOffset? ProcessDate { get; set; }
    
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v11.0.0.0)")]
    public partial class StatusType 
    {
        [Newtonsoft.Json.JsonProperty("code", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Code { get; set; }
    
        [Newtonsoft.Json.JsonProperty("text", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Text { get; set; }
    
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v11.0.0.0)")]
    public partial class MembershipType 
    {
        [Newtonsoft.Json.JsonProperty("memberNumber", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [System.ComponentModel.DataAnnotations.StringLength(10, MinimumLength = 10)]
        public string MemberNumber { get; set; }
    
        [Newtonsoft.Json.JsonProperty("memberRefNumber", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [System.ComponentModel.DataAnnotations.StringLength(1, MinimumLength = 1)]
        public string MemberRefNumber { get; set; }
    
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v11.0.0.0)")]
    public partial class ServiceMessagesType 
    {
        [Newtonsoft.Json.JsonProperty("highestSeverity", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public ServiceMessagesTypeHighestSeverity HighestSeverity { get; set; }
    
        [Newtonsoft.Json.JsonProperty("serviceMessage", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required]
        [System.ComponentModel.DataAnnotations.MinLength(1)]
        public System.Collections.Generic.ICollection<ServiceMessageType> ServiceMessage { get; set; } = new System.Collections.ObjectModel.Collection<ServiceMessageType>();
    
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v11.0.0.0)")]
    public partial class ServiceMessageType 
    {
        [Newtonsoft.Json.JsonProperty("code", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string Code { get; set; }
    
        [Newtonsoft.Json.JsonProperty("severity", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public ServiceMessageTypeSeverity Severity { get; set; }
    
        [Newtonsoft.Json.JsonProperty("reason", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string Reason { get; set; }
    
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v11.0.0.0)")]
    public enum ServiceMessagesTypeHighestSeverity
    {
        [System.Runtime.Serialization.EnumMember(Value = @"Fatal")]
        Fatal = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = @"Error")]
        Error = 1,
    
        [System.Runtime.Serialization.EnumMember(Value = @"Warning")]
        Warning = 2,
    
        [System.Runtime.Serialization.EnumMember(Value = @"Informational")]
        Informational = 3,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v11.0.0.0)")]
    public enum ServiceMessageTypeSeverity
    {
        [System.Runtime.Serialization.EnumMember(Value = @"Fatal")]
        Fatal = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = @"Error")]
        Error = 1,
    
        [System.Runtime.Serialization.EnumMember(Value = @"Warning")]
        Warning = 2,
    
        [System.Runtime.Serialization.EnumMember(Value = @"Informational")]
        Informational = 3,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v11.0.0.0)")]
    [System.AttributeUsage(System.AttributeTargets.Class, AllowMultiple = true)]
    internal class JsonInheritanceAttribute : System.Attribute
    {
        public JsonInheritanceAttribute(string key, System.Type type)
        {
            Key = key;
            Type = type;
        }
    
        public string Key { get; }
    
        public System.Type Type { get; }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v11.0.0.0)")]
    internal class JsonInheritanceConverter : Newtonsoft.Json.JsonConverter
    {
        internal static readonly string DefaultDiscriminatorName = "discriminator";
    
        private readonly string _discriminator;
    
        [System.ThreadStatic]
        private static bool _isReading;
    
        [System.ThreadStatic]
        private static bool _isWriting;
    
        public JsonInheritanceConverter()
        {
            _discriminator = DefaultDiscriminatorName;
        }
    
        public JsonInheritanceConverter(string discriminator)
        {
            _discriminator = discriminator;
        }
    
        public override void WriteJson(Newtonsoft.Json.JsonWriter writer, object value, Newtonsoft.Json.JsonSerializer serializer)
        {
            try
            {
                _isWriting = true;
    
                var jObject = Newtonsoft.Json.Linq.JObject.FromObject(value, serializer);
                jObject.AddFirst(new Newtonsoft.Json.Linq.JProperty(_discriminator, GetSubtypeDiscriminator(value.GetType())));
                writer.WriteToken(jObject.CreateReader());
            }
            finally
            {
                _isWriting = false;
            }
        }
    
        public override bool CanWrite
        {
            get
            {
                if (_isWriting)
                {
                    _isWriting = false;
                    return false;
                }
                return true;
            }
        }
    
        public override bool CanRead
        {
            get
            {
                if (_isReading)
                {
                    _isReading = false;
                    return false;
                }
                return true;
            }
        }
    
        public override bool CanConvert(System.Type objectType)
        {
            return true;
        }
    
        public override object ReadJson(Newtonsoft.Json.JsonReader reader, System.Type objectType, object existingValue, Newtonsoft.Json.JsonSerializer serializer)
        {
            var jObject = serializer.Deserialize<Newtonsoft.Json.Linq.JObject>(reader);
            if (jObject == null)
                return null;

            //var discriminator = Newtonsoft.Json.Linq.Extensions.Value<string>(jObject.GetValue(_discriminator));
            //var subtype = GetObjectSubtype(objectType, discriminator);
           
            //var objectContract = serializer.ContractResolver.ResolveContract(subtype) as Newtonsoft.Json.Serialization.JsonObjectContract;
            //if (objectContract == null || System.Linq.Enumerable.All(objectContract.Properties, p => p.PropertyName != _discriminator))
            //{
            //    jObject.Remove(_discriminator);
            //}
    
            try
            {
                _isReading = true;
                return serializer.Deserialize(jObject.CreateReader(), objectType);
            }
            finally
            {
                _isReading = false;
            }
        }
    
        private System.Type GetObjectSubtype(System.Type objectType, string discriminator)
        {
            foreach (var attribute in System.Reflection.CustomAttributeExtensions.GetCustomAttributes<JsonInheritanceAttribute>(System.Reflection.IntrospectionExtensions.GetTypeInfo(objectType), true))
            {
                if (attribute.Key == discriminator)
                    return attribute.Type;
            }
    
            return objectType;
        }
    
        private string GetSubtypeDiscriminator(System.Type objectType)
        {
            foreach (var attribute in System.Reflection.CustomAttributeExtensions.GetCustomAttributes<JsonInheritanceAttribute>(System.Reflection.IntrospectionExtensions.GetTypeInfo(objectType), true))
            {
                if (attribute.Type == objectType)
                    return attribute.Key;
            }
    
            return objectType.Name;
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v11.0.0.0)")]
    internal class DateFormatConverter : Newtonsoft.Json.Converters.IsoDateTimeConverter
    {
        public DateFormatConverter()
        {
            DateTimeFormat = "yyyy-MM-dd";
        }
    }

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "13.0.5.0 (NJsonSchema v********* (Newtonsoft.Json v11.0.0.0))")]
    public partial class ApiException : System.Exception
    {
        public int StatusCode { get; private set; }

        public string Response { get; private set; }

        public System.Collections.Generic.IReadOnlyDictionary<string, System.Collections.Generic.IEnumerable<string>> Headers { get; private set; }

        public ApiException(string message, int statusCode, string response, System.Collections.Generic.IReadOnlyDictionary<string, System.Collections.Generic.IEnumerable<string>> headers, System.Exception innerException) 
            : base(message + "\n\nStatus: " + statusCode + "\nResponse: \n" + response.Substring(0, response.Length >= 512 ? 512 : response.Length), innerException)
        {
            StatusCode = statusCode;
            Response = response; 
            Headers = headers;
        }

        public override string ToString()
        {
            return string.Format("HTTP Response: \n\n{0}\n\n{1}", Response, base.ToString());
        }
    }

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "13.0.5.0 (NJsonSchema v********* (Newtonsoft.Json v11.0.0.0))")]
    public partial class ApiException<TResult> : ApiException
    {
        public TResult Result { get; private set; }

        public ApiException(string message, int statusCode, string response, System.Collections.Generic.IReadOnlyDictionary<string, System.Collections.Generic.IEnumerable<string>> headers, TResult result, System.Exception innerException) 
            : base(message, statusCode, response, headers, innerException)
        {
            Result = result;
        }
    }

}

#pragma warning restore 1591
#pragma warning restore 1573
#pragma warning restore  472
#pragma warning restore  114
#pragma warning restore  108