﻿namespace Capstone2.RestServices.Invoice.Utility
{
    public static class Constants
    {
        public const string CLAIMTYPE_GENERAL = "O";
        public const string CLAIMTYPE_SPECIALIST = "S";

        public const string INVALID_MEDICARE_NUMBER_400 = "The check digit becomes the 9th digit in the Medicare Card number. The 10th digit is the Medicare Card number and must not be zero.";
        public const string INVALID_MEDICARE_MEMBER_NUMBER_400 = "Patient Medicare Card Number must be supplied";

        public const string ECLIPSE_CLAIMTYPE_AG = "AG";
        public const string ECLIPSE_CLAIMTYPE_MB = "MB";
        public const string ECLIPSE_CLAIMTYPE_SC = "SC";
        public const string ECLIPSE_CLAIMTYPE_PC = "PC";

        public const string ECLIPSE_SERVICECODETYPECODE_MBS = "C";
        public const string ECLIPSE_SERVICECODETYPECODE_MISC = "M";
        public const string ECLIPSE_SERVICECODETYPECODE_PROSTHETIC = "P";

        public const string ECLIPSE_OEC_TYPECODE_OEC = "OEC";
        public const string ECLIPSE_OEC_TYPECODE_ECF = "ECF";
        public const string ECLIPSE_OEC_TYPECODE_ECM = "ECM";

        public const string IMC_STATUS_SUCCESS = "SUCCESS";
        public const string IMC_STATUS_RECEIVED = "RECEIVED";
        public const string IMC_STATUS_MEDICARE_VERIFIED= "MEDICARE_VERIFIED";
        public const string IMC_STATUS_MEDICARE_REJECTED = "MEDICARE_REJECTED";
        public const string IMC_STATUS_HEALTH_FUND_VERIFIED = "HEALTH_FUND_VERIFIED";
        public const string IMC_STATUS_HEALTH_FUND_REJECTED = "HEALTH_FUND_REJECTED";

    }

    public static class MedicareRequestType
    {
        public const int MEDICARE_EXCEPTION = 1;
        public const int BULK_BILL = 2;
        public const int DVA = 3;
        public const int IMC = 4;
    }

    public enum InvoiceRequestType
    {
        InvoiceConversion = 1,
        Adjustment = 2,
        InvoiceAdded = 3,
        InvoiceUpdated = 4,
        InvoiceSummaryStatus = 5
    }

    public enum IFCIssueCode
    {
        V ,
        W ,
        N ,
        X 
    }
}
