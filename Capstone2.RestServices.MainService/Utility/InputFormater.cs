﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Formatters;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.ObjectPool;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System.Buffers;

namespace Capstone2.RestServices.MainService.Utility
{
    public class InputFormater : NewtonsoftJsonInputFormatter
    {
        public InputFormater(ILogger logger, JsonSerializerSettings serializerSettings, ArrayPool<char> charPool, ObjectPoolProvider objectPoolProvider, MvcOptions options, MvcNewtonsoftJsonOptions jsonOptions) : base(logger, serializerSettings, charPool, objectPoolProvider, options, jsonOptions)
        {
            // Use consistent null handling to prevent BodyModelBinder null reference exceptions
            serializerSettings.NullValueHandling = NullValueHandling.Include;
            serializerSettings.MissingMemberHandling = MissingMemberHandling.Ignore;
            serializerSettings.DefaultValueHandling = DefaultValueHandling.Include;
        }

        public static InputFormater GetInputFormater()
        {
            var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
            var logger = loggerFactory.CreateLogger<InputFormater>();
            var serializerSettings = new JsonSerializerSettings
            {
                // Consistent with global configuration to prevent null reference exceptions
                NullValueHandling = NullValueHandling.Include,
                ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                ContractResolver = new DefaultContractResolver(),
                MissingMemberHandling = MissingMemberHandling.Ignore,
                DefaultValueHandling = DefaultValueHandling.Include,
                // Add error handling for malformed JSON
                Error = (sender, args) =>
                {
                    args.ErrorContext.Handled = true;
                }
            };
             var options = new MvcOptions();
            var jsonOptions = new MvcNewtonsoftJsonOptions();
            return new InputFormater(logger, serializerSettings, ArrayPool<char>.Shared, new DefaultObjectPoolProvider(), options, jsonOptions);

        }
    }
}
