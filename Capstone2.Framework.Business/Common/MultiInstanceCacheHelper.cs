using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using System;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using System.Threading;
using System.Linq;

namespace Capstone2.Framework.Business.Common
{
    public interface IMultiInstanceCacheHelper
    {
        Task<T> GetFromCacheWithFallback<T>(string cacheKey, Func<Task<T>> fallbackFactory, TimeSpan? expiration = null);
        Task<T> GetFromCache<T>(string cacheKey);
        Task SetIntoCache<T>(T data, string cacheKey, TimeSpan? expiration = null);
        Task RemoveCache(string cacheKey);
        Task<bool> TryLockAsync(string lockKey, TimeSpan lockDuration, Func<Task> action);
    }

    public class MultiInstanceCacheHelper : IMultiInstanceCacheHelper
    {
        private readonly IDistributedCache _distributedCache;
        private readonly ILogger<MultiInstanceCacheHelper> _logger;
        private readonly SemaphoreSlim _localSemaphore = new SemaphoreSlim(1, 1);

        public MultiInstanceCacheHelper(IDistributedCache distributedCache, ILogger<MultiInstanceCacheHelper> logger)
        {
            _distributedCache = distributedCache;
            _logger = logger;
        }

        /// <summary>
        /// Gets data from cache with fallback to factory method if cache miss
        /// Handles multi-instance scenarios with distributed locking
        /// </summary>
        public async Task<T> GetFromCacheWithFallback<T>(string cacheKey, Func<Task<T>> fallbackFactory, TimeSpan? expiration = null)
        {
            try
            {
                // First try to get from cache
                var cachedData = await GetFromCache<T>(cacheKey);
                if (cachedData != null)
                {
                    return cachedData;
                }

                // If cache miss, use distributed lock to prevent multiple instances from executing the same expensive operation
                var lockKey = $"lock:{cacheKey}";
                var lockDuration = TimeSpan.FromMinutes(5); // Adjust based on your needs

                T result = default(T);
                bool lockAcquired = false;

                try
                {
                    lockAcquired = await TryAcquireLockAsync(lockKey, lockDuration);
                    
                    if (lockAcquired)
                    {
                        // Double-check cache after acquiring lock (another instance might have populated it)
                        cachedData = await GetFromCache<T>(cacheKey);
                        if (cachedData != null)
                        {
                            return cachedData;
                        }

                        // Execute the factory method
                        result = await fallbackFactory();
                        
                        // Cache the result
                        if (result != null)
                        {
                            await SetIntoCache(result, cacheKey, expiration);
                        }
                    }
                    else
                    {
                        // If we couldn't acquire the lock, wait a bit and try cache again
                        await Task.Delay(100);
                        cachedData = await GetFromCache<T>(cacheKey);
                        if (cachedData != null)
                        {
                            return cachedData;
                        }

                        // If still no cache hit, execute fallback without caching
                        _logger.LogWarning($"Could not acquire lock for {lockKey}, executing fallback without caching");
                        result = await fallbackFactory();
                    }
                }
                finally
                {
                    if (lockAcquired)
                    {
                        await ReleaseLockAsync(lockKey);
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error in GetFromCacheWithFallback for key: {cacheKey}");
                
                // If all else fails, execute the fallback
                try
                {
                    return await fallbackFactory();
                }
                catch (Exception fallbackEx)
                {
                    _logger.LogError(fallbackEx, $"Fallback factory also failed for key: {cacheKey}");
                    throw;
                }
            }
        }

        public async Task<T> GetFromCache<T>(string cacheKey)
        {
            try
            {
                var dataFromCache = await _distributedCache.GetAsync(cacheKey);
                if (dataFromCache?.Length > 0)
                {
                    var dataToString = Encoding.UTF8.GetString(dataFromCache);
                    return JsonSerializer.Deserialize<T>(dataToString);
                }
                return default(T);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting from cache for key: {cacheKey}");
                return default(T);
            }
        }

        public async Task SetIntoCache<T>(T data, string cacheKey, TimeSpan? expiration = null)
        {
            try
            {
                var cacheOptions = new DistributedCacheEntryOptions();
                
                if (expiration.HasValue)
                {
                    cacheOptions.SetAbsoluteExpiration(expiration.Value);
                }
                else
                {
                    cacheOptions.SetAbsoluteExpiration(TimeSpan.FromHours(23)); // Default expiration
                }

                var cachedDataString = JsonSerializer.Serialize(data);
                var dataToCache = Encoding.UTF8.GetBytes(cachedDataString);
                
                await _distributedCache.SetAsync(cacheKey, dataToCache, cacheOptions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error setting cache for key: {cacheKey}");
            }
        }

        public async Task RemoveCache(string cacheKey)
        {
            try
            {
                await _distributedCache.RemoveAsync(cacheKey);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error removing cache for key: {cacheKey}");
            }
        }

        public async Task<bool> TryLockAsync(string lockKey, TimeSpan lockDuration, Func<Task> action)
        {
            bool lockAcquired = false;
            try
            {
                lockAcquired = await TryAcquireLockAsync(lockKey, lockDuration);
                if (lockAcquired)
                {
                    await action();
                    return true;
                }
                return false;
            }
            finally
            {
                if (lockAcquired)
                {
                    await ReleaseLockAsync(lockKey);
                }
            }
        }

        private async Task<bool> TryAcquireLockAsync(string lockKey, TimeSpan lockDuration)
        {
            try
            {
                var lockValue = Environment.MachineName + "_" + Environment.ProcessId + "_" + DateTime.UtcNow.Ticks;
                var lockExpiry = DateTime.UtcNow.Add(lockDuration);
                
                var lockData = new { Value = lockValue, Expiry = lockExpiry };
                var lockDataBytes = Encoding.UTF8.GetBytes(JsonSerializer.Serialize(lockData));
                
                var cacheOptions = new DistributedCacheEntryOptions()
                    .SetAbsoluteExpiration(lockDuration);

                // Try to set the lock - this will fail if the key already exists
                var existingLock = await _distributedCache.GetAsync(lockKey);
                if (existingLock == null)
                {
                    await _distributedCache.SetAsync(lockKey, lockDataBytes, cacheOptions);
                    
                    // Verify we actually got the lock
                    await Task.Delay(10); // Small delay to ensure consistency
                    var verifyLock = await _distributedCache.GetAsync(lockKey);
                    if (verifyLock != null)
                    {
                        var verifyData = JsonSerializer.Deserialize<dynamic>(Encoding.UTF8.GetString(verifyLock));
                        if (verifyData.GetProperty("Value").GetString() == lockValue)
                        {
                            return true;
                        }
                    }
                }
                
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error acquiring lock for key: {lockKey}");
                return false;
            }
        }

        private async Task ReleaseLockAsync(string lockKey)
        {
            try
            {
                await _distributedCache.RemoveAsync(lockKey);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error releasing lock for key: {lockKey}");
            }
        }
    }
}
