﻿using Microsoft.AspNetCore.Http;
using System.Linq;
using System.Net;



namespace Capstone2.Framework.Business.Common
{
    public static class ClientIp
    {
        public static string GetClientIpAddress(HttpContext context)
        {
            IPAddress remoteIpAddress = context.Connection.RemoteIpAddress;
            string result = "";
            if (remoteIpAddress != null)
            {
                // If we got an IPV6 address, then we need to ask the network for the IPV4 address 
                // This usually only happens when the browser is on the same machine as the server.
                if (remoteIpAddress.AddressFamily == System.Net.Sockets.AddressFamily.InterNetworkV6)
                {
                    remoteIpAddress = System.Net.Dns.GetHostEntry(remoteIpAddress).AddressList
                    .First(x => x.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork);

                }
                result = remoteIpAddress.ToString();
            }
            return result;

        }
        public static string GetClientIpAddress(IPAddress remoteIpAddress)
        {
           //IPAddress remoteIpAddress = baseHttpRequestContext.RemoteIpAddress;
            string result = "";
            if (remoteIpAddress != null)
            {
                // If we got an IPV6 address, then we need to ask the network for the IPV4 address 
                // This usually only happens when the browser is on the same machine as the server.
                if (remoteIpAddress.AddressFamily == System.Net.Sockets.AddressFamily.InterNetworkV6)
                {
                    remoteIpAddress = System.Net.Dns.GetHostEntry(remoteIpAddress).AddressList
                    .First(x => x.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork);

                }
                result = remoteIpAddress.ToString();
            }
            return result;

        }
    }
}
