﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using BCrypt.Net;

namespace Capstone2.Framework.Business.Common
{
    public static class BcryptHashingHelper
    {
        public static string GetHashedPassword(string password)
        {
           
            string salt  = BCrypt.Net.BCrypt.GenerateSalt(12);
            
            string passwordHash = BCrypt.Net.BCrypt.HashPassword(password, salt);
            return passwordHash;
        }
        public static bool VerifyHashedPassword(string password, string hashedPassword)
        {            
            return BCrypt.Net.BCrypt.Verify(password, hashedPassword);
        }
    }
}
