﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Capstone2.Framework.Business.Common
{
    public static class CommonFinanceCalculator
    {
        public static decimal CalculateGst (decimal fee,decimal gst)
        {
            decimal gstfee = default(decimal); ;
            if (fee > default(decimal))
            {
                gstfee = Rounding((fee * gst), 2);
            }
            return gstfee;
        }

        public static decimal Rounding(decimal fee,int? precision)
        {
            precision = (precision ==null || precision==default(int))?2: precision;
            return Math.Round(fee, (int)precision);            
        }
        public static decimal CalculateGstFromFeeIncGst(decimal feeIncGst, decimal gst)
        {
            decimal gstfee = default(decimal); ;
            if (feeIncGst > default(decimal))
            {
                gstfee = feeIncGst - (feeIncGst / (1 + gst));
                return Rounding(gstfee, 2);

            }
            return gstfee;
        }
    }
}
