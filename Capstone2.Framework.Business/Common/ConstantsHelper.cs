﻿namespace Capstone2.Framework.Business.Common
{
    public class ConstantsHelper
    {
        private const string HTTP = "http://";
        private const string HTTPS = "https://";
        public static string GetResetPasswordUrl(string subDomain, string domainUrl, string resetPwdEndpoint)
        {
            string requestURL = string.Empty;
            if (!string.IsNullOrWhiteSpace(subDomain) && !string.IsNullOrWhiteSpace(domainUrl) && !string.IsNullOrWhiteSpace(resetPwdEndpoint))
                requestURL = $"{HTTPS}{subDomain}.{domainUrl}/{resetPwdEndpoint}";
            return requestURL;
        }
    }
}
