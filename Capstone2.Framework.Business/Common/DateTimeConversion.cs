﻿using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace Capstone2.Framework.Business.Common
{
    public static class DateTimeConversion
    {

        /// <summary>
        /// Returns TimeZone adjusted time for a given from a Utc or local time.
        /// </summary>
        /// <param name="timeZoneId"></param>
        /// <returns></returns>
        private static TimeZoneInfo TimeZone(string timeZoneId)
        {
            var tzi = TimeZoneInfo.FindSystemTimeZoneById(timeZoneId);
            return tzi;
        }

        /// <summary>
        /// Return Converted Time To UTC without day light hours impact
        /// var dt = new DateTime(2019,10,11,10,10,00,00);
        ///var tz = TimeZoneInfo.FindSystemTimeZoneById("AUS Eastern Standard Time");
        ///10/10/2019 9:10:00 PM utcOffset.ToOffset(tz.GetUtcOffset(utcOffset)).DateTime
        ///10/10/2019 8:10:00 PM utcOffset.ToOffset(sourceTimeZone.BaseUtcOffset).DateTime
        /// </summary>
        /// <param name="dateTime"></param>
        /// <param name="timeZoneId"></param>
        /// <returns></returns>
        public static DateTime ConvertTimeToUtcWithoutDayLight(DateTime dateTime, string timeZoneId)
        {
            var sourceTimeZone = TimeZone(timeZoneId);
            var utcOffset = new DateTimeOffset(dateTime, TimeSpan.Zero);
            return utcOffset.ToOffset(sourceTimeZone.BaseUtcOffset).DateTime;
        }


        /// <summary>
        /// Return Converted Time To UTC
        /// </summary>
        /// <param name="dateTime"></param>
        /// <param name="timeZoneId"></param>
        /// <returns></returns>

        public static DateTime? ConvertTimeToUtc(DateTime dateTime, string timeZoneId)
        {
            try
            {
                dateTime = DateTime.SpecifyKind(dateTime, DateTimeKind.Unspecified);
                var sourceTimeZone = TimeZone(timeZoneId);
                return TimeZoneInfo.ConvertTimeToUtc(dateTime, sourceTimeZone);
            }
            catch (Exception e)
            {
                return null;
            }
        }

        /// <summary>
        /// Return Converted Time from UTC
        /// </summary>
        /// <param name="dateTime"></param>
        /// <param name="timeZoneId"></param>
        /// <returns></returns>
        public static DateTime? ConvertTimeFromUtc(DateTime dateTime, string timeZoneId)
        {
            try
            {
                dateTime = DateTime.SpecifyKind(dateTime, DateTimeKind.Unspecified);
                var sourceTimeZone = TimeZone(timeZoneId);
                return TimeZoneInfo.ConvertTimeFromUtc(dateTime, sourceTimeZone);
            }
            catch (Exception e)
            {
                return null;
            }

        }

        /// <summary>
        /// Return Converted Time from TimezoneId
        /// </summary>
        /// <param name="dateTime"></param>
        /// <param name="timeZoneId"></param>
        /// <returns></returns>
        public static DateTime? ConvertTime(DateTime dateTime, string timeZoneId)
        {
            try
            {
                var sourceTimeZone = TimeZone(timeZoneId);
                return TimeZoneInfo.ConvertTime(dateTime, sourceTimeZone);
            }
            catch (Exception e)
            {
                return null;
            }

        }

        /// <summary>
        /// Return UTC offset
        /// </summary>
        /// <param name="dateTime"></param>
        /// <param name="timeZoneId"></param>
        /// <returns></returns>
        public static TimeSpan? GetUtcOffset(DateTime dateTime, string timeZoneId)
        {
            try
            {
                var sourceTimeZone = TimeZone(timeZoneId);
                return sourceTimeZone.GetUtcOffset(dateTime);
            }
            catch (Exception e)
            {
                return null;
            }

        }


        /// <summary>
        /// Return Converted Time from TimezoneId (without changing the datetime value)
        /// </summary>
        /// <param name="dateTime"></param>
        /// <param name="timeZoneId"></param>
        /// <returns></returns>
        public static DateTime? SetTimeZoneToDateTIme(DateTime dateTime, string timeZoneId)
        {
            try
            {
                var sourceTimeZone = TimeZone(timeZoneId);
                return TimeZoneInfo.ConvertTime(dateTime, sourceTimeZone, sourceTimeZone);
            }
            catch (Exception e)
            {
                return null;
            }

        }
    }
}
