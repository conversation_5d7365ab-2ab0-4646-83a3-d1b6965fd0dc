﻿using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace Capstone2.Framework.Business.Common
{
    public interface IDistributedCacheHelper
    {
        Task<dynamic> GetFromCache<TResult>(string cachedKey);
        Task SetIntoCache(dynamic responseData, string cachedKey);
        Task RemoveCache(string cachekey);
        Task SetIntoCacheRolePermissionModuleWise(dynamic responseData, string cachedKey, string parentCachedKey);
        Task DeleteAllChildKeysCache(string parentCachedKey);
    }
    public class DistributedCacheHelper : IDistributedCacheHelper
    {
        private IDistributedCache _redisCache;
        private DistributedCacheEntryOptions _cacheOptions;
        private readonly ILogger _logger;

        public DistributedCacheHelper(IDistributedCache cache, ILogger<DistributedCacheHelper> logger)
        {
            _redisCache = cache;
            _cacheOptions = new DistributedCacheEntryOptions().SetAbsoluteExpiration(DateTime.UtcNow.AddHours(23));
            _logger = logger;

        }

        public async Task<dynamic> GetFromCache<TResult>(string cachedKey)
        {
            try
            {
                var dataFromRedis = await _redisCache.GetAsync(cachedKey);
                if ((dataFromRedis?.Count() ?? 0) > 0)
                {
                    var dataToString = Encoding.UTF8.GetString(dataFromRedis);
                    return JsonSerializer.Deserialize<TResult>(dataToString);
                }
                return null;
            }
            catch (Exception e)
            {
                _logger.LogError($"Cannot Get from RedisCache. {e.Message} {e.StackTrace}");
                return null;
            }
        }
        public async Task SetIntoCache(dynamic responseData, string cachedKey)
        {
            try
            {


                var cachedDataString = JsonSerializer.Serialize(responseData);
                var dataToCache = Encoding.UTF8.GetBytes(cachedDataString);
                if (_cacheOptions.AbsoluteExpiration.HasValue && _cacheOptions.AbsoluteExpiration.Value <= DateTime.UtcNow)
                {
                    _cacheOptions.SetAbsoluteExpiration(DateTime.UtcNow.AddHours(23));
                    await _redisCache.RefreshAsync(cachedKey);
                }
                await _redisCache.SetAsync(cachedKey, dataToCache, _cacheOptions);
            }
            catch (Exception e)
            {
                _logger.LogError($"Cannot Set RedisCache. {e.Message} {e.StackTrace}");

            }
        }

        public async Task RemoveCache(string cachekey)
        {
            try
            {

                await _redisCache.RemoveAsync(cachekey);
            }
            catch (Exception e)
            {
                _logger.LogError($"Cannot remove from RedisCache. {e.Message} {e.StackTrace}");

            }
        }

        /// <summary>
        /// Store All roles permission key module , Role & organization wise, So on update of rolespermission we can delete all
        /// </summary>
        /// <param name="responseData"></param>
        /// <param name="cachedKey"></param>
        /// <param name="parentCachedKey"></param>
        /// <returns></returns>
        public async Task SetIntoCacheRolePermissionModuleWise(dynamic responseData, string cachedKey, string parentCachedKey)
        {
            try
            {


                List<string> responseCachedKeyData = await GetFromCache<List<string>>(parentCachedKey);
                if (responseCachedKeyData == null)
                    responseCachedKeyData = new List<string>();

                responseCachedKeyData.Add(cachedKey);

                await SetIntoCache(responseData, cachedKey);

                //Set parent Key
                await SetIntoCache(responseCachedKeyData, parentCachedKey);
            }
            catch (Exception e)
            {
                _logger.LogError($"Cannot SetIntoCacheRolePermissionModuleWise . {e.Message} {e.StackTrace}");
            }
        }

        /// <summary>
        /// Remove All child keys of role
        /// </summary>
        /// <param name="parentCachedKey"></param>
        public async Task DeleteAllChildKeysCache(string parentCachedKey)
        {
            try
            {

                List<string> responseCachedKeyData = await GetFromCache<List<string>>(parentCachedKey);
                if (responseCachedKeyData != null)
                {
                    foreach (string cacheKey in responseCachedKeyData)
                        await RemoveCache(cacheKey);
                }
            }
            catch (Exception e)
            {
                _logger.LogError($"Cannot DeleteAllChildKeysCache . {e.Message} {e.StackTrace}");

            }
        }
    }
}