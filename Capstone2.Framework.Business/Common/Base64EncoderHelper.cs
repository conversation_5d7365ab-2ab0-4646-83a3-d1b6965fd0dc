﻿using System;
using System.Text;

namespace Capstone2.Framework.Business.Common
{
    public static class Base64EncoderHelper
    {
        public static string EncodeBase64(this string value)
        {
            var valueBytes = Encoding.UTF8.GetBytes(value);
            return Convert.ToBase64String(valueBytes);
        }

        public static string DecodeBase64(this string value)
        {
            var valueBytes = System.Convert.FromBase64String(value);
            return Encoding.UTF8.GetString(valueBytes);
        }
        public static string EncodeQueryModelFilterParam(this string value)
        {
            string encodedFilter = EncodeBase64(value);
            return ((encodedFilter is null)?string.Empty : encodedFilter.Replace("+", "-").Replace("/", "_"));
        }
    }
}
