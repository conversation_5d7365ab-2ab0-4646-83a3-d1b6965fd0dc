﻿using Capstone2.Framework.Business.Enum;
using System;

namespace Capstone2.Framework.Business.Exceptions
{
    public class BusinessException : Exception
    {
        public float ErrorCode { get; set; }       

        public string ErrorDescription
        {
            get { return ((EnumErrorCode)ErrorCode).GetDescription(); }
        }

        public BusinessException(float errorCode, string message) : base(message)
        {
            ErrorCode = errorCode;
        }

        public BusinessException(float errorCode, Exception exception) : base(((EnumErrorCode)errorCode).GetDescription(), exception)
        {
            ErrorCode = errorCode;
        }
    }
}
