﻿using System.ComponentModel;

namespace Capstone2.Framework.Business.Enum
{
    public enum EnumErrorCode
    {
        [Description("Bad Request")]
        BadRequest = 400,

        [Description("Unauthorized Access")]
        UnauthorizedAccess = 401,

        [Description("Exception while cross service calls")]
        CrossServiceCallFailure = 410,

        [Description("Internal Server Error")]
        InternalServerError = 500,
    }
}
