﻿using System;
using System.Collections.Generic;

#nullable disable

namespace Capstone2.Shared.Models.Entities
{
    public partial class ActivityLogInfo
    {
        public ActivityLogInfo()
        {
            ActivityLogChildEntries = new HashSet<ActivityLogChildEntryInfo>();
        }

        public long Id { get; set; }
        public long PatientDetailsId { get; set; }
        public int OrgId { get; set; }
        public string Description { get; set; }
        public string Title { get; set; }
        public short ActivityLogTypeId { get; set; }
        public short? ActivityStatusId { get; set; }
        public long? FileDetailsId { get; set; }
        public long? EntityId { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public long? ModifiedBy { get; set; }
        public short? ActivityLogSubTypeId { get; set; }

        public virtual ICollection<ActivityLogChildEntryInfo> ActivityLogChildEntries { get; set; }
    }

    public partial class ActivityLogChildEntryInfo
    {
  
        public long Id { get; set; }
        public long ActivityLogId { get; set; }
        public int OrgId { get; set; }
        public string Description { get; set; }
        public string Title { get; set; }
        public short? SubTypeId { get; set; }
        public string VariableJson { get; set; }
        public short? ActivityStatusId { get; set; }
        public long? FileDetailsId { get; set; }
        public long? ParentEntityId { get; set; }
        public string ParentDescription { get; set; }
        public string ParentTitle { get; set; }
        public short ParentActivityLogTypeId { get; set; }
        public long? EntityId { get; set; }

    }

    public class VariableJSON
    {
        public string Operation { get; set; }
        public string Column { get; set; }
        public object OldValue { get; set; }
        public object NewValue { get; set; }

    }
}
