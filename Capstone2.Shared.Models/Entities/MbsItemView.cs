﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Capstone2.Shared.Models.Entities
{
    partial class MbsItemView
    {

    }
    public partial class MbsDataView
    {
        public long Id { get; set; }
        public long ItemNum { get; set; }
        public string Description { get; set; }
        public float ScheduleFee { get; set; }
        public float Benefit75 { get; set; }
        public float Benefit85 { get; set; }
        public float Benefit100 { get; set; }
        public short? ClinicalCategoryId { get; set; }
        public string ClinicalCategoryName { get; set; }
        public string Category { get; set; }
        public string Group { get; set; }
        public string SubGroup { get; set; }
        public string FeeType { get; set; }
        public DvaView DvaData { get; set; }
        public TheatreView TheatreData { get; set; }
        public MbsItemView MbsItemView { get; set; }

    }

    public partial class DvaView
    {
        public long Id { get; set; }
        public string LMOFee { get; set; }
        public string RMFSInHospitalFee { get; set; }
        public string RMFSOutOfHospitalFee { get; set; }


    }

    public partial class TheatreView
    {
        public int Id { get; set; }
        public long ItemNum { get; set; }
        public string Band { get; set; }
        public string PatientClassOvernight { get; set; }
        public string PatientClassSameDay { get; set; }
    }
    public partial class MbsItemView
    {
        public MbsItemView()
        {
            MbsItemCustomFeeAssocs = new HashSet<MbsItemCustomFeeInfoAssoc>();
        }

        public long Id { get; set; }
        public int OrgId { get; set; }
        public short? EpisodeCateogoriesIdLvl1 { get; set; }
        public short? EpisodeCateogoriesIdLvl2 { get; set; }
        public string ECLvl2Name { get; set; }
        public short? EpisodeCateogoriesIdLvl3 { get; set; }
        public string ECLvl3Name { get; set; }
        public long ItemNum { get; set; }
        public string UserDefinedDescription { get; set; }
        public bool Gstinc { get; set; }
        public decimal? PrivateFee { get; set; }
        public decimal? GstFee { get; set; }
        public decimal? PrivateFeeIncGst
        {
            get
            {
                if (Gstinc == true)
                    return PrivateFee + GstFee;
                else
                    return null;
            }
        }
        public short StatusId { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public long? ModifiedBy { get; set; }

        public virtual ICollection<MbsItemCustomFeeInfoAssoc> MbsItemCustomFeeAssocs { get; set; }

    }
}
