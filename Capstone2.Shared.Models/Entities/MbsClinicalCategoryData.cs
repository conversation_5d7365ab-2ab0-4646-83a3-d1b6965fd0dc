﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

#nullable disable

namespace Capstone2.Shared.Models.Entities
{

    public partial class MbsClinicalCategoryData
    {
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Key, Column(Order = 0)]
        public int Id { get; set; }
        public long ItemNum { get; set; }
        public short ClinicalCategoryId { get; set; }
        public string ProcedureType { get; set; }
        public short StatusId { get; set; }
        [Required, DatabaseGenerated(DatabaseGeneratedOption.Computed)]
        public DateTime CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public long? ModifiedBy { get; set; }

        public virtual ClinicalCategory ClinicalCategory { get; set; }
    }

    public partial class ListMbsClinicalCategory
    {
        public short Id { get; set; }
        public int OrgId { get; set; }
        public short StatusId { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public long? ModifiedBy { get; set; }
    }
}
