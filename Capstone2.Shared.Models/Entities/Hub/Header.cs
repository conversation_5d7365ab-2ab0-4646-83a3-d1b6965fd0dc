﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.Shared.Models.Entities.Hub
{
    public class RequestHeader
    {

        public string ToSystem { get; set; }
        public string FromSystem { get; set; }
        public string SendingFacility { get; set; }
        public string SendingApp { get; set; }
        public string ReceivingFacility { get; set; }
        public string ReceivingApp { get; set; }
        public string MessageID { get; set; }
        public string MessageType { get; set; }
        public string EffectiveDate { get; set; }
        public string ExpirationDate { get; set; }
        public string OriginatorID { get; set; }
        public string TenantID { get; set; }
        public string TenantPrefix { get; set; }
    }
    public class ResponseHeader
    {

        public string ToSystem { get; set; }
        public string FromSystem { get; set; }
        public string Status { get; set; }
        public string RequestDateUtc { get; set; }
        public string RequestID { get; set; }
        public string ErrorDescription { get; set; }
        public string StackTrace { get; set; }
        public string ProcessRunID { get; set; }
        public string Message { get; set; }


    }

    public class CommonHubHeader
    {
        public string TenantID { get; set; }

    }

    public class MessageHeader
    {
        public string MessageID { get; set; }
        public string Status { get; set; }
        public string MessageGuid { get; set; }
        public string MessageControlID { get; set; }
        public string DateCreatedUtc { get; set; }
        public string FromSystem { get; set; }
        public string MessageType { get; set; }
        public string ToSystem { get; set; }
        public string ProcessRunID { get; set; }
    }
}
