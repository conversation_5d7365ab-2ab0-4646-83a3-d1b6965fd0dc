﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.Shared.Models.Entities.Hub
{
    public class CapstoneHubPatient
    {
        public string GivenName { get; set; }
        public string LastName { get; set; }
        public string MiddleName { get; set; }
        public string DOB { get; set; }
        public string Sex { get; set; }
        public string Race { get; set; }
        public IDRec IDRec { get; set; }
        public CapstoneHubAddress Address { get; set; }
        public CapstoneHubPhone Phone { get; set; }

    }
    public class IDRec
    {
        public string ID { get; set; }
        public string IDType { get; set; }
        public string IDAuthority { get; set; }

    }
}
