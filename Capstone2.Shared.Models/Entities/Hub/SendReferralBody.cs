﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.Shared.Models.Entities.Hub
{
    public class MessageRequest
    {
        public RequestHeader Header { get; set; }
        public MessageRequestBody Body { get; set; }

    }
    public class SendReferralResponse
    {
        public ResponseHeader Header { get; set; }
        public ResponseBody Body { get; set; }
        //public ICollection<HL7Message> HL7Messages { get; set; }
        //public string Status { get; set; }
        //public string Message { get; set; }
        //public string Code { get; set; }
        //public string Description { get; set; }
        //public string ErrorDetails { get; set; }
        //public string ErrorType { get; set; }
        //public string ProcessRunID { get; set; }

    }

    public class MessageRequestBody
    {
        public Referral Referral { get; set; }
        public ICollection<Provider> Providers { get; set; }
        public CapstoneHubPatient Patient { get; set; }
        public OBRequest OBRequest { get; set; }
        public ICollection<OBResult> OBResults { get; set; }


    }
    public class InputMessageRequestBody
    {
        public long? LetterId { get; set; }
        public short SecureMessagingTypeId { get; set; }
        public Referral Referral { get; set; }
        public ICollection<Provider> Providers { get; set; }
        public CapstoneHubPatient Patient { get; set; }
        public OBRequest OBRequest { get; set; }
        public ICollection<InputOBResult> OBResults { get; set; }


    }
    public class Referral
    {
        public string Reason { get; set; }
        public string ExternalIdentifier { get; set; }

    }
    public class OBResult
    {
        //public string OrderNumber { get; set; }
        public string Comment { get; set; }
        public string ResultData { get; set; }
        public string ResultURL { get; set; }
        public string ResultType { get; set; }

    }
    public class InputOBResult
    {
        //public string OrderNumber { get; set; }
        public string Comment { get; set; }
        public string ResultData { get; set; }
        public long? FileDetailsId { get; set; }
        public string ResultURL { get; set; }
        public string ResultType { get; set; }

    }
    public class OBRequest
    {
        public string Comment { get; set; }
        public string ResultType { get; set; }
        public string OrderNumber { get; set; }
        // public string ResultData { get; set; }
        // public string ResultURL { get; set; }

    }
    public class ResponseBody
    {
        public string MessageGuid { get; set; }
        public string Ack { get; set; }
      
    }
}
