﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Capstone2.Shared.Models.Entities.Hub
{
    public class Provider
    {
        public string GivenName { get; set; }
        public string LastName { get; set; }
        public string DoctorRole { get; set; }
        public bool Orderer { get; set; }
        public string Prefix { get; set; }
        public string ProvID { get; set; }
        public string Email { get; set; }
        public string Phone { get; set; }
        public string Fax { get; set; }
        public Location Location { get; set; }
        public CapstoneHubAddress Address { get; set; }
    }
    public class Location
    {
        public string Facility { get; set; }
    }
    public class CapstoneHubAddress
    {
        public string StreetAddress { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string PostalCode { get; set; }
        public string Country { get; set; }
        //public string ProvID { get; set; }
        //public string Email { get; set; }
        //public string Phone { get; set; }
        //public string Fax { get; set; }
        //public string Location { get; set; }
    }

    public class ProviderInfo
    {
        public Provider Provider { get; set; }        
        public bool IsActive { get; set; }
        public string Comments { get; set; }       
    }

}
