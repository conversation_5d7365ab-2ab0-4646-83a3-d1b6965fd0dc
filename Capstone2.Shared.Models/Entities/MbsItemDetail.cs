﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Capstone2.Shared.Models.Entities
{
    public partial class MbsItemDetail
    {
        public MbsItemDetail()
        {
            MbsItemCustomFeeAssocs = new HashSet<MbsItemCustomFeeAssoc>();
        }

        public long Id { get; set; }
        public int OrgId { get; set; }
        public short? EpisodeCateogoriesIdLvl1 { get; set; }
        public short? EpisodeCateogoriesIdLvl2 { get; set; }
        public short? EpisodeCateogoriesIdLvl3 { get; set; }
        public long ItemNum { get; set; }
        public string UserDefinedDescription { get; set; }
        public bool Gstinc { get; set; }
        public decimal? PrivateFee { get; set; }
        public decimal? GstFee { get; set; }
        public short StatusId { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public long? ModifiedBy { get; set; }

        public virtual ICollection<MbsItemCustomFeeAssoc> MbsItemCustomFeeAssocs { get; set; }

    }
    public partial class AssignCategoryInput
    {
        public short? EpisodeCateogoriesIdLvl1 { get; set; }
        public short? EpisodeCateogoriesIdLvl2 { get; set; }
        public short? EpisodeCateogoriesIdLvl3 { get; set; }

    }
}
