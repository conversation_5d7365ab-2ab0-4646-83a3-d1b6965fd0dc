﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Capstone2.Shared.Models.Entities
{
    public class ConsultNoteTemplate
    {
        public ConsultNoteTemplate()
        {
            ConsultNoteTemplateControls = new HashSet<ConsultNoteTemplateControls>();
            ConsultNoteTemplateUserAssocs = new HashSet<ConsultNoteTemplateUserAssocs>();
        }

        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Key, Column(Order = 0)]
        public long Id { get; set; }
        public int OrgId { get; set; }
        public string Name { get; set; }
        public int CompanyDetailsId { get; set; }
        public string DeleteReason { get; set; }
        public short? NumberOfRows { get; set; }
        public string NumberOfColumnsJson { get; set; }
        public short? StatusId { get; set; }
        [Required, DatabaseGenerated(DatabaseGeneratedOption.Computed)]
        public DateTime CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public long? CreatedBy { get; set; }
        public long? ModifiedBy { get; set; }

        public virtual ICollection<ConsultNoteTemplateControls> ConsultNoteTemplateControls { get; set; }

        public virtual ICollection<ConsultNoteTemplateUserAssocs> ConsultNoteTemplateUserAssocs { get; set; }
    }

    public class ConsultNoteTemplateView
    {
        public long Id { get; set; }
        public int OrgId { get; set; }
        public string Name { get; set; }
        public int CompanyDetailsId { get; set; }
        public string DeleteReason { get; set; }
        public short? NumberOfRows { get; set; }
        public string NumberOfColumnsJson { get; set; }
        public short? StatusId { get; set; }
        [Required, DatabaseGenerated(DatabaseGeneratedOption.Computed)]
        public DateTime CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public long? CreatedBy { get; set; }
        public long? ModifiedBy { get; set; }

        public ICollection<ConsultNoteTemplateControlsView> ConsultNoteTemplateControls { get; set; }

        public ICollection<ConsultNoteTemplateUserAssocsView> ConsultNoteTemplateUserAssocs { get; set; }
    }

    public class ConsultNoteTemplateList
    {
        public long Id { get; set; }
        public int OrgId { get; set; }
        public string Name { get; set; }
        public short? StatusId { get; set; }
        public DateTime CreatedDate { get; set; }
        public long? CreatedBy { get; set; }
        public string CreatedByFirstName { get; set; }
        public string CreatedBySurName { get; set; }
    }

    public class ConsultNoteTemplateDelete
    {
        public string Reason { get; set; }
    }
}
