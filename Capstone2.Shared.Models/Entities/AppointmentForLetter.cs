﻿using System;
using System.Collections.Generic;

namespace Capstone2.Shared.Models.Entities
{
    public partial class AppointmentForLetter
    {        
        public long Id { get; set; }
        public int OrgId { get; set; }
        public long? PatientDetailsId { get; set; }
        public string  AppointmentType { get; set; }
        public string AppointmentDate { get; set; }
        public string AppointmentTime { get; set; }
        public string AppointmentProvider { get; set; }
        public string AppointmentEntity { get; set; }
        public string AdmissionDate { get; set; }
        public string AdmissionTime { get; set; }
        public string AppointmentDetails { get; set; }
        public string Anaesthetist { get; set; }
        public string Assistant { get; set; }
        public string AnaestheticType { get; set; }
        public string Hospital { get; set; }
    }
}
