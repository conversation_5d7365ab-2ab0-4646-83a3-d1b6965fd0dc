﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Capstone2.Shared.Models.Entities
{
	public class ReportDetailStoredProcs
	{
		public ReportDetailStoredProcs()
		{
			//ReportDetails = new ReportDetails();
		}
		public long ID { get; set; }
		public long ReportId { get; set; }
		public string StoredProcName { get; set; }
		public string ReportDataSetName { get; set; }
		public string Description { get; set; }
		public bool Status { get; set; }
		public DateTime ModifiedDate { get; set; }
	}
}
